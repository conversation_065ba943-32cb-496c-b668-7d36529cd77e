Vue.component("select-pos-price", {
  props: ["priceWay"],
  data() {
    return {
      // 表单数据
      formData: {
        defaultPrice: "", // 默认price字段
        use_dow: [
          {
            dow: [], // 多选DOW值
            price: "" // 单选price值
          }
        ] // 动态DOW表单项
      },
      // price选项数组
      priceOptions: [
        { label: "PRICE1", value: "PRICE1" },
        { label: "PRICE2", value: "PRICE2" },
        { label: "PRICE3", value: "PRICE3" },
        { label: "PRICE4", value: "PRICE4" },
        { label: "PRICE5", value: "PRICE5" },
        { label: "PRICE6", value: "PRICE6" },
        { label: "PRICE7", value: "PRICE7" },
        { label: "PRICE8", value: "PRICE8" }
      ],
      // DOW选项数组 (1-7代表周一到周日，H代表节假日)
      dowOptions: [
        { label: "1", value: "1" },
        { label: "2", value: "2" },
        { label: "3", value: "3" },
        { label: "4", value: "4" },
        { label: "5", value: "5" },
        { label: "6", value: "6" },
        { label: "7", value: "7" },
        { label: "H", value: "H" }
      ],
      // 表单验证规则
      rules: {
        defaultPrice: [{ required: true, message: "please select price", trigger: "change" }]
      }
    }
  },
  created() {
    let priceWay = {}
    if (typeof this.priceWay === "function") {
      priceWay = this.priceWay.call(this.$root)
    }
    if (priceWay.hasOwnProperty("defaultPrice")) {
      this.formData = { ...this.formData, ...priceWay }
    }

    // console.log(this.formData, "priceWay")
  },
  mounted() {},

  computed: {
    // 判断第一个表单项是否填写完整
    isFirstItemComplete() {
      if (this.formData.use_dow.length === 0) return false
      const firstItem = this.formData.use_dow[0]
      return firstItem && firstItem.dow && firstItem.dow.length > 0 && firstItem.price !== ""
    },
    // 判断添加按钮是否应该禁用
    isAddButtonDisabled() {
      return this.formData.use_dow.length > 7
    }
  },
  methods: {
    // 添加新的DOW表单项
    addDowItem() {
      this.formData.use_dow.push({
        dow: [], // 多选DOW值
        price: "" // 单选price值
      })
      // 添加后滚动到底部
      this.$nextTick(() => {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: "smooth"
        })
      })
    },
    // 删除DOW表单项
    removeDowItem(index) {
      // 不能删除第一项
      if (index === 0) return
      this.formData.use_dow.splice(index, 1)
    },
    // 判断DOW选项是否禁用
    isDowDisabled(dowValue, currentIndex) {
      // 检查当前DOW值是否已被其他表单项使用
      for (let i = 0; i < this.formData.use_dow.length; i++) {
        if (
          i !== currentIndex &&
          this.formData.use_dow[i].dow &&
          this.formData.use_dow[i].dow.includes(dowValue)
        ) {
          return true
        }
      }
      return false
    },
    // 判断price选项是否禁用
    isPriceDisabled(priceValue, currentIndex) {
      // 动态DOW表单区域内的Price只与区域内的其他Price互斥，不与默认价格互斥
      // 检查当前price值是否已被其他DOW表单项使用
      for (let i = 0; i < this.formData.use_dow.length; i++) {
        if (i !== currentIndex && this.formData.use_dow[i].price === priceValue) {
          return true
        }
      }
      return false
    },
    // 获取表单项的验证规则
    getRulesForItem(index, field) {
      // 第一项不是必填项，后续项是必填项
      if (index === 0 && this.formData.use_dow.length == 1) {
        // 第一项返回空数组，表示不参与校验
        return []
      } else {
        return [
          {
            required: true,
            message: field === "dow" ? "please select dow" : "please select price",
            trigger: "change",
            // 为DOW字段添加类型校验，确保正确处理数组
            type: field === "dow" ? "array" : "string"
          }
        ]
      }
    },
    // 提交表单
    submitPosPriceForm() {
      let isPass = false
      this.$refs.posPriceDowForm.validate(valid => {
        if (valid) {
          isPass = true
        }
      })
      if (isPass) {
        return this.formData
      } else {
        return false
      }
    },
    // 重置表单
    resetForm() {
      this.$refs.posPriceDowForm.resetFields()
      this.formData.use_dow = []
      this.addDowItem()
    }
  },

  template: `
       <template>
         <div class="posPrice-container">
        <!-- 表单组件 -->
        <el-form ref="posPriceDowForm" :model="formData" :rules="rules" label-width="auto" size="medium">
          <!-- 默认price字段 -->
          <el-form-item label="Default Price" prop="defaultPrice">
            <el-select
              v-model="formData.defaultPrice"
              placeholder="please select price"
              style="width: 100%"
            >
              <el-option
                v-for="item in priceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <!-- 动态DOW表单区域标题 -->
          <div style="margin: 40px 0 10px 0">
            <el-divider content-position="left">
              <span style="font-size: 16px; font-weight: bold">DOW form</span>
            </el-divider>
          </div>

          <!-- 动态DOW表单区域 -->
          <div v-for="(item, index) in formData.use_dow" :key="index" class="posPrice-form-item">
            <div class="posPrice-form-content">
              <!-- DOW多选下拉框 -->
              <el-form-item
                :label="'Use DOW'"
                :prop="'use_dow.' + index + '.dow'"
                :rules="getRulesForItem(index, 'dow')"
              >
                <el-select
                  v-model="item.dow"
                  multiple
                  placeholder="please select dow"
                  style="width: 100%"
                  clearable

                >
                  <el-option
                    v-for="option in dowOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    :disabled=" isDowDisabled(option.value, index)"
                  ></el-option>
                </el-select>
              </el-form-item>

              <!-- Price单选下拉框 -->
              <el-form-item
                :label="'Price'"
                :prop="'use_dow.' + index + '.price'"
                :rules="getRulesForItem(index, 'price')"
              >
                <el-select
                  v-model="item.price"
                  placeholder="please select price"
                  style="width: 100%"
                  clearable

                >
                  <el-option
                    v-for="option in priceOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    :disabled="isPriceDisabled(option.value, index)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 第一项显示添加按钮，其他项显示删除按钮 -->
            <template v-if="index === 0">
              <el-button
                type="primary"
                icon="el-icon-plus"
                circle
                size="mini"
                class="posPrice-add-btn"
                @click="addDowItem"
                :disabled="!isFirstItemComplete || isAddButtonDisabled"
              ></el-button>
            </template>
            <template v-else>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                class="posPrice-delete-btn"
                @click="removeDowItem(index)"
              ></el-button>
            </template>
          </div>
        </el-form>
      </div>
      </template>
  `
})
