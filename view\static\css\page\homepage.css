@charset "utf-8";

/*--首页--*/
:root {
  --nabeColor: #c7b14c;
  --ppgColor: #770239;
  --szyColor: #297d2f;
  --bzzColor: #415047;
  --styleColor: #ED6211;
}

.search .search-box {
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 11rem;
  left: 13rem;
}

.search .search-box i {
  position: absolute;
  top: 0px;
  margin: 0 auto;
  left: 30%;
  line-height: 2.5rem;
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.5rem;
  z-index: 8;
}

.search .search-box input {
  border: transparent;
  border-radius: 1rem;
  font-size: 0.8rem;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.4rem 0.2rem;
  padding-left: 48%;
  color: rgba(255, 255, 255, 1);
  position: absolute;
  top: 0.37rem;
  z-index: 1;
  width: 100%;
}

.search .search-box input::-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search .search-box input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search .search-box input:focus::-webkit-input-placeholder {
  text-align: left;
}

.search button {
  position: absolute;
  top: 0.4rem;
  right: -3.1rem;
  border: none;
  font-size: 1rem;
  height: 1.7rem;
  width: 3rem;
  background: transparent;
  z-index: 0;
  color: rgba(255, 255, 255, 1);
  border-radius: 0.2rem;
  display: none;
}

.search button:active {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.8);
}

.search-content {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9;
  display: none;
}

.title {
  height: 1.2rem;
  line-height: 1.2rem;
  font-size: 0.8rem;
  padding: 0.5rem;
}

.title i {
  font-size: 1.35rem;
  margin-right: 0.2rem;
  float: left;
}

/*--分类--*/
.sortNav {
  width: 5.5rem;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  z-index: 2;
  padding-bottom: 2.5rem;
  background: rgba(0, 0, 0, 0.02);
  background: #f0f0f0;
}

.sortNav a.icon-stars {
  font-size: 0.8rem;
  text-align: inherit;
  color: rgba(0, 0, 0, 0.7);
}

.sortNav a:before {
  font-size: 1.1rem;
  margin: 0 0.08rem;
  line-height: 1rem;
  vertical-align: text-top;
}

.sortNav a {
  display: block;
  /* height: 2.5rem; */
  /* line-height: 2.5rem; */
  padding: 0.8rem 0;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.75rem;
  width: 5.5rem;
  background-image: -webkit-linear-gradient(
    bottom,
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.1) 60%,
    transparent 60%
  );
  background-size: 100% 1px;
  background-position: bottom;
  background-repeat: no-repeat;
}

.sortNav a:active {
  background: rgba(0, 0, 0, 0.03);
  color: rgba(0, 0, 0, 0.8);
  background-image: -webkit-linear-gradient(
    bottom,
    rgba(0, 0, 0, 0.12),
    rgba(0, 0, 0, 0.12) 60%,
    transparent 60%
  );
  background-size: 100% 1px;
  background-position: bottom;
  background-repeat: no-repeat;
}

.sortNav a.select {
  background: rgba(255, 255, 255, 1);
  color: rgba(255, 159, 0, 1);
  width: 5.5rem;
  background-image: -webkit-linear-gradient(
    bottom,
    rgba(0, 0, 0, 0.12),
    rgba(0, 0, 0, 0.12) 60%,
    transparent 60%
  );
  background-size: 100% 1px;
  background-position: bottom;
  background-repeat: no-repeat;
}

.sortNav p {
  font-size: 0.8rem;
  vertical-align: middle;
  border-width: 0 0 1px 0;
  background: rgba(255, 255, 255, 0.5);
  padding: 0.2rem 0;
  width: 99.5%;
}

.sortNav p:before {
  font-size: 1rem;
  margin: 0 0.1rem;
  line-height: 1rem;
  vertical-align: text-top;
}

.sortContent {
  height: 100%;
  /* width: 100%; */
  margin-left: 5.5rem;
  /* position: absolute; */
  position: fixed;
  overflow-y: auto;
  /* left: 6.3rem; */
  /* margin-left: 6.3rem; */
  z-index: 1;
  background: rgba(255, 255, 255, 1);
  border-width: 0 0 0 1px;
}

.con_tainner {
  content: '';
  display: block;
  width: 0;
  height: 0;
  clear: both;
}

.con_tainner {
  zoom: 1;
}

.sortContent .list-content {
  overflow-y: auto;
  padding: 0 0 3.5rem 0;
}

.sortContent .list-content li {
  margin-top: 0;
  border-width: 0 0 1px 0;
}

.sortContent .pro-img {
  width: 3.5rem !important;
}

.sortContent .pro-con {
  padding-left: 4rem !important;
}

.sortContent .list-content h3 {
  font-size: 0.65rem;
}

.sortContent .list-content b {
  font-size: 0.7rem;
}

/*--产品列表--*/

.list-content {
  /* padding: 0 0 2.5rem 0; */
  width: 100%;
}

.list-content li {
  position: relative;
  /* padding: 0.5rem; */
  padding: 0.5rem 0rem 0.5rem 0.4rem;
  background: rgba(255, 255, 255, 1);
  border-width: 1px 0;
  overflow: hidden;
  margin-top: 0.5rem;
}

.list-content li h3 {
  font-size: 0.8rem;
  display: block;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
  color: rgba(0, 0, 0, 0.7);
}

.list-content li font {
  font-size: 0.6rem;
  color: rgba(255, 159, 0, 1);
  display: block;
}

.list-content li b {
  font-size: 0.65rem;
  display: block;
  color: rgba(231, 5, 19, 1);
  padding: 0.2rem 0;
  font-weight: inherit;
}

.list-content li b s {
  font-size: 0.5rem;
  margin-left: 0.5rem;
  color: rgba(0, 0, 0, 0.5);
}

.list-content li p {
  font-size: 0.6rem;
  line-height: 0.9rem;
  color: rgba(0, 0, 0, 0.5);
}

.list-content li p span {
  color: rgba(0, 142, 70, 1);
}

.list-content li p i {
  margin: 0 0.1rem 0 0.5rem;
  font-size: 0.8rem;
}

.list-content li p:nth-of-type(2) {
  color: rgba(0, 0, 0, 0.7);
}

.list-content .pro-img {
  width: 5rem;
  float: left;
  margin-right: 0.5rem;
  padding-top: 0.2rem;
}

.list-content .pro-img img {
  width: 100%;
  display: block;
  background: url(/img/web-order/ltimg.gif) no-repeat center;
  height: auto;
  background-size: cover;
  max-height: 4rem;
}

.list-content .pro-con {
  padding-left: 5.5rem;
}

.list-content .list-cart {
  position: absolute;
  right: 0.5rem;
  bottom: 0;
  font-size: 1.5rem;
  color: rgba(255, 159, 0, 1);
}

.list-content .list-cart:active {
  color: rgba(255, 159, 0, 0.5);
}

.list-search i.icon-sousuo {
  position: absolute;
  right: 2.5rem;
  top: 0;
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  text-align: center;
  display: inline-block;
  color: rgba(255, 255, 255, 1);
}

.list-search i.icon-sousuo:active {
  background: rgba(0, 0, 0, 0.1);
}

.list-search .search-box {
  display: none;
}

.list-search .search-box input {
  border: transparent;
  border-radius: 1rem;
  font-size: 0.8rem;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.4rem 0.2rem;
  padding-left: 1.9rem;
  color: rgba(255, 255, 255, 1);
  position: absolute;
  top: 0.37rem;
  z-index: 1;
  width: 100%;
}

.list-search .search-box i {
  left: 0.15rem;
  color: rgba(255, 255, 255, 0.5);
  right: initial;
  width: initial;
}

/*--商品详情--*/
.D-slide {
  width: 100%;
}

.D-slide img {
  width: 100%;
}

.D-head {
  padding: 0 0.5rem;
  background: rgba(255, 255, 255, 1);
  border-width: 1px 0;
}

.D-head h2 {
  line-height: 1.3rem;
  font-size: 0.9rem;
  color: rgba(0, 0, 0, 0.8);
  padding: 0.5rem 0.1rem 0 0.1rem;
  position: relative;
}

.D-head h2 i {
  position: absolute;
  right: 0.1rem;
  top: 0.5rem;
  color: rgba(0, 0, 0, 0.2);
}

.D-head h2:active i {
  color: rgba(255, 159, 0, 1);
}

.D-head font {
  font-size: 0.7rem;
  padding: 0 0.1rem 0.5rem 0.1rem;
  display: block;
  color: rgba(255, 159, 0, 1);
}

.D-head p {
  border-width: 1px 0 0 0;
  font-size: 1.2rem;
  color: rgba(231, 5, 19, 1);
  padding: 0.3rem 0;
  margin-top: 0.5rem;
  font-family: '微软雅黑';
}

.D-head p s {
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.5);
  margin-left: 0.6rem;
}

.D-pro-content {
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  margin-top: 0.5rem;
  padding: 0.1rem 0;
}

.D-pro-content img {
  width: 100%;
  display: block;
}

/*--加减--*/
.D-BuyNum {
  position: absolute;
  height: 2.85rem;
  right: 0;
  bottom: 0;
}

.D-BuyNum button {
  height: 2.2rem;
  padding: 0 0 0 0.5rem;
  background: transparent;
}

.D-BuyNum button i {
  border-radius: 50%;
  -o-border-image: none;
  border-image: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: initial;
  width: 1.2rem;
  height: 1.2rem;
  line-height: 1.2rem;
  display: block;
  /*color: rgba(231,5,19,1);*/
  font-size: 0.8rem;
}

.D-BuyNum button span {
  border-radius: 50%;
  -o-border-image: none;
  border-image: none;
  border: 1px solid white;
  padding: initial;
  width: 1.2rem;
  height: 1.2rem;
  line-height: 1.2rem;
  display: block;
  color: white;
  font-size: 0.8rem;
}

.D-BuyNum button:active i {
  border: 1px solid rgba(255, 0, 0, 0.3);
  background: rgba(255, 159, 0, 0.1);
  color: rgba(0, 0, 0, 1);
}

.D-BuyNum button:last-child {
  margin-left: 1rem;
  padding: 0 0.5rem 0 0;
}

.D-BuyNum input {
  color: white;
  height: 3.1rem;
  width: 1rem;
  line-height: 2.2rem;
  overflow: hidden;
  position: absolute;
  border-width: 0px;
  text-align: center;
  font-size: 0.8rem;
  background: transparent;
  -webkit-appearance: none !important;
  margin: 0;
}

/*--按钮--*/
.button button {
  width: 100%;
  font-size: 1.1rem;
  text-align: center;
  display: block;
  overflow: hidden;
  background: rgba(255, 159, 0, 1);
  color: #fff;
  border-radius: 0.3rem;
  cursor: pointer;
  -webkit-transition: background 0.05s ease-in-out;
  -o-transition: background 0.05s ease-in-out;
  transition: background 0.05s ease-in-out;
}

.button button:active {
  /* background:#E3BF7A !important; */
  background: #e3bf7a !important;
  color: rgba(255, 255, 255, 1) !important;
}

.button button:disabled {
  background: rgba(230, 230, 230, 1);
  color: rgba(0, 0, 0, 0.1);
}

.button button:active:disabled {
  background: rgba(230, 230, 230, 1) !important;
  color: rgba(0, 0, 0, 0.1) !important;
}

/*--列表--*/
.list {
  width: 100%;
}

.list ul,
.list ol {
  padding: 0 1rem 0 2.5rem;
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  margin-top: 0.5rem;
}

.list ul li,
.list ol li {
  width: 100%;
  line-height: 2.5rem;
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.6);
  position: relative;
  padding: 0 0.1rem 0 0.3rem;
  border-width: 0 0 1px 0;
  -o-border-image: url(/img/web-order/line.png) 2 2 2 2;
  border-image: url(/img/web-order/line.png) 2 2 2 2;
}

.list ul li i {
  position: absolute;
  left: -2rem;
  top: 50%;
  margin-top: -0.8rem;
  font-size: 1.3rem;
  width: 1.6rem;
  height: 1.6rem;
  line-height: 1.6rem;
  border-radius: 30%;
  text-align: center;
  color: rgba(255, 255, 255, 1);
  background: rgba(0, 0, 0, 0.5);
}

.list ul li input[type='text'],
input[type='password'],
input[type='datetime'],
input[type='date'],
input[type='tel'] {
  width: 100%;
  height: 1rem;
  line-height: 1rem;
  padding: 0;
  font-size: 0.8rem;
  border: none;
  background: transparent;
  color: rgba(0, 0, 0, 0.6);
}

.list ul li.code input {
  width: 65%;
}

.list ul li.code img {
  position: absolute;
  right: 0;
  top: 0.3rem;
  width: 5rem;
}

.list ul li.code button {
  width: 5.5rem;
  height: 2.2rem;
  line-height: 2.2rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0.2rem;
  position: absolute;
  right: 0;
  top: 0.15rem;
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.5);
}

.list ul li.code button:active {
  background: #e3bf7a !important;
  color: rgba(255, 255, 255, 1) !important;
}

.list ul li.head {
  padding: 0.5rem 0.1rem 0.5rem 0.3rem;
}

.list ul li.head img {
  width: 4rem;
  display: block;
  border-radius: 0.3rem;
}

.list ul li.head button {
  padding: 0 1rem;
  height: 100%;
  line-height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  border-width: 0 0 0 1px;
  background: transparent;
  color: rgba(0, 0, 0, 0.3);
  font-size: 0.8rem;
}

.list ul li.head button font {
  font-size: 0.6rem;
}

.list ul li.head button:before {
  content: attr(placeholder);
  color: #aaa;
}

.list ul li.head button:active {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.7);
}

.list ul li span {
  position: absolute;
  right: 0;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0.15);
}

.list ul li:active {
  color: rgba(0, 0, 0, 0.9);
}

.list ul li:active span {
  color: #e3bf7a;
}

.list ul li em {
  width: 1.2rem;
  height: 1.2rem;
  line-height: 3rem;
  overflow: hidden;
  text-align: center;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -0.6rem;
}

.list ul li.select em {
  background: rgba(255, 159, 0, 1);
  line-height: 1.2rem;
  border: 1px solid rgba(255, 159, 0, 1);
  color: rgba(255, 255, 255, 1);
}

.list ul li:last-child,
.list ol li:last-child {
  border-width: 0;
}

.list ul:nth-child(1) li:nth-of-type(1) i {
  background: rgba(81, 189, 189, 1);
}

.list ul:nth-child(1) li:nth-of-type(2) i {
  background: rgba(246, 148, 99, 1);
}

.list ul:nth-child(1) li:nth-of-type(3) i {
  background: rgba(247, 132, 165, 1);
}

.list ul:nth-child(1) li:nth-of-type(4) i {
  background: rgba(48, 181, 214, 1);
}

.list ul:nth-child(1) li:nth-of-type(5) i {
  background: rgba(133, 173, 50, 1);
}

.list ul:nth-child(1) li:nth-of-type(6) i {
  background: rgba(215, 173, 75, 1);
}

.list ul:nth-child(1) li:nth-of-type(7) i {
  background: rgba(99, 123, 123, 1);
}

.list ul:nth-child(1) li:nth-of-type(8) i {
  background: rgba(58, 146, 90, 1);
}

.list ul:nth-child(2) li:nth-of-type(1) i {
  background: rgba(119, 195, 31, 1);
}

.list ul:nth-child(2) li:nth-of-type(2) i {
  background: rgba(246, 148, 99, 1);
}

.list ul:nth-child(2) li:nth-of-type(3) i {
  background: rgba(247, 132, 165, 1);
}

.list ul:nth-child(2) li:nth-of-type(4) i {
  background: rgba(48, 181, 214, 1);
}

.list ul:nth-child(2) li:nth-of-type(5) i {
  background: rgba(133, 173, 50, 1);
}

.list ul:nth-child(2) li:nth-of-type(6) i {
  background: rgba(215, 173, 75, 1);
}

.list ul:nth-child(2) li:nth-of-type(7) i {
  background: rgba(99, 123, 123, 1);
}

.list ul:nth-child(2) li:nth-of-type(8) i {
  background: rgba(58, 146, 90, 1);
}

.list ul:nth-child(3) li:nth-of-type(1) i {
  background: rgba(64, 94, 144, 1);
}

.list ul:nth-child(3) li:nth-of-type(2) i {
  background: rgba(244, 83, 91, 1);
}

.list ul:nth-child(3) li:nth-of-type(3) i {
  background: rgba(242, 186, 103, 1);
}

.list ul:nth-child(3) li:nth-of-type(4) i {
  background: rgba(90, 133, 212, 1);
}

.list ul:nth-child(4) li:nth-of-type(1) i {
  background: rgba(244, 83, 91, 1);
}

.list-title ul {
  padding-left: 4.5rem;
}

.list-title ul li {
  line-height: 1.2rem !important;
  padding: 0.7rem 0.1rem 0.7rem 0.3rem;
}

.list-title ul li i {
  width: initial;
  background: none !important;
  left: -4rem !important;
  color: rgba(0, 0, 0, 0.6);
  display: inline-block;
  font-style: normal;
  font-size: 0.8rem;
}

.list-select ul {
  padding-left: 0.5rem;
}

.list-select ul li.icon-correct:before {
  position: absolute;
  right: 0.5rem;
  color: rgba(255, 159, 0, 1);
  font-size: 1rem;
}

.list-select ul li font {
  position: absolute;
  right: 0.4rem;
  color: rgba(0, 0, 0, 0.8);
}

.list ol {
  padding: 0 1rem 0 0.5rem !important;
}

.list ol li.success:before {
  content: '成功';
  color: rgba(0, 0, 0, 0.3);
  float: right;
}

.list ol li.fail:before {
  content: '失败';
  color: rgba(255, 0, 0, 1);
  float: right;
}

.list .title {
  padding: 0.5rem 0.5rem 0 0.5rem;
}

.textarea {
  width: 100%;
  padding: 0.05rem;
  background: transparent;
  border: transparent;
  font-size: 0.8rem;
  overflow: auto;
  max-height: 5rem;
  display: block;
}

.textarea:empty:before {
  content: attr(data-placeholder);
  color: #aaa;
}

/*--提交订单--*/
.order-Address {
  margin-top: 0.5rem;
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  position: relative;
}

.order-Address dl {
  padding: 0.45rem 2rem 0.45rem 0.55rem;
  border-top-width: 3px;
  border-bottom-width: 3px;
  border-lef-width: 0;
  border-right-width: 0;
  font-size: 0.9rem;
  position: relative;
  zoom: 0.8;
}

.order-Address dl dt i {
  font-size: 1.1rem;
  line-height: 0.9rem;
  vertical-align: baseline;
  color: rgba(0, 0, 0, 0.3);
}

.order-Address dl dd {
  padding: 0.2rem 0 0 0.1rem;
}

.order-Address dl dd:nth-of-type(2) {
  position: absolute;
  right: 0.3rem;
  top: 50%;
  margin-top: -0.5rem;
  background: rgba(255, 159, 0, 1);
  color: rgba(255, 255, 255, 1);
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  border-radius: 50%;
  padding: 0;
  display: none;
}

.order-Address ul {
  border-width: 1px 0 0 0;
  width: 100%;
}

.order-Address ul li {
  width: 1%;
  display: table-cell;
  border-width: 0 1px 0 0;
}

.order-Address ul li button {
  width: 100%;
  text-align: center;
  background: transparent;
  height: 2rem;
  line-height: 2rem;
  color: rgba(0, 0, 0, 0.3);
  font-size: 0.8rem;
}

.order-Address ul li button:active {
  color: rgba(0, 0, 0, 0.8);
}

.order-Address.select dl {
  -o-border-image: url(/img/web-order/Addressborder.gif) 6 repeat;
  border-image: url(/img/web-order/Addressborder.gif) 6 repeat;
  padding: 0.3rem 2rem 0.3rem 0.4rem;
}

.order-Address.select dl dd:nth-of-type(2) {
  display: inline-block;
}

.order-Address font,
.order-product font,
.order-Delivery font {
  position: absolute;
  right: 0.3rem;
  top: 50%;
  margin-top: -0.5rem;
  height: 1rem;
  line-height: 1rem;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0.2);
}

.order-Address:active font,
.order-product:active font,
.order-Delivery:active font {
  color: rgba(255, 159, 0, 1);
}

.order-Address .icon-code {
  font-size: 1.2rem;
  right: 0.5rem;
  color: rgba(0, 0, 0, 0.3);
  height: 100%;
  top: 0;
  right: 0;
  margin: 0;
  width: 2rem;
}

.order-Address .icon-code:before {
  position: absolute;
  top: 50%;
  margin-top: -0.5rem;
  left: 0.3rem;
}

footer {
  position: fixed;
  left: 0;
  bottom: 0px;
  z-index: 2;
  width: 100%;
  background: rgba(0, 0, 0, 0.85);
  height: 2.5rem;
}

footer .button {
  display: inline-block;
  float: right;
}

/*footer button:first-child{background: #107ABB; color: rgba(255,255,255,1);}*/
footer .D-footer-left {
  font-size: 0.7rem;
  text-align: center;
  width: 35%;
}

footer .D-footer-left a {
  display: table-cell;
  width: 1%;
  color: rgba(255, 255, 255, 0.7);
  padding-top: 0.25rem;
}

footer .D-footer-left a:active {
  color: rgba(255, 255, 0, 1);
}

footer .D-footer-left a.select {
  color: rgba(255, 159, 0, 1);
}

footer .D-footer-left a i {
  display: block;
  font-size: 1.5rem;
  line-height: 1.1rem;
}

footer .C-footer-left {
  width: 50%;
  line-height: 1rem;
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.6);
  padding: 0.2rem 0 0 2.5rem;
}

footer .C-footer-left b {
  font-weight: inherit;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 1);
}

footer h4 {
  font-size: 1rem;
  color: rgba(255, 255, 255, 1);
  line-height: 2.5rem;
  padding-left: 0.5rem;
}

/*快速添加收货地址*/
.order-add-address {
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  text-align: center;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.order-add-address:active {
  color: rgba(255, 159, 0, 1);
}

.Add-Address-Content {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 1);
  display: none;
  z-index: 104;
}

.Add-Address-Content ul {
  margin: 0;
}

.Add-Address-Content ul li font {
  width: 0.2rem;
}

.list .list03 {
  padding: 0 0 0 0.1rem;
}

.list select {
  background: transparent;
  color: rgba(0, 0, 0, 0.3);
  border: none;
  font-size: 0.8rem;
  width: auto;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  padding-right: 0.2rem;
  margin: 0.8rem 0;
}

.list select:last-child {
  border-right: none;
  padding-right: inherit;
}

.list option {
  padding: 0;
}

/*快速管理地址*/
.Edit-Address-Content {
  background: rgba(255, 255, 255, 1);
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: none;
  z-index: 101;
}

.Edit-Address-Content ul {
  padding: 0;
}

.Edit-Address-Content ul li {
  border-width: 0 0 1px 0;
  font-size: 0.7rem;
  padding: 0.5rem 2.5rem 0.5rem 2.8rem;
  position: relative;
  word-break: break-all;
}

.Edit-Address-Content ul li p {
  color: rgba(0, 0, 0, 0.4);
  padding-top: 0.2rem;
}

.Edit-Address-Content ul li.select span {
  border: 1px solid rgba(255, 159, 0, 1);
  background: rgba(255, 159, 0, 1);
}

.Edit-Address-Content ul li.select span:before {
  display: block;
  color: rgba(255, 255, 255, 1);
}

.Edit-Address-Content span {
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  position: absolute;
  left: 0.8rem;
  top: 50%;
  margin-top: -0.5rem;
  font-size: 1rem;
  background: transparent;
}

.Edit-Address-Content span:before {
  display: none;
}

.Edit-Address-Content font {
  position: absolute;
  right: 0;
  top: 0;
  width: 2.5rem;
  display: block;
  height: 100%;
  line-height: 100%;
  font-size: 1rem;
}

.Edit-Address-Content font:active {
  background: rgba(0, 0, 0, 0.01);
  color: rgba(255, 159, 0, 1);
}

.Edit-Address-Content font:before {
  position: absolute;
  top: 50%;
  left: 0.7rem;
  margin-top: -0.5rem;
}

.order-product {
  margin-top: 0.5rem;
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  padding: 0.5rem 4rem 0.5rem 0.4rem;
  position: relative;
}

.order-product ul {
  width: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.order-product ul li {
  white-space: nowrap;
}

.order-product ul li img {
  width: 4rem;
  display: inline-block;
  margin-right: 0.5rem;
}

.order-product font:nth-of-type(2) {
  right: 1.4rem;
  font-size: 0.7rem;
  color: rgba(0, 0, 0, 0.7);
  margin-top: -0.5rem;
}

.order-product:before {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0.4rem;
  width: 0.5rem;
  content: '';
  z-index: 1;

  /* Webkit: Safari 5.1+, Chrome 10+ */

  /* Firefox 3.6+ */

  /* Opera 11.10+ */

  background: -o-linear-gradient(
    to right left,
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );

  /* IE 10 */

  background: -ms-linear-gradient(
    to right left,
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );
}

.order-product:after {
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 3.95rem;
  width: 2.5rem;
  content: '';

  /* Webkit: Safari 5.1+, Chrome 10+ */

  /* Firefox 3.6+ */

  /* Opera 11.10+ */

  background: -o-linear-gradient(
    to right left,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 1)
  );

  /* IE 10 */

  background: -ms-linear-gradient(
    to right left,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 1)
  );
}

.order-Delivery {
  margin-top: 0.5rem;
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  position: relative;
  overflow: hidden;
  font-size: 0.8rem;
}

.order-Delivery p {
  position: absolute;
  top: 0.5rem;
  left: 0.6rem;
  height: 1rem;
  line-height: 1rem;
}

.order-Delivery i {
  font-size: 1.5rem;
  vertical-align: text-top;
  color: rgba(0, 0, 0, 0.3);
  margin-right: 0.3rem;
}

.order-Delivery-content {
  float: right;
  text-align: right;
  color: rgba(0, 0, 0, 0.7);
  margin: 0.2rem 0.8rem 0.1rem 0.4rem;
}

.order-Delivery-content li {
  padding: 0 !important;
  color: rgba(0, 0, 0, 0.6) !important;
}

.order-Delivery-content li:last-child {
  color: rgba(255, 159, 0, 1) !important;
}

.order-Delivery .FilterContentList {
  position: initial;
  padding: 2rem 0 0.6rem 0.4rem;
  overflow: hidden;
}

.order-Delivery .order-Delivery-Pick {
  border-width: 1px 0 0 0;
  padding: 0.2rem 0.6rem;
}

.order-Delivery .order-Delivery-Pick li {
  border-width: 1px 0 0 0;
  line-height: 1.2rem;
  padding: 0.4rem 0;
  color: rgba(0, 0, 0, 0.4);
  font-size: 0.7rem;
}

.order-Delivery .list ul {
  margin-top: 0;
  border-width: 1px 0 0 0;
}

.order-Delivery-time {
  padding: 0.6rem;
  border-width: 1px 0 0 0;
}

.order-Delivery-time dd {
  height: 1rem;
  line-height: 1rem;
  padding: 0.2rem 0 0.5rem 0;
  vertical-align: bottom;
  color: rgba(255, 159, 0, 1);
}

.order-Delivery-time:active {
  background: rgba(0, 0, 0, 0.05);
}

.order-Delivery-time dd input {
  width: 49%;
  height: 1.5rem;
  line-height: 1.5rem;
  color: rgba(255, 159, 0, 1);
  font-size: 0.8rem;
  position: relative;
  background: rgba(0, 0, 0, 0.05);
}

.order-Delivery-time dd input:last-child {
  margin-left: 1%;
}

.order-Delivery-time dd input[type='time'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;
}

.order-Delivery-time dd input:before {
  position: absolute;
  right: 0.2rem;
  top: 0;
  font-size: 1.2rem;
  color: rgba(0, 0, 0, 0.2);
}

.order-Remark {
  margin-top: 0.5rem;
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  padding: 0.5rem 0.6rem;
  position: relative;
}

.order-Remark p {
  height: 1rem;
  line-height: 1rem;
  font-size: 0.8rem;
}

.order-Remark i {
  font-size: 1.2rem;
  vertical-align: text-top;
  color: rgba(0, 0, 0, 0.3);
  margin-right: 0.3rem;
}

.order-Remark samp {
  padding-right: 0.7rem;
  display: block;
}

.order-Remark textarea {
  width: 100%;
  padding: 0.05rem;
  margin-top: 0.3rem;
  line-height: 1rem;
  font-size: 0.7rem;
  border-radius: 0.2rem;
  border-width: 1px;
  -o-border-image: url('/img/web-order/circle-line.png') 10/5px;
  border-image: url('/img/web-order/circle-line.png') 10/5px;
}

.order-Remark textarea:focus {
  -o-border-image: url('/img/web-order/circle-line-hover.png') 10/5px;
  border-image: url('/img/web-order/circle-line-hover.png') 10/5px;
}

.order-Total-Price {
  margin-top: 0.5rem;
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  padding: 0.3rem 0.6rem;
  position: relative;
  font-size: 0.8rem;
}

.order-Total-Price li {
  overflow: hidden;
  line-height: 1.4rem;
}

.order-Total-Price li span {
  float: right;
  color: rgba(231, 5, 19, 1);
}

.order-Coupon {
  margin-top: 0.5rem;
  border-width: 1px 0 0 0;
  background: rgba(255, 255, 255, 1);
  position: relative;
}

.order-Coupon li {
  height: 2.5rem;
  line-height: 2.5rem;
  border-width: 0 0 1px 0;
  position: relative;
  padding: 0 0.6rem;
  font-size: 0.8rem;
}

.order-Coupon li:active i {
  color: rgba(255, 159, 0, 1);
}

.order-Coupon i {
  position: absolute;
  right: 0.3rem;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0.2);
}

.order-Coupon span {
  position: absolute;
  right: 1.4rem;
  font-size: 0.7rem;
}

.order-Coupon font {
  padding: 0.1rem 0.3rem;
  background: rgba(231, 5, 19, 1);
  color: rgba(255, 255, 255, 1);
  border-radius: 0.2rem;
  margin-left: 0.3rem;
  font-size: 0.6rem;
}

.order-Coupon p {
  display: inline-block;
  color: rgba(0, 0, 0, 0.3);
  margin-left: 0.5rem;
  font-size: 0.7rem;
}

.order-Coupon input[type='checkbox'] {
  margin-top: 0.9rem;
  width: 0.6rem;
  height: 0.6rem;
}

.order-Pro {
  display: inline-block;
  padding: 0 !important;
}

.order-Pro ul {
  margin-top: 0.5rem;
}

.order-Pro li {
  margin: 0;
  border-width: 1px 0 0 0;
}

.order-Pro li:last-child {
  border-width: 1px 0;
}

.order-Pro li:active {
  border-width: 1px 0 0 0;
}

.order-Pro li:last-child:active {
  border-width: 1px 0;
}

.order-Pro li i {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  margin-top: -0.5rem;
  background: rgba(255, 159, 0, 1);
  color: rgba(255, 255, 255, 1);
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  border-radius: 50%;
  display: none;
}

.order-Pro li.select i {
  display: inline-block;
}

/*.order-Pro .pro-img{margin-left: 0; width: 4.5rem;}*/
.order-Pro .pro-img {
  margin-left: 0;

  width: 80px;
}

.order-Pro .pro-con {
  padding-left: 4.5rem;
}

.order-Pro-Title {
  border-width: 1px 0 0 0;
  height: 2rem;
  line-height: 2rem;
  background: rgba(255, 159, 0, 0.05);
  color: rgba(231, 5, 19, 1);
  font-size: 0.7rem;
  padding: 0 0.5rem;
  margin-top: 0.5rem;
}

/*--我的--*/

/*--我的优惠券--*/
.Coupon-add {
  padding: 0.6rem;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 1);
  border-width: 0 0 1px 0;
}

.Coupon-add dt {
  height: 1rem;
  line-height: 1rem;
}

.Coupon-add dd {
  position: relative;
  padding: 0.5rem 4rem 0 0;
}

.Coupon-add input {
  padding: 0.5rem 0.3rem;
  font-size: 0.8rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 0.2rem;
  border: none;
  width: 100%;
  position: relative;
}

.Coupon-add button {
  width: 3rem;
  height: 1.95rem;
  line-height: 1.95rem;
  font-size: 1rem;
  border-radius: 0;
  position: absolute;
  right: 0;
  top: 0.5rem;
  border-radius: 0.2rem;
}

.Coupon-Content {
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  margin-top: 0.5rem;
}

.Coupon-Content-header {
  padding: 0.5rem 0;
  border-width: 0 0 1px 0;
}

.Coupon-Content-header a {
  width: 1%;
  display: table-cell;
  height: 1.5rem;
  line-height: 1.5rem;
  font-size: 0.8rem;
  text-align: center;
  border-width: 0 1px 0 0;
  position: relative;
}

.Coupon-Content-header a:active {
  color: rgba(255, 159, 0, 1);
}

.Coupon-Content-header a.active {
  color: rgba(255, 159, 0, 1);
}

.Coupon-Content-header a.active:before {
  border-bottom: 2px solid rgba(255, 159, 0, 1);
  content: '';
  width: 50%;
  position: absolute;
  bottom: -0.5rem;
  left: 25%;
}

.Coupon-Content-list {
  padding: 0.6rem;
}

.Couponbox {
  width: 100%;
  position: relative;
  height: 8.22rem;
  margin-bottom: 0.5rem;
}

.Couponbox-header {
  width: 100%;
  height: 5rem;
  border-radius: 0.3rem 0.3rem 0 0;
  background: rgba(53, 187, 189, 1) url('/img/web-order/Round.png') repeat-x
    bottom;
  background-size: 0.5rem;
  position: relative;
  overflow: hidden;
  color: rgba(255, 255, 255, 1);
  text-align: right;
  z-index: 1;
}

.Couponbox-header:before {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 1);
  position: absolute;
  left: -2rem;
  top: 50%;
  margin: -1.5rem 0 0 0;
  content: '';
}

.Couponbox-header:after {
  font-size: 12rem;
  color: rgba(255, 255, 255, 0.1);
  position: absolute;
  left: -3.5rem;
  top: -5rem;
  line-height: 15rem;
  content: '\e801';
  font-family: 'fontello';
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.Couponbox-header b {
  font-size: 4rem;
  line-height: 3rem;
  height: 3rem;
  vertical-align: bottom;
  margin: 0.8rem 0 0 0;
  display: inline-block;
  font-weight: inherit;
}

.Couponbox-header b sub {
  font-size: 1.5rem;
  line-height: 1.8rem;
  vertical-align: bottom;
}

.Couponbox-header p {
  display: inline-block;
  font-size: 1.45rem;
  line-height: 2.1rem;
  float: right;
  padding: 1rem 0.8rem 0 0.2rem;
}

.Couponbox-header p font {
  display: block;
  font-size: 1rem;
  line-height: 1rem;
  text-align: left;
  padding: 0 0 0 0.1rem;
}

.Couponbox-footer {
  position: absolute;
  top: 4.6rem;
  left: 0;
  right: 0;
  padding: 0.5rem 0.3rem 0 0.3rem;
  line-height: 1.2rem;
  font-size: 0.7rem;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 0 0 0.3rem 0.3rem;
  border-width: 0 1px 1px 1px;
  -o-border-image: url('/img/web-order/circle-line.png') 14/7px;
  border-image: url('/img/web-order/circle-line.png') 14/7px;
}

.Couponbox-footer span {
  color: rgba(255, 159, 0, 1);
}

/*--会员中心--*/
.member-header {
  width: 100%;
  overflow: hidden;
  height: 12rem;
  background: rgba(255, 255, 255, 1);
  position: relative;
}

.member-header .icon-waves {
  font-size: 8rem;
  line-height: 8rem;
}

.member-header .waves1 {
  position: absolute;
  bottom: 0;
  left: 0;
  color: #e3bf7a;
  z-index: 3;
  line-height: 7.3rem;
}

.member-header .waves2 {
  position: absolute;
  bottom: 0.35rem;
  left: -1.8rem;
  color: rgba(0, 172, 152, 0.6);
  z-index: 2;
  line-height: 7.3rem;
}

.member-header .waves3 {
  position: absolute;
  bottom: 0.7rem;
  left: 0rem;
  color: rgba(0, 172, 152, 0.2);
  z-index: 1;
  line-height: 7.3rem;
}

.member-header p {
  text-align: center;
  font-size: 1rem;
  display: block;
  padding: 0.5rem 0;
  position: relative;
  z-index: 4;
}

.member-header b {
  font-weight: inherit;
  display: block;
  font-size: 1rem;
  font-size: 0.8rem;
}

.member-header ul {
  width: 100%;
  height: 2rem;
  line-height: 1.1rem;
  position: relative;
  z-index: 4;
  margin: 0.7rem 0;
}

.member-header ul li {
  width: 1%;
  display: table-cell;
  text-align: center;
  color: rgba(255, 255, 255, 1);
  font-size: 0.7rem;
  border-width: 0 1px 0 0;
}

.member-header .button {
  background: transparent;
  width: 3rem;
  height: 2.5rem;
  position: fixed;
  z-index: 100;
  left: 0;
}

.member-header .return {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 2.1rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.2);
  display: inline-block;
  padding: 0;
  top: 0.5rem;
  left: 0.5rem;
  -webkit-box-shadow: 0 0 0 0.1rem rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 0 0.1rem rgba(0, 0, 0, 0.1);
}

.member-header .return:active {
  background: rgba(0, 0, 0, 0.5);
}

.member-header .return i {
  margin-right: 0.28rem;
}

.memberhead {
  width: 5rem;
  height: 5rem;
  overflow: hidden;
  border: 0.1rem solid rgba(255, 255, 255, 1);
  background: rgba(250, 250, 250, 1);
  border-radius: 50%;
  position: relative;
  z-index: 4;
  margin: 0 auto;
}

.memberhead img {
  width: 100%;
}

.memberhead i {
  font-size: 4.9rem;
  line-height: 5rem;
  color: rgba(0, 0, 0, 0.05);
}

.memberOrder-header {
  height: 2.55rem;
}

.memberOrder-nav {
  padding: 0.5rem 0;
  border-width: 0 0 1px 0;
  position: fixed;
  z-index: 2;
  background: rgba(255, 255, 255, 1);
}

.memberOrder-nav a {
  width: 1%;
  display: table-cell;
  height: 1.5rem;
  line-height: 1.5rem;
  font-size: 0.8rem;
  text-align: center;
  border-width: 0 1px 0 0;
  position: relative;
}

.memberOrder-nav a:active {
  color: rgba(255, 159, 0, 1);
}

.memberOrder-nav a.select {
  color: rgba(255, 159, 0, 1);
}

.memberOrder-nav a.select:before {
  border-bottom: 2px solid rgba(255, 159, 0, 1);
  content: '';
  width: 2.6rem;
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  margin-left: -1.3rem;
}

.memberOrder-list {
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  padding: 0 0.5rem;
  margin-top: 0.7rem;
}

.memberOrder-list p {
  height: 1.8rem;
  line-height: 1.8rem;
  font-size: 0.7rem;
}

.memberOrder-list p span {
  color: rgba(231, 5, 19, 1);
}

.memberOrder-list .order-product {
  margin: 0;
  padding: 0.6rem 0.6rem 0.6rem 0;
}

.memberOrder-list .order-product:before {
  left: 0;
}

.memberOrder-list .order-product:after {
  right: 0.6rem;
}

.memberOrder-list .order-product font:nth-of-type(1) {
  right: 0.5rem;
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.7);
  margin-top: -0.5rem;
}

.memberOrder-list button {
  padding: 0.35rem 0.6rem;
  border-radius: 0.2rem;
  font-size: 0.7rem;
  background: rgba(0, 0, 0, 0.05);
  vertical-align: middle;
  color: rgba(0, 0, 0, 0.5);
  float: right;
  margin: 0.08rem 0 0 0.2rem;
}

.memberOrder-list button:active {
  background: #e3bf7a;
  color: rgba(255, 255, 255, 1);
}

.memberOrder-list p:nth-child(1) button.icon-delete {
  background: transparent;
  font-size: 1rem;
  padding: 0.2rem 0.5rem;
}

.memberOrder-list p:nth-child(1) button.icon-delete:active {
  color: rgba(231, 5, 19, 1);
}

.memberDetailheader {
  padding: 0.6rem;
  background: rgba(255, 255, 255, 1);
  border-width: 0 0 1px 0;
  font-size: 0.8rem;
}

.memberDetaillist {
  padding: 0;
  /* margin-top: 0.5rem; */
}

.memberDetaillist ul {
  margin: 0;
  padding-bottom: 2.5rem;
}

.memberDetaillist .pro-con {
  font-size: 0.6rem;
}

.memberDetaillist .pro-con h3 {
  font-size: 0.8rem;
}

.memberDetaillist .pro-con p {
  padding-left: 0.12rem;
  display: inline-block;
}

.memberDetaillist button {
  padding: 0.35rem 0.6rem;
  border-radius: 0.2rem;
  font-size: 0.7rem;
  background: rgba(0, 0, 0, 0.05);
  vertical-align: middle;
  color: rgba(0, 0, 0, 0.5);
  margin-top: -0.8rem;
  position: absolute;
  right: 0.5rem;
  top: 50%;
}

.memberDetaillist button:active {
  background: #e3bf7a;
  color: rgba(255, 255, 255, 1);
}

.memberDetailPrice {
  border-width: 1px 0;
  background: rgba(255, 255, 255, 1);
  padding: 0.3rem 0;
  margin-top: 0.5rem;
  text-align: center;
  line-height: 1.3rem;
  margin-bottom: 2.5rem;
}

.memberDetailPrice p {
  font-size: 0.8rem;
}

.memberDetailPrice p:nth-of-type(2) {
  font-size: 1rem;
  color: rgba(231, 5, 19, 1);
}

.memberDetailPrice p:last-child {
  font-size: 0.6rem;
  color: rgba(0, 0, 0, 0.5);
}

.memberDetailPrice font {
  font-size: 0.6rem;
}

.memberDetailFooter {
  position: fixed;
  height: 2.5rem;
  line-height: 2.3rem;
  bottom: 0;
  left: 0;
  width: 100%;
  border-width: 1px 0 0 0;
  background: rgba(255, 255, 255, 0.98);
  text-align: right;
  vertical-align: middle;
}

/**原版*/
/*.memberDetailFooter button{padding: .5rem .8rem; border-radius: .2rem; font-size: .7rem; background: #107ABB; color: rgba(255,255,255,1); margin: 0 .2rem 0 0;}*/
.memberDetailFooter button {
  padding: 0.5rem 0.8rem;
  border-radius: 0.2rem;
  font-size: 0.7rem;
  /*background:  var(--styleColor);*/
  /*牛一的背景色*/
  background: var(--styleColor);
  color: rgba(255, 255, 255, 1);
  margin: 0 0.2rem 0 0;
}

.memberDetailFooter button:active {
  background: #73a86f;
  color: rgba(255, 255, 255, 1);
}

/*--弹出层--*/
.LayerContent {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 101;
  display: none;
  background: rgba(255, 255, 255, 1);
  min-height: 1rem;
}

.LayerHeader,
.LayerFooter {
  font-size: 0.8rem;
  height: 2rem;
  line-height: 2.1rem;
  padding: 0 0.5rem;
}

.LayerHeader {
  text-align: center;
  border-width: 0 0 1px 0;
}

.LayerHeader .Del {
  font-size: 0.7rem;
  height: 2rem;
  line-height: 2rem;
  padding: 0 0.5rem;
  color: rgba(231, 5, 19, 1);
  display: inline-block;
  position: absolute;
  right: 0;
  background: transparent;
}

.LayerHeader .Del i:before {
  font-size: 0.9rem;
  vertical-align: middle;
  margin: 0 0 0.1rem 0;
}

.LayerHeader .Del:active {
  color: rgba(231, 5, 19, 0.3);
}

.LayerHeader .head-r {
  height: 2rem;
  line-height: 2rem;
  color: rgba(0, 0, 0, 0.3) !important;
  font-size: 0.8rem;
  background: transparent;
}

.LayerHeader .head-r:active {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(231, 5, 19, 1) !important;
}

.LayerHeader button {
  height: 2rem;
  line-height: 2rem;
  color: rgba(0, 0, 0, 0.3);
}

.LayerHeader button:active {
  color: rgba(231, 5, 19, 1);
}

.LayerFooter {
  height: initial;
  padding: 0.4rem 0.5rem;
}

.LayerFooter span {
  color: rgba(231, 5, 19, 1);
}

.LayerFooter p {
  font-size: 0.6rem;
  line-height: 0.9rem;
  color: rgba(0, 0, 0, 0.5);
}

/*--购物车--*/
.cartContent {
  position: fixed;
  right: 0;
  left: 0;
  bottom: 2.5rem;
  z-index: 101;
  display: none;
  background: rgba(255, 255, 255, 1);
}

.cartContent .LayerHeader {
  border-width: 0 0 1px 0;
}

.cartContent .LayerFooter {
  padding: 0 0.5rem;
}

.cartContentList {
  padding: 0 0.5rem;
  max-height: 15rem;
  min-height: 3rem;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.cartContentList li {
  font-size: 0.7rem;
  line-height: 1rem;
  border-width: 0 0 1px 0;
  padding: 0.2rem 5rem 0.2rem 0;
  position: relative;
  overflow: hidden;
}

.cartContentList li img {
  width: 3rem;
  float: left;
  margin-right: 0.5rem;
}

.cartContentList li p {
  font-size: 0.7rem;
  display: block;
  margin-top: 0.2rem;
  width: 50%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.cartContentList li span {
  color: rgba(231, 5, 19, 1);
}

.cartContentList .D-BuyNum {
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -1.1rem;
}

.DetailContent:before,
.DeliverysContent:before {
  content: '';
  height: 1.1rem;
  position: fixed;
  top: 2rem;
  left: 0;
  width: 100%;
  z-index: 1;

  /* Webkit: Safari 5.1+, Chrome 10+ */

  /* Firefox 3.6+ */

  /* Opera 11.10+ */

  background: -o-linear-gradient(
    top,
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );

  /* IE 10 */

  background: -ms-linear-gradient(
    top,
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );
}

.DeliverysContent:after {
  content: '';
  height: 1.5rem;
  position: fixed;
  bottom: 2rem;
  left: 0;
  width: 100%;
  z-index: 1;

  /* Webkit: Safari 5.1+, Chrome 10+ */

  /* Firefox 3.6+ */

  /* Opera 11.10+ */

  background: -o-linear-gradient(
    top,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 1)
  );

  /* IE 10 */

  background: -ms-linear-gradient(
    top,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 1)
  );
}

/*--产品详情--*/
.DetailContent {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 31%;
  width: 100%;
  background: rgba(240, 240, 240, 1);
  display: none;
  z-index: 103;
}

.DetailContentBox {
  position: relative;
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.DetailContent:before {
  top: 0;
  height: 2.5rem;
  position: absolute;
}

.DetailContent .LayerFooter {
  height: 2.5rem;
  line-height: 2.5rem;
  background: rgba(0, 0, 0, 0.85);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  color: rgba(255, 255, 255, 0.6);
  padding: 0 0 0 0.5rem;
}

.DetailContent .LayerFooter button {
  height: 2.9rem;
  line-height: 2.5rem;
  padding: 0 0.5rem;
  font-size: 0.9rem;
  /*background: #107ABB;*/
  color: rgba(255, 255, 255, 1);
  float: right;
}

.DetailContent .LayerFooter button:active {
  background: #e3bf7a;
  color: rgba(255, 255, 255, 1);
}

.DetailContent .LayerFooter button:disabled {
  background: rgba(230, 230, 230, 1);
  color: rgba(0, 0, 0, 0.1);
}

.DetailContent .LayerFooter button:active:disabled {
  background: rgba(230, 230, 230, 1) !important;
  color: rgba(0, 0, 0, 0.1) !important;
}

.DetailContent .LayerHeader {
  height: 2.5rem;
  line-height: 2.5rem;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
}

.DetailContent .LayerHeader .button {
  background: transparent;
  width: 3rem;
  height: 2.9rem;
  position: absolute;
  right: 0;
}

.DetailContent .LayerHeader .button:active .close {
  background: rgba(0, 0, 0, 0.5);
}

.DetailContent .LayerHeader .close {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.45rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.2);
  display: inline-block;
  padding: 0;
  top: 0.5rem;
  right: 0.5rem;
  left: initial;
  -webkit-box-shadow: 0 0 0 0.1rem rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 0 0.1rem rgba(0, 0, 0, 0.1);
}

/*--配送点--*/
.DeliveryContent {
  position: fixed;
  right: 0;
  left: 0;
  top: 2.5rem;
  bottom: 0;
  z-index: 103;
  display: none;
  background: rgba(240, 240, 240, 0.95);
  padding: 0.5rem;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  font-size: 0.7rem;
}

.FilterContentList p {
  height: 1.5rem;
  line-height: 1.5rem;
  font-size: 0.8rem;
  padding: 0.4rem 0.2rem;
  display: block;
}

.FilterContentList ul {
  width: 100%;
  overflow: hidden;
}

.FilterContentList li {
  width: 33.333333%;
  display: inline-block;
  float: left;
  position: relative;
}

.FilterContentList li span {
  margin: 0.2rem;
  height: 1.5rem;
  line-height: 1.5rem;
  text-align: center;
  display: block;
  border-radius: 0.3rem;
  overflow: hidden;
  border-width: 1px;
  -o-border-image: url('/img/web-order/circle-line.png') 10/5px;
  border-image: url('/img/web-order/circle-line.png') 10/5px;
}

.FilterContentList li span.select {
  border-width: 1px;
  -o-border-image: url('/img/web-order/circle-line-hover.png') 10/5px;
  border-image: url('/img/web-order/circle-line-hover.png') 10/5px;
  color: rgba(255, 159, 0, 1);
  background: rgba(255, 159, 0, 0.05);
}

.FilterContentList li span.select:after {
  content: '';
  position: absolute;
  right: -0.75rem;
  top: -0.6rem;
  width: 0.5rem;
  height: 0.2rem;
  border: 0.6rem solid;
  border-color: transparent transparent transparent rgba(255, 159, 0, 1);
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.FilterContentList li span.select.icon-correct:before {
  font-size: 0.5rem;
  position: absolute;
  right: 0.25rem;
  top: -0.2rem;
  border-radius: 0 0.28rem 0 0;
  color: rgba(255, 255, 255, 1);
  z-index: 1;
}

/*--配送点说明--*/
.DeliverysContent {
  position: fixed;
  bottom: 0;
  min-height: 5rem;
  max-height: 80%;
  width: 100%;
  display: none;
  background: rgba(255, 255, 255, 1);
  z-index: 105;
}

.DeliverysContent .LayerHeader font {
  font-size: 1.25rem;
  vertical-align: sub;
  color: rgba(0, 0, 0, 0.3);
}

.DeliverysContent .LayerHeader span {
  color: rgba(255, 159, 0, 1);
}

.DeliverysContent .LayerFooter {
  height: 4rem;
  line-height: 4rem;
  text-align: center;
}

.DeliverysContent .LayerFooter button {
  font-size: 0.7rem;
  padding: 0.5rem 1rem;
  border-radius: 0.3rem;
  background: rgba(255, 159, 0, 1);
  color: rgba(255, 255, 255, 1);
}

.DeliverysContent .LayerFooter button:active {
  background: #e3bf7a;
}

.DeliverysContent:before {
  position: absolute;
}

.DeliverysContent:after {
  position: absolute;
  bottom: 4.8rem;
}

.DeliverysContentBox {
  position: relative;
  max-height: 13rem;
  padding: 0.5rem;
  font-size: 0.7rem;
  line-height: 1.5rem;
  text-align: center;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.DeliverysContentBox span {
  white-space: normal;
  display: inline-block;
  padding: 0 0.3rem;
}

/*--活动列表--*/
.Activity {
}

.Activity ul {
  padding: 0.5rem;
}

.Activity ul li {
  width: 100%;
}

.Activity ul li img {
  width: 100%;
  border-radius: 0.2rem;
}

/*--通知公告--*/
.Notice {
  border-width: 1px 0;
  background: rgba(250, 243, 232, 0.9);
  top: 2.5rem;
  left: 0;
  right: 0;
  height: 1.5rem;
  line-height: 1.5rem;
  z-index: 10;
  padding: 0 0.2rem;
  font-size: 0.6rem;
  vertical-align: middle;
  color: rgba(125, 60, 60, 1);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.Notice i {
  color: rgba(255, 60, 60, 1);
  font-size: 1rem;
  line-height: 0.6rem;
  vertical-align: text-top;
  margin-right: 0.2rem;
}

.Notice a {
  color: rgba(125, 60, 60, 1);
}

/*--订单支付--*/
.orderPay {
  position: fixed;
  width: 14rem;
  height: 12.4rem;
  background: rgba(255, 255, 255, 1);
  border-radius: 0.3rem;
  left: 50%;
  top: 50%;
  margin: -6.2rem 0 0 -7rem;
  display: none;
  z-index: 101;
}

.orderPay .list ul {
  margin: 0;
  border-width: 0 0 1px 0;
  background: transparent;
}

.orderPay .close {
  left: initial;
  right: 0;
}

/*-商品弹出框图片相对位置-*/
.img-position {
  text-align: center;
}

/*-homepage頁面，右上角中英文切換樣式-*/
.current-trans {
  font: 14px/1.5 Tahoma, Helvetica, Arial, '宋体', sans-serif;
  line-height: 2.5rem;
  /* position: absolute;
  right: 0px;

  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%); */
  /* padding: 15px; */
  /* padding-right: 0.5rem; */
}

/*-homepage頁面，枱號樣式-*/
.header h1 a {
  font: 14px/1.5 Tahoma, Helvetica, Arial, '宋体', sans-serif;
  color: #fff;
}

/*-homepage頁面，"選規格"按鈕樣式-*/
.single-btn {
  /*background-color:  var(--styleColor);*/
  /*牛一的背景色*/
  background-color: var(--styleColor);
  border: none;
  color: white;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font: 14px/1.5 Tahoma, Helvetica, Arial, '宋体', sans-serif;
  margin: 4px 2px;
  cursor: pointer;
  -webkit-transition-duration: 0.4s;
  /* Safari */
  -o-transition-duration: 0.4s;
  transition-duration: 0.4s;
  -webkit-box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2),
    0 6px 20px 0 rgba(0, 0, 0, 0.19);
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  float: right;
  width: 53px;
  border-radius: 15px;
}

/*-homepage頁面，"選規格"弹出框样式-*/
.glyphicon-plus {
  color: #ff9e02;
  font-size: 10px;
  margin-right: 5px;
}

/*-homepage頁面，"選規格"弹出框样式-*/
.glyphicon-plus-button {
  color: #fff;
}

/*-homepage頁面，"選規格"弹出框样式-*/
.label-item li {
  /* display: inline-block; */
  height: 30px;
  /* float: left; */
  /* line-height: 30px; */
  padding: 0 1rem;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  /* cursor: pointer; */

  color: #666;
  width: 5rem;
  margin-bottom: 3%;
  overflow: hidden;
  /*超出的部分隐藏起来。*/
  white-space: nowrap;
  /*不显示的地方用省略号...代替*/
}

/* .label-item li {
  margin-left: 0.5rem;

} */
/*-homepage頁面，"選規格"弹出框样式-*/
.label-item {
  /*border: 1px solid #e6e6e6;*/
  padding: 0.625rem 1.25rem;
  border-radius: 0 2px 2px 0;
  position: relative;
  overflow: hidden;
  background: #fff;
  padding-bottom: 3rem;
}

/*-homepage頁面，"選規格"弹出框样式-*/
/*.label-item .selected{*/
/*	color:#ccc;*/
/*}*/
.label-item .selected {
  color: white;
  background-color: var(--styleColor);
}

.label-item .selected .glyphicon-plus:before {
  content: '\002b';
  font-size: 19px;
  font-weight: bold;
  color: white;
}

/*-homepage頁面，"選規格"弹出框样式-*/
.item-span {
  font: 0.6rem ahoma, Helvetica, Arial, '宋体', sans-serif;
}

/*-homepage頁面，"選規格"弹出框样式-*/
.glyphicon-c {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glyphicon-plus:before {
  content: '\002b';
  font-size: 19px;
  font-weight: bold;
}

.glyphicon-plus {
  height: 100%;
  color: var(--styleColor);
  font-size: 10px;
  margin-right: 5px;
  line-height: 28px;
  /* display: flex;
                    align-items: center; */
}

.glyphicon-plus-button {
  color: #fff;
}

/***内容頁面--細項--加入購物車樣式以及特效***/
.single-order {
  float: right;
}

.add-to-cart {
  --color: #fff;
  --icon: var(--color);
  --cart: #fff;
  --dots: #fff;
  --background: var(--styleColor);
  --shadow: rgba(0, 9, 61, 0.16);
  cursor: pointer;
  position: relative;
  outline: none;
  border: none;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: transparent;
  font-size: 16px;
  border-radius: 22px;
  padding: 8px 10px;
  font-weight: 500;
  line-height: 20px;
  -webkit-transform: scale(var(--s, 0.97));
  -ms-transform: scale(var(--s, 0.97));
  transform: scale(var(--s, 0.97));
  -webkit-box-shadow: 0 var(--s-y, 4px) var(--s-b, 12px) var(--shadow);
  box-shadow: 0 var(--s-y, 4px) var(--s-b, 12px) var(--shadow);
  color: var(--color);
  background: var(--background);
  transition: box-shadow 0.3s, -webkit-transform 0.3s;
  -webkit-transition: -webkit-transform 0.3s, -webkit-box-shadow 0.3s;
  transition: -webkit-transform 0.3s, -webkit-box-shadow 0.3s;
  -o-transition: transform 0.3s, box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s,
    -webkit-box-shadow 0.3s;
  transition: transform 0.3s, box-shadow 0.3s, -webkit-transform 0.3s;
  /* margin-top: 4%; */
  border-radius: 1.5625rem !important;
  padding: 0 1rem;
}

.add-to-cart .default {
  padding-left: 16px;
  position: relative;
  opacity: var(--o, 1);
  -webkit-transform: scale(var(--s, 1));
  -ms-transform: scale(var(--s, 1));
  transform: scale(var(--s, 1));
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: transform 0.3s, opacity 0.3s;
  transition: transform 0.3s, opacity 0.3s;
  transition: transform 0.3s, opacity 0.3s, -webkit-transform 0.3s;
  -webkit-transition-delay: var(--d, 0.3s);
  -o-transition-delay: var(--d, 0.3s);
  transition-delay: var(--d, 0.3s);
}

.add-to-cart .default:before,
.add-to-cart .default:after {
  content: '';
  width: 2px;
  height: 12px;
  left: 0;
  top: 4px;
  border-radius: 1px;
  background: var(--icon);
  position: absolute;
  -webkit-transform: rotate(var(--r, 0deg));
  -ms-transform: rotate(var(--r, 0deg));
  transform: rotate(var(--r, 0deg));
  transition: -webkit-transform 0.25s;
  -webkit-transition: -webkit-transform 0.25s;
  -o-transition: transform 0.25s;
  transition: transform 0.25s;
  transition: transform 0.25s, -webkit-transform 0.25s;
}

.add-to-cart .default:after {
  --r: 90deg;
}

.add-to-cart .success {
  opacity: var(--o, 0);
  -webkit-transform: translate(-50%, var(--y, 12px));
  -ms-transform: translate(-50%, var(--y, 12px));
  transform: translate(-50%, var(--y, 12px));
  position: absolute;
  top: 12px;
  left: 50%;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
  -webkit-transition-delay: var(--d, 0s);
  -o-transition-delay: var(--d, 0s);
  transition-delay: var(--d, 0s);
}

.add-to-cart .dots {
  width: 4px;
  height: 4px;
  top: 20px;
  left: 50%;
  margin-left: -7px;
  border-radius: 2px;
  position: absolute;
  -webkit-transform-origin: 10px 50%;
  -ms-transform-origin: 10px 50%;
  transform-origin: 10px 50%;
  background: var(--dots);
  -webkit-box-shadow: 5px 0 0 var(--dots), 10px 0 0 var(--dots);
  box-shadow: 5px 0 0 var(--dots), 10px 0 0 var(--dots);
  opacity: var(--o, 0);
  -webkit-transform: scale(var(--s, 0.4));
  -ms-transform: scale(var(--s, 0.4));
  transform: scale(var(--s, 0.4));
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
  -webkit-transition-delay: var(--d, 0s);
  -o-transition-delay: var(--d, 0s);
  transition-delay: var(--d, 0s);
}

.add-to-cart .cart {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  border-radius: inherit;
  overflow: hidden;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

/* .add-to-cart .cart:before {
  content: '';
  position: absolute;
  width: 20px;
  height: 16px;
  background: var(--background);
  top: 17px;
  right: 100%;
  z-index: 1;
  margin-right: -20px;
  -webkit-transform: translateX(-18px) rotate(-16deg);
  transform: translateX(-18px) rotate(-16deg);
} */
.add-to-cart .cart > div {
  top: 13px;
  right: 100%;
  -webkit-transform: translateX(-18px) rotate(-16deg);
  -ms-transform: translateX(-18px) rotate(-16deg);
  transform: translateX(-18px) rotate(-16deg);
  position: absolute;
  z-index: 2;
  -webkit-transform-origin: 1px 21px;
  -ms-transform-origin: 1px 21px;
  transform-origin: 1px 21px;
}

/* .add-to-cart .cart > div:before,
.add-to-cart .cart > div:after {
  content: '';
  position: absolute;
  top: var(--t, 4px);
  left: var(--l, 0);
  height: 2px;
  width: var(--w, 18px);
  background: var(--cart);
  border-radius: 1px;
} */
.add-to-cart .cart > div:after {
  --w: 16px;
  --t: 14px;
  --l: 1px;
}

.add-to-cart .cart > div > div {
  width: 2px;
  height: var(--h, 15px);
  border-radius: 1px;
  -webkit-transform: rotate(var(--r, -8deg));
  -ms-transform: rotate(var(--r, -8deg));
  transform: rotate(var(--r, -8deg));
  background: var(--cart);
  position: relative;
}

.add-to-cart .cart > div > div:before,
.add-to-cart .cart > div > div:after {
  content: '';
  position: absolute;
  background: inherit;
}

.add-to-cart .cart > div > div:after {
  width: 4px;
  height: 4px;
  border-radius: 2px;
  bottom: var(--b, -6px);
  left: var(--l, 0);
}

.add-to-cart .cart > div > div:first-child:before {
  border-radius: inherit;
  top: 0;
  right: 0;
  height: 2px;
  width: 6px;
  -webkit-transform-origin: 5px 1px;
  -ms-transform-origin: 5px 1px;
  transform-origin: 5px 1px;
  -webkit-transform: rotate(16deg);
  -ms-transform: rotate(16deg);
  transform: rotate(16deg);
}

.add-to-cart .cart > div > div:last-child {
  --h: 12px;
  --r: 8deg;
  position: absolute;
  left: 16px;
  bottom: -1px;
}

.add-to-cart .cart > div > div:last-child:after {
  --l: -2px;
  --b: -5px;
}

.add-to-cart.added .default {
  --o: 0;
  --s: 0.8;
  --d: 0s;
}

.add-to-cart.added .default:before {
  --r: -180deg;
}

.add-to-cart.added .default:after {
  --r: -90deg;
}

.add-to-cart.added .dots {
  --o: 1;
  --s: 1;
  --d: 0.3s;
  -webkit-animation: dots 2s linear forwards;
  animation: dots 2s linear forwards;
}

.add-to-cart.added .success {
  --o: 1;
  --y: 0;
  --d: 1.8s;
}

.add-to-cart.added .cart:before,
.add-to-cart.added .cart > div {
  -webkit-animation: cart 2s forwards;
  animation: cart 2s forwards;
}

.add-to-cart:not(.added):hover {
  --s: 1;
  --s-y: 8px;
  --s-b: 20px;
}

.add-to-cart:not(.added):active {
  --s: 0.94;
  --s-y: 2px;
  --s-b: 6px;
}

/***内容頁面--細項--加入購物車樣式以及特效***/

/*中间的过度的横线*/
.link-top {
  width: 100%;
  height: 1px;
  border-top: solid #f0f0f0 2px;
}

/*細項彈出框圖片div樣式*/
.label-item-img {
  text-align: center;

  /* margin-bottom: 1rem; */
}

.prodImg {
  width: 6rem;
  height: 6rem;
  float: left;
  -webkit-box-shadow: 3px 3px 4px #000;
  box-shadow: 3px 3px 4px #000;
  margin: 0 0.4rem 0.1rem 0;
  border-radius: 50%;
}

/**彈出框-添加到購物車按鈕樣式*/
/*.single-order-footer{*/
/*	left: 0px;*/
/*	bottom: 0px;*/
/*	width: 100%;*/
/*	height: 50px;*/
/*	background-color: white;*/
/*	margin-bottom: -9%;*/
/*	position: relative;*/
/*}*/

.single-order-footer {
  position: -ms-device-fixed;
  bottom: 0;
  /* width: 100%; */
  background: rgba(0, 0, 0, 0.85);
  left: 0;
  height: 3rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  /* padding: 0 1rem; */
}

/* .single-order-footer-left {
  font-size: 18px;
  color:  var(--styleColor);
  margin-left: 0.3rem;
  padding: 0 2%;
} */

.single-order-footer-right {
  width: 12rem;
  margin-right: 0.3rem;
}

/* lladdit */
.products {
  font-size: 0.75rem;
  /* display: flex; */
  /* align-items: center; */
  /* margin-bottom: 1rem; */
}

.black_warp {
  float: left;
  margin-right: 1.5rem;
  height: 42px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.blackIcon {
  width: 1.5rem;
}

.addcartBtn {
  background-color: var(--styleColor);
  width: 12rem;
  height: 2rem;
  border-radius: 1rem;
  color: white;
  font-size: 1rem;
}

.list_itm_warp {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0 0.5rem 0 0;
  margin-bottom: 0.5rem;
}

.foodhead_warp {
  /* display: flex; */
  margin-bottom: 1rem;
}

.taste_warp {
  padding: 0.5rem 0rem;
  border-top: 1px solid #ccc;
  font-size: 0.7rem;
  color: var(--styleColor);
}

.buyNum_warp {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0.8rem 0rem;
  border-top: 1px solid #ccc;
}

.addSubtract {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.addbtn,
.delbtn {
  text-align: center;
  border-radius: 50%;
  -o-border-image: none;
  border-image: none;
  border: 1px solid var(--styleColor);
  padding: initial;
  width: 1.2rem;
  height: 1.2rem;
  line-height: 1.2rem;
  color: var(--styleColor);
  font-size: 0.8rem;
}

.numInput_warp {
  width: 1.5rem;
  font-size: 0.8rem;
  text-align: center;
  border: none;
}

.buy_text {
  font-size: 0.7rem;
  color: var(--styleColor);
}

#single-number {
  width: 1.5rem;
  text-align: center;
  border: 0;
  font-size: 0.8rem;
}

.clearfix:after {
  content: '';
  /*设置内容为空*/
  height: 0;
  /*高度为0*/
  line-height: 0;
  /*行高为0*/
  display: block;
  /*将文本转为块级元素*/
  visibility: hidden;
  /*将元素隐藏*/
  clear: both;
  /*清除浮动*/
}

.clearfix {
  zoom: 1;
  /*为了兼容IE*/
}

.cart_line_li h4 {
  font-size: 0.8rem;
}

.scalIcon_warp {
  position: fixed;
  top: 4rem;
  right: 0;
  padding: 0.3rem 0.5rem;
  border-radius: 3.125rem 0.3125rem 0.3125rem 3.125rem;
  background-color: var(--styleColor);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

/* #articleScale {
    background-color:  var(--styleColor);
    position: fixed;
    left: 5rem;
    bottom: 2.5rem;
    top: 50px;
    width: 1.3rem;
    z-index: 50;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
} */

.menu_2 {
  width: 100%;
}

/* 下單成功跳轉的layer樣式 */
.suclayer_content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100vh;
}

.suclayer_header {
  width: 100%;
  padding: 1rem 0;
  background-color: var(--styleColor);
  color: #fff;
  text-align: center;
}

.suclayer_header h4,
.suclayer_header p {
  padding: 0 1rem;
}

.suclayer_header h4 {
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.suclayer_header p {
  font-size: 0.8rem;
}

.suclayer_middle {
  padding: 2rem 1rem;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.suclayer_bcgstyle {
  width: 100%;
}

.suclayer_earn_warp {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.suclayer_earn_text {
  text-align: center;
  font-weight: 600;
  font-size: 0.8rem;
  padding: 1.5rem 0;
}

.suclayer_earn_button {
  text-align: center;
  width: 15rem;
  height: 1.8rem;
  line-height: 1.8rem;
  border-radius: 1rem;
  color: var(--styleColor);
  font-weight: 600;
  background-color: #ccc;
  font-size: 0.8rem;
}

.suclayer_footer_warp {
  width: 100%;
  height: 2rem;
  background-color: var(--styleColor);
  text-align: center;
  line-height: 2rem;
  font-weight: 600;
  color: #fff;
  border-radius: 2rem;
  font-size: 1rem;
}

body.modal-open {
  position: fixed;
  width: 100%;
}

/* .cart_line_li span {
} */
/* 牛一下单成功待跳转界面 */
.nobeSucceed_warp {
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  background-color: #fff;
  z-index: 20891014;
}

.nobeSucceed_warp_top {
  text-align: center;
  line-height: 2rem;
  width: 100%;
  height: 2rem;
  background-color: var(--styleColor);
  color: #fff;
}

.nobeSucceed_warp_cont {
  width: 100%;
  position: absolute;
  top: 20%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  text-align: center;
}

.nobeSucceed_warp_logo {
  padding-bottom: 1rem;
  width: 50%;
}

.li_warp {
  border: 1px solid red;
  overflow-y: auto;
  /* height: 100%; */
}
