/*
 *@desc 函数防抖
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param immediate true 表立即执行，false 表非立即执行
 */
function debounce(func, wait, immediate) {
  let timeout
  return () => {
    let args = arguments
    let context = this
    if (timeout) clearTimeout(timeout)
    if (immediate) {
      var callNow = !timeout
      timeout = setTimeout(() => {
        timeout = null
      }, wait)
      if (callNow) func.apply(context, args)
    } else {
      timeout = setTimeout(function () {
        func.apply(context, args)
      }, wait)
    }
  }
}

// 防抖
// function debounce(func, wait = 1000) { //可以放入项目中的公共方法中进行调用（鹅只是省事）
//   let timeout;
//   return function (event) {
//     clearTimeout(timeout)
//     timeout = setTimeout(() => {
//       func.call(this, event)
//     }, wait)
//   }
// }
