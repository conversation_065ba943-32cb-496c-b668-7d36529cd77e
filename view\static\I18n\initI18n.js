/**
 * @description: 初始化语言包 <网络请求>
 * @async
 * @param {String} prefix api前缀
 * @return: {Object} 语言包 || false
 * */
const initI18nPkg = function (prefix = "./") {
  const domain = window.location.host.split(".")[0]
  return fetch(`${prefix}./i18n/getAll?domain=${domain}`)
    .then(r => r.json())
    .then(res => {
      const { data, statusCode } = res
      if (statusCode === 200) {
        return data
      } else {
        return false
      }
    })
}
/**
 * @description: 通过url判断当前是哪一个页面
 * @return: { mark:String } 当前页面的标识
 * @example 1:  http://localhost:8080/index/  => { mark:"index" }
 * @example 2:  http://localhost:8080/payOrderPage.html  => { mark:"payOrderPage" }
 * */
function findPageMark() {
  let path = window.location.pathname
  let markList = [
    "map.html",
    "menuPage",
    "payOrderPage.html",
    "payOrderPage.html",
    "paySuccessPage.html",
    "payFailurePage.html",
    "historyIndex",
    "member-order.html",
    "privacyPage.html"
  ]
  let mark = null
  //通过url判断当前页面
  for (let i = 0; i < markList.length; i++) {
    if (path.indexOf(markList[i]) !== -1) {
      mark = markList[i].split(".")[0]
      break
    }
  }
  //若没有 ,则为index页(验证  /index/ 判断2个斜杠 )
  if (!mark) {
    if (path.split("/").length === 3) {
      mark = "index"
    }
  }
  return { mark }
}
/**
 * @description: 判断是否第一次进入页面
 * @return: { Boolean } true:是第一次进入页面  false:不是第一次进入页面
 * */
function isFirstLoad() {
  if (
    typeof window.performance !== "undefined" &&
    typeof window.performance.navigation !== "undefined" &&
    typeof window.performance.navigation.type !== "undefined"
  ) {
    return !!!performance.navigation.type
  } else {
    return performance.getEntriesByType("navigation")[0].type === "navigate"
  }
}
/**
 * @description 转换i18n中的结构
 * @example {label:'l',type:'t',value:{en:'aa',zh:'bb',...}} => { en:{t:aa},zh:{t:bb},...}
 * @return {Object} 转换后的i18n
 * */
function formatI18n(i18nPkg) {
  const i18n = {}
  for (let key in i18nPkg) {
    const { label, type, value } = i18nPkg[key]
    for (let lang in value) {
      if (!i18n[lang]) {
        i18n[lang] = {}
      }
      i18n[lang][type] = value[lang]
    }
  }
  return i18n
}

//立即执行函数
;(async function () {
  const isFirst = isFirstLoad() //是否第一次进入页面
  const PageMap = {
    index: "index",
    payOrderPage: "orderList",
    historyIndex: "orderList", //前端文件名为"member-order.html"
    menuPage: "menu",
    map: "location",
    paySuccessPage: "payDone",
    payFailurePage: "payFail",
    privacyPage: "payFail"
  }
  const Global = "global"
  let { mark } = findPageMark()
  const currentPage = PageMap[mark]
  const apiPrefix = currentPage === "index" ? "./" : "../"
  async function reloadI18n() {
    // 重新请求语言包
    let data = await initI18nPkg(apiPrefix)
    const worker = new Worker(apiPrefix + "./static/I18n/diffI18n.workers.js")
    const local = await fetch(apiPrefix + "./static/I18n/language.json").then(res => res.json())
    worker.postMessage({ local, remote: data, name: "byod-diff-i18n" })
    return new Promise((resolve, reject) => {
      worker.onmessage = e => {
        const { diff, remote } = e.data
        // 格式化diff数据
        data = remote
        for (const tab in diff) {
          let toMerge = diff[tab].map(el => {
            return {
              label: el.label,
              type: el.type,
              slots: el.slots,
              value: {
                en: el.en,
                zh: el.zh,
                thirdLan: el.thirdLan
              }
            }
          })
          data[tab] = Array.isArray(data[tab]) ? data[tab] : []
          data[tab].push(...toMerge)
        }
        sessionStorage.setItem("i18nPkg", JSON.stringify(data))
        try {
          window.i18n = formatI18n([...data[currentPage], ...data[Global]])
          resolve()
        } catch {
          // alert("Language pack initialization failed, please try again later")
          reject()
        }
      }
    })
  }
  const isIndex = mark === "index"
  /**
   * 以下代码:刷新获取最新语言包(reloadI18n)
  if (isFirst) {
    // 是第一次进入,判断是否有语言包
    const i18nPkg = sessionStorage.getItem("i18nPkg")
    if (i18nPkg) {
      // 有语言包(前一个页面保存的),直接使用
      try {
        const i18nPkgObj = JSON.parse(i18nPkg)
        window.i18n = formatI18n([...i18nPkgObj[currentPage], ...i18nPkgObj[Global] ])
        console.log("has i18nPkg --> don't load i18nPkg")
      } catch {
        await reloadI18n()
        console.log("parse i18nPkg error, reload i18nPkg")
      }
    } else {
      // 没有语言包(测试手痒删除(* ￣︿￣)),请求语言包
      await reloadI18n()
      console.log("first load page, load i18nPkg")
    }
  } else {
    // 不是第一次进入(刷新当前页面),获取最新语言包
    await reloadI18n()
    console.log("reload page, reload i18nPkg")
  }
   */
  // index页刷新获取最新语言包,其他页面使用sessionStorage中的语言包
  if (isIndex) {
    window.initI18n = async function () {
      return await reloadI18n()
    }
  } else {
    const i18nPkg = sessionStorage.getItem("i18nPkg")
    try {
      const i18nPkgObj = JSON.parse(i18nPkg)
      window.i18n = formatI18n([...i18nPkgObj[currentPage], ...i18nPkgObj[Global]])
    } catch {
      if (!i18nPkg) {
        if (mark === "paySuccessPage") {
          // window.close()
          await reloadI18n()
          // alert("The necessary parameters are missing, please rescan the QR code.")
        } else {
          alert("Please rescan QR code")
        }
      } else {
        alert("Language pack initialization failed, please try again later")
      }
    }
  }
})()
