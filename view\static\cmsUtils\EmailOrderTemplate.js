let labelI18n = {
  orderDateTime: {
    en: "Order Date",
    zh: "點餐日期",
    thirdLan: "Order Date"
  },
  address: {
    en: "Location",
    zh: "分店",
    thirdLan: "Location"
  },
  tableNumber: {
    en: "Table Number",
    zh: "枱號",
    thirdLan: "Table Number"
  },
  billNumber: {
    en: "Bill Number",
    zh: "訂單編號",
    thirdLan: "Bill Number"
  },
  paymentMethod: {
    en: "Payment Method",
    zh: "付款方式",
    thirdLan: "Payment Method"
  },
  totalAmount: {
    en: "Total Amount",
    zh: "總金額",
    thirdLan: "Total Amount"
  },
  yourOrder: {
    en: "Your Order",
    zh: "你的訂單",
    thirdLan: "Your Order"
  },
  total: {
    en: "Total",
    zh: "合計",
    thirdLan: "Total"
  },
  appointmentTime: {
    en: "Appointment Time",
    zh: "预约时间",
    thirdLan: "Appointment Time"
  }
}

function defaultTemplate(lan) {
  const widthStyle = lan === "zh" ? "100px" : "150px"

  return `
  <p class="placeholderLabel"><br /></p>
<div
  class="orderTemplate"
  style="
    max-width: 600px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  "
>
  <div style="margin-bottom: 20px; font-size: 14.5px">
    <p style="margin: 5px 0">
      <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
        ${labelI18n.orderDateTime[lan]}:
      </span>
      <span style="color: #555">#orderDateTime</span>
    </p>
    <p style="margin: 5px 0">
      <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
        ${labelI18n.address[lan]}:
      </span>
      <span style="color: #555">#address</span>
    </p>
    <p style="margin: 5px 0">
      <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
        ${labelI18n.tableNumber[lan]}:
      </span>
      <span style="color: #555">#tableNumber</span>
    </p>
    <p style="margin: 5px 0">
      <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
        ${labelI18n.billNumber[lan]}:
      </span>
      <span style="color: #555">#billNumber</span>
    </p>
    <p style="margin: 5px 0">
      <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
        ${labelI18n.paymentMethod[lan]}:
      </span>
      <span style="color: #555">#paymentMethod</span>
    </p>
    <p style="margin: 5px 0">
      <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
        ${labelI18n.totalAmount[lan]}:
      </span>
      <span style="color: #555">#currency#amt</span>
    </p>
  </div>
  <hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0" />
  <div>
    <div style="padding-bottom: 5px; margin-bottom: 10px">${labelI18n.yourOrder[lan]}</div>
    #orderStart #mainFoodStart
    <table style="width: 100%; margin-bottom: 5px; border-spacing: 0; color: unset">
      <tr>
        <td style="width: 50%; text-align: left; padding-right: 5px">
          <span>#mainFoodName</span>
        </td>
        <td style="width: 25%; text-align: center; padding-right: 5px">
          <span>#mainFoodFinalQty</span>
        </td>
        <td style="width: 25%; text-align: right">
          <span>#currency#mainFoodSinglePrice</span>
        </td>
      </tr>
    </table>
    #mainFoodEnd #itemFoodStart
    <table style="width: 100%; margin-bottom: 5px; border-spacing: 0; color: #818181">
      <tr>
        <td style="width: 50%; text-align: left; padding-right: 5px; padding-left: 10px">
          <span>#itemFoodName</span>
        </td>
        <td style="width: 25%; text-align: center; padding-right: 5px">
          <span>#itemFoodFinalQty</span>
        </td>
        <td style="width: 25%; text-align: right">
          <span>#currency#itemFoodSinglePrice</span>
        </td>
      </tr>
    </table>
    #itemFoodEnd #orderEnd
  </div>
  <hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0" />
  <div style="text-align: end; color: #3e3e3e">
    <strong style="margin-right: 10px; font-size: 15px">${labelI18n.total[lan]}</strong>
    <strong>#currency#totalPrice</strong>
  </div>
  <div style="text-align: end; margin-top: 5px; font-size: 18px">
    <strong style="margin-right: 10px">${labelI18n.totalAmount[lan]}</strong>
    <strong>#currency#amt</strong>
  </div>
</div>
<p class="placeholderLabel"><br /></p>

`
}

function pickUpTemplate(lan) {
  const widthStyle = lan === "zh" ? "100px" : "150px"

  return `
  <p><br></p>
  <div
    class="orderTemplate"
    style="
      max-width: 600px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    "
  >
    <div style="margin-bottom: 20px">
      <p style="margin: 5px 0">
        <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
          ${labelI18n.orderDateTime[lan]}:
        </span>
        <span style="color: #555">#orderDateTime</span>
      </p>
       <p style="margin: 5px 0">
        <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
          ${labelI18n.address[lan]}:
        </span>
        <span style="color: #555">#address</span>
      </p>
      <p style="margin: 5px 0">
        <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
          ${labelI18n.tableNumber[lan]}:
        </span>
        <span style="color: #555">#tableNumber</span>
      </p>
      <p style="margin: 5px 0">
        <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
          ${labelI18n.appointmentTime[lan]}:
        </span>
        <span style="color: #555">#pickupTime</span>
      </p>
      <p style="margin: 5px 0">
        <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
          ${labelI18n.paymentMethod[lan]}:
        </span>
        <span style="color: #555">#paymentMethod</span>
      </p>
      <p style="margin: 5px 0">
        <span style="display: inline-block; width: ${widthStyle}; color: #000; font-weight: bold">
          ${labelI18n.totalAmount[lan]}:
        </span>
        <span style="color: #555">#currency#amt</span>
      </p>
    </div>
    <hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0" />
    <div>
      <div style="padding-bottom: 5px; margin-bottom: 10px">${labelI18n.yourOrder[lan]}</div>
     #orderStart #mainFoodStart
      <table style="width: 100%; margin-bottom: 5px; border-spacing: 0; color: unset">
        <tr>
          <td style="width: 50%; text-align: left; padding-right: 5px">
            <span>#mainFoodName</span>
          </td>
          <td style="width: 25%; text-align: center; padding-right: 5px">
            <span>#mainFoodFinalQty</span>
          </td>
          <td style="width: 25%; text-align: right">
            <span>#currency#mainFoodSinglePrice</span>
          </td>
        </tr>
      </table>
      #mainFoodEnd #itemFoodStart
      <table style="width: 100%; margin-bottom: 5px; border-spacing: 0; color: #818181">
        <tr>
          <td style="width: 50%; text-align: left; padding-right: 5px; padding-left: 10px">
            <span>#itemFoodName</span>
          </td>
          <td style="width: 25%; text-align: center; padding-right: 5px">
            <span>#itemFoodFinalQty</span>
          </td>
          <td style="width: 25%; text-align: right">
            <span>#currency#itemFoodSinglePrice</span>
          </td>
        </tr>
      </table>
      #itemFoodEnd #orderEnd
    </div>
    <hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0" />
    <div style="text-align: end; color: #3e3e3e">
      <strong style="margin-right: 10px; font-size: 15px">${labelI18n.total[lan]}</strong>
      <strong>#currency#totalPrice</strong>
    </div>
    <div style="text-align: end; margin-top: 5px; font-size: 18px">
      <strong style="margin-right: 10px">${labelI18n.totalAmount[lan]}</strong>
      <strong>#currency#amt</strong>
    </div>
  </div>
  <p><br></p>
  `
}
