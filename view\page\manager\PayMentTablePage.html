<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>

    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/moment/moment.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>
    <script src="../../static/elementUI/locale/zh.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>

    <style>
      html,
      body {
        padding: 0;
        margin: 0;
      }

      p {
        margin: 0;
      }

      #app {
        padding: 20px 15px;
      }

      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .header-box {
        display: flex;
        position: relative;
        /* margin: 20px 0; */
      }

      @media (max-width: 1200px) {
        .el-col-lg-6 {
          flex-basis: 50%;
          max-width: 50%;
        }
      }

      @media (max-width: 768px) {
        .el-col-lg-6 {
          flex-basis: 100%;
          max-width: 100%;
        }
      }
      .el-table th.el-table__cell > .cell {
        white-space: nowrap;
      }
      .orderIcon {
        font-size: 25px;
        color: #a11f24;
      }
      .emailIcon {
        font-size: 25px;
        color: #409eff;
      }
      .order-time {
        font-size: 18px;
        font-weight: bold;
        padding-bottom: 25px;
      }
      .order-info {
        display: flex;
        /* flex-direction: column; */
        justify-content: space-between;
        flex-wrap: wrap;
        overflow: auto;
        max-height: 45vh;
        color: rgba(0, 0, 0, 0.7);
      }
      /* .order-info 下的div每个宽度50% */
      .order-info > div {
        width: 50%;
        padding-bottom: 20px;
      }
      .order-time {
        font-size: 18px;
        font-weight: bold;
      }
      .order-number,
      .reference-number,
      .order-amount,
      .pickup-date,
      .discount-col,
      .order-foods {
        font-size: 15.5px;
      }
      .order-foods {
        display: flex;
        width: 100% !important;
      }
      .order-foods-lable {
        flex-shrink: 0;
      }
      .dialog-footer {
        text-align: right;
      }
      /* .order-info-cell 下的第一个span浅灰色,第二个span */
      .order-info-cell > span:first-child {
        color: #999;
      }
      .order-info-cell > span:last-child {
        /* font-weight: bold; */
      }

      .littleitem {
        font-size: 14px;
        color: #909399;
        /* margin: 0.1rem 0rem 0.1rem 0.2rem; */
      }
      .littleitem p {
        margin-top: 2px;
        margin-bottom: 2px;
      }
      .littleitem-inXi {
        margin-left: 0.2rem;
      }
      .littleitem-endXi {
        margin-left: 0.4rem;
      }
      .order-info .discount-col {
        width: fit-content;
        display: flex;
        justify-content: space-between;
      }
      .discount-col span:nth-child(even) {
        min-width: fit-content;
        text-align: end;
        align-self: center;
        margin-left: 30px;
      }
      .discount-col span span {
        margin-left: 0.2rem;
      }
      .order-foods-info {
        margin: 0rem 10px;
      }
      .order-foods-info-foodTitle {
        font-size: 16px;
      }
      .order-foods-info-foodSideDish {
        margin-top: 5px;
        margin-left: 8px;
      }
      .order-card {
        min-height: 45vh;
      }
      .order-foods-detail {
        display: flex;
        flex-direction: column;
      }
      .export-table-box {
        display: flex;
        justify-content: end;
        margin-bottom: 10px;
      }
      .coding-type-radio-box {
        display: flex;
        flex-direction: column;
        width: 90%;
        padding-left: 20px;
      }
      .coding-type-radio-box label {
        margin-bottom: 10px;
      }
      .custom-coding-input input {
        outline: none;
        border: none;
        border-bottom: 1px solid rgb(220, 223, 230);
        border-radius: unset;
        height: 30px;
        line-height: 30px;
        padding: 0;
      }
      .refundImg {
        cursor: pointer;
      }
      .el-table th > .cell {
        white-space: nowrap; /* 文本在一行显示，不换行 */
        text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本。*/
        overflow: hidden; /* 超出部分隐藏 */
      }
      .payMent-table-expand {
        display: flex;
        /*justify-content: space-around;*/
        width: 100%;
        flex-wrap: wrap;
      }

      .payMent-table-expand label {
        /* width: 90px; */
        color: #99a9bf;
        white-space: nowrap;
      }

      .payMent-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
        padding-left: 40px;
        display: flex;
        align-items: center;
      }

      .payMent-table-expand .el-form-item .el-form-item__content {
        height: 100%;
        display: flex;
        align-items: center;
        line-height: normal;
      }
      .textRed {
        color: #ff4d4f;
      }
    </style>
  </head>

  <body>
    <div id="app" class="test" v-loading="lanLoading">
      <template>
        <el-backtop v-if="tableHeight" target=".el-table__body-wrapper" :visibility-height="10">
          UP
        </el-backtop>
        <template>
          <div class="header-box" ref="headerBox">
            <el-form
              ref="searchForm"
              :model="searchForm"
              :inline="true"
              class="searchForm"
              size="small"
            >
              <el-row :gutter="0" class="form-row">
                <el-col :span="colSpan" v-for="(item, index) in searchItems" :key="index">
                  <el-form-item
                    :label="i18n[locale][item.prop]"
                    :prop="item.prop"
                    label-width="auto"
                  >
                    <el-input
                      clearable
                      v-model="searchForm[item.prop]"
                      :placeholder="i18n[locale][item.prop+'Ph']"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="colSpan" v-for="(item, index) in selectItems" :key="index">
                  <el-form-item
                    :label="i18n[locale][item.prop]"
                    :prop="item.prop"
                    label-width="auto"
                  >
                    <el-select
                      v-if="item.prop=='payStatus'"
                      v-model="searchForm[item.prop]"
                      :placeholder="i18n[locale][item.prop+'Ph']"
                      clearable
                    >
                      <el-option
                        :label="i18n[locale][option.label]"
                        :value="option.value"
                        v-for="(option, index) in item.options"
                        :key="index"
                      ></el-option>
                    </el-select>
                    <el-cascader
                      v-else
                      :placeholder="i18n[locale][item.prop+'Ph']"
                      clearable
                      :options="item.options"
                      :props="{ checkStrictly: true }"
                      @change="paymentChange"
                    ></el-cascader>
                  </el-form-item>
                </el-col>
                <el-col :span="colSpan" v-for="(item, index) in dateItems" :key="index">
                  <el-form-item
                    :label="i18n[locale][item.prop]"
                    :prop="item.prop"
                    label-width="auto"
                  >
                    <el-date-picker
                      v-model="searchForm[item.prop]"
                      :type="item.type"
                      :placeholder="i18n[locale][item.prop+'Ph']"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="colSpan" class="form-btn">
                  <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="getSearch">
                      {{i18n[locale]['search']}}
                    </el-button>
                    <el-button @click="resetForm('searchForm')">
                      {{i18n[locale]['reset']}}
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </template>
        <!-- 导出数据弹窗选项 -->
        <template>
          <el-dialog
            :title="i18n[locale]['exportDiaTitle']"
            :visible.sync="exportDialogVisible"
            @close="exportDiaClose"
          >
            <div class="coding-type-radio-box">
              <el-radio
                v-model="encodingTypeRadio"
                :label="item.label"
                v-for="item in encodingTypeOptions"
                :key="item.value"
              >
                {{item.label}}
              </el-radio>
              <el-radio v-model="encodingTypeRadio" label="customStr">
                <el-input
                  v-model="customCodingInput"
                  class="custom-coding-input"
                  @focus="encodingTypeRadio = 'customStr'"
                ></el-input>
              </el-radio>
            </div>

            <span slot="footer" class="dialog-footer">
              <el-button @click="exportDialogVisible = false">
                {{i18n[locale]['cancelBtn']}}
              </el-button>
              <el-button style="margin-right: 8px" type="primary" @click="exportSubmit">
                {{i18n[locale]['confirmBtn']}}
              </el-button>
            </span>
          </el-dialog>
        </template>
        <template>
          <!-- 导出表格数据 -->
          <div class="export-table-box">
            <el-button
              size="small"
              type="success"
              icon="el-icon-download
              "
              @click="exportDialogVisible=true"
              style="margin-right: 10px"
            >
              {{i18n[locale]['export']}}
            </el-button>
            <el-dropdown @command="handleI18n">
              <el-button size="small" type="primary" plain class="i18n-btn">
                Language / 語言
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="en">English</el-dropdown-item>
                <el-dropdown-item command="zh">繁體</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <el-table
            v-if="tableHeight"
            :data="tableData"
            style="width: 100%"
            :max-height="tableHeight"
            border
            id="exportTable"
            ref="report-table"
            :empty-text="i18n[locale]['noData']"
            class="tableContent"
            v-loading="tableLoading"
          >
            <el-table-column type="expand" :fixed="true">
              <template slot-scope="props">
                <el-form label-position="left" class="payMent-table-expand">
                  <el-form-item
                    :label="i18n[locale][item.prop]"
                    v-for="(item,index) in expandTableColumn"
                    class=""
                  >
                    <span>{{ props.row[item.prop]}}</span>
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column
              :fixed="item.fixed"
              :prop="item.prop"
              :label="i18n[locale][item.prop]"
              :width="item.width"
              v-for="(item,index) in tableColumn"
              align="center"
            >
              <template slot="header" slot-scope="scope">
                <el-tooltip :content="i18n[locale][item.prop]" placement="top" effect="light">
                  <span>{{i18n[locale][item.prop]}}</span>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-tag
                  v-if="item.prop=='payStatus'"
                  :type="toDealWithPayStatus(scope.row.payStatus,'tag')"
                >
                  {{i18nPayStatus(scope.row.payStatus)}}
                </el-tag>
                <span v-else-if="item.prop=='createDatetimeUTC'">
                  {{scope.row[item.prop]|momentUTC }}
                </span>
                <div v-else-if="item.prop=='billNumber'">
                  <i class="el-icon-s-order orderIcon" @click="getOrder(scope.row)"></i>
                  <p style="margin: 0" :class="{'textRed':scope.row.orderStatus==4}">
                    {{scope.row.billNumber }}
                  </p>
                </div>
                <div v-else-if="item.label=='Email/Phone'">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="scope.row.email"
                    placement="top"
                    v-if="scope.row.email"
                  >
                    <i class="el-icon-message emailIcon"></i>
                  </el-tooltip>
                  <p style="margin: 0">{{scope.row.customerPhone }}</p>
                </div>
                <div v-else-if="item.label=='Refund'&&showRefund(scope.row)">
                  <el-popconfirm
                    title="Whether the refund is confirmed?"
                    icon-color="red"
                    @confirm="onRefund(scope.row)"
                  >
                    <img
                      src="../../static/img/svg/refund.svg"
                      alt=""
                      class="refundImg"
                      slot="reference"
                    />
                  </el-popconfirm>
                </div>
                <span v-else>{{scope.row[item.prop]}}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>

        <template>
          <el-dialog
            :visible.sync="orderDialogVisible"
            :title="i18n[locale]['orderDiaTitle']"
            @closed="handleOrderDiaClose"
          >
            <el-card class="order-card" v-loading="orderLoading">
              <div slot="header" class="clearfix">
                <div class="order-time">{{ payOrderObj?.date }}</div>
              </div>
              <template v-if="payOrderObj">
                <div class="order-info">
                  <div class="order-number order-info-cell">
                    <span>{{i18n[locale]['billNumber']}} :</span>
                    <span>{{ payOrderObj.billNumber }}</span>
                  </div>
                  <div class="reference-number order-info-cell">
                    <span>{{i18n[locale]['referenceNumber']}} :</span>
                    <span>{{ payOrderObj.merchantRef }}</span>
                  </div>
                  <div class="order-amount order-info-cell">
                    <span>{{i18n[locale]['orderAmount']}} :</span>
                    <span>{{payOrderObj.amtType}}{{ payOrderObj.amt }}</span>
                  </div>
                  <div class="pickup-date order-info-cell">
                    <span>{{i18n[locale]['pickupDate']}} :</span>
                    <span>
                      {{ payOrderObj.pickupTime?payOrderObj.pickupTime:i18n[locale]['immediately']
                      }}
                    </span>
                  </div>
                  <div class="order-info-cell discount-col" v-if="getDiscountInfo(payOrderObj)">
                    <span>
                      {{i18n[locale]['discount']}} :
                      <span>{{getDiscountInfo(payOrderObj).desc}}</span>
                    </span>
                    <span>
                      -{{payOrderObj.amtType||'$'}}{{getDiscountInfo(payOrderObj).amount}}
                    </span>
                  </div>
                  <div class="order-foods order-info-cell">
                    <span class="order-foods-lable">{{i18n[locale]['foodDetails']}} :</span>
                    <div class="order-foods-detail">
                      <div
                        class="order-foods-info"
                        v-for="foodItem in payOrderObj.orderData"
                        :key="foodItem.fCode"
                      >
                        <div class="order-foods-info-foodTitle">
                          <span>{{inListTitle(foodItem,'en')}}</span>
                          <span>({{inListTitle(foodItem,'zh')}})</span>
                        </div>
                        <div
                          class="order-foods-info-foodSideDish"
                          v-if="hasNewOrderItemData(foodItem)"
                        >
                          <div v-html="showlogic(foodItem,'xi')" class="littleitem">
                            {{showlogic(foodItem,'xi')}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="order-info-cell order-foods"
                    v-if="payOrderObj.promotionFoodList&&payOrderObj.promotionFoodList.length"
                  >
                    <span>{{i18n[locale]['giveAwayList']}} :</span>
                    <div class="order-foods-detail" style="margin-left: 10px">
                      <div v-html="showGiveAway(payOrderObj)" class="littleitem"></div>
                    </div>
                  </div>
                </div>
              </template>

              <div class="no-order-data" v-else><el-empty description="No Data"></el-empty></div>
            </el-card>
          </el-dialog>
        </template>
      </template>

      <!-- <div @click="onScroll">哈哈</div> -->
    </div>
    <script>
      var app = new Vue({
        el: "#app",
        filters: {
          momentUTC: (value, formatString) => {
            let { value: TimeZone } = JSON.parse(sessionStorage.getItem("localTimeZone"))
            formatString = formatString || "YYYY-MM-DD HH:mm:ss"
            if (value) {
              // 将时间格式数据转化为moment对象
              const date = moment(value)
              // 将时区数据格式转化为分钟数
              const timeZoneOffset = moment.duration(TimeZone).asMinutes()
              // 将时间转化为对应时区的时间
              const dateInTimeZone = date.add(timeZoneOffset, "minutes")
              // 将时间转化为对应格式
              return moment(dateInTimeZone).format(formatString)
            }
          }
        },
        data: {
          tableHeight: 0,
          date: "",
          domain: sessionStorage.getItem("domain"),
          searchForm: {
            storeNumber: "",
            tableNumber: "",
            billNumber: "",
            merchantRef: "",
            payType: "",
            payCompany: "",
            payStatus: "",
            transactionID: "",
            email: "",
            customerPhone: "",
            startCreateDatetimeUTC: "",
            endCreateDatetimeUTC: ""
          },
          searchItems: [
            {
              label: "Store Number",
              prop: "storeNumber",
              placeholder: "Please enter store number"
            },
            {
              label: "Table Number",
              prop: "tableNumber",
              placeholder: "Please enter table number"
            },
            { label: "BillNumber", prop: "billNumber", placeholder: "Please enter bill number" },
            { label: "MerchantRef", prop: "merchantRef", placeholder: "Please enter merchantRef" },
            {
              label: "transactionID",
              prop: "transactionID",
              placeholder: "Please enter transaction ID"
            },
            {
              label: "Email",
              prop: "email",
              placeholder: "Please enter email"
            },
            {
              label: "Phone",
              prop: "customerPhone",
              placeholder: "Please enter phone"
            }
          ],
          selectItems: [
            {
              label: "Payment Method",
              prop: "payType",
              placeholder: "Please enter payment method",
              options: [
                { label: "Wallet", value: "wallet" },
                { label: "iPay88", value: "iPay88" },
                { label: "Razer", value: "razer" },
                {
                  label: "EFT",
                  value: "EFT",
                  children: [
                    { label: "VM", value: "VM" },
                    { label: "VISA", value: "VISA" },
                    { label: "MASTERCARD", value: "MASTERCARD" },
                    { label: "UNIONPAY", value: "UNIONPAY" },
                    { label: "FPS", value: "FPS" },
                    { label: "ALIPAYCN", value: "ALIPAYCN" },
                    { label: "ALIPAYHK", value: "ALIPAYHK" },
                    { label: "WECHAT", value: "WECHAT" },
                    { label: "OCTOPUS", value: "OCTOPUS" },
                    { label: "PAYME", value: "PAYME" }
                  ]
                },
                {
                  label: "BOC",
                  value: "boc",
                  children: [
                    { label: "ALL", value: "ALL" },
                    { label: "BOCPAY", value: "BOCPAY" },
                    { label: "ALIPAY", value: "ALIPAY" },
                    { label: "ALIPAYMO", value: "ALIPAYMO" },
                    { label: "WECHATPAY", value: "WECHATPAY" },
                    { label: "TAIFUNGPAY", value: "TAIFUNGPAY" },
                    { label: "ICBCEPAY", value: "ICBCEPAY" },
                    { label: "MPAY", value: "MPAY" },
                    { label: "LUSOPAY", value: "LUSOPAY" },
                    { label: "UEPAY", value: "UEPAY" },
                    { label: "CGBPAY", value: "CGBPAY" },
                    { label: "BNUAPP", value: "BNUAPP" },
                    { label: "MPGS", value: "MPGS" },
                    { label: "UNIONPAYQR", value: "UNIONPAYQR" }
                  ]
                },
                {
                  label: "Windcave",
                  value: "Windcave",
                  children: [
                    { label: "card", value: "card" },
                    { label: "account2account", value: "account2account" },
                    { label: "alipay", value: "alipay" },
                    { label: "applepay", value: "applepay" },
                    { label: "googlepay", value: "googlepay" },
                    { label: "paypal", value: "paypal" },
                    { label: "interac", value: "interac" },
                    { label: "unionpay", value: "unionpay" },
                    { label: "oxipay", value: "oxipay" },
                    { label: "visacheckout", value: "visacheckout" },
                    { label: "wechat", value: "wechat" }
                  ]
                }
              ]
            },
            {
              label: "Payment Status",
              prop: "payStatus",
              placeholder: "Please select a payment status",
              options: [
                { label: "Created", value: 1 },
                { label: "Paid", value: 2 },
                { label: "Cancelled", value: 3 },
                { label: "TimedOut", value: 4 },
                { label: "RefundApplied", value: 5 }
              ]
            }
          ],
          dateItems: [
            {
              label: "Start Date",
              prop: "startCreateDatetimeUTC",
              placeholder: "Select date and time",
              type: "datetime"
            },
            {
              label: "End Date",
              prop: "endCreateDatetimeUTC",
              placeholder: "Select date and time",
              type: "datetime"
            }
          ],
          tableData: [],

          tableColumn: [
            {
              label: "Store Number",
              prop: "storeNumber",
              fixed: true
            },
            {
              label: "Table Number",
              prop: "tableNumber",
              fixed: true
            },
            {
              label: "Customer Name",
              prop: "customerName",
              width: "",
              fixed: true
            },
            {
              label: "Email/Phone",
              prop: "emailPhone",
              width: ""
            },
            {
              label: "Date Time",
              prop: "createDatetimeUTC",
              width: ""
            },
            {
              label: "BillNumber",
              prop: "billNumber",
              width: "",
              fixed: false
            },
            {
              label: "MerchantRef",
              prop: "merchantRef",
              fixed: false
            },
            {
              label: "Amt",
              prop: "amt",
              width: "90",
              fixed: false
            },
            {
              label: "Payment Method",
              prop: "payType",
              fixed: false
            },
            {
              label: "Payment Status",
              prop: "payStatus",
              fixed: false,
              isScope: false
            },
            {
              label: "Refund",
              prop: "refund",
              width: "80",
              fixed: false,
              isScope: false
            }
          ],
          expandTableColumn: [
            {
              label: "transaction ID",
              prop: "transactionID"
            }
          ],
          payTypeObj: [
            "VM",
            "UNIONPAY",
            "FPS",
            "ALIPAYHK",
            "ALIPAYCN",
            "WECHAT",
            "OCTOPUS",
            "PAYME"
          ],
          payStatusObj: [
            { label: "Created", value: 1 },
            { label: "Paid ", value: 2 },
            { label: "Cancelled", value: 3 },
            { label: "TimedOut ", value: 4 },
            { label: "RefundApplied ", value: 5 }
          ],
          payOrderObj: null,
          orderDialogVisible: false,
          orderLoading: false,
          tableLoading: false,
          lanLoading: false,
          diaTop: "15vh",
          exportDialogVisible: false,
          encodingTypeOptions: [
            { label: "GBK", value: "GBK" },
            { label: "x-windows-950", value: "x-windows-950" }
          ],
          encodingTypeRadio: "GBK",
          customCodingInput: "",
          copyFilterPam: "", //用于导出时候的参数
          ARPM: ["razer", "boc"], //allowRefundPaymentMethods 允许退款的支付方式
          refundList: [], //允许退款的店铺列表
          locale: "en",
          i18n: {
            en: {
              storeNumber: "Store Number",
              storeNumberPh: "Please enter store number",
              tableNumber: "Table Number",
              tableNumberPh: "Please enter table number",
              billNumber: "Bill Number",
              billNumberPh: "Please enter bill number",
              merchantRef: "MerchantRef",
              merchantRefPh: "Please enter merchantRef",
              transactionID: "Transaction ID",
              transactionIDPh: "Please enter transaction ID",
              email: "Email",
              emailPh: "Please enter email",
              customerPhone: "Phone",
              customerPhonePh: "Please enter phone",
              payType: "Payment Method",
              payTypePh: "Please enter payment method",
              payStatus: "Payment Status",
              payStatusPh: "Please select a payment status",
              startCreateDatetimeUTC: "Start Date",
              startCreateDatetimeUTCPh: "Select date and time",
              endCreateDatetimeUTC: "End Date",
              endCreateDatetimeUTCPh: "Select date and time",
              search: "Search",
              reset: "Reset",
              customerName: "Customer Name",
              emailPhone: "Email/Phone",
              createDatetimeUTC: "Date Time",
              amt: "Amt",
              refund: "Refund",
              export: "Export",
              exportDiaTitle: "Please select a encoding type",
              cancelBtn: "Cancel",
              confirmBtn: "Confirm",
              orderDiaTitle: "Order Information",
              errorRequest: "Request failed, please try again",
              errorGetOrder: "Failed to get order information",
              errorExport: "Encoding error, please re-select and download",
              refundSuccess: "The refund was successful",
              disableRefund: "Refund is not allowed",
              refundFail: "Refund failed",
              referenceNumber: "Reference Number",
              orderAmount: "Order Amount",
              pickupDate: "Pickup Date",
              immediately: "immediately",
              discount: "Discount",
              foodDetails: "Food Details",
              giveAwayList: "Give Away List",
              noData: "No Data",
              Created: "Created",
              Paid: "Paid",
              Cancelled: "Cancelled",
              TimedOut: "TimedOut",
              RefundApplied: "Refund Applied"
            },
            zh: {
              storeNumber: "店鋪編號",
              storeNumberPh: "請輸入店鋪編號",
              tableNumber: "臺號",
              tableNumberPh: "請輸入臺號",
              billNumber: "賬單號碼",
              billNumberPh: "請輸入賬單號碼",
              merchantRef: "商家編號",
              merchantRefPh: "請輸入商家編號",
              transactionID: "交易ID",
              transactionIDPh: "請輸入交易ID",
              email: "電郵",
              emailPh: "請輸入電郵",
              customerPhone: "電話",
              customerPhonePh: "請輸入電話",
              payType: "付款方式",
              payTypePh: "請輸入付款方式",
              payStatus: "付款狀態",
              payStatusPh: "請選擇付款狀態",
              startCreateDatetimeUTC: "開始日期",
              startCreateDatetimeUTCPh: "選擇日期和時間",
              endCreateDatetimeUTC: "結束日期",
              endCreateDatetimeUTCPh: "選擇日期和時間",
              search: "搜索",
              reset: "重設",
              customerName: "客戶名稱",
              emailPhone: "電郵/電話",
              createDatetimeUTC: "日期時間",
              amt: "金額",
              refund: "退款",
              export: "導出",
              exportDiaTitle: "請選擇一種編碼類型",
              cancelBtn: "取消",
              confirmBtn: "確認",
              orderDiaTitle: "訂單信息",
              errorRequest: "請求失敗，請再試一次",
              errorGetOrder: "無法獲取訂單信息",
              errorExport: "編碼錯誤，請重新選擇並下載",
              refundSuccess: "退款成功",
              disableRefund: "退款功能已禁用",
              refundFail: "退款失敗",
              referenceNumber: "參考號碼",
              orderAmount: "訂單金額",
              pickupDate: "取貨日期",
              immediately: "盡快",
              discount: "折扣",
              foodDetails: "食品詳情",
              giveAwayList: "贈品列表",
              noData: "暫無數據",
              Created: "已創建",
              Paid: "已支付",
              Cancelled: "已取消",
              TimedOut: "已超時",
              RefundApplied: "已申請退款"
            }
          }
        },

        computed: {
          colSpan() {
            // return this.$screen.lg ? 6 : 4
            //获取当前屏幕的宽度
            let width = document.body.clientWidth
            //大于1400显示4列,小于1400显示3列
            return width > 1400 ? 6 : 8
          },
          i18nPayStatus() {
            return val => {
              return this.i18n[this.locale][val]
            }
          }
        },
        mounted() {
          let { startTime, endTime } = this.getDayTime()
          // 默认显示当天时间数据
          this.searchForm.startCreateDatetimeUTC = startTime
          this.searchForm.endCreateDatetimeUTC = endTime
          // console.log(this.searchForm, 'this.searchForm')
          this.getSearch()
          // 页面加载的时候设置table的高度
          this.tableHeight = this.getTableHeight(0, "headerBox")
          // 页面大小该变的时候（缩放页面）设置table的高度（可加可不加）
          window.onresize = () => {
            this.tableHeight = this.getTableHeight(0, "headerBox")
          }
          // if (document.documentElement.clientHeight < 680) {
          //   this.diaTop = "10vh"
          // } else {
          //   this.diaTop = "15vh"
          // }
          // 屏幕高度小于时候设置diaTop小一些
        },
        methods: {
          hasNewOrderItemData(item) {
            const keys = [
              "newOrderItemFoodList",
              "newOrderItemMListList",
              "newOrderItemMTypeList",
              "newOrderItemFoodTypeList"
            ]
            return keys.some(it => {
              return Array.isArray(item[it]) && item[it].length
            })
          },
          paymentChange(list) {
            if (Array.isArray(list)) {
              if (list.length < 2) {
                this.searchForm.payCompany = list[0] || ""
                this.searchForm.payType = ""
              } else {
                this.searchForm.payCompany = list[0]
                this.searchForm.payType = list[1]
              }
            }
          },
          formatDateTime(form) {
            let { startCreateDatetimeUTC: start, endCreateDatetimeUTC: end } = form
            let { value } = JSON.parse(sessionStorage.getItem("localTimeZone"))
            let formatString = "YYYY-MM-DD HH:mm:ss"

            if (start) {
              let startCreateDatetimeUTC = moment(start)
                .subtract(moment.duration(value))
                .format(formatString)
              form = {
                ...form,
                startCreateDatetimeUTC
              }
            }
            if (end) {
              let endCreateDatetimeUTC = moment(end)
                .subtract(moment.duration(value))
                .format(formatString)
              form = {
                ...form,
                endCreateDatetimeUTC
              }
            }
            return form
          },
          getData(searchForm = {}) {
            // value-format="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
            // res.data.forEach(item => {
            //   item.payStatus = this.toDealWithPayStatus(item.payStatus)
            // });
            this.tableLoading = true
            let filterPam = this.fillerVal(searchForm)
            filterPam = this.formatDateTime(filterPam)
            let { errorRequest } = this.i18n[this.locale]
            $.get({
              url: "../../manager_payLog/getAllByParam",
              dataType: "json",
              data: filterPam,
              success: res => {
                if (res.statusCode === 200) {
                  // console.log(res, '初始请求数据')
                  res.data.forEach(item => {
                    item.payStatus = this.toDealWithPayStatus(item.payStatus)
                    item.amt = `${item.amtType || ""}${item.amt}`
                  })
                  this.tableData = res.data
                  this.refundList = res.refundList
                  console.log(this.tableData, "this.tableData")
                  // copy一份数据用于导出
                  this.copyFilterPam = filterPam
                } else {
                  this.$message.error(errorRequest)
                }
              },
              error: () => {
                this.$message.error(errorRequest)
              },
              complete: () => {
                this.tableLoading = false
              }
            })
          },
          getSearch() {
            let cloneForm = {
              ...this.searchForm,
              startCreateDatetimeUTC: this.searchForm.startCreateDatetimeUTC,
              endCreateDatetimeUTC: this.searchForm.endCreateDatetimeUTC
            }
            this.getData(cloneForm)
          },
          toDealWithPayStatus(status, type = "") {
            if (!status) return
            let mapObj = {
              1: "Created",
              2: "Paid",
              3: "Cancelled",
              4: "TimedOut",
              5: "RefundApplied"
            }
            let tagObj = {
              Created: "",
              Paid: "success",
              Cancelled: "danger",
              TimedOut: "warning",
              RefundApplied: "warning"
            }
            if (type != "tag") {
              return mapObj[status]
            } else {
              return tagObj[status]
            }
          },
          // 过滤假值
          fillerVal(tetsObj) {
            if (!tetsObj) return {}
            let filterPam = {}
            for (let i in tetsObj) {
              if (tetsObj[i]) {
                filterPam[i] = tetsObj[i]
              }
            }
            return filterPam
          },
          resetForm(formName) {
            this.$refs[formName].resetFields()
            // console.log(this.$refs[formName], 'this.$refs[formName]')
          },
          // 获取当天时间范围
          getDayTime() {
            let startTime = moment().startOf("day").format("YYYY-MM-DD HH:mm:ss") // 当天0点的时间格式
            let endTime = moment().endOf("day").format("YYYY-MM-DD HH:mm:ss") // 当天23点59分59秒的时间格式

            return { startTime, endTime }
          },
          /**
            * table的max-height高度

            * @param val，固定的站位高度（例如：分页高度）
            * @param name，动态站位的高度获取的name（例如：查询动态的查询条件）
            * @returns {number}，返回可用的高度
            * @param fixHeight,最外层样式高度
            */
          getTableHeight(val = 32, name) {
            //  #app 的样式padding为20,20
            // 头部导航固定70px
            let fixHeight = 20 + 40 + 15
            let searchFormHeight = this.$refs[name].clientHeight // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - fixHeight) // 计算完之后剩余table可用的高度

            return tableHeight
          },
          getOrder(row) {
            this.orderDialogVisible = true
            this.orderLoading = true
            let { errorGetOrder } = this.i18n[this.locale]
            $.post({
              url: "../../pay/getOrderInfo",
              dataType: "json",
              traditional: true, //允许传递数组或者对象
              data: {
                companyName: this.domain,
                storeNumber: row.storeNumber,
                merchantRefList: [row.merchantRef],
                noHide: true //UIConfig可配置是否隐藏未支付订单,当前接口固定传值为true
              },
              success: res => {
                if (res.statusCode == 200) {
                  // console.log(JSON.parse(JSON.stringify(res)), "res")
                  if (res.orderList[0]) {
                    this.payOrderObj = res.orderList[0]
                  }
                  // console.log(this.payOrderObj, "this.payOrderArray")
                } else {
                  this.$message.error(errorGetOrder)
                }
              },
              error: () => {
                this.$message.error(errorGetOrder)
              },
              complete: () => {
                this.orderLoading = false
              }
            })
          },
          // 封装显示细项tile
          inListTitle(item, language) {
            // let language = "en"
            let xiTitle = ""

            let isFoodList = item.hasOwnProperty("desc1") || item.hasOwnProperty("desc2")
            const getTitle = (foodListTitle, mlistTitle) => {
              return isFoodList ? foodListTitle : mlistTitle
            }

            if (language === "en") {
              xiTitle = getTitle(item.nameA || item.desc1, item.nameA || item.name)
            } else if (language === "zh") {
              xiTitle = getTitle(item.nameB || item.desc2, item.nameB || item.name2)
            } else {
              xiTitle = item.multi1 || getTitle(item.nameA || item.desc1, item.nameA || item.name)
            }
            return xiTitle
          },
          // 购物车细项文字逻辑
          showlogic(item, xiType, endXi = null) {
            let arry = this.flatMyXiArry(item) //扁平化我的细项数组
            let arryText = []
            arry.forEach(item => {
              let itemPrice
              if (item.desc1) {
                itemPrice = item.upa1 && item.upa1 != 0 ? `(${this.showXiPrice(item.upa1)})` : ""
              } else {
                itemPrice = item.price && item.price != 0 ? `(${this.showXiPrice(item.price)})` : ""
              }
              let name = `${this.inListTitle(item, "en")}(${this.inListTitle(item, "zh")})`
              let data = `${name} ${itemPrice}`
              arryText.push(data + this.showlogic(item, "myXi", endXi ? "endXi" : "inXi"))
            })
            let str = ""
            arryText.forEach((item, i) => {
              if (xiType == "myXi" || xiType == "optonXi") {
                if (endXi == "endXi") {
                  str += item + "+"
                } else {
                  str += `<p class='littleitem-inXi'>${item}</p>`
                }
              } else {
                str += `<p>${item}</p>`
              }
            })
            if (str.length > 0) {
              str = str.substr(0, str.length - 1)
              if (xiType == "myXi") {
                if (endXi == "endXi") {
                  return `<p class='littleitem-endXi'>[${str}]</p>`
                } else {
                  return `<p class='littleitem-inXi'>${str}</p>`
                }
              } else {
                return str
              }
            } else {
              return ""
            }
          },
          // 扁平化到第一层
          flatMyXiArry(item) {
            let arry = [...(item.newOrderItemFoodList || []), ...(item.newOrderItemMListList || [])]
            let newOrderItemFoodTypeList = item.newOrderItemFoodTypeList || []
            let newOrderItemMTypeList = item.newOrderItemMTypeList || []
            let allTypeArry = [...newOrderItemFoodTypeList, ...newOrderItemMTypeList]
            if (allTypeArry.length != 0) {
              allTypeArry.sort((a, b) => {
                return a.peerSort - b.peerSort
              })
              allTypeArry.forEach(item => {
                if (item.newOrderItemFoodList && item.newOrderItemFoodList.length != 0) {
                  item.newOrderItemFoodList.forEach(i => {
                    arry.push(i)
                  })
                } else if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                  item.newOrderItemMListList.forEach(i => {
                    arry.push(i)
                  })
                }
              })
            }
            if (item.packingBoxMList) {
              arry.push(item.packingBoxMList)
            }
            return arry
          },
          showGiveAway(item) {
            let { promotionFoodList = [] } = item
            let list = promotionFoodList.reduce((pro, cur) => {
              pro.push(...cur.promotionFoodList)
              return pro
            }, [])
            let domStr = list.reduce((pro, cur) => {
              let name = `<span style="color: rgba(0, 0, 0, 0.7)">${this.inListTitle(
                cur,
                "en"
              )} (${this.inListTitle(cur, "zh")})</span>`
              pro += `${name}</br>`
              return pro
            }, "")
            return domStr
          },
          getDiscountInfo(foodItem) {
            let { discountList = [] } = foodItem
            if (discountList.length) {
              let discount = discountList[0]
              let { amount, promotionDesc } = discount
              let desc = promotionDesc ? JSON.parse(promotionDesc)["en"] || "" : ""
              return {
                amount,
                desc
              }
            }
            return false
          },
          showXiPrice(price) {
            if (price > 0) {
              return `+${this.payOrderObj.amtType}${price}`
            } else {
              let absPrice = Math.abs(price)
              return `-${this.payOrderObj.amtType}${absPrice}`
            }
          },
          handleOrderDiaClose() {
            // 置空this.payOrderObj
            this.payOrderObj = null
          },
          exportDiaClose() {
            this.encodingTypeRadio = "GBK"
            this.customCodingInput = ""
          },
          exportSubmit() {
            let data = {
              ...this.copyFilterPam,
              encodingType:
                this.encodingTypeRadio != "customStr"
                  ? this.encodingTypeRadio
                  : this.customCodingInput
            }
            let { errorExport } = this.i18n[this.locale]
            $.post({
              url: "../../manager_payLog/downloadCSV",
              dataType: "json",
              data,
              complete: res => {
                if (res.responseText) {
                  if (res.responseJSON?.statusCode == 400 || res.responseJSON?.statusCode == 500) {
                    this.$message.error(errorExport)
                  } else {
                    let csvData = res.responseText
                    // let csvData = data.replace(/,/g, ";") // 将逗号替换为其他字符
                    let blob = new Blob([csvData], { type: "text/plain;charset=utf-8" })
                    let url = window.URL.createObjectURL(blob)
                    let a = document.createElement("a")
                    a.href = url
                    a.download = "data.csv" // 下载的文件名为"data.csv"
                    a.click()
                    window.URL.revokeObjectURL(url)
                    //关闭弹窗
                    this.exportDialogVisible = false
                  }
                } else {
                  this.$message.error(errorExport)
                }
              }
            })
          },
          //退款逻辑
          onRefund(row) {
            let { payType, payCompany, merchantRef, transactionID } = row
            if (!this.ARPM.includes(payType) && !this.ARPM.includes(payCompany)) return
            //过滤无效值
            let data = this.filterObj({
              payType,
              payCompany,
              merchantRef,
              transactionID
            })
            let { refundSuccess, disableRefund, refundFail } = this.i18n[this.locale]
            $.post({
              url: "../../manager_payLog/refund",
              dataType: "json",
              traditional: true, //允许传递数组或者对象
              data,
              success: res => {
                if (res.statusCode == 200) {
                  this.$message.success(refundSuccess)
                  this.getSearch()
                } else if (res.statusCode == 400) {
                  this.$message.error(disableRefund)
                } else {
                  this.$message.error(refundFail)
                }
              },
              error: error => {
                this.$message.error(refundFail)
              }
            })
          },
          showRefund(row) {
            let { payCompany, payStatus, createDatetimeUTC, storeNumber } = row
            let paymentStatus = false //支付状态是否允许退款
            // //雷蛇支付并且没有退款中或退款成功的订单
            if (this.ARPM.includes(payCompany) && payStatus == "Paid") {
              if (payCompany == "razer") {
                //判断当前UTC时间是否小于createDatetimeUTC+180天的时间
                let nowUTC = moment.utc() // 获取当前 UTC 时间
                let nowTime = nowUTC.format("YYYY-MM-DD HH:mm:ss")
                let endTime = moment(createDatetimeUTC)
                  .add(180, "days")
                  .format("YYYY-MM-DD HH:mm:ss")
                paymentStatus = moment(nowTime).isBefore(endTime)
              } else {
                paymentStatus = true
              }
            }
            //看支付方法配置是否允许显示退款按钮
            let refundable = this.getRefundableStatus(storeNumber)
            let isShow = paymentStatus && refundable
            return isShow
          },
          getRefundableStatus(storeNumber) {
            // 先尝试找到匹配的storeNumber
            let store = this.refundList.find(s => s.storeNumber === storeNumber)
            // 如果没有找到匹配的storeNumber，那么找"*"
            if (!store) {
              store = this.refundList.find(s => s.storeNumber === "*")
            }
            // 如果还是没有找到，那么返回一个默认值
            if (!store) {
              return null // 或者你想返回的其他默认值
            }
            // 返回找到的refundable值
            return store.refundable
          },

          //过滤对象无效值
          filterObj(obj) {
            return Object.fromEntries(
              Object.entries(obj).filter(([key, value]) => {
                return value !== "" && value !== null && value !== undefined
              })
            )
          },
          handleI18n(command) {
            if (this.tableData.length > 150) {
              //大数据会卡页面,采用loading过渡
              this.lanLoading = true
              setTimeout(() => {
                this.lanLoading = false
              }, 1500)
            }
            this.locale = command
            if (command == "en") {
              ELEMENT.locale(ELEMENT.lang.en)
            } else {
              ELEMENT.locale(ELEMENT.lang.zhCN)
            }
            // this.$forceUpdate()
          }
        }
      })
    </script>
  </body>
</html>
