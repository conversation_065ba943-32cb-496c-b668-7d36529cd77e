Vue.component("OrderWaitTips", {
  props: ["systemLanguage", "openTable", "orderWaitObj"],
  template: `
  <div class="orderWait-warp">
    <div class="left">{{systemLanguage.orderWaitLastUpdate}}<span class='queue-time'>{{ lastUpdated }}</span>
    </div>
  <div class="right" v-html="orderWaitMsg"></div>
</div>
  `,
  data() {
    return {}
  },
  created() {
    // setTimeout(() => {
    //   console.log(this.orderWaitMsg, "OrderWaitTips created")
    // }, 3000)
  },
  computed: {
    lastUpdated() {
      let { lastUpdatedTime } = app.queuedObj
      return lastUpdatedTime
    },
    orderWaitMsg() {
      let { waitTxt, waitTime } = this.orderWaitObj
      console.log("🚀 ~ orderWaitMsg ~ this.orderWaitObj:", this.orderWaitObj)
      const spanDom = val => {
        return `<span class='queue-time'>${val}</span>`
      }
      // return app.systemLanguage[waitTxt].replace("#min", spanDom(waitTime))
      let { orderWaitTimeTip, orderWaitRangeTip } = app.systemLanguage
      if (waitTxt === "orderWaitTimeTip") {
        return orderWaitTimeTip.replace("#min", spanDom(waitTime[0]))
      } else if (waitTxt === "orderWaitRangeTip") {
        return orderWaitRangeTip
          .replace("#minMinutes", spanDom(waitTime[0]))
          .replace("#maxMinutes", spanDom(waitTime[1]))
      }
    }
  },
  methods: {}
})
