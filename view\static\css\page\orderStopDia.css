.queue-app {
  display: none;
  width: 100%;
  height: 100%;
}
.queue-header {
  width: 100%;
  height: 40%;
}
.queue-header-img {
  width: 100%;
  max-height: 100%;
}

.queue-content {
  flex: 1;
  box-sizing: border-box;
}
.queue-content-tip {
  text-align: center;
}
.queue-content-title {
  font-size: 0.7rem;
  padding: 0.5rem 0.3rem 0;
}
.queue-content-text {
  font-size: 0.4rem;
  text-align: center;
  padding: 0.7rem 0.5rem 0rem;
}
.queue-content-progressBar {
  width: 90%;
  height: 0.8rem;
  background-color: #f0f0f0;
  margin: 1rem auto; /* 外边距 */
  border-radius: 0.2rem;
  overflow: hidden; /* to clip the inner progress */
  position: relative; /* to position the inner progress */
  padding: 0.1rem; /* 内边距，用于减少溢出 */
}
.queue-content-progress {
  height: 100%;
  width: 0%; /* initial width is 0 */
  background-color: #36c387; /* set progress bar color */
  transition: width 0.1s ease-out; /* smooth transition for width change */
  border-radius: 0.15rem; /* rounding the corners of the progress bar */
}
.queue-content-waitingTime {
  display: flex;
  justify-content: space-between;
  font-size: 0.38rem;
  padding: 0rem 0.6rem 0;
  margin-bottom: 0.25rem;
}

.queue-content-lastUpdated {
  padding: 0rem 0.6rem 0;
  font-size: 0.36rem;
}
@media screen and (min-width: 320px) and (max-width: 375px) {
  .queue-header {
    height: 35%;
  }
  .queue-content-title {
    font-size: 0.6rem;
  }
  .queue-content-text {
    font-size: 0.36rem;
  }

  .queue-content-waitingTime {
    font-size: 0.34rem;
  }
  .queue-content-lastUpdated {
    font-size: 0.32rem;
  }
  /* .queue-header-img {
    max-height: 35%;
  } */
}
