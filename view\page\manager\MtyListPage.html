<!DOCTYPE html>
<html lang="en" style="padding: 0 0 80px 0">
  <head>
    <meta charset="utf-8" />
    <title></title>
    <script src="../../static/vue/vue.min.js"></script>

    <!-- <meta name="description" content="Bootstrap Basic Tab Based Navigation Example" /> -->

    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <link href="../../static/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <script src="../../static/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="../../static/plugins/fontawesome-free/css/all.min.css" />
    <!-- 弹窗组件 -->
    <link rel="stylesheet" href="../../static/plugins/toastr/toastr.css" />
    <script src="../../static/plugins/toastr/toastr.min.js"></script>
    <!-- 引入moment -->
    <script src="../../static/moment/moment.js"></script>
    <!--layer彈出框-->
    <link rel="stylesheet" href="../../static/tools/layer_3/mobile/need/layer.css" />
    <script type="text/javascript" src="../../static/tools/layer_3/layer.js"></script>
    <script src="../../static/plugins/sortable/Sortable.min.js"></script>
    <script src="../../static/cmsUtils/commonFunction.js"></script>
    <!-- 搜索组件css(组件js在body标签后面加载) -->
    <link rel="stylesheet" href="../../static/cmsUtils/cmsSearchList/searchList.css" />
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style type="text/css">
      body {
        background-color: #f3efe0;
        /*页面背景色*/
        /* padding-top: 7px; */
        font-family: Arial, Helvetica, sans-serif;
      }

      #app {
        position: absolute;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
      }

      /* 追加套餐遮罩层 */
      #cover {
        display: none;
        position: fixed;
        background: #000000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0.4;
        z-index: 1;
      }

      /* 菜单 */
      #menu li {
        width: 16%;
        /*占宽*/
        font-weight: bold;
        /*字体加粗*/
        background-color: #f3f3f4;
        /*背景色灰色*/
        margin: 0;
        padding: 0;
        user-select: none;
      }

      #Food_detail {
        width: 20% !important;
      }

      #menu li a {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 菜单卡 */
      .card {
        /*border-color: #107ABB;*/
        /*border-style: solid;*/
        /*border-width: 3px;*/
        box-sizing: border-box;
        margin-top: 10px;
        /*上边距*/
        margin-right: 10px;
        /*右边距*/
        border-radius: 5px;
        /*圆角*/
        background: azure;
        /*背景色*/
        /* height: 100px; */
        width: 100%;
        padding: 10px;
        font-size: 14px;
      }

      .card table {
        table-layout: fixed;

        width: 100%;
        height: 100%;
        border-spacing: 0;
      }

      .card tr {
        height: 25px;
      }

      .upload {
        opacity: 0;
        /* margin: 10px; */
        filter: alpha(opacity=0);
        align-content: center;
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 9;
        cursor: pointer;
        /*小手指*/
        font-size: 0;
        /*按钮大小*/
      }

      .card-no-img,
      .upload {
        height: 75px;
        width: 75px;
      }

      .card-img {
        width: 75px;
        /* vertical-align: super; */
        border-radius: 5px;
        position: absolute;
        top: 10px;
        /* margin-top: -25px; */
      }

      .card-no-img {
        border-style: solid;
        /* 无图片时边框样式*/
        border-width: 2px;
        border-color: #107abb;
        border-radius: 5px;
        vertical-align: super;
      }

      /* 开关 */
      .switch {
        margin: auto;
        float: right;
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
        margin-left: 5px;
      }

      .switch input {
        display: none;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      input:checked + .slider {
        background-color: #2196f3;
      }

      input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
      }

      /* 开关--圆角 */
      .slider.round {
        border-radius: 17px;
      }

      .slider.round:before {
        border-radius: 50%;
      }
      input[type="checkbox"]:disabled + .slider.round {
        cursor: not-allowed;
      }
      /* 下一级按钮 */
      .next-type-td {
        width: 35px;
      }

      .center_nextbtn {
        /* display: flex;
      align-items: center; */
      }

      .next-type-button {
        width: 35px;
        height: 43%;
        padding: 0;
        word-wrap: break-word;
      }

      #detailEditBtn {
        width: 35px;
        height: 40px;
        padding: 0;
        color: white;
      }

      #editBtn {
        width: 35px;
        height: 43%;
        margin-bottom: 15px;
        padding: 0;
        color: white;
      }

      .next-type-button-disabled {
        background: #9d9d9d;
        width: 35px;
        height: 43%;
        padding: 0;
        word-wrap: break-word;
      }

      .card-name {
        width: 100%;
        padding-left: 2px;
      }

      .card-name-warp {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0;
        min-height: 20px;
      }

      .card-name-warp:nth-of-type(1) {
        font-size: 14px;
        font-weight: 600;
        text-align: start;
      }

      .card-name-warp:nth-of-type(2) {
        font-size: 12px;
      }

      .pad_topB {
        padding: 10px 0;
      }

      .modal-body {
        display: flex;
        align-content: center;
        flex-direction: column;
        max-height: 60vh;
        overflow-y: auto;
        padding: 1rem 1.5rem;
      }

      .form-control {
        font-size: 14px;
      }

      .label_title {
        display: inline-block;
        align-self: center;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: 700;
        font-size: 14px;
      }

      .modal-header {
        border: 0;
        text-align: center;
      }

      .modal-footer {
        border: 0;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .Form textarea {
        display: inline-block;
        padding: 6px 12px;
        font-size: 18px;
        font-weight: 300;
        line-height: 1.4;
        color: #221919;
        background: #fff;
        border: 1px solid #a4a2a2;
        resize: none;

        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -ms-box-sizing: border-box;
        -webkit-box-sizing: border-box;

        border-radius: 6px;
        -moz-border-radius: 6px;
        -webkit-border-radius: 6px;

        box-shadow: inset 0 1px rgba(34, 25, 25, 0.15), 0 1px rgba(255, 255, 255, 0.8);
        -moz-box-shadow: inset 0 1px rgba(34, 25, 25, 0.15), 0 1px rgba(255, 255, 255, 0.8);
        -webkit-box-shadow: inset 0 1px rgba(34, 25, 25, 0.15), 0 1px rgba(255, 255, 255, 0.8);

        -webkit-transition: all 0.08s ease-in-out;
        -moz-transition: all 0.08s ease-in-out;
      }

      .Form textarea {
        min-height: 90px;
      }

      .Form textarea:focus {
        border-color: #006499;
        box-shadow: 0 1px rgba(34, 25, 25, 0.15) inset, 0 1px rgba(255, 255, 255, 0.8),
          0 0 14px rgba(82, 162, 235, 0.35);
        -moz-box-shadow: 0 1px rgba(34, 25, 25, 0.15) inset, 0 1px rgba(255, 255, 255, 0.8),
          0 0 14px rgba(82, 162, 235, 0.35);
        -webkit-box-shadow: 0 1px rgba(34, 25, 25, 0.15) inset, 0 1px rgba(255, 255, 255, 0.8),
          0 0 14px rgba(82, 162, 235, 0.35);
      }

      .wordage {
        font-size: 14px;
        color: #5e5e5e;
        margin: 0 15px 0 20px;
      }

      .foodName_warp {
        text-align: center;
      }

      .foodName {
        font-size: 1rem;
      }

      .introduction {
        display: flex;
      }

      .label_title {
        text-align: right;
      }

      .nav_div {
        padding: 10px 15px;
      }

      .nav_div:last-child {
        border-right: 2px solid #606266;
      }

      .modal-content {
      }

      #additionalFood,
      #foodAllergen {
        display: flex;
        flex-wrap: wrap;
        max-height: 120px;
        overflow-y: auto;
      }

      .foodAllergen_warp {
        width: 33.33%;
        margin-bottom: 20px;
      }

      .allergen_input {
        zoom: 140%;
        vertical-align: top;
        margin-right: 5px !important;
      }

      /* 追加套餐 */
      .adOrderWarp {
        font-size: 12px;
        padding-top: 5px;
        padding-bottom: 5px;
        color: #303133;
        display: flex;
        flex-wrap: wrap;
      }

      .adOrderCell {
        padding: 10px;
        margin: 0px 10px 10px 0;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .adOrderTitle {
        max-width: 100px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .adOrderCell p {
        margin-bottom: 3px;
      }

      .process {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }

      .processbar {
        width: 200px;
        height: 30px;
      }

      ._load {
        width: 100%;
      }

      .pro,
      .sumData {
        font-size: 16px;
      }

      .processbar::-webkit-progress-bar {
        background: #d7d7d7;
      }

      .processbar::-webkit-progress-value,
      .processbar::-moz-progress-bar {
        background: orange;
      }

      .hideLoad {
        display: none;
      }

      #body_warp {
        height: calc(100% - 40px);
        margin: 0 10px 0 0;
      }

      .border {
        overflow: auto;
        border: 5px solid #d24735;
        border-bottom: none;
        border-radius: 5px;
      }

      .border:before {
        content: attr(version);
        position: fixed;
        left: 0;
        bottom: 9%;
        display: flex;
        align-items: center;
        visibility: unset;
        min-height: 50px;
        max-width: 200px;
        padding: 0 30px;
        /*margin-left: 15px;*/
        font-size: 25px;
        color: #fff;
        overflow-wrap: break-word;
        background-color: #d24735;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        z-index: 10;
        pointer-events: none;
      }

      #body_warp:has(.process) {
        border: none;
      }

      #body_warp:has(.process):before {
        visibility: hidden;
      }

      #body_warp > div {
        float: left;
        padding-left: 10px;
      }

      .nav > li > a {
        position: relative;
        display: block;
        padding: 10px 15px;
        color: #337ab7;
        text-decoration: none;
        font-size: 14px;
      }

      .nav-tabs > li.active > a {
        color: #555;
        cursor: default;
        background-color: #fff;
        border: 1px solid #ddd;
        border-bottom-color: transparent;
      }

      .switchBox {
        cursor: pointer;
        display: flex;
        height: 100%;
        align-items: center;
        position: absolute;
      }
      .switchBox > div {
        margin-right: 10px;
      }
      .switchBox-title {
        white-space: nowrap;
        /* padding-left: 2px; */
      }
      .card-bottom {
        position: relative;
      }
      .switchBox-cell {
        display: flex;
        align-items: center;
      }
      .switchBox-icon {
        width: 18px;
        height: 18px;
      }
      .card-bottom-useTime {
        /* flex-shrink: 0; */
      }
      .card-name-info {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding-right: 10px;
        margin: 0;
        /* 去掉bootstrap的margin样式 */
      }
      .modal .modal-body .pos-base-info {
        display: flex;
        flex-direction: column;
        width: fit-content;
      }
      .pos-base-info p {
        margin: 0;
        padding: 0;
      }
      .form-type-item .type-title {
        color: #3f9ef1;
        font-size: 16px;
        margin: 10px 0;
      }
      .form-type-item label.text-start span {
        display: block;
        font-size: 12px;
      }
      .switchWarp {
        display: flex;
        align-items: center;
      }
    </style>

    <script type="text/javascript">
      $(function () {
        //自定义参数
        toastr.options = {
          closeButton: false, //是否显示关闭按钮（提示框右上角关闭按钮）。
          debug: false, //是否为调试。
          progressBar: true, //是否显示进度条（设置关闭的超时时间进度条）
          positionClass: "toast-top-center", //消息框在页面显示的位置
          onclick: null, //点击消息框自定义事件
          showDuration: "300", //显示动作时间
          hideDuration: "1000", //隐藏动作时间
          timeOut: "2000", //自动关闭超时时间
          extendedTimeOut: "1000",
          showEasing: "swing",
          hideEasing: "linear",
          showMethod: "fadeIn", //显示的方式，和jquery相同
          hideMethod: "fadeOut" //隐藏的方式，和jquery相同
          //等其他参数o
        }
      })

      let { storeNumber, domain, versionNumber } = getRequestHeaderParams()
      baseUrl = `http://appwise.oss-cn-hongkong.aliyuncs.com/${domain}/`
      if (storeNumber !== "*") {
        baseUrl += `${storeNumber}/image/`
      } else {
        baseUrl += "image/"
      }
      if (versionNumber !== "") {
        //为空时为生产环境
        baseUrl += `${versionNumber}/`
      }

      var smallImageSuffix = "?x-oss-process=image/resize,w_75" + "," + new Date()
      var mtyAllList
      var updateTime
      var pathList = new Array() //二维数组，元素定义
      var imageSuffix = ["jpg", "png", "webp", "bmp", "gif", "tiff"]
      // 获取allergen icons过敏原图片

      function queryData() {
        var allergenArry = []
        let { storeNumber, domain } = getRequestHeaderParams()
        if (storeNumber && storeNumber.length != 0) {
          let data = {
            domain,
            storeNumber,
            typeNameList: ["Allergen icons"]
          }
          $.get({
            url: "../../photoConfig/getSpecifiedPhotoConfig",
            dataType: "json",
            traditional: true,
            data,
            success: function (res) {
              console.log(res, "照片")
              let data = res.photoConfigList
              data.forEach(item => {
                if (item.typeName == "Allergen icons" && item.usable) {
                  item.logoUrl =
                    "http://appwise.oss-cn-hongkong.aliyuncs.com" +
                    "/" +
                    item.domain +
                    "/" +
                    item.storeNumber +
                    "/image/" +
                    item.typeName +
                    "/" +
                    item.fileName +
                    ".jpg?x-oss-process=image/resize,w_80,h_80"

                  allergenArry.push(item)
                }
              })
              // 过敏食物逻辑
              var contentHtml = ""
              console.log(allergenArry, "过敏食物逻辑")
              allergenArry.forEach(item => {
                contentHtml +=
                  '<div class="form-check form-check-inline foodAllergen_warp">' +
                  `<input  onclick="getCheckBoxValueOne()" class="form-check-input allergen_input" type="checkbox" id="inlineCheckbox" value="${item.fileName}" name="allergenInput"/>` +
                  `<img src="${item.logoUrl}" alt=""  />` +
                  "</div>"
              })
              $("#foodAllergen").html(contentHtml)
              console.log(allergenArry, "allergenArry")
              showcheckout()
            },
            error: function () {
              toastr.error("Failed to get allergen image")
            }
          })
        } else {
          console.log("storeNumber没有")
        }
      }

      // 排序
      function sortAllList(arry, codeType, sortType) {
        if (arry.length != 0) {
          let useSort = false
          for (let i = 0; i < arry.length; i++) {
            let item = arry[i]
            if (!item) continue
            if (item[sortType] && item[sortType] != 0) {
              useSort = true
              break
            }
          }
          // 实行排序
          if (useSort) {
            arry.sort(function (a, b) {
              if (a[sortType] == b[sortType]) {
                // seq相同进一步根据Code排序
                return a[codeType].localeCompare(b[codeType])
              } else {
                return a[sortType] - b[sortType]
              }
            })
          } else {
            arry.sort(function (a, b) {
              return a[codeType].localeCompare(b[codeType])
            })
          }
        }
      }
      // 递归排序
      function recursiveFun(arry) {
        for (var i in arry) {
          if (!arry[i]) continue
          if (arry[i].mListList && arry[i].mListList.length != 0) {
            // sortAllList(arry[i].mListList, "code", "sort");
            arry[i].mListList.forEach(o => {
              o.detailType = "Mlist"
            })
            recursiveFun(arry[i].mListList)
          }
          if (arry[i].mTypeList && arry[i].mTypeList.length != 0) {
            arry[i].mTypeList.forEach(e => {
              e.detailType = "Mtype"
            })
            sortAllList(arry[i].mTypeList, "code", "finalSort")
            recursiveFun(arry[i].mTypeList)
          }
        }
      }
      function formatNum(num) {
        if (!/^(\+|-)?(\d+)(\.\d+)?$/.test(num)) {
          return num
        }
        var a = RegExp.$1,
          b = RegExp.$2,
          c = RegExp.$3
        var re = new RegExp().compile("(\\d)(\\d{3})(,|$)")
        while (re.test(b)) b = b.replace(re, "$1,$2$3")
        return a + "" + b + "" + c
      }
      function initData(type) {
        window.parent.disabledDrag() //  调用父页面方法禁止拖拽开关
        window.parent.showVersionTag() // 调取ifm父页面标出当前版本
        let process = document.querySelector(".process")
        let sumData = document.querySelector(".sumData")
        let pro = document.querySelector(".pro")
        var xhr = new XMLHttpRequest()
        let domain = sessionStorage.getItem("domain")
        let storeNumber = sessionStorage.getItem("storeNumber")
        let versionNumber = sessionStorage.getItem("versionNumber") || ""
        let isFoodCourt = sessionStorage.getItem("storeSelectionFoodCourtMode") || false // 获取选择台号后是否进入foodCourt模式(默认false)
        xhr.open("get", `../../manager_mType/getAllMTypeAndMList?isFoodCourt=${isFoodCourt}`, true)
        // 设置的header头必须要放到open()后面
        xhr.setRequestHeader("domain", domain)
        xhr.setRequestHeader("storeNumber", storeNumber)
        xhr.setRequestHeader("versionNumber", versionNumber == "PROD" ? "" : versionNumber)
        xhr.responseType = "text"

        let time = 0
        let sum = 0
        let number = 0
        xhr.onprogress = function (pe) {
          number++
          let time1 = pe.timeStamp.toFixed(0)
          if (number > 1) {
            pro.innerHTML = formatNum(
              ((((pe.loaded - sum) / 1024) * 1000) / (time1 - time)).toFixed(2)
            )
          } else {
            pro.innerHTML = formatNum((((pe.loaded / 1024) * 1000) / time1).toFixed(2))
          }
          sumData.innerHTML = formatNum((pe.loaded / 1024).toFixed(2))
          time = time1
          sum = pe.loaded
        }
        xhr.onloadend = function () {
          let result = JSON.parse(xhr.response)
          if (result.hasOwnProperty("error")) {
            process.classList.add("hideLoad")
            alert("Failed to initialize data")
            return
          }
          mtyAllList = []
          mtyAllList = result
          mtyAllList.forEach(item => {
            item.detailType = "Mtype" // 第一层自身遍历赋值标识
          })
          sortAllList(mtyAllList, "code", "finalSort")
          recursiveFun(mtyAllList)
          console.log(mtyAllList, "mtyAllList")

          if (type == "init") {
            $.getScript("../../static/cmsUtils/cmsMtyListPageDrag.js")
              .done(() => {})
              .fail(() => {
                console.log("加载cmsMtyListPageDrag失败")
              })
          }
          setTimeout(() => {
            switch (type) {
              case 1:
                createSiElm(pathList[0].index, pathList[0].id, pathList[0].type)
                break
              case 2:
                createWuElm(pathList[1].index, pathList[1].id, pathList[1].type)
                break
              case 3:
                createLiuElm(pathList[2].index, pathList[2].id, pathList[2].type)
                break
              case 4:
                createQiElm(pathList[3].index, pathList[3].id, pathList[3].type)
                break
              case 5:
                createBaElm(pathList[4].index, pathList[4].id, pathList[4].type)
                break
              default:
                createSetMealElm()
            }
          }, 10)
          window.parent.passDrag("init") // 父页面开启拖拽开关
        }
        xhr.onerror = function () {
          alert("Failed to initialize data")
        }
        xhr.send()
      }
      /**
       * @description 在input[type=file]时,校验图片的宽高
       * 宽:360 - 440; 高:240 - 292
       * @param {File} file 文件file
       * */
      function checkImageWH(file) {
        let iframe = window.top.document.querySelector("iframe")
        let sizeMap = iframe
          ? JSON.parse(iframe.dataset.imageSize)
          : {
              imageMaxWidth: 440,
              imageMinWidth: 360,
              imageMaxHigh: 292,
              imageMinHigh: 240
            }
        let url = window.URL || window.webkitURL
        let img = new Image() //手动创建一个Image对象
        img.src = url.createObjectURL(file) //创建Image的对象的url
        return new Promise(res => {
          img.onload = function () {
            let { width, height } = this
            let cw = width <= sizeMap["imageMaxWidth"] && width >= sizeMap["imageMinWidth"]
            let ch = height <= sizeMap["imageMaxHigh"] && height >= sizeMap["imageMinHigh"]
            console.log("图片width:", width, "图片height:", height)
            if (cw && ch) {
              res({ state: true })
            } else {
              res({
                state: false,
                sizeMap
              })
            }
          }
        })
      }

      /*
             id:文件名
             label:this,带FIle
             typeName:文件夹名
              */
      async function upload(id, label, typeName) {
        let domain = sessionStorage.getItem("domain")
        let file = $(label)[0].files[0]
        if (typeName === "mlist") {
          let { state, sizeMap } = await checkImageWH(file).catch()
          if (!state) {
            let { imageMaxWidth, imageMinWidth, imageMaxHigh, imageMinHigh } = sizeMap
            toastr.error(
              `The maximum width and height is ${imageMaxWidth} x ${imageMaxHigh},and minimum  is ${imageMinWidth} x ${imageMinHigh}`
            )
            return false
          }
        }
        // 判断文件大小合法性
        if (!file) return
        // 上传图片
        var fd = new FormData()
        fd.append("id", id)
        fd.append("file", file)
        fd.append("updateTime", updateTime)
        fd.append("pathList", JSON.stringify(pathList))
        fd.append("typeName", typeName)
        fd.append("domain", domain)
        let version = sessionStorage.getItem("versionNumber")
        if (version !== "PROD") {
          fd.append("version", version)
        }
        $.post({
          url: "../../manager_photo/upload",
          dataType: "json",
          processData: false,
          contentType: false,
          cache: false,
          data: fd,
          success: function (result) {
            if (result.errFlag1) {
              toastr.error(result.errMessage1)
            } else {
              updateTime = result.updateTime
              // 更新单一数据
              if (result.updateFlag) {
                // 更新时间一致，只更新当前数据
                var imgLaber = $(label.nextElementSibling)
                imgLaber.attr("onerror", "noImg(this);")
                imgLaber.attr(
                  "src",
                  baseUrl + typeName + "/" + id + ".jpg" + smallImageSuffix + "," + new Date()
                )
                imgLaber.removeClass("card-no-img")
                imgLaber.addClass("card-img")
                // 更新title属性
                $(label).attr("title", id + ".jpg")
              } else {
                // 更新时间不一致，初始化全部数据，并重置页面
                foodTypeList = $.parseJSON(result.foodTypeListJson)
                //TODO:弹出数据更新了的提示框，自己消失的那种
                createFoodTypeElm()
              }
            }
          },
          error: function (data) {
            toastr.error("Upload picture abnormal")
          }
        })
      }

      //无法加载图片时使用默认图片
      function noImg(img) {
        $(img).attr("src", "../../static/img/noimage3.jpg")
        $(img).removeAttr("onerror") //避免读取不到默认图片时死循环
        $(img).removeClass("card-img")
        $(img).addClass("card-no-img")
        var input = $(img).siblings("input")
        input.attr("title", "Please upload pictures")
      }

      function createSetMealElm() {
        window.parent.disabledDrag("allDisabled") // 调用父页面方法禁止拖拽开关,第栏目mty排序无意义直接禁止
        // 清空多余路径信息
        pathList.length = 0
        $(parent.document.getElementById("navigation")).html("")
        // 设置菜单激活效果
        $("#Food_detail").addClass("active").siblings().removeClass("active")

        var html = ""
        $.each(mtyAllList, function (i, mTypeItem) {
          html += createMTypeCard(i, mTypeItem, "1")
        })

        $(document.getElementById("body_warp")).html(html)
      }
      // 创建第四层细项卡
      var siClickItem = {}
      var mList
      function createSiElm(clickIndex, id, type) {
        //创建第二层的item
        // 清空多余路径信息
        window.parent.passDrag() // 放开禁止拖拽

        pathList.length = 1
        var mType = mtyAllList[clickIndex]
        pathList[0] = {
          index: clickIndex,
          id: id,
          type
        } //记录food的索引和id
        siClickItem = mType
        // switch (type) {
        //   case "Mlist":
        //     siClickItem = mType.mListList[clickIndex];
        //     break;
        //   case "Mtype":
        //     siClickItem = mType.mTypeList[clickIndex];
        //     break;
        // }
        // 激活菜单

        $("#Food_Indetail").addClass("active").siblings().removeClass("active")

        mList = mType.mListList
        var html = ""
        $(parent.document.getElementById("navigation")).html("")

        if (siClickItem.mTypeList && siClickItem.mTypeList.length > 0) {
          $.each(siClickItem.mTypeList, function (i, mTypeItem) {
            html += createMTypeCard(i, mTypeItem, "1")
          })
        }
        if (siClickItem.mListList && siClickItem.mListList.length > 0) {
          $.each(siClickItem.mListList, function (i, mListListItem) {
            html += createMListListCard(i, mListListItem, "1")
          })
        }

        $(document.getElementById("body_warp")).html(html)
      }
      // 第五层细项卡
      var outMtypeIndex, outMlistIndex //储存外层索引，解决传值只能拷贝无法改变元数据问题
      var wuClickItem = {} //第五层点击的item
      function createWuElm(clickIndex, id, type) {
        //创建第三层的item
        window.parent.passDrag() // 放开禁止拖拽

        // 清空多余路径信息
        pathList.length = 2
        var outMType = mtyAllList[pathList[0].index]
        var outMlist = outMType.mListList[clickIndex]
        outMtypeIndex = pathList[0].index //储存第一层fty
        outMlistIndex = clickIndex //储存footype下的food
        pathList[1] = {
          index: clickIndex,
          id: id,
          type
        } //记录food的索引和id

        switch (type) {
          case "Mlist":
            wuClickItem = siClickItem.mListList[clickIndex]
            console.log("第四层Mlist点击")
            break
          case "Mtype":
            wuClickItem = siClickItem.mTypeList[clickIndex]
            console.log(siClickItem.mTypeList[clickIndex], "第四层Mtype下mlist点击")
            break
        }
        console.log(wuClickItem, "第四层点击进入")

        // 激活菜单
        $("#Specific_detail").addClass("active").siblings().removeClass("active")
        var html = ""

        $(parent.document.getElementById("navigation")).html("")

        if (wuClickItem.mTypeList && wuClickItem.mTypeList.length > 0) {
          $.each(wuClickItem.mTypeList, function (i, mTypeItem) {
            html += createMTypeCard(i, mTypeItem, "3")
          })
        }
        if (wuClickItem.mListList && wuClickItem.mListList.length > 0) {
          $.each(wuClickItem.mListList, function (i, mListListItem) {
            html += createMListListCard(i, mListListItem, "3")
          })
        }
        // for (let index = 0; index < 10; index++) {
        //   html += html
        // }
        $(document.getElementById("body_warp")).html(html)
      }
      // 第六层细项卡
      var liuClickItem = {} //第五层点击的item
      function createLiuElm(clickIndex, id, type) {
        window.parent.passDrag() // 放开禁止拖拽

        // 清空多余路径信息
        pathList.length = 3
        pathList[2] = {
          index: clickIndex,
          id: id,
          type
        } //记录food的索引和id
        // var foodType = foodTypeList[pathList[0].index];
        // var food = foodType.foodList[pathList[1].index] //第二层food;
        switch (type) {
          case "Mlist":
            liuClickItem = wuClickItem.mListList[clickIndex]
            console.log("第五层Mlist点击")
            break
          case "Mtype":
            liuClickItem = wuClickItem.mTypeList[clickIndex]
            console.log(wuClickItem.mTypeList[clickIndex], "第五层Mtype下mlist点击")
            break
        }
        console.log(liuClickItem, "第五层点击进入")

        // 激活菜单
        $("#Specific_Indetail").addClass("active").siblings().removeClass("active")

        var html = ""

        $(parent.document.getElementById("navigation")).html("")
        if (liuClickItem.mTypeList && liuClickItem.mTypeList.length > 0) {
          $.each(liuClickItem.mTypeList, function (i, mTypeItem) {
            html += createMTypeCard(i, mTypeItem, "4")
          })
        }
        if (liuClickItem.mListList && liuClickItem.mListList.length > 0) {
          $.each(liuClickItem.mListList, function (i, mListListItem) {
            html += createMListListCard(i, mListListItem, "4")
          })
        }
        $(document.getElementById("body_warp")).html(html)
      }
      // 第七层细项卡
      var qiClickItem = {} //第七层点击的item

      function createQiElm(clickIndex, id, type) {
        window.parent.passDrag() // 放开禁止拖拽
        // 清空多余路径信息
        pathList.length = 4
        pathList[3] = {
          index: clickIndex,
          id: id,
          type
        } //记录food的索引和id
        switch (type) {
          case "Mlist":
            qiClickItem = liuClickItem.mListList[clickIndex]
            console.log("第六层Mlist点击")
            break
          case "Mtype":
            qiClickItem = liuClickItem.mTypeList[clickIndex]
            console.log(liuClickItem.mTypeList[clickIndex], "第六层Mtype下mlist点击")
            break
        }
        console.log(qiClickItem, "第六层点击进入")
        // 激活菜单
        $("#Specific_Indetail_four").addClass("active").siblings().removeClass("active") // 添加当前元素的样式
        var html = ""

        $(parent.document.getElementById("navigation")).html("")
        if (qiClickItem.mListList && qiClickItem.mListList.length > 0) {
          $.each(qiClickItem.mListList, function (i, mListListItem) {
            html += createMListListCard(i, mListListItem, "5")
          })
        }

        if (qiClickItem.mTypeList && qiClickItem.mTypeList.length > 0) {
          $.each(qiClickItem.mTypeList, function (i, mTypeItem) {
            html += createMTypeCard(i, mTypeItem, "5")
          })
        }

        $(document.getElementById("body_warp")).html(html)
      }
      // 第八层细项卡
      var baClickItem = {} //第七层点击的item

      function createBaElm(clickIndex, id, type) {
        window.parent.passDrag() // 放开禁止拖拽
        // 清空多余路径信息
        pathList.length = 5
        pathList[4] = {
          index: clickIndex,
          id: id,
          type
        } //记录food的索引和id
        switch (type) {
          case "Mlist":
            baClickItem = qiClickItem.mListList[clickIndex]
            break
          case "Mtype":
            baClickItem = qiClickItem.mTypeList[clickIndex]
            break
        }

        // 激活菜单
        $("#Specific_Indetail_five").addClass("active").siblings().removeClass("active") // 添加当前元素的样式
        var html = ""

        $(parent.document.getElementById("navigation")).html("")

        if (baClickItem.mListList && baClickItem.mListList.length > 0) {
          $.each(baClickItem.mListList, function (i, mListListItem) {
            html += createMListListCard(i, mListListItem, "6")
          })
        }

        if (baClickItem.mTypeList && baClickItem.mTypeList.length > 0) {
          $.each(baClickItem.mTypeList, function (i, mTypeItem) {
            html += createMTypeCard(i, mTypeItem, "6")
          })
        }

        $(document.getElementById("body_warp")).html(html)
      }

      //生成单个细项MType卡
      function createMTypeCard(mTypeIndex, mTypeItem, type) {
        // console.log(mTypeItem, "遍历的mTypeItem");
        let spliceSuffix = isJPG(mTypeItem.photoSuffix)
        let photoNaming = mTypeItem.code + spliceSuffix
        var mTypeModalHtml = `<button id="editBtn"   class="btn btn-info" onclick="onfMtypeModal(${mTypeIndex},${type});">Info</button>`
        var html =
          '<div class=" col-xl-2 col-lg-3 col-md-4 col-sm-6 col-xs-6" style="padding-right: 0;margin-right: 0">\n' +
          '        <div class="card" card-type="mType">\n' +
          '            <table id="' +
          "card_" +
          mTypeItem.code +
          '" cellspacing="0" cellpadding="0">\n' +
          "                <tr>\n" +
          '                    <td rowspan="3" width="75px" >' +
          '                       <input type="file"' +
          `title=\"${photoNaming}"\"` + // 图片后缀提示
          'id="' +
          mTypeItem.code +
          '" name="file" class="upload" onclick="this.value=null" onchange="upload(\'' +
          mTypeItem.code +
          "',this ,'mtype')\" accept=\"image/*\"/>" +
          '                           <img class="card-img" src="' +
          baseUrl +
          "mtype/" +
          mTypeItem.code +
          spliceSuffix +
          smallImageSuffix +
          '" onerror="noImg(this);"/></td>\n' +
          '                    <td  colspan="2" class="card-name" title="' +
          mTypeItem.desc2 +
          '">' +
          `<p class="card-name-warp">${mTypeItem.nameA || mTypeItem.desc || "&nbsp;"}</p>` +
          `<p class="card-name-warp">${mTypeItem.nameB || mTypeItem.desc2 || "&nbsp;"}</p>` +
          "</td>\n" +
          '                    <td rowspan="4" class="next-type-td">\n' +
          mTypeModalHtml

        var len = 0

        if (mTypeItem.mTypeList) len += mTypeItem.mTypeList.length
        if (mTypeItem.mListList) len += mTypeItem.mListList.length
        if (len !== 0) {
          html += '<button class="next-type-button btn btn-primary" '

          if (pathList.length < 1) {
            // 创建第3项，我的细项
            html += `onclick=createSiElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
          } else if (pathList.length == 1) {
            html += `onclick=createWuElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
            //TODO:创建第4项，子项
          } else if (pathList.length == 2) {
            html += `onclick=createLiuElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
            //TODO:创建第4项，子项
          } else if (pathList.length == 3) {
            html += `onclick=createQiElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
          } else if (pathList.length == 4) {
            html += `onclick=createBaElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
          }
        } else {
          html += '<button class="next-type-button-disabled" disabled="disabled"'
        }

        html +=
          ">\n" +
          '                            <font size="1" color="white">' +
          len +
          "</font><br/>\n" +
          "                        </button>\n" +
          "                    </td>\n" +
          "                </tr>\n" +
          "                <tr>\n" +
          '                    <td colspan="2" style="color: #9d9d9d" class="card-name">' +
          mTypeItem.code +
          "</td>\n" +
          "  </tr>\n" +
          `<tr></tr>` +
          "                <tr>\n" +
          `  <td class="card-bottom-useTime" >
              <i class="fa fa-clock-o"></i>
              ${
                !mTypeItem.use_dow && !mTypeItem.use_date && !mTypeItem.use_time ? "全日" : "自定義"
              }
            </td>` +
          "                </tr>\n"

        //堂食/外帶/總開關
        const switchBoxCells = [
          {
            mode: "take-away",
            imgSrc: "../../static/img/cms/take-away.jpg",
            checked: mTypeItem.finalTouch && mTypeItem.takeawayTouch,
            disabled: mTypeItem.finalTouch ? "" : "disabled"
          },
          {
            mode: "dine-in",
            imgSrc: "../../static/img/cms/dine-in.jpg",
            checked: mTypeItem.finalTouch && mTypeItem.dineInTouch,
            disabled: mTypeItem.finalTouch ? "" : "disabled"
          },
          {
            mode: "master-switch",
            imgSrc: "../../static/img/cms/master-switch.jpg",
            checked: mTypeItem.finalTouch,
            disabled: ""
          }
        ]

        const switchBoxHtml = switchBoxCells
          .map(
            cell => `
                  <div class='switchBox-cell'>
                    <img src="${cell.imgSrc}" alt="" class="switchBox-icon" />
                    <label class="switch">
                      <input data-mode="${cell.mode}" type="checkbox" onclick="onTypeCheckbox('${
              mTypeItem.code
            }', this, 'mTypeItem', '${mTypeIndex}', '${type}')" ${cell.checked ? "checked" : ""} ${
              cell.disabled
            }>
                      <div class="slider round"></div>
                    </label>
                  </div>
                `
          )
          .join("")
        html += `
                  <tr class="card-bottom">
                    <td class="switchBox">
                      ${switchBoxHtml}
                    </td>
                  </tr>
                `
        html += "</table>\n" + " </div>\n" + "</div>"
        return html
      }
      //生成单个Mlist卡
      function createMListListCard(mlistIndex, mlistItem, type) {
        // console.log(mlistItem, "mlistItem");
        let spliceSuffix = isJPG(mlistItem.photoSuffix)
        let photoNaming = mlistItem.code + spliceSuffix
        let showPrice =
          mlistItem[priceName("mlist")] == 0 ? "" : showCurrency + mlistItem[priceName("mlist")]
        var mListModalHtml = `<button id="editBtn"   class="btn btn-info" onclick="onMlistModal(${mlistIndex},${type});">Info</button>`
        var html =
          '<div class=" col-xl-2 col-lg-3 col-md-4 col-sm-6 col-xs-6" style="padding-right: 0;margin-right: 0">\n' +
          '        <div class="card" card-type="mlist">\n' +
          '            <table id="' +
          "card_" +
          mlistItem.code +
          '" cellspacing="0" cellpadding="0">\n' +
          "                <tr>\n" +
          '                    <td rowspan="3" width="75px" >' +
          '                       <input type="file"' +
          `title=\"${photoNaming}"\"` + // 图片后缀提示
          'id="' +
          mlistItem.code +
          '" name="file" class="upload" onclick="this.value=null" onchange="upload(\'' +
          mlistItem.code +
          "',this ,'mlist')\" accept=\"image/*\"/>" +
          '                           <img class="card-img" src="' +
          baseUrl +
          "mlist/" +
          mlistItem.code +
          spliceSuffix +
          smallImageSuffix +
          '" onerror="noImg(this);"/></td>\n' +
          '                    <td  colspan="2" class="card-name" title="' +
          mlistItem.name2 +
          '">' +
          `<p class="card-name-warp">${mlistItem.nameA || mlistItem.name || "&nbsp;"}</p>` +
          `<p class="card-name-warp">${mlistItem.nameB || mlistItem.name2 || "&nbsp;"}</p>` +
          "</td>\n" +
          '                    <td rowspan="4" class="next-type-td">\n' +
          mListModalHtml
        var len = 0

        if (mlistItem.mTypeList) len += mlistItem.mTypeList.length
        if (mlistItem.mListList) len += mlistItem.mListList.length
        if (len !== 0) {
          html += '<button class="next-type-button btn btn-primary" '
          if (pathList.length < 1) {
            // 创建第3项，我的细项
            html += `onclick=createSiElm(${mlistIndex},'${mlistItem.code}','Mlist')`
          } else if (pathList.length == 1) {
            html += `onclick=createWuElm(${mlistIndex},'${mlistItem.code}','Mlist')`
            //TODO:创建第4项，子项
          } else if (pathList.length == 2) {
            html += `onclick=createLiuElm(${mlistIndex},'${mlistItem.code}','Mlist')`
          } else if (pathList.length == 3) {
            html += `onclick=createQiElm(${mlistIndex},'${mlistItem.code}','Mlist')`
          } else if (pathList.length == 4) {
            html += `onclick=createBaElm(${mlistIndex},'${mlistItem.code}','Mlist')`
          }
        } else {
          html += '<button class="next-type-button-disabled" disabled="disabled"'
        }
        html +=
          ">\n" +
          '                            <font size="1" color="white">' +
          len +
          "</font><br/>\n" +
          "                        </button>\n" +
          "                    </td>\n" +
          "                </tr>\n" +
          "                <tr>\n" +
          '                    <td colspan="2" style="color: #9d9d9d" class="card-name">' +
          `<p class="card-name-info" id="identificationAndPrice">
                <span> ${mlistItem.code} </span>
                <span> ${showPrice} </span>
                </p>` +
          "</td>\n" +
          "                </tr>\n" +
          `<tr></tr>` +
          "                <tr>\n" +
          `  <td class="card-bottom-useTime" >
        <i class="fa fa-clock-o"></i>
        ${!mlistItem.use_dow && !mlistItem.use_date && !mlistItem.use_time ? "全日" : "自定義"}
      </td>` +
          "                </tr>\n"

        //堂食/外帶/總開關
        const switchBoxCells = [
          {
            mode: "take-away",
            imgSrc: "../../static/img/cms/take-away.jpg",
            checked: mlistItem.finalTouch && mlistItem.takeawayTouch,
            disabled: mlistItem.finalTouch ? "" : "disabled"
          },
          {
            mode: "dine-in",
            imgSrc: "../../static/img/cms/dine-in.jpg",
            checked: mlistItem.finalTouch && mlistItem.dineInTouch,
            disabled: mlistItem.finalTouch ? "" : "disabled"
          },
          {
            mode: "master-switch",
            imgSrc: "../../static/img/cms/master-switch.jpg",
            checked: mlistItem.finalTouch,
            disabled: ""
          }
        ]

        const switchBoxHtml = switchBoxCells
          .map(
            cell => `
        <div class='switchBox-cell'>
          <img src="${cell.imgSrc}" alt="" class="switchBox-icon" />
          <label class="switch">
            <input data-mode="${cell.mode}" type="checkbox" onclick="onTypeCheckbox('${
              mlistItem.code
            }', this, 'mlistItem', '${mlistIndex}', '${type}')" ${cell.checked ? "checked" : ""} ${
              cell.disabled
            }>
            <div class="slider round"></div>
          </label>
        </div>
      `
          )
          .join("")

        html += `
        <tr class="card-bottom">
          <td class="switchBox">
            ${switchBoxHtml}
          </td>
        </tr>
      `

        html += "</table>\n" + " </div>\n" + "</div>"

        return html
      }

      function getTargetItem(typeIndex, itemHierarchy, type) {
        if (itemHierarchy != 1) {
          var outMType = mtyAllList[outMtypeIndex]
          var outMlist = outMType.mListList[outMlistIndex]
        }
        // hierarchy 层级
        let targetItem
        // fType
        if (type == "mlistItem") {
          // mType
          if (itemHierarchy == "1") {
            targetItem = mList[typeIndex]
          } else if (itemHierarchy == "2") {
            targetItem = siClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "3") {
            targetItem = wuClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "4") {
            targetItem = liuClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "5") {
            targetItem = qiClickItem.mListList[typeIndex]
          } else {
            targetItem = baClickItem.mListList[typeIndex]
          }
        } else if (type == "mTypeItem") {
          // mlist
          if (itemHierarchy == "1") {
            targetItem = mtyAllList[typeIndex]
          } else if (itemHierarchy == "2") {
            targetItem = siClickItem.mTypeList[typeIndex]
          } else if (itemHierarchy == "3") {
            targetItem = wuClickItem.mTypeList[typeIndex]
          } else if (itemHierarchy == "4") {
            targetItem = liuClickItem.mTypeList[typeIndex]
          } else if (itemHierarchy == "5") {
            targetItem = qiClickItem.mTypeList[typeIndex]
          } else {
            targetItem = baClickItem.mTypeList[typeIndex]
          }
        }
        return targetItem
      }

      var typeOption // 定义组别变量
      var foodAllergenData = ""
      var customDetail = "" //定义通用获取到的弹窗对象（修改重新赋值） ;

      //Mlist查询追加套餐
      function queryAdOeder(code) {
        // console.log(fCode, "fCode");
        let data = {
          code
        }
        $.get({
          url: "../../manager_extraMListSetMeal/selectOne",
          data,
          dataType: "json",
          success: function (res) {
            // console.log(res, "追加套餐");
            if (res.RESULT_CODE == 0) {
              // 遍历套餐item
              let setMealItemHtml = ""
              res.list.forEach(item => {
                setMealItemHtml +=
                  ' <div class="adOrderCell">' +
                  `<p class="adOrderTitle">${item.subCode}</p>` +
                  ' <div class="adOrderItemBtn">' +
                  `<button type="button" class="btn btn-warning btn-xs" onclick="onEditSetMeal(this)" data-setMeal-item='${JSON.stringify(
                    item
                  )}'>Edit</button>` +
                  " " +
                  `<button type="button" class="btn btn-danger btn-xs" onclick="onDelSetMeal(${item.id})" >Delete</button>` +
                  "</div>" +
                  "</div>"
              })
              $("#additionalMlist").html(setMealItemHtml)
            } else {
              toastr.error("Set Meal data query failed")
            }
          },
          error: function () {
            toastr.error("Set Meal data query failed")
          }
        })
      }

      // 细项mListList弹窗
      // var customMlist = '';
      //mty需要显示/隐藏则mList反之的 差异
      function popModelOptionDiff(type) {
        if (type === "mty") {
          $("#onAdSetMealMlist").hide() // Add Set Meal 按钮 mList专用
          $("#detailModal #additionalMlist").parent().hide() // Set Meal Item  mList专用
          $("#mlistShowImg").parents(".row").show() // Mlist Show Img  mty专用
          $("#mlistColumnSelect").parents(".row").show() //Mlist Display Column mty专用
          $("#mPackingBoxMListCode").parents(".row").hide() // packingBoxMListCode  mList专用
          $("#mlistItemCtrlModelSelect").show()
        } else {
          $("#onAdSetMealMlist").show()
          $("#detailModal #additionalMlist").parent().show()
          $("#mlistShowImg").parents(".row").hide()
          $("#mlistColumnSelect").parents(".row").hide()
          $("#mPackingBoxMListCode").parents(".row").show()
          $("#mlistItemCtrlModelSelect").hide()
        }
      }
      function resetModalSubtitle(type, data = {}) {
        let code = ""
        let name = ""
        let name2 = ""
        let selector = ""
        switch (type) {
          case "mt":
            code = data.code
            name = data.desc
            name2 = data.desc2
            selector = "#detailModal"
            break
          case "ml":
            code = data.code
            name = data.name
            name2 = data.name2
            selector = "#detailModal"
            break
          default:
            break
        }

        let children = $(selector + " .pos-base-info").children()
        if (type === "mt") {
          $(children[1]).html("Modifier Group: <strong></strong>")
        } else if (type === "ml") {
          $(children[1]).html("Modifier Code: <strong></strong>")
        }
        $(children[1])
          .find("strong")
          .text(code || "")
        $(children[2])
          .find("strong")
          .text(name || "")
        $(children[3])
          .find("strong")
          .text(name2 || "")
      }
      function onMlistModal(e, type) {
        if (type == "1") {
          customDetail = mList[e]
        } else if (type == "2") {
          customDetail = siClickItem.mListList[e]
        } else if (type == "3") {
          customDetail = wuClickItem.mListList[e]
        } else if (type == "4") {
          customDetail = liuClickItem.mListList[e]
        } else if (type == "5") {
          customDetail = qiClickItem.mListList[e]
        } else {
          customDetail = baClickItem.mListList[e]
        }
        // customDetail.detailType = "Mlist";
        $("#detailNameA").val(customDetail.nameA || customDetail.name)
        $("#detailNameB").val(customDetail.nameB || customDetail.name2)
        $("#detailMulti1").val(customDetail.multi1)
        $("#detailtitle").text(customDetail.name2)
        $("#mPackingBoxMListCode").val(customDetail.packingBoxMListCode)
        // $('#lNoDup_switch').prop('checked',customDetail.lNoDup=='true')
        $("#MexpiredBanSuperior").prop("checked", Boolean(customDetail.expiredBanSuperior ?? true))

        $("#detailModal").modal("show")
        queryAdOeder(customDetail.code)
        //处理不同类型弹窗 显示不同选项
        popModelOptionDiff("mList")
        resetModalSubtitle("ml", customDetail)
        console.log(customDetail, "来源mListList点击的细项")
      }
      // 细项mTypeList弹窗
      // var customMType = '';

      function onfMtypeModal(e, type) {
        if (type == "1") {
          customDetail = mtyAllList[e]
        } else if (type == "3") {
          customDetail = wuClickItem.mTypeList[e]
        } else if (type == "4") {
          customDetail = liuClickItem.mTypeList[e]
        } else if (type == "5") {
          customDetail = qiClickItem.mTypeList[e]
        } else {
          customDetail = baClickItem.mTypeList[e]
        }

        // else if (type == "3") {
        //   customDetail = siClickItem.mTypeList[e];
        // } else {
        //   customDetail = wuClickItem.mTypeList[e];
        // }
        // customDetail.detailType = "Mtype";
        // 赋值
        $("#detailNameA").val(customDetail.nameA || customDetail.desc)
        $("#detailNameB").val(customDetail.nameB || customDetail.desc2)
        $("#detailMulti1").val(customDetail.multi1)
        $("#detailtitle").text(customDetail.desc2)

        $("#mlistColumnSelect").val(customDetail.mList_display_column || "2") //详情页mty下的mlist分栏
        console.log(customDetail, "customDetail.mList_show_src")
        $("#mlistShowImg").prop("checked", customDetail.mList_show_src)
        // $('#lNoDup_switch').prop('checked',customDetail.lNoDup=='true')
        if (customDetail.item_ctrl_model === 0 || !customDetail.item_ctrl_model) {
          $("#mlist_show_itemCtrl").prop("checked", true)
          $("#mlist_hidden_itemCtrl").removeProp("checked")
        } else {
          $("#mlist_hidden_itemCtrl").prop("checked", true)
          $("#mlist_show_itemCtrl").removeProp("checked")
        }
        $("#detailModal").modal("show")
        //处理不同类型弹窗 显示不同选项
        popModelOptionDiff("mty")
        resetModalSubtitle("mt", customDetail)
        console.log(customDetail, "来源mTypeList点击的细项")
      }

      function getCheckBoxValueOne() {
        //获取name="box"选中的checkBox的元素
        var ids = $('input:checkbox[name="allergenInput"]:checked')
        var data = ""
        for (var i = 0; i < ids.length; i++) {
          //利用三元运算符去点

          data += ids[i].value + (i == ids.length - 1 ? "" : ",")
        }
        // 一下存数组形式

        foodAllergenData = data
        console.log(foodAllergenData)
      }
      // 复选回显
      function showcheckout() {
        let allergenIcons = customfood.allergen_icons
        if (allergenIcons && allergenIcons.length != 0) {
          let checkArray = allergenIcons.split(",")
          // let checkArray = ['soybeans', 'dairy', 'eggs'];
          var checkBoxAll = $("input[name='allergenInput']")
          checkArray.forEach((item, i) => {
            $.each(checkBoxAll, function (j, checkbox) {
              var checkValue = $(checkbox).val()
              if (checkArray[i] == checkValue) {
                $(checkbox).attr("checked", true)
              }
            })
          })

          // arry.forEach(j=>{
          //   if(item.)
          // })
        }
      }

      /**
       * @description:  复选框点击事件
       * @param {string} code 数据唯一code
       * @param {dom} e 按钮dom
       * @param {string} type 数据类型(foodlist/foodType/mlistItem/mTypeItem)
       * @param {number} typeIndex 数据索引
       * @param {number} itemHierarchy 数据所在层数
       * @return {*}
       */
      function onTypeCheckbox(code, e, type, typeIndex, itemHierarchy) {
        let typeItem = getTargetItem(typeIndex, itemHierarchy, type)
        let checked = e.checked
        //获取e的data-mode
        let mode = e.getAttribute("data-mode")
        let hierarchy = pathList.length
        // 第二层的mlist数据固定调用updateOne接口,其他层级(上一层存在mlistCode)的mlist数据调用updateMListTouch接口;各自传参也不一样
        let { lastType, lastCode } = getLastCode()
        if (mode == "take-away" || mode == "dine-in") {
          let data = {
            currentType: type,
            currentCode: code,
            [mode == "take-away" ? "takeawayTouch" : "dineInTouch"]: checked
          }
          $.post({
            url: `../../manager_itemTouch/updateItemModelTouch`,
            contentType: "application/json", //默认的编码方式
            dataType: "json",
            data: JSON.stringify(data),
            success: function (result) {
              if (result.statusCode == 200) {
                typeItem[mode == "take-away" ? "takeawayTouch" : "dineInTouch"] = checked
                console.log(typeItem, "typeItem")
                toastr.success("The change was successful")
              } else {
                toastr.error("The change was failed")
                initData(hierarchy)
              }
            },
            error: function (error) {
              console.log(error, "错误")
              toastr.error("The change was failed")
              initData(hierarchy)
            }
          })
        } else {
          let data = {
            lastType,
            lastCode,
            currentType: type,
            currentCode: code,
            touch: checked
          }

          $.post({
            url: `../../manager_itemTouch/updateItemTouch`,
            dataType: "json",
            data,
            success: function (result) {
              // console.log(result, "修改按钮");
              if (result.statusCode == 200) {
                typeItem.finalTouch = checked
                let siblings = $(e)
                  .closest(".switchBox")
                  .find(".switchBox-cell")
                  .not($(e).closest(".switchBox-cell"))
                siblings.each(function () {
                  let siblingMode = $(this).find('input[type="checkbox"]').attr("data-mode")
                  $(this).find('input[type="checkbox"]').prop("disabled", !checked)
                  if (!checked) {
                    $(this).find('input[type="checkbox"]').prop("checked", false)
                  } else {
                    $(this)
                      .find('input[type="checkbox"]')
                      .prop(
                        "checked",
                        siblingMode == "take-away" ? typeItem.takeawayTouch : typeItem.dineInTouch
                      )
                  }
                })

                console.log(typeItem, "typeItem")
                toastr.success("The change was successful")
              } else {
                toastr.error("The change was failed")
                initData(hierarchy)
              }
            },
            error: function (error) {
              console.log(error, "错误")
              toastr.error("The change was failed")
              initData(hierarchy)
            }
          })
        }

        // let data = {
        //   lastType,
        //   lastCode,
        //   currentType: type,
        //   currentCode: code,
        //   touch: checked
        // }

        // $.post({
        //   url: `../../manager_itemTouch/updateItemTouch`,
        //   dataType: "json",
        //   data,
        //   success: function (result) {
        //     console.log(result, "修改按钮")
        //     if (result.statusCode == 200) {
        //       typeItem.finalTouch = checked
        //       toastr.success("The change was successful")
        //     } else {
        //       toastr.error("The change was failed")
        //       initData(hierarchy)
        //     }
        //   },
        //   error: function (error) {
        //     console.log(error, "错误")
        //     toastr.error("The change was failed")
        //     initData(hierarchy)
        //   }
        // })
      }
      function getLastCode() {
        let lastType = null,
          lastCode = null
        if (pathList && pathList.length > 1) {
          let reverseArray = pathList.concat([]).reverse()
          if (reverseArray[0].type !== "food" && reverseArray[0].type !== "Mlist") {
            //找上一级的listCode
            lastType = reverseArray[1].type
            lastCode = reverseArray[1].id
          } else {
            // 要么是food要么是mlist
            lastType = reverseArray[0].type
            lastCode = reverseArray[0].id
          }

          // 统一传递值
          lastType = lastType == "food" ? "foodlist" : "mlistItem"
        }
        return { lastType, lastCode }
      }
      // 弹出编辑追加套餐
      function onEditSetMeal(item) {
        let setMlealItem = JSON.parse(item.getAttribute("data-setMeal-item"))
        // JSON.parse();
        console.log(setMlealItem, "数据")

        $("#edSetMeal_fCode").val(setMlealItem.code)
        $("#edSetMeal_siType").val(setMlealItem.siType)
        $("#edSetMeal_subCode").val(setMlealItem.subCode)
        $("#edSetMeal_qty").val(setMlealItem.qty)
        $("#edSetMeal_minQty").val(setMlealItem.minQty)
        $("#edSetMeal_maxQty").val(setMlealItem.maxQty)
        $("#edSetMeal_seq").val(setMlealItem.seq)
        $("#edSetMeal_lNoDup").prop("checked", !!setMlealItem.lNoDup)
        // $("#edSetMeal_mymodi").val(setMlealItem.mymodi);
        $("#onEdOrderSubmit").data("idNum", setMlealItem.id)
        $("#detailModal").modal("hide")
        $("#editSetMealModal").modal("show")
      }
      // 删除追加套餐
      function onDelSetMeal(id) {
        console.log(id, "onDelSetMeal")
        let data = {
          id
        }
        $.post({
          url: "../../manager_extraMListSetMeal/deleteOne",
          dataType: "json",
          data,
          success: result => {
            initData(pathList.length)
            queryAdOeder(customDetail.code) //查询追加套餐数据
            toastr.success("Success")
            console.log(result, "删除追加套餐")
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Error")
          }
        })
        // JSON.parse();
      }
    </script>
  </head>

  <body>
    <div id="app">
      <div style="position: fixed; left: 0; top: 0; z-index: 999; width: 100%">
        <ul id="menu" class="nav nav-tabs">
          <li id="Food_detail" onclick="createSetMealElm()">
            <a>Combos & Modifiers</a>
          </li>
          <li
            id="Food_Indetail"
            onclick="createSiElm(pathList[0].index,pathList[0].id,pathList[0].type)"
          >
            <a>Modifier Options</a>
          </li>
          <li
            id="Specific_detail"
            onclick="createWuElm(pathList[1].index,pathList[1].id,pathList[1].type)"
          >
            <a>2th level Options</a>
          </li>
          <li
            id="Specific_Indetail"
            onclick="createLiuElm(pathList[2].index,pathList[2].id,pathList[2].type)"
          >
            <a>3th level Options</a>
          </li>
          <li
            id="Specific_Indetail_four"
            onclick="createQiElm(pathList[3].index,pathList[3].id,pathList[3].type)"
          >
            <a>4th level Options</a>
          </li>
          <li id="Specific_Indetail_five">
            <a>5th level Options</a>
          </li>
        </ul>
      </div>
      <div id="body_warp" style="margin-top: 45px; margin-right: 10px">
        <div class="process">
          <img id="loading" src="../../static/img/svg/loading1.svg" />
          <!-- <progress class="processbar" id="processbar" translate="yes" max="100" value="0"></progress> -->
          <div class="_load">
            Network speed:
            <span class="pro">0.00</span>
            kb/s
          </div>
          <div class="_load">
            Data transmitted:
            <span class="sumData">0.00</span>
            kb
          </div>
        </div>
      </div>

      <!-- 细项Modal -->
      <div
        class="modal fade"
        id="detailModal"
        role="dialog"
        aria-labelledby="detailLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-xl" style="top: 5%">
          <div class="modal-content">
            <div class="modal-header">
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <div class="pos-base-info">
                <p>POS Info</p>
                <p>
                  Modifier Group:
                  <strong></strong>
                </p>
                <p>
                  Name #1:
                  <strong></strong>
                </p>
                <p>
                  Name #2:
                  <strong></strong>
                </p>
              </div>
              <form>
                <div class="form-type-item">
                  <div class="type-title">
                    <strong></strong>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title" for="detailNameA">
                      Override Name 1
                    </label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control" id="detailNameA" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title" for="detailNameB">
                      Override Name 2
                    </label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control" id="detailNameB" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-4 col-form-label label_title"
                      for="detailMulti1"
                    >
                      Name 3
                    </label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control" id="detailMulti1" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-4 col-form-label label_title"
                      for="mlistColumnSelect"
                    >
                      Items per row
                    </label>
                    <div class="col-sm-8">
                      <select class="form-control" id="mlistColumnSelect">
                        <option>1</option>
                        <option>2</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row" id="mlistItemCtrlModelSelect">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      If Sold Out
                    </label>
                    <div class="col-sm-8">
                      <form>
                        <input
                          type="radio"
                          name="showMlistItemCtrl"
                          id="mlist_show_itemCtrl"
                          value="0"
                        />
                        <label for="mlist_show_itemCtrl">Display 'Sold Out' overlay</label>
                        <input
                          style="margin-left: 30px"
                          type="radio"
                          id="mlist_hidden_itemCtrl"
                          name="showMlistItemCtrl"
                          value="1"
                        />
                        <label for="mlist_hidden_itemCtrl">Hidden</label>
                      </form>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      Display visual
                    </label>
                    <div class="col-sm-8 switchWarp">
                      <label class="switch" style="float: left">
                        <input type="checkbox" id="mlistShowImg" />
                        <div class="slider round"></div>
                      </label>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-4 col-form-label label_title"
                      for="mPackingBoxMListCode"
                    >
                      Takeaway Packaging
                      <span>for automatic inclusion in takeaway orders</span>
                    </label>
                    <div class="col-sm-8">
                      <input
                        type="text"
                        class="form-control"
                        id="mPackingBoxMListCode"
                        placeholder="Input POS modifier code"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      Bans up-level when expired
                    </label>
                    <div class="col-sm-8 switchWarp">
                      <label class="switch">
                        <input type="checkbox" id="MexpiredBanSuperior" />
                        <div class="slider round"></div>
                      </label>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      Set Meal Item
                    </label>
                    <div class="col-sm-8 adOrderWarp" id="additionalMlist"></div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <!-- Mlist新增套餐按钮 -->
              <button
                type="button"
                class="btn btn-info"
                id="onAdSetMealMlist"
                data-bs-toggle="modal"
                data-bs-target="#adSetMealModal"
              >
                Add Set Meal
              </button>
              <button type="button" class="btn btn-primary" id="ondetailSubmit">Submit</button>
            </div>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal -->
      </div>
      <!--追加套餐添加Modal    -->
      <div
        class="modal fade"
        id="adSetMealModal"
        role="dialog"
        aria-labelledby="addOrderModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-xl" role="document" style="top: 5%">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Add Set Meal</h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <form id="adOrderForm">
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">siType:</label>
                  <div class="col-sm-9">
                    <select class="form-control form-select" id="adSetMeal_siType">
                      <option value="">Please select</option>
                      <option value="3">MList</option>
                      <option value="4">MType</option>
                    </select>
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">subCode</label>
                  <div class="col-sm-9">
                    <input type="text" class="form-control" id="adSetMeal_subCode" name="subCode" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Qty</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_qty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Minimum Qty</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_minQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Maximum Qty</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_maxQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Seq</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_seq" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">No Dup</label>
                  <div class="col-sm-9 switchWarp">
                    <label class="switch" style="float: left">
                      <input type="checkbox" id="adSetMeal_lNoDup" />
                      <div class="slider round"></div>
                    </label>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="onAdSetMealSub">
                Save changes
              </button>
            </div>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
      <!-- 追加套餐编辑Modal -->
      <div
        class="modal fade"
        id="editSetMealModal"
        role="dialog"
        aria-labelledby="editSetMealLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-xl" role="document" style="top: 15%">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Edit Set Meal</h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <form id="editOrderForm">
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">siType</label>
                  <div class="col-sm-9">
                    <select class="form-control form-select" id="edSetMeal_siType">
                      <option value="3">MList</option>
                      <option value="4">MType</option>
                    </select>
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">subCode</label>
                  <div class="col-sm-9">
                    <input type="text" class="form-control" id="edSetMeal_subCode" name="subCode" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Qty</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_qty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Minimum Qty</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_minQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Maximum Qty</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_maxQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">Seq</label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_seq" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">No Dup</label>
                  <div class="col-sm-9 switchWarp">
                    <label class="switch" style="float: left">
                      <input type="checkbox" id="edSetMeal_lNoDup" />
                      <div class="slider round"></div>
                    </label>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="onEdOrderSubmit">
                Save changes
              </button>
            </div>
          </div>
          <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
      </div>
      <!-- 搜索food/mlist功能弹窗 -->
      <div
        class="modal fade"
        id="searchListModal"
        role="dialog"
        aria-labelledby=""
        aria-hidden="true"
        tabindex="-1"
      >
        <div class="modal-dialog modal-xl modal-dialog-centered">
          <div class="modal-content searchListContent">
            <div class="modal-body">
              <!-- 搜索框 -->
              <form onsubmit="return false">
                <div class="form-group row">
                  <div class="input-group flex-nowrap searchListWarp input-group-lg">
                    <span class="input-group-text" id="inputGroup-sizing-lg">
                      <!-- <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                    class="bi bi-search" viewBox="0 0 16 16">
                    <path
                      d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z" />
                  </svg> -->
                      <label
                        class="DocSearch-MagnifierLabel"
                        for="docsearch-input"
                        id="docsearch-label"
                      >
                        <svg
                          width="20"
                          height="20"
                          class="DocSearch-Search-Icon"
                          viewBox="0 0 20 20"
                          stroke-opacity=".5"
                        >
                          <path
                            d=" M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533
                      0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418
                      2.9419 7.7115 0 10.6533z"
                            stroke="currentColor"
                            fill="none"
                            fill-rule="evenodd"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          ></path>
                        </svg>
                        <svg
                          class="DocSearch-Loading-Icon"
                          viewBox="0 0 38 38"
                          stroke="currentColor"
                          stroke-opacity=".5"
                        >
                          <g fill="none" fill-rule="evenodd">
                            <g transform="translate(1 1)" stroke-width="2">
                              <circle stroke-opacity=".3" cx="18" cy="18" r="18"></circle>
                              <path
                                d="M36 18c0-9.94-8.06-18-18-18"
                                transform="rotate(354.267 18 18)"
                              >
                                <animateTransform
                                  attributeName="transform"
                                  type="rotate"
                                  from="0 18 18"
                                  to="360 18 18"
                                  dur="1s"
                                  repeatCount="indefinite"
                                ></animateTransform>
                              </path>
                            </g>
                          </g>
                        </svg>
                      </label>
                      <div class="searchList-LoadingIndicator">
                        <svg viewBox="0 0 38 38" stroke="currentColor" stroke-opacity=".5">
                          <g fill="none" fill-rule="evenodd">
                            <g transform="translate(1 1)" stroke-width="2">
                              <circle stroke-opacity=".3" cx="18" cy="18" r="18"></circle>
                              <path d="M36 18c0-9.94-8.06-18-18-18">
                                <animateTransform
                                  attributeName="transform"
                                  type="rotate"
                                  from="0 18 18"
                                  to="360 18 18"
                                  dur="1s"
                                  repeatCount="indefinite"
                                ></animateTransform>
                              </path>
                            </g>
                          </g>
                        </svg>
                      </div>
                    </span>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Search Mlist Data"
                      aria-label=""
                      id="searchListInput"
                      aria-describedby="inputGroup-sizing-lg"
                      autofocus="true"
                      spellcheck="false"
                    />
                  </div>
                </div>
              </form>
              <!-- 展示搜索结果 -->
              <div class="listSearch-StartScreen">
                <ul class="searchList-Data"></ul>
                <p class="listSearch-Help">No recent searches</p>
              </div>
            </div>
            <div class="modal-footer">
              <ul class="searchList-Commands">
                <li>
                  <kbd class="searchList-Commands-Key">
                    <svg width="15" height="15" aria-label="Enter key" role="img">
                      <g
                        fill="none"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.2"
                      >
                        <path d="M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"></path>
                      </g>
                    </svg>
                  </kbd>
                  <span class="searchList-Label">to select</span>
                </li>

                <li>
                  <kbd class="searchList-Commands-Key">
                    <svg width="15" height="15" aria-label="Escape key" role="img">
                      <g
                        fill="none"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.2"
                      >
                        <path
                          d="M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"
                        ></path>
                      </g>
                    </svg>
                  </kbd>
                  <span class="searchList-Label">to close</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 遮罩 -->
      <div id="cover"></div>
      <!-- /.modal -->
      <!-- tost -->
    </div>
  </body>
  <script src="../../static/cmsUtils/cmsSearchList/searchList.js"></script>

  <script>
    window.onload = async function () {
      await getUIConfig(domain)
      await initData("init")
      // 获取版本信息,设置边框
      let version = sessionStorage.getItem("versionNumber")
      if (version !== "PROD") {
        $("#body_warp").addClass("border")
        $("#body_warp.border").attr("version", versionTag())
      }
      function versionTag() {
        let version = sessionStorage.getItem("versionNumber")
        switch (version) {
          case "0":
            return "UAT"
          case "PROD":
            return "PROD"
          default:
            let date = moment(parseInt(version)).format("YYYY-MM-DD")
            let time = moment(parseInt(version)).format("HH:mm:ss")
            if (date !== "Invalid date" && time !== "Invalid date") {
              return `${date} ${time}`
            } else {
              return "HISTORY"
            }
        }
      }
      function toNum(val) {
        if (val) {
          return Number(val)
        } else {
          return ""
        }
      }
      // 模态框提交
      $("#onSubmit").click(function () {
        var value = $("#TextArea1").val()
      })
      // 追加套餐t添加弹窗确定
      $("#onAdSetMealSub").click(function () {
        let sitype = $("#adSetMeal_siType option:selected").val()
        let subCode = $("#adSetMeal_subCode").val()
        console.log(sitype, subCode)
        if (!sitype || !subCode) {
          toastr.error("Please complete siType/subCode")
          return
        }
        let data = {
          siType: +$("#adSetMeal_siType option:selected").val(),
          subCode: $("#adSetMeal_subCode").val(),
          qty: $("#adSetMeal_qty").val(),
          maxQty: toNum($("#adSetMeal_maxQty").val()),
          minQty: toNum($("#adSetMeal_minQty").val()),
          seq: toNum($("#adSetMeal_seq").val()),
          lNoDup: $("#adSetMeal_lNoDup").prop("checked"),
          code: customDetail.code
          // mymodi: $("#adSetMeal_mymodi").val(),
        }
        $.post({
          url: "../../manager_extraMListSetMeal/addOne",
          dataType: "json",
          data,
          success: function (res) {
            if (res.RESULT_CODE == 0) {
              toastr.success("Success")
              // queryAdOeder(fCode)  //查询追加套餐数据
              initData(pathList.length)
              console.log(res, "追加套餐")
              queryAdOeder(customDetail.code)
              $("#adSetMealModal").modal("hide")
            } else {
              toastr.error("Fail to Add")
            }
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Fail to add ")
          }
        })
      })
      // 追加套餐编辑弹窗确定
      $("#onEdOrderSubmit").click(function () {
        let sitype = $("#edSetMeal_siType option:selected").val()
        let subCode = $("#edSetMeal_subCode").val()
        if (!sitype || !subCode) {
          toastr.error("Please complete siType/subCode")
          return
        }
        let data = {
          id: $("#onEdOrderSubmit").data("idNum"),
          siType: +$("#edSetMeal_siType option:selected").val(),
          subCode: $("#edSetMeal_subCode").val(),
          qty: $("#edSetMeal_qty").val(),
          minQty: toNum($("#edSetMeal_minQty").val()),
          maxQty: toNum($("#edSetMeal_maxQty").val()),
          seq: toNum($("#edSetMeal_seq").val()),
          lNoDup: $("#edSetMeal_lNoDup").prop("checked"),
          code: customDetail.code
          // mymodi: $("#edSetMeal_mymodi").val(),
        }
        // console.log(toNum($("#edSetMeal_minQty").val()), 'edSetMeal_minQty')
        $.post({
          url: "../../manager_extraMListSetMeal/updateOne",
          dataType: "json",
          data,
          success: function (res) {
            if (res.RESULT_CODE == 0) {
              toastr.success("Success")
              console.log(res, "编辑追加套餐")
              queryAdOeder(customDetail.code)
              $("#editSetMealModal").modal("hide")
            } else {
              toastr.error("Fail to Edit")
            }
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Error")
          }
        })
      })
      // 细项弹窗确定
      $("#ondetailSubmit").click(function () {
        // toastr.error('Error');
        let type = customDetail.detailType
        let lan1 = $("#detailNameA").val()
        let lan2 = $("#detailNameB").val()
        let lan3 = $("#detailMulti1").val()
        switch (type) {
          case "setM":
            console.log("setM")
            customDetail.name = lan1
            customDetail.name2 = lan2
            break
          case "Mlist":
            console.log("Mlist")
            var code = customDetail.code
            subMlist(code, lan1, lan2, lan3)
            // customDetail.name = lan1
            // customDetail.name2 = lan2
            break
          case "Mtype":
            console.log("Mtype")
            var code = customDetail.code

            subMtylist(code, lan1, lan2, lan3)
            break
        }
        console.log(customDetail, "提交后的对象")
        $("#detailModal").modal("hide")
      })
      // mlist第三语言提交
      function subMlist(code, lan1, lan2, lan3) {
        let expiredBanSuperior = $("#MexpiredBanSuperior").prop("checked")
        let packingBoxMListCode = $("#mPackingBoxMListCode").val()
        let obj = {
          code,
          nameA: lan1,
          nameB: lan2,
          multi1: lan3,
          packingBoxMListCode,
          expiredBanSuperior
        }
        $.post({
          url: "../../mList/update",
          dataType: "json",
          data: obj,
          success: function (res) {
            console.log(res)
            if (res.RESULT_CODE == -1) {
              toastr.error("Error")
            } else {
              toastr.success("Success")
              customDetail.nameA = lan1
              customDetail.nameB = lan2
              customDetail.multi1 = lan3
              customDetail.packingBoxMListCode = packingBoxMListCode
              customDetail.expiredBanSuperior = expiredBanSuperior
            }
            $("#foodModal").modal("hide")
            // console.log(onfoodType, 'q请求');
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Error")
          }
        })
      }
      // mtylist第三语言提交
      function subMtylist(code, lan1, lan2, lan3) {
        let obj = {
          code,
          nameA: lan1,
          nameB: lan2,
          multi1: lan3,
          mList_display_column: $("#mlistColumnSelect option:selected").val(),
          mList_show_src: $("#mlistShowImg").prop("checked"),
          item_ctrl_model: $("input[name='showMlistItemCtrl']:checked").val()
          // lNoDup:$("#lNoDup_switch").prop('checked')
        }
        $.post({
          url: "../../manager_mType/updateOne",
          dataType: "json",
          data: obj,
          success: function (res) {
            console.log(res)
            if (res.RESULT_CODE == -1) {
              toastr.error("Error")
            } else {
              toastr.success("Success")
              customDetail.desc = lan1
              customDetail.desc2 = lan2
              customDetail.multi1 = lan3
              customDetail.mList_display_column = obj.mList_display_column
              customDetail.mList_show_src = obj.mList_show_src
              customDetail.item_ctrl_model = obj.item_ctrl_model
            }
            $("#foodModal").modal("hide")
            // console.log(onfoodType, 'q请求');
          },
          error: function (data) {
            toastr.error("Error")
            console.log(data, "错误")
          }
        })
      }

      $(function () {
        //自定义参数
        toastr.options = {
          closeButton: false, //是否显示关闭按钮（提示框右上角关闭按钮）。
          debug: false, //是否为调试。
          progressBar: true, //是否显示进度条（设置关闭的超时时间进度条）
          positionClass: "toast-top-center", //消息框在页面显示的位置
          onclick: null, //点击消息框自定义事件
          showDuration: "300", //显示动作时间
          hideDuration: "1000", //隐藏动作时间
          timeOut: "2000", //自动关闭超时时间
          extendedTimeOut: "1000",
          showEasing: "swing",
          hideEasing: "linear",
          showMethod: "fadeIn", //显示的方式，和jquery相同
          hideMethod: "fadeOut" //隐藏的方式，和jquery相同
          //等其他参数o
        }
      })

      //当meal弹窗关闭
      let adSetMealModalElement = document.getElementById("adSetMealModal")
      adSetMealModalElement.addEventListener("hidden.bs.modal", function (event) {
        $("#detailModal").modal("show")
        document.querySelector("#adOrderForm").reset()
      })
      let editSetMealModalElement = document.getElementById("editSetMealModal")
      editSetMealModalElement.addEventListener("hidden.bs.modal", function (event) {
        $("#detailModal").modal("show")
        document.querySelector("#editOrderForm").reset()
      })
    }
  </script>
</html>
