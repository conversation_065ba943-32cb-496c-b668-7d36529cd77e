<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link href="../../static/elementUI/index.css" rel="stylesheet" />
    <!-- 引入组件库 -->
    <script src="../../static/moment/moment.js"></script>
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>
    <script src="../../static/cmsUtils/commonFunction.js"></script>
    <script src="../../static/js/versionTag.js"></script>
    <!--    <script src="./mock.js"></script>-->
    <style>
      .version-number {
        font-size: 25px !important;
        padding: 5px 30px !important;
      }
      .el-form {
        display: flex;
        flex-wrap: wrap;
      }
      .el-table__body tr.current-row td {
        background-color: #e2e1e1 !important;
      }

      .search-f {
        display: flex;
        align-items: center;
      }

      .search-f .el-input {
        width: auto;
      }

      .search-f .el-input__inner {
        height: 30px;
        line-height: 30px;
        flex: 1;
        width: 400px;
      }
      .search-st {
        padding-left: 4px;
      }

      :is(.search-ft, .search-st) .el-select .el-input__inner {
        height: 30px;
        line-height: 30px;
        width: 350px;
      }

      .search-f .el-input__icon {
        line-height: 30px;
      }

      .search-f .el-button {
        height: 30px;
        line-height: 28px;
        color: #fff;
        padding: 0 10px;
        margin-left: 20px;
      }

      .el-form {
        padding: 10px 0 0 5px;
        position: relative;
      }

      .el-form .el-form-item,
      .el-form .el-form-item__content {
        /*display: inline-block;*/
        margin-right: 20px;
        display: flex;
        width: fit-content;
      }

      .el-form .el-form-item {
        margin-bottom: 5px;
      }

      .el-table_1_column_1 .el-input__inner {
        text-align: center;
      }

      .el-form :first-child .el-form-item__label {
        padding-right: 16px;
      }

      /*  tooltip */
      .el-tooltip__popper.is-light {
        border: 1px solid #cbc4c4;
        padding: 5px;
      }

      .el-tooltip__popper[x-placement^="top"] .popper__arrow {
        border-top-color: #cbc4c4 !important;
      }
      .el-message-box {
        vertical-align: unset;
      }
    </style>
  </head>
  <body>
    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>

    <div id="app">
      <version-tag style="border-top: 5px solid #d24735" v-if="showVersionTag"></version-tag>
      <el-form>
        <el-form-item label="Food Type">
          <div class="search-ft">
            <el-select
              clearable
              filterable
              placeholder="Please select a food type to search"
              v-model="tempFoodTypeCode"
            >
              <el-option
                :key="item"
                :label="handleFtyLabel(item)"
                :value="item.code"
                v-for="item in foodTypeCodeList"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="Store Type">
          <div class="search-st">
            <el-select
              clearable
              filterable
              multiple
              placeholder="Please select store type"
              v-model="searchStoreTypeKey"
            >
              <el-option
                :key="item.id"
                :label="item.tag_name"
                :value="item.id"
                v-for="item in storeTypeList"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="Food Code" style="flex-basis: 100%">
          <div class="search-f">
            <el-input
              @input="tempSearchKey=tempSearchKey.toUpperCase()"
              clearable
              placeholder="Please enter a food code to search"
              prefix-icon="el-icon-search"
              v-model="tempSearchKey"
            ></el-input>
            <el-button
              :disabled="t_loading"
              :loading="b_loading"
              @click="handleClickSearch"
              type="primary"
            >
              Search
            </el-button>
            <el-button @click="handleClickReset" type="info">Reset</el-button>
          </div>
        </el-form-item>
        <div class="toggleDesc" style="position: absolute; bottom: 0; right: 40px">
          <el-dropdown :hide-on-click="false" trigger="click">
            <el-tag class="el-dropdown-link" type="info">
              Desc
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-tag>
            <el-dropdown-menu class="drop-down-desc" ref="dropDownDesc" slot="dropdown">
              <el-dropdown-item>
                <el-checkbox v-model="toggleDesc.desc1">Desc1</el-checkbox>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-checkbox v-model="toggleDesc.desc2">Desc2</el-checkbox>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-form>
      <el-table
        :data="dataList"
        :fit="false"
        :max-height="tableHeight"
        :stripe="true"
        border
        highlight-current-row
        ref="table"
        style="width: 100%; user-select: none"
        v-loading="t_loading"
      >
        <!--        :render-header="renderHeader"-->
        <el-table-column align="center" fixed label="Food Code" width="150">
          <template slot-scope="scope">{{scope.row.fCode}}</template>
        </el-table-column>
        <el-table-column align="center" fixed label="Desc1" v-if="toggleDesc.desc1" width="200">
          <template slot-scope="scope">{{scope.row.desc1}}</template>
        </el-table-column>
        <el-table-column align="center" fixed label="Desc2" v-if="toggleDesc.desc2" width="200">
          <template slot-scope="scope">{{scope.row.desc2}}</template>
        </el-table-column>
        <el-table-column align="center" fixed label="All" width="220">
          <template slot-scope="scope">
            <!--                allSelect[scope.row.label]     :value="exposeAllChange(scope.row).v"     -->
            <el-radio-group
              @change="handleAllChange($event,scope.row)"
              @click.stop.native
              size="mini"
              v-model="allSelect[scope.row.fCode]"
            >
              <el-radio-button label="Enable"></el-radio-button>
              <el-radio-button disabled label="-"></el-radio-button>
              <el-radio-button label="Disable"></el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column
          :key="item"
          :prop="item"
          :render-header="(h,obj) => renderHeader(h,obj,item)"
          :width="autoWidth(item)"
          align="center"
          v-for="(item,index) in storeNumberList"
        >
          <template slot-scope="scope">
            <el-switch
              @change="handleChangeSwitchOfStoreNumber($event,scope.row,item)"
              @click.stop.native
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.store[item]"
            ></el-switch>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :disabled="t_loading||b_loading"
        :page-size="pageSize"
        :page-sizes="[20, 30, 40, 50]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        background
        layout="total, sizes, prev, pager, next, jumper"
        style="
          width: 100%;
          position: fixed;
          bottom: 5px;
          opacity: 1;
          z-index: 100;
          background-color: #f4f4f4;
        "
      ></el-pagination>
    </div>
    <script>
      const app = new Vue({
        el: "#app",
        data() {
          return {
            //all栏对应的状态
            allSelect: {},
            //table高度
            tableHeight: 0,
            // domain: 'test2',
            domain: sessionStorage.getItem("domain"),
            showVersionTag: false,
            dataList: [],
            storeNumberList: [],
            storeTypeList: [],
            page: 1, //当前页
            searchKey: "", //搜索关键字
            tempSearchKey: "", //临时存储搜索关键字
            pageSize: 20, //每页显示条数
            total: 0, //总条数
            t: null, //节流定时器
            t_loading: false, //table加载状态
            b_loading: false, //搜索按钮加载状态
            foodTypeCodeList: [], //foodTypeCode列表
            foodTypeCode: "", //选择的foodTypeCode
            tempFoodTypeCode: "", //临时存储foodTypeCode
            searchStoreTypeKey: [], //搜素storeType.多选
            toggleDesc: {
              desc1: true,
              desc2: false
            }, //是否显示desc列
            copiedStore: null //已复制的storeNumber
          }
        },
        created() {
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 170 //数值"140"根据需要调整
          let version = sessionStorage.getItem("versionNumber") || ""
          this.showVersionTag = version !== "PROD"
        },
        mounted() {
          this.changeDropDownPos()
          this.getData()
          this.getAllFoodTypeCode()
          this.getStoreTypeList()
        },
        updated() {
          //解决el-table组件高度自适应问题:表头fixed后表格错位
          //缺点是会导致闪烁
          setTimeout(() => {
            this.$refs.table.doLayout()
          })
        },
        components: {
          VersionTag
        },
        methods: {
          getStoreTypeList() {
            let data = {
              domain: this.domain
            }
            $.get({
              url: "../../manager_StoreType/selectAll",
              data,
              xhrFields: {
                responseType: "json"
              },
              success: res => {
                if (res.statusCode !== 200) {
                  this.$message.error("Query store type data error！")
                } else {
                  // let sortData=res.data.sort((a,b)=>a.id-b.id)
                  console.log(res.data, "store Type list")
                  this.storeTypeList = res.data
                }
              },
              error: function (error) {
                console.log(error)
                // this.$message.error('Fail to load！');
              }
            })
          },
          //改变下拉框的位置,默认有偏移
          //逻辑:侦听dom的attribute : x-placement改变,则改变dom的left属性
          changeDropDownPos() {
            let target = document.querySelector(".drop-down-desc")
            let options = {
              attributes: true, //观察node对象的属性
              attributeFilter: ["x-placement"] //需要观察的属性
            }
            let mb = new MutationObserver(() => {
              target.style.left = document.body.offsetWidth - 130 + "px"
            })
            mb.observe(target, options)
          },
          //处理搜索框foodTypeCode的label处理
          handleFtyLabel(r) {
            return `${r.code} | ${r.name} | ${r.name2} `
          },
          //自定义表头
          renderHeader(h, { column, $index }, item) {
            //el-tooltip 使用插槽content
            return h("div", {}, [
              h(
                "el-tooltip",
                {
                  props: {
                    effect: "light",
                    placement: "top-start"
                    // value:true
                  }
                },
                [
                  h(
                    "span",
                    {
                      style: {
                        color: "#8492a6",
                        fontSize: "14px",
                        cursor: "pointer"
                      }
                    },
                    item
                  ),
                  h(
                    "div",
                    {
                      slot: "content"
                    },
                    [
                      h(
                        "span",
                        {
                          // slot:'content',
                          style: {
                            color: "#a3a0a0",
                            cursor: "pointer",
                            userSelect: "none",
                            display: "block",
                            fontSize: "13px",
                            marginBottom: "3px"
                          }
                        },
                        `copied:  ${this.copiedStore || ""}`
                      ),
                      h(
                        "el-button",
                        {
                          // slot:'content',
                          style: {
                            // color: '#f4f4f4',
                            fontSize: "12px",
                            cursor: "pointer",
                            userSelect: "none",
                            padding: "2px 5px",
                            borderRadius: "4px"
                          },
                          props: {
                            type: "primary"
                          },
                          on: {
                            click: e => {
                              e.preventDefault()
                              this.copiedStore = item
                            }
                          }
                        },
                        "copy"
                      ),
                      h(
                        "el-button",
                        {
                          // slot:'content',
                          props: {
                            type: "info"
                          },
                          style: {
                            color: "#f4f4f4",
                            fontSize: "12px",
                            cursor: "pointer",
                            userSelect: "none",
                            padding: "2px 5px",
                            borderRadius: "4px"
                          },

                          on: {
                            click: async () => {
                              if (this.copiedStore && this.copiedStore !== item) {
                                await this.$confirm(
                                  `All data of store number ${item} will be overwritten by store number ${this.copiedStore}, please confirm the operation`,
                                  "warning",
                                  {
                                    distinguishCancelAndClose: true,
                                    confirmButtonText: "confirm",
                                    cancelButtonText: "cancel"
                                  }
                                )
                                  .then(async r => {
                                    await this.requestCopyStore(this.copiedStore, item)
                                    this.getData(this.pageSize, this.pageNum, this.searchKey)
                                  })
                                  .catch(e => {
                                    this.$message({
                                      message: "Operation cancelled"
                                    })
                                  })
                              } else {
                                this.$message({
                                  message: this.copiedStore
                                    ? "The copied value is equal to the destination."
                                    : "please copy first",
                                  type: "warning"
                                })
                              }
                            }
                          }
                        },
                        "paste"
                      )
                    ]
                  )
                ]
              )
            ])
          },
          async requestCopyStore(copiedStore, pasteStore) {
            const data = {
              copyStoreNum: copiedStore,
              pasteStoreNum: pasteStore,
              domain: this.domain
            }
            await $.ajax({
              url: "../../manager_storeNumber_foodCode/copyStore",
              type: "post",
              data,
              success: res => {
                res = JSON.parse(res)
                if (res.statusCode === 200) {
                  this.$message({
                    message: "Successful operation",
                    type: "success"
                  })
                } else {
                  this.$message({
                    message: "Operation failed, please try again !",
                    type: "warning"
                  })
                }
              },
              error: () => {
                this.$message({
                  message: "Operation failed, please try again !",
                  type: "warning"
                })
              }
            })
          },
          //处理点击重置按钮
          handleClickReset() {
            this.tempSearchKey = ""
            this.searchKey = ""
            this.tempFoodTypeCode = ""
            this.foodTypeCode = ""
            this.searchStoreTypeKey = []
          },
          //table表头宽度自适应
          autoWidth(r) {
            if (r) {
              let widthNum,
                minWidth = 65 //最小宽度
              if (isNaN(Number(r))) {
                widthNum = r.toString().split("").length * 16
              } else {
                widthNum = r.toString().split("").length * 30
              }
              if (widthNum < minWidth) {
                return minWidth + "px"
              } else {
                return widthNum + "px"
              }
            }
          },
          //处理点击搜索按钮逻辑
          handleClickSearch() {
            // if(this.t)  return
            this.b_loading = true
            // this.t = setTimeout(() => {
            this.searchKey = this.tempSearchKey
            this.foodTypeCode = this.tempFoodTypeCode
            this.page = 1
            this.getData(this.pageSize, 1, this.searchKey, "search")
            //     this.t = null;
            // }, 100);
          },
          //点击storeNumber对应的switch开关
          handleChangeSwitchOfStoreNumber(e, row, storeNumber) {
            // console.log(e)
            const { fCode, store } = row
            //判断是否满足全选\反选
            const u = Object.values(store).reduce((a, c) => a + c, 0)
            this.allSelect[fCode] =
              u === Object.values(store).length ? "Enable" : u ? "-" : "Disable"

            //发送请求
            this.sendRequest("one", !e, row, storeNumber)
          },
          getData(size, page, key, type) {
            // const r = init()
            // console.log(r)
            this.t_loading = true
            const data = {
              domain: this.domain,
              pageSize: size || 20,
              pageNum: page || 1,
              searchFoodCode: key || this.searchKey || "",
              searchFoodTypeCode: this.foodTypeCode || "",
              searchStoreTypeCode: this.searchStoreTypeKey.toString() || ""
            }
            $.get({
              url: "../../manager_storeNumber_foodCode/getStoreNumberFoodCodeByDomain",
              dataType: "json",
              data,
              success: r => {
                this.t_loading = false
                this.b_loading = false
                this.dataList = r.data.data //数据
                this.setAllFCodeSelect(r.data.storeNumber, r.data.data) //设置全选\反选
                this.storeNumberList = r.data.storeNumber //存储店铺编号
                this.total = r.data.foodCodeCount //总条数
                if (type === "search") {
                  let msg = null
                  if (r.data.foodCodeCount) {
                    msg = `${r.data.foodCodeCount} pieces of data were queried`
                  } else {
                    msg = `No data was queried`
                  }
                  this.$notify({
                    title: "Query done!",
                    message: msg,
                    offset: 30,
                    type: "success",
                    duration: 3000
                  })
                }
              },
              error: e => {
                this.t_loading = false
                this.b_loading = false
                console.log(e)
                this.$message({
                  message: "Failed to get data, please try again later",
                  type: "error"
                })
              }
            })
          },
          //获取所有的foodTypeCode数据
          getAllFoodTypeCode() {
            const data = {
              domain: this.domain
            }
            $.get({
              url: "../../manager_storeNumber_foodCode/getFoodTypeSelectData",
              dataType: "json",
              data,
              success: r => {
                this.foodTypeCodeList = r.data
                // this.foodTypeCodeList = createArray()//创建foodTypeCode数组
              },
              error: e => {
                console.log(e)
                this.$message({
                  message: "Failed to get data, please try again later",
                  type: "error"
                })
              }
            })
          },
          //sendRequest:switch改变时请求
          sendRequest(type, e, row, storeNumber) {
            let params = {
              domain: this.domain,
              foodCode: row.fCode
            }
            let p = ""
            if (type === "all") {
              p = "updateAllStoreNumberByFoodCode"
              params.oldStatus = e !== "Enable"
              params.storeNumberCode = this.storeNumberList.toString()
            } else {
              p = "updateOne"
              params.oldStatus = e
              params.storeNumber = storeNumber
            }
            $.post({
              url: `../../manager_storeNumber_foodCode/${p}`,
              dataType: "json",
              data: params,
              success: r => {
                // console.log(r)
                if (r.statusCode === 200) {
                  //将当前row下所有的store对应开关更改,或重新获取数据渲染
                  if (type === "all") {
                    this.changeSwitchInRow(e, row)
                  }
                  this.$message({
                    message: "Successfully modified!",
                    type: "success"
                  })
                } else {
                  this.$message({
                    message: "Update failed, please try again later",
                    type: "error"
                  })
                  this.getData(this.pageSize, this.page, this.searchKey)
                }
              },
              error: e => {
                this.$message({
                  message: "Update failed, please try again later",
                  type: "error"
                })
                this.getData(this.pageSize, this.page, this.searchKey)
                // console.log(e)
              }
            })
          },
          //点击all时,触发事件
          handleAllChange(e, row) {
            // console.log(e)
            //发送请求
            this.sendRequest("all", e, row)
          },
          //改变当前row下的所有switch开关
          changeSwitchInRow(e, row) {
            const d = row.store
            let s = e === "Enable"
            for (let key in d) {
              if (Object.hasOwnProperty.call(d, key)) {
                d[key] = s
              }
            }
          },
          //为每个fCode对应的store/all设置一个状态,在获取到数据时执行
          setAllFCodeSelect(storeNumber, data) {
            data.forEach(e => {
              //vArr : 一行中所有的value值数组,不包含第一位label
              const vArr = Object.values(e.store) || []
              const v =
                vArr.reduce((a, c) => a + c, 0) === storeNumber.length
                  ? "Enable"
                  : vArr.reduce((a, c) => a + c, 0) === 0
                  ? "Disable"
                  : "-"
              //长度相同,则全为true,全选   长度为true,反之长度=0,则不存在true,为反选,
              // const v = vArr.length === storeNumber.length ? 'Enable' : vArr.length ? '-' : 'Disable'
              this.$set(this.allSelect, e.fCode, v)
            })
          },
          //每页的size改变时触发,20/40/60
          handleSizeChange(val) {
            this.pageSize = val
            this.getData(val, this.page, this.searchKey || "")
            this.$refs["table"].bodyWrapper.scrollTop = 0
          },
          //每页的page改变时触发,1/2/3
          handleCurrentChange(val) {
            this.page = val
            this.getData(this.pageSize, val, this.searchKey || "")
            this.$refs["table"].bodyWrapper.scrollTop = 0
          }
        }
      })
    </script>
  </body>
</html>
