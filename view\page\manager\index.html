<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title></title>
    <!-- boostrap -->
    <link href="../../static/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <script src="../../static/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="../../static/plugins/fontawesome-free/css/all.min.css" />
    <!-- overlayScrollbars -->
    <link
      rel="stylesheet"
      href="../../static/plugins/overlayScrollbars/css/OverlayScrollbars.min.css"
    />
    <link rel="stylesheet" href="../../static/css/index.css" />
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <!-- <script src="dist/js/adminlte.js"></script> -->
    <!-- 折叠侧边栏 -->
    <link href="../../static/nav/css/style.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../../static/nav/css/reset.css" />
    <!-- 弹窗组件 -->
    <link rel="stylesheet" href="../../static/plugins/toastr/toastr.css" />
    <script src="../../static/plugins/toastr/toastr.min.js"></script>
    <!-- 开关组件 -->
    <script src="../../static/plugins/jelly-switch/jelly-switch.js"></script>
    <!-- 引入moment -->
    <script src="../../static/moment/moment.js"></script>
    <script src="../../static/moment/moment.timezone.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />
    <script src="../../static/js/debounce.js"></script>
    <script src="../../static/cmsUtils/commonFunction.js"></script>
    <style>
      .l {
        display: flex;
        justify-content: center;
      }

      .label_title {
        text-align: right;
      }

      .changeBlue {
        color: #1aa5fb;
      }

      .changeWhite {
        color: white;
      }

      #storeNumContent {
        max-height: 300px;
        overflow-y: auto;
        text-align: center;
        padding: 1.5rem 1rem;
      }

      /* checkStoreNumberModal 优化样式 */
      #checkStoreNumberModal .modal-header {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        padding: 1.25rem 1.5rem;
      }

      #checkStoreNumberModal .modal-title {
        width: 100%;
        text-align: center;
        font-weight: 500;
        font-size: 1.1rem;
        color: #495057;
        margin: 0;
      }

      #checkStoreNumberModal .modal-body {
        padding: 2rem 1.5rem;
      }

      #checkStoreNumberModal .form-check {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.75rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: background-color 0.2s ease;
        cursor: pointer;
        user-select: none;
      }

      #checkStoreNumberModal .form-check:hover {
        background-color: #f8f9fa;
      }

      #checkStoreNumberModal .form-check-input {
        margin-right: 0.75rem;
        margin-top: 0;
      }

      #checkStoreNumberModal .form-check-label {
        font-size: 0.95rem;
        color: #495057;
        cursor: pointer;
        margin-bottom: 0;
      }

      #checkStoreNumberModal .modal-footer {
        border-top: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
        justify-content: center;
        gap: 0.75rem;
      }

      #checkStoreNumberModal .btn {
        min-width: 100px;
        font-size: 0.9rem;
        padding: 0.5rem 1.25rem;
        border-radius: 5px;
      }

      #checkStoreNumberModal .badge {
        font-size: 0.7rem;
        padding: 0.35em 0.65em;
      }
      #nav {
        overflow-y: auto;
      }

      /* FoodCourt 模式选择弹窗样式 */
      #foodCourtConfirmModal .card {
        transition: all 0.3s ease;
        border-width: 2px;
      }

      #foodCourtConfirmModal .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      #foodCourtConfirmModal .card.border-success {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
      }

      #foodCourtConfirmModal .card.bg-light {
        background-color: #f8f9fa !important;
      }

      #foodCourtConfirmModal .card-title {
        font-weight: 600;
      }

      #foodCourtConfirmModal .list-unstyled li {
        padding: 2px 0;
      }

      /* .modal-mask {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        background-color: #000;
        opacity: 0.5;
      } */
      /* .modal {
        background-color: #000;
        opacity: 0.5;
      } */
    </style>
  </head>

  <body class="">
    <div id="container" class="display-nav">
      <!-- 选择storeNumber弹窗 -->
      <div
        id="checkStoreNumberModal"
        tabindex="1"
        aria-hidden="true"
        class="modal fade"
        aria-labelledby="storeNumberModalLabel"
      >
        <div class="modal-mask"></div>
        <div class="modal-dialog modal-lg modal-dialog-centered resetModalStyle">
          <div class="modal-content">
            <div class="modal-header">
              <div class="d-flex align-items-center justify-content-center w-100 position-relative">
                <div class="d-flex align-items-center">
                  <h5 class="modal-title mb-0" id="storeNumberModalLabel">Please select a shop</h5>
                  <span
                    id="currentModeIndicator"
                    class="badge bg-secondary ms-3"
                    style="font-size: 0.75rem"
                  ></span>
                </div>
                <button
                  type="button"
                  class="btn-close position-absolute"
                  style="right: 0"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
            </div>
            <div class="modal-body" id="storeNumContent"></div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="onCheckStore()">
                Save changes
              </button>
            </div>
          </div>
        </div>
      </div>
      <!--    选择DBF文件编码弹窗-->
      <div
        id="checkDBFEncodeModal"
        tabindex="-1"
        aria-hidden="true"
        class="modal fade"
        aria-labelledby="DBFEncodeLabel"
      >
        <div class="modal-mask"></div>
        <div class="modal-dialog modal-xl modal-dialog-centered resetModalStyle">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="DBFEncodeLabel">Please select a encoding type</h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body" id="DBFEncodeContent">
              <div class="form-check">
                <input id="encode-gbk" name="encode" type="radio" value="GBK" />
                <label for="encode-gbk">GBK</label>
              </div>
              <div class="form-check">
                <input id="encode-x-windows-950" name="encode" type="radio" value="x-windows-950" />
                <label for="encode-x-windows-950">x-windows-950</label>
              </div>
              <div class="form-check">
                <input id="optional-encode" class="optional-encode" name="encode" type="radio" />
                <label for="optional-encode">
                  <input
                    style="outline: none; border: none; border-bottom: 1px solid #000; width: 200px"
                    type="text"
                    onfocus="focusOptionalEncode()"
                    placeholder="Please enter code type"
                  />
                </label>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-primary" onclick="onCheckEncode()">
                Confirm
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- 选择时区 -->
      <div
        class="modal fade"
        id="selectTimeZoneModal"
        role="dialog"
        aria-labelledby=""
        aria-hidden="true"
        tabindex="-1"
      >
        <div class="modal-dialog modal-xl modal-dialog-centered resetModalStyle">
          <div class="modal-content TimeZoneListContent">
            <div class="modal-header">
              <h5 class="modal-title">Please select time zone</h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <div class="card border-light modal-body-warp">
                <div class="input-group mb-3">
                  <label for="search-time-zone"></label>
                  <input
                    type="text"
                    class="form-control"
                    id="search-time-zone"
                    placeholder="Enter search content"
                    basic-addon2
                    aria-describedby="search-time-zone"
                  />
                  <span class="input-group-text">
                    <i class="fa fa-search"></i>
                  </span>
                </div>
                <div class="list-group list-group-flush" id="time-zone-list"></div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="confirmTimeZone()">
                Save changes
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- foodCourt模式确认弹窗 -->
      <div
        id="foodCourtConfirmModal"
        tabindex="-1"
        aria-hidden="true"
        class="modal fade"
        aria-labelledby="foodCourtConfirmLabel"
      >
        <div class="modal-mask"></div>
        <div class="modal-dialog modal-lg modal-dialog-centered resetModalStyle">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="foodCourtConfirmLabel">
                <i class="fa fa-cogs me-2"></i>
                Select Operation Mode
              </h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <div class="text-center mb-4">
                <p class="fs-6 text-muted mb-3">
                  Please select the table switching mode you wish to use:
                </p>
              </div>

              <!-- 模式选择卡片 -->
              <div class="row g-3">
                <!-- 普通模式 -->
                <div class="col-md-6">
                  <div
                    class="card h-100 border-secondary"
                    style="cursor: pointer"
                    onclick="selectModeCard('normal')"
                  >
                    <div class="card-body text-center">
                      <div class="mb-3">
                        <i class="fa fa-exchange fa-2x text-secondary"></i>
                      </div>
                      <h6 class="card-title text-secondary">Normal Table Switch Mode</h6>
                    </div>
                  </div>
                </div>

                <!-- FoodCourt模式 -->
                <div class="col-md-6">
                  <div
                    class="card h-100 border-secondary"
                    style="cursor: pointer"
                    onclick="selectModeCard('foodcourt')"
                  >
                    <div class="card-body text-center">
                      <div class="mb-3">
                        <i class="fa fa-cutlery fa-2x text-secondary"></i>
                      </div>
                      <h6 class="card-title text-secondary">FoodCourt Mode</h6>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="canvas">
        <!-- 侧边栏 -->
        <div id="nav">
          <!-- static\nav\js\custom.js -->
          <a id="title" href="index.html" style="display: flex; justify-content: center">
            <img id="logoImg" class="brand-image" style="opacity: 0.8" />
          </a>
          <i class="bi bi-alphabet-uppercase"></i>

          <ul id="toggle">
            <li>
              <p class="store_name_warp">
                <img src="../../static/img/cms/shopIcon.jpg" alt="" />
                <span id="nav_storeNumber"></span>
              </p>
            </li>
            <li>
              <div class="border active" id="homePage">
                <span class="menu-icons fa fa-home"></span>
                <a href="#">HOME</a>
              </div>
            </li>
            <li>
              <div id="MtyListPage">
                <span class="menu-icons fa fa-home"></span>
                <a href="#">Mtype List</a>
              </div>
            </li>
            <li>
              <div id="bcgPage">
                <span class="menu-icons fa fa-file-image-o"></span>
                <a href="#">Image Menu</a>
                <!-- ./views/advertisControl.html -->
              </div>
            </li>
            <li>
              <div id="catePage">
                <span class="menu-icons fa fa-cog"></span>
                <a href="#">Category Menu</a>
              </div>
            </li>
            <li>
              <div id="tablePage">
                <span class="menu-icons fa fa-table"></span>
                <a href="#">Table Menu</a>
              </div>
            </li>
            <li>
              <div id="ConfigPage">
                <span class="menu-icons fa fa-cogs"></span>
                <a href="#">Config Menu</a>
              </div>
            </li>
            <li>
              <div id="Localization">
                <span class="menu-icons fa fa-language"></span>
                <a href="#">Localization</a>
              </div>
            </li>
            <li>
              <div id="MergingRulesPage">
                <span class="menu-icons fa fa-window-restore"></span>
                <a href="#">Merging Rules</a>
              </div>
            </li>
            <li>
              <div id="VersionMenuPage">
                <span class="menu-icons fa fa-history"></span>
                <a href="#">Version Menu</a>
              </div>
            </li>
            <li>
              <div id="CrmMenuPage">
                <span class="menu-icons fa fa-history"></span>
                <a href="#">CRM Menu</a>
              </div>
            </li>
            <li>
              <div id="RechargeMenuPage">
                <img src="../../static/img/cms/rechargeIcon.jpg" class="cmsCustomMenu-icon" />
                <a href="#">Recharge</a>
              </div>
            </li>
            <li>
              <div id="PaymentMenu">
                <span class="menu-icons fa fa-credit-card"></span>
                <a href="#">Payment</a>
                <span class="the-btn fa fa-plus"></span>
              </div>
              <ul id="childToggle">
                <li>
                  <a href="#" id="PaymentMethods" parentClass="PaymentMenu">Payment Methods</a>
                </li>
                <li>
                  <a href="#" id="PaymentHistory" parentClass="PaymentMenu">Payment History</a>
                </li>
              </ul>
            </li>

            <li>
              <div id="ShopMenu">
                <span class="menu-icons fa fa-archive"></span>
                <a href="#">Shop</a>
                <span class="the-btn fa fa-plus"></span>
              </div>
              <ul>
                <li>
                  <a href="#" id="ShopMaster">Shop Master</a>
                </li>
                <li>
                  <a href="#" id="StopBusinessHours">Business Hours</a>
                </li>
                <li>
                  <a href="#" id="ZoneConfig">Zone Config</a>
                </li>
                <li>
                  <a href="#" id="HQModule">HQ Menu Master</a>
                </li>
                <li>
                  <a href="#" id="StoreType">Store Type</a>
                </li>
                <li>
                  <a href="#" id="TacLog">TAC Log</a>
                </li>
              </ul>
            </li>
            <li>
              <div id="PromoMenu">
                <span class="menu-icons fa fa-table"></span>
                <a href="#">Promo Menu</a>
                <span class="the-btn fa fa-plus"></span>
              </div>
              <ul>
                <li>
                  <a href="#" id="PromotionPage" parentClass="PromoMenu">Promotion</a>
                </li>
                <li>
                  <a href="#" id="PromoDiscountMenu" parentClass="PromoMenu">Promo Discount</a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <!-- 右侧主内容 -->
        <div class="content_warp">
          <!-- 头部菜单 -->
          <div class="top_warp">
            <div class="navLeft">
              <a href="#" class="toggle-nav" id="bars"><i class="fa fa-bars"></i></a>
            </div>
            <div class="navRight">
              <!--时区选择-->
              <div class="time-zone navMenuWarp">
                <div
                  class="dropdownLabel"
                  data-bs-toggle="modal"
                  data-bs-target="#selectTimeZoneModal"
                >
                  <i class="fa fa-globe"></i>
                  Time Zone
                </div>
              </div>
              <!-- 搜索list数据 -->
              <div class="search navMenuWarp">
                <div class="dropdownLabel" onclick="showSearchListModal()">
                  <i class="fa fa-search"></i>
                  Search
                </div>
              </div>
              <!-- 历史版本查看 -->
              <div class="historyVersionWarp navMenuWarp">
                <div
                  id="historyVersionLable"
                  class="dropdown-toggle dropdownLabel"
                  aria-expanded=" false"
                  data-bs-toggle="dropdown"
                  onclick="showHistoryVSMenu()"
                >
                  <span>Version</span>
                </div>
                <ul
                  class="dropdown-menu historyVersionMenu"
                  aria-labelledby="historyVersionLable"
                ></ul>
              </div>
              <!-- 拖拽开关 -->
              <div class="dragBox">
                <div
                  id="dropdownDrag"
                  aria-expanded="false"
                  data-bs-toggle="dropdown"
                  onclick="disableDragMenu()"
                  class="dropdown-toggle dropdownLabel"
                >
                  Drag
                </div>
                <ul class="dropdown-menu" aria-labelledby="dropdownDrag" id="dropdownDrag-menu">
                  <li>
                    <a class="dropdown-item disabled" href="#" onclick="resetDragOrder()">
                      Reset Drag
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item disabled" href="#" onclick="subDrag()">Submit Drag</a>
                  </li>
                  <!-- <li><a class="dropdown-item" href="#">Menu item</a></li> -->
                </ul>
                <jelly-switch id="js1" onToggle=" return handleToggle(this)">
                  <!-- <p slot="content-left">Drag</p> -->
                </jelly-switch>
                <!-- <i class="fa fa-arrows-alt iconColor"></i> -->
              </div>
              <div class="download-option-DBF navMenuWarp">
                <div
                  id="downloadDBF"
                  aria-expanded="false"
                  data-bs-toggle="dropdown"
                  class="dropdown-toggle dropdownLabel"
                >
                  <span style="margin-left: 5px">Download</span>
                </div>
                <ul class="dropdown-menu" aria-labelledby="downloadDBF">
                  <!-- 下载无用Mlist Txt -->
                  <li>
                    <a class="dropdown-item" onclick="getUnuseMlist()">Data Inspection</a>
                  </li>
                  <li><a class="dropdown-item" href="#" type="dbf">FOOD</a></li>
                  <li><a class="dropdown-item" href="#" type="dbf">FOODTYPE</a></li>
                  <li><a class="dropdown-item" href="#" type="dbf">MLIST</a></li>
                  <li><a class="dropdown-item" href="#" type="dbf">MTYPE</a></li>
                  <li onclick="downloadAllDBF()"><a class="dropdown-item">DOWNLOAD ALL</a></li>
                </ul>
              </div>

              <!-- 刷新缓存 -->
              <div class="updateCacheBox navMenuWarp">
                <div
                  class="iconHover"
                  id="updateCacheContent"
                  aria-expanded="false"
                  data-bs-toggle="dropdown"
                >
                  <i class="fa fa-refresh" id="refreshIcon"></i>
                  <span style="margin-left: 5px" class="dropdownLabel">Update Cache</span>
                  <!-- 提示更新缓存图标 -->
                  <i
                    class="fa fa-exclamation-circle updateCache-exclamation-icon"
                    aria-hidden="true"
                  ></i>
                </div>
                <!-- 更新缓存下拉菜单 -->
                <ul class="dropdown-menu" aria-labelledby="updateCacheContent">
                  <li>
                    <a class="dropdown-item" href="#" onclick="onUpdateAllCache()">
                      Update All Cache
                    </a>
                  </li>
                  <li class="refreshDataLi">
                    <a class="dropdown-item disabled" href="#" onclick="onRefreshData()">
                      Refresh Data
                    </a>
                    <!-- 未读状态右上角小点 -->
                    <span class="ivu-badge-dot"></span>
                  </li>
                  <li class="refreshVersionInformation">
                    <a class="dropdown-item" href="#" onclick="onRefreshVersion()">
                      Version Update
                    </a>
                    <!-- 未读状态右上角小点 -->
                    <span class="ivu-badge-dot"></span>
                  </li>
                  <!--                   <li> -->
                  <!--                     <a class="dropdown-item" href="#" onclick="onRefreshDatabase()"> -->
                  <!--                       Refresh Database -->
                  <!--                     </a> -->
                  <!--                   </li> -->
                </ul>
              </div>

              <!-- 账户名 -->
              <div class="dropdown accountName">
                <div
                  class="userInfo iconHover"
                  role="button"
                  id="dropdownMenuLink"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <i class="fa fa-user" aria-hidden="true"></i>
                  <span id="userName" class="nav_text"></span>
                </div>
                <ul class="dropdown-menu navRight_menu" aria-labelledby="dropdownMenuLink">
                  <li class="menu_item_warp">
                    <div class="dropdown-item" onclick="$('#uploadLogo').click()">
                      <i class="fa fa-upload" aria-hidden="true"></i>
                      <span>Upload LOGO</span>
                    </div>
                    <input
                      id="uploadLogo"
                      type="file"
                      name="file"
                      onchange="uploadLogo('logo',this,'logo')"
                      accept="image/*"
                      value="Upload LOGO"
                      style="display: none"
                    />
                  </li>
                  <li class="select_storeNumber_li">
                    <div
                      class="dropdown-item selectStoreNumberBox"
                      data-bs-toggle="modal"
                      data-bs-target="#checkStoreNumberModal"
                    >
                      <img src="../../static/img/cms/storeIcon.svg" alt="" />
                      <span id="nowStoreNumber"></span>
                    </div>
                  </li>
                </ul>
              </div>

              <!-- 退出登录 -->
              <a href="../../logout" class="logoOut" onclick="LogOut()">
                <div class="logout iconHover">
                  <i class="fa fa-sign-out" aria-hidden="true"></i>
                  <!-- <img src="../../static/img/cms/logoOut.svg" alt=""> -->
                  <span class="nav_text">logout</span>
                </div>
              </a>
            </div>
          </div>
          <!-- 菜单内容 -->
          <iframe src="" id="iframe" name="iframe" class="content_warp_iframeBox"></iframe>
        </div>
      </div>
    </div>
    <script type="text/javascript">
      $(function () {
        //自定义参数
        toastr.options = {
          closeButton: false, //是否显示关闭按钮（提示框右上角关闭按钮）。
          debug: false, //是否为调试。
          progressBar: true, //是否显示进度条（设置关闭的超时时间进度条）
          positionClass: "toast-top-center", //消息框在页面显示的位置
          onclick: null, //点击消息框自定义事件
          showDuration: "300", //显示动作时间
          hideDuration: "1000", //隐藏动作时间
          timeOut: "2000", //自动关闭超时时间
          extendedTimeOut: "1000",
          showEasing: "swing",
          hideEasing: "linear",
          showMethod: "fadeIn", //显示的方式，和jquery相同
          hideMethod: "fadeOut" //隐藏的方式，和jquery相同
          //等其他参数o
        }
      })
      let encodingType = ""
      let targetName = []
      //下载所有dbf文件
      const downloadAllDBF = () => {
        $.each($(".download-option-DBF li a[type]"), (index, el) => {
          el.click()
        })
      }
      //显示自定义编码的弹窗
      const showCheckoutEncodeModel = () => {
        $("#checkDBFEncodeModal").modal("show")
        $.each($("input[name='encode']"), (index, el) => {
          $(el).prop("checked", false)
        })
        encodingType = ""
        $("label[for='optional-encode'] input").val(encodingType)
      }
      $("#checkDBFEncodeModal").on("hidden.bs.modal", function (e) {
        targetName = []
      })
      //给每个dbf文件类型新增点击事件, 显示弹窗
      $.each($(".download-option-DBF li a[type]"), (index, el) => {
        el.addEventListener("click", e => {
          showCheckoutEncodeModel()
          targetName.push(e.target.textContent)
        })
      })
      const downloadDBF = () => {
        let newArr = [...targetName]
        let { domain, storeNumber, versionNumber } = getRequestHeaderParams()
        newArr.forEach((el, index) => {
          const xhr = new XMLHttpRequest()
          xhr.open("get", "../../manager_DBF/" + el + "?encodingType=" + encodingType, true)
          // 设置的header头必须要放到open()后面
          xhr.setRequestHeader("domain", domain)
          xhr.setRequestHeader("storeNumber", storeNumber)
          xhr.setRequestHeader("versionNumber", versionNumber)
          xhr.responseType = "blob"
          xhr.send()
          xhr.onload = function () {
            //状态码为200: 先转换blob为string,转换失败则为正确的blob流(可下载),转换成功则是后端返回错误状态码:400encode错误,抛出弹窗错误
            let value = xhr.response
            if (this.status === 200) {
              value
                .text()
                .then(r => {
                  try {
                    let v = JSON.parse(r)
                    if (v.statusCode) {
                      console.log("编码错误,请重新选择编码后下载")
                      toastr.error("Encoding error, please re-select and download")
                    }
                  } catch {
                    const tmpLink = document.createElement("a")
                    const objectUrl = URL.createObjectURL(value)
                    tmpLink.href = objectUrl
                    tmpLink.download = el + ".DBF"
                    tmpLink.click()
                    URL.revokeObjectURL(objectUrl)
                  }
                })
                .catch(err => {
                  const tmpLink = document.createElement("a")
                  const objectUrl = URL.createObjectURL(value)
                  tmpLink.href = objectUrl
                  tmpLink.download = el + ".DBF"
                  tmpLink.click()
                  URL.revokeObjectURL(objectUrl)
                })
            } else {
              console.log("编码错误,请重新选择编码后下载")
              toastr.error("Encoding error, please re-select and download")
            }
            targetName.splice(index, 1)
          }
        })
      }
      //选择encode回调
      const onCheckEncode = () => {
        let jqueryDOM = $("#DBFEncodeContent input[name='encode']:checked")
        if (jqueryDOM.length) {
          let value = jqueryDOM.val()
          if ($(jqueryDOM[0]).hasClass("optional-encode")) {
            value = $("label[for='optional-encode'] input").val()
          }
          encodingType = value
          downloadDBF()
          $("#checkDBFEncodeModal").modal("hide")
        } else {
          console.log("未选择encode")
          toastr.warning("Please select an encoding")
        }
      }
      const focusOptionalEncode = () => {
        $("#optional-encode").prop("checked", true)
      }
      const showHistoryVSMenu = () => {
        // 获取.historyVersionMenu是否有内容,有则展示下拉菜单,没有则提示没有历史版本
        if ($(".historyVersionMenu").children().length) {
          // 获取缓存的versionNumber字段,有值则对应的版本号添加选中样式
          let versionNumber = sessionStorage.getItem("versionNumber")
          // 遍历$(".historyVersionMenu li")元素,如果有versionNumber字段,则塞入svg
          $.each($(".historyVersionMenu li"), (index, el) => {
            //  判断 $(el).find('a')下是否有svg,有则删除
            if ($(el).find("svg").length) {
              $(el).find("svg").remove()
            }
            // 获取el下versionNumber属性值
            if ($(el).attr("versionNumber") == versionNumber) {
              // $(el).find(a)下的a标签后面添加svg
              $(el).find("a")
                .append(`<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-check-lg" color="#0d6efd"
            viewBox="0 0 18 18">
            <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
           </svg>`)
            }
          })
        } else {
          toastr.warning("No history version")
        }
      }
      const selectVersion = version => {
        sessionStorage.setItem("versionNumber", version)
        // 获取toggle选中的栏目,重新加载页面
        let activeName = $("#toggle li div.active").attr("id")
        // 不是MtyListPage页都跳转到HOME页面
        // todo 仅处理hq页面,且版本标识(HIST/UAT未作兼容处理(移除)) ----  activeName无法判断到子菜单,此处使用iframeSrc判断
        let iframeSrc = $("iframe").attr("src")
        if (activeName == "MtyListPage") {
          $("#iframe").attr("src", `./MtyListPage.html`)
        } else if (iframeSrc.includes("HQModule")) {
          $("#iframe").attr("src", `./HQModule.html`)
          $(".versionTag").remove() // 移除版本标识(home/mty)
        } else {
          // 选中状态为HOME
          $("#toggle li div").removeClass("active")
          $("#homePage").addClass("active")
          $("#iframe").attr("src", `./menu.html`)
        }
        // // 标出当前版本
        // showVersionTag()
      }
      //获取历史版本记录
      function getHistoryVersion(domain, formPage) {
        // post请求
        $.post({
          url: "../../manager_historyVersion/getAll",
          dataType: "json",
          success: res => {
            if (res.data) {
              const convertSourceData = data => {
                if (data == 0) {
                  return "UAT"
                } else {
                  // 使用new Date()将时间戳转化为YYYY-MM-DD HH:mm:ss格式
                  return moment(data).format("YYYY-MM-DD HH:mm:ss")
                }
              }
              let html = `<li versionNumber="PROD"><a href="#" class="dropdown-item historyVersion-li-a"  onclick="selectVersion('PROD')"><span>V : PROD</span></a></li>`
              res.data.forEach((el, index) => {
                html += `
            <li versionNumber="${el.versionBackupTime}">
              <a href="#" class="dropdown-item historyVersion-li-a" onclick="selectVersion('${
                el.versionBackupTime
              }')">
               <span>V : ${convertSourceData(el.versionBackupTime)}</span>
              </a>
            </li>`
              })
              $(".historyVersionMenu").html(html)
            }
          },
          error: err => {
            console.log(err)
          }
        })
        // let tableData = [
        //   {
        //     versionBackupTime: 0,
        //     timeOfAppointment: 1675308477020
        //   },
        //   {
        //     versionBackupTime: 1675308477020,
        //     timeOfAppointment: 1675308477020
        //   },
        //   {
        //     versionBackupTime: 1675308477030,
        //     timeOfAppointment: 1675308477020
        //   },
        //   {
        //     versionBackupTime: 1675308477040,
        //     timeOfAppointment: 1675308477020
        //   }
        // ]
      }
      // 标出当前版本
      function showVersionTag() {
        // 清除所有class为versionTag的span标签
        $(".versionTag").remove()
        let versionNumber = sessionStorage.getItem("versionNumber")
        let tagStyle = {
          PROD: "",
          0: "el-tag--success"
        }
        let tagLabel = {
          PROD: "PROD",
          0: "UAT"
        }
        let style =
          versionNumber == "PROD" || versionNumber == 0
            ? tagStyle[versionNumber]
            : "el-tag--warning"
        let txt = versionNumber == "PROD" || versionNumber == 0 ? tagLabel[versionNumber] : "HIST"
        let activeName = $("#toggle li div.active").attr("id")
        // 不是MtyListPage页都跳转到HOME页面
        if (activeName == "MtyListPage") {
          // 添加span标签到#homePage后面
          $("#MtyListPage").append(`<span class="versionTag el-tag ${style}" >${txt}</span>`)
        } else {
          // 添加span标签到#homePage后面
          $("#homePage").append(`<span class="versionTag el-tag ${style}">${txt}</span>`)
        }
      }
      $("#toggle").hide()
      //dom加载完成后自定义title
      window.addEventListener("DOMContentLoaded", () => {
        //初始加载index页面判断是否存在缓存versionNumber,如果没有则设置为PROD
        if (!sessionStorage.getItem("versionNumber")) {
          sessionStorage.setItem("versionNumber", "PROD")
        }
        $.getJSON("../../static/utils/config.json", function (data) {
          document.title = data.CMS.DocumentTitle
        })
        $.post({
          url: "../../manager_user/getUserInfo",
          dataType: "json", //返回数据类型,xml,html,script,json字符串,jsonp,text文本
          success: function (res) {
            let {
              userInfo = [],
              imageMinWidth = 360,
              imageMaxWidth = 440,
              imageMinHigh = 240,
              imageMaxHigh = 292
            } = res
            let iframe = document.querySelector("iframe")
            iframe.setAttribute(
              "data-image-size",
              JSON.stringify({ imageMinWidth, imageMaxWidth, imageMinHigh, imageMaxHigh })
            )
            iframe.src = "./menu.html"
            $("#userName").text(userInfo[0])
            $("#form_userName").val(userInfo[0])
            domain = userInfo[1]
            checkPermissions(userInfo[2]) //校验权限
            // 儲存domain
            if (domain) sessionStorage.setItem("domain", domain)
            getHistoryVersion(domain)
            console.log(domain, "domaindomaindomaindomain")
            logoImgSrc =
              "https://appwise.oss-cn-hongkong.aliyuncs.com/" +
              domain +
              "/image/logo/logo.jpg?x-oss-process=image/resize,h_50"
            $("#logoImg").attr("src", logoImgSrc)
            $("#logoImg").attr("onerror", "uploadLogoImg()")
            $("#logoImg").removeAttr("hidden")
          }
        })
        initTimeZoneList()
      })
      /**
       * 根据getUserInfo接口的数组第三个参数判断是否有权限
       * @param {string} authority 若为!!false则有全权限，其他则只显示对应菜单
       * */
      function checkPermissions(authority = "") {
        if (authority) {
          // 验证权限
          let authArray = authority.split(",")
          let $toggleEl = $("#toggle")
          let firstLevel = []

          // 找到所有需要显示的第一级
          $toggleEl.find("> li > div > a").each(function () {
            let $aEl = $(this)
            let label = $aEl.text()
            if (authArray.includes(label)) {
              let $liEl = $aEl.closest("li")
              $liEl.attr("auth", true)
              firstLevel.push($liEl)
            }
          })
          firstLevel.forEach($liEl => {
            // 找到所有需要显示的第二级
            $liEl.find("> ul > li  > a").each(function () {
              let $aEl = $(this)
              let label = $aEl.text()
              if (authArray.includes(label)) {
                let $liEl = $aEl.closest("li")
                $liEl.attr("auth", true)
              }
            })
          })
          // 找到存在1级权限但没有2级别的1级,将其所有2级加上auth属性
          firstLevel.forEach($liEl => {
            if ($liEl.find("> ul > li[auth]").length === 0) {
              $liEl.find("> ul > li").attr("auth", true)
            }
          })

          //将所有没有auth属性的元素移除
          $toggleEl.find("li:not([auth])").remove()
          // 默认指向第一个有auth属性的元素
          let $firstEl = $toggleEl.find("li[auth]").eq(0)
          let $firstChildEl = $firstEl.find("> ul > li[auth]").eq(0)
          $toggleEl.show()
          // 若存在子级则指向第一个子级,触发a标签click
          if ($firstChildEl.length) {
            $firstEl.find("> div[id]").trigger("click") //展开子菜单
            $firstChildEl.find("> a[id]").trigger("click") //点击子菜单
          } else {
            // 不存在则直接找到li下的div触发click
            $firstEl.find("> div[id]").trigger("click")
          }
          // 若为空数组则没有权限,移除右侧iframe
          if (firstLevel.length === 0) {
            $toggleEl.hide()
            $("#iframe").remove()
            $toggleEl.after(
              '<div style="text-align: center;font-size: 20px;color: #999;margin-top: 100px;">No Permission</div>'
            )
          }
        } else {
          // 无需验证
          $("#toggle").show()
        }
      }
      var domain
      var logoImgSrc
      var imageSuffix = ["jpg", "png", "webp", "bmp", "gif", "tiff"]

      function jumpIframe(i, id) {
        document.getElementById("iframe").contentWindow.jumpAnchor(i, id)
      }
      //把storeNumber塞入dom中
      function setStoreNumberInDom(value) {
        $("#nowStoreNumber").text(value)
        $("#nav_storeNumber").text(value)
      }
      // 查询店铺名
      function setStore(allergenArray) {
        if (allergenArray.length != 0) {
          const getStoreData = storeItem => {
            let {
              storeNumber,
              storeName: { en: firstLanName, zh, thirdLan }
            } = storeItem
            let displayedName = firstLanName || storeNumber // 如果没有英文名，则显示店铺号
            return { storeNumber, displayedName, zh, thirdLan }
          }
          // 获取缓存的店铺名
          let sessionStoreNumber = sessionStorage.getItem("storeNumber")
          if (!sessionStoreNumber) {
            // 如果没有缓存，则获取第一个店铺名
            let { storeNumber, displayedName } = getStoreData(allergenArray[0]) // 获取第一个店铺名
            sessionStorage.setItem("storeNumber", storeNumber)
            setStoreNumberInDom(displayedName)
          } else {
            // 如果有缓存，则获取缓存的店铺名,判断是否在店铺名列表中
            let sessionStoreNumItem = allergenArray.find(item => {
              return item.storeNumber == sessionStoreNumber
            })
            if (sessionStoreNumItem) {
              let { storeNumber, displayedName } = getStoreData(sessionStoreNumItem) // 获取第一个店铺名
              setStoreNumberInDom(displayedName)
            } else {
              // 如果不在店铺名列表中，则获取第一个店铺名
              let { storeNumber, displayedName } = getStoreData(allergenArray[0]) // 获取第一个店铺名
              sessionStorage.setItem("storeNumber", storeNumber)
              setStoreNumberInDom(displayedName)
            }
          }
          // id为storeNumContent下遍历生成单选框
          let storeNumContent = $("#storeNumContent")
          var storeMeuHtml = ""
          allergenArray.forEach((item, index) => {
            let { storeNumber, displayedName } = getStoreData(item)
            storeMeuHtml +=
              '<div class="form-check">' +
              ` <input class="form-check-input" type="radio" name="storeRadio"
                value="${storeNumber}" data-name="${displayedName}" id="store${index}">` +
              `<label class="form-check-label" for="store${index}">${displayedName} </label>` +
              `</div>`
          })
          storeNumContent.html(storeMeuHtml)
        } else {
          setStoreNumberInDom("No Data")
        }
      }

      $(function () {
        var iframeid = document.getElementById("iframe") //iframe id
        // if (document.getElementById) {
        //   iframeid.height =
        //     document.documentElement.clientHeight -
        //     document.getElementById('header').scrollHeight -
        //     7;
        // }
      })

      function uploadLogoImg() {
        $("#logoImg").removeAttr("onerror")
        $("#logoImg").attr("src", "../../static/img/logo/aipha-logo.jpg")
      }

      /*
       id:文件名
       label:this,带FIle
       typeName:文件夹名
        */
      function uploadLogo(id, label, typeName) {
        var file = $(label)[0].files[0]
        // 判断文件大小合法性
        if (!file) return
        // 上传图片
        var fd = new FormData()
        fd.append("id", id)
        fd.append("file", file)
        fd.append("updateTime", "")
        fd.append("pathList", "")
        fd.append("typeName", typeName)
        fd.append("domain", domain)
        let version = sessionStorage.getItem("versionNumber")
        if (version !== "PROD") {
          fd.append("version", version)
        }
        $.post({
          url: "../../manager_photo/upload",
          dataType: "json",
          processData: false,
          contentType: false,
          cache: false,
          data: fd,
          success: function (result) {
            if (result.errFlag1) {
              toastr.error(result.errMessage1)
            } else {
              $("#logoImg").attr("onerror", "uploadLogoImg()")
              $("#logoImg").attr("src", logoImgSrc + "," + new Date())
            }
          },
          error: function (data) {
            toastr.error("Upload picture abnormal")
          }
        })
      }
      // 更新所有缓存
      function onUpdateAllCache() {
        toastr.options = { positionClass: "toast-top-center" }
        toastr.info("Updating cache ..  please wait")
        $.get({
          url: "../../manager_updateCache/updateAll",
          dataType: "json",
          success: function (res) {
            if (res.statusCode == 200) {
              toastr.success("Cache update succeeded")
            } else {
              toastr.error("Cache update failed")
            }
          },
          error: function () {
            toastr.error("Cache update failed")
          }
        })
      }
      // 更新Home数据缓存
      async function onRefreshData() {
        toastr.options = { positionClass: "toast-top-center" }
        toastr.info("Refresh Data ..  please wait")
        let initData = window.parent.document.getElementById("iframe").contentWindow.initData
        let pathList = window.parent.document.getElementById("iframe").contentWindow.pathList
        if (!initData) return false
        await initData(pathList.length)
        await toastr.success("Data Refresh succeeded")
      }
      // 更新OIS、D2S等程序版本信息
      function onRefreshVersion() {
        toastr.options = { positionClass: "toast-top-center" }

        $.get({
          url: "../../manager_versionNum/refreshVersionInformation",
          dataType: "json",
          success: function (res) {
            console.log(res, "res")
            if (res.statusCode == 200) {
              toastr.success("Version Update succeeded")
            } else {
              toastr.error(res.data.join("<br>"), null, {
                enableHtml: true // 允许HTML
              })
            }
          },
          error: function () {
            toastr.error("Version Update failed")
          }
        })
      }
      // 开启提示刷新Home数据样式
      function onOpenRefreshData() {
        // .updateCache-exclamation-icon 显示
        $(".updateCache-exclamation-icon").show()
        $(".refreshDataLi a").removeClass("disabled") // li标签可用
        //.ivu-badge-dot 显示
        $(".refreshDataLi").find(".ivu-badge-dot").show()
      }
      $(".refreshDataLi a").addClass("disabled") // li标签禁用
      //.ivu-badge-dot 隐藏
      // 关闭提示刷新Home数据样式
      function onCloseRefreshData() {
        // .updateCache-exclamation-icon 隐藏
        $(".updateCache-exclamation-icon").hide()
        $(".refreshDataLi").find(".ivu-badge-dot").hide()
      }

      function LogOut() {
        sessionStorage.clear() //重置选中
        // sessionStorage.removeItem("choseMenuId") //重置选中
      }
      // 监听点击菜单点击点击控制拖拽开关是否禁止
      $("#toggle li").click(function () {
        let activeName = $("#toggle li div.active").attr("id")

        // 获取active的name属性
        let passSwitchListMenu = ["homePage", "MtyListPage"]
        if (passSwitchListMenu.includes(activeName)) {
          // passDrag() // 关闭开关禁用状态
          resetDragSwitch("off") // 重置开关启用样式
        } else {
          disabledDrag("allDisabled")
        }
      })
      // 点击拖拽开关事件
      function handleToggle(e) {
        // console.log('The present value of switch is ' + e.ariaChecked);
        // 获取iframe子页面的变量
        if (e.ariaChecked == "true") {
          resetDragSwitch("on")
        } else {
          resetDragSwitch("off")
        }
      }

      // 重置拖拽顺序按钮
      function resetDragOrder() {
        let onResetDrag = window.parent.document.getElementById("iframe").contentWindow.onResetDrag
        onResetDrag()
      }
      // 提交拖拽按钮
      function subDrag() {
        let onSubDrag = window.parent.document.getElementById("iframe").contentWindow.onSubDrag
        onSubDrag()
      }
      // 禁止拖拽开关及下拉按钮
      function disabledDrag(type = "switchDisabled") {
        js1.disabled = true
        if (type == "allDisabled") {
          let switchVal = $("#js1").attr("aria-checked") || "false"
          if (switchVal == "true") {
            $("#js1").attr("aria-checked", false)
            resetDragSwitch("off")
          }
        }
      }

      //允许拖拽
      function passDrag() {
        js1.disabled = false
      }
      // 控制拖拽下拉提交菜单根据拖拽开关状态决定是否禁用
      function disableDragMenu() {
        // jq获取元素属性aria-checked
        let switchVal = $("#js1").attr("aria-checked") || "false" // 开关开启状态(初始状态为undefined)
        let switchDisabled = js1.disabled // 开关禁用状态
        console.log(switchVal, switchDisabled, "switchVal")
        $("#dropdownDrag-menu>li>a").each(function (e, i) {
          if (switchVal == "false" || switchDisabled) {
            $(this).addClass("disabled")
          } else {
            $(this).removeClass("disabled")
          }
        })
      }
      // 重置开关状态
      function resetDragSwitch(type) {
        let sortable = window.parent.document.getElementById("iframe").contentWindow.sortable
        if (!sortable) return
        let dragDiv = $(
          window.parent.document.getElementById("iframe").contentWindow.document
        ).find("#body_warp")
        if (type == "on") {
          $("#js1").attr("aria-checked", true) // 开关关闭
          sortable.options.disabled = false
          dragDiv.css("cursor", "move")
        } else {
          $("#js1").attr("aria-checked", false) // 开关关闭
          sortable.options.disabled = true
          dragDiv.css("cursor", "auto")
        }
      }
      // 弹窗选择店铺窗口
      function onCheckStore() {
        $("#checkStoreNumberModal").modal("hide")

        // 显示foodCourt模式确认对话框
        $("#foodCourtConfirmModal").modal("show")
      }

      // 模式卡片选择函数
      function selectModeCard(mode) {
        // 移除所有卡片的选中状态
        $("#foodCourtConfirmModal .card").removeClass("border-success").removeClass("bg-light")

        // 为选中的卡片添加选中样式
        if (mode === "normal") {
          $("#foodCourtConfirmModal .card").eq(0).addClass("border-success bg-light")
        } else if (mode === "foodcourt") {
          $("#foodCourtConfirmModal .card").eq(1).addClass("border-success bg-light")
        }

        // 存储选择的模式
        $("#foodCourtConfirmModal").data("selectedMode", mode)

        // 自动确认选择
        setTimeout(() => {
          onFoodCourtConfirm(mode === "foodcourt")
        }, 300)
      }

      // FoodCourt mode confirmation handler function
      function onFoodCourtConfirm(isFoodCourt) {
        // 获取name为storeRadio选中的值
        let storeRadio = $("input[name='storeRadio']:checked").val()
        sessionStorage.setItem("storeNumber", storeRadio)

        // 获取name为storeRadio选中的值中属性为data-name的值
        let firstLanName = $("input[name='storeRadio']:checked").attr("data-name")
        setStoreNumberInDom(firstLanName) // 修改店铺名称

        // 显示选择结果的提示信息
        let modeText = isFoodCourt ? "FoodCourt Mode" : "Normal Table Switch Mode"
        toastr.success(`Selected ${modeText}, Current Store: ${firstLanName}`)
        console.log(storeRadio, "选择的店铺代号", firstLanName, "选择的店铺名称", "模式:", modeText)

        // Set sessionStorage identifier for store selection FoodCourt mode
        sessionStorage.setItem("storeSelectionFoodCourtMode", isFoodCourt.toString())
        console.log("Store selection FoodCourt mode set to:", isFoodCourt)

        // Close confirmation dialog
        $("#foodCourtConfirmModal").modal("hide")

        // Continue with original page judgment logic
        let activeName = $("#toggle li div.active").attr("id")
        if (activeName == "homePage" || activeName == "MtyListPage") {
          let reloadPage = window.parent.document.getElementById("iframe")
          reloadPage.contentWindow.location.reload()
        }
      }
      // jq监听打开弹窗
      $("#checkStoreNumberModal").on("show.bs.modal", function () {
        // 回显单选框选中的值
        let storeNumber = sessionStorage.getItem("storeNumber")
        $("input[name='storeRadio'][value='" + storeNumber + "']").prop("checked", true)

        // 更新模式指示器
        updateModeIndicator()
      })

      // 优化店铺选择交互：点击整个 .form-check 容器选中单选按钮
      $(document).on("click", "#checkStoreNumberModal .form-check", function (e) {
        // 如果点击的是 input 或 label，让默认行为处理
        if ($(e.target).is("input") || $(e.target).is("label")) {
          return
        }

        // 阻止事件冒泡
        e.stopPropagation()

        // 找到当前容器内的单选按钮并选中
        const radioInput = $(this).find("input[type='radio']")
        if (radioInput.length > 0) {
          radioInput.prop("checked", true)
          // 触发 change 事件，以防有其他监听器需要响应
          radioInput.trigger("change")
        }
      })

      // 更新模式指示器函数
      function updateModeIndicator() {
        let isFoodCourtMode = sessionStorage.getItem("storeSelectionFoodCourtMode") === "true"
        let modeText = isFoodCourtMode ? "FoodCourt Mode" : "Normal Mode"
        let badgeClass = isFoodCourtMode ? "bg-warning" : "bg-secondary"

        $("#currentModeIndicator")
          .text(modeText)
          .removeClass("bg-secondary bg-warning")
          .addClass(badgeClass)
      }

      // 监听 FoodCourt 模式选择弹窗的显示事件
      $("#foodCourtConfirmModal").on("show.bs.modal", function () {
        // 重置所有卡片的选中状态
        $("#foodCourtConfirmModal .card").removeClass("border-success").removeClass("bg-light")
        // 清除存储的选择模式
        $("#foodCourtConfirmModal").removeData("selectedMode")
      })

      $(function () {
        //自定义参数
        toastr.options = {
          closeButton: false, //是否显示关闭按钮（提示框右上角关闭按钮）。
          debug: false, //是否为调试。
          progressBar: true, //是否显示进度条（设置关闭的超时时间进度条）
          positionClass: "toast-top-center", //消息框在页面显示的位置
          onclick: null, //点击消息框自定义事件
          showDuration: "300", //显示动作时间
          hideDuration: "1000", //隐藏动作时间
          timeOut: "2000", //自动关闭超时时间
          extendedTimeOut: "1000",
          showEasing: "swing",
          hideEasing: "linear",
          showMethod: "fadeIn", //显示的方式，和jquery相同
          hideMethod: "fadeOut" //隐藏的方式，和jquery相同
          //等其他参数o
        }
      })
      // 点击id=searchListBtn调用menu文件的showSearchListModal方法
      function showSearchListModal() {
        let activeName = $("#toggle li div.active").attr("id")
        let passSwitchListMenu = ["homePage", "MtyListPage"]
        if (!passSwitchListMenu.includes(activeName)) {
          // 提示请在食品数据页面启用搜索
          toastr.warning("Please enable search on the food data page")
        } else {
          let showSearchListModal =
            window.parent.document.getElementById("iframe").contentWindow.showSearchListModal
          showSearchListModal()
        }
      }
      const defaultTimeZone = {
        label: "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi",
        value: "+08:00"
      }
      let localTimeZone = sessionStorage.getItem("localTimeZone")
      if (!localTimeZone) {
        sessionStorage.setItem("localTimeZone", JSON.stringify(defaultTimeZone))
      }
      // 渲染时区列表
      function renderTimeZoneList(res) {
        let parent = document.querySelector("#selectTimeZoneModal  .list-group")
        //清空列表
        parent.innerHTML = ""
        let i = 0
        for (i; i < res.length; i++) {
          let item = document.createElement("a")
          item.className = "list-group-item list-group-item-action"
          item.setAttribute("data-bs-toggle", "list")
          item.setAttribute("value", res[i].value)
          item.textContent = res[i].label
          parent.appendChild(item)
        }
      }
      // 初始化时区的选中状态,默认或者sessionStorage
      function initTimeZoneSelect() {
        let localTimeZone = sessionStorage.getItem("localTimeZone")
        let timeZone = localTimeZone ? JSON.parse(localTimeZone) : defaultTimeZone
        let { label, value } = timeZone
        //移除所有active类
        // 找到所有value满足的dom,在筛选满足label的dom,加上active类
        $(`#selectTimeZoneModal  .list-group a`).each(function () {
          $(this).removeClass("active")
          if ($(this).text() == label) {
            $(this).addClass("active")
          }
        })
      }
      // 动态创建列表
      function initTimeZoneList() {
        // 获取json数据
        $.getJSON("../../static/js/cms/timezone.json", function (res) {
          //创建list
          renderTimeZoneList(res)
          // 监听input输入,防抖,调用搜索方法 dom:search-time-zone
          let searchInput = document.querySelector("#search-time-zone")
          initTimeZoneSelect()
          searchInput.addEventListener(
            "input",
            debounce(useSearchTimeZone.bind(this, searchInput, res), 200, false)
          )
        })
        $("#selectTimeZoneModal").on("shown.bs.modal", function (e) {
          let activeItem = $(this).find("a.active")
          activeItem[0].scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "start"
          })
        })
        $("#selectTimeZoneModal").on("hidden.bs.modal", initTimeZoneSelect)
      }
      //   搜索array中的label字段，忽略大小写
      function useSearchTimeZone(queryString, array) {
        queryString = queryString.value
        let searchRes = []
        for (let i = 0; i < array.length; i++) {
          let item = array[i]
          if (item.label.toLowerCase().indexOf(queryString.toLowerCase()) > -1) {
            searchRes.push(item)
          }
        }
        // 将list渲染到页面
        renderTimeZoneList(searchRes)
        initTimeZoneSelect()
      }
      // 确认选择时区按钮
      function confirmTimeZone() {
        //  找到 active类的dom,拿到对应label和value,存入session,关闭modal
        let activeItem = $("#selectTimeZoneModal  .list-group a.active")
        let label = activeItem.text()
        let value = activeItem.attr("value")
        sessionStorage.setItem("localTimeZone", JSON.stringify({ label, value }))
        $("#selectTimeZoneModal").modal("hide")
        //   刷新iframe的src属性
        const iframe = document.getElementById("iframe")
        let activeName = $("#toggle li div.active").attr("id")
        let refreshList = ["VersionMenuPage", "PaymentMenu"]
        if (refreshList.includes(activeName)) {
          iframe.contentWindow.location.reload()
        }
      }
      // 下载Mlist数据
      function getUnuseMlist() {
        fetch("../../manager_mList/getUnuseMlist", {
          headers: {
            domain: sessionStorage.getItem("domain"),
            storeNumber: sessionStorage.getItem("storeNumber")
          }
        })
          .then(r => r.blob())
          .then(blob => {
            let url = window.URL.createObjectURL(blob)
            let fileLink = document.createElement("a")
            fileLink.href = url
            fileLink.download = "MTMl.txt"
            document.body.appendChild(fileLink)
            fileLink.click()
            fileLink.remove()
          })
      }
    </script>
    <!-- 左侧导航栏逻辑，包含点击跳转 -->
    <script src="../../static/nav/js/custom.js"></script>
    <script></script>
  </body>
</html>
