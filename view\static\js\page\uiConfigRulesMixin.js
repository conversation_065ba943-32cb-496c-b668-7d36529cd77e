const uiConfigRulesMixin = {
  data() {
    return {
      rules: {
        storeNumber: [
          {
            required: true,
            message: "Please enter the store number",
            trigger: "blur"
          }
        ],
        type: [
          {
            required: true,
            message: "Please select type",
            trigger: "change"
          }
        ],
        currency: [
          {
            required: true,
            message: "Please select a currency",
            trigger: "change"
          }
        ],
        currencyWay: [
          {
            required: true,
            message: "Please enter currency symbol ",
            trigger: "blur"
          }
        ],
        page: [
          {
            required: true,
            message: "Please select the page",
            trigger: "change"
          }
        ],
        priceWay: [
          {
            required: true,
            message: "Please select POS unit price 1-8",
            trigger: "change"
          }
        ],
        color: [
          {
            required: true,
            message: "Please enter theme color",
            trigger: "blur"
          },
          {
            pattern: /^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/,
            message: "Please enter legal color",
            trigger: "blur"
          }
        ],
        pcBackgroundColor: [
          {
            required: true,
            message: "Please enter PC version background color",
            trigger: "blur"
          },
          {
            pattern: /^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/,
            message: "Please enter legal color",
            trigger: "blur"
          }
        ],
        numberDishes: [
          {
            required: true,
            validator: OrderNumber,
            trigger: "blur"
          }
        ],
        // 动态地址
        address: {
          en: [
            {
              required: true,
              message: "Please enter shop address(en)",
              trigger: "blur"
            }
          ],
          thirdLan: [
            {
              required: true,
              message: "Please enter shop address(thirdLan)",
              trigger: "blur"
            }
          ],
          zh: [
            {
              required: true,
              message: "Please enter shop address(zh-hk)",
              trigger: "blur"
            }
          ]
        },
        // 购物车文字提示
        shopTipText: {
          en: [
            {
              required: true,
              message: "Please enter a valid value",
              trigger: "change"
            }
          ],
          thirdLan: [
            {
              required: true,
              message: "Please enter a valid value",
              trigger: "change"
            }
          ],
          zh: [
            {
              required: true,
              message: "Please enter a valid value",
              trigger: "change"
            }
          ]
        },

        ohqTimeout: [
          {
            required: true,
            message: "Please enter default time horizon",
            trigger: "change"
          }
        ],
        // 配置送达时间
        pickupTime: {
          pickupDayRange: [
            {
              required: true,
              message: "Please enter a valid value",
              trigger: "change"
            }
          ],

          pickupTimeInterval: [
            {
              required: true,
              message: "Please enter a valid value",
              trigger: "change"
            },
            {
              pattern: /^([05]|([1-9]+\d*[05]))?$/,
              message: "Value must be a multiple of 5",
              trigger: "change"
            }
          ],
          advanceOrderMinutes: [
            {
              required: true,
              message: "Please enter a valid value",
              trigger: "change"
            }
          ],
          printsKPImmediately: [
            {
              required: true,
              message: "Please enter a valid value",
              trigger: "change"
            }
          ]
        },
        // 配置 SHOW WITH FCODE limit数量限制提示
        limitByFcode: {
          en: [
            {
              required: true,
              message: "Please enter a restriction hint(en)",
              trigger: "blur"
            }
          ],
          zh: [
            {
              required: true,
              message: "Please enter a restriction hint(zh-hk)",
              trigger: "blur"
            }
          ],
          thirdLan: [
            {
              required: true,
              message: "Please enter a restriction hint(thirdLan)",
              trigger: "blur"
            }
          ]
        },
        thirdLan: [
          {
            required: true,
            message: "Please enter third language support",
            trigger: "blur"
          }
        ],
        preOrderPINLength: [
          {
            required: true,
            trigger: "blur"
          }
        ],
        hotSaleNum: [
          {
            required: true,
            trigger: "blur"
          }
        ],
        // 服务费
        serviceCharges: {
          openType: [
            {
              required: true,
              trigger: "change",
              message: "Please select the service charge settings"
            }
          ],
          countType: [
            {
              required: true,
              trigger: "change",
              message: "Please select the service charge settings"
            }
          ]
        },

        //保留小数位数
        keepDecimals: {
          significantDigits: [
            {
              required: true,
              trigger: "blur",
              message: "Please enter the number of significant decimal places"
            }
          ]
        },
        //地图范围
        shopMapSearchScope: {
          scope: [
            {
              required: true,
              trigger: "blur",
              message: "Please enter the shop map search scope"
            }
          ]
        },
        StaffMode: {
          addFoodCode: [
            {
              required: true,
              trigger: "blur",
              message: "Please enter food code"
            }
          ]
        },
        sponsoredLink: [
          {
            required: true,
            trigger: "blur",
            message: "Please enter post order external link"
          },
          {
            pattern: /^(((ht|f)tps?):\/\/)?[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?$/,
            message: "Please enter the correct url",
            trigger: "change"
          }
        ],
        redirectAfterLogin: [
          {
            required: true,
            message: "Please enter Re-direct to food cat on startup",
            trigger: "blur"
          }
        ],
        aliyunSMS: {
          accessKeyEncrypt: [
            {
              required: true,
              message: "Please enter the  access key encrypt ",
              trigger: "blur"
            }
          ],
          hierarchicalList: [
            {
              required: true,
              message: "Please enter the region option",
              trigger: "blur"
            }
          ],
          signName: [
            {
              required: true,
              message: "Please enter the sign name",
              trigger: "blur"
            }
          ],
          templateCode: [
            {
              required: true,
              message: "Please enter the template code",
              trigger: "blur"
            }
          ],
          validTime: [
            {
              required: true,
              trigger: "blur",
              message: "Please enter the valid time"
            }
          ],
          countdown: [
            {
              required: true,
              trigger: "blur",
              message: "Countdown must be at least 60 seconds"
            }
          ]
        },

        aliyunSMSGlobe: {
          accessKeyEncrypt: [
            {
              required: true,
              message: "Please enter the  access key encrypt ",
              trigger: "blur"
            }
          ],
          hierarchicalList: [
            {
              required: true,
              message: "Please enter the region option",
              trigger: "blur"
            }
          ],
          senderId: [
            {
              required: true,
              trigger: "change",
              validator: validateSmsSenderId
            }
          ],
          message: [
            {
              required: true,
              trigger: "blur",
              validator: validateSmsMessage
            }
          ],
          validTime: [
            {
              required: true,
              trigger: "blur",
              message: "Please enter the valid time"
            }
          ],
          countdown: [
            {
              required: true,
              trigger: "blur",
              message: "Please enter the countdown"
            }
          ]
        },

        // 部署版本出错时，发送邮件配置
        errorEmailConfig: {
          host: [
            {
              required: true,
              message: "Please enter the host",
              trigger: "blur"
            }
          ],
          email: [
            {
              required: true,
              message: "Please enter the email",
              trigger: "blur"
            },
            {
              pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+$/,
              message: "Please enter the correct email",
              trigger: "blur"
            }
          ],
          senderName: [
            {
              required: true,
              message: "Please enter the sender name",
              trigger: "blur"
            }
          ],
          to: [
            {
              required: true,
              message: "Please enter the target email",
              trigger: "blur"
            },
            {
              pattern:
                /^[A-Za-z0-9]+(?:[_-][A-Za-z0-9]+)*@[A-Za-z0-9]+(?:[-.][A-Za-z0-9]+)*\.[A-Za-z]{2,}(?:;[A-Za-z0-9]+(?:[_-][A-Za-z0-9]+)*@[A-Za-z0-9]+(?:[-.][A-Za-z0-9]+)*\.[A-Za-z]{2,})*$/,
              message: "Please enter the correct email",
              trigger: "blur"
            }
          ],
          authorizationCode: [
            {
              required: true,
              message: "Please enter the authorization code",
              trigger: "blur"
            }
          ]
        },
        orderFoodSorting: [
          {
            required: true,
            message: "Please enter the sorting rule",
            trigger: "blur"
          }
        ],
        mTypeCodeLength: [
          {
            required: true,
            message: "Please enter the mType code length",
            trigger: "blur"
          }
        ],
        distanceAlert: [
          {
            required: true,
            trigger: "change",
            message: "Please enter a valid distance"
          }
        ],
        delayRefreshCacheTime: [
          {
            required: true,
            trigger: "change",
            message: "Please enter a delay time (second)"
          }
        ],
        menuPopup: {
          timeout: [
            {
              required: true,
              trigger: "change",
              message: "Please enter idle time"
            }
          ],
          carouselInterval: [
            {
              required: true,
              trigger: "change",
              message: "Please enter a carousel interval"
            }
          ]
        },
        browserRestrictions: {
          category: [
            {
              type: "array",
              required: true,
              message: "Please select at least one category",
              trigger: "change"
            }
          ]
        },
        combinedNavigation: {
          color: [
            {
              required: false,
              message: "Please enter theme color",
              trigger: "blur"
            },
            {
              pattern: /^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/,
              message: "Please enter a hexadecimal color(start with #)",
              trigger: "blur"
            }
          ]
        },
        foodCourt: {
          column: [
            {
              required: true,
              message: "Please select the column",
              trigger: "blur"
            }
          ],
          transactionCode: [
            {
              required: true,
              message: "Please enter the transaction code",
              trigger: "blur"
            },
            {
              pattern: /^[a-zA-Z0-9]{1,6}$/,
              message: "The entered transaction code is illegal",
              trigger: "blur"
            }
          ],
          customerCode: [
            {
              required: true,
              message: "Please enter the customer code",
              trigger: "blur"
            },
            {
              pattern: /^[a-zA-Z0-9]{1,6}$/,
              message: "The entered customer code is illegal",
              trigger: "blur"
            }
          ],
          firstBillNumber: [
            {
              required: true,
              message: "Please enter the first bill number",
              trigger: "blur"
            },
            {
              pattern: /^\d{3}-\d{1,6}$/,
              message: "The entered bill number is illegal, example: 080-000001",
              trigger: "blur"
            }
          ],
          resetByHourOfDay: [
            {
              required: true,
              message: "Please enter the bill number Interval days",
              trigger: "blur"
            }
          ]
        },
        displayCRM: {
          birthdayPromptEN: [
            {
              required: true,
              message: "Please enter birthday prompt (en)",
              trigger: "change"
            }
          ],
          birthdayPromptZH: [
            {
              required: true,
              message: "Please enter birthday prompt(zh-hk)",
              trigger: "change"
            }
          ],
          birthdayPromptThirdLan: [
            {
              required: true,
              message: "Please enter birthday prompt(thirdLan)",
              trigger: "change"
            }
          ],
          requiredMemtype2: [
            {
              required: true,
              message: "Please enter the required memtype2",
              trigger: "change"
            }
          ]
        },
        salesControl: {
          countScope: [
            {
              required: true,
              message: "This field is required",
              trigger: "change"
            }
          ],
          maxCount: [
            {
              required: true,
              message: "This field is required",
              trigger: "change"
            }
          ],
          estimatedTime: [
            {
              required: true,
              message: "This field is required",
              trigger: "change"
            }
          ],
          refreshTime: [
            {
              required: true,
              message: "This field is required",
              trigger: "change"
            }
          ],
          includeFCode: [
            {
              required: true,
              message: "This field is required",
              trigger: "change"
            }
          ]
        },
        billTax: {
          billTaxRate: [
            {
              required: true,
              message: "This field is required",
              trigger: "change"
            },
            {
              pattern: /^(?!0(\.0+)?$)\d+(\.\d+)?$/,
              message: "This field is required",
              trigger: "blur"
            }
          ]
        },
        emailSenderManagement: {
          email: [
            {
              required: true,
              message: "Please enter the email",
              trigger: "blur"
            },
            {
              pattern: /@/,
              message: "Please enter the correct email",
              trigger: "blur"
            }
          ],
          senderName: [
            {
              required: true,
              message: "Please enter the sender name",
              trigger: "blur"
            }
          ],
          smtpRadio: [
            {
              required: true,
              trigger: ["blur"]
            }
          ],
          smtpDiy: [
            {
              validator: validateReqSmtpDiy,
              message: "Please enter the smtp host",
              trigger: ["change"]
            }
          ],

          authorizationCode: [
            {
              required: true,
              message: "Please enter the authorization code",
              trigger: "blur"
            }
          ]
        },
        memberAccountManagement: {
          memtype2: [
            {
              required: true,
              message: "Please enter the member type",
              trigger: "blur"
            }
          ],
          verifyContact: [
            {
              required: true,
              pattern: /(Email|Telephone)/,
              message: "Must select either Email or Telephone",
              trigger: "change"
            }
          ],
          // memberExpRadio: [
          //   {
          //     required: true,
          //     validator: validateReqMemberExpData,
          //     message: "Please enter the member expiration date",
          //     trigger: "blur"
          //   }
          // ],
          // pointExpRadio: [
          //   {
          //     required: true,
          //     validator: validateReqPointExpData,
          //     message: "Please enter the point expiration date",
          //     trigger: "blur"
          //   }
          // ],
          registerTitle: {
            en: [
              {
                required: true,
                message: "Please enter the title in the first language",
                trigger: "change"
              }
            ],
            zh: [
              {
                required: true,
                message: "Please enter the title in the second language",
                trigger: "change"
              }
            ],
            thirdLan: [
              {
                required: true,
                message: "Please enter the title in the third language",
                trigger: "change"
              }
            ]
          },
          resetPasswordTitle: {
            en: [
              {
                required: true,
                message: "Please enter the title in the first language",
                trigger: "change"
              }
            ],
            zh: [
              {
                required: true,
                message: "Please enter the title in the second language",
                trigger: "change"
              }
            ],
            thirdLan: [
              {
                required: true,
                message: "Please enter the title in the third language",
                trigger: "change"
              }
            ]
          },
          registerMsg: {
            en: [
              {
                required: true,
                message: "Please enter the message in the first language",
                trigger: "change"
              },
              {
                validator: validateReqHasCodeReq,
                trigger: "change"
              }
            ],
            zh: [
              {
                required: true,
                message: "Please enter the message in the second language",
                trigger: "change"
              },
              {
                validator: validateReqHasCodeReq,
                trigger: "change"
              }
            ],
            thirdLan: [
              {
                required: true,
                message: "Please enter the message in the third language",
                trigger: "change"
              },
              {
                validator: validateReqHasCodeReq,
                trigger: "change"
              }
            ]
          },
          resetPasswordMsg: {
            en: [
              {
                required: true,
                message: "Please enter the message in the first language",
                trigger: "change"
              },
              {
                validator: validateReqHasCodeReq,
                trigger: "change"
              }
            ],
            zh: [
              {
                required: true,
                message: "Please enter the message in the second language",
                trigger: "change"
              },
              {
                validator: validateReqHasCodeReq,
                trigger: "change"
              }
            ],
            thirdLan: [
              {
                required: true,
                message: "Please enter the message in the third language",
                trigger: "change"
              },
              {
                validator: validateReqHasCodeReq,
                trigger: "change"
              }
            ]
          },
          aliyunGlobe_registerMsg: {
            en: [
              {
                validator: validateReqHasCode,
                trigger: "change"
              }
            ],
            zh: [
              {
                validator: validateReqHasCode,
                trigger: "change"
              }
            ],
            thirdLan: [
              {
                validator: validateReqHasCode,
                trigger: "change"
              }
            ]
          },
          aliyunGlobe_resetPasswordMsg: {
            en: [
              {
                validator: validateReqHasCode,
                trigger: "change"
              }
            ],
            zh: [
              {
                validator: validateReqHasCode,
                trigger: "change"
              }
            ],
            thirdLan: [
              {
                validator: validateReqHasCode,
                trigger: "change"
              }
            ]
          }
        }
      }
    }
  },
  methods: {}
}
