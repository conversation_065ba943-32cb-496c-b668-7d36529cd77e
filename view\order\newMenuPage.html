<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dohtonbori</title>

  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
  <!-- rem布局 -->
  <script src="../js/page/lib-flexible.js"></script>
  <!-- 懒加载 -->
  <script src="../js/page/lazy.js"></script>
  <link rel="stylesheet" type="text/css" href="../css/page/newMenuPage.css" />
  <script src="../plugins/jQuery/jquery-3.6.0.min.js"></script>
  <script src="../vue/vue.min.js"></script>
  <script src="../moment/moment.js"></script>
  <!-- 语言下拉菜单 -->
  <link href="../lanSelect/jquery.sweet-dropdown.min.css" rel="stylesheet" type="text/css" />
  <script src="../lanSelect/jquery.sweet-dropdown.min.js"></script>
  <!--layer彈出框-->
  <link rel="stylesheet" href="../tools/layer_3/mobile/need/layer.css" />
  <script type="text/javascript" src="../tools/layer_3/layer.js"></script>
  <!-- 选择器 -->
  <!-- <link rel="stylesheet" type="text/css" href="../mobileSelect/mobileSelect.css">
  <script src="../mobileSelect/mobileSelect.js" type="text/javascript"></script> -->

  <!-- vconsole 真机测试 -->
  <!-- <script src="https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js"></script> -->
</head>

<body>
  <div id="app" :class="{'fixed': fixed}">
    <!-- 购物车蒙层 -->
    <template>
      <div class="mongolia" @click="clickShowCart"></div>
    </template>
    <!-- loading蒙层 -->
    <template>
      <div class="loader loader-3" v-show="loading">
        <div class="dot dot1"></div>
        <div class="dot dot2"></div>
        <div class="dot dot3"></div>
      </div>
    </template>

    <!-- 新版切换语言下拉 -->
    <template>
      <div class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect" id="dropdown-standard">
        <ul class="selectUl" style="font-size: 0.35rem; border-radius: 0.1rem; padding: 0.1rem 0">
          <li v-for="item in lanSele" :key="item.value" @click="onLanSelect(item.value)">
            <a href="#" style="line-height: 0.5rem">{{item.lable}}</a>
          </li>
        </ul>
      </div>
    </template>
    <template>
      <div class="app_container">
        <div class="header_warp" @touchstart="headerTouchStart" @touchmove="headerTouchMove" @touchend="headerTouchEnd">
          <div class="table_info">
            <!-- <span>dd</span> -->
            <span class="table_info_num">
              <span>{{ systemLanguage.tableLan }}</span>
              {{ openTable.tableNumber }}
            </span>
            <!-- <span class="table_info_lan" @click="onLangSwitcher">
                English / 繁中</span
              > -->
            <!-- 新版切换语言Icon -->
            <img src="../img/newImage/indexSwitchLan.jpg" alt="" style="width: 0.53rem" class="table_info_lan"
              data-dropdown="#dropdown-standard" data-add-anchor-x="5" />
          </div>
        </div>
        <!-- 滚动Tab头 -->
        <div class="tab_warp" @scroll.passive="getScroll($event)">
          <div :class="[{ tab_active: tabIsActive == index }, 'tab_cell']" v-for="(item, index) in allDataList"
            :key="index" @click="onTab(item, index)">
            <!-- {{openTable.language}} -->
            <template v-if="openTable.language == 'en'">
              {{ item.fType_nameA || item.name }}
            </template>
            <template v-else-if="openTable.language == 'zh'">
              {{ item.fType_nameB || item.name2 }}
            </template>
            <template v-else>{{ item.multi1 }}</template>
          </div>
        </div>
        <!-- 箭头 -->
        <img src="../img/newImage/rightPrompt.jpg" alt="" class="toRight_img" v-if="!loading && showPromptRight" />
        <img src="../img/newImage/leftPrompt.jpg" alt="" class="toLeft_img" v-if="!loading && showPromptLeft" />
        <!-- 中间内容区 -->
        <div class="content_warp" @touchstart="contentTouchStart" @touchmove="contentTouchMove"
          @touchend="contentTouchEnd">
          <div class="content_cell" v-for="(item, index) in showFoodList" :key="index">
            <!-- 价钱 -->
            <div class="foodPrice_warp" v-if="item.upa1!=0">${{item.upa1}}</div>
            <div class="addFoodBtn" @click="clickOrIn(event, item)" v-if="showaPlusSign(item)">
              +
            </div>
            <div @click="onfoodInfo(item)">
              <img v-lazy="item.imglink" alt="" class="food_img" />
              <!-- food名 -->
              <div class="food_text">
                <template v-if="openTable.language == 'en'">{{ item.desc1 }}</template>
                <template v-else-if="openTable.language == 'zh'">{{ item.desc2 }}</template>
                <template v-else>{{ item.multi1 }}</template>
              </div>
              <!-- 过敏源 -->
              <div class="allergen_warp" v-if="item.allergen_icons && item.allergen_icons.length != 0">
                <img v-for="(e, index) in item.allergen_icons" :key="index" :src="allergenList[0][e]" alt=""
                  class="allergen_Icon" />
              </div>
            </div>
          </div>
        </div>
        <div class="footer_warp" @touchstart="headerTouchStart" @touchmove="headerTouchMove" @touchend="headerTouchEnd">
          <div class="shop_car" @click="clickShowCart">{{ systemLanguage.cartlan }}</div>
        </div>
      </div>
    </template>
    <!-- 购物车弹出层-->
    <template>
      <div class="cart_warp" v-if="showCartTab">
        <!-- 返回按钮 -->
        <div class="cart_info_warp_header">
          <img src="../img/page/black.jpg" alt="" class="blackIcon" @click="onBlack" />
          <span class="table_info_num">
            <span>{{ systemLanguage.tableLan }}</span>
            {{ openTable.tableNumber }}
          </span>
        </div>
        <!-- 购物车内容 -->
        <div class="cart_food_box">
          <div v-if="shopCartList.length==0" class="cart_food_null">
            {{systemLanguage.nullFoodText}}
          </div>
          <div class="cart_food_warp" v-else>
            <!-- 购物车标题 -->
            <div class="your_cart">{{systemLanguage.carPageTitle}}</div>
            <!-- 购物车主内容 -->
            <div class="cart_food_cell" v-for="(item, index) in shopCartList" :key="index">
              <div class="cart_food_img" v-if="openTable.thumbnail">
                <img :src="foodBaseUrl + item.fCode + '.jpg'" alt="" class="cart_food_img_cell"
                  onerror="this.src='../img/newImage/timg.jpg'" />
              </div>

              <div class="cart_info">
                <div class="info_left">
                  <!-- food名称 -->
                  <div class="cart_food_title">
                    <template v-if="openTable.language == 'en'">{{ item.desc1 }}</template>
                    <template v-else-if="openTable.language == 'zh'">{{ item.desc2 }}</template>
                    <template v-else>{{ item.multi1 }}</template>
                  </div>
                  <!-- 细项 -->
                  <div v-if="openTable.sideDish">
                    <div class="littleitem" v-if="
                          item.newOrderItemFoodList.length != 0 ||
                          item.newOrderItemMListList.length != 0 ||
                          item.newOrderItemMTypeList.length != 0 ||
                      
                          item.newOrderItemFoodTypeList.length != 0
                           ">
                      {{ showlogic( item.newOrderItemFoodList, item.newOrderItemMListList,
                      item.newOrderItemMTypeList, item.newOrderItemFoodTypeList ) }}
                    </div>
                  </div>

                  <!-- 价格和 -->
                  <div class="cart_food_priceNum" v-if="openTable.price">
                    <div class="cart_food_price">
                      <div v-if="calculatedTotal(item)!=0">
                        $
                        <span class="cart_food_price_amount">{{ calculatedTotal(item)}}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!--加数量  -->
                <div class="info_right">
                  <div class="cart_food_numBox">
                    <div class="cart_del_btn" @click="delCarFood(item, index)">-</div>
                    <div class="cart_food_numVal">{{ item.qty1 }}</div>
                    <div class="cart_add_btn" @click="addCarFood(item)">+</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 送单按钮 -->
        <div @click="subOrder" :class="[{ disabledSend: allshoplNumber == 0 }, 'send_single']">
          {{ systemLanguage.sendDanLan }}
        </div>
      </div>
    </template>

    <!-- 抛物线DOM -->
    <template>
      <div class="ball-container">
        <!--小球-->
        <div v-for="ball in balls">
          <transition name="drop" @before-enter="beforeDrop" @enter="dropping" @after-enter="afterDrop">
            <div class="ball" v-show="ball.show">
              <div class="inner inner-hook">
                <img :src="addImgLink" alt="" style="
                      width: 40px;
                      height: 40px;
                      border-radius: 50%;
                      position: relative;
                      z-index: 200;
                    " onerror="this.src='../img/newImage/timg.jpg'" />
              </div>
            </div>
          </transition>
        </div>
      </div>
    </template>
    <!-- 蒙层提示 -->
    <template>
      <!-- 关闭蒙层 -->
      <div class="close_warp" @click="closeBg" style="display: none">
        <svg width="1em" height="1em" viewBox="0 0 16 16" class="bi bi-x" fill="currentColor"
          xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd"
            d="M11.854 4.146a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708-.708l7-7a.5.5 0 0 1 .708 0z" />
          <path fill-rule="evenodd"
            d="M4.146 4.146a.5.5 0 0 0 0 .708l7 7a.5.5 0 0 0 .708-.708l-7-7a.5.5 0 0 0-.708 0z" />
        </svg>
      </div>
      <!-- 超过商品数量蒙层 -->
      <div class="mongolia2" style="display: none" @click="closeBg"></div>
      <div id="dialog" style="display: none">
        <div class="maskimg_warp">
          <!-- <p class="maskimg_text1">{{ systemLanguage.promptText1 }}</p> -->
          <p class="maskimg_text2">{{ systemLanguage.promptText2 }}</p>
        </div>
      </div>
    </template>
    <!-- food详细信息 -->
    <template>
      <transition>
        <div class="food_info_warp" v-if="showFoodWarp">
          <!-- 头部 -->
          <div class="food_info_warp_header">
            <!-- 返回按钮 -->
            <img src="../img/page/black.jpg" alt="" class="blackIcon" @click="blackbtn" />

            <!-- 滚动标签 -->
            <div v-if="nameRoll" id="foodName_warp" class="food_info_warp_header_title merquee">
              <div class="merquee-txt">
                <template v-if="openTable.language == 'en'">{{ foodInfoItem.desc1 }}</template>
                <template v-else-if="openTable.language == 'zh'">
                  {{ foodInfoItem.desc2 }}
                </template>
                <template v-else>{{ foodInfoItem.multi1 }}</template>
              </div>
            </div>
            <!-- 不滚动 -->
            <div v-else class="food_info_warp_header_title">
              <template v-if="openTable.language == 'en'">{{ foodInfoItem.desc1 }}</template>
              <template v-else-if="openTable.language == 'zh'">{{ foodInfoItem.desc2 }}</template>
              <template v-else>{{ foodInfoItem.multi1 }}</template>
            </div>
          </div>
          <!-- 内容区 -->
          <div class="food_info_warp_content">
            <div class="clearfix food_info_warp_content_top">
              <img v-lazy="foodInfoItem.imglink" alt="" class="food_info_warp_img" />
              <div class="food_info_warp_content_products">
                <template v-if="openTable.language == 'en'">
                  <pre class="pre_style">{{ foodInfoItem.prod_textareaA }}</pre>
                </template>
                <template v-else-if="openTable.language == 'zh'">
                  <pre class="pre_style">{{ foodInfoItem.prod_textareaB }}</pre>
                </template>
                <template v-else>
                  <pre class="pre_style">{{ foodInfoItem.prod_textareaC }}</pre>
                </template>
              </div>
            </div>
            <!-- 过敏源 -->
            <div class="food_info_warp_allergen" v-if="
                  foodInfoItem.allergen_icons &&
                  foodInfoItem.allergen_icons.length != 0
                ">
              <div class="food_info_warp_allergen_title">{{ systemLanguage.allergenText }}</div>
              <img v-for="(e, index) in foodInfoItem.allergen_icons" :key="index" :src="allergenList[0][e]" alt=""
                class="food_info_warp_allergen_Icon" />
            </div>
            <!-- 细项 -->
            <div class="food_info_warp_content_infoPoints" v-if="
                  foodInfoItem.foodList.length != 0 ||
               
                  foodInfoItem.mListList.length != 0 
                ">
              <div class="infoPoints_lable">{{ systemLanguage.singleItemTaste }}</div>
              <div class="infoPoints_content">
                <!-- 细项foodList -->
                <div v-for="(item, index) in foodInfoItem.foodList" :key="index" class="infoPoints_content_cell">
                  <div class="infoPoints_select">
                    <img src="../img/newImage/custom.jpg" alt="" class="infoPoints_addicon" id="trigger1"
                      @click.stop="onPicker(item,'foodList')" v-if="item.listSelect&&item.listSelect.length!=0" />
                    <template v-if="openTable.language == 'en'">{{ item.desc1 }}</template>
                    <template v-else-if="openTable.language == 'zh'">{{ item.desc2 }}</template>
                    <template v-else>{{ item.multi1 }}</template>
                    <!-- 细项价钱 -->
                    <template v-if="item.upa1&&item.upa1!=0">(${{ item.upa1 }})</template>
                  </div>
                  <div class="detailTaste">{{todofineItem(item.localListSelect)}}</div>
                </div>
                <!-- 细项mlistList -->
                <div v-for="(item, index) in foodInfoItem.mListList" :key="index" class="infoPoints_content_cell">
                  <div class="infoPoints_select">
                    <img src="../img/newImage/custom.jpg" alt="" class="infoPoints_addicon" id="trigger1"
                      @click.stop="onPicker(item,'mListList')" v-if="item.listSelect&&item.listSelect.length!=0" />
                    <template v-if="openTable.language == 'en'">{{ item.name }}</template>
                    <template v-else-if="openTable.language == 'zh'">{{ item.name2 }}</template>
                    <template v-else>{{ item.multi1 }}</template>
                    <!-- 细项价钱 -->
                    <template v-if="item.upa1&&item.upa1!=0">(${{ item.upa1 }})</template>
                  </div>
                  <div class="detailTaste">{{todofineItem(item.localListSelect)}}</div>
                </div>
              </div>
            </div>
            <div v-if="foodInfoItem.foodTypeList.length != 0||foodInfoItem.mTypeList.length != 0" class="foodDivider">
            </div>
            <!-- 可选细项foodtypeInList(不再全部遍历进foodtypeInList显示)  -->
            <div class="food_info_warp_content_foodtypeInList"
              v-if="foodInfoItem.foodTypeList&&foodInfoItem.foodTypeList.length != 0">
              <div class="foodInfoItem_cell" v-for="(vItem, i) in foodInfoItem.foodTypeList" :key="i">
                <div class="optionalTitle">
                  <template v-if="openTable.language == 'en'">
                    {{ vItem.fType_nameA||vItem.name }}
                  </template>
                  <template v-else-if="openTable.language == 'zh'">
                    {{ vItem.fType_nameB||vItem.name2 }}
                  </template>
                  <template v-else>{{ vItem.multi1 }}</template>
                  <span v-if="(vItem.minQty||vItem.maxQty)">
                    <span class="selectMinNum">{{selectPrompt(vItem.minQty,vItem.maxQty)}}</span>
                  </span>
                </div>
                <div class="infoPoints_content" v-if="vItem.foodList.length!=0">
                  <div v-for="(item, index) in vItem.foodList" :key="index" class="infoPoints_content_cell">
                    <div :class="[
                    item.selected
                      ? 'infoPoints_select'
                      : 'infoPoints_noselect'
                  ]" @click="multiselect(item, 'foodtypeInList',vItem)">
                      <img src="../img/newImage/custom.jpg" alt="" class="infoPoints_addicon"
                        @click.stop="onPicker(item,'foodtypeInList')"
                        v-if="item.listSelect&&item.listSelect.length!=0&&item.selected" />
                      <template v-if="openTable.language == 'en'">{{ item.desc1 }}</template>
                      <template v-else-if="openTable.language == 'zh'">{{ item.desc2 }}</template>
                      <template v-else>{{ item.multi1 }}</template>
                      <!-- 细项价钱 -->
                      <template v-if="item.upa1&&item.upa1!=0">(${{ item.upa1 }})</template>
                    </div>
                    <div class="detailTaste">{{todofineItem(item.localListSelect)}}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 可选细项mtypeList(不再全部遍历进可选细项mtypeInList显示)    -->
            <div class="food_info_warp_content_mtypeInList"
              v-if="foodInfoItem.mTypeList&&foodInfoItem.mTypeList.length != 0">
              <div class="foodInfoItem_cell" v-for="(vItem, i) in foodInfoItem.mTypeList" :key="i">
                <div class="optionalTitle">
                  <template v-if="openTable.language == 'en'">{{ vItem.desc }}</template>
                  <template v-else-if="openTable.language == 'zh'">{{ vItem.desc2 }}</template>
                  <template v-else>{{ vItem.multi1 }}</template>
                  <span v-if="(vItem.minQty||vItem.maxQty)">
                    <span class="selectMinNum">{{selectPrompt(vItem.minQty,vItem.maxQty)}}</span>
                  </span>
                </div>
                <div class="infoPoints_content" v-if="vItem.mListList.length!=0">
                  <div v-for="(item, index) in vItem.mListList" :key="index" class="infoPoints_content_cell">
                    <div :class="[
                    item.selected
                      ? 'infoPoints_select'
                      : 'infoPoints_noselect',
                  ]" @click="multiselect(item, 'mtypeInList',vItem)">
                      <img src="../img/newImage/custom.jpg" alt="" class="infoPoints_addicon"
                        @click.stop="onPicker(item,'mtypeInList')"
                        v-if="item.listSelect&&item.listSelect.length!=0&&item.selected" />
                      <template v-if="openTable.language == 'en'">{{ item.name }}</template>
                      <template v-else-if="openTable.language == 'zh'">{{ item.name2 }}</template>
                      <template v-else>{{ item.multi1 }}</template>
                      <template v-if="item.upa1&&item.upa1!=0">(${{ item.upa1 }})</template>
                    </div>
                    <div class="detailTaste">{{todofineItem(item.localListSelect)}}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 购买数量 -->
            <div class="food_info_warp_content_shopNum">
              <div class="shopNumTitle">{{ systemLanguage.buyNum }}</div>
              <div class="cart_food_numBox">
                <div class="cart_del_btn" @click="delfoodInfo()">-</div>
                <div class="cart_food_numVal">{{ foodInfoItem.qty1 }}</div>
                <div class="cart_add_btn" @click="addfoodInfo()">+</div>
              </div>
            </div>
          </div>
          <!-- 底部 -->
          <div class="food_info_warp_footer">
            <div class="joinCartBtn" @click="joinCartF">{{ systemLanguage.addCartText }}</div>
          </div>
        </div>
      </transition>
    </template>

    <!-- tip提示 -->
    <template>
      <div class="tip_warp">
        <div class="tip_cell">
          {{ tipTxt }}
          <!-- {{systemLanguage.clearText}} -->
        </div>
      </div>
    </template>
    <!-- 我的细项蒙层 -->
    <template>
      <div class="mongolia3"></div>
    </template>
    <!-- 我的细项弹窗选择 -->
    <template>
      <transition name="fade">
        <div class="foodDialog" v-show="hiddenDialog">
          <div class="title">请选择</div>
          <div class="optionalFood" id="myxiDialog">
            <div class="radio-box" v-for="(item,index) in listSelect" :key="index">
              <label class="checkLabel">
                <input :value="item" @change="check" type="checkbox" name="group1" id="group1" v-model="checkVal" />
                <span class="checkSpan" v-if="openTable.language == 'en'">
                  {{item.name||item.nameA}}
                </span>
                <span class="checkSpan" v-if="openTable.language == 'zh'">
                  {{item.name2||item.nameB}}
                </span>
                <span class="checkSpan" v-else>{{item.multi1}}</span>
              </label>
            </div>
          </div>
          <div class="btns">
            <div class="cencelBtn small_btn" @click="onCencelMyxi">取消</div>
            <div class="defaultBtn small_btn" @click="onfineItem">确定</div>
          </div>
        </div>
      </transition>
    </template>
  </div>
  <script>
    // var vConsole = new VConsole();

    Vue.use(VueLazyload, {
      attempt: 1,
      error: "../img/newImage/timg.jpg",
      loading:
        "data:image/svg+xml;base64,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"
    })

    var app = new Vue({
      el: "#app",
      data: {
        showFoodWarp: false,
        openTable: null,
        tabIsActive: 0,
        systemLanguage: "",
        lanSele: [
          {
            lable: "English",
            value: "en"
          },
          {
            lable: "繁體",
            value: "zh"
          }
          // {
          //   lable: '简体',
          //   value: 'gbk',
          // },
        ],
        // lanbackup: {
        //   en: 'name',
        //   zh: 'name2',
        // }, // 语言选择
        originalArry: [], // 原数组
        allDataList: [],
        foodList: [], // 食品内容页
        shopCartList: [], // 购物车数组
        isPopup: false,
        // 抛物线
        count: 0,
        addImgLink: "",
        balls: [
          //小球 设为3个
          {
            show: false
          },
          {
            show: false
          },
          {
            show: false
          }
        ],
        dropBalls: [],
        nameRoll: false,
        foodInfoItem: {},
        allshoplNumber: 0, // 监听购物车总数，
        foodInfodetails: [], // 细项
        tipTxt: "",
        showLittleDIV: false, // 细项是否显示
        // 本地细项
        localfoodListArry: [],
        localmlistListArry: [],
        localmtypeListArry: [],

        localfoodTypeListArry: [],
        foodBaseUrl: null,
        menuTimer: 0, // 计时器
        loading: false,
        hasPriceNum: 5, // 有价钱数量限制,
        nullPriceNum: 2, // 无价钱数量限制,
        showPromptRight: false,
        showPromptLeft: false,
        allergenList: [], // 过敏源数据
        fixed: true,
        contentStartY: 0,
        isShowaPlusSign: false, // 是否显示菜单加号
        hiddenDialog: false,
        checkVal: [],
        // checkradio: '',
        listSelect: [],
        parentListSelect: {}, // 点击的细项，用于重新塞数据
        myXitype: "", // 点击我的细项类型，用于区分
        showCartTab: false // 是否显示购物车页面
      },

      created () {
        this.initializeThe() //初始化数据
        this.fixLan() //固定语言
        this.getData() //初始化商品信息
        this.timer() //计时器启动
        this.getAllergenImg() //过敏源图片

        // let use_dow = '1234567';
        // let use_date = '2020.01.01-2099.12.31';
        // let use_time = '05:30-16:59';
        // let res = this.consistentTimePeriod(use_dow, use_date, use_time);
        // console.log(res, '88');
        // // console.log(
        //   '测试ios 2020-09-16 Between 2020-01-01, 2099-12-31 ',
        //   moment('11:35:00').isBetween[('11:35:00', '23:59:00')]
        // );
        // var format = 'hh:mm:ss';
        // var time = moment('12:04:00', format),
        //   beforeTime = moment('12:04:00', format),
        //   afterTime = moment('23:59:00', format);

        // if (time.isBetween(beforeTime, afterTime, null, '[]')) {
        //   console.log('is between');
        // } else {
        //   console.log('is not between');
        // }
      },
      mounted () { },

      computed: {
        // 过滤food超时数据
        showFoodList () {
          return this.foodList.filter(item => {
            return this.consistentTimePeriod(item.use_dow, item.use_date, item.use_time) == true
          })
        }
      },
      watch: {
        // openTable: {
        //   handler(newVal, oldVal) {
        //     this.openTable = newVal;
        //     // this.allDataList = this.allDataList;
        //     // console.log(this.allDataList);
        //     // console.log('监听obj整个对象的变化');
        //   },
        //   deep: true,
        // },

        shopCartList (newVal, oldVal) {
          let allshoplNumber = 0
          newVal.forEach(item => {
            allshoplNumber += item.qty1
          })
          this.allshoplNumber = allshoplNumber
          // console.log(this.allshoplNumber, '购物车数量');
        }
      },

      methods: {
        check () {
          // this.listSelect.forEach((item) => {
          //   item.isChecked = false;
          // });
          // //再设置当前点击项选中
          // this.checkradio = this.listSelect[index];
          // console.log(this.checkradio, '点击的对象');
          // // 设置值，以供传递
          // this.listSelect[index].isChecked = true;
          console.log(this.checkVal)
        },
        onfineItem () {
          let selectObj = this.checkVal
          console.log(selectObj, "提交的对象")
          let localListSelect = this.parentListSelect.localListSelect
          this.$set(this.parentListSelect, "localListSelect", selectObj)
          this.hiddenDialog = false
          $(".mongolia3").fadeOut()
          // 重新塞回数据
          // 重新区分mlis和mtyp
          let newData = {
            mlis: [],
            mty: []
          }
          var tempArr = []
          var newArr = []
          selectObj.forEach(e => {
            if (e.myxiType == "mlis") {
              newData.mlis.push(e)
            } else {
              newData.mty.push(e)
            }
            //新建属性名
            // if (Object.keys(newData).indexOf('' + e.myxiType) === -1) {
            //   newData[e.myxiType] = []
            // }
            // //对应插入属性值（目前只可能是mlis和mty两个属性）
            // newData[e.myxiType].push(e)
          })

          let newMty = newData.mty || [] //遍历是同步，遍历完获取mty数据再进一步归类
          if (newMty.length != 0) {
            for (let i = 0; i < newMty.length; i++) {
              if (tempArr.indexOf(newMty[i].dadCode) === -1) {
                newArr.push({
                  code: newMty[i].dadCode,
                  newOrderItemMListList: [newMty[i]]
                })
                tempArr.push(newMty[i].dadCode)
              } else {
                for (let j = 0; j < newArr.length; j++) {
                  if (newArr[j].code == newMty[i].dadCode) {
                    newArr[j].newOrderItemMListList.push(newMty[i])
                    break
                  }
                }
              }
            }
          }
          newData.mty = newArr //重新替换mty
          console.log("最终输出：", newData)
          let type = this.myXitype
          var localArry
          switch (type) {
            case "foodList":
              localArry = this.localfoodListArry
              localArry.forEach(item => {
                if (item.fCode == this.parentListSelect.fcode) {
                  item.newOrderItemMListList = newData.mlis
                  item.newOrderItemMTypeList = newData.mty
                }
              })
              console.log(localArry, "foodlist添加我的细项本地")
              break
            case "foodtypeInList":
              localArry = this.localfoodTypeListArry
              console.log(localArry, "foodtypeInList添加我的细项本地")
              localArry.forEach(item => {
                //遍历第一层大类code=》newOrderItemFoodList=》细项
                item.newOrderItemFoodList.forEach(e => {
                  if (e.fCode == this.parentListSelect.fcode) {
                    e.newOrderItemMListList = newData.mlis
                    e.newOrderItemMTypeList = newData.mty
                  }
                })
              })
              break
            case "mListList":
              localArry = this.localmlistListArry
              localArry.forEach(item => {
                if (item.code == this.parentListSelect.code) {
                  item.newOrderItemMListList = newData.mlis
                  item.newOrderItemMTypeList = newData.mty
                }
              })
              break
            case "mtypeInList":
              localArry = this.localmtypeListArry
              localArry.forEach(item => {
                item.newOrderItemMListList.forEach(e => {
                  if (e.code == this.parentListSelect.code) {
                    e.newOrderItemMListList = newData.mlis
                    e.newOrderItemMTypeList = newData.mty
                  }
                })
              })
              break
          }
          console.log(localArry, "bendi")
          // 选中我的细项
        },
        onCencelMyxi () {
          this.hiddenDialog = false
          $(".mongolia3").fadeOut()
        },
        // 处理细项中的细项
        todofineItem (data) {
          let lan = this.openTable.language
          if (data) {
            if (data.length == 0) {
              return ""
            } else {
              let arryText = []
              data.forEach((item, i) => {
                if (lan == "en") {
                  if (item.name) {
                    arryText.push(item.name)
                  } else if (item.desc) {
                    arryText.push(item.desc)
                  } else if (item.desc1) {
                    arryText.push(item.desc1)
                  }
                } else if (lan == "zh") {
                  if (item.name2) {
                    arryText.push(item.name2)
                  } else if (item.desc2) {
                    arryText.push(item.desc2)
                  }
                } else {
                  if (item.multi1) {
                    arryText.push(item.multi1)
                  } else if (item.name3) {
                    arryText.push(item.name3)
                  }
                }
              })
              let str = ""
              arryText.forEach((item, i) => {
                str += arryText[i] + "" + "+" + ""
              })
              if (str.length > 0) {
                str = str.substr(0, str.length - 1)
              }
              return str
            }
          } else {
            return ""
          }
        },
        // 初始化openTable/shopcart信息
        initializeThe () {
          this.openTable = JSON.parse(this.getQueryString("openTable"))

          this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
          console.log(this.openTable)
        },
        getData () {
          let that = this
          that.loading = true
          let { companyName, storeNumber, tableNumber, performType } = that.openTable
          let tableKey
          if (performType == 1) {
            tableKey = that.openTable.tableKey
          } else {
            tableKey = ""
          }
          $.get({
            url: "../food/getFoodAndFoodTypeList",
            data: {
              companyName,
              tableKey,
              performType,
              table_number: tableNumber,
              store_number: storeNumber
            },
            success: function (res) {
              if (res.RESULT_CODE == 0) {
                let domain = location.host.split(".")[0]
                let baseUrl = "https://appwise.oss-cn-hongkong.aliyuncs.com/" + domain + "/image/"
                that.foodBaseUrl = baseUrl + "food/"
                res.list.sort((objectN, objectM) => {
                  let valueN = objectN["sort2"]
                  let valueM = objectM["sort2"]
                  if (valueN > valueM) return 1
                  else if (valueN < valueM) return -1
                  else return 0
                })
                // res.list[0].use_date = '5';

                res.list.forEach(e => {
                  if (that.consistentTimePeriod(e.use_dow, e.use_date, e.use_time)) {
                    that.allDataList.push(e)
                  }

                  if (e.foodList) {
                    e.foodList.sort((objectN, objectM) => {
                      let valueN = objectN["seq"]
                      let valueM = objectM["seq"]
                      if (valueN > valueM) return 1
                      else if (valueN < valueM) return -1
                      else return 0
                    })
                    e.foodList.forEach(item => {
                      // item.foodList = [{
                      //   desc1: 'aa',
                      //   desc2: 'aa',
                      //   desc3: 'aa',
                      //   fCode: 'aa',
                      //   k1: 'aa',
                      //   listSelect: [{
                      //     id: '1',
                      //     val: '冰'
                      //   }, {
                      //     id: '2',
                      //     val: '去冰'
                      //   }, {
                      //     id: '3',
                      //     val: '加冰'
                      //   }]
                      // }]
                      if (item.allergen_icons && item.allergen_icons.length != 0) {
                        item.allergen_icons = item.allergen_icons.split(",")
                        item.allergen_icons.forEach(iconName => { })
                      }
                      item.newOrderItemFoodList = [] //添加选中细项
                      item.newOrderItemMListList = [] //添加选中细项
                      item.newOrderItemMTypeList = [] //添加选中细项

                      item.newOrderItemFoodTypeList = [] //添加选中细项
                      item.imglink = baseUrl + "food/" + item.fcode + ".jpg?" + moment().valueOf()
                      // item.use_date = '5';
                      that.ftySort(item.foodTypeList) //大food=>fty排序
                      that.mtySort(item.mTypeList) //大food=>fty排序
                    })
                    // 处理对应时间段显示食品
                  }
                })

                that.originalArry = res.list //原数组

                // console.log(that.allDataList);
                if (that.allDataList.length != 0) {
                  that.foodList = that.allDataList[0].foodList
                  if (that.allDataList.length >= 4) {
                    that.showPromptRight = true //向右滑动图标提示
                    this.showPromptLeft = false
                  }
                } else {
                  that.layerDia("The selected table is currently unavailable for service")
                }
                that.loading = false
              } else {
                that.layerDia("The selected table is currently unavailable for service")
                // console.log('error');
              }
            },
            error: function (error) {
              alert(JSON.stringify(error))
              // console.log('error');

              // that.loading = false;
            }
          })
        },
        // opentable解析
        getQueryString (name) {
          var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i")
          var r = decodeURI(window.location.search).substr(1).match(reg)
          if (r != null) {
            return unescape(r[2])
          }
          return null
        },
        // 获取过敏源图片
        getAllergenImg () {
          let data = {
            domain: sessionStorage.getItem("domain"),
            storeNumber: sessionStorage.getItem("storeNumber"),
            typeNameList: ["Allergen icons"]
          }
          $.post({
            url: "../photoConfig/getSpecifiedPhotoConfig",
            dataType: "json",
            traditional: true,
            data,
            success: res => {
              let imgList = res.photoConfigList
              let obj = {}
              if (imgList.length != 0) {
                imgList.forEach(item => {
                  item.logoUrl =
                    "https://appwise.oss-cn-hongkong.aliyuncs.com" +
                    "/" +
                    item.domain +
                    "/" +
                    item.storeNumber +
                    "/image/" +
                    item.typeName +
                    "/" +
                    item.fileName +
                    ".jpg?x-oss-process=image/resize,w_100,h_100"
                  obj[item.fileName] = item.logoUrl
                })
                this.allergenList.push(obj)
                console.log(this.allergenList, "zzz")
              }
            },
            error: function () {
              alert("error")
            }
          })
        },
        // 固定语言
        fixLan () {
          // let language = sessionStorage.getItem(language) || 'zh';
          let { language } = this.openTable
          console.log(language)
          // 后期将固定语言抽出单独js文件引入
          switch (language) {
            case "en":
              this.systemLanguage = {
                tableLan: "Table Number：",
                cartlan: "View your cart",
                historicalLan: "My Order",
                sendDanLan: "Place your order",
                orderlan: "My Order",
                promptText1: "Fight Food Waste with Us",
                promptText2:
                  "Please select two delicacy per order. Additional orders are welcome.",
                // promptText2:
                //   'Choose up to 4 delicacies per order. Re-order is welcomed.',
                clearText: "Please put the food into the shopping cart first!",
                buyNum: "Order quantity",
                addCartText: "Add to cart",
                singleItemTaste: "Default Option ",
                timeoutMsg: "This food is not available at this moment!",
                allergenText: "Allergen",
                nullFoodText: "The shopping cart is empty!",
                nullhisfoodText: "The historical record is temporarily empty!",
                maxNumText: "Exceeding the maximum quantity limit!",
                minNumText: "Below the minimum quantity limit！",
                carPageTitle: "Your Cart",
                errorTimeout: "Not until the next order",
                errorMsg: "The network is busy, please try again later!",
                errorDesc: "Cannot place order. Please contact our staff.",
                defSuccessMsg: "Order placed successfully!"
              }
              break
            case "zh":
              this.systemLanguage = {
                tableLan: "枱號：",
                cartlan: "購物車",
                historicalLan: "訂單",
                sendDanLan: "送單",
                orderlan: "訂單",
                promptText1: "攜手減少浪費食物",
                // promptText2: '每次下單可選四款食物，下單次數不限.',
                promptText2: "每次下單可選兩款餐點，下單次數不限.",
                clearText: "請先將食品放入購物車",
                buyNum: "購買數量",
                addCartText: "加入購物車",
                singleItemTaste: "默認選項",
                timeoutMsg: "食品暫停供應!",
                allergenText: "致敏原",
                nullFoodText: "購物車暫時是空的!",
                nullhisfoodText: "歷史記錄暫時是空的!",
                maxNumText: "超出最大數量限制!",
                minNumText: "低於最小數量限制！",
                carPageTitle: "購物車",
                errorTimeout: "距離下次下單還需",
                errorMsg: "網絡繁忙，請稍後嘗試!",
                errorDesc: "下單不成功，請聯絡工作人員.",
                defSuccessMsg: "送單成功!"
              }
              break
            case "gbk":
              this.systemLanguage = {
                tableLan: "台号：",
                cartlan: "购物车",
                historicalLan: "订单",
                sendDanLan: "送单",
                orderlan: "订单",
                promptText1: "携手减少浪费食物",
                // promptText2: '每次下單可選四款食物，下單次數不限.',
                promptText2: "每次下单可选两款餐点，下单次数不限.",
                clearText: "请先将食品加入购物车",
                buyNum: "购买数量",
                addCartText: "加入购物车",
                singleItemTaste: "默认选项",
                timeoutMsg: "食品暂停供应!",
                allergenText: "致敏原",
                nullFoodText: "购物车暂时是空的!",
                nullhisfoodText: "历史记录暂时是空的!",
                maxNumText: "超出最大数量限制!",
                minNumText: "低于最小数量限制!",
                carPageTitle: "购物车",
                errorTimeout: "距离下次下单还需",
                errorMsg: "网络繁忙,请稍后尝试!",
                errorDesc: "下单不成功，请联络工作人员.",
                defSuccessMsg: "送单成功！"
              }
              break
          }
        },

        // 提示语弹窗封装
        // onTipshow(text) {
        //   this.tipTxt = text;
        //   $('.tip_warp').fadeIn();
        //   setTimeout(function () {
        //     $('.tip_warp').fadeOut();
        //   }, 1500);
        // },
        onTab (item, index) {
          let showItem = this.consistentTimePeriod(item.use_dow, item.use_date, item.use_time)
          if (showItem) {
            this.tabIsActive = index
            this.foodList = item.foodList
          } else {
            // 不满足显示条件
            let txt = this.timeoutMsg
            this.layerDia(txt)
            this.getData() // 重新请求
          }

          console.log(item)
          // this.isShowaPlusSign = this.showaPlusSign(item)
        },
        // 是否显示菜单添加
        showaPlusSign (item) {
          // foodTypeList,foodList 有数据无加号进入选择界面
          // mListList,mTypeList 有数据，有加号进入选择界面
          // 全无 数据直接添加
          if (item.foodTypeList.length > 0 || item.foodList.length > 0) {
            return false
          } else {
            return true
          }
          // return item.code == "AA"
        },
        // 点击添加判断是直接添加还是进入详情界面
        clickOrIn (event, item) {
          if (item.mTypeList.length > 0 || item.mListList.length > 0) {
            this.onfoodInfo(item) //进入界面
          } else {
            this.additem(event, item) //直接添加
          }
        },
        clickShowCart () {
          this.showCartTab = true //显示food弹窗
        },

        // 抛物线
        additem (event, addFoodObj) {
          console.log(addFoodObj, "addFoodObj")
          let newfoodObj = {
            desc1: addFoodObj.desc1,
            desc2: addFoodObj.desc2,
            multi1: addFoodObj.multi1,
            fCode: addFoodObj.fcode,
            k1: addFoodObj.k1,
            kpName: addFoodObj.kpName,
            single: addFoodObj.single,
            qty1: 1, // 商品数量
            upa1: addFoodObj.upa1,
            mapCode: addFoodObj.mapCode,
            seq: addFoodObj.seq,
            fgroup: addFoodObj.fgroup,
            level: addFoodObj.level,
            points: addFoodObj.points,
            t_able: addFoodObj.t_able,
            sc_able: addFoodObj.sc_able,
            discount: addFoodObj.discount,
            newOrderItemFoodList: [],
            newOrderItemMListList: [],
            newOrderItemMTypeList: [],

            newOrderItemFoodTypeList: []
          }
          let { carhasMoneyItem, carnullMoneyItem } = this.cartlimit()
          if (addFoodObj.upa1 != 0) {
            // if (carhasMoneyItem >= this.hasPriceNum) {
            //   console.log('有钱购物车的爆表了');
            //   $('.mongolia2,#dialog,.close_warp').fadeIn();
            //   return;
            // }
          } else {
            if (carnullMoneyItem >= this.nullPriceNum) {
              console.log("没钱的爆表了")
              $(".mongolia2,#dialog,.close_warp").fadeIn()
              return
            }
          }

          let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
          let thereAre = false //储存变量遍历结束判断
          if (shopCartList && shopCartList.length !== 0) {
            // 有数据
            for (let i = 0; i < shopCartList.length; i++) {
              let item = shopCartList[i]
              if (item.fCode == addFoodObj.fcode) {
                thereAre = true
                // 存在相同食品
                if (
                  item.newOrderItemFoodList.length == 0 &&
                  item.newOrderItemMListList.length == 0 &&
                  item.newOrderItemMTypeList.length == 0 &&
                  item.newOrderItemFoodTypeList.length == 0
                ) {
                  item.qty1++
                  break
                } else {
                  // 相同不同细项
                  shopCartList.push(newfoodObj)
                  break
                }
              }
            }

            if (!thereAre) {
              //不存在相同直接+1
              if (addFoodObj.upa1 != 0) {
                // if (carhasMoneyItem >= this.hasPriceNum) {
                //   console.log('有钱的爆表了');
                //   $('.mongolia2,#dialog,.close_warp').fadeIn();
                //   return;
                // }
              } else {
                if (carnullMoneyItem >= this.nullPriceNum) {
                  console.log("没钱的爆表了")
                  $(".mongolia2,#dialog,.close_warp").fadeIn()
                  return
                }
              }
              shopCartList.push(newfoodObj)
            }
          } else {
            // 没有数据直接添加
            shopCartList.push(newfoodObj)
          }
          this.shopCartList = shopCartList
          console.log(shopCartList)
          sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))

          this.addImgLink = addFoodObj.imglink
          this.drop(event.target)
        },
        drop (el) {
          //抛物
          for (let i = 0; i < this.balls.length; i++) {
            let ball = this.balls[i]
            if (!ball.show) {
              ball.show = true
              ball.el = el
              this.dropBalls.push(ball)
              return
            }
          }
        },
        beforeDrop (el) {
          /* 购物车小球动画实现 */
          let count = this.balls.length

          while (count--) {
            let ball = this.balls[count]
            if (ball.show) {
              let rect = ball.el.getBoundingClientRect() //元素相对于视口的位置
              let x = rect.left - 100

              // let x = rect.left - 120;
              // debugger;
              // let y = -(window.innerHeight - rect.top - 80); //获取y
              let y = -(window.innerHeight - rect.top - 80) //获取y
              el.style.display = ""
              el.style.webkitTransform = "translateY(" + y + "px)" //translateY
              el.style.transform = "translateY(" + y + "px)"
              let inner = el.getElementsByClassName("inner-hook")[0]
              inner.style.webkitTransform = "translateX(" + x + "px)"
              inner.style.transform = "translateX(" + x + "px)"
            }
          }
        },
        dropping (el, done) {
          /*重置小球数量  样式重置*/
          let rf = el.offsetHeight
          el.style.webkitTransform = "translate3d(0,0,0)"
          el.style.transform = "translate3d(0,0,0)"
          let inner = el.getElementsByClassName("inner-hook")[0]
          inner.style.webkitTransform = "translate3d(0,0,0)"
          inner.style.transform = "translate3d(0,0,0)"
          el.addEventListener("transitionend", done)
        },
        afterDrop (el) {
          /*初始化小球*/
          let ball = this.dropBalls.shift()
          if (ball) {
            ball.show = false
            el.style.display = "none"
          }
        },
        delCarFood (item, index) {
          let shopCartList = this.shopCartList
          if (item.qty1 > 1) {
            item.qty1--
          } else if (item.qty1 == 1) {
            shopCartList.splice(index, 1)
          }
          sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
          this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))
        },
        addCarFood (item) {
          // console.log('购物车添加');
          console.log(item, "购物车添加")
          let shopCartList = this.shopCartList
          let res = this.numberAllow(item) //判断是否符合,不符合直接return
          if (!res) {
            return
          } else {
            item.qty1++
            sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
            this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))
          }
        },
        // 判断是否符合数量限制
        numberAllow (testItem) {
          let { carhasMoneyItem, carnullMoneyItem } = this.cartlimit()
          let itemPrice = this.calculatedTotal(testItem)
          console.log(itemPrice, "总计")
          if (itemPrice != 0) {
            // if (carhasMoneyItem >= this.hasPriceNum) {
            //   console.log('有钱的爆表了');
            //   $('.mongolia2,#dialog,.close_warp').fadeIn();
            //   return false;
            // }
          } else {
            if (carnullMoneyItem >= this.nullPriceNum) {
              console.log("没钱的爆表了")
              $(".mongolia2,#dialog,.close_warp").fadeIn()
              return false
            }
          }
          return true
        },
        // 商品详情删除
        delfoodInfo () {
          let foodInfoItem = this.foodInfoItem
          console.log("详情减")
          if (foodInfoItem.qty1 > 1) {
            foodInfoItem.qty1--
          } else {
            console.log("不可以再减")
          }
        },
        // 商品详情添加
        addfoodInfo () {
          // console.log('详情页添加');
          let foodInfoItem = this.foodInfoItem
          let foodqty1 = this.foodInfoItem.qty1
          // console.log(allshoplNumber, foodqty1);
          let res = this.foodNumberAllow(foodInfoItem, foodqty1)
          if (!res) {
            return
          } else {
            this.foodInfoItem.qty1++
          }
          // console.log(this.foodInfoItem);
        },
        // 商品详情数量限制
        foodNumberAllow (testItem, foodNum) {
          let { carhasMoneyItem, carnullMoneyItem } = this.cartlimit()
          let itemPrice = this.calculatedTotal(testItem)
          console.log(itemPrice, "商品详情总计")
          if (itemPrice != 0) {
            // if (carhasMoneyItem + foodNum >= this.hasPriceNum) {
            //   console.log('有钱的爆表了');
            //   $('.mongolia2,#dialog,.close_warp').fadeIn();
            //   return false;
            // }
          } else {
            if (carnullMoneyItem + foodNum >= this.nullPriceNum) {
              console.log("没钱的爆表了")
              $(".mongolia2,#dialog,.close_warp").fadeIn()
              return false
            }
          }
          return true
        },
        // 判断购物车是否大于2数量
        cartlimit () {
          let shopCartList = this.shopCartList
          // this.showLittleDIV = false; //重置细项是否显示
          let quantitativeNum = {
            carhasMoneyItem: 0,
            carnullMoneyItem: 0
          }
          shopCartList.forEach(item => {
            // 价钱不等于0
            if (item.upa1 != 0) {
              quantitativeNum.carhasMoneyItem += item.qty1
            } else {
              quantitativeNum.carnullMoneyItem += item.qty1
            }
          })

          return quantitativeNum
        },
        // 食品详情
        onfoodInfo (item) {
          let showItem = this.consistentTimePeriod(item.use_dow, item.use_date, item.use_time)
          if (!showItem) {
            // 不满足显示条件
            let txt = this.timeoutMsg
            this.layerDia(txt)
            return
          }
          this.showFoodWarp = true //显示food弹窗
          console.log(item, "点击的food")
          this.foodInfoItem = {
            ...item,
            qty1: 1
          }
          let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
          // 将购物车的数据和点击商品对象合并
          if (shopCartList.length != 0) {
            shopCartList.forEach(e => {
              if (e.fCode == item.fcode) {
                this.foodInfoItem = {
                  ...this.foodInfoItem,
                  ...e,
                  qty1: 1
                }
              }
            })
          }
          this.toDealWithSome()
          this.toDealWithfoodName()
          console.log(this.foodInfoItem, "处理后this.foodInfoItem")
        },
        onPicker (item, type) {
          $(".mongolia3").fadeIn()
          // 先取消所有选中项
          this.checkVal = item.localListSelect || []
          this.hiddenDialog = true
          this.listSelect = item.listSelect
          this.parentListSelect = item
          this.$nextTick(() => {
            document.getElementById("myxiDialog").scrollTop = 0
          })
          this.myXitype = type
          console.log(item, "点击我的细项", type)
          // mobileSelect1.updateWheels(item.listSelect)
        },
        // foodItem长度超过后滚动显示
        toDealWithfoodName () {
          let foodName = ""
          if (this.openTable.language == "en") {
            foodName = this.foodInfoItem.desc1
          } else {
            foodName = this.foodInfoItem.desc2
          }
          let nameObj = this.textSize("0.45rem", "Arial", foodName)
          if (nameObj.width > 200) {
            this.nameRoll = true
          } else {
            this.nameRoll = false
          }
        },
        // 处理foodInfoItem细项等逻辑
        toDealWithSome () {
          this.localfoodListArry = []
          this.localmlistListArry = []
          this.localmtypeListArry = []

          this.localfoodTypeListArry = []
          let { foodList, mListList } = this.foodInfoItem
          let foodTypeList = this.foodInfoItem.foodTypeList
          let mTypeList = this.foodInfoItem.mTypeList
          // 是否显示
          // console.log(
          //   'foodList',
          //   foodList,
          //   'foodTypeList',
          //   foodTypeList,

          //   'mListList',
          //   mListList,
          //   'mTypeList',
          //   mTypeList
          // );
          let foodListLen = foodList.length
          let foodTypeListLen = foodTypeList.length

          let mlistListLen = mListList.length
          let mtypeListLen = mTypeList.length

          if (
            foodListLen != 0 ||
            foodTypeListLen != 0 ||
            mlistListLen != 0 ||
            mtypeListLen != 0
          ) {
            console.log("细项中有不为零")
            this.showLittleDIV = true
            if (foodListLen != 0) {
              this.fListSort(foodList) //固定细项foodlist排序
              foodList.forEach(item => {
                this.localfoodListArry.push({
                  desc1: item.desc1,
                  desc2: item.desc2,
                  multi1: item.multi1,
                  fCode: item.fcode,
                  k1: item.k1,
                  kpName: item.kpName,
                  single: item.single,
                  qty1: item.qty1,
                  upa1: item.upa1,
                  newOrderItemMListList: [], //我的细项
                  newOrderItemMTypeList: [] //我的细项
                })
                // 我的细项
                Vue.set(item, "listSelect", [])
                Vue.set(item, "localListSelect", [])
                let foodMlist = item.mListList || [] //foodList=>每个对象的mlistList
                if (foodMlist.length != 0) {
                  item.listSelect.push(...foodMlist) //把foodList的我的细项加入选项
                  foodMlist.forEach(i => {
                    //带上标识
                    i.myxiType = "mlis"
                  })
                }
                let foodMty = item.mTypeList || [] //foodList=>每个对象的mtypeList
                if (foodMty.length != 0) {
                  foodMty.forEach(myXi => {
                    item.listSelect.push(...myXi.mListList) //把foodList的我的细项加入选项
                    myXi.mListList.forEach(i => {
                      //带上标识
                      i.myxiType = "mty"
                      i.dadCode = myXi.code
                    })
                  })
                }
              })
            }

            if (mlistListLen != 0) {
              this.mListSort(mListList) //固定细项foodlist排序
              mListList.forEach(item => {
                this.localmlistListArry.push({
                  code: item.code,
                  k1: item.k1,
                  kpName: item.kpName,
                  single: item.single,
                  name: item.name,
                  name2: item.name2,
                  multi1: item.multi1,
                  newOrderItemMListList: [],
                  newOrderItemMTypeList: []
                })
                // 我的细项
                Vue.set(item, "listSelect", [])
                Vue.set(item, "localListSelect", [])
                let mMlist = item.mListList || [] //foodList=>每个对象的mlistList
                if (mMlist.length != 0) {
                  item.listSelect.push(...mMlist) //把foodList的我的细项加入选项
                  mMlist.forEach(i => {
                    //带上标识
                    i.myxiType = "mlis"
                  })
                }
                let mMty = item.mTypeList || [] //foodList=>每个对象的mtypeList
                if (mMty.length != 0) {
                  mMty.forEach(myXi => {
                    item.listSelect.push(...myXi.mListList) //把foodList的我的细项加入选项
                    myXi.mListList.forEach(i => {
                      //带上标识
                      i.myxiType = "mty"
                      i.dadCode = myXi.code
                    })
                  })
                }
              })
            }

            // foodTypeList = []
            // foodTypeList.push({
            //   code: 'AD',
            //   color: null,
            //   foodList: [{
            //       desc1: 'kele',
            //       desc2: '繁体可乐',
            //       fcode: 'AL03',
            //       multi1: '简体可乐',
            //       k1: null,
            //       kpName: null,
            //       qty1: null,
            //       upa1: null,
            //       mListList: [{
            //         code: "XB01",
            //         name2: '冰mlistList',
            //       }, {
            //         code: "XB02",
            //         name2: 'mListList',
            //       }],
            //       mTypeList: [{
            //           code: "XC",
            //           mListList: [{
            //             code: "XC01",
            //             name2: '冰'
            //           }, {
            //             code: "XC02",
            //             name2: '去冰'
            //           }, {
            //             code: "XC03",
            //             name2: '加冰'
            //           }]
            //         },
            //         {
            //           code: "XA",
            //           mListList: [{
            //             code: "XC04",
            //             name2: '你'
            //           }, {
            //             code: "XC05",
            //             name2: '我'
            //           }, {
            //             code: "XC06",
            //             name2: '他'
            //           }]
            //         },
            //       ]
            //     },
            //     {
            //       desc1: 'xuebi',
            //       desc2: '繁体雪碧',
            //       multi1: '简体雪碧',
            //       fcode: 'AL04',
            //       k1: null,
            //       kpName: null,
            //       qty1: null,
            //       upa1: 100,
            //       mListList: [{
            //         code: "XB01",
            //         name2: '雪碧mlistList',
            //       }, {
            //         code: "XB02",
            //         name2: '雪碧2mlistList',
            //       }],
            //       mTypeList: [{
            //           code: "XC",
            //           mListList: [{
            //             code: "XC01",
            //             name2: '冰雪碧'
            //           }, {
            //             code: "XC02",
            //             name2: '去冰雪碧'
            //           }, {
            //             code: "XC03",
            //             name2: '加冰雪碧'
            //           }]
            //         },
            //         {
            //           code: "XA",
            //           mListList: [{
            //             code: "XC04",
            //             name2: '你雪碧'
            //           }, {
            //             code: "XC05",
            //             name2: '我雪碧'
            //           }, {
            //             code: "XC06",
            //             name2: '他雪碧'
            //           }]
            //         },
            //       ]
            //     },
            //     {
            //       desc1: 'ning',
            //       desc2: '繁体柠檬茶',
            //       multi1: '简体柠檬茶',
            //       fcode: 'nmc',
            //       k1: null,
            //       kpName: null,
            //       qty1: null,
            //       upa1: 20,
            //       mListList: [],
            //       mTypeList: []
            //     },
            //   ],
            //   fType_nameA: 'en饮料优先',
            //   fType_nameB: 'zh饮料优先',
            //   multi1: 'gbk饮料优先',
            //   icon: null,
            //   invLite: null,
            //   maxQty: 1,
            //   md_ipp: null,
            //   md_uName: null,
            //   minQty: 1,
            //   mybatisUpdateStatusMap: null,
            //   name: 'yinliao',
            //   name2: '饮料',
            // }, {
            //   code: 'ALO',
            //   color: null,
            //   foodList: [{
            //       desc1: 'kele2',
            //       desc2: '繁体可乐2',
            //       fcode: 'AL032',
            //       multi1: '简体可乐2',
            //       k1: null,
            //       kpName: null,
            //       qty1: null,
            //       upa1: 30,
            //       mListList: [{
            //         code: "XB89",
            //         name2: '可乐mlistList',
            //       }, {
            //         code: "XB02",
            //         name2: '可乐2mlistList',
            //       }],
            //       mTypeList: [{
            //           code: "X78",
            //           mListList: [{
            //             code: "XC01",
            //             name2: '冰可乐'
            //           }, {
            //             code: "XC02",
            //             name2: '去冰可乐'
            //           }, {
            //             code: "XC03",
            //             name2: '加冰可乐'
            //           }]
            //         },
            //         {
            //           code: "XA",
            //           mListList: [{
            //             code: "XC04",
            //             name2: '你可乐'
            //           }, {
            //             code: "XC05",
            //             name2: '我可乐'
            //           }, {
            //             code: "XC06",
            //             name2: '他可乐'
            //           }]
            //         },
            //       ]
            //     },
            //     {
            //       desc1: 'xuebi2',
            //       desc2: '繁体雪碧2',
            //       multi1: '简体雪碧2',
            //       fcode: 'AL042',
            //       k1: null,
            //       kpName: null,
            //       qty1: null,
            //       upa1: 0,
            //     }

            //   ],
            //   fType_nameA: 'en饮料优先2',
            //   fType_nameB: 'zh饮料优先2',
            //   multi1: 'gbk饮料优先2',
            //   icon: null,
            //   invLite: null,
            //   maxQty: 1,
            //   md_ipp: null,
            //   md_uName: null,
            //   minQty: 1,
            //   mybatisUpdateStatusMap: null,
            //   name: 'yinliao2',
            //   name2: '饮料2',
            // });
            this.foodInfoItem.foodTypeList = foodTypeList.filter((item, index) => {
              return item.foodList.length != 0
            })
            foodTypeList.forEach(item => {
              if (item.foodList && item.foodList.length != 0) {
                this.fListSort(item.foodList) //fty=>foodlist排序
                item.foodList.forEach(inItem => {
                  Vue.set(inItem, "selected", false) //细项点击
                  // 我的细项
                  Vue.set(inItem, "listSelect", [])
                  Vue.set(inItem, "localListSelect", [])
                  let ftyMlist = inItem.mListList || [] //foodTypeList=>foodList=>mListList
                  if (ftyMlist.length != 0) {
                    inItem.listSelect.push(...ftyMlist)
                    ftyMlist.forEach(i => {
                      //带上标识
                      i.myxiType = "mlis"
                    })
                  }
                  let ftyInfoInMty = inItem.mTypeList || [] //foodTypeList=>foodList=>mTypeList
                  if (ftyInfoInMty.length != 0) {
                    ftyInfoInMty.forEach(myXi => {
                      inItem.listSelect.push(...myXi.mListList) //把foodList的我的细项加入选项
                      myXi.mListList.forEach(i => {
                        //带上标识
                        i.myxiType = "mty"
                        i.dadCode = myXi.code
                      })
                    })
                  }
                })
              }
            })

            // mTypeList = []
            // mTypeList.push({
            //     code: 'oo',
            //     color: null,
            //     desc: 'mtypeList1',
            //     desc2: '繁体mtypeList1',
            //     desc3: '简体mtypeList1',
            //     gindex: null,
            //     icon: null,
            //     k1: null,
            //     maxQty: null,
            //     minQty: 1,
            //     mListList: [{
            //         code: 'uii',
            //         k1: null,
            //         kpName: 'uii',
            //         name: 'uii',
            //         name2: '繁体uu',
            //         name3: '简体uu',
            //         upa1: 20,
            //         mTypeList: [{
            //           code: "XC",
            //           mListList: [{
            //             code: "XC01",
            //             name2: "少葱"
            //           }, {
            //             code: "XC02",
            //             name2: "多葱"
            //           }, {
            //             code: "XC05",
            //             name2: '我雪碧'
            //           }, {
            //             code: "XC06",
            //             name2: '他雪碧'
            //           }]
            //         }, {
            //           code: "XW",
            //           mListList: [{
            //             code: "XW01",
            //             name2: "XW少葱"
            //           }, {
            //             code: "XW02",
            //             name2: "XW多葱"
            //           }, {
            //             code: "XW05",
            //             name2: 'XW我雪碧'
            //           }, {
            //             code: "XW06",
            //             name2: 'XW他雪碧'
            //           }]
            //         }]
            //       },
            //       {
            //         code: 'uio',
            //         k1: null,
            //         kpName: 'uio',
            //         name: 'uio',
            //         name2: '繁体uo',
            //         name3: '简体uo',
            //         upa1: 5,
            //       },
            //       {
            //         code: 'uiy',
            //         k1: null,
            //         kpName: 'uiy',
            //         name: 'uiy',
            //         name2: '繁体uy',
            //         name3: '简体uy',
            //         upa1: 5,
            //       },
            //       {
            //         code: 'ooo',
            //         k1: null,
            //         kpName: 'ooo',
            //         name: 'ooo',
            //         name2: '繁体u它88',
            //         name3: '简体u它88',
            //         upa1: 0,
            //       },
            //     ],
            //     mybatisUpdateStatusMap: null,
            //     sort: null,
            //   },
            //   //  {
            //   //   code: 'YY',
            //   //   color: null,
            //   //   desc: 'mtypeList2',
            //   //   desc2: '繁体mtypeList2',
            //   //   desc3: '简体mtypeList2',
            //   //   gindex: null,
            //   //   icon: null,
            //   //   k1: null,
            //   //   maxQty: 1,
            //   //   minQty: 1,
            //   //   mListList: [{
            //   //       code: 'uii',
            //   //       k1: null,
            //   //       kpName: 'uii',
            //   //       name: 'uii',
            //   //       name2: '繁体uii',
            //   //       name3: '简体uii',
            //   //       upa1: null,
            //   //     },
            //   //     {
            //   //       code: 'uio',
            //   //       k1: null,
            //   //       kpName: 'uio',
            //   //       name: 'uio',
            //   //       name2: '繁体uio',
            //   //       name3: '简体uio',
            //   //       upa1: null,
            //   //     },
            //   //     {
            //   //       code: 'uiy',
            //   //       k1: null,
            //   //       kpName: 'uiy',
            //   //       name: 'uiy',
            //   //       name2: '繁体uiy',
            //   //       name3: '简体uiy',
            //   //       upa1: null,
            //   //     },
            //   //     {
            //   //       code: 'ooo',
            //   //       k1: null,
            //   //       kpName: 'ooo',
            //   //       name: 'ooo',
            //   //       name2: '繁体ooo',
            //   //       name3: '简体ooo',
            //   //       upa1: null,
            //   //     },
            //   //   ],
            //   //   mybatisUpdateStatusMap: null,
            //   //   sort: null,
            //   // }
            // );
            this.foodInfoItem.mTypeList = mTypeList.filter((item, index) => {
              return item.mListList.length != 0
            })
            mTypeList.forEach((item, index) => {
              if (item.mListList && item.mListList.length != 0) {
                this.mListSort(item.mListList) //mty=>mlistp排序
                item.mListList.forEach(inItem => {
                  Vue.set(inItem, "selected", false)
                  // 我的细项
                  Vue.set(inItem, "listSelect", [])
                  Vue.set(inItem, "localListSelect", [])
                  let mtyMlist = inItem.mListList || []
                  if (mtyMlist.length != 0) {
                    inItem.listSelect.push(...mtyMlist)
                    mtyMlist.forEach(i => {
                      //带上标识
                      i.myxiType = "mlis"
                    })
                  }
                  let mtyInMlisInMty = inItem.mTypeList || []
                  if (mtyInMlisInMty.length != 0) {
                    mtyInMlisInMty.forEach(myXi => {
                      inItem.listSelect.push(...myXi.mListList)
                      myXi.mListList.forEach(i => {
                        //带上标识
                        i.myxiType = "mty"
                        i.dadCode = myXi.code
                      })
                    })
                  }
                })
              }
            })
            console.log(
              "foodList",
              foodList,
              "foodTypeList",
              foodTypeList,

              "mListList",
              mListList,
              "mTypeList",
              mTypeList
            )
            // console.log(mTypeList, '00');
          } else {
            this.showLittleDIV = false
          }
        },

        // 判断数组是否相同(foodList,foodTypeList,)
        sameArry (a, b) {
          if (a.length !== b.length) {
            // 判断数组的长度
            return false
          } else {
            // 循环遍历数组的值进行比较
            for (let i = 0; i < a.length; i++) {
              if (a[i] !== b[i]) {
                return false
              }
            }
            return true
          }
        },

        joinCartF () {
          if (this.belowFoodTypeNum()) return //遍历所有套餐项是否小于最小数量
          if (this.belowMtypeNum()) return //遍历所有套餐项是否小于最小数量
          let allshoplNumber = this.allshoplNumber
          let foodqty1 = this.foodInfoItem.qty1
          let foodInfoItem = this.foodInfoItem
          let localfoodListArry = this.localfoodListArry
          let localmlistListArry = this.localmlistListArry
          let localmtypeListArry = this.localmtypeListArry

          let localfoodTypeListArry = this.localfoodTypeListArry
          let thereAre = false //储存变量遍历结束判断code
          let ISselect = false //储存变量遍历结束判断selectedItem
          let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
          // let thereAre = false; //储存变量遍历结束判断code
          // var ISselect = false; //储存变量遍历结束判断selectedItem
          console.log(localfoodTypeListArry, localmtypeListArry, "加入购物车")

          let { carhasMoneyItem, carnullMoneyItem } = this.cartlimit()
          let itemPrice = this.calculatedTotal(foodInfoItem)
          console.log(itemPrice, "商品详情总计")
          if (itemPrice != 0) {
            // if (carhasMoneyItem + foodqty1 > this.hasPriceNum) {
            //   console.log('有钱的爆表了');
            //   $('.mongolia2,#dialog,.close_warp').fadeIn();
            //   return;
            // }
          } else {
            if (carnullMoneyItem + foodqty1 > this.nullPriceNum) {
              console.log("没钱的爆表了")
              $(".mongolia2,#dialog,.close_warp").fadeIn()
              return
            }
          }

          if (shopCartList && shopCartList.length !== 0) {
            // 有数据
            for (let i = 0; i < shopCartList.length; i++) {
              let sameFood = shopCartList[i]
              if (sameFood.fCode == foodInfoItem.fcode) {
                thereAre = true
                //  购物车单个food
                let cartFcodeArry = this.sortCartCode(sameFood)
                //  本地单个food
                let localFcodeArry = this.sortlocalCode(sameFood)
                let isEqual = this.sameArry(localFcodeArry, cartFcodeArry)
                if (isEqual) {
                  // 判断是否相同细项，相同合并
                  ISselect = true
                  sameFood.qty1 += foodqty1
                  console.log("全部相同合并")
                  break
                } else {
                  console.log("相同code，不同细项")
                }
              }
            }

            if (!thereAre || !ISselect) {
              console.log("未知逻辑")
              let newfoodObj = {
                desc1: foodInfoItem.desc1,
                desc2: foodInfoItem.desc2,
                multi1: foodInfoItem.multi1,
                fCode: foodInfoItem.fcode,
                k1: foodInfoItem.k1,
                kpName: foodInfoItem.kpName,
                single: foodInfoItem.single,
                upa1: foodInfoItem.upa1,
                newOrderItemFoodList: localfoodListArry,
                newOrderItemMListList: localmlistListArry,
                newOrderItemMTypeList: localmtypeListArry,

                newOrderItemFoodTypeList: localfoodTypeListArry,
                qty1: foodInfoItem.qty1
              }
              shopCartList.push(newfoodObj)
            }
          } else {
            // 没有数据直接添加
            console.log("wu")
            let newfoodObj = {
              desc1: foodInfoItem.desc1,
              desc2: foodInfoItem.desc2,
              multi1: foodInfoItem.multi1,
              fCode: foodInfoItem.fcode,
              k1: foodInfoItem.k1,
              kpName: foodInfoItem.kpName,
              single: foodInfoItem.single,
              upa1: foodInfoItem.upa1,
              newOrderItemFoodList: localfoodListArry,
              newOrderItemMListList: localmlistListArry,
              newOrderItemMTypeList: localmtypeListArry,

              newOrderItemFoodTypeList: localfoodTypeListArry,
              qty1: foodInfoItem.qty1
            }
            shopCartList.push(newfoodObj)
          }
          sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
          this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))

          // console.log(this.shopCartList);
          this.showFoodWarp = false
          this.resetSelecte()
          // $('.food_info_warp').fadeOut();
        },
        // 我的细项选择
        sortCartCode (oneFood) {
          let allCodeArry = []
          let foodSetArry = [...oneFood.newOrderItemFoodList]

          if (foodSetArry.length != 0) {
            foodSetArry.forEach(item => {
              let thisItemxiCode = this.onCmyXi(item)
              allCodeArry.push(item.fCode, ...thisItemxiCode)
            })
          }
          if (oneFood.newOrderItemFoodTypeList.length != 0) {
            oneFood.newOrderItemFoodTypeList.forEach(item => {
              allCodeArry.push(item.code)
              if (item.newOrderItemFoodList && item.newOrderItemFoodList.length != 0) {
                item.newOrderItemFoodList.forEach(i => {
                  let thisItemxiCode = this.onCmyXi(i)
                  allCodeArry.push(i.fCode, ...thisItemxiCode)
                })
              }
            })
          }
          if (oneFood.newOrderItemMListList.length != 0) {
            oneFood.newOrderItemMListList.forEach(item => {
              let thisItemxiCode = this.onCmyXi(item)
              allCodeArry.push(item.code, ...thisItemxiCode)
            })
          }

          if (oneFood.newOrderItemMTypeList.length != 0) {
            oneFood.newOrderItemMTypeList.forEach(item => {
              allCodeArry.push(item.code)
              if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                item.newOrderItemMListList.forEach(i => {
                  let thisItemxiCode = this.onCmyXi(i)
                  allCodeArry.push(i.code, ...thisItemxiCode)
                })
              }
            })
          }
          console.log(allCodeArry, "购物车的code")
          allCodeArry.sort((x, y) => x > y)
          return allCodeArry
        },
        sortlocalCode () {
          let allCodeArry = []
          let foodSetArry = [...this.localfoodListArry]
          if (foodSetArry.length != 0) {
            foodSetArry.forEach(item => {
              let thisItemxiCode = this.onCmyXi(item)
              allCodeArry.push(item.fCode, ...thisItemxiCode)
            })
          }
          if (this.localfoodTypeListArry.length != 0) {
            this.localfoodTypeListArry.forEach(item => {
              allCodeArry.push(item.code)
              if (item.newOrderItemFoodList && item.newOrderItemFoodList.length != 0) {
                item.newOrderItemFoodList.forEach(i => {
                  let thisItemxiCode = this.onCmyXi(i)
                  allCodeArry.push(i.fCode, ...thisItemxiCode)
                })
              }
            })
          }
          if (this.localmlistListArry.length != 0) {
            this.localmlistListArry.forEach(item => {
              let thisItemxiCode = this.onCmyXi(item)
              allCodeArry.push(item.code, ...thisItemxiCode)
            })
          }

          if (this.localmtypeListArry.length != 0) {
            this.localmtypeListArry.forEach(item => {
              allCodeArry.push(item.code)
              if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                item.newOrderItemMListList.forEach(i => {
                  let thisItemxiCode = this.onCmyXi(i)
                  allCodeArry.push(i.code, ...thisItemxiCode)
                })
              }
            })
          }
          console.log(allCodeArry, "本地的code")
          allCodeArry.sort((x, y) => x > y)
          return allCodeArry
        },
        // 我的细项code遍历
        onCmyXi (everyItem) {
          let evmyxiCode = []
          if (everyItem.newOrderItemMListList && everyItem.newOrderItemMListList.length != 0) {
            everyItem.newOrderItemMListList.forEach(item => {
              evmyxiCode.push(item.code)
            })
          }
          if (everyItem.newOrderItemMTypeList && everyItem.newOrderItemMTypeList.length != 0) {
            everyItem.newOrderItemMTypeList.forEach(item => {
              evmyxiCode.push(item.code)
              if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                item.newOrderItemMListList.forEach(i => {
                  evmyxiCode.push(i.code)
                })
              }
            })
          }
          return evmyxiCode
        },
        // 多选
        multiselect (item, type, vItem) {
          if (item.selected) return //禁止自己点自己
          let foodtypeInList = this.localfoodTypeListArry //细项foodTypeList.foodList
          let mtypeInList = this.localmtypeListArry //细项foodTypeList.foodList
          console.log(type)
          switch (type) {
            case "foodtypeInList":
              this.radioXi(foodtypeInList, vItem, type)
              this.localfoodTypeListArry = this.mergeFtypeList(foodtypeInList, item, vItem)
              break
            case "mtypeInList":
              this.radioXi(mtypeInList, vItem, type)
              this.localmtypeListArry = this.mergeMtypeList(mtypeInList, item, vItem)
              break
          }
          Vue.set(item, "selected", true)
          mtypeInList = mtypeInList.filter(item => {
            return item.newOrderItemMListList.length != 0
          })
          foodtypeInList = foodtypeInList.filter(item => {
            return item.newOrderItemFoodList.length != 0
          })
          console.log(
            // this.localfoodListArry,
            // this.localmListListArry,

            this.localfoodTypeListArry,
            this.localmtypeListArry,
            "本地储存的点击细项数组"
          )
        },
        getNewOrderItemFoodList (list, item, vItem) {
          const obj = {
            code: vItem.code,
            newOrderItemFoodList: [
              {
                desc1: item.desc1,
                desc2: item.desc2,
                multi1: item.multi1,
                fCode: item.fcode,
                k1: item.k1,
                kpName: item.kpName,
                qty1: item.qty1 || 1,
                upa1: item.upa1,
                single: item.single,
                mapCode: item.mapCode,
                seq: item.seq,
                fgroup: item.fgroup,
                level: item.level,
                points: item.points,
                t_able: item.t_able,
                sc_able: item.sc_able,
                discount: item.discount,
                newOrderItemMListList: [],
                newOrderItemMTypeList: []
              }
            ]
          }
          list.push(obj)
          return list
        },
        mergeFtypeList (list, item, vItem) {
          if (list.length != 0) {
            for (let i = 0; i < list.length; i++) {
              if (list[i].code == vItem.code) {
                const orderItem = list[i].newOrderItemFoodList
                orderItem.push({
                  desc1: item.desc1,
                  desc2: item.desc2,
                  multi1: item.multi1,
                  fCode: item.fcode,
                  k1: item.k1,
                  kpName: item.kpName,
                  qty1: item.qty1 || 1,
                  upa1: item.upa1,
                  single: item.single,
                  mapCode: item.mapCode,
                  seq: item.seq,
                  t_able: item.t_able,
                  sc_able: item.sc_able,
                  discount: item.discount,
                  newOrderItemMListList: [],
                  newOrderItemMTypeList: []
                })
                return list
                break
              }
            }
            return this.getNewOrderItemFoodList(list, item, vItem)
          }
          return this.getNewOrderItemFoodList(list, item, vItem)
        },
        mergeMtypeList (list, item, vItem) {
          if (list.length != 0) {
            for (let i = 0; i < list.length; i++) {
              if (list[i].code == vItem.code) {
                list[i].newOrderItemMListList.push({
                  code: item.code,
                  k1: item.k1,
                  kpName: item.kpName,
                  name: item.name,
                  name2: item.name2,
                  name3: item.name3,
                  qty1: item.qty1 || 1,
                  single: item.single,
                  mapCode: item.mapCode,
                  seq: item.seq,
                  fgroup: item.fgroup,
                  level: item.level,
                  points: item.points,
                  discType: item.discType,
                  multi1: item.multi1,
                  newOrderItemMListList: [],
                  newOrderItemMTypeList: [],
                  price: item.price
                })
                return list
                break
              }
            }
            const obj = {
              code: vItem.code,
              newOrderItemMListList: [
                {
                  code: item.code,
                  k1: item.k1,
                  kpName: item.kpName,
                  name: item.name,
                  name2: item.name2,
                  name3: item.name3,
                  qty1: item.qty1 || 1,
                  single: item.single,
                  mapCode: item.mapCode,
                  seq: item.seq,
                  fgroup: item.fgroup,
                  level: item.level,
                  points: item.points,
                  discType: item.discType,
                  multi1: item.multi1,
                  newOrderItemMListList: [],
                  newOrderItemMTypeList: [],
                  price: item.price
                }
              ]
            }
            list.push(obj)
            return list
          }
          const obj = {
            code: vItem.code,
            newOrderItemMListList: [
              {
                code: item.code,
                k1: item.k1,
                kpName: item.kpName,
                name: item.name,
                name2: item.name2,
                name3: item.name3,
                qty1: item.qty1 || 1,
                single: item.single,
                mapCode: item.mapCode,
                seq: item.seq,
                fgroup: item.fgroup,
                level: item.level,
                points: item.points,
                discType: item.discType,
                multi1: item.multi1,
                newOrderItemMListList: [],
                newOrderItemMTypeList: [],
                price: item.price
              }
            ]
          }
          list.push(obj)

          return list
        },
        // foodType，mty套餐选择单选
        radioXi (inList, vItem, type) {
          let max = vItem.maxQty || 50
          // let max = 2;
          let selectednNum = 0
          let vItemList
          if (type == "foodtypeInList") {
            vItemList = vItem.foodList
          } else {
            vItemList = vItem.mListList
          }
          console.log(vItem, "vItem")
          vItemList.forEach(e => {
            if (e.selected) {
              selectednNum += 1
            }
          })
          if (selectednNum == +max) {
            var Index = (inList || []).findIndex(inList => inList.code == vItem.code)
            let arry, firstItemCode, codeTxt
            if (type == "foodtypeInList") {
              arry = inList[Index].newOrderItemFoodList
              firstItemCode = arry[0].fCode
              codeTxt = "fcode"
            } else {
              arry = inList[Index].newOrderItemMListList
              firstItemCode = arry[0].code
              codeTxt = "code"
            }
            vItemList.forEach(e => {
              if (e[codeTxt] == firstItemCode) {
                this.$set(e, "selected", false)
                this.$set(e, "localListSelect", []) //清除我的细项数据
              }
            })
            arry.splice(0, 1)
          }
        },
        // 套餐选中数量限制（低于数量逻辑）
        belowFoodTypeNum () {
          if (this.foodInfoItem.foodTypeList) {
            let belowTypeList = this.foodInfoItem.foodTypeList
            let selectednNum = 0
            for (let i = 0; i < belowTypeList.length; i++) {
              let item = belowTypeList[i]
              let min = item.minQty
              // let min = 2;
              selectednNum = 0
              item.foodList.forEach(e => {
                if (e.selected) {
                  selectednNum += 1
                }
              })
              if (selectednNum < min) {
                let txt = this.systemLanguage.minNumText
                this.layerDia(txt)
                return true
                break
              } else {
                console.log("满足最小数量限制")
              }
            }
          } else {
            console.log("没有foodTypeList")
            return false
          }
        },
        belowMtypeNum () {
          if (this.foodInfoItem.mTypeList) {
            let belowTypeList = this.foodInfoItem.mTypeList
            let selectednNum = 0
            for (let i = 0; i < belowTypeList.length; i++) {
              let item = belowTypeList[i]
              let min = item.minQty
              // let min = 2;
              selectednNum = 0
              item.mListList.forEach(e => {
                if (e.selected) {
                  selectednNum += 1
                }
              })
              if (selectednNum < min) {
                let txt = this.systemLanguage.minNumText
                this.layerDia(txt)
                return true
                break
              } else {
                console.log("满足最小数量限制")
              }
            }
          } else {
            console.log("没有mtypeList")
            return false
          }
        },
        // 关闭蒙面层
        closeBg () {
          $(".mongolia2,#dialog,.close_warp").fadeOut()
        },
        // 语言切换逻辑
        onLangSwitcher () {
          let { language } = this.openTable

          if (language == "en") {
            // Vue.set(this.openTable, 'language', 'zh');
            // this.openTable.language = 'zh';
            this.openTable = {
              ...this.openTable,
              language: "zh"
            }
          } else {
            this.openTable = {
              ...this.openTable,
              language: "en"
            }

            // Vue.set(this.openTable, 'language', 'en');

            // this.openTable.language = 'en';
          }
          this.fixLan()
        },
        // 新版切换语言
        onLanSelect (value) {
          switch (value) {
            case "en":
              this.openTable = {
                ...this.openTable,
                language: "en"
              }
              break
            case "zh":
              this.openTable = {
                ...this.openTable,
                language: "zh"
              }
              break
            case "gbk":
              this.openTable = {
                ...this.openTable,
                language: "gbk"
              }
              break
          }
          this.fixLan()
        },
        settlement () {
          this.tipTxt = this.systemLanguage.clearText
          let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
          if (shopCartList.length == 0) {
            let txt = this.systemLanguage.clearText
            this.layerDia(txt)
          } else {
            // window.location.href =
            //   '../orderDetailIndex?param=' +
            //   encodeURIComponent(JSON.stringify(shopCartList)) +
            //   '&openTable=' +
            //   encodeURIComponent(JSON.stringify(this.openTable));
            // 跳转订单页
            window.location.href =
              "../order/orderDetailIndex?openTable=" +
              encodeURIComponent(JSON.stringify(this.openTable)) //
          }
        },
        // 计算文字宽度决定是否滚动
        textSize (fontSize, fontFamily, text) {
          var span = document.createElement("span")
          var result = {}
          result.width = span.offsetWidth
          result.height = span.offsetHeight
          span.style.visibility = "hidden"
          span.style.fontSize = fontSize
          span.style.fontFamily = fontFamily
          span.style.display = "inline-block"
          document.body.appendChild(span)
          if (typeof span.textContent != "undefined") {
            span.textContent = text
          } else {
            span.innerText = text
          }
          result.width = parseFloat(window.getComputedStyle(span).width) - result.width
          result.height = parseFloat(window.getComputedStyle(span).height) - result.height
          return result
        },
        blackbtn () {
          // window.scrollTo(0, 1);
          this.showFoodWarp = false
          this.resetSelecte()
          // $('.food_info_warp').fadeOut();
        },
        // 重置选中
        resetSelecte () {
          let {
            foodList,
            foodTypeList,

            mListList,
            mTypeList
          } = this.foodInfoItem

          foodList.forEach(item => {
            item.selected = false
          })
          foodTypeList.forEach(item => {
            item.selected = false
          })

          mListList.forEach(item => {
            item.selected = false
          })
          mTypeList.forEach(item => {
            item.selected = false
          })
        },
        // 购物车细项文字逻辑
        showlogic (
          newOrderItemFoodList,
          newOrderItemMListList,
          newOrderItemMTypeList,

          newOrderItemFoodTypeList
        ) {
          let arry = [
            ...newOrderItemFoodList,
            ...newOrderItemMListList

            // ...newOrderItemMTypeList,
            // ...newOrderItemFoodTypeList,
          ]
          // console.log(newOrderItemFoodTypeList, newOrderItemMTypeList, '购物车细项处理逻辑');
          if (newOrderItemFoodTypeList.length != 0) {
            newOrderItemFoodTypeList.forEach(e => {
              e.newOrderItemFoodList.forEach(i => {
                arry.push(i)
              })
            })
          }
          if (newOrderItemMTypeList.length != 0) {
            newOrderItemMTypeList.forEach(e => {
              e.newOrderItemMListList.forEach(i => {
                arry.push(i)
              })
            })
          }

          let arryText = []
          arry.forEach(item => {
            let price = item.upa1 && item.upa1 != 0 ? `($${item.upa1})` : ""
            if (this.openTable.language == "en") {
              if (item.name) {
                arryText.push(item.name + this.cartMyxiText(item, "en") + price)
              } else if (item.desc) {
                arryText.push(item.desc + this.cartMyxiText(item, "en") + price)
              } else if (item.desc1) {
                arryText.push(item.desc1 + this.cartMyxiText(item, "en") + price)
              }
            } else if (this.openTable.language == "zh") {
              if (item.name2) {
                arryText.push(item.name2 + this.cartMyxiText(item, "zh") + price)
              } else if (item.desc2) {
                arryText.push(item.desc2 + this.cartMyxiText(item, "zh") + price)
              }
            } else {
              if (item.multi1) {
                arryText.push(item.multi1 + this.cartMyxiText(item, "gbk") + price)
              }
            }
          })
          let str = ""
          arryText.forEach((item, i) => {
            str += arryText[i] + "" + "+" + ""
          })
          if (str.length > 0) {
            str = str.substr(0, str.length - 1)
          }
          return str
        },
        cartMyxiText (item, type) {
          let myxiTxt = []
          if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
            item.newOrderItemMListList.forEach(i => {
              if (type == "en") {
                myxiTxt.push(i.name)
              } else if (type == "zh") {
                myxiTxt.push(i.name2)
              } else {
                myxiTxt.push(i.multi1)
              }
            })
          }
          if (item.newOrderItemMTypeList && item.newOrderItemMTypeList.length != 0) {
            item.newOrderItemMTypeList.forEach(i => {
              i.newOrderItemMListList.forEach(e => {
                if (type == "en") {
                  myxiTxt.push(e.name)
                } else if (type == "zh") {
                  myxiTxt.push(e.name2)
                } else {
                  myxiTxt.push(e.multi1)
                }
              })
            })
          }
          let str = ""
          myxiTxt.forEach((item, i) => {
            str += myxiTxt[i] + "" + "+" + ""
          })
          if (str.length > 0) {
            str = str.substr(0, str.length - 1)
            return `[${str}]`
          } else {
            return ""
          }
        },
        // 计算总价格（包含细项）
        calculatedTotal (cartItem) {
          let basePrice = cartItem.upa1
          let arry = []
          if (cartItem.newOrderItemFoodList.length != 0) {
            cartItem.newOrderItemFoodList.forEach(e => {
              arry.push(e)
            })
          }
          if (cartItem.newOrderItemMListList.length != 0) {
            cartItem.newOrderItemMListList.forEach(e => {
              e.upa1 = e.price // mlist价格为price字段
              arry.push(e)
            })
          }

          if (cartItem.newOrderItemFoodTypeList.length != 0) {
            cartItem.newOrderItemFoodTypeList.forEach(e => {
              e.newOrderItemFoodList.forEach(i => {
                arry.push(i)
              })
            })
          }
          if (cartItem.newOrderItemMTypeList.length != 0) {
            cartItem.newOrderItemMTypeList.forEach(e => {
              e.newOrderItemMListList.forEach(i => {
                i.upa1 = i.price // mlist价格为price字段
                arry.push(i)
              })
            })
          }
          arry.forEach(priceItem => {
            if (priceItem.upa1 && priceItem.upa1 != 0) {
              basePrice += priceItem.upa1
            }
          })
          return basePrice * cartItem.qty1
        },
        // 计时器
        timer () {
          let millisecond = 0 //时间默认值
          setInterval(function () {
            //1000 每一秒計算一次
            millisecond += 1000
            this.menuTimer = millisecond
          }, 1000)
        },
        // 是否符合时间
        consistentTimePeriod (use_dow, use_date, use_time) {
          // let timeStamp = 1614312918000;
          let timeStamp = moment(this.openTable.date).valueOf()
          // let timeStamp = this.openTable.timestamp;
          console.log(timeStamp, "timeStamp时间戳")
          //后台返回时间+启动计时器时间
          let currentTime = this.timeFormatt(timeStamp + this.menuTimer)
          // result ==true直接显示
          let result = false
          if (
            (use_dow == null || use_dow == "" || use_dow == undefined) &&
            (use_date == null || use_date == "" || use_date == undefined) &&
            (use_time == null || use_time == "" || use_time == undefined)
          ) {
            result = true //直接显示
          }

          var flag = false
          if (
            result == false &&
            use_date != null &&
            use_date != "" &&
            use_date != undefined &&
            use_time != null &&
            use_time != "" &&
            use_time != undefined
          ) {
            var use_date_arr = use_date.split("-") //獲取日期 2020.06.01-2020.07.01
            var use_time_arr = use_time.split("-") //獲取時間 09:00-12:00
            var whatDay = this.dateConversion(timeStamp) //獲取當天星期幾
            // console.log(whatDay, 'whatDay');
            var flag = false
            if (use_dow.indexOf(whatDay) >= 0) {
              //對比日期字符串中，是否存在
              flag = true
            } else {
              if (whatDay == 6 || whatDay == 7) {
                if (use_dow.indexOf("H")) {
                  flag = true
                }
              }
            }

            if (flag) {
              let startTime = use_time_arr[0] + ":00" //獲取開始時間  格式09:00:00
              let endTime = use_time_arr[1] + ":00" //獲取結束時間  格式09:00:00
              let currentTimeArry = currentTime.trim().split(" ") //獲取系统时间 2020/09/16
              let nowCurrentTime = currentTimeArry[1]
              let t = null
              let nowCurrentDate = currentTimeArry[0]
              let mindate = use_date_arr[0].replace(/\./g, "/")
              let maxdate = use_date_arr[1].replace(/\./g, "/")
              let t1 = moment(nowCurrentDate).format("YYYY-MM-DD")
              let t2 = moment(mindate).format("YYYY-MM-DD")
              let t3 = moment(maxdate).format("YYYY-MM-DD")
              // console.log(t1, t2, t3);
              // 判断当前日期是否在指定日期内
              if (moment(t1).isBetween(t2, t3, null, "[]")) {
                console.log("日期符合时间")
                t = true
              } else {
                console.log("日期不符合时间")
                t = false
              }

              let minutesRes = this.compareTime(startTime, nowCurrentTime, endTime)
              if (minutesRes && t) {
                result = true
              } else {
                result = false
              }
            }
          }
          return result
        },
        //獲取當天星期幾
        dateConversion (timeStamp) {
          let week = moment(timeStamp).day()
          switch (week) {
            case 1:
              return "1"
            case 2:
              return "2"
            case 3:
              return "3"
            case 4:
              return "4"
            case 5:
              return "5"
            case 6:
              return "6"
            case 0:
              return "7"
          }
        },
        //兩個時間對比 格式：02:00:00
        compareTime (startTime, nowCurrentTime, endTime) {
          let format = "hh:mm:ss"
          let time = moment(nowCurrentTime, format),
            beforeTime = moment(startTime, format),
            afterTime = moment(endTime, format)
          if (time.isBetween(beforeTime, afterTime, null, "[]")) {
            console.log("is between")
            return true
          } else {
            console.log("is not between")
            return false
          }
        },

        // 时间戳格式化
        timeFormatt (timestamp) {
          return moment(timestamp).format("YYYY/MM/DD HH:mm:ss")
        },
        getScroll (event) {
          let scrollLeft = parseInt(event.target.scrollLeft)
          var n2 = parseInt(event.target.clientWidth) //外层容器的宽度
          var n3 = parseInt(event.target.scrollWidth) //外层容器实际宽度，当没有滚动条时与clientWidth相等
          if (scrollLeft == 0) {
            this.showPromptLeft = false
            this.showPromptRight = true
          } else if (scrollLeft + n2 == n3) {
            console.log("终于到达最右端")
            this.showPromptLeft = true
            this.showPromptRight = false
          } else if (scrollLeft > 40) {
            this.showPromptLeft = true
            this.showPromptRight = true
          }
        },
        headerTouchStart (e) {
          // 固定元素滑动，浮动起来
          this.fixed = true
        },
        headerTouchMove (e) {
          // 组织默认事件，防止跳动
          e.preventDefault()
        },
        headerTouchEnd (e) {
          // 互动结束，浮动解除，防止滚动元素无法滚动
          this.fixed = false
        },
        contentTouchStart (e) {
          // console.log(e);
          this.contentStartY = e.changedTouches[0].clientY
        },
        contentTouchMove (e) {
          let endY = e.changedTouches[0].clientY
          // 获取滚动的距离
          let diff = endY - this.contentStartY
          let scrollTop = $(".content_warp").scrollTop()
          // 如果拉到顶了还继续往下拉
          if (diff > 0 && scrollTop <= 0) {
            this.fixed = true
            e.preventDefault()
          } else if (diff < 0) {
            // 如果没有拉到顶，则正常滑动内容栏
            this.fixed = false
          }
        },
        contentTouchEnd (e) {
          this.fixed = false
        },
        // 购物车页面
        onBlack () {
          this.showCartTab = false
        },
        // 直接提交订单
        subOrder () {
          if (this.allshoplNumber == 0) {
            let txt = this.systemLanguage.clearText
            this.layerDia(txt)
            return
          }
          var index = layer.load(2) //加載層
          let newOrderItem_foodListJson = sessionStorage.getItem("shopCartList")
          let { companyName, storeNumber, tableNumber, tableKey } = this.openTable
          let { errorMsg, errorTimeout, errorDesc, defSuccessMsg } = this.systemLanguage
          let that = this
          $.ajax({
            type: "POST", //请求方式
            url: "../store/saveTableOrder", //请求url地址
            data: {
              companyName,
              storeNumber,
              tableNumber,
              tableKey: tableKey || "",
              newOrderItem_foodListJson
            },
            success: function (result) {
              //请求成功后返回数据data
              layer.close(index) //關閉加載層
              if (!result) {
                layer.msg(errorMsg) //提示层
                return
              }
              let resultObj = JSON.parse(result)
              console.log(result, "88")
              if (resultObj.errorCode == "error403") {
                layer.msg(errorTimeout + " " + parseInt(resultObj.errorDesc / 1000) + " " + "s") //提示层
                return
              }
              if (resultObj.errorCode || resultObj.statusCode == 401) {
                layer.msg(errorDesc) //提示层
                return
              }
              layer.msg(
                defSuccessMsg,
                {
                  time: 1500 //2秒关闭（如果不配置，默认是3秒）
                },
                function () {
                  let openTableData = JSON.stringify(that.openTable)
                  window.location.href =
                    "../order/newMenuPage?openTable=" +
                    encodeURIComponent(openTableData) +
                    "&param="
                  sessionStorage.removeItem("shopCartList")
                }
              )
            },
            error: function (err) {
              console.log(err.statusText)
              console.log("异常")
            }
          })
        },
        // layer弹窗提示
        layerDia (txt) {
          layer.msg(txt, {
            scrollbar: false,
            skin: "tipDia",
            closeBtn: 2,
            time: 0,
            shade: [0]
          })
        },
        // fty升序排序
        ftySort (arry) {
          if (arry.length != 0) {
            arry.sort(function (a, b) {
              return a.sort2 - b.sort2
            })
          }
        },
        mtySort (arry) {
          if (arry.length != 0) {
            arry.sort(function (a, b) {
              return a.sort - b.sort
            })
          }
        },
        fListSort (foodList) {
          if (foodList.length != 0) {
            let useSeqSort = false
            for (let i = 0; i < foodList.length; i++) {
              let item = foodList[i]
              if (item.seq && item.seq != 0) {
                useSeqSort = true
                break
              }
            }
            // 实行排序
            if (useSeqSort) {
              foodList.sort(function (a, b) {
                return a.seq - b.seq
              })
            } else {
              console.log("zhe")
              foodList.sort(function (a, b) {
                return a.fcode.localeCompare(b.fcode)
              })
            }
          }
        },
        mListSort (mListList) {
          if (mListList.length != 0) {
            let useSort = false
            for (let i = 0; i < mListList.length; i++) {
              let item = mListList[i]
              if (item.sort && item.sort != 0) {
                useSort = true
                break
              }
            }
            // 实行排序
            if (useSort) {
              mListList.sort(function (a, b) {
                return a.sort - b.sort
              })
            } else {
              mListList.sort(function (a, b) {
                return a.code.localeCompare(b.code)
              })
            }
          }
        },
        // 选择提示
        selectPrompt (minQty, maxQty) {
          let lan = this.openTable.language
          if (minQty == maxQty) {
            if (minQty == 1) {
              if (lan == "en") {
                return `(Choose ${minQty} item)`
              } else {
                return ` (請選 ${minQty} 項) `
              }
            } else if (minQty > 1) {
              if (lan == "en") {
                return `(Choose ${minQty} items)`
              } else {
                return ` (請選 ${minQty} 項) `
              }
            }
          } else if (minQty != maxQty) {
            if (lan == "en") {
              return `(Choose ${minQty}-${maxQty} item(s))`
            } else {
              return ` (請選 ${minQty}-${maxQty} 項) `
            }
          }
        }
      }
    })
  </script>
</body>

</html>