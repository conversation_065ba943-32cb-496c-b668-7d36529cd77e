.dialog_footer {
  text-align: right;
  padding-top: 15px;
}

.check_warp {
  max-height: 150px;
  overflow: auto;
}

/* .check_warp::-webkit-scrollbar {
  display: none;
} */
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.check_warp::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}

.check_warp::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

.check_warp::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #555;
}
