* {
  margin: 0;
  padding: 0;
  font-family: Arial, Helvetica, sans-serif;
}

:root {
  --ppgColor: #770239;
}

.title {
  font-size: 0.45rem;
  font-weight: 600;
  text-align: center;
  padding: 0.5rem 0;
  /* border-bottom: 1px solid #eee; */
  /* border-bottom: 0; */
  /* background-color: #6e143a;
  color: white; */
  box-shadow: 0px 6px 5px -5px #ccc;
}
.errWarp {
  margin-top: 2rem;
  /* position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); */
  text-align: center;
}
.errIco {
  padding-bottom: 0.4rem;
  width: 3rem;
}
.tip {
  text-align: center;
  /* padding-bottom: 0.7rem; */
  padding: 0 0.3rem 0.7rem;
}
.tiperr1 {
  font-size: 0.7rem;
  padding-bottom: 0.5rem;
}
.tiperr2 {
  font-size: 0.37rem;
  color: #ccc;
  padding: 0rem 0.5rem;
}
.backBtn {
  margin: 0 auto;
  width: 5rem;
  padding: 0.3rem 0.15rem;
  background-color: var(--ppgColor);
  color: white;
  text-align: center;
  font-size: 0.4rem;
  border-radius: 0.05rem;
}
