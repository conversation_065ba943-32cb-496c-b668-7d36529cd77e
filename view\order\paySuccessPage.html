<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <script src="../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../static/vue/vue.min.js"></script>
    <script src="../static/js/page/lib-flexible.js"></script>
    <link rel="stylesheet" href="../static/css/page/paySuccessPage.css" />
    <!-- I18n国际化 -->
    <script src="../static/I18n/initI18n.js"></script>
    <!-- moment -->
    <script src="../static/moment/moment.js"></script>
    <!-- 引入 layui.css/layui.js -->
    <link rel="stylesheet" href="../static/layui/css/layui.css" />
    <script src="../static/layui/layui.js"></script>
    <script src="../static/js/index/utils.js"></script>
    <script src="../static/js/page/math.js"></script>
    <script src="../static/byod_webUtils/public.js"></script>
    <script src="../static/js/index/useOpenTableInterface.js"></script>
  </head>

  <body>
    <div id="app" v-cloak>
      <!-- <button @click="changeLan" class="changelan">点我改变语言</button>
      <button @click="changeMode" class="changeMode">点我改变模式</button> -->
      <div class="successPayBox" v-if="showType === 'normal'">
        <div class="successPayBox-center">
          <img src="../static/img/payImage/sucPay.jpg" alt="" class="successPayBox-center-img" />
          <p class="successPayBox-center-successTip">{{PayTip}}</p>
          <div class="successPayBox-center-orderNumberBox">
            <p class="successPayBox-center-orderNumberBox-title">
              {{ systemLanguage.successOrderNumber}}
            </p>
            <p class="successPayBox-center-orderNumberBox-num" :class="{'is-loading':loading}">
              <span class="billNumber-prefix">{{cutBillNumber(parmObj.billNumber,'prefix')}}</span>
              <span class="billNumber-suffix">{{cutBillNumber(parmObj.billNumber,'suffix')}}</span>
            </p>
          </div>
          <p
            class="successPayBox-center-waitTip"
            :class="{'is-loading':loading}"
            v-html="parmObj.successPrompt"
          ></p>
        </div>
        <div class="successPayBox-boundary"></div>
        <div class="successPayBox-bottom" ref="billingWarp">
          <ul class="successPayBox-bottom-cell-title" ref="billingLabel">
            <div id="orderInfo">
              <li
                class="successPayBox-bottom-cell-fixedTitle"
                v-if="parmObj.tableNumber!='TAKEAWAY'"
              >
                <p>{{systemLanguage.successPayTableNum}}:</p>
                <p :class="{'is-loading':loading}">{{parmObj.tableNumber}}</p>
              </li>
              <li class="successPayBox-bottom-cell-fixedTitle">
                <p>{{systemLanguage.successPayAmt}}:</p>
                <p :class="{'is-loading':loading}">
                  <template v-if="!loading&&parmObj.amt">{{currencyWay}}{{parmObj.amt}}</template>
                </p>
              </li>

              <li class="successPayBox-bottom-cell-fixedTitle">
                <!-- 订单时间 -->
                <template v-if="isShowPayTimeTxt">
                  <p>{{systemLanguage.successPayTime}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.time}}</p>
                </template>
                <!-- 点餐时间 -->
                <template v-else>
                  <p>{{systemLanguage.successCreateTime}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.createTime}}</p>
                </template>
              </li>

              <li class="successPayBox-bottom-cell-fixedTitle">
                <p>{{systemLanguage.successPayMerchantRef}}:</p>
                <p :class="{'is-loading':loading}">{{parmObj.merchantRef}}</p>
              </li>
              <li class="successPayBox-bottom-cell-fixedTitle" v-if="loading||parmObj.payKey">
                <p>{{systemLanguage.successPayMethod}}:</p>
                <p :class="{'is-loading':loading}">
                  <template v-if="parmObj.payKey">{{formatPayVal(parmObj.payVal)}}</template>
                </p>
              </li>
              <li
                class="successPayBox-bottom-cell-fixedTitle"
                v-if="loading||parmObj.tableNumber==='TAKEAWAY'"
              >
                <p>{{systemLanguage.successSelectedStores}}:</p>
                <p :class="{'is-loading':loading}">
                  <template v-if="!loading">
                    {{openTable.storeName&&openTable.storeName[openTable.language]||''}}
                  </template>
                </p>
              </li>
              <li class="successPayBox-bottom-cell-fixedTitle">
                <p>{{systemLanguage.successPickUpDate}}:</p>
                <p :class="{'is-loading':loading}">{{parmObj.pickupTime}}</p>
              </li>
            </div>

            <div
              class="points-wallet-container"
              v-if="parmObj.points_gain||parmObj.wallet_points_r"
            >
              <div class="pointsWarp" v-if="parmObj.points_gain" :class="pointsWarpClass">
                <li class="successPayBox-bottom-cell-fixedTitle">{{systemLanguage.pointsTitle}}</li>
                <li class="successPayBox-bottom-cell-fixedTitle" v-if="parmObj.points_open">
                  <p>{{systemLanguage.openingPointsTxt}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.points_open}}</p>
                </li>
                <li class="successPayBox-bottom-cell-fixedTitle">
                  <p>{{systemLanguage.earnedPointsTxt}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.points_gain}}</p>
                </li>
                <li class="successPayBox-bottom-cell-fixedTitle" v-if="parmObj.points_used">
                  <p>{{systemLanguage.redeemedPointsTxt}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.points_used}}</p>
                </li>
                <li class="successPayBox-bottom-cell-fixedTitle" v-if="parmObj.finalPoints">
                  <p>{{systemLanguage.balancePointsTxt}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.finalPoints}}</p>
                </li>
              </div>
              <div
                class="walletWarp"
                v-if="parmObj.wallet_points_r"
                :class="{'balanceWarp': parmObj.points_gain && parmObj.wallet_points_o}"
              >
                <li class="successPayBox-bottom-cell-fixedTitle">{{systemLanguage.walletTitle}}</li>
                <li class="successPayBox-bottom-cell-fixedTitle" v-if="parmObj.wallet_points_o">
                  <p>{{systemLanguage.openingWalletTxt}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.wallet_points_o}}</p>
                </li>
                <li class="successPayBox-bottom-cell-fixedTitle">
                  <p>{{systemLanguage.earnedWalletTxt}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.wallet_points_u}}</p>
                </li>
                <li class="successPayBox-bottom-cell-fixedTitle" v-if="parmObj.wallet_points_r">
                  <p>{{systemLanguage.balanceWalletTxt}}:</p>
                  <p :class="{'is-loading':loading}">{{parmObj.wallet_points_r}}</p>
                </li>
              </div>
            </div>
          </ul>
        </div>
        <div class="successPayBox-bottom-btn-box">
          <button class="successPayBox-bottom-btn" @click="onBlack" style="min-height: 40px">
            {{blackBtnTxt}}
          </button>
        </div>
      </div>

      <template v-if="showType === 'memberRecharge'">
        <recharge-pay-success
          :system-language="systemLanguage"
          :parm-obj="parmObj"
          :loading="loading"
          :currency-way="currencyWay"
          :is-show-pay-time-txt="isShowPayTimeTxt"
          :pay-tip="PayTip"
        ></recharge-pay-success>
      </template>
    </div>
    <script src="../static/js/index/device.js"></script>
    <script src="../static/components/RechargePaySuccess.js"></script>
    <script>
      //dom加载完成后自定义title
      window.addEventListener("DOMContentLoaded", () => {
        $.getJSON("../static/utils/config.json", function (data) {
          document.title = data.BYOD.DocumentTitle || ""
        })
      })
      let html = document.documentElement
      //MutationObserver 用于监视对DOM树所做的更改
      //当DOM树发生化时，MutationObserver对象会收到通知
      const MutationObserver =
        window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver
      if (MutationObserver) {
        let observer = new MutationObserver(function () {
          // let zoomFontSize = sessionStorage.getItem("zoomFontSize")
          let htmlFontSize = parseInt($("html").css("font-size"))
          if (window.innerWidth < 375 || window.innerWidth == 375) {
            if (htmlFontSize > 40) {
              html.style.fontSize = "40px"
            }
          } else if (htmlFontSize > 46) {
            html.style.fontSize = "45px"
          }
        })
        observer.observe(html, {
          attributes: true,
          attributeFilter: ["style"]
        })
      }
      new Vue({
        el: "#app",
        data: {
          systemLanguage: {},
          openTable: {},
          parmObj: {
            amt: "",
            points_open: "", //原始积分
            points_gain: "", //增加的积分(此字段决定是否显示积分内容)
            points_used: "", //扣除积分
            finalPoints: "", // points_open + points_gain 最终积分
            merchantRef: "",
            time: "", //订单时间
            createTime: "", //点餐时间(创建时间,用于没有订单时间的情况)
            currencyWay: "",
            successPrompt: "",
            allBillNumber: "",
            payKey: "",
            payVal: "",
            wallet_points_o: "", // 原有余额 (original)
            wallet_points_u: "", // 本次使用 (used)
            wallet_points_r: "" // 剩余余额 (remaining)
            // originalSavingsCardDate: "", //原来的储蓄卡日期
            // finalSavingsCardDate: "", //最终储蓄卡日期
            // originalSavingsCardPoints: null, //原来的储蓄卡积分
            // finalSavingsCardPoints: null, //最终储蓄卡积分
            // originalMembershipDate: "", //原来的会员日期
            // finalMembershipDate: "", //最终会员日期
            // originalMembershipPoints: null, //原来的会员积分
            // finalMembershipPoints: null //最终会员积分
          },
          currencyWay: "", //价格符号
          loading: true,
          PayTip: "",
          firstRequestFlag: true,
          timer: null,
          menuLanguageList: {},
          showType: "normal"
        },
        async created() {
          this.showType = this.getQueryVariable("specialItem") || "normal"
          await this.makeI18nLoaded()
          this.initMenuLanguage()
          await this.getUIConfig()
          this.initializeThe()
          this.fixLan()
          this.getParmObj()
        },
        mounted() {},
        watch: {
          parmObj(val) {
            //判断数据大小决定样式
            this.addTinyDeviceClass()
          }
        },

        methods: {
          initMenuLanguage() {
            const i18n = sessionStorage.getItem("i18nPkg")
            try {
              const { menu } = JSON.parse(i18n)
              const formatData = formatI18n([...menu])
              this.menuLanguageList = Object.freeze(formatData)
            } catch {}
          },
          formatPayVal(val) {
            const lan = this.openTable.language
            return this.menuLanguageList[lan][val + "Txt"] || val
          },
          makeI18nLoaded() {
            const i18n = sessionStorage.getItem("i18nPkg")
            return new Promise(resolve => {
              if (!i18n) {
                this.timer = setInterval(() => {
                  if (window.i18n) {
                    this.timer = null
                    resolve()
                  }
                }, 50)
              } else {
                resolve()
              }
            })
          },
          getUIConfig() {
            const openTable = sessionStorage.getItem("openTable")
            if (openTable) return Promise.resolve()
            const storeNumber = this.getQueryVariable("storeNumber")
            const domain = window.location.host.split(".")[0]
            return fetch(`../uiConfig/getStoreUIConfig?domain=${domain}&storeNumber=${storeNumber}`)
              .then(r => r.json())
              .then(r => {
                const { statusCode, uiConfigList = [], ...rest } = r
                if (statusCode !== 200) return Promise.reject()
                const config = dynamicConfig && dynamicConfig(uiConfigList)
                const language = localStorage.getItem("language") || "zh"
                this.openTable = { ...config, language, storeNumber, domain, ...rest }
                this.currencyWay = this.openTable.currencyWay || "$"
                sessionStorage.setItem("openTable", JSON.stringify(this.openTable))
              })
              .catch(e => {})
          },
          initializeThe() {
            this.openTable = JSON.parse(sessionStorage.getItem("openTable") || "{}")
            // 动态价格符号
            this.currencyWay = this.openTable.currencyWay || "$"
            // 成功清除数据
            sessionStorage.removeItem("shopCartList")
            //初始化主题颜色
            $("html").css({ "--styleColor": this.openTable.color })
          },
          initSomeTip() {
            //初始提示
            let { pendingPayTip } = this.systemLanguage
            this.PayTip = pendingPayTip
          },
          fixLan() {
            let language = this.openTable.language || "zh"
            this.systemLanguage = window.i18n[language]
            this.initSomeTip()
          },

          onBlack() {
            if (this.openTable.goHistoryAfterOrder) {
              goHistoryAfterOrder(this.openTable.performType)
            } else {
              const domain = sessionStorage.getItem("domain")
              if (domain) {
                window.location.href = "../order/menuPage.html"
              } else {
                const index = localStorage.getItem("indexPageUrl")
                try {
                  window.location.href = new URL(index).href
                } catch {}
              }
            }
          },

          getQueryVariable(variable) {
            let query = window.location.search.substring(1) // 获取 "?" 后面的参数部
            if (!query) return null
            query = query.split("?")[0]
            // 检查参数部分是否包含&符号
            if (query.includes("&")) {
              // 参数没有全加密，根据&进行截取并获取参数
              const urlParams = new URLSearchParams(query)
              let value = urlParams.get(variable)
              // 如果是merchantRef参数，则进行Base64解码
              if (variable === "merchantRef" && value) {
                value = atob(value)
              }
              return value
            } else {
              // 参数完全加密，解析Base64再获取参数
              const decodedParams = atob(query)
              const urlParams = new URLSearchParams(decodedParams)
              return urlParams.get(variable)
            }
          },
          getParmObj() {
            // let merchantRef = "3a0ac1ce613fbdbdf983ae71e306719bf9570de2186b"
            let merchantRef =
              this.getQueryVariable("merchantRef") || this.getQueryVariable("merchantOrderNo")

            let { errorGetParmObjTxt, instantPickup } = this.systemLanguage
            let timeout = 5000 // 初始超时时间为5秒
            const maxTimeout = 40000 // 最大超时时间为40秒
            const duplicateRequest = () => {
              if (timeout < maxTimeout) {
                timeout += 1000 // 超时后递增超时时间
                doRequest()
              } else {
                layer.msg(errorGetParmObjTxt)
              }
            }
            const doRequest = () => {
              this.loading = true
              var index = layer.load(2, { shade: 0 }) // 加载层
              const domain = window.location.host.split(".")[0]
              let requestData = { domain, merchantRef, firstRequestFlag: this.firstRequestFlag }
              $.post({
                url: "../pay/getOrderStatus",
                dataType: "json",
                data: requestData,
                timeout: timeout,
                success: res => {
                  // res.statusCode = 210
                  const showData = res => {
                    let finalPoints = ""
                    if (res.points_gain) {
                      let val = floatAdd(res.points_open, res.points_gain)
                      finalPoints = floatAdd(val, res.points_used)
                    }
                    this.parmObj = {
                      ...this.parmObj,
                      ...res,
                      amt: retainSignificantDecimals(res.multipleTotalAmt || res.amt),
                      time: res.date,
                      pickupTime: res.pickupTime ? res.pickupTime : instantPickup,
                      finalPoints, //最终积分
                      statusCode: res.statusCode, // 状态码
                      originalSavingsCardPoints: this.formatPoints(res.originalSavingsCardPoints),
                      finalSavingsCardPoints: this.formatPoints(res.finalSavingsCardPoints),
                      originalMembershipPoints: this.formatPoints(res.originalMembershipPoints),
                      finalMembershipPoints: this.formatPoints(res.finalMembershipPoints)
                      //   billNumber: res.billNumber,
                      //   createTime: res.createTime,
                      //   points_gain: res.points_gain, //增加积分
                      //   points_open: res.points_open, //原始积分
                      //   points_used: res.points_used, //扣除积分
                      //   payKey: res.payKey,
                      //   payVal: res.payVal,
                      //   successPrompt: res.successPrompt,
                      //   merchantRef: res.merchantRef,
                      //   tableNumber: res.tableNumber,
                      //   wallet_points_o: res.wallet_points_o, // 原有余额
                      //   wallet_points_u: res.wallet_points_u, // 本次使用
                      //   wallet_points_r: res.wallet_points_r, // 剩余余额
                    }
                    this.$nextTick(() => {
                      this.setMaxPDomWidth() //重置p标签的宽度
                    })
                  }
                  console.log(JSON.parse(JSON.stringify(res)), "res")
                  if (res.statusCode == 200) {
                    // res.payVal='OCTOPUS'
                    // res.payKey='source'
                    let { successPayTip, successRechargePayTip } = this.systemLanguage
                    this.PayTip =
                      this.showType === "memberRecharge" ? successRechargePayTip : successPayTip
                    showData(res)
                    console.log(JSON.parse(JSON.stringify(this.parmObj)), " this.parmObj")
                    // this.setCookie(merchantRef) // 加入缓存
                  } else if (res.statusCode == 210) {
                    //解决请求EFT接口很长时间没回复，导致下单失败一直loading，客户以为下不了单跑去重复给钱下单的问题
                    this.firstRequestFlag = false
                    let {
                      pendingPaymentStatus,
                      shopOrderPending,
                      rechargingTip,
                      rechargeSuccessTip
                    } = this.systemLanguage
                    let orderStatusTip = {
                      normal: {
                        6: pendingPaymentStatus,
                        7: shopOrderPending
                      },
                      memberRecharge: {
                        6: pendingPaymentStatus,
                        7: shopOrderPending,
                        8: rechargingTip,
                        9: rechargeSuccessTip
                      }
                    }

                    if (res.orderStatus) {
                      this.PayTip = orderStatusTip[this.showType][res.orderStatus] || ""
                    }
                    res.points_gain = "" //积分不显示
                    showData(res)
                    duplicateRequest()
                  } else if (res.statusCode === 0) {
                    doRequest()
                  } else {
                    layer.msg(errorGetParmObjTxt)
                  }
                },
                error: (xhr, status, error) => {
                  this.firstRequestFlag = true
                  duplicateRequest()
                },
                complete: () => {
                  layer.close(index) // 关闭加载层
                  this.loading = false
                  // console.log('成功失败都执行')
                }
              })
            }
            doRequest()
          },

          cutBillNumber(billNumber = "", type) {
            //传递的billNumber根据'-截取,根据type返回
            if (!billNumber.includes("-")) return billNumber
            let arr = billNumber.split("-")
            if (type == "prefix") return arr[0]
            else if (type == "suffix") return arr[1]
          },
          //为小型设备添加class,避免太多数据造成样式挤压
          addTinyDeviceClass() {
            //如果.successPayBox-bottom-cell-title下数量大于7,则添加小型设备类名
            this.$nextTick(() => {
              //jq获取successPayBox-bottom-cell-title下的li
              const liTags = $(
                "ul.successPayBox-bottom-cell-title > li, ul.successPayBox-bottom-cell-title > .pointsWarp > li"
              )
              console.log("🚀 ~ this.$nextTick ~ liTags:", liTags)
              if (liTags.length >= 7) {
                this.$refs.billingWarp.classList.add("tinyDevice")
              }
            })
          },
          setMaxPDomWidth() {
            // 分别获取不同区域的label宽度
            const orderInfoLabels = document.querySelectorAll(
              "#orderInfo li.successPayBox-bottom-cell-fixedTitle > p:first-child"
            )
            const pointsLabels = document.querySelectorAll(
              ".pointsWarp li.successPayBox-bottom-cell-fixedTitle > p:first-child"
            )
            const walletLabels = document.querySelectorAll(
              ".walletWarp li.successPayBox-bottom-cell-fixedTitle > p:first-child"
            )

            const getMaxWidth = elements => {
              let max = 0
              elements.forEach(el => {
                const width = Math.ceil(el.offsetWidth) + 2 // 向上取整并添加2px缓冲
                if (width > max) max = width
              })
              return max
            }

            const orderMaxWidth = getMaxWidth(orderInfoLabels)
            const pointsMaxWidth = getMaxWidth(pointsLabels)
            const walletMaxWidth = getMaxWidth(walletLabels)

            orderInfoLabels.forEach(el => (el.style.width = `${orderMaxWidth}px`))
            pointsLabels.forEach(el => (el.style.width = `${pointsMaxWidth}px`))
            walletLabels.forEach(el => (el.style.width = `${walletMaxWidth}px`))
          },
          //切换语言
          changeLan() {
            let openTable = JSON.parse(sessionStorage.getItem("openTable"))
            openTable.language === "zh" ? (openTable.language = "en") : (openTable.language = "zh")
            sessionStorage.setItem("openTable", JSON.stringify(openTable))
            this.fixLan()
            //刷新页面
            location.reload()
          },
          changeMode() {
            this.showType = this.showType === "memberRecharge" ? "normal" : "memberRecharge"
          },
          //转换数据格式,后端数据库无法存储数组类型

          formatArrData(data, changeType) {
            if (changeType == "str") {
              data = data || []
              return data.join(",")
            } else {
              data = data || ""
              return data.split(",")
            }
          },
          formatPoints(points) {
            if (points !== undefined && points !== null) {
              return floatDiv(points, 100)
            } else {
              return null
            }
          }
        },
        computed: {
          isShowPayTimeTxt() {
            const { time, createTime } = this.parmObj
            return time || !createTime
          },
          pointsWarpClass() {
            return {
              balanceWarp: this.parmObj.points_gain && this.parmObj.wallet_points_o,
              "pointsWarp-divider": this.parmObj.wallet_points_o
            }
          },
          blackBtnTxt() {
            let { successPayBlackOrderBtn, successPayBlackBtn } = this.systemLanguage
            return this.openTable.goHistoryAfterOrder ? successPayBlackOrderBtn : successPayBlackBtn
          }
        }
      })
    </script>
  </body>
</html>
