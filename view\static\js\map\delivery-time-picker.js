let DeliveryTimePicker = {
  rootEl: "",
  option: {},
  dayList: [],
  weekdayText: {
    zh: ["周日", "週一", "週二", "週三", "週四", "週五", "週六"],
    en: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
    value: []
  },
  dayNameText: {
    zh: ["今天", "明天", "後天"],
    en: ["Today", "Tomorrow", "DayAfterTomorrow"],
    value: []
  },
  headerText: {
    value: "請選擇預計取餐時間"
  },
  takeawayHeaderTxt: {
    en: "Please select an estimated pickup time",
    zh: "請選擇預計取餐時間"
  },
  testTimeHeaderTxt: {
    en: "Please select a simulation time",
    zh: "請選擇模擬時間"
  },
  instantPickupTxt: {
    en: "immediately",
    zh: "盡快",
    value: "immediately"
  },
  warnText: "",
  timeRange: {},
  currentDayIndex: 0,
  isShow: false,
  timeZone: "",
  createDiv(rootEl) {
    let warnDom = document.createElement("div")
    warnDom.className = "warn-icon-btn"
    warnDom.addEventListener("click", this.showWarn.bind(this, false))
    let closeDom = document.createElement("div")
    closeDom.className = "close-btn"
    closeDom.addEventListener("click", this.close.bind(this))
    let headerDom = document.createElement("div")
    headerDom.className = "picker-header"
    headerDom.innerHTML = `<span class='headerTip'>${this.headerText.value}</span> `
    // 将 warnDom 插入到 headerDom 的最前面
    headerDom.insertBefore(warnDom, headerDom.firstChild)
    headerDom.appendChild(closeDom)
    let dayUlDom = document.createElement("ul")
    dayUlDom.id = "pickerDay"
    let timeUlDom = document.createElement("ul")
    timeUlDom.id = "pickerTime"
    let contentDom = document.createElement("div")
    contentDom.className = "picker-content"
    contentDom.appendChild(dayUlDom)
    contentDom.appendChild(timeUlDom)
    let boxDom = document.createElement("div")
    boxDom.className = "picker-box hide"
    boxDom.appendChild(headerDom)
    boxDom.appendChild(contentDom)
    let pickerDom = document.createElement("div")
    pickerDom.id = "deliveryTimePicker"
    pickerDom.appendChild(boxDom)
    if (rootEl) {
      this.rootEl = rootEl
      document.getElementById(rootEl.split("#")[1]).appendChild(pickerDom)
    } else {
      document.body.appendChild(pickerDom)
    }
  },
  init(option, rootEl) {
    option = option ? option : {}
    option.checkTimeObj = option.checkTimeObj ? option.checkTimeObj : {}
    option.lan = option.lan === "zh" ? "zh" : "en"
    option.tableNumber = option.tableNumber ? option.tableNumber : "TAKEAWAY" //默认外卖台号(却分模式显示头部信息)
    this.warnText = option.warnText
    if (option.lan === "en") {
      this.dayNameText.value = this.dayNameText.en
      this.weekdayText.value = this.weekdayText.en
      this.headerText.value =
        option.tableNumber === "TAKEAWAY" ? this.takeawayHeaderTxt.en : this.testTimeHeaderTxt.en
    } else {
      this.headerText.value =
        option.tableNumber === "TAKEAWAY" ? this.takeawayHeaderTxt.zh : this.testTimeHeaderTxt.zh
      this.dayNameText.value = this.dayNameText.zh
      this.weekdayText.value = this.weekdayText.zh
    }
    this.createDiv(rootEl)

    if (option.clickOutside) {
      document.addEventListener("click", this.clickToClose)
    }
    this.option = option
    this.renderDayList()
  },
  destroy() {
    if (this.option.clickOutside) {
      document.removeEventListener("click", this.clickToClose)
    }
    let pickerDom = document.getElementById("deliveryTimePicker")
    if (this.rootEl) {
      document.getElementById(this.rootEl.split("#")[1]).removeChild(pickerDom)
    } else {
      document.body.removeChild(pickerDom)
    }
    // DeliveryTimePicker = null
  },
  clickToClose(e) {
    if (
      this.isShow &&
      !document.querySelector("#deliveryTimePicker .picker-box").contains(e.target)
    ) {
      this.close()
    }
  },
  /**
   * @description 匹配是否为未来2天(包括今天)的日期
   * @param { `{month:number}/{day:number}`} dateStr 日期字符串,年份为当前年
   * @param { 0|1|2 } range 日期范围,0为今天,1为明天,2为后天
   * */
  matchFutureDate(dateStr, range) {
    // 计算目标日期
    let targetDate = new Date(this.timeZone)
    targetDate.setDate(targetDate.getDate() + range)

    // 提取目标日期的月份和日期
    let targetMonth = targetDate.getMonth() + 1
    targetMonth = targetMonth >= 10 ? targetMonth : "0" + targetMonth
    let targetDay = targetDate.getDate()
    targetDay = targetDay >= 10 ? targetDay : "0" + targetDay

    // 格式化目标日期为字符串
    let targetDateStr = `${targetMonth}/${targetDay}`

    // 返回目标日期是否与传入的日期字符串匹配
    return targetDateStr === dateStr
  },
  /**
   * @description 判断传入日期列表是否包含未来2天(包括今天)的日期
   * @param { Array } dateList 日期列表
   * @define { 0|1|2 } 0为今天,1为明天,2为后天 ;for中循环的2,为只判断未来1天<今天和明天>, "后天"为暂不匹配
   * @returns { number | boolean } 返回对应的日期索引, 0 | 1 | 2 , boolean为不匹配
   * */
  matchFutureTwoDay(dateList) {
    let index = dateList.length
    let lastItem = dateList[index - 1]
    for (let i = 0; i < 2; i++) {
      if (this.matchFutureDate(lastItem, i)) {
        return i
      }
    }
    return false
  },
  /**
   * @description 转换日期字符串为Date对象
   * @param { `{month:number}/{day:number}`} dateStr 日期字符串,年份为当前年
   * @return { Date }
   * @example parseDateStr('03/05') => new Date(thisYear, 2, 5)
   * */
  parseDateStr(dateStr, timeZone) {
    const currentYear = new Date(timeZone).getFullYear()
    const dateParts = dateStr.split("/")
    const month = parseInt(dateParts[0], 10)
    const day = parseInt(dateParts[1], 10)
    // 月份需要减去1，因为 JavaScript 中月份从0开始
    return new Date(currentYear, month - 1, day)
  },
  /**
   * @description 初始化左侧日期列表
   * */
  renderDayList() {
    let dayDom = document.getElementById("pickerDay")
    dayDom.innerHTML = ""
    this.dayList = []
    let timeRangeKeys = Object.keys(this.timeRange)
    for (let i = 0; i < timeRangeKeys.length; i++) {
      let dayStr = timeRangeKeys[i]
      let newDay = this.parseDateStr(dayStr, this.timeZone)
      this.dayList.push({
        year: newDay.getFullYear(),
        month: newDay.getMonth() + 1,
        date: newDay.getDate(),
        day: newDay.getDay()
      })
      // i<=1 只转换今天和明天
      if (!this.option.showDate && i <= 1) {
        let index = this.matchFutureTwoDay(timeRangeKeys.slice(0, i + 1))
        if (typeof index === "number") {
          dayStr = this.dayNameText.value[index]
        }
      }
      if (this.option.showWeekday) {
        dayStr += "（" + this.weekdayText.value[newDay.getDay()] + "）"
      }
      let liStr = `
        <li class="${this.currentDayIndex === i && "active"}" 
            data-date="${newDay.getDate()}"
            onclick="DeliveryTimePicker.changeDay('${i}')">
        ${dayStr}</li>  
      `
      dayDom.innerHTML += liStr
    }
    dayDom.scrollTop = 0
  },
  // 显示组件
  show(timeRange = {}, startTimeSecond = "00", timeZone) {
    this.timeZone = timeZone
    this.startTimeSecond = startTimeSecond
    this.timeRange = this.handleInstantPickup(timeRange)
    this.renderDayList()
    document.getElementById("deliveryTimePicker").style.display = "block"
    window.requestAnimationFrame(() => {
      document.querySelector("#deliveryTimePicker .picker-box").classList = ["picker-box show"]
      this.isShow = true
    })
    this.renderTimeList("init") // 打开时间插件更新时间列表
  },
  // 关闭组件
  close() {
    //应酬warn-icon-btn
    this.toggleDisplayWarn(false)
    document.querySelector("#deliveryTimePicker .picker-box").classList = ["picker-box hide"]
    setTimeout(() => {
      document.getElementById("deliveryTimePicker").style.display = "none"
      this.isShow = false
    }, 300)
  },

  /**
   * @description 根据option.checkTimeObj获取日期的索引
   * @returns { number } 应该选中的日期索引
   * */
  getDayIndexByEcho() {
    let { date, hourStrAndMinute } = this.option.checkTimeObj
    if (date && hourStrAndMinute) {
      //   menuPage回显
      let index = this.dayList.findIndex(item => {
        return isEquals(item, date)
      })
      return index >= 0 ? index : 0
    }
    // map 打开
    return 0
  },
  /**
   * @description 渲染组件右侧的时间列表
   * @param { 'init'|'noInit' } type 根据type回显选中的时间
   * */
  renderTimeList(type = "noInit") {
    let timeDom = document.getElementById("pickerTime")
    timeDom.innerHTML = ""
    let timeRangeKeys = Object.keys(this.timeRange)
    if (timeRangeKeys.length === 0) return
    let preloadList = this.timeRange[timeRangeKeys[this.currentDayIndex]]
    timeDom.innerHTML = preloadList.reduce((pre, cur, index, arr) => {
      let [period, timing] = cur.split("[")
      pre += `<li data-hourStrAndMinute=${period}  onclick="DeliveryTimePicker.chooseTime(${index})">${period}</li>  `
      return pre
    }, "")
    this.echoActiveDayTime(type)
  },
  // 改变日期
  changeDay(index) {
    if (document.querySelector("#deliveryTimePicker #pickerDay").children.length > 0) {
      if (index !== this.currentDayIndex) {
        let activeDom = document.querySelector("#deliveryTimePicker #pickerDay .active")
        if (activeDom) {
          activeDom.className = ""
        }
        let currentDom = document.querySelector("#deliveryTimePicker #pickerDay").children[index]
        currentDom.className = "active"
        currentDom.scrollIntoView({ block: "center" })
        this.currentDayIndex = index
        this.renderTimeList()
      }
    }
  },
  // 选择时间
  chooseTime(index) {
    if (document.querySelector("#deliveryTimePicker #pickerTime").children.length > 0) {
      let activeDom = document.querySelector("#deliveryTimePicker #pickerTime .active")
      if (activeDom) {
        activeDom.className = ""
      }
      let currentDom = document.querySelector("#deliveryTimePicker #pickerTime").children[index]
      currentDom.className = "active"
      this.close()

      let day = this.dayList[this.currentDayIndex]
      let timeStr =
        day.year +
        "-" +
        (day.month < 10 ? "0" + day.month : day.month) +
        "-" +
        (day.date < 10 ? "0" + day.date : day.date) +
        " " +
        currentDom.textContent
      if (this.option.callback && typeof this.option.callback === "function") {
        // 获取currentDom属性data-hourStrAndMinute
        let hourStrAndMinute = currentDom.getAttribute("data-hourStrAndMinute")

        this.option.callback(day, hourStrAndMinute, timeStr, this.startTimeSecond)
      } else {
        console.warn(
          "option of DeliveryTimePicker.init(option) dosen't has callback function! ---from DeliveryTimePicker"
        )
      }
    }
  },
  /**
   * @description 回显选中的时间和日期
   * @param { 'init'|'noInit' } type 根据type回显选中的时间, init为show时,初始化渲染,noInit为切换日期的渲染
   * */
  echoActiveDayTime(type) {
    if (type === "init") {
      // 选中day日期
      this.changeDay(this.getDayIndexByEcho())
      // 选中time时间
      this.echoActiveTime()
    } else {
      // 选中time时间
      if (+this.currentDayIndex === this.getDayIndexByEcho()) {
        //   切换为原来的日期
        this.echoActiveTime()
      }
    }
  },
  /**
   * @description 回显选中的时间time
   * */
  echoActiveTime() {
    let { hourStrAndMinute } = this.option.checkTimeObj
    let timeListDom = document.querySelectorAll("#deliveryTimePicker #pickerTime li")
    for (const timeItem of timeListDom) {
      if (timeItem.getAttribute("data-hourStrAndMinute") === hourStrAndMinute) {
        timeItem.className = "active"
        timeItem.scrollIntoView()
        break
      }
    }
  },
  /**
   * @descriptionc 处理'尽快'的逻辑:判断若当前时间在营业时间范围内,第一个则显示尽快,反正不处理
   * */
  handleInstantPickup(data) {
    let keys = Object.keys(data)
    let { requestData: { openingHours = app.openTable.openingHours } = {} } = app
    if (
      verificationTimePeriod([], openingHours, true, this.timeZone) &&
      app.openTable.tableNumber.toUpperCase() !== "TEST"
    ) {
      if (this.matchFutureDate(keys[0], 0)) {
        //   若第一个日期为当天,将第一个时间段改为尽快
        let { lan } = this.option
        let firstTime = data[keys[0]][0]
        let firstTimeArr = firstTime.split("[")
        firstTimeArr[0] = this.instantPickupTxt[lan]
        data[keys[0]][0] = firstTimeArr.join("[")
      }
    }
    return data
  },
  /**
   * @description pickup弹窗header左侧有一个警告图标,点击后触发的事件
   * @param { boolean } isPassive 是否为被动触发此函数,点击icon-btn为主动触发
   * */
  showWarn(isPassive) {
    let iconWrap = document.querySelector("#deliveryTimePicker .warn-icon-btn .warn-tip-wrap")
    if (isPassive && iconWrap) return
    if (iconWrap) {
      iconWrap.remove()
      return
    }
    let warnTipWrap = document.createElement("div")
    warnTipWrap.className = "warn-tip-wrap"
    let warnTip = document.createElement("div")
    warnTip.className = "warn-tip"
    warnTip.textContent = this.warnText
    warnTipWrap.appendChild(warnTip)
    let warnIcon = document.querySelector("#deliveryTimePicker .warn-icon-btn")
    warnIcon.appendChild(warnTipWrap)
  },
  /**
   * @description 切换显示警告图标
   * @param { boolean } status 是否显示警告图标
   * */
  toggleDisplayWarn(status) {
    let warnDom = document.querySelector("#deliveryTimePicker .warn-icon-btn")
    if (!this.warnText) return
    warnDom.style.display = status ? "block" : "none"
    if (status) this.showWarn(true)
  }
}

function isEquals(obj1, obj2) {
  let keys1 = Object.keys(obj1)
  let keys2 = Object.keys(obj2)
  if (keys1.length !== keys2.length) return false
  for (let key of keys1) {
    if (typeof obj1[key] === "object" && typeof obj2[key] === "object") {
      if (!isEquals(obj1[key], obj2[key])) return false
    } else if (obj1[key] !== obj2[key]) return false
  }
  return true
}

/**
 * @description 是否为及时取件(尽快)
 * @param { string } pickupTime 取件时间
 * @returns { boolean } true: 不是及时取件 false: 是及时取件
 * */
function notInstantPickup(pickupTime = "") {
  if (!pickupTime) return false
  let pickupTimeStr = pickupTime.split(" ")[1] || ""
  return pickupTimeStr.match(/\d{2}:\d{2}/)
}
/**
 *  @description格式化取件时间<拼接上秒>
 *  @param { string } pickupTime 取件时间 `2023-09-05 14:13-14:45`
 *  @param { string } seconds 秒数
 *  @returns {string} 格式化后的取件时间
 * */
function formatPickupTime(pickupTime = "", seconds = "00") {
  let [date, time] = pickupTime.split(" ")
  let [startTime, endTime] = time.split("-")
  startTime = startTime + ":" + seconds
  endTime = endTime + ":" + seconds
  return `${date} ${startTime}-${endTime}`
}

if (!String.prototype.replaceAll) {
  String.prototype.replaceAll = function (search, replacement) {
    let target = this
    return target.replace(new RegExp(search, "g"), replacement)
  }
}
/**
 * @description 判断当前时间是否在营业日期时间内/前
 * @param {Array} datePeriod 日期段 ['2023-09-04', '2023-09-04']
 * @param {Array} timePeriod 时间段 ['09:57:44', '17:57:44']
 * @param { Boolean } isScope true: 判断是否在日期段内 false: 判断是否在最后日期前
 * @param timeZone {string} 后端返回的当前时区时间
 * @returns {Boolean} true: 在当前时间段内 false: 不在当前时间段内
 * */
function verificationTimePeriod(
  datePeriod = [],
  timePeriod = [],
  isScope = false,
  timeZone = new Date()
) {
  let [startDate = "", endDate = ""] = datePeriod
  let [startTime, endTime] = timePeriod
  startDate = startDate.replaceAll("-", "/")
  endDate = endDate.replaceAll("-", "/")
  let current = new Date(timeZone)
  let curDate = current.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit"
  })
  let curTime = current.toLocaleTimeString("zh-CN", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  })
  let status = false
  const verityDate = (start, end) => {
    let startStamp = new Date(start).getTime()
    let endStamp = new Date(end).getTime()
    let curStamp = new Date(curDate).getTime()
    if (isScope) {
      // 判断是否在日期段内
      return curStamp >= startStamp && curStamp <= endStamp
    }
    return curStamp <= endStamp
  }
  const verityTime = (start, end) => {
    let startStamp = new Date(`${curDate} ${start}`).getTime()
    let endStamp = new Date(`${curDate} ${end}`).getTime()
    let curStamp = new Date(`${curDate} ${curTime}`).getTime()
    if (isScope) {
      return curStamp >= startStamp && curStamp <= endStamp
    }
    return curStamp <= endStamp
  }
  const hasTomorrow = (start, end) => {
    let { pickupTime: { pickupDayRange = 0 } = {} } = app.openTable
    let startStamp = new Date(start).getTime()
    let endStamp = new Date(end).getTime()
    let day = new Date(curDate).getDate()
    let towStamp = new Date(new Date(curDate).setDate(day + 1)).getTime()
    return pickupDayRange && endStamp >= towStamp && startStamp <= towStamp
  }
  if (!datePeriod.length && !timePeriod.length) {
    status = true
  }
  if (datePeriod.length && timePeriod.length) {
    if (isScope) {
      //判断是否在日期时间段内,需要日期时间都满足
      status = verityDate(startDate, endDate) && verityTime(startTime, endTime)
    } else {
      // 先判断日期是否满足,若时间不满足则要判断日期是否存在后一天
      status =
        verityDate(startDate, endDate) &&
        (verityTime(startTime, endTime) || hasTomorrow(startDate, endDate))
    }
  }
  if (datePeriod.length && !timePeriod.length) {
    status = verityDate(startDate, endDate)
  }
  if (!datePeriod.length && timePeriod.length) {
    if (isScope) {
      status = verityTime(startTime, endTime)
    } else {
      //只存在时间段,满足
      status = true
    }
  }
  return status
}
