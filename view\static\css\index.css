ol,
ul {
  padding-left: 0 !important;
}
.ol-left-tiled li {
  list-style-type: none;
  float: left;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.navRight {
  /* position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%); */
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  /* float: right; */
}

.userInfo {
  font-size: 20px;
  margin-right: 20px;
  color: #999c9e;
}

.logout {
  font-size: 20px;
  color: #999c9e;
}

.nav_text {
  margin-left: 5px;
  color: #888888 !important;
}

.content_Box {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.content_warp {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.content_warp_iframeBox {
  flex: 1;
  width: 100%;
  height: 100%;
}
.top_warp {
  /* height: ; */
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  padding: 10px 0;
  background-color: #263238;
}

.navRight {
  padding-right: 20px;
}

.userInfo,
.logout {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.navRight a {
  text-decoration: none !important;
}

.navRight .dropdown-menu {
  min-width: 20px;
  padding: 10px 0px;
}
.navRight_menu {
  font-size: 14px;
}
.navRight_menu .menu_item_warp .dropdown-item:hover {
  background-color: #f8f9fa !important;
}

.storeNumber,
.accountName,
.logoOut {
  display: flex;
  align-items: center;
}

.dropdown-item {
  padding: 10px 10px;
  cursor: pointer;
}
.dropdown-item-storeNumBox {
  display: flex;
  justify-content: space-between;
}
.dropdown-item-storeNum {
  flex: 1;
}
.dropdown-item-delBtn {
  padding: 0 10px;
  border-radius: 3px;
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}
.select_storeNumber_li {
  position: relative;
}
#nowStoreNumber {
  display: inline-block;
  max-width: 55px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 5px;
  margin-bottom: -2px;
}
.selectStoreNumberBox::after {
  content: ">";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  margin-top: -10px;
}

#logoImg {
  width: 200px;
}
.navMenuWarp {
  display: flex;
  align-items: center;
  margin-right: 17px;
  color: #999c9e;
  font-size: 18px;
  position: relative;
}

.iconHover:focus,
.iconHover:hover {
  color: #23527c;
  /* text-decoration: underline; */
}

.roundIcon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #fff;
  display: inline-block;
  color: burlywood;
  text-align: center;
  line-height: 20px;
  border: 1px solid burlywood;
  margin: 0 10px 0 0 !important;
}
.dragBox {
  display: flex;
  align-items: center;
  color: #888888 !important;
  /* margin-top: 2px; */
  /* flex-direction: column; */
  height: 100%;
}
.dragBox jelly-switch {
  display: flex;
  height: 100%;
  align-items: center;
  margin-top: 5px;
  margin-left: -6px;
}

/* 重置bootstrap的下拉箭头样式 */
.dropdown-toggle::after {
  margin-left: 0.1em;
  vertical-align: middle;
  color: #999c9e;
}
#updateCacheContent {
  display: flex;
  align-items: center;
}
/* 有缓存更新图标 */
.updateCache-exclamation-icon {
  display: none;
  position: absolute;
  right: -15px;
  top: 3px;
  color: #e6a23c;
  font-size: 16px;
}
/* .updateCacheBox .dropdown-menu {
  min-width: 30px;
  padding: 10px 0px;
} */
.refreshDataLi {
  position: relative;
}
.ivu-badge-dot {
  display: none;
  position: absolute;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-transform-origin: 0 center;
  transform-origin: 0 center;
  top: 8px;
  right: 27px;
  height: 8px;
  width: 8px;
  border-radius: 100%;
  background: #e6a23c;
  z-index: 10;
  -webkit-box-shadow: 0 0 0 1px #fff;
  box-shadow: 0 0 0 1px #fff;
}
.dropdownLabel {
  font-weight: 400;
  font-size: 20px;
  color: #888888 !important;
  cursor: pointer;
}
.historyVersion-li-a {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.historyVersion-li-a span {
  margin-right: 10px;
}
.navLeft {
  display: flex;
  align-items: center;
}

#toggle li {
  position: relative;
}

.store_name_warp {
  position: absolute;
  bottom: 0;
  display: flex;
  justify-content: end;
  right: 10px;
  /* width: 100%; */
  color: #fff;
}
.store_name_warp img {
  width: 20px;
  height: 20px;
  margin: 11px 11px 10px 37px;
}
.store_name_warp span {
  max-width: 205px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 41px;
}
.el-tag {
  background-color: #ecf5ff;
  display: inline-block;
  height: 20px;
  padding: 0 5px;
  line-height: 19px;
  font-size: 10px;
  color: #409eff;
  border: 1px solid #d9ecff;
  border-radius: 4px;
  box-sizing: border-box;
  white-space: nowrap;
}

.versionTag {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  /* display: inline !important; */
}

.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #faecd8;
  color: #e6a23c;
}
.el-tag--success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}
.historyVersionMenu {
  max-height: 400px;
  overflow-y: auto;
}
/** 时区选择器 **/
#selectTimeZoneModal .modal-body {
}

#selectTimeZoneModal .list-group {
  max-height: 50vh;
  min-height: 200px;
  overflow: auto;
}
#selectTimeZoneModal .input-group-text {
  background-color: transparent;
}
