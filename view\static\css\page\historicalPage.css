* {
  margin: 0;
  padding: 0;
}

html,
body {
  width: 100%;
  height: 100%;
}

#app {
  height: 100%;
  width: 100%;
}

:root {
  --styleColor: #ed6211;
}

.flexBox {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.hitspopUp_warp {
  position: relative;
  height: 1.2rem;
  /* font-family: Arial, Helvetica, sans-serif; */
  z-index: 2;
}

.header {
  height: 1.2rem;
  line-height: 1.2rem;
  background-color: var(--styleColor);
}

.title {
  color: #fff;
  text-align: center;
  font-size: 0.55rem;
}

.blackIcon {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0.3rem;
  width: 0.8rem;
}

.hits_food {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  position: relative;
  padding: 0.5rem 0 1.4rem;
  overflow-y: scroll;
  position: relative;
}

.hits_food ::-webkit-scrollbar {
  display: none;
}

.cart_food_null {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 0.37rem;
  color: #ccc;
}

.cart_food_cell {
  /* padding: 0.2rem 0; */
  /* margin: 0 0 0.2rem 0; */
}

.cart_food_img {
  width: 1.8rem;
  height: 1.8rem;
}

.cart_info {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  padding: 0.1rem 0.5rem;
  color: rgba(0, 0, 0, 0.7);
}

.cart_food_title {
  font-size: 0.4rem;
}

.cancelOrder_title {
  color: #ccc;
  text-decoration: line-through;
}

.littleitem {
  font-size: 0.28rem;
  padding: 0.05rem 0.2rem 0.05rem 0.2rem;
}

.cart_food_priceNum {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  font-size: 0.3rem;
}

.cart_food_price {
  color: #ee0a24;
  font-size: 0.35rem;
}

.cancel_style {
  color: #ccc;
}

.cart_food_numBox {
  font-size: 0.4rem;
}

.cart_food_numVal {
  font-size: 0.37rem;
  /* line-height: 0.6rem;
  padding: 0 0.5rem; */
}

.cart_food_price_amount {
  /* font-size: 0.35rem; */
}

/* loading */
.loader {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;

  vertical-align: middle;
  z-index: 501;
}

.loader-3 .dot {
  width: 10px;
  height: 10px;
  background: var(--styleColor);
  border-radius: 50%;
  position: absolute;
  top: -webkit-calc(50% - 5px);
  top: calc(50% - 5px);
}

.loader-3 .dot1 {
  left: 0px;
  -webkit-animation: dot-jump 0.5s cubic-bezier(0.77, 0.377, 0.64, 0.28)
    alternate infinite;
  animation: dot-jump 0.5s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate
    infinite;
}

.loader-3 .dot2 {
  left: 20px;
  -webkit-animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.377, 0.64, 0.28)
    alternate infinite;
  animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate
    infinite;
}

.loader-3 .dot3 {
  left: 40px;
  -webkit-animation: dot-jump 0.5s 0.37s cubic-bezier(0.77, 0.377, 0.64, 0.28)
    alternate infinite;
  animation: dot-jump 0.5s 0.37s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate
    infinite;
}

@-webkit-keyframes dot-jump {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

@keyframes dot-jump {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

.cardBox {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  background-color: #fff;
  overflow: hidden;
  color: #303133;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.footBox {
  font-size: 0.35rem;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1.2rem;
  background-color: var(--styleColor);
  border-radius: 0.2rem 0.2rem 0 0;
  color: white;
  z-index: 2;
}

.allPrice_num {
  color: white;
  font-weight: 500;
  font-size: 0.38rem;
}

.dot_box {
  display: inline-block;
  /* margin-left: -5px;
  margin-top: -5px; */
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background: red;
  -webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.placeOrderTime {
  position: relative;
  height: 0.5rem;
  margin: 0rem auto;
}

.placeOrderTime i {
  display: block;
  height: 1px;
  background: #e1e1e1;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 100%;
}

.placeOrderTime p {
  font-size: 0.35rem;
  color: #c1c1c1;
  background: white;
  padding: 0 0.5rem;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 2;
}

.price_warp {
  position: absolute;
  right: 0.3rem;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
}

.endTime {
  position: absolute;
  left: 0.3rem;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 0.33rem;
}

.null_hits_food {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 0.5rem;
  /* color: var(--styleColor); */
  color: #ccc;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.emptyIcon {
  width: 5rem;
  padding-bottom: 0.5rem;
}
.unitPrice {
  width: 33.33%;
  font-size: 0.35rem;
  color: #ee0a24;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.footnote {
  font-size: 0.35rem;
  margin-top: 0.5rem;
  color: #ccc;

  text-align: center;
}

.noDataBox {
  font-size: 0.45rem;
  position: fixed;
  top: 50%;
  left: 50%;
  white-space: nowrap;
  transform: translate(-50%, -50%);
  color: var(--styleColor);
  z-index: 2;
}
.totalPrices,
.totalCharge {
  text-align: end;
  /* margin-right: 0.2rem; */
  /* font-size: 0.3rem; */
}
.homeIcon {
  width: 0.6rem;
  position: absolute;
  right: 0.4rem;
  top: 50%;
  transform: translateY(-50%);
}
