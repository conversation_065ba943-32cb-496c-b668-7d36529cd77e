<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :header-cell-style="{fontSize: '15px'}"
          :max-height="tableHeight"
          empty-text="No Data"
        >
          <el-table-column prop="storeNumber" label="Store Number" align="center">
            <template slot-scope="scope">{{scope.row.storeNumber}}</template>
          </el-table-column>
          <el-table-column prop="rule" label="Rules" align="center">
            <template slot-scope="scope">{{scope.row.rule}}</template>
          </el-table-column>
          <el-table-column label="Switch" align="center" width="180">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.touch"
                @change="onEditSwitch($event, scope.row)"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column prop label="Operation" align="center">
            <el-table-column width="110" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible = true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="onDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 類別Add弹窗 -->
      <template>
        <el-dialog title="Add Dialog" :visible.sync="addDialogVisible" @close="addCloseDialog">
          <el-form :model="addForm" ref="addForm" label-width="auto" :rules="rules">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="addForm.storeNumber"
                placeholder="Please enter the store number"
              ></el-input>
            </el-form-item>
            <el-form-item label="Rules" prop="rule">
              <el-input
                type="textarea"
                v-model="addForm.rule"
                :autosize="{ minRows: 3, maxRows: 8}"
                placeholder="Format ：F:AA01+M:AA01=F:AA02;"
              ></el-input>
            </el-form-item>
            <el-form-item label="Switch" prop="touch">
              <el-switch v-model="addForm.touch"></el-switch>
            </el-form-item>
            <div class="dialog_footer">
              <el-button type="primary" @click="addDialogVisible=false">Cancel</el-button>
              <el-button style="margin-right: 8px" @click="onAdd('addForm')">Add</el-button>
            </div>
          </el-form>
        </el-dialog>
      </template>
      <!-- 单元格编辑弹窗 -->
      <template>
        <el-dialog title="Edit Dialog" :visible.sync="editDialogVisible">
          <el-form :model="editForm" ref="editForm" label-width="auto" :rules="rules">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="editForm.storeNumber"
                placeholder="Please enter the store number"
              ></el-input>
            </el-form-item>
            <el-form-item label="Rules" prop="rule">
              <el-input
                type="textarea"
                v-model="editForm.rule"
                :autosize="{ minRows: 3, maxRows: 8}"
                placeholder="Format ：F:AA01+M:AA01=F:AA02;"
              ></el-input>
            </el-form-item>
            <el-form-item label="Switch" prop="touch">
              <el-switch v-model="editForm.touch"></el-switch>
            </el-form-item>
            <div class="dialog_footer">
              <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
              <el-button style="margin-right: 8px" @click="subEdit('editForm')">Submit</el-button>
            </div>
          </el-form>
        </el-dialog>
      </template>
      <!-- 单元格删除弹窗 -->
    </div>
    <script>
      const checkRules = (rule, value, callback) => {
        let reg =
          /^([FM][:][a-zA-Z0-9]+(\(D\))?([+][FM][:][a-zA-Z0-9]+(\(D\))?)*[=][FM][:][a-zA-Z0-9]+[;]\n?)*([FM][:][a-zA-Z0-9]+(\(D\))?([+][FM][:][a-zA-Z0-9]+(\(D\))?)*[=][FM][:[a-zA-Z0-9]+[;]?\n?)$/
        if (!reg.test(value)) {
          return callback(new Error("Please enter valid rules"))
        } else {
          return callback()
        }
      }
      var app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          domain: sessionStorage.getItem("domain"),
          addDialogVisible: false,
          delDialogVisible: false,
          editDialogVisible: false,
          rules: {
            storeNumber: [
              {
                required: true,
                message: "Please enter the store number",
                trigger: "change"
              }
            ],
            rule: [
              {
                required: true,
                message: "Please enter rules",
                trigger: "change"
              },
              {
                validator: checkRules,
                message: "Please enter valid rules",
                trigger: "change"
              } // 自定义校验
            ]
          },
          addForm: {
            storeNumber: "",
            rule: "",
            touch: true
          },
          editForm: {
            id: "",
            storeNumber: "",
            rule: "",
            touch: ""
          },
          tableData: []
        },
        created() {
          this.getData()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整
        },
        methods: {
          getData() {
            $.get({
              url: "../../manager_mergeFood/getAll",
              dataType: "json",
              success: res => {
                if (res.statusCode == 200) {
                  console.log(res, "初始请求数据")
                  this.tableData = res.data
                } else {
                  this.$message.error("Request failed, please try again")
                }
              },
              error: error => {
                this.$message.error("Request failed, please try again")
              }
            })
          },
          onAdd(addForm) {
            let data = {
              ...this.addForm,
              domain: this.domain
            }
            this.$refs[addForm].validate(valid => {
              if (valid) {
                $.post({
                  url: "../../manager_mergeFood/addList",
                  data,
                  dataType: "json",
                  success: res => {
                    if (res.statusCode == 200) {
                      this.getData()
                      this.$message.success("Successfully added！")
                      this.addDialogVisible = false
                    } else {
                      this.$message.error("Fail to add, please try again")
                    }
                  },
                  error: error => {
                    // console.log(res);
                    this.$message.error("Fail to add, please try again")
                  }
                })
              } else {
                this.$message.error("Please enter complete information")
              }
            })
          },
          onEdit(index, row) {
            this.editDialogVisible = true
            let { id, storeNumber, rule, touch } = row
            this.editForm = {
              id,
              storeNumber,
              rule,
              touch
            }
          },
          subEdit(editForm) {
            let data = {
              ...this.editForm,
              domain: this.domain
            }
            this.$refs[editForm].validate(valid => {
              if (valid) {
                $.post({
                  url: "../../manager_mergeFood/updateOne ",
                  data,
                  dataType: "json",
                  success: res => {
                    if (res.statusCode == 200) {
                      this.$message.success("Edit success！")
                      this.editDialogVisible = false
                    } else {
                      this.$message.error("Fail to edit, please try again")
                    }
                    this.getData()
                  },
                  error: error => {
                    this.$message.error("Fail to edit, please try again")
                  }
                })
              } else {
                this.$message.error("Please enter complete information")
              }
            })
          },
          onDel(index, row) {
            let { id } = row
            $.post({
              url: "../../manager_mergeFood/deleteOne",
              data: { id },
              dataType: "json",
              success: res => {
                if (res.statusCode == 200) {
                  this.$message.success("Successfully delete")
                } else {
                  this.$message.error("Fail to delete, please try again")
                }
                this.getData()
              },
              error: error => {
                // console.log(res);
                this.$message.error("Fail to delete, please try again")
              }
            })
          },
          // 开关触发
          onEditSwitch($event, row) {
            console.log(row)
            let { id, storeNumber, rule, touch } = row
            let data = {
              id,
              storeNumber,
              rule,
              touch,
              domain: this.domain
            }
            $.post({
              url: "../../manager_mergeFood/updateOne ",
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode == 200) {
                  // console.log(res);
                  this.$message.success("Edit success！")
                  this.editDialogVisible = false
                } else {
                  this.$message.error("Fail to edit, please try again")
                }
                this.getData()
              },
              error: error => {
                this.$message.error("Fail to edit, please try again")
              }
            })
          },
          // 上传对话框关闭事件
          addCloseDialog() {
            // 点击关闭 数据重置
            this.$refs["addForm"].resetFields()
            // this.$refs.upload.clearFiles();
            // this.addForm = {
            //   type_name: '',
            //   sign: 'false',
            //   restrictions1: '',
            //   restrictions2: '',
            //   store_number: '',
            // };
          }
        }
      })
    </script>
  </body>
</html>
