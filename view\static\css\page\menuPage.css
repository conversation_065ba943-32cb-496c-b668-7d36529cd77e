@import "../../vendor/Roboto.css";
/* 字体图标 */
@import "../../vendor/materialdesignicons.css";
@import "../../vuetify/initVuetify.css";
@import "./birthdayCard.css";
@import "./orderStopDia.css";
@import "./verticalOrderLayout.css";
@import "./member-center.css";
/* 国旗图标 */
@import "../../vendor/flagIcons/flag-icons.css";
/* 测试台时间模拟 */
@import "./testTimeSimulation.css";
* {
  margin: 0;
  padding: 0;
  /* -webkit-overflow-scrolling: touch; */
}
/* html,
body {
  width: 100%;
  height: 100%;
} */

:root {
  --nabeColor: #c7b14c;
  --ppgColor: #770239;
  --szyColor: #297d2f;
  --bzzColor: #415047;
  --styleColor: #ed6211;
}
/* 关联:支付邮箱最后提示空是否提交 */
body .layui-layer-dialog {
  min-width: 60% !important;
}

.layui-layer-content {
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}
/* 取消a标签点击时候的背景颜色 */
a {
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  -webkit-user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none;
}

.clearfix {
  zoom: 1;
}

.clearfix:after {
  content: "";
  /*设置内容为空*/
  height: 0;
  /*高度为0*/
  line-height: 0;
  /*行高为0*/
  display: block;
  /*将文本转为块级元素*/
  visibility: hidden;
  /*将元素隐藏*/
  clear: both;
  /*清除浮动*/
}

#app {
  /* width: 100%;
  height: 100%; */
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  -webkit-overflow-scroll: touch;
  font-family: Arial, Helvetica, sans-serif;
  box-sizing: content-box;
}

.fixed {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.app_container {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.changeTxt:hover {
  background-color: var(--styleColor) !important;
}
.header_warp {
  /* padding: 0.5rem 0 0 0; */
  background-color: var(--styleColor);
}

.table_info {
  display: flex;
  align-items: center;
  position: relative;
  font-size: 0.35rem;
  color: #fff;
  height: 1.2rem;
  /* height: 1.5rem; */
  /* display: flex;
  align-items: center; */
}
.header-tableIcon {
  width: 5.5vw;
}
.header-tableNumber {
  padding: 0 1vw;
}
.table_info_num {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  box-sizing: content-box;
}
.topNavTitle {
  max-width: 4rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tab_warp {
  position: relative;
  display: flex;
  overflow: auto;
  background-color: #e4e7e9;
  /* padding-bottom: 0.2rem; */
  padding: 0.4rem 0 0rem;
  /* height: 1.3rem;
  align-items: center; */
  /* border-top: 1px solid #fff; */
  /* scroll-behavior: smooth; */
  flex-shrink: 0;
  overscroll-behavior: contain; /* <<< 关键：防止容器滚动“溢出”影响外层页面 */
}
/* .tab_warp::-webkit-scrollbar {
  display: none;
} */
.mobile_menuPage .tab_warp::-webkit-scrollbar {
  display: none;
}

.tab_cell {
  flex: 0 0 auto;
  /* width: 1.8rem; */
  min-width: 1.8rem;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  padding: 0.2rem 0.35rem;
  /* background-color: var(--styleColor); */
  color: #666874;
  font-weight: normal;
  font-size: 0.39rem;
  margin: 0 0.1rem;
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
  /* -webkit-box-shadow: -5px 5px 10px -4px #571b23, 5px 5px 10px -4px #571b23;
  box-shadow: -5px 5px 10px -4px #571b23, 5px 5px 10px -4px #571b23; */
  /* -webkit-box-shadow: -5px 5px 10px -4px #a68400, 5px 5px 10px -4px #a68400;
  box-shadow: -5px 5px 10px -4px #a68400, 5px 5px 10px -4px #a68400; */
}

.tab_cell:first-child {
  margin-left: 0rem;
}

.tab_cell:last-child {
  margin-right: 0rem;
}

/*剩余库存*/
.inventory-info {
  font-size: 0.3rem !important;
  color: indianred;
}

/* 售罄 */
.content_warp_one .soldOut_warp {
  background-color: rgba(140, 143, 152, 0.5);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 2.5rem;
  border-radius: 0.2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}
.hotSale_warp_cell_top .soldOut_warp,
.content_warp_two .soldOut_warp,
.content_warp_six .soldOut_warp,
.content_warp_third .soldOut_warp {
  background-color: rgba(140, 143, 152, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  border-radius: 0.3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}
.food-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow-y: auto;
  background-color: #f7f8fa;
  padding: 0 0.4rem 0.4rem;
}
.infLoop {
}
.noInfLoop {
  padding-top: 0.4rem;
}
.infLoop > div:first-child {
  padding-top: 0.4rem;
}

.foodCard {
  /* border: 1px solid; */
}
.foodCard-title::before {
  content: "------";
}
.foodCard-title::after {
  content: "------";
}
.foodCard-title {
  font-size: 0.45rem;
  text-align: center;
  padding: 0.5rem 0;
  color: var(--styleColor);
}
.foodCard-title div.dot {
  display: inline-block;
  width: 0.09rem;
  height: 0.09rem;
  border-radius: 50%;
  border: 1px solid var(--styleColor);
  transform: translateY(-0.06rem);
}
.foodCard-banner {
  width: 100%;
  padding: 0.25rem 0;
}

.food-container .foodCard:first-child .foodCard-banner {
  /* 在这里添加您需要设置的样式 */
  padding-top: 0;
}
.food-container .foodCard:first-child .foodCard-title {
  /* 在这里添加您需要设置的样式 */
  padding-top: 0;
}

.foodCard-banner img {
  width: 100%;
  max-height: 2rem;
}
.tab_active {
  background-color: #f7f8fa;
  font-weight: 550;
  color: var(--styleColor);
  box-shadow: -5px -5px 10px -4px#9698A4, 5px -5px 10px -4px#9698A4;
}
.content_warp_zero .soldOut_warp {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 90vw;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(140, 143, 152, 0.5);
  border-radius: 13px;
  z-index: 2;
}

.content_warp_zero {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* padding: 0.2rem 0; */
  /* overflow: scroll; */
  background-color: #f7f8fa;
}

.content_warp_zero .soldOut_warp .soldOutLogo {
  width: 80%;
  /* height: 5rem; */
  flex-shrink: 0;
}
.content_warp_zero .disableBox {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 90vw;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  z-index: 2;
  /* border-radius: 13px; */
}
.content_warp_zero .contentCellStyle {
  background-color: #fff;
  border-radius: 0.3rem;
  overflow: hidden;
  position: relative;
  margin-bottom: 0.3rem;
}
.content_warp_zero .contentCellStyle:nth-last-child(1) {
  margin-bottom: 0rem;
}

.content_warp_zero .imgBox {
  overflow: hidden;
}
.content_warp_zero .foodTex_box .layout1 {
  max-width: 100%;
  height: calc(100vw * 0.59);
  display: block;
  margin: auto;
  /* height: 6rem; */
}
.content_warp_zero .foodTex_box {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contentCellStyle .food_txtAndPrice_zero {
  /* margin-right: auto;
  margin-left: 0.5rem; */
  margin: 0.05rem auto 0.05rem auto;
  width: 90vw;
}
.food_txtAndPrice_zero .food_text {
  text-align: left;
  font-size: 0.35rem;
}
.contentCellStyle .addBtnZero {
  bottom: 1.8rem;
  right: 0.45rem;
  width: 1rem;
  height: 1rem;
}
.food_txtAndPrice_zero .price_warp {
  text-align: left;
  text-indent: 0.2rem;
  font-size: 0.35rem;
}
.content_warp_one .soldOutLogo {
  width: 1.5rem;
}

.content_warp_one .contentCellStyle {
  position: relative;
}
.content_warp_one .food_txtAndPrice_one .price_warp {
  padding: 0rem 0.2rem 0rem;
  text-align: left;
}
.food_txtAndPrice_two .food_txtAndPrice_one .price_warp {
  padding: 0rem 0.2rem 0rem;
  text-align: left;
}

/* 三行布局 */
.content_warp_third {
  display: grid;
  /* 每行3个元素 */
  grid-template-columns: repeat(3, 1fr);

  grid-row-gap: 0.4rem; /* 行间距 */
  grid-column-gap: 0.3rem; /* 列间距 */
}

.content_warp_third .contentCellStyle {
  position: relative;
  background-color: #fff;
  border-radius: 0.3rem;
  overflow: hidden;
  box-shadow: 0 8px 12px #ebedf0;
}

.content_warp_six .soldOutLogo,
.content_warp_third .soldOutLogo {
  /* width: 2.5rem; */
  width: 100%;
}

/* 两行布局 */
.content_warp_two {
  box-sizing: border-box;

  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-row-gap: 0.4rem; /* 行间距 */
  grid-column-gap: 0.5rem; /* 列间距 */
}
.tran {
  position: relative;
  transition: all 0.5s ease-in-out;
}
.content_warp_two .contentCellStyle {
  position: relative;

  background-color: #fff;
  border-radius: 0.3rem;
  overflow: hidden;
  box-shadow: 0 8px 12px #ebedf0;
}

.content_warp_two .soldOutLogo {
  width: 100%;
}

/* 一行布局 */
.content_warp_one {
  flex: 1;
  box-sizing: border-box;
}

.content_warp_one .contentCellStyle {
  position: relative;
  margin-bottom: 0.3rem;
}
.content_warp_one .contentCellStyle:nth-last-child(1) {
  margin-bottom: 0rem;
}
.content_warp_one .foodTex_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content_warp_one .foodTex_box .food_img_warp {
  background-color: #fff;
  width: 2.5rem;
  border-radius: 0.2rem;
  overflow: hidden;
}
.content_warp_one .food_text {
  text-align: left;
  font-size: 0.33rem;
  order: -1;
}

.content_warp_one .food_img {
  height: 1.8rem;
  max-width: 100%;
  display: block;
  margin: auto;
}

.content_warp_two .food_img {
  height: calc(100vw * 0.47 * 0.6667);
  max-width: 100%;
  display: block;
  margin: auto;
}
/*定位解决3: 2比例: div.food_img_warp 添加.image-wrapper */
.image-wrapper {
  width: 100%; /* 确保包裹元素的宽度与图片宽度一致 */
  display: inline-block; /* 或者使用其他适当的 display 属性，使得 ::before 生效 */
  position: relative; /* 设置相对定位，以便图片能正确地覆盖 */
}

.image-wrapper::before {
  content: "";
  display: block;
  padding-top: 66.66%; /* 2/3 的宽度，对应 3:2 的宽高比 */
}

.image-wrapper .food_img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* 确保图片覆盖整个容器，同时保持宽高比 */
}

/*比例是 3:2 */
.content_warp_six .food_img,
.content_warp_third .food_img {
  max-width: 100%;
  display: block;
  margin: auto;
  height: calc(100vw * 0.3 * 0.6667);
  /* border-radius: 0.3rem 0.3rem 0 0; */
}
.content_warp_six .food_img {
  height: 2.5rem;
}

.content_warp_one .addFoodBtn {
  width: 0.5rem;
  height: 0.5rem;
  bottom: 0.05rem;
  right: 0.1rem;
}

.content_warp_two .addFoodBtn {
  top: calc(70vw * 0.47 * 0.6667);
  right: 0.15rem;
  width: 0.7rem;
  height: 0.7rem;
  font-size: 0.7rem;
}
.content_warp_six .addFoodBtn,
.content_warp_third .addFoodBtn {
  top: calc(65vw * 0.3 * 0.6667);
  right: 5%;
  width: 0.55rem;
  height: 0.55rem;
}
.content_warp_six .addFoodBtn {
  top: 1.8rem;
}
.food_img_warp {
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  /* width: 100%;
  height: 100%; */
  height: 100%;
  width: 100%;
}
.img_tag {
  display: block;
}
.content_warp_zero .img_tag {
  height: 1.8rem;
}
.content_warp_zero .img_tag:not(:last-child) {
  margin-right: 0.2rem;
}
.content_warp_one .img_tag {
  height: 0.6rem;
}
.content_warp_two .img_tag {
  height: 1.1rem;
}
.content_warp_third .img_tag {
  height: 0.65rem;
}
.content_warp_six .img_tag {
  height: 0.85rem;
}
.img_tag_topLeft {
  position: absolute;
  top: 0rem;
  left: 0rem;
  display: flex;
}
.img_tag_topRight {
  position: absolute;
  top: 0rem;
  right: 0.05rem;
  display: flex;
  z-index: 1;
}
.img_tag_bottomLeft {
  position: absolute;
  bottom: 0rem;
  left: 0rem;
  display: flex;
}
/* 六栏布局 */
.content_warp_six {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  /* gap: 0.3rem;取消行列0.3rem间隔解决grid布局底部部分浏览器无法兼容padding  */
  grid-column-gap: 0.3rem;
  grid-auto-flow: dense;
  padding: 0.4rem 0.4rem 0;
  /* margin-bottom: 0.1rem; */
  /* padding: 0.4rem; */
  overflow-y: scroll;
  align-content: start;
}

.content_warp_six .contentCellStyle {
  position: relative;
  background-color: #fff;
  border-radius: 0.3rem;
  margin-bottom: 0.3rem; /* 设置每个子元素底部间隔解决grid布局padding失效 */
  box-shadow: 0 8px 12px #ebedf0;
  overflow: hidden;
}
/* 选择倒数第1个到倒数第6个 */
/* .content_warp_six .contentCellStyle:nth-last-child(-n + 6):nth-last-child(n + 1) {
  margin-bottom: 0.4rem;
} */
.content_warp_six .bigFood {
  grid-column: span 2;
  grid-row: span 2;
  /* min-height: 5.5rem !important; */
}

.content_warp_six .foodTex_box {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.content_warp_six .foodTex_box .food_txtAndPrice_one {
  order: unset;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 0.8rem !important;
}
.content_warp_six .bigFood .addFoodBtn {
  top: 3.1rem;
  right: 0.2rem;
  width: 0.7rem;
  height: 0.7rem;
}
.content_warp_six .bigFood .food_img {
  height: 4rem;
}
.content_warp_six .bigFood .food_txtAndPrice_one .food_text {
  font-size: 0.5rem;
}
.content_warp_six .bigFood .food_txtAndPrice_one .price_warp {
  font-size: 0.45rem;
}
.content_warp_six .bigFood .soldOutLogo {
  width: 4.5rem;
}
.baseaddFoodBtn {
  position: absolute;
  /* bottom: 38%; */
  right: 0.13rem;
  /* width: 0.6rem;
  height: 0.6rem; */
  border-radius: 50%;
  background-color: #fff;
  color: var(--styleColor);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.6rem;
  z-index: 1;
  border: 1px solid var(--styleColor);
}

.food_txtAndPrice_one {
  order: -1;
}

.food_text {
  /* width: 100%;  */
  /* height: 50%; */
  /* height: 0.5rem; */
  margin: 0.1rem 0.2rem;
  font-size: 0.3rem;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.footer_warp {
  position: relative;
  z-index: 4;
  display: flex;
  height: 1.3rem;
  color: #fff;
}

.history_car_order {
  background-color: rgba(40, 67, 79, 0.9);
  /* background-color: var(--styleColor); */
}

.history_car_fty {
  background-color: rgba(40, 67, 79, 0.9);
}

.history_car {
  flex: 1;
  font-size: 0.38rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.shop_car {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 0.38rem;
  /* background-color: rgba(40, 67, 79, 0.9); */
  background-color: var(--styleColor);
}

.shopNum_warp {
  width: 0.6rem;
  height: 0.6rem;
  line-height: 0.6rem;
  border-radius: 50% 50%;
  background: rgba(255, 255, 0, 1);
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  color: rgba(244, 83, 91, 1);
  position: absolute;
  top: 0;
  right: 10%;
  text-align: center;
  font-size: 0.45rem;
  font-style: initial;
}

.cartPromptText {
  text-align: center;
  margin-bottom: 0.2rem;
  color: #797977;
  padding: 0 0.2rem;
}
.cartPromptText .normal_font {
  font-size: 0.3rem;
}
.cartPromptText .enlarge_font {
  font-size: 0.36rem;
}

/* .send_single {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--styleColor);
  font-size: 0.5rem;
  position: relative;
} */

/* 按钮闪烁css3 */
.subAnimated {
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.subAnimated.subInfinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.subFlash {
  -webkit-animation-name: flash;
  animation-name: flash;
}

@-webkit-keyframes flash {
  0%,
  100%,
  50% {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0.4;
  }
}

@keyframes flash {
  0%,
  100%,
  50% {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0.4;
  }
}

/* 向下手指提示点击 */
.down_warp_icon {
  /* height: 0.7rem; */
  width: 1rem;
}

.down_warp {
  position: absolute;
  z-index: 4;
  top: -0.6rem;
  /* right: 1rem; */
  left: calc(50% - 0.5rem);
  -webkit-animation: down-warp-icon 1.5s linear infinite;
  animation: down-warp-icon 1.5s linear infinite;
}

@-webkit-keyframes down-warp-icon {
  25% {
    -webkit-transform: translateY(-10px);
  }

  50%,
  100% {
    -webkit-transform: translateY(0);
  }

  75% {
    -webkit-transform: translateY(10px);
  }
}

@keyframes down-warp-icon {
  25% {
    transform: translateY(-10px);
  }

  50%,
  100% {
    transform: translateY(0);
  }

  75% {
    transform: translateY(10px);
  }
}

/*  */
.cartImage {
  width: 0.6rem;
  padding-bottom: 0.1rem;
}

.bottomSubmitIcon {
  width: 0.52rem;
  padding-bottom: 0.1rem;
}
/* 购物车页 */

.cart_warp {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #fff;
  /* display: none; */
  z-index: 520;
  display: flex;
  flex-direction: column;
}

.cart_info_warp_header,
.send-order-view-header {
  width: 100%;
  background-color: var(--styleColor);
  position: relative;
  font-size: 0.35rem;
  color: #fff;
  height: 1.2rem;
  display: flex;
  align-items: center;
}
.pc .cart_info_warp_header,
.pc .send-order-view-header {
  font-size: 16px;
}
/* 购物车配送信息 */
.cart_info_warp_delivery_icon {
  width: 0.5rem;
  padding-right: 0.1rem;
  vertical-align: top;
}
.cart_info_warp_delivery {
  margin-top: 0.3rem;
  border: 0.01rem solid #afb5bb;
  font-size: 0.35rem;
  padding: 0.2rem 0.4rem;
  color: #828282;
  border-radius: 0.1rem;
}
.cart_info_warp_delivery_address {
  display: flex;
  align-items: center;
}
.cart_info_warp_delivery_time {
  /*padding-top: 0.3rem;*/
  display: flex;
  justify-content: space-between;
  /*border-top: 0.01rem solid #afb5bb;*/
}
.cart_info_warp_delivery_time_left {
  display: flex;
  align-items: center;
}
.send_single {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--styleColor);
  font-size: 0.5rem;
  border-radius: 0.2rem;
  color: #fff;
  margin: 0 0.2rem 0.3rem;
  /*padding: 0.25rem 0;*/
  height: 1.1rem;
}

.disabledSend {
  /* cursor: pointer; */
  cursor: not-allowed;
  opacity: 0.5;
}

.cart_food_box {
  flex: 1;
  /* min-height: 2rem; */
  padding: 0rem 0.4rem 0.4rem 0.4rem;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cart_food_null {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.4rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #ccc;
}
.emptyOrderICon {
  width: 5rem;
}

.cart_food_warp {
  /* padding-bottom: 1.2rem;
  border-bottom: 1px solid rgb(220, 220, 220); */
}

.your_cart {
  /* margin: 0.2rem 0rem 0.8rem; */
  /* margin: 0.8rem 0; */
  font-size: 0.5rem;
  color: var(--styleColor);
  display: flex;
}
.your_cart > span {
  margin-right: auto;
}
.cart_food_warp_content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  /* height: 100%; */
}
.cart_food_cell {
  display: flex;
  margin: 0 0 0.2rem 0;
  padding: 0 0.2rem;
}

.cart_food_img {
  flex-shrink: 0;
  width: 1.8rem;
}

.cart_food_img_cell {
  width: 100%;

  /* height: 100%; */
  height: auto;
  background-size: cover;
  border-radius: 0.2rem;
  max-height: 1.8rem;
  -webkit-user-drag: none;
}

.cart_info {
  flex: 1;

  display: flex;

  justify-content: space-between;
  margin-bottom: 0.3rem;
  /* margin-left: 0.3rem; */
  color: rgba(0, 0, 0, 0.7);
  padding-left: 0.2rem;
}

.info_left {
  padding-right: 0.4rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.info_right {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.cart_food_priceNum {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  font-size: 0.3rem;
}
.auto-cutlery-item .cart_food_priceNum {
  margin-left: auto;
  margin-right: 0.5rem;
  align-items: center;
}
.auto-cutlery-item .cart_info {
  margin-bottom: 0;
}
.additionalDisConfTip {
  padding-top: 0.1rem;
  padding-left: 0.3rem;
  color: #ef5350;
}
.cart_food_title {
  font-size: 0.35rem;
}

.cart_food_sideDish {
  flex: 1;
}

.littleitem {
  font-size: 0.28rem;
  word-break: break-word;
}
.littleitem-inXi {
  margin-left: 0.1rem;
}
.littleitem-endXi {
  margin-left: 0.2rem;
}
.totalPrice,
.cart_food_price {
  color: #ee0a24;
}

.cart_food_price_amount {
  font-size: 0.45rem;
}

.cart_food_numBox {
  display: flex;
  margin-right: 0.1rem;
}

.cart_food_numVal {
  font-size: 0.45rem;
  line-height: 0.6rem;
  width: 1rem;
  text-align: center;
  /* padding: 0 0.5rem; */
}

.cart_add_btn,
.cart_del_btn {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 0.5rem;
  width: 0.6rem;
  height: 0.6rem;
  background-color: var(--styleColor);
  color: #fff;
}

.cart-soldout {
  font-size: 0.25rem;
  color: indianred;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  margin-right: 3px;
}
.cart_food_cell:has(.cart-soldout) :not(.cart-soldout, .cart_remove_btn) {
  /*background-color: rgba(200,200,200,.3);*/
  /*border: 1px solid silver;*/
  /*border-radius: 5px;*/
  color: slategrey;
}
.cart_remove_btn {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 0.4rem;
  width: 0.6rem;
  height: 0.6rem;
  background-color: red;
}
.info_right:has(.cart_remove_btn) .cart_add_btn {
  /*pointer-events: none;*/
  cursor: not-allowed;
  background-color: #cccccc;
}

/* 蒙层 （后期再统一写）*/

.mongolia {
  display: none;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 2;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.mongolia2 {
  display: none;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 500;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* 小球抛物线 */
.shop {
  display: flex;
  flex-wrap: wrap;
  /* position: fixed;
  top: 300px;
  left: 400px; */
}

.ball {
  position: fixed;
  left: 70%;
  bottom: 1.3rem;
  z-index: 200;
  transition: all 0.5s cubic-bezier(0.49, -0.29, 0.75, 0.41);
  /*贝塞尔曲线*/
  /* 解决闪烁问题 */
  -webkit-transform: translate3d(0, 0, 0);
  /*开启硬件加速*/
  -webkit-backface-visibility: hidden;
  /*元素旋转时隐藏背面*/
  -webkit-transform-style: preserve-3d;
  /*保留3D空间*/
}

.inner {
  /* width: 16px;
  height: 16px; */
  border-radius: 50%;
  /* background-color: rgb(0, 160, 220); */
  transition: all 0.5s linear;
}

/* 对应food弹窗 */
.food_info_warp {
  /* display: none; */
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;

  display: flex;
  flex-direction: column;
  background-color: #fff;
  z-index: 521;
  font-size: 0.45rem;
}

.food_info_warp_header {
  height: 1.2rem;
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  z-index: 1005;
  background-color: var(--styleColor);
}

/* .food_info_warp_header_black {
  padding-left: 0.3rem;
} */

.blackIcon {
  width: 0.8rem;
  padding-left: 0.4rem;
}

.food_info_warp_header_title {
  text-align: center;
  width: 200px;
  color: #fff;
  white-space: nowrap;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
}

/* 跑马灯效果 */
.merquee-txt {
  display: inline-block;
  padding-left: 100%;
  /* 从右至左开始滚动 */
  -webkit-animation: marqueeTransform 7s linear infinite;
  animation: marqueeTransform 7s linear infinite;
}

/* 跑马灯动画 */
@-webkit-keyframes marqueeTransform {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(-100%, 0);
  }
}

@keyframes marqueeTransform {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(-100%, 0);
  }
}

.food_info_warp_img {
  /* float: left; */
  width: 3rem;
  /* height: 3rem; */
  box-shadow: 4px 4px 5px #c7c7c8;
  /* margin: 0 0.4rem 0.1rem 0; */
  border-radius: 0.2rem;
  /* object-fit: cover; */
}

.food_info_warp_content {
  /* position: fixed;
  left: 0;
  right: 0;
  top: 1rem; */
  flex: 1;
  bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 1.825rem 0.36rem 0.625rem;
}
.rolling-food-fixed-wrap {
  position: sticky;
  left: 0;
  top: 0;
}
.food_info_warp_content_top {
  padding-bottom: 0.5rem;
  padding-left: 0.1rem;
  display: flex;
  flex-direction: row;
}
.rolling-food-wrap {
  /*width: 103%;!*多出3%为遮掩大图的右阴影*!*/
  position: relative;
  z-index: 4;
  transform: translateY(0);
  background-color: white;
}

.fold-food-type-item > .fold-item-list-warp {
  display: grid;
  grid-template-rows: 0fr;
  transition: grid-template-rows 100ms;
  overflow: hidden;
  border-bottom: 1px solid #cccccc;
}
.fold-food-type-item:last-child > .fold-item-list-warp {
  border-bottom: none;
}
.fold-food-type-item label {
  cursor: pointer;
  /*transition: grid-template-rows 0.2s ease-out ;*/
  position: relative;
}
:checked + .fold-food-type-item label::after {
  transform: scaleY(-1);
}
:checked ~ .fold-item-list-warp {
  grid-template-rows: 1fr;
  border-bottom: none;
}

.fold-item-list-warp .infoPoints_content {
  min-height: 0;
}
.fold-food-type-item .infoPoints_content {
  padding-bottom: 0;
}
.fold-food-type-item input:checked ~ .fold-item-list-warp {
  overflow: visible;
}

.fold-food-type-item .expand-icon {
  display: flex;
  width: 0.6rem;
  height: 0.6rem;
  margin-left: auto;
  margin-right: 0.1rem;
  transition: transform 0.1s ease-in-out;
}
.fold-food-type-item input:checked ~ label .expand-icon {
  transform: rotate(180deg);
}
.fold-food-type-item label .selected-list {
  display: flex;
  flex-direction: column;
  font-size: 0.3rem;
  margin-top: -0.35rem;
  margin-bottom: 0.2rem;
}
.fold-food-type-item label .selected-list p::before {
  content: "•";
  color: var(--styleColor);
  position: absolute;
  left: 0;
}
.fold-food-type-item label .selected-list p {
  position: relative;
  width: 100%;
  height: 0.45rem;
  padding-left: 0.25rem;
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.fold-food-type-item input:checked ~ label .selected-list {
  display: none;
}

.rolling-food-title {
  font-weight: 550;
  font-size: 0.45rem;
  /*box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;*/
  height: 1.2rem;
  display: flex;
  align-items: center;
  position: sticky;
  top: -1.825rem;
  left: 0;
  z-index: 9999;
  background-color: white;
}
.food_info_img_Tag {
  position: relative;
  margin: 0 0.4rem 0.1rem 0;
  width: auto;
}

.food_info_img_Tag .img_tag {
  height: 0.75rem;
}
.food_info_warp_content_products {
  font-size: 0.4rem;
  font-family: microsoft yahei, arial;
}
.food-big-pic-content {
  flex-direction: column;
}
.food_info_warp_content_top.food-big-pic-content {
  padding-left: 0;
}
.food-big-pic-content .food_info_img_Tag {
  /* width: 98%; */
  margin-right: 0;
}
.food-big-pic-content .img_tag {
  height: 1.5rem;
}
/* 保留字体格式标签 */
.pre_style {
  font-size: 0.3rem;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  font-family: Arial, Helvetica, sans-serif;
}

.food_info_warp_content_shopNum {
  border-top: 1px solid #ccc;
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0rem;
}

.shopNumTitle {
  font-size: 0.35rem;
  color: var(--styleColor);
}

.food_info_warp_footer {
  /* position: fixed;
  bottom: 0;
  left: 0;
  right: 0; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.joinCartBtn {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--styleColor);
  font-size: 0.5rem;
  border-radius: 0.2rem;
  color: #fff;
  margin: 0 0.2rem 0.3rem;
  padding: 0.25rem 0;
}

/* 细项 */
.foodDialog_infoPoints_lable,
.infoPoints_lable {
  padding: 0.3rem 0rem 0.5rem;
  font-size: 0.35rem;
  /* border-top: 1px solid #ccc; */
  color: var(--styleColor);
}

/* 细项删除Icon */
.delXiItem_warp {
  margin-right: 0.15rem;
  width: 0.5rem;
  height: 0.5rem;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delXiItem_icon {
  width: 0.35rem;
}

/* 细项数量容器 */
.xiNum_warp {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #fff;
  color: #ee0a24;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ccc;
}

.infoPoints_addicon {
  position: absolute;
  right: -0.25rem;
  top: -0.25rem;
  width: 0.5rem;
  z-index: 1000;
}

.infoPoints_content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  /* overflow: auto; */
  padding-bottom: 0.3rem;
  color: #666;
}
/* <div class="food_disable_box"></div> */

.infoPoints_content_cell {
  position: relative;
  display: flex;
  flex-direction: column;
  /* width: 45%; */
  margin-bottom: 0.35rem;
}
.food_disable_box {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #b3b3b3;
  z-index: 100;
  border-radius: 5px;
}
.oneColumnList {
  width: 100% !important;
}
/* 详情List栏目长度 */
.twoColumnList {
  width: 45%;
}

.detailTaste {
  font-size: 0.25rem;
  margin-bottom: 0.1rem;
  min-height: 0.35rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.defaultlist {
  width: 45%;
}

.default_infoPoints {
  font-size: 0.3rem;
  display: flex;
  align-items: center;
  border: 1px solid #e6e6e6;
  /* min-height: 0.5rem; */
  border-radius: 4px;
  padding: 0.1rem 0.18rem;
  position: relative;
  flex: 1;
}

.xi_Title {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 0.6rem;
  z-index: 100;
}

.infoPoints_select {
  position: relative;
  color: #fff;
  background-color: var(--styleColor);
  z-index: 1;
}

.infoPoints_noselect {
  color: #666;
  background-color: #fff;
}

.foodDivider {
  border-top: 1px solid #ccc;
}

/* [v-cloak] {
  display: none !important;
} */
/* loading三个小球 */

.loader {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  vertical-align: middle;
  z-index: 501;
}

.loader-3 .dot {
  width: 10px;
  height: 10px;
  background: var(--styleColor);
  border-radius: 50%;
  position: absolute;
  top: calc(50% - 5px);
}

.loader-3 .dot1 {
  left: 0px;
  -webkit-animation: dot-jump 0.5s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
}

.loader-3 .dot2 {
  left: 20px;
  -webkit-animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
}

.loader-3 .dot3 {
  left: 40px;
  -webkit-animation: dot-jump 0.5s 0.4s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s 0.4s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
}

@-webkit-keyframes dot-jump {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-15px);
  }
}

@keyframes dot-jump {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-15px);
  }
}

/* 向左右提示 */

.toRight_img {
  position: absolute;
  top: 2.57rem;
  right: 0.4rem;
  z-index: 2;
  -webkit-animation: bounce-up 1.4s linear infinite;
  animation: bounce-up 1.4s linear infinite;
  height: 0.6rem;
}

.toLeft_img {
  position: absolute;
  top: 2.57rem;
  left: 0.4rem;
  z-index: 1;
  -webkit-animation: bounce-up 1.4s linear infinite;
  animation: bounce-up 1.4s linear infinite;
  height: 0.6rem;
}

@-webkit-keyframes bounce-up {
  25% {
    -webkit-transform: translateX(10px);
  }

  50%,
  100% {
    -webkit-transform: translateX(0);
  }

  75% {
    -webkit-transform: translateX(-10px);
  }
}

@keyframes bounce-up {
  25% {
    transform: translateX(10px);
  }

  50%,
  100% {
    transform: translateX(0);
  }

  75% {
    transform: translateX(-10px);
  }
}

/* allergen icon */
.allergen_warp {
  padding: 0 0.2rem;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  /* height: 1.8rem; */

  /* display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all; */
}

.allergen_Icon {
  /* float: left; */
  width: 0.7rem;
  height: 0.7rem;
  padding: 0.1rem 0.1rem 0.1rem 0;
}

.food_info_warp_allergen {
  border-top: 1px solid #ccc;
  padding: 0.5rem 0rem;
  background-color: white;
}

.food_info_warp_allergen_title {
  font-size: 0.35rem;
  padding-bottom: 0.2rem;
  color: var(--styleColor);
}

.food_info_warp_allergen_Icon {
  padding-right: 0.2rem;
  width: 1.3rem;
  height: 1.3rem;
}

/* 套餐细项（后改） */
.optionalTitle {
  padding: 0.3rem 0rem 0.5rem;
  font-size: 0.35rem;
  color: var(--styleColor);
  display: flex;
  align-items: center;
}

.selectCol2 {
  background-color: #f84143;
}
.selectCol1 {
  background-color: #8cc940;
}
.selectMinNumBox {
  margin-left: 0.3rem;
  border-radius: 5px;
  padding: 0.05rem;
  /* margin-right: auto; */
  box-sizing: border-box;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.selectMinNum {
  color: #fff;
  padding: 0.05rem;
}

/* 菜单food价钱 */
/* 新版价钱 */
.price_warp {
  font-size: 0.35rem;
  margin-bottom: 0.1rem;
  text-align: center;
  color: var(--styleColor);
}

.foodPrice_warp {
  color: #fff;
  font-size: 0.36rem;
  position: absolute;
  top: 15px;
  padding: 8px 10px;
  background: var(--styleColor);
  box-shadow: -1px 2px 4px rgba(0, 0, 0, 0.5);
  /* color: #F8463F; */
}

.foodPrice_warp:before,
.foodPrice_warp:after {
  position: absolute;
  content: "";
  display: block;
}

.foodPrice_warp:before {
  width: 7px;
  height: 100%;
  padding: 0 0 7px;
  top: 0;
  left: -7px;
  background: inherit;
  border-radius: 5px 0 0 5px;
}

.foodPrice_warp:after {
  width: 5px;
  height: 5px;
  background: rgba(0, 0, 0, 0.35);
  bottom: -5px;
  left: -5px;
  border-radius: 5px 0 0 5px;
}

/* 细项弹窗选择 */

.foodDialog {
  /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
  border-radius: 0.3rem;
  /* border: 1px solid #ebeef5; */
  font-size: 0.35rem;
  /* padding: 0.4rem 0.4rem ; */
}
.mar_LR {
  margin: 0 0.4rem;
}

.optionalFood {
  margin: 0.2rem 0rem 0.4rem;
  display: flex;
  max-height: 7.3rem;
  /* height: 6rem; */
  overflow-y: auto;
  /* overflow-x: hidden; */
  flex-direction: column;
  -webkit-overflow-scrolling: auto;
}

.small_btn {
  padding: 0.15rem 0.4rem;
  font-size: 0.3rem;
  border-radius: 3px;
}

.cencelBtn {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  margin-right: 0.3rem;
}

.defaultBtn {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}

.primaryBtn {
  margin-left: 0.3rem;
  color: #fff;
  background-color: #07c160;
  border: 1px solid #07c160;
}

.btns {
  display: flex;
  justify-content: flex-end;
}

.radio-box {
  width: 50%;
  /* margin-right: 0.3rem; */
}

.checkLabel {
  display: flex;
  align-items: center;
}

.checkSpan {
  /* font-size: 0.25rem; */
  /* width: 2.5rem; */
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input[type="checkbox"],
input[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  position: relative;
  right: 0;
  bottom: 0;
  left: 0;
  height: 20px;
  width: 20px;
  vertical-align: -0.8rem;
  transition: all 0.15s ease-out 0s;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  margin: 0.1rem;
  outline: none;
  border-radius: 10%;
  vertical-align: middle;
}

/* Checkbox */
input[type="checkbox"]:before,
input[type="checkbox"]:after {
  position: absolute;
  content: "";
  background: #fff;
  transition: all 0.2s ease-in-out;
}

input[type="checkbox"]:before {
  left: 2px;
  top: 6px;
  width: 0;
  height: 2px;
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
}

input[type="checkbox"]:after {
  right: 9px;
  bottom: 3px;
  width: 2px;
  height: 0;
  transform: rotate(40deg);
  -webkit-transform: rotate(40deg);
  -moz-transform: rotate(40deg);
  -ms-transform: rotate(40deg);
  -o-transform: rotate(40deg);
  transition-delay: 0.2s;
}

input[type="checkbox"]:checked:before {
  left: 1px;
  top: 10px;
  width: 6px;
  height: 2px;
}

input[type="checkbox"]:checked:after {
  right: 5px;
  bottom: 1px;
  width: 2px;
  height: 14px;
}

input[type="checkbox"]:indeterminate:before,
input[type="checkbox"]:indeterminate:after {
  width: 7px;
  height: 2px;
  transform: rotate(0);
  -webkit-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
}

input[type="checkbox"]:indeterminate:before {
  left: 1px;
  top: 7px;
}

input[type="checkbox"]:indeterminate:after {
  right: 1px;
  bottom: 7px;
}

/* Radio */
input[type="radio"] {
  vertical-align: middle;
  border-radius: 50%;
}

input[type="radio"]:checked:before {
  transform: scale(1);
}

input[type="radio"]:before {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 3px;
  transform: scale(0);
  transition: all ease-out 250ms;
}

input[type="checkbox"],
input[type="radio"] {
  border: 2px solid var(--styleColor);
}

input[type="checkbox"]:checked,
input[type="checkbox"]:indeterminate,
input[type="radio"]:checked:before {
  background: var(--styleColor);
}
/* 定义淡入淡出的动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/*slide-up进入离开动画*/
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.5s ease;
}

.slide-up-enter,
.slide-up-leave-active {
  transform: translateY(100%);
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  animation-duration: 0.5s;
  animation-timing-function: ease;
}

.slide-enter-active {
  animation-name: slide-in;
}

.slide-leave-active {
  animation-name: slide-out;
}

@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* 主fty页 */
.app_fty_container {
  flex: 1;
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  flex-wrap: wrap;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
  /* background-color: #f7f8fa; */
  padding: 0.4rem 0.4rem;
  align-content: flex-start;
}
/** food court start*/
section.food_court {
  /*box-sizing: border-box;*/
  position: absolute;
  left: 0;
  top: 0;
  z-index: 100;
  transform-origin: 15px 15px;
}
.food_court .mask_layer {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.food_court_header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  height: 50px;
  padding: 0 10px;
  /*border-bottom: 1px solid var(--styleColor);*/
}
.food_court_store {
  flex: 1;
  overflow-y: auto;
  background-color: #ffffff;
  padding: 5px 0;
  box-sizing: border-box;
  height: calc(100% - 100px);
}
.food_court_store .store_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: 0 10px;
  overflow: auto;
}
.food_court_store .store_list .store_item {
  /*box-shadow: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;*/
  box-shadow: 0 8px 12px #ebedf0;
  background-color: rgb(234, 231, 231, 0.3);
}
.food_court_store .store_item .img_placeholder {
  border-radius: 10px;
  background-color: #ffffff;
}
.one_column.store_list {
}

.one_column .store_item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 10px;
  margin: 6px 0;
  padding: 10px;
  width: 100%;
  height: 3.5rem;
}

.one_column .store_item .store_item_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  margin-bottom: 0.25rem;
  min-height: 25px;
}
.one_column .store_item .store_item_header > * {
  margin: 0 0.1rem;
  vertical-align: middle;
}
.one_column .store_item .store_item_header img {
  width: 0.5rem;
  height: auto;
  margin-right: 3px;
}
.one_column .store_item .store_item_header .store_name {
  font-size: 0.4rem;
  /*height: 100%;*/
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.one_column .store_item .store_item_header .store_cart_info {
  display: flex;
  align-items: self-end;
  justify-content: center;
}
.one_column .store_item .store_item_header .store_cart_info .store_cart_count {
  /*color: var(--styleColor);*/
  font-size: 0.38rem;
}
.one_column .store_item .store_item_header strong {
  flex: 1;
}
.one_column .store_item .store_item_details {
  display: flex;
  align-items: center;
  margin-top: 0.25rem;
  flex: 1;
}
.one_column .store_item .store_item_details .store_item_desc {
  flex: 1;
  height: fit-content;
  padding: 5px 5px 0 5px;
  font-size: 0.32rem;
  color: #7c7c7c;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}
.one_column .store_item .store_item_details .store_item_logo {
  height: 1.5rem;
  width: 2rem;
  object-fit: cover;
}
.one_column .store_item .store_item_details .into_store__btn {
  width: 0.75rem;
  /*height: 50px;*/
  /*padding: 0 10px;*/
  /*background-color: var(--styleColor);*/
  color: #ffffff;
  border-radius: 5px;
  margin: 0 5px 0 10px;
}

.two_column.store_list {
  flex-direction: row;
  align-items: self-start;
  flex-wrap: wrap;
  padding: 0;
  height: auto;
}

.two_column .store_item:nth-child(2n) {
  margin-left: 0;
}
:where(.two_column, .three_column) .store_item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 10px;
  margin: 6px 0;
  /*padding: 5px;*/
  width: 45%;
  height: 6rem;
  margin: 5px 3.3%;
}
:where(.two_column, .three_column) .store_item > :not(.store_item_logo_wrap) {
  padding: 0 10px;
}
:where(.two_column, .three_column) .store_item .store_item_logo_wrap {
  width: 100%;
  height: 45%;
  margin-bottom: 6px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  overflow: hidden;
  background-color: #ffffff;
}
:where(.two_column, .three_column) .store_item .store_item_logo_wrap img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
:where(.two_column, .three_column) .store_item .store_item_header {
  display: flex;
  align-items: flex-start;
  height: 0.9rem;
}
:where(.two_column, .three_column) .store_item .store_item_header strong {
  font-size: 0.35rem;
  margin-left: 5px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}
:where(.two_column, .three_column) .store_item .store_item_header .store_min_logo {
  width: 0.4rem;
  height: 0.4rem;
  margin-top: 0.03rem;
}
:where(.two_column, .three_column) .store_item .store_cart_info {
  display: flex;
  align-items: flex-end;
  /*margin-bottom: auto;*/
}
:where(.two_column, .three_column) .store_item .store_cart_info .store_cart_count {
  margin-right: auto;
  font-size: 0.37rem;
}
:where(.two_column, .three_column) .store_item .store_cart_info > img {
  width: 0.4rem;
  height: 0.4rem;
  margin-right: 3px;
}
:where(.two_column, .three_column) .store_item .store_cart_info .into_store__btn img {
  width: 0.5rem;
  height: auto;
}
:where(.two_column, .three_column) .store_item .store_item_desc {
  /*min-height: 1.25rem;*/
  font-size: 0.32rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  color: #7c7c7c;
}

.three_column.store_list {
  flex-direction: row;
  align-items: self-start;
  flex-wrap: wrap;
  padding: 0;
  height: auto;
}
.three_column.store_list .store_item {
  width: 30%;
  margin: 5px 2.5%;
  height: 4rem;
}
.three_column.store_list .store_item:nth-child(3n-1) {
  margin: 5px 0;
}
.three_column .store_item .store_item_logo_wrap {
  height: 50%;
}
.three_column .store_item .store_item_header {
  margin-bottom: auto;
}
.three_column .store_item .store_item_header .store_name {
  font-size: 0.3rem;
}
.three_column .store_item .store_cart_info {
  margin-bottom: auto;
}
.three_column .store_item .store_item .store_cart_info .store_cart_count {
  font-size: 0.35rem;
}

.cart_food_warp_content .store_item_wrap {
  /*background-color: rgb(234, 231, 231, 0.3);*/
  border-radius: 0.2rem;
  padding: 1px 0;
  margin: 5px 0;
}
.cart_food_warp_content .store_item_wrap .store_info {
  display: flex;
  align-items: center;
  height: max-content;
  padding: 5px 10px;
  margin-bottom: 0.2rem;
}
.cart_food_warp_content .store_item_wrap .store_info .store_logo img {
  width: 0.5rem;
  margin-right: 0.15rem;
  height: auto;
}
.cart_food_warp_content .store_item_wrap .store_info .store_name {
  font-size: 0.4rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hr.gradient {
  height: 1px;
  background: linear-gradient(0deg, #fff, #ccc);
}
/** food court end*/
.app_ftyPage,
.food_court {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
}

.app_fty_container_cell {
  position: relative;
  width: 47%;
  overflow: hidden;
  /* margin: 0.2rem 0; */
  /* min-height: 4.5rem; */
  background-color: #fff;
  border-radius: 0.3rem;
  margin-bottom: 0.3rem;
  box-shadow: 0 8px 12px #ebedf0;
  display: flex;
  flex-direction: column;
}

.app_fty_container_cell_top {
  /* border-radius: 0.3rem; */
  display: flex;
  align-items: center;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  height: 3.5rem;
  background-color: #f2efd9;
  padding: 0 0.4rem;
}
.app_fty_container_cell_top_1 {
  /* border-radius: 0.3rem; */
  display: flex;
  align-items: center;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  height: 2.5rem;
  background-color: #f2efd9;
  padding: 0 0.4rem;
}
.app_fty_container_cell_top_Box {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-top: 2px solid black;
  font-size: 0.35rem;
}
.app_fty_container_cell_top_Box_1 {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-top: 2px solid black;
  font-size: 0.25rem;
}

/* 自适应宽高比 */
.adaptivity_ftyImg_warp {
  position: relative;
  width: 100%;
  padding-top: 69.5%;
  /* 16:9 宽高比 */
}

.ftyImg {
  position: absolute;
  top: 0;
  height: 100%;
  max-width: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.fty_top_txt {
  padding-top: 0.05rem;
  /* color: white;
  transition: 0.5s;
  font-weight: 500;
  text-shadow: 0 0 0.1em, 0 0 0.3em; */
}

.app_fty_container_cell_bottom {
  padding: 0.2rem 0.2rem;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fty_bottom_txt {
  font-size: 0.37rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.fty_bottom_txt_1 {
  font-size: 0.28rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.backFtyIcon,
.back-store-list-icon {
  width: 0.8rem;
  margin-left: 0.4rem;
}
.back-store-list-icon {
  width: 0.53rem;
}
.back-store-list-icon {
  width: 0.53rem;
}
.discountIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0.4rem;
}
.useInputError {
  color: red;
}
.useInputError input::-webkit-input-placeholder {
  color: red;
}
.useInputError input::-moz-input-placeholder {
  color: red;
}
.useInputError input::-ms-input-placeholder {
  color: red;
}
.useInputError input {
  border: 1px solid red;
}
.useInputError span {
  font-size: 12px;
}

.fty_footer_bottom {
  position: relative;
  z-index: 4;
  display: flex;
  height: 1.3rem;
  color: #fff;
}

.fty_send_single {
  flex: 1;
  display: flex;
  justify-content: center;
  background-color: var(--styleColor);
  /* background-color: rgba(40, 67, 79, 0.9); */
  /* font-size: 0.5rem; */
  position: relative;
}

.fty_send_single_cell {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  justify-content: center;
}

.defaultSubAtTheBottom {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.38rem;
  flex-direction: column;
}
.preOrderSubAtTheBottom {
  font-size: 0.5rem;
}
.sendOrderMaxWidth {
  max-width: 80vw;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.fty_shopNum_warp {
  width: 0.6rem;
  height: 0.6rem;
  line-height: 0.6rem;
  border-radius: 50% 50%;
  background: rgba(255, 255, 0, 1);
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  color: rgba(244, 83, 91, 1);
  text-align: center;
  font-size: 0.45rem;
  font-style: initial;
  position: absolute;
  top: 0rem;
  right: -0.8rem;
}

.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  will-change: transform;
  transition: all 0.3s;
  position: relative;
  /* height: 100vh; */
}

.slide-right-enter {
  transform: translateX(-100%);
}

.slide-right-leave-active {
  transform: translateX(100%);
}

.slide-left-enter {
  transform: translateX(100%);
}

.slide-left-leave-active {
  transform: translateX(-100%);
}

.dropdown-menu {
  z-index: 1000 !important;
}

/* layer弹出层UI */
.dialogBox {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.cancelLayer {
  background-color: #f4f4f4 !important;
  color: #000 !important;
  font-size: 14px !important;
  border: 1px solid #c7c7c8 !important;
  border-radius: 3px !important;
}
.layerContentBox {
  text-indent: 10px;
  padding: 20px !important;
  flex: 1;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}
.confirmLayer {
  color: #f4f4f4 !important;
  font-size: 14px !important;
  background-color: #1aa377 !important ;
  border-radius: 3px !important;
}

.tipDia {
  min-width: 6rem !important;
  max-width: 8rem !important;
  filter: alpha(opacity=60) !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  color: #fff !important;
  border: none !important;
  border-radius: 0.2rem !important;
}

.tipDia .layui-layer-content {
  word-break: normal !important;
}

.tipDia .layui-layer-setwin .layui-layer-close2:hover {
  background-position: -149px -31px;
}

.tip_Fty_merge {
  background-color: #e7e7e7 !important;
  border-radius: 20px !important;
  font-size: 14px !important;
}
.merge_content {
  display: none;
  padding: 0.3rem 0;
}

.merge_box {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.fty_item {
  position: relative;
  width: 43%;
  max-height: 4.5rem;
  overflow: hidden;
  border-radius: 0.3rem;
  background-color: #fff;
}
.fty_item:nth-child(odd) {
  margin: 3% 0% 3% 5%;
}
.fty_item:nth-child(even) {
  margin: 3% 4% 3% 5%;
}

.oneLayout {
  background-color: #07c160;
}

.disableBox {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  z-index: 2;
  border-radius: 0.2rem;
}

/*  */
.xiNumDiaLayer {
  width: 85% !important;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
}

.xiNumDiaLayer .layui-layer-content {
  overflow-y: auto !important;
}

.foodDialogLayer .layui-layer-setwin .layui-layer-close2:hover,
.xiNumDiaLayer .layui-layer-setwin .layui-layer-close2:hover {
  background-position: -149px -31px;
}

.foodDialogLayer {
  width: 90%;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
}
.secondDialogLayer {
  width: 83%;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
}
.secXiNumDia,
.xiNumDia {
  font-size: 0.35rem;
  padding: 0.4rem 0.4rem;
  display: flex;
  flex-direction: column;
}

.selXiCell {
  padding-bottom: 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.xiNumDia_checkLabel {
  display: flex;
  align-items: center;
  flex: 1;
}

.xiNumDia_xiContent {
  display: flex;
}

.xiNumDia_txt {
  flex: 1;
  margin-right: 0.1rem;
}

.xiNumDia_xi {
  font-size: 0.3rem;
}

.xiNumDia_myXi {
  font-size: 0.3rem;
  margin-left: 0.05rem;
  word-break: break-word;
  white-space: pre-line;
}

.xiNumDia_chooseBtn {
  height: 0.5rem;
  line-height: 0.5rem;
  padding: 0.1rem 0.2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--styleColor);
  color: #fff;
  font-size: 0.3rem;
  border-radius: 0.1rem;
  flex-shrink: 0;
}

.xiNumDia_btns {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.2rem;
}

/* 购物车限制提示语样式 */
.tipDia .tips_Title {
  margin-bottom: 0.3rem;
}

.tipDia .tips_p {
  font-size: 0.32rem;
}

/* 可选细项弹窗 */
.secondDialog .title,
.foodDialog .title {
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #ccc;
}
.foodDialog .title {
  /* 另外写的原因是铅笔图标溢出要出现滚动条 拆开来写样式*/
  margin: 0.4rem 0.4rem 0;
}
.foodDialog .btns {
  /* 另外写的原因是铅笔图标溢出要出现滚动条 */
  margin: 0rem 0.4rem 0.4rem;
}
.foodDialog_infoPoints_lable {
  border-top: 0px;
}

.addSealXi_Title {
  width: 48%;
  margin-bottom: 0.35rem;
}

#myxiDialog .default_infoPoints {
  font-size: 0.3rem;
  padding: 0.1rem 0.18rem;
}

/* .foodDialog .infoPoints_content_cell {
  width: 48%;
} */

#myxiDialog .twoColumnList {
  width: 48%;
}
/* #myxiDialog .oneColumnList{
  width: 100%;
} */
.foodInfoItem_cell .infoPoints_content_cell {
  margin-bottom: 0.35rem;
  box-sizing: border-box;
}

/* 更改细项选择css */
.edit_xi_btn {
  text-align: center;
  min-width: 1.6rem;
  width: fit-content;
  padding: 0.05rem 0.4rem;
  font-size: 0.37rem;
  color: var(--styleColor);
  border: 2px solid var(--styleColor);
  border-radius: 0.2rem;
}
/*清空购物车按钮*/
.clear_cart_btn {
  min-width: unset;
  margin-left: 0.1rem;
}

.edit_xiFood_item_btn {
  text-align: center;
  width: 100%;
  padding: 0.1rem 0;
  color: #fff;
  background-color: var(--styleColor);
  font-size: 0.32rem;
  border-radius: 0.2rem;
  margin-top: 0.2rem;
}

.oneColumnList.lModPhotoStyle .typeListImg {
  max-width: 1rem;
  height: 0.75rem;
}
/* 详情页type类型下 栏目*/
.typeListImg {
  max-width: 0.8rem;
  height: 0.5rem;
  -o-object-fit: cover;
  object-fit: cover;
  margin-right: 0.15rem;
  border-radius: 0.1rem;
}

/* 追加套餐下第二层 */

.secondDialog {
  /* width: 7.3rem !important; */
  /* width: 85%;
  border-radius: 0.3rem;
  border: 1px solid #ebeef5;
  font-size: 0.32rem;
  padding: 0.3rem 0.3rem;
  border: none !important;
  filter: alpha(opacity=60) !important;
  border-radius: 0.2rem !important;
  box-sizing: border-box; */
  /* border-radius: 0.3rem; */
  font-size: 0.32rem;
  padding: 0.3rem 0.3rem;
}
.secOptionalFood {
  margin: 0.2rem 0rem 0.4rem;
  display: flex;
  max-height: 5rem;
  overflow-y: auto;

  flex-direction: column;
  -webkit-overflow-scrolling: auto;
}
#secXiDialog .default_infoPoints {
  font-size: 0.25rem;
  /* font-size: 0.28rem; */
  padding: 0.1rem 0.18rem;
}

/* .secondDialog .infoPoints_content_cell {
  width: 48%;
} */
#secXiDialog .twoColumnList {
  width: 48%;
}
.secondDialog_infoPoints_lable {
  padding: 0.3rem 0rem 0.5rem;
  /* font-size: 0.3rem; */
  border-top: 0;
  color: var(--styleColor);
}
.myXiClickXiItem_cell .optionalTitle {
  font-size: 0.32rem;
}

.soldoutList {
  /* position: absolute;
    width: 0.8rem;
    top: 0rem;
    right: 0rem; */
  position: absolute;
  width: 1.3rem;
  top: -0.26rem;
  right: 0rem;
  z-index: 101;

  /* transform: translate(-50%,-50%); */
}
.hideBack {
  background-color: #b3b3b3 !important;
  color: #666;
  pointer-events: none;
}
.errorDataBox {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.5rem;
  color: var(--styleColor);
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #ccc;
}
.errorBoxICon {
  width: 5rem;
}

/* 预点餐功能 */

.preOrderDiaLayer {
  width: 85%;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
}

.preOrderDia {
  font-size: 0.35rem;
  padding: 0.4rem 0.4rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.preOrder-form-item {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
}

.preOrder-form-item:nth-last-child(2) {
  margin: 0.5rem 0;
}

.preOrder-form-item:nth-last-child(1) {
  justify-content: flex-end;
  /* margin-bottom: 0; */
}
.payMent-form-item-email {
  padding-bottom: 0.3rem;
}
.payMent-form-item-email-label {
  margin: 0 0.3rem 0 0.5rem;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}
.payMent-form-item-email-input-block {
  width: 100%;
}

.payMent-form-item-bottom {
  /* justify-content: space-between; */
  justify-content: flex-end;
}
.subBtn-is-disabled {
  color: #fff;
  background-color: #a0cfff !important;
  border-color: #a0cfff !important;
}

.payMent-form-clause {
  display: flex;
  align-items: center;
  font-size: 0.3rem;
  color: #777777;
}
.payMent-form-clause-checkbox {
  transform: scale(0.7, 0.7);
}
.privacyTxt {
  color: #007fff;
  padding-left: 0.1rem;
}
.payMent-form-item .layui-btn-sm,
.preOrder-form-item .layui-btn-sm {
  padding: 0;
  width: 1.5rem;
}
.preOrder-label {
  display: flex;
  /* align-content: center; */
  justify-content: center;
  flex-direction: column;
}
.QRcodeIcon,
.tableIcon {
  width: 0.7rem;
  padding-right: 0.5rem;
}

.hotSaleData {
  /* padding: 0.5rem 0; */
  /* border-top: 0.2rem solid #ccc;
  border-bottom: 0.2rem solid #ccc; */
}
.hotSale_top_title {
  font-size: 0.5rem;
  color: var(--styleColor);
}
.hotSale_warp_content {
  display: flex;
  overflow-x: auto;
  padding: 0.3rem 0;
}
.hotSale_warp_content::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 0.1rem; /*宽度竖滚动条的尺寸*/
  height: 0.1rem; /*高度横滚动条的尺寸*/
}
.hotSale_warp_content::-webkit-scrollbar-thumb {
  /*滚动条里面的条*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #bcb9b7;
}
.hotSale_warp_content::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 10px;
  background: #ffffff;
}

.hotSale_warp_cell {
  /* width: 3rem;
  height: 3rem;
  background-color: red; */
  /* font-size: 0.3rem; */
  display: flex;
  flex-direction: column;
  align-items: center;
}
.hotSale_warp_cell:not(:first-child) {
  margin-left: 0.2rem;
}
.hotSale_item_img {
  height: 2rem;
  border-radius: 0.2rem;
  /* border-radius: 30% 70% 17% 83% / 60% 60% 40% 40%; */
}

.hotSale_warp_cell_top {
  position: relative;
}
.hotSale_warp_cell_top .soldOutLogo {
  width: 1.8rem;
}
.hotSale_item_price {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 0.05rem 0.2rem;
  background-color: #c8291f;
  color: #fff;
  font-size: 0.3rem;
  border-radius: 0.05rem;
}
.hotSale_warp_cell_name {
  /* position: absolute; */
  max-width: 2.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  word-break: break-all;
  font-size: 0.3rem;
  padding-top: 0.1rem;
}

.maskLayer {
  position: relative;
  /* width: 100%; */
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  z-index: 200;
  border-radius: 0.2rem;
  filter: blur(1px);
}
.successOrderImg {
  width: 1.5rem;
}
.postOrderPopup_tip {
  padding-top: 0.3rem;
}
.customerPhonePopup,
.questionnairePopup,
#discountPopup,
.postOrderPopup {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.5rem 0;
}
.menuCarouselLayer,
.backCheckPasswordLayer,
.searchLayer,
.customerPhoneDiaLayer,
.discountLayer,
.defaultLayer,
.postOrderDiaLayer {
  width: 85%;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
  font-size: 0.35rem;
}
.homeIcon {
  width: 0.8rem;
  margin-left: 0.4rem;
  padding: 0.1rem;
  box-sizing: border-box;
}
/* 支付选择窗口 */

.payMentLayer {
  width: 80%;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
}
.payment-methods-wrap {
  font-size: 0.35rem;
  padding: 0.4rem 0;
  /* display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center; */
}
.payment-methods-title {
  padding: 0.3rem 0;
}
.payMent-form-payTypeBox {
  /*max-height: 6rem;*/
  overflow: auto;
  box-sizing: content-box;
}
.payMent-form-item {
  width: 100%;
  display: flex;
  align-content: center;
  box-sizing: border-box;
  /* margin-bottom: 0.5rem; */
  /* margin-left: 0.7rem; */
  /* justify-content: center; */
}
.payMent-label {
  padding-left: 0.1rem;
  margin-bottom: 0.3rem;
}
.payMent-label.disabled {
  opacity: 0.5;
}
.payAtCashierIcon,
.onlinePaymentIcon {
  width: 1.3rem;
  padding: 0 0.1rem 0 0.3rem;
}
/* 显示柜台支付号码 */
.paidAtCashierLayer {
  width: 85%;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
  box-shadow: none !important;
}

.paidAtCashierSucc {
  /* position: fixed;
  top: 50%;
  left: 50%;

  transform: translate(-50%, -50%);
  box-shadow: 1px 1px 50px rgb(0 0 0 / 30%);
  z-index: 521; */
  display: none;
  padding: 0.5rem;
}
.paidAtCashier-top {
  display: flex;
  justify-content: center;
  align-items: center;
}
.paidAtCashier-top-icon {
  width: 1rem;
  padding-right: 0.2rem;
}
.paidAtCashier-top-title {
  font-size: 0.6rem;
}

.paidAtCashier-center {
  text-align: center;
  font-size: 0.4rem;
  padding: 0.5rem;
}
.paidAtCashier-bottom {
  /* overflow: hidden; */ /* 解决圆角失效 */
  /* overflow: auto;  */
  border-radius: 0.2rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.paidAtCashier-bottom-title {
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
  text-align: center;
  padding: 0.3rem 0;
  background-color: var(--styleColor);
  font-size: 0.4rem;
  color: #fff;
}
.paidAtCashier-bottom-num {
  font-size: 1rem;
  color: var(--styleColor);
  height: 2.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.table_info_leftIcon {
  position: absolute;
  right: 0.25rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
}
.navHeaderIcon {
  width: 5.5vw;
  padding-right: 3vw;
}
.user-icon-content {
  display: flex;
  align-items: center;
}
.user-icon-content span {
  align-self: flex-end;
  /*line-height: 1;*/
}
.food-court-icon {
  width: 0.5rem;
  padding-left: 0.2rem;
  display: flex;
  align-items: center;
}
.navHeaderIcon.user-icon {
  width: 0.5rem;
  margin-left: 0.3rem;
  padding-right: unset;
}
/* .table_info_leftIcon .navHeaderIcon {
  padding-right: 0.3rem;
} */
/* table_info_leftIcon里面倒数第第二元素取消padding */
.table_info_leftIcon .navHeaderIcon:nth-last-child(2) {
  padding-right: 0;
}

/* 支付邮箱 */
.templateLayer {
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.2rem !important;
  font-size: 0.35rem;
}
body .templateLayer .layui-layer-btn0 {
  background-color: #ff5722;
  border-color: #ff5722;
}
body .templateLayer .layui-layer-btn1 {
  background-color: #1e9fff;
  border-color: #1e9fff;
  color: #fff;
}
body .templateLayer .layui-layer-content {
  word-break: keep-all;
}
.payMentEmailLayer {
  width: 75%;
}
.payMentEmailDia {
  display: none;
  padding: 0.5rem;
}
.payMentEmailDia-reminder {
  text-align: center;
  margin-bottom: 0.4rem;
}
.emptyMailLayer {
  width: 65%;
}
input[type="checkbox"]:disabled {
  color: #cccccc;
  background-color: #cccccc;
  border: 2px solid #cccccc;
}

/*外卖*/
.delivery_warp {
  display: flex;
  align-items: center;
  height: fit-content;
  /* justify-content: space-around; */
  justify-content: center;
  font-size: 0.3rem;
  padding: 0 0.4rem;
  min-height: 40px;
}
.delivery_warp .storeTime,
.delivery_warp .address {
  position: relative;
  height: 100%;
  vertical-align: middle;
  text-align: center;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
.delivery_warp_storeData {
  display: flex;
  align-items: center;
  padding-top: 0.05rem;
}
.delivery_warp_interval {
  padding: 0 0.2rem;
}
.delivery_warp_icon {
  width: 0.45rem;
  height: 0.45rem;
  padding-right: 0.1rem;
  vertical-align: middle;
}
.delivery_warp .address span,
.delivery_warp .storeTime span {
  white-space: nowrap;
}
.delivery_warp_storeName {
  /* 允许换行 */
  word-wrap: break-word;
}
.backMapIcon {
  width: 0.25rem;
  height: 0.25rem;
  margin-left: 0.1rem;
}
.backMapTips .layui-layer-title {
  text-align: center;
  padding: unset;
}

/*购物车赠品样式*/
.giveAway {
}
.giveAway .info_left {
  justify-content: center;
  /*align-items: center;*/
  flex-direction: column;
}
.give_away_logo_text {
  padding: 0 1rem;
  font-size: 0.3rem;
  color: var(--styleColor);
}
.give_away_icon {
  width: 0.5rem;
  height: 0.5rem;
  margin-right: 0.87rem;
}

.layui-custom-style .layui-layer-btn .layui-layer-btn0,
.backMapTips .layui-layer-btn .layui-layer-btn0,
.payAtCashierLayer .layui-layer-btn .layui-layer-btn0,
.discountLayer .layui-layer-btn .layui-layer-btn0,
.customerPhoneDiaLayer .layui-layer-btn .layui-layer-btn0 {
  border-color: #ff5721;
  background-color: #ff5721;
  color: #fff;
}
.layui-custom-style .layui-layer-btn .layui-layer-btn1,
.backMapTips .layui-layer-btn .layui-layer-btn1,
.payAtCashierLayer .layui-layer-btn .layui-layer-btn1,
.discountLayer .layui-layer-btn .layui-layer-btn1,
.customerPhoneDiaLayer .layui-layer-btn .layui-layer-btn1 {
  border-color: #1e9fff;
  background-color: #1e9fff;
  color: #fff;
}

.discountAvailableLayer .layui-layer-btn .layui-layer-btn1,
.defaultLayer .layui-layer-btn .layui-layer-btn0 {
  border-color: var(--styleColor);
  background-color: var(--styleColor);
  color: #fff;
}
.discountAvailableLayer .layui-layer-btn .layui-layer-btn0 {
  border-color: #ccc !important;
  background-color: transparent !important;
  color: #333;
}

.qnrEmail {
  margin-top: 0.5rem;
}

.customerPhoneInputBox {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.5rem;
}
.customerPhoneInputBox .requiredIcon {
  color: #f56c6c;
  margin-right: 4px;
  font-size: 0.5rem;
}

.expand_content {
}
.expand_content .discount_content {
  display: flex;
  margin: -0.2rem 0 0.5rem 0.2rem;
  padding: 0.2rem 0 0.2rem 0.2rem;
  border-left: 5px solid var(--styleColor);
  justify-content: space-between;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
  background-color: #f8f9fa;
  font-size: 12px;
}
.expand_content .disabledDiscount {
  color: #bcbec2 !important;
  background-color: #f4f4f5 !important;
  text-decoration: line-through !important;
  border-left: 5px solid #bcbec2 !important;
}
.discount_content .discount_info {
  flex: 1;
  word-break: break-all;
  word-wrap: break-word;
  max-width: 80%;
  padding-left: 2em;
  text-indent: -2em;
  align-self: center;
}
.discount_content .discount_info p {
  padding: 0.05rem 0;
}
.discount_content .discount_use {
  padding-right: 0.2rem;
  width: fit-content;
  align-self: center;
}
#searchFoodDia {
  padding: 0.3rem;
  display: none;
  background-color: #f7f8fa;
}
.searchList-Commands {
  max-height: 5rem;
  overflow-y: scroll;
}

.layui-customPanel-form,
.searchFoodDia-form {
  display: flex;
}
.layui-customPanel-label,
.searchFoodDia-label {
  width: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border-width: 0.1px;
  border-style: solid;
  border-radius: 2px 0 0 2px;
  box-sizing: border-box;
  border: 0.1px solid #eee;
  border-right: none;
}
.layui-customPanel-label-icon,
.searchFoodDia-icon {
  font-size: 0.5rem !important;
  color: var(--styleColor);
}
.layui-customPanel-label-input,
.searchFoodDia-inputBox {
  flex: 1;
}
.searchList-content {
  margin-top: 0.3rem;
  margin-left: 0.4rem;
}
.searchList-content h2 {
  font-size: 0.4rem;
  color: var(--styleColor);
}
.searchList-Commands-li {
  display: flex;
  flex-direction: column;

  padding: 0.3rem 0;
  border-bottom: 1px solid #eee;
}

.searchList-Commands-li:last-child {
  border-bottom: none;
}
.searchList-Commands-li-foodWarp {
  margin: 0.2rem;
  position: relative;
}
.searchList-Commands-li-foodContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.searchList-Commands-li-foodContentRight {
  padding-right: 0.2rem;
}
.searchList-Commands-li-foodImg {
  height: 1.6rem;
  width: 2.5rem;
  border-radius: 0.2rem;
  flex-shrink: 0;
}
.searchList-Commands-li-addBtn {
  height: 0.5rem;
  width: 0.5rem;
  bottom: 0.1rem;
  right: 0.1rem;
}
.listSearch-Help {
  height: 2rem;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.2rem 0;
}
.searchList-highlightTxt {
  height: 100%;
  /* display: flex;
  justify-content: center;
  align-items: center; */
  color: var(--styleColor);
}
/* +1动画 */
#addOne-tenth {
  /* display: none; */
  color: var(--styleColor);
  height: 200px;
  animation: addOneMove 1s;
  position: absolute;
  opacity: 0;
}
@keyframes addOneMove {
  from {
    top: 100px;
    opacity: 100;
  }
  to {
    top: 50px;
    opacity: 0;
  }
}
.searchList-itemCtrl {
  background-color: rgba(140, 143, 152, 0.5);
  position: absolute;
  top: 0;
  right: 0;
  height: 1.6rem;
  width: 2.5rem;
  border-radius: 0.2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}
.searchList-itemCtrl img {
  width: 1.5rem;
}

/*订单提交页面 start*/
#send-order-view {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 520;
  box-sizing: border-box;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

#send-order-view #app {
  box-sizing: border-box;
}
.send-order-view-main {
  flex: 1;
  padding: 0 0.5rem;
}
.send-order-view-footer {
  width: 100%;
}
.discount_code_input .v-messages:not(.error--text) .v-messages__message {
  color: var(--styleColor);
}
.discountConflict-text-field {
  text-decoration: line-through;
}
.discountConflict-text-field .v-messages:not(.error--text) .v-messages__message {
  color: rgba(0, 0, 0, 0.38);
}
/*订单提交页面 end*/

/* 购物车页必选项<餐具/包装袋>*/
.takeaway-required-content .required-item {
  display: flex;
  align-items: center;
  margin: 0.1rem 0;
}

/* 购物车页无接触*/
.takeaway-cutlery-delivery-content {
  display: flex;
  flex-direction: column;
  padding-top: 10px;
  margin: 0.3rem 0;
}
.takeaway-cutlery-delivery-content > div {
  display: grid;
  grid-template-columns: repeat(1, 100%);
}
.takeaway-cutlery-delivery-content > div > div {
  margin: 0.05rem 0;
}
.takeaway-cutlery-delivery-content label {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  font-size: 0.3rem;
  width: fit-content;
}
.takeaway-cutlery-delivery-content label input {
  transform: scale(0.8);
  min-width: 16px;
}

/*  固定的餐具数量选择*/
.fixed-additional-item {
}
.fixed-additional-item .cart_del_btn:disabled {
  background-color: #cccccc;
}
.fixed-additional-item .info_left {
  height: 100%;
}
.fixed-additional-item .info_left .cart_food_title {
  display: flex;
  align-items: center;
  height: 100%;
}
.fixed-additional-item .cart_info {
  padding-left: 0;
}
.fixed-additional-item .info_left {
  width: 3.4rem;
}
.fixed-additional-item .cart_info .cart_food_priceNum {
  align-items: center;
}
.fixed-additional-item label {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  font-size: 0.3rem;
  width: fit-content;
}
.fixed-additional-item label input {
  transform: scale(0.8);
  min-width: 16px;
}
/* 强调 fty*/
.stressKeyFtyAnimate {
  -webkit-animation: twinkling 1.5s infinite ease-in-out;
  -webkit-animation-duration: 1.5s;
  animation-duration: 1.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  box-shadow: var(--styleColor) 0px 0px 4px;
  animation-iteration-count: 2;
}

@-webkit-keyframes twinkling {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 0.65;
  }
  50% {
    opacity: 0.3;
  }
  75% {
    opacity: 0.65;
  }
  100% {
    opacity: 1;
  }
}

/* 购物车左右摆动动画 */
@keyframes icon-bounce {
  0%,
  100% {
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  25% {
    -moz-transform: rotate(20deg);
    -ms-transform: rotate(20deg);
    -webkit-transform: rotate(20deg);
    transform: rotate(20deg);
  }

  50% {
    -moz-transform: rotate(-20deg);
    -ms-transform: rotate(-20deg);
    -webkit-transform: rotate(-20deg);
    transform: rotate(-20deg);
  }

  75% {
    -moz-transform: rotate(10deg);
    -ms-transform: rotate(10deg);
    -webkit-transform: rotate(10deg);
    transform: rotate(10deg);
  }

  85% {
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg);
  }
}
.swingAnimation {
  -webkit-animation: icon-bounce 0.5s alternate;
  -moz-animation: icon-bounce 0.5s alternate;
  -o-animation: icon-bounce 0.5s alternate;
  animation: icon-bounce 0.5s alternate;
}
/* 脉冲动画 */
@keyframes btn-pulse {
  0% {
    transform: scale(0.95);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.95);
  }
}
.btnPulse {
  animation-name: btn-pulse;
  -webkit-animation-name: btn-pulse;
  animation-duration: 2s;
  -webkit-animation-duration: 2s;
  animation-timing-function: linear;
  -webkit-animation-timing-function: linear;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  visibility: visible !important;
}
/*购物车确认按钮 动画*/
.anima-shakeX {
  -webkit-animation: HeadShake 2s ease-in infinite;
  animation: my-shakeX 2s ease-in infinite;
}
@keyframes my-shakeX {
  0% {
    transform: translateX(0);
  }
  3.5% {
    transform: translateX(-6px);
  }
  9.5% {
    transform: translateX(5px);
  }
  15.5% {
    transform: translateX(-3px);
  }
  21.5% {
    transform: translateX(2px);
  }
  25% {
    transform: translateX(0);
  }
}

/** byod 版本标识区域*/
.version_diff_warp {
  position: absolute;
  left: 0;
  z-index: 1;
  bottom: 10%;
  background-color: var(--styleColor);
  height: 40px;
  color: white;
  font-size: 20px;
  line-height: 40px;
  padding: 0 10px;
  box-sizing: content-box;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 10px;
}
.container_border {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-right: 5px solid var(--styleColor);
  border-left: 5px solid var(--styleColor);
  /*padding: 0.4rem calc(0.4rem - 5px);*/
  z-index: 555;
  box-sizing: border-box;
  pointer-events: none;
}
.main_warp {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0;
  overflow: auto;
}
.verify-repeated-layer {
  width: 72% !important;
}
.verify-repeated-layer .layui-layer-content {
  text-align: center;
}
.cart_food_null_txt {
  width: 8rem;
  text-align: center;
}
#paidAtCashierQRcode {
  padding: 0.5rem 0 0;
  min-height: 150px;
  display: flex;
  justify-content: center;
}
.text-field-prepend-inner {
  padding-top: 0.25em;
  z-index: 1;
  color: #f56c6c;

  /* width: 2px; */
}
.text-field-required .v-label--active {
  left: -0.28rem !important;
}
.baseLayer {
  box-shadow: 0 11px 15px -7px rgba(0, 0, 0, 0.2), 0 24px 38px 3px rgba(0, 0, 0, 0.14),
    0 9px 46px 8px rgba(0, 0, 0, 0.12) !important;
  border-radius: 4px !important;
}
/* 外卖模式支付弹出确认店铺等信息提交窗口 */
.takeAwayMode-subDia {
  width: 78% !important;
}
.takeAwayMode-subDia-cardTitle {
  padding: 0.4rem;
  font-size: 0.58rem;
  font-weight: 400;
}
.takeAwayMode-subDia-cardText {
}
.takeAwayMode-subDia-cardText-store {
  background-color: #f5f6f8;
  margin: 0.3rem;
  padding: 0.2rem;
  border-radius: 0.1rem;
  display: flex;
}
.takeAwayMode-subDia-cardText-store .content-left {
  flex: 1;
  color: #333;
}
.takeAwayMode-subDia-cardText-store .content-left .content-storeName {
  font-size: 0.4rem;
  font-weight: 600;
  margin-bottom: 0.1rem;
}
.takeAwayMode-subDia-cardText-store .content-left .content-address {
  font-size: 0.3rem;
}
.takeAwayMode-subDia-cardText-store .content-left .content-distance {
  font-size: 0.28rem;
}
.warningDistance {
  color: red;
}
.takeAwayMode-subDia-cardText-store .content-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-left: 0.3rem;
  color: #8a8a8a;
  font-size: 0.3rem;
  border-left: 0.01rem solid #e6e6e6;
}

.takeAwayMode-subDia-cardText-tip {
  margin: 0.3rem 0.3rem 0;
  display: flex;
  font-size: 0.3rem;
  color: red;
}
.takeAwayMode-subDia-svg {
  width: 0.43rem;
  height: 0.43rem;
}
/* .v-dialog__content {
  z-index: 1000000;
} */

.timeOutPrompt-Pop {
  text-align: center;
  position: relative;
  line-height: 0.58rem;
  font-size: 0.35rem;
  background-color: rgba(0, 0, 0, 0.7) !important;
  color: #fff;
  border: none;
}
.timeOutPrompt-Pop .layui-layer-content {
  padding: 0.4rem;
  height: unset !important;
}

.checkPassword-form-warp {
  margin: 0.47rem;
}
.checkPassword-form-warp .checkPasswordButton {
  display: flex;
  justify-content: center;
}
.checkPassword-form-warp .checkPasswordButton button {
  width: 90%;
}
.checkPassword-form-item {
  display: flex;
  align-content: center;
  justify-content: center;
  font-size: 0.3rem;
  margin-bottom: 0.5rem;
}
.checkPassword-form-item .passwordModel-label {
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin: 0px !important;
}
.checkPassword-form-item .checkPasswordInput {
  position: relative;
  width: 75%;
}
.checkPassword-form-item .checkPasswordInput .eye-warp {
  position: absolute;
  width: 0.47rem;
  height: 0.47rem;
  top: 50%;
  transform: translateY(-50%);
  right: 0.3rem;
}
.checkPassword-form-item .checkPasswordInput .icon-eye-close {
  background: url("../../img/newImage/invisibleEyeIcon.jpg") no-repeat;
  background-size: 100%;
}
.checkPassword-form-item .checkPasswordInput .icon-eye-open {
  background: url("../../img/newImage/visibleEyeIcon.jpg") no-repeat;
  background-size: 100%;
}
.checkPassword-form-item img {
  height: 0.59rem;
  padding-right: 0.4rem;
}
.carousel-warp {
  padding: 0.4rem 0;
  background-color: #e7e9eb;
  display: none;
  height: 5rem;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 组合tab栏  --start----*/
.combined_tab_warp {
  display: flex;
  flex-direction: column;
}
.combined_tab_warp > ul {
  position: relative;
  display: flex;
  overflow: auto;
  flex-shrink: 0;
  /* 防止滚动溢出影响外层 */
  overscroll-behavior: contain;
}
.combined_tab_warp .fr_tabs {
  background-color: #e3e7ea;
}
.combined_tab_warp .sec_tabs {
  background-color: #cccdcf;
}
.mobile_menuPage .combined_tab_warp > ul::-webkit-scrollbar {
  display: none;
}
.combined_tab_warp .tab_active {
  box-shadow: none;
  background-color: unset;
}
.combined_tab_warp li.tab_cell {
  min-width: unset;
  cursor: pointer;
  border-radius: unset;
}
.combined_tab_warp li.tab_active span {
  background-color: var(--styleColor);
  color: white;
  /*mix-blend-mode: difference;*/
  padding: 4px 8px;
  border-radius: 0.25rem;
}
.combined_tab_warp .fr_tabs li.tab_cell:hover {
  /*background-color: #d5dadd;*/
  /*box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;*/
}
.tabs-slider-wrapper {
  bottom: 0;
  margin: 0;
  position: absolute;
  transition: 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
  z-index: 1;
}
.tabs-slider-wrapper .tabs-slider {
  height: 2px;
  background-color: var(--styleColor);
}
/* 组合tab栏  ----end----*/

/* 下单等待时间提示组件 */
.orderWait-warp {
  display: flex;
  justify-content: space-between;
  padding: 0.1rem 0rem;
  font-size: 0.35rem;
  color: var(--styleColor);
}
.orderWait-warp .left,
.orderWait-warp .right {
  align-self: center;
  text-align: center;
  width: 50%;
  padding: 0 0.2rem;
}
.orderWait-warp .left {
  position: relative;
}
.orderWait-warp .left::after {
  content: "";
  display: block;
  width: 1px;
  height: 0.4rem;
  background-color: #c7c7c7;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.queue-time {
  margin: 0 0.1rem;
  font-size: 0.4rem;
}
.font-weight-medium {
}
.discountFails {
  position: relative;
}
.discountFails span {
  color: #bcbec2 !important;
}
.discountFails::before {
  content: " ";
  /* width: 100%; */
  height: 1px;
  background-color: #bcbec2;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  /* left: 0.5rem;
  right: 0.5rem; */
}
.totalAmount-warp > div {
  padding-bottom: 0.15rem;
}
.totalAreaConfMsg {
  max-width: 85%;
  color: #ef5350;
  margin-bottom: 0 !important;
}
.phoneField .v-messages__message {
  color: #ff5252 !important;
  caret-color: #ff5252 !important;
}
.btnDisabledStyle {
  background-color: #cccccc;
  color: #ffffff;
  border: 1px solid #cccccc;
}
.optionalText {
  margin-left: 0.2rem;
  color: #8cc940;
  /* font-size: 0.37rem; */
}
/* 媒体查询--小屏幕(请放置最后) */
@media screen and (min-width: 320px) and (max-width: 375px) {
  .user-logo img {
    max-height: 2rem;
  }
  .paidAtCashier-bottom-num {
    font-size: 0.95rem;
  }
  .personalCenterWarp {
    padding: 0.5rem 0.8rem;
  }

  .user-card-content {
    max-height: 4.5rem;
    overflow-y: auto;
  }

  .features-grid {
    gap: 12px;
    padding: 0 5px;
  }

  .feature-item {
    padding: 10px;
  }

  .feature-icon {
    font-size: 20px;
    margin-bottom: 5px;
  }

  .feature-title {
    font-size: 12px;
  }

  .feature-desc {
    font-size: 10px;
  }
}
