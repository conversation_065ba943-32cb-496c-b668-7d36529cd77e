/*!
 * SweetDropdown: Sweet and versatile dropdowns
 * v1.0.0, 2017-04-09
 * http://github.com/adeptoas/sweet-dropdown
 *
 * Copyright (c) 2016 Adepto.as AS · Oslo, Norway
 * Dual licensed under the MIT and GPL licenses.
 *
 * See LICENSE-MIT.txt and LICENSE-GPL.txt
 */
!(function ($) {
  var showDropdown;
  return (
    ($.fn.sweetDropdown = function (method, data) {
      switch (method) {
        case 'attach':
          return $(this).attr('data-dropdown', data);
        case 'detach':
          return $(this).removeAttr('data-dropdown');
        case 'show':
          return $(this).click();
        case 'hide':
          return $.sweetDropdown.hideAll(), $(this);
        case 'enable':
          return $(this).removeClass('dropdown-disabled');
        case 'disable':
          return $(this).addClass('dropdown-disabled');
      }
    }),
    ($.sweetDropdown = function () {}),
    ($.sweetDropdown.attachAll = function () {
      return (
        $('body')
          .off('click.dropdown')
          .on('click.dropdown', '[data-dropdown]', showDropdown),
        $('[data-dropdown]')
          .off('click.dropdown')
          .on('click.dropdown', showDropdown),
        $('html, .sweet-modal-content')
          .off('click.dropdown')
          .on('click.dropdown', $.sweetDropdown.hideAll),
        $(window)
          .off('resize.dropdown')
          .on('resize.dropdown', $.sweetDropdown.hideAll),
        !0
      );
    }),
    ($.sweetDropdown.hideAll = function (e, hideException) {
      var animTimeout, el, hideExceptionID, targetGroup, trigger;
      return (
        null == e && (e = null),
        null == hideException && (hideException = null),
        (targetGroup = e ? $(e.target).parents().addBack() : null),
        targetGroup &&
        targetGroup.hasClass('dropdown-menu') &&
        !targetGroup.is('A')
          ? void 0
          : ((el = '.dropdown-menu'),
            (trigger = '[data-dropdown]'),
            (hideExceptionID = ''),
            hideException &&
              ((hideExceptionID = $(hideException).attr('id')),
              $('[data-dropdown="#' + hideExceptionID + '"]').hasClass(
                'dropdown-open'
              ) ||
                ((el = '.dropdown-menu:not(#' + hideExceptionID + ')'),
                (trigger = '[data-dropdown!="#' + hideExceptionID + '"]'))),
            $('body')
              .find(el)
              .removeClass('dropdown-opened')
              .end()
              .find(trigger)
              .removeClass('dropdown-open'),
            (animTimeout = window.setTimeout(function () {
              return $('body').find(el).hide().end();
            }, 200)),
            !0)
      );
    }),
    ($.sweetDropdown.ANCHOR_POSITIONS = [
      'top-left',
      'top-center',
      'top-right',
      'right-top',
      'right-center',
      'right-bottom',
      'bottom-left',
      'bottom-center',
      'bottom-right',
      'left-top',
      'left-center',
      'left-bottom',
    ]),
    ($.sweetDropdown.defaults = { anchorPosition: 'center' }),
    (showDropdown = function (e) {
      var $anchor,
        $dropdown,
        $trigger,
        addAnchorX,
        addAnchorY,
        addX,
        addY,
        anchorPosition,
        anchorSide,
        bottomTrigger,
        hasAnchor,
        heightDropdown,
        heightTrigger,
        i,
        isDisabled,
        isOpen,
        left,
        leftTrigger,
        len,
        position,
        positionParts,
        ref,
        rightTrigger,
        top,
        topTrigger,
        widthDropdown,
        widthTrigger;
      if (
        (null == e && (e = null),
        ($trigger = $(this)),
        ($dropdown = $($trigger.data('dropdown'))),
        ($anchor = $dropdown.find('.dropdown-anchor')),
        (hasAnchor = $dropdown.hasClass('dropdown-has-anchor')),
        (isOpen = $trigger.hasClass('dropdown-open')),
        (isDisabled = $trigger.hasClass('dropdown-disabled')),
        (widthDropdown = $dropdown.outerWidth()),
        (widthTrigger = $trigger.outerWidth()),
        (heightDropdown = $dropdown.outerHeight()),
        (heightTrigger = $trigger.outerHeight()),
        (topTrigger = $trigger.position().top),
        (leftTrigger = $trigger.position().left),
        $trigger.hasClass('dropdown-use-offset') &&
          ((topTrigger = $trigger.offset().top),
          (leftTrigger = $trigger.offset().left)),
        (bottomTrigger = topTrigger + heightTrigger),
        (rightTrigger = leftTrigger + widthTrigger),
        $dropdown.length < 1)
      )
        return console.error(
          '[SweetDropdown] Could not find dropdown: ' + $(this).data('dropdown')
        );
      if (
        ($anchor.length < 1 &&
          hasAnchor &&
          (($anchor = $('<div class="dropdown-anchor"></div>')),
          $dropdown.prepend($anchor)),
        void 0 !== e && (e.preventDefault(), e.stopPropagation()),
        isOpen || isDisabled)
      )
        return !1;
      for (
        $.sweetDropdown.hideAll(null, $trigger.data('dropdown')),
          anchorPosition = $.sweetDropdown.defaults.anchorPosition,
          ref = $.sweetDropdown.ANCHOR_POSITIONS,
          i = 0,
          len = ref.length;
        len > i;
        i++
      )
        (position = ref[i]),
          $dropdown.hasClass('dropdown-anchor-' + position) &&
            (anchorPosition = position);
      if (
        ((top = 0),
        (left = 0),
        (positionParts = anchorPosition.split('-')),
        (anchorSide = positionParts[0]),
        (anchorPosition = positionParts[1]),
        'top' === anchorSide || 'bottom' === anchorSide)
      )
        switch (anchorPosition) {
          case 'left':
            left = leftTrigger;
            break;
          case 'center':
            left = leftTrigger - widthDropdown / 2 + widthTrigger / 2;
            break;
          case 'right':
            left = rightTrigger - widthDropdown;
        }
      if ('left' === anchorSide || 'right' === anchorSide)
        switch (anchorPosition) {
          case 'top':
            top = topTrigger;
            break;
          case 'center':
            top = topTrigger - heightDropdown / 2 + heightTrigger / 2;
            break;
          case 'bottom':
            top = topTrigger + heightTrigger - heightDropdown;
        }
      switch (anchorSide) {
        case 'top':
          top = topTrigger + heightTrigger;
          break;
        case 'right':
          left = leftTrigger - widthDropdown;
          console.log(left,'left');
          break;
        case 'bottom':
          top = topTrigger - heightDropdown;
          break;
        case 'left':
          left = leftTrigger + widthTrigger;
      }
      return (
        (addX = parseInt($dropdown.data('add-x'))),
        (addY = parseInt($dropdown.data('add-y'))),
        isNaN(addX) || (left += addX),
        isNaN(addY) || (top += addY),
        (addAnchorX = parseInt($trigger.data('add-anchor-x'))),
        (addAnchorY = parseInt($trigger.data('add-anchor-y'))),
        isNaN(addAnchorX) || $anchor.css({ marginLeft: addAnchorX }),
        isNaN(addAnchorY) || $anchor.css({ marginTop: addAnchorY }),
        $dropdown.css({ top: top, left: left, display: 'block' }),
        window.setTimeout(function () {
          return $dropdown.addClass('dropdown-opened');
        }, 0),
        $trigger.addClass('dropdown-open'),
        $trigger
      );
    }),
    $(function () {
      return $.sweetDropdown.attachAll();
    })
  );
})(jQuery);
