var navHeaderTable = {
  props: ["openTable", "systemLanguage"],
  template: ` 
    <div class="table_info_num" v-if="dineIn">
     {{systemLanguage.dineIn}}
     </div>
    <div class="table_info_num" v-else-if="isFoodCourtMode&&openTable.storeName">
      <span >{{foodCourtHeadText}}</span>
    </div>
    <div class="table_info_num" v-else-if="openTable.tableNumber!='PREORDER'&&openTable.tableNumber!='TAKEAWAY'">
      <img src="../static/img/newImage/navTableIcon.jpg" class="header-tableIcon navHeaderIcon" />
      <span class="header-tableNumber" style="padding: 0 0.1rem">:</span>
      <span>{{ openTable.tableNumber }}</span>
    </div>
    <!-- 预点餐头部定制化文字显示 -->
    <div class="table_info_num topNavTitle" v-else-if="openTable.tableNumber=='PREORDER'">
      {{ systemLanguage.preOrderTopNavTitle }}
    </div>
    <div class="table_info_num" v-else-if="openTable.tableNumber=='TAKEAWAY'">
      <span>{{ systemLanguage.pickupTakes }}</span>
    </div>`,
  computed: {
    isFoodCourtMode() {
      let { mode } = JSON.parse(sessionStorage.getItem("mode") || "{}")
      return mode === "FoodCourt"
    },
    // 在店铺里则显示店铺的storeName,反正这*的配置
    foodCourtHeadText() {
      let showFoodCourt = sessionStorage.getItem("showFoodCourtView") === "true"
      let { storeName, language, selectedStoreConfig = {} } = this.openTable
      return showFoodCourt
        ? storeName[language]
        : selectedStoreConfig.storeName
        ? selectedStoreConfig.storeName[language]
        : ""
    },
    dineIn() {
      let { dineInStoreNumber = "", tableNumber } = this.openTable
      dineInStoreNumber = dineInStoreNumber
        .toString()
        .split(";")
        .filter(e => e)
      return dineInStoreNumber.includes(tableNumber)
    }
  },
  methods: {}
}
