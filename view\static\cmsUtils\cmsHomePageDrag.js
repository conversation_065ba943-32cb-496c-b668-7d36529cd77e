// 拖拽组件初始化
var el = document.getElementById("body_warp")
// sortable.option('sort', false);

/**
 * @description: 拖拽排序配置
 * @param orderSort 每次点击排序的数据
 * @param nowCardList 当前容器的数据
 * @param firstActiveArray 未拖拽前的数据
 * @param flag 未拖拽前的标识
 */
let orderSort = [],
  nowCardList = [],
  firstActiveArray,
  isFirstDrag = true
var sortable = Sortable.create(el, {
  disabled: true,
  // sort: false,
  filter: ".process", // 过滤器，不需要进行拖动的元素
  dataIdAttr: "sortable-id",
  group: "localStorage-sortable",
  // store: {//缓存到localStorage
  //   get: function (sortable) {
  //     var order = localStorage.getItem(sortable.options.group.name);
  //     return order ? order.split('|') : [];
  //   },
  //   set: function (sortable) {
  //     var order = sortable.toArray();
  //     localStorage.setItem(sortable.options.group.name, order.join('|'));
  //   }
  // },

  onChoose: function (/**Event*/ evt) {
    // orderSort = sortable.toArray()
    // console.log(orderSort, '开始');
    // console.log(evt, '开始');
    // console.log(sortable.toArray(), '开始点击');
    // sortable.options.store.set(sortable)

    var childElement = $("#body_warp").children("div")
    childElement.each(function (e, i) {
      $(this).attr("sortable-id", e)
    })
    orderSort = sortable.toArray()

    nowCardList = []
    $(evt.to)
      .children()
      .each(function (e, i) {
        nowCardList.push($(this).children().attr("card-type"))
      })
    // console.log(nowCardList, orderSort, '排序');
  },

  onUpdate: function (/**Event*/ evt) {
    let list = getActiveList()
    let requestData = {
      superiorCode: "",
      superiorType: 1
    }
    // console.log(JSON.parse(JSON.stringify(pathList)), 'pathList');
    if (pathList.length != 0) {
      let superiorItem = pathList[pathList.length - 1] //上级Item
    }
    console.log(requestData, "requestData")
    let array = getStratDrag(list)
    console.log(array, "打印的list")
    let nowElementType = nowCardList[evt.newIndex]
    let comparisonElementType = nowCardList[evt.oldIndex]
    let allType = ["foodType", "mType"]
    if (nowElementType == comparisonElementType) {
      // console.log('相同数据类型可以拖拽');
    } else {
      if (allType.includes(nowElementType) && allType.includes(comparisonElementType)) {
        // console.log('相同数据类型allType可以拖拽');
      } else {
        sortable.sort(orderSort)
        toastr.error("Different types of data cannot be dragged")
        console.log("不同数据类型")
      }
    }
    // console.log(comparisonElementType, nowElementType, '前后元素');
    // console.log(evt.oldIndex, evt.newIndex, '前后位置');
  }
})
// 重置排序,遍历初始数组，对比重新设置sortable-id属性
// card-typ:foodType,mType,mlist,food
function onResetDrag() {
  let firstActiveList = getActiveList() // 获取未提交前的所有原始数据
  let firstActiveArray = getStratDrag(firstActiveList) // 处理数据
  var childElement = $("#body_warp").children("div")
  childElement.each(function (e, i) {
    // 数组返回type相同的索引值
    let divType = $(this).children("div").attr("card-type")
    let tableId = $(this).children("div").children("table").attr("id")
    // 截取_后面的字符串
    let code = tableId.split("_")[1]
    let firstIndex = firstActiveArray.findIndex(item => {
      return item.typeName == divType && item.code == code
    })
    $(this).attr("sortable-id", firstIndex)
  })
  sortable.sort(orderSort)
}
// 拖拽确认添加请求接口
function onSubDrag() {
  let data = getEndDragData() // 获取最终排序数据
  let versionNumber = sessionStorage.getItem("versionNumber")
  let domain = sessionStorage.getItem("domain")
  let storeNumber = sessionStorage.getItem("storeNumber")
  $.post({
    url: "../../manager_itemSort/update",
    data: JSON.stringify(data),
    dataType: "json",
    contentType: "application/json;charset=UTF-8",
    traditional: true, // 防止深度序列号
    headers: {
      domain,
      storeNumber,
      versionNumber: versionNumber == "PROD" ? "" : versionNumber
    },
    // 前置操作
    beforeSend: function () {
      toastr.warning("Edit...", "Please wait", {
        timeOut: 0,
        extendedTimeOut: 0
      })
    },
    success: res => {
      toastr.clear()
      if (res.statusCode == 200) {
        toastr.success("Successfully Edit")
        console.log(pathList.length, pathList, "成功后请求pathList")
        let hierarchy = pathList.length
        if (hierarchy != 0) {
          // 有嵌套相同的数据修改排序需要对应更改index索引
          let parentCode = pathList[hierarchy - 1].id
          let parentType = pathList[hierarchy - 1].type
          // 遍历出相同id和type的子集数据
          let childrenIndex = []
          pathList.forEach((item, index) => {
            if (item.id == parentCode && item.type == parentType) {
              if (index + 1 < hierarchy) childrenIndex.push(index + 1)
            }
          })
          console.log(parentCode, parentType, childrenIndex, "测试相同")
          if (childrenIndex.length) {
            console.log("有相同数据")
            childrenIndex.forEach(i => {
              editPathListIndex(i, data)
            })
          }
        }
        initData(pathList.length)
      } else {
        switch (res.statusCode) {
          case 401:
            toastr.error("Data for this store already exists")
            break
          default:
            toastr.error("Fail to Edit")
            break
        }
      }
    },
    error: error => {
      // console.log(res);
      toastr.error("Fail to Edit")
    }
  })
}

// 获取当前选中下的数据
function getActiveList() {
  let tempPath = pathList.length
  let list
  // console.log(pathList.length, "pathList.length")
  let pathListObj = {
    0: foodTypeList,
    1: foodList,
    3: siClickItem,
    4: wuClickItem,
    5: liuClickItem,
    6: qiClickItem,
    7: baClickItem,
    8: jiuClickItem
  }
  if (pathList.length == 2) {
    var foodType = foodTypeList[pathList[0].index]
    list = foodType.foodList[pathList[1].index]
  } else {
    list = pathListObj[tempPath]
  }
  return list
}
function getStratDrag(data) {
  let array = []
  if (Array.isArray(data)) {
    data.forEach(item => {
      array.push({ code: item.code || item.fCode, typeName: item.fCode ? "food" : "foodType" })
    })
  } else {
    let foodList = data.foodList || []
    let mListList = data.mListList || []
    let allTypeArry = data.allTypeArry || []
    foodList.forEach(item => {
      array.push({ code: item.fCode, typeName: "food" })
    })
    mListList.forEach(item => {
      array.push({ code: item.code, typeName: "mlist" })
    })
    allTypeArry.forEach(item => {
      array.push({ code: item.code, typeName: item.typeName == "ftyItem" ? "foodType" : "mType" })
    })
  }
  return array
}

// 遍历获取拖拽结束后的数据
function getEndDragData() {
  let endDragObj = {
    fSort: "", // food的Code
    mlSort: "", // mlist的Code
    ftSort: "", // fty的Code
    ftSortIndex: "", // fty的索引
    mtSort: "", // mty的Code
    mtSortIndex: "" // mty的Code
  }
  let targetDiv = $("#body_warp").children("div")
  let allTypeIndex = -1
  targetDiv.each(function (itemIndex, dom) {
    let type = $(this).children("div").attr("card-type") //遍历获取拖拽结束后每个item的类型
    // let index = $(this).attr("sortable-id") //遍历获取拖拽结束item的索引
    let tableId = $(this).children("div").children("table").attr("id") //遍历获取拖拽结束item的code
    // 截取_后面的字符串
    let code = tableId.split("_")[1]
    if (type == "foodType" || type == "mType") allTypeIndex++ //索引从0开始自增
    switch (type) {
      case "foodType":
        endDragObj.ftSort = endDragObj.ftSort + code + ","
        endDragObj.ftSortIndex = endDragObj.ftSortIndex + allTypeIndex + ","
        break
      case "mType":
        endDragObj.mtSort = endDragObj.mtSort + code + ","
        endDragObj.mtSortIndex = endDragObj.mtSortIndex + allTypeIndex + ","
        break
      case "mlist":
        endDragObj.mlSort = endDragObj.mlSort + code + ","
        break
      default:
        // 默认类型food
        endDragObj.fSort = endDragObj.fSort + code + ","
        break
    }
  })
  // 处理数据(遍历对象判空,删除最后一个逗号)
  for (let key in endDragObj) {
    if (endDragObj[key] == "") {
      // 如果为空则删除该键
      delete endDragObj[key]
    } else {
      endDragObj[key] = endDragObj[key].substring(0, endDragObj[key].length - 1)
    }
  }
  // 添加上层索引
  let typeObj = {
    fty: 1,
    food: 2,
    Mtype: 4,
    Mlist: 5
  }

  if (pathList.length != 0) {
    endDragObj["superiorCode"] = pathList[pathList.length - 1].id
    endDragObj["superiorType"] = typeObj[pathList[pathList.length - 1].type]
  } else {
    endDragObj["superiorCode"] = ""
    endDragObj["superiorType"] = 1
  }

  console.log(endDragObj, "foodDragObj")
  return endDragObj
}

function editPathListIndex(index, requestData) {
  let item = pathList[index],
    pathListCode = item.id,
    pathListType = item.type
  // 获取某个字符串的索引
  console.log(item, "相同的子集Item")
  let mapObj = {
    fty: {
      codeSort: "ftSort",
      indexSort: "ftSortIndex"
    },
    Mtype: {
      codeSort: "mtSort",
      indexSort: "mtSortIndex"
    },
    food: {
      codeSort: "fSort"
    },
    mlist: {
      codeSort: "mlSort"
    }
  }

  let stringCode = mapObj[pathListType]["codeSort"]
  // 截取逗号隔开的字符串
  let codeListArry = requestData[stringCode].split(",")
  // 获取code在数组中的索引
  let codeIndex = codeListArry.indexOf(pathListCode)
  if (pathListType == "food" || pathListType == "mlist") {
    item.index = codeIndex
    console.log(item, "修改的数据food或mlist")
  } else {
    let indexListArry = requestData[mapObj[pathListType]["indexSort"]].split(",")
    item.index = +indexListArry[codeIndex]
    console.log(item, "修改的数据fty或mty")
  }
  console.log(pathList, "相同修改数据后")
}
// 回显层级替换最新数据
function replaceData(foodTypeList) {
  if (pathList.length && pathList.length >= 2) {
    var foodType = foodTypeList[pathList[0].index]
    var food = foodType.foodList[pathList[1].index] //第二层food;
    let typeObj = {
      fty: "allTypeArry",
      food: "foodList",
      Mtype: "allTypeArry",
      Mlist: "mListList"
    }
    pathList.forEach((item, i) => {
      let dataType = [typeObj[item.type]]
      // console.log(item, dataType, "dataType--" + i)
      switch (i) {
        case 2:
          siClickItem = food[dataType][item.index]
          // console.log(siClickItem, "siClickItem")
          break
        case 3:
          wuClickItem = siClickItem[dataType][item.index]
          // console.log(wuClickItem, "wuClickItem")
          break
        case 4:
          liuClickItem = wuClickItem[dataType][item.index]
          // console.log(liuClickItem, "liuClickItem")
          break
        case 5:
          qiClickItem = liuClickItem[dataType][item.index]
          // console.log(qiClickItem, "qiClickItem")
          break
        case 6:
          baClickItem = qiClickItem[dataType][item.index]
          // console.log(baClickItem, "baClickItem")
          break
        case 7:
          jiuClickItem = baClickItem[dataType][item.index]
          break
        default:
        // createFoodTypeElm();
      }
    })
  }
}
