const VerticalOrderLayout = Vue.component("vertical-order-layout", {
  props: {
    tabIsActive: String, //当前选中的ftyCode
    tabDataList: Array,
    inListTitle: Function,
    outListTitle: Function,
    hasFoodList: Function,
    displayMemberFtype: Function,
    showOuterPrice: Function,
    showAddCartBtn: Function,
    imgTagUrl: Function,
    priceName: Function,
    additem: Function,
    onfoodInfo: Function
  },

  data() {
    return {
      activeFtyIndex: null,
      ftyArray: [],
      verticalInitScroll: null,
      foodItemHeight: 0
    }
  },
  computed: {
    replaceH_() {
      return originalUrl => {
        // // 获取根元素字体大小
        const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
        // 计算4rem对应的像素值
        const heightInPixels = Math.ceil(4 * rootFontSize) //向上取整
        // const newUrl = originalUrl.replace(/h_\d+/, `h_${heightInPixels}`).replace(/w_\d+,?/, "")
        const newUrl = originalUrl.replace(/h_\d+/, `h_${heightInPixels}`)
        return newUrl
      }
    }
  },

  created() {},
  mounted() {},
  watch: {
    tabDataList(newVal) {
      this.ftyArray = newVal
    },
    tabIsActive(newVal) {
      this.updateActiveFty(newVal)
    }
  },
  methods: {
    updateActiveFty(code) {
      this.activeFtyIndex = this.ftyArray.findIndex(item => item.code === code)
    },
    // 锚点导航-主要代码逻辑
    goVSidebarItem(sItem) {
      let { code } = sItem
      app.tabIsActive = code
      this.removeEventListener() //移除滚动监听(最后两栏数据较少一页显示会导致点击最后一项无法高亮,需要移除监听再添加监听)
      const targetElement = document.querySelector(`div[data-code="${code}"]`)
      if (targetElement) {
        // 立即滚动到目标元素
        targetElement.scrollIntoView({ block: "start" })
        setTimeout(() => {
          this.addEventListener()
        }, 100)
      }
      // console.log("🚀 ~ goVSidebarItem ~ code:", code)
    },
    // 滚动监听器
    onScroll() {
      const scrollItems = document.querySelectorAll(".vertical-content-foodCard")
      let newActiveCode = null,
        newActiveIndex = null
      let { distanceNavHeight = 0 } = app.openTable.verticalOrderLayout //自定义距离顶部高度
      // let distanceNavHeight = 200 //自定义距离顶部高度
      for (let i = scrollItems.length - 1; i >= 0; i--) {
        // 为了确保与content容器的相对位置，我们需要获取content容器的scrollTop值
        const contentScrollTop = this.$refs.contentScrollContainer.scrollTop + distanceNavHeight
        // 确定当前滚动项相对于content容器顶部的距离
        const itemTopRelativeToContainer =
          scrollItems[i].offsetTop - this.$refs.contentScrollContainer.offsetTop
        // 判断滚动条滚动距离是否大于当前滚动项相对于content容器顶部的距离
        if (contentScrollTop >= itemTopRelativeToContainer) {
          //获取scrollItems[i]的id,截取tab后面的数字
          newActiveCode = scrollItems[i].getAttribute("data-code")
          newActiveIndex = +scrollItems[i].id.replace("tab", "")
          break
        }
      }
      if (newActiveCode !== null && app.tabIsActive !== newActiveCode) {
        app.tabIsActive = newActiveCode
        // this.activeFtyIndex = newActiveIndex
        this.$nextTick(() => {
          const DOM = document.querySelector(".sidebar-item.activeStyle")
          DOM.scrollIntoView({
            block: "center"
          })
        })
      }
    },

    sidebarItemClass(code, index) {
      return {
        "sidebar-item": true,
        activeStyle: app.tabIsActive === code,
        "btr-radius": this.activeFtyIndex + 1 === index,
        "bbr-radius": this.activeFtyIndex - 1 === index
      }
    },

    handleClick(item) {
      if (item.isExpired) {
        app.layerDia(app.systemLanguage.dataTimeOutTip)
      }
    },
    addEventListener() {
      this.$refs.contentScrollContainer.addEventListener("scroll", this.onScroll, {
        passive: true
      })
    },
    removeEventListener() {
      this.$refs.contentScrollContainer.removeEventListener("scroll", this.onScroll)
    },

    intoScroll(code) {
      this.removeEventListener()
      const targetElement = document.querySelector(`div[data-code="${code}"]`)

      if (targetElement) {
        // 左边导航栏滚动到当前选中的项
        const DOM = document.querySelector(".sidebar-item.activeStyle")
        DOM.scrollIntoView({
          block: "center"
        })
        //右边食品列表滚动到当前选中的项
        targetElement.scrollIntoView({ block: "start" })
        setTimeout(() => {
          this.addEventListener()
        }, 100) // 100毫秒延时，根据需要调整
      }
    }
  },
  template: `      
  <div id="vertical-component">  
  <div class="vertical-sidebar">
    <div
      ref="sidebarItems"
      :id="'fty-'+sItem.code"
      v-for="(sItem, e)  in ftyArray"
      :key="sItem.code"
      @click="goVSidebarItem(sItem)"
      :class="sidebarItemClass(sItem.code,e)"
      v-if="hasFoodList(sItem)"
      v-show="displayMemberFtype(sItem)"
    >
      <div>{{outListTitle(sItem)}}</div>
    </div>
  </div>
  <div class="vertical-content" ref="contentScrollContainer">
    <div
      class="vertical-content-foodCard"
      v-for="(ftyItem, i) in ftyArray"
      :key="ftyItem.code"
      :id="'tab'+i"
      :data-code="ftyItem.code"
      v-if="hasFoodList(ftyItem)&&displayMemberFtype(ftyItem)"
    >
      <div class="foodType-title">{{ outListTitle(ftyItem)}}</div>
      <div class="food-item" v-for="(item, index) in ftyItem.foodList" :key="item.fCode" @click="onfoodInfo(item)"
      >
        <div
          class="vertical-food-mask"
          v-show="item.itemCtrl||item.isExpired"
          @click.stop="handleClick(item)"
        >
           <img v-show="item.itemCtrl" class="vertical-food-soldOutImg" src="../static/img/newImage/soldOut.jpg" />
        </div>
        <!-- 图片标识(热门/新品) -->
        <template v-if="item.imageTagName">
          <div :class="'img_tag_'+item.imageTagAzimuth">
            <img
              v-for="(extraPaths, i) in item.imageTagName.split(',')"
              :src="imgTagUrl(item,extraPaths)"
              class="img_tag"
              :key="i"
            />
          </div>
        </template>
        <img v-lazy="{ src: replaceH_(item.imglink), error: '../static/img/newImage/imgPh.jpg' }" class="vertical-food-thumbnail" />
        <div class="food-details">
          <div class="food-details-left">
            <p class="name">{{inListTitle(item)}}</p>
            <p class="price" v-show="item[priceName('foodList')]">
              {{showOuterPrice(item[priceName('foodList')])}}
            </p>
          </div>
          <button class="add-btn" v-if="showAddCartBtn(item)" @click.stop="additem(event, item)">+</button>
        </div>
      </div>
    </div>
  </div>
</div>

  
  `
})
