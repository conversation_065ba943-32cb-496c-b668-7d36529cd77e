const VuetifyBaseDia = {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: void 0
    },
    content: {
      type: String,
      default: void 0
    },
    action: {
      type: Array,
      default: []
    }
  },

  template: `
     <v-dialog
      v-model="show"
      persistent
    >
      <v-card>
        <v-card-title>{{title}}</v-card-title>
        <v-card-text style="font-size: 0.4rem">
          <p> {{content}}</p>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            v-for="btn in action"
            key="btn.text"
            :color="btn.color"
            text
            @click="btn.handler"
          >
            {{btn.text}}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>`
}
