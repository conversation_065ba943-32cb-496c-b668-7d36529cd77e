<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :header-cell-style="{fontSize: '15px'}"
          :max-height="tableHeight"
          empty-text="No Data"
        >
          <el-table-column prop="storeNumber" label="Store Number" align="center">
            <template slot-scope="scope">
              <el-tag>{{scope.row.storeNumber}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop=" tableNumber" label="Table Number" align="center">
            <template slot-scope="scope">
              <el-tag>{{scope.row.tableNumber}}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="saveOrderInterval"
            label="Save Order Interval(s)"
            align="center"
            width="180"
          ></el-table-column>
          <el-table-column prop label="Operation" align="center">
            <el-table-column width="100" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible = true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="onDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 類別Add弹窗 -->
      <template>
        <el-dialog title="Add Dialog" :visible.sync="addDialogVisible" @close="addCloseDialog">
          <el-form :model="addForm" ref="addForm" label-width="auto" :rules="rules">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="addForm.storeNumber"
                placeholder="Please enter the store number"
              ></el-input>
            </el-form-item>
            <el-form-item label="Table Number" prop="tableNumber">
              <el-input
                v-model="addForm.tableNumber"
                placeholder="Please enter the table number"
              ></el-input>
            </el-form-item>
            <el-form-item label=" Save Order Interval(s)" prop="saveOrderInterval">
              <el-input
                v-model.number="addForm.saveOrderInterval"
                placeholder="Please enter the interval time"
              ></el-input>
            </el-form-item>
            <div class="dialog_footer">
              <el-button type="primary" @click="addDialogVisible=false">Cancel</el-button>
              <el-button style="margin-right: 8px" @click="onAdd('addForm')">Add</el-button>
            </div>
          </el-form>
        </el-dialog>
      </template>
      <!-- 单元格编辑弹窗 -->
      <template>
        <el-dialog title="Edit Dialog" :visible.sync="editDialogVisible">
          <el-form :model="editForm" ref="editForm" label-width="auto">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="editForm.storeNumber"
                placeholder="Please enter the store number"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="Table Number" prop="tableNumber">
              <el-input
                v-model="editForm.tableNumber"
                placeholder="Please enter the table number"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item
              label=" Save Order Interval(s)"
              prop="saveOrderInterval"
              :rules="[
                  { type: 'number', message: 'must be a numeric value' },
                ]"
            >
              <el-input
                v-model.number="editForm.saveOrderInterval"
                placeholder="Please enter the interval time;"
              ></el-input>
            </el-form-item>
            <div class="dialog_footer">
              <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
              <el-button style="margin-right: 8px" @click="subEdit">Submit</el-button>
            </div>
          </el-form>
        </el-dialog>
      </template>
      <!-- 单元格删除弹窗 -->
    </div>
    <script>
      var app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          domain: sessionStorage.getItem("domain"),
          addDialogVisible: false,
          delDialogVisible: false,
          editDialogVisible: false,
          rules: {
            storeNumber: [
              {
                required: true,
                message: "Please enter the store number",
                trigger: "blur"
              }
            ],
            tableNumber: [
              {
                required: true,
                message: "Please enter the table number",
                trigger: "blur"
              }
            ],
            saveOrderInterval: [{ type: "number", message: "must be a numeric value" }]
          },
          addForm: {
            storeNumber: "",
            tableNumber: "",
            saveOrderInterval: ""
          },
          editForm: {
            storeNumber: "",
            tableNumber: "",
            saveOrderInterval: "",
            domain: ""
          },
          // delForm: {
          //   storeNumber: '',
          //   tableNumber: '',
          //   saveOrderInterval: '',
          //   domain: '',
          // },
          delsubForm: {
            sign: "",
            restrictions2: "",
            store_number: ""
          },
          tableData: []
        },
        created() {
          this.getData()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整
        },
        methods: {
          getData() {
            let data = {
              domain: this.domain
            }
            $.get({
              url: "../../manager_table/getAll",
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode === 200) {
                  console.log(res, "数据")
                  this.tableData = res.data
                } else {
                  this.$message.error(res.data || "Fail to load!")
                }
              },
              error: function (error) {
                console.log(error)
                // this.$message.error('Fail to load！');
              }
            })
          },
          onAdd(addForm) {
            let data = this.addForm
            data = {
              ...data,
              domain: this.domain
            }
            console.log(data)
            this.$refs[addForm].validate(valid => {
              if (valid) {
                $.post({
                  url: "../../manager_table/addOne",
                  data,
                  dataType: "json",
                  success: res => {
                    if (res.statusCode === 200) {
                      this.getData()
                      this.$message.success("Successfully added！")
                      this.addDialogVisible = false
                    } else {
                      this.$message.error(res.data || "Fail to add！")
                    }
                  },
                  error: () => {
                    // console.log(res);
                    this.$message.error("Fail to add！")
                  }
                })
              } else {
                this.$message.error("Fail to add！")
              }
            })
          },
          onEdit(index, row) {
            console.log(row)
            this.editDialogVisible = true
            let { storeNumber, tableNumber, saveOrderInterval, domain } = row
            this.editForm = {
              storeNumber: row.storeNumber,
              tableNumber: row.tableNumber,
              saveOrderInterval: row.saveOrderInterval,
              domain: row.domain
            }
          },
          subEdit() {
            let data = this.editForm

            $.post({
              url: "../../manager_table/updateOne ",
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode === 200) {
                  this.getData()
                  this.$message.success("Edit success！")
                  this.editDialogVisible = false
                } else {
                  this.$message.error(res.data || "Edit failure！")
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Edit failure！")
              }
            })
          },
          onDel(index, row) {
            // this.delDialogVisible = true;
            let { storeNumber, tableNumber, saveOrderInterval, domain } = row
            let data = {
              storeNumber: row.storeNumber,
              tableNumber: row.tableNumber,
              saveOrderInterval: row.saveOrderInterval,
              domain: row.domain
            }

            $.post({
              url: "../../manager_table/deleteOne",
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode === 200) {
                  this.getData()
                  this.$message.success("Successfully delete！")
                } else {
                  this.$message.error(res.data || "Fail to delete！")
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Fail to delete！")
              }
            })
          },

          // 上传对话框关闭事件
          addCloseDialog() {
            // 点击关闭 数据重置
            this.$refs["addForm"].resetFields()
            // this.$refs.upload.clearFiles();
            // this.addForm = {
            //   type_name: '',
            //   sign: 'false',
            //   restrictions1: '',
            //   restrictions2: '',
            //   store_number: '',
            // };
          }
        }
      })
    </script>
  </body>
</html>
