Vue.component("recharge-pay-success", {
  props: ["systemLanguage", "loading", "parmObj", "currencyWay", "isShowPayTimeTxt", "PayTip"],
  template: `
<div class="rechargeBox-success-page">
  <div class="rechargeBox-content">
    <div class="rechargeBox-success-header">
      <img src="../static/img/payImage/sucPay.jpg" alt="" class="rechargeBox-success-icon" />
      <div class="rechargeBox-success-title">{{PayTip}}</div>
      <div class="rechargeBox-success-amount">
        <div :class="{'rechargeBox-text-loading': loading}" class="rechargeBox-amount-container">
          <template v-if="!loading">
            <span class="rechargeBox-currency-symbol">{{currencyWay}}</span>
            {{parmObj.amt}}
          </template>
        </div>
      </div>
      <div
        class="rechargeBox-success-tip"
        v-if="!loading&&parmObj.statusCode == 200"
        v-html="systemLanguage.rechargeSuccessTxt"
      ></div>
    </div>

    <div class="rechargeBox-info-container">
      <!-- 基本支付信息 -->
      <div class="rechargeBox-info-item">
        <span class="rechargeBox-info-label">{{systemLanguage.successPayAmt}}</span>
        <span class="rechargeBox-info-value" :class="{'rechargeBox-text-loading': loading}">
          <template v-if="!loading&&parmObj.amt">{{currencyWay}}{{parmObj.amt}}</template>
        </span>
      </div>

      <div class="rechargeBox-info-item">
        <template v-if="isShowPayTimeTxt">
          <span class="rechargeBox-info-label">{{systemLanguage.successPayTime}}</span>
          <span class="rechargeBox-info-value" :class="{'rechargeBox-text-loading': loading}">
            <template v-if="!loading">{{parmObj.time}}</template>
          </span>
        </template>
        <template v-else>
          <span class="rechargeBox-info-label">{{systemLanguage.successChargeCreateTime}}</span>
          <span class="rechargeBox-info-value" :class="{'rechargeBox-text-loading': loading}">
            <template v-if="!loading">{{parmObj.createTime}}</template>
          </span>
        </template>
      </div>

      <div class="rechargeBox-info-item">
        <span class="rechargeBox-info-label">{{systemLanguage.successPayMerchantRef}}</span>
        <span class="rechargeBox-info-value" :class="{'rechargeBox-text-loading': loading}">
          <template v-if="!loading">{{parmObj.merchantRef}}</template>
        </span>
      </div>

      <div class="rechargeBox-info-item">
        <span class="rechargeBox-info-label">{{systemLanguage.successPayMethod}}</span>
        <span class="rechargeBox-info-value" :class="{'rechargeBox-text-loading': loading}">
          <template v-if="!loading">{{formatPayVal(parmObj.payVal)}}</template>
        </span>
      </div>

      <!-- 储值卡信息 -->
      <div class="rechargeBox-info-row" v-if="showCardInfo">
        <div class="rechargeBox-info-col">
          <span class="rechargeBox-info-label">{{systemLanguage.savingsCardDate}}</span>
          <div class="rechargeBox-points-change">
            <template v-if="!loading">
              <template v-if="parmObj.finalSavingsCardDate">
                <div class="rechargeBox-original-value" v-if="parmObj.originalSavingsCardDate">
                  <span class="rechargeBox-original-value-text">
                    {{parmObj.originalSavingsCardDate}}
                  </span>
                </div>
                <span class="rechargeBox-info-value">{{parmObj.finalSavingsCardDate}}</span>
              </template>
              <template v-else>-</template>
            </template>
            <template v-else>
              <div class="rechargeBox-skeleton-row">
                <div class="rechargeBox-text-loading rechargeBox-small-loading"></div>
                <div class="rechargeBox-text-loading"></div>
              </div>
            </template>
          </div>
        </div>
        <div class="rechargeBox-info-divider"></div>
        <div class="rechargeBox-info-col">
          <span class="rechargeBox-info-label">{{systemLanguage.savingsCardPoints}}</span>
          <div class="rechargeBox-points-change">
            <template v-if="!loading">
              <template v-if="parmObj.finalSavingsCardPoints">
                <div class="rechargeBox-original-value">
                  <span class="rechargeBox-original-value-text">
                    {{parmObj.originalSavingsCardPoints}}
                  </span>
                  <span class="rechargeBox-points-increase">
                    +{{addPoints(parmObj.finalSavingsCardPoints ,
                    parmObj.originalSavingsCardPoints)}}
                  </span>
                </div>
                <div class="rechargeBox-info-value">{{parmObj.finalSavingsCardPoints}}</div>
              </template>
              <template v-else>-</template>
            </template>
            <template v-else>
              <div class="rechargeBox-skeleton-row">
                <div class="rechargeBox-text-loading rechargeBox-small-loading"></div>
                <div class="rechargeBox-text-loading rechargeBox-large-loading"></div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 会员信息 -->
      <div class="rechargeBox-info-row" v-if="showMemberInfo">
        <div class="rechargeBox-info-col">
          <span class="rechargeBox-info-label">{{systemLanguage.membershipDate}}</span>
          <div class="rechargeBox-points-change">
            <template v-if="!loading">
              <template v-if="parmObj.finalMembershipDate">
                <div class="rechargeBox-original-value" v-if="parmObj.originalMembershipDate">
                  <span class="rechargeBox-original-value-text">
                    {{parmObj.originalMembershipDate}}
                  </span>
                </div>
                <span class="rechargeBox-info-value">{{parmObj.finalMembershipDate}}</span>
              </template>
              <template v-else>-</template>
            </template>
            <template v-else>
              <div class="rechargeBox-skeleton-row">
                <div class="rechargeBox-text-loading rechargeBox-small-loading"></div>
                <div class="rechargeBox-text-loading"></div>
              </div>
            </template>
          </div>
        </div>
        <div class="rechargeBox-info-divider"></div>
        <div class="rechargeBox-info-col">
          <span class="rechargeBox-info-label">{{systemLanguage.membershipPoints}}</span>
          <div class="rechargeBox-points-change">
            <template v-if="!loading">
              <template v-if="parmObj.finalMembershipPoints">
                <div class="rechargeBox-original-value">
                  <span class="rechargeBox-original-value-text">
                    {{parmObj.originalMembershipPoints}}
                  </span>
                  <span class="rechargeBox-points-increase">
                    +{{addPoints(parmObj.finalMembershipPoints , parmObj.originalMembershipPoints)}}
                  </span>
                </div>
                <div class="rechargeBox-info-value">{{parmObj.finalMembershipPoints}}</div>
              </template>
              <template v-else>-</template>
            </template>
            <template v-else>
              <div class="rechargeBox-skeleton-row">
                <div class="rechargeBox-text-loading rechargeBox-small-loading"></div>
                <div class="rechargeBox-text-loading rechargeBox-large-loading"></div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="rechargeBox-footer">
    <div class="rechargeBox-home-btn" @click="onBlack">{{systemLanguage.successPayBlackBtn}}</div>
  </div>
</div>
`,
  data() {
    return {}
  },
  created() {},
  mounted() {},
  computed: {
    showCardInfo() {
      let { finalSavingsCardDate, finalSavingsCardPoints } = this.parmObj
      return finalSavingsCardDate != undefined || finalSavingsCardPoints != undefined
    },
    showMemberInfo() {
      let { finalMembershipDate, finalMembershipPoints } = this.parmObj
      return finalMembershipDate != undefined || finalMembershipPoints != undefined
    }
  },
  watch: {},
  methods: {
    formatPayVal(val) {
      return this.$parent.formatPayVal(val)
    },
    onBlack() {
      this.$parent.onBlack()
      // let openTable = JSON.parse(sessionStorage.getItem("openTable") || "{}")
      // if (
      //   openTable.initTableNumber === "TAKEAWAY" &&
      //   openTable.storeNumber === "*" &&
      //   !sessionStorage.getItem("mode")
      // ) {
      //   window.location.href = "./map.html"
      // } else {
      // }
      // let sourcePage = sessionStorage.getItem("beforeRechargeAndPayment")
      // sessionStorage.setItem("afterRechargeAndPayment", sourcePage)
      // if (sourcePage == "index") {
      //   let indexUrl = localStorage.getItem("indexPageUrl")
      //   window.location.href = indexUrl
      // } else {
      //   this.$parent.onBlack()
      // }
    },

    addPoints(final, original) {
      let hasValue =
        final !== undefined && final !== null && original !== undefined && original !== null //兼容低版本判断有效值
      if (hasValue) {
        return floatSub(final, original)
      }
    }
  }
})
