const BottomSheet = {
  template: `
    <div class="bottom-sheet" :style="{zIndex: zIndex}" ref="bottomSheet" :inert="!showSheet" role="dialog">
      <transition name="fade">
        <div
            :style="{ backgroundColor: overlayColor }"
            @click="clickOnOverlayHandler"
            class="bottom-sheet__overlay"
            v-if="overlay && showSheet"
        />
      </transition>
      <div
          ref="bottomSheetContent"
          :style="{
            maxWidth: maxWidthString,
            maxHeight: maxHeightString,
            transform: translateValueString,
            height: sheetHeightString
          }"
          :class="sheetContentClasses"
      >
        <div ref="bottomSheetHeader" class="bottom-sheet__header" v-if="showHeader">
          <div class="bottom-sheet__draggable-area" ref="bottomSheetDraggableArea">
            <slot name="drag">
              <div class="bottom-sheet__draggable-thumb" :style="{backgroundColor: dragColor}"></div>
            </slot>
          </div>
          <slot name="header" />
        </div>
<!--  :style="{overflowY: showSheet ? 'auto' : 'hidden'}"   关闭时隐藏滚动条   -->
        <div ref="bottomSheetMain" class="bottom-sheet__main">
          <slot />
        </div>
        <div ref="bottomSheetFooter" class="bottom-sheet__footer">
          <slot name="footer" />
        </div>
      </div>
    </div>`,

  props: {
    showHeader: {
      type: Boolean,
      default: true
    },
    overlay: {
      type: Boolean,
      default: true
    },
    overlayColor: {
      type: String,
      default: "#0000004D"
    },
    maxWidth: {
      type: Number,
      default: undefined
    },
    maxHeight: {
      type: Number,
      default: undefined
    },
    overlayClickClose: {
      type: Boolean,
      default: true
    },
    canSwipe: {
      type: Boolean,
      default: true
    },
    closeHeightPercent: {
      type: Number,
      default: 100
    },
    initSheetHeight: {
      type: Number,
      default: undefined
    },
    zIndex: {
      type: Number,
      default: 99999
    },
    customClass: {
      type: String,
      default: ""
    },
    dragColor: {
      type: String,
      default: "#333333"
    }
  },
  data() {
    return {
      showSheet: false,
      translateValue: this.closeHeightPercent,
      isDragging: false,
      contentScroll: 0,
      sheetHeight: 0
    }
  },
  methods: {
    initHeight() {
      this.sheetHeight =
        this.initSheetHeight ??
        this.$refs.bottomSheetHeader.offsetHeight +
          this.$refs.bottomSheetMain.offsetHeight +
          this.$refs.bottomSheetFooter.offsetHeight
    },
    clickOnOverlayHandler() {
      if (this.overlayClickClose) {
        this.close()
      }
    },
    dragHandler(event, type) {
      if (this.canSwipe) {
        this.isDragging = true

        const preventDefault = e => {
          e.preventDefault()
        }

        if (type === "main") {
          this.contentScroll = this.$refs.bottomSheetMain.scrollTop
          document.documentElement.style.overflowY = "hidden"
          document.documentElement.style.overscrollBehavior = "none"
        }

        if (this.showSheet) {
          if (event.deltaY > 0) {
            if (type === "main" && event.type === "panup") {
              this.translateValue = this.pixelToVh(event.deltaY)
              if (event.cancelable) {
                this.$refs.bottomSheetMain.addEventListener("touchmove", preventDefault)
              }
            }

            if (type === "main" && event.type === "pandown" && this.contentScroll === 0) {
              this.translateValue = this.pixelToVh(event.deltaY)
            }

            if (type === "area") {
              this.translateValue = this.pixelToVh(event.deltaY)
            }

            if (event.type === "panup") {
              this.$emit("dragging-up")
            }
            if (event.type === "pandown") {
              this.$emit("dragging-down")
            }
          }
        } else {
          if (type === "main" && event.type === "panup") {
            if (event.cancelable) {
              this.$refs.bottomSheetMain.addEventListener("touchmove", preventDefault)
            }
            let tslVal = this.closeHeightPercent + this.pixelToVh(event.deltaY)
            if (tslVal >= 0) {
              this.translateValue = tslVal
            }
          }
          if (type === "main" && event.type === "pandown" && this.contentScroll === 0) {
            this.translateValue = this.closeHeightPercent + this.pixelToVh(event.deltaY)
          }

          if (type === "area") {
            let tslVal = this.closeHeightPercent + this.pixelToVh(event.deltaY)
            if (tslVal >= 0) {
              this.translateValue = tslVal
            }
          }

          if (event.type === "panup") {
            this.$emit("dragging-up")
          }
          if (event.type === "pandown") {
            this.$emit("dragging-down")
          }
        }

        if (event.isFinal) {
          this.$refs.bottomSheetMain.removeEventListener("touchmove", preventDefault)

          if (type === "main") {
            this.contentScroll = this.$refs.bottomSheetMain.scrollTop
          }
          this.isDragging = false
          if (this.showSheet) {
            if (
              (this.pixelToVh(event.deltaY) >= 15 && this.contentScroll === 0) ||
              (this.pixelToVh(event.deltaY) >= 15 && type === "area")
            ) {
              this.close()
            } else {
              this.open()
            }
          } else {
            if (this.pixelToVh(event.deltaY) <= -5) {
              this.open()
            } else {
              this.close()
            }
          }
        }
      }
    },
    pixelToVh(pixel) {
      const height =
        this.maxHeight && this.maxHeight <= this.sheetHeight ? this.maxHeight : this.sheetHeight
      return (pixel / height) * 100
    },
    close() {
      this.showSheet = false
      this.translateValue = this.closeHeightPercent
      setTimeout(() => {
        document.documentElement.style.overflowY = "auto"
        document.documentElement.style.overscrollBehavior = ""
        this.$emit("closed")
      }, this.transitionDuration * 1000)
    },
    open() {
      this.translateValue = 0
      document.documentElement.style.overflowY = "hidden"
      document.documentElement.style.overscrollBehavior = "none"
      this.showSheet = true
      setTimeout(() => {
        this.$emit("opened")
      }, this.transitionDuration * 1000)
    },
    keyupHandler(event) {
      const isFocused = element => document.activeElement === element
      const isSheetElementFocused =
        this.$refs.bottomSheet.contains(event.target) && isFocused(event.target)

      if (event.key === "Escape" && !isSheetElementFocused) {
        this.close()
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener("keyup", this.keyupHandler)
  },
  async mounted() {
    setTimeout(() => {
      this.initHeight()

      window.addEventListener("keyup", this.keyupHandler)

      if (!this.showHeader) return

      /**
       * Create instances of Hammerjs
       */
      const hammerAreaInstance = new Hammer(this.$refs.bottomSheetDraggableArea, {
        inputClass: Hammer.TouchMouseInput,
        recognizers: [[Hammer.Pan, { direction: Hammer.DIRECTION_VERTICAL }]]
      })

      // const hammerMainInstance = new Hammer(this.$refs.bottomSheetMain, {
      //   inputClass: Hammer.TouchMouseInput,
      //   recognizers: [[Hammer.Pan, { direction: Hammer.DIRECTION_VERTICAL }]]
      // })

      /**
       * Set events and handlers to hammerjs instances
       */
      hammerAreaInstance.on("panstart panup pandown panend", e => {
        this.dragHandler(e, "area")
      })

      // hammerMainInstance.on("panstart panup pandown panend", e => {
      //   this.dragHandler(e, "main")
      // })
    }, 100)
  },
  watch: {
    initSheetHeight(newVal, oldVal) {
      this.initHeight()
    }
  },
  computed: {
    sheetContentClasses() {
      return [
        "bottom-sheet__content",
        {
          "bottom-sheet__content--fullscreen": this.sheetHeight >= window.innerHeight,
          "bottom-sheet__content--dragging": this.isDragging,
          "bottom-sheet__header-border-radius": this.showHeader
        },
        this.customClass
      ]
    },
    maxWidthString() {
      return `${this.maxWidth}px`
    },
    maxHeightString() {
      return this.maxHeight ? `${this.maxHeight}px` : "inherit"
    },
    translateValueString() {
      return `translate3d(0, ${this.translateValue}%, 0)`
    },
    sheetHeightString() {
      return this.sheetHeight && this.sheetHeight > 0 ? `${this.sheetHeight + 1}px` : "auto"
    }
  }
}
