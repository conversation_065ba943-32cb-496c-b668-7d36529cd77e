// touch-listener.js

Vue.component("personalCenter", {
  props: ["systemLanguage", "openTable", "memberCenterLogo"],
  mixins: [registerAndResetMixin, accountMixin],
  template: `<div id="personalCenterPopup">
  <div class="personalCenterWarp">
    <div class="user-center-logout" v-if="loginStatus&&!showAccountList">
      <img
        :src="combinedImageUrl('static/img/newImage/logout.jpg')" 
        alt=""
        class=""
        @click="handUserLogout" 
      />
    </div>
    <div class="user-center-close" v-else @click="onClose">  
      <i class="layui-icon layui-icon-close"></i>
    </div>
    <div class="user-logo" v-if="memberCenterLogo">     
      <img :src="memberCenterLogo" class="" /> 
    </div>

    <div class="user-content member-warp" :class="{ 'no-logo': !memberCenterLogo }">
      <div class="user-fade user-login" :class="{ 'show': !isLogin }">
        <template v-if="!isLogin">
          <div class="user-login-content" :style="{'margin-bottom':memberCenterLogo?'1rem':'2rem'}">
            <p class="user-login-content1">{{systemLanguage.PCLoginTitle}}</p>
            <p class="user-login-content2">{{systemLanguage.PCLoginContent}}</p>
          </div>

          <div class="login-form">
            <form class="layui-form" action="">
              <div
                v-for="field in loginFormConfig"
                :key="field.name"
                class="layui-form-item"
                :class="{'error-field': formErrors[field.name]}"
              >
                <label
                  class="layui-form-label"
                  :style="{width: labelWidth + 'px'}"
                  :class="{'required': field.required}"
                  v-if="field.type !== 'password' || memberAccountManagement"
                >
                  <span ref="formLabel">{{ authFormShowText('formLabel', field.name) }}</span>
                </label>
                <div class="layui-input-block" :style="{marginLeft: labelWidth + 'px'}">
                  <!-- 普通输入框 -->
                  <template v-if="field.type === 'input'">
                    <input
                      :type="field.inputType || 'text'"
                      v-model="formData[field.name]"
                      class="layui-input"
                      :placeholder="authFormShowText('formPH',field.name)"
                      @blur="validateField(field.name)"
                      spellcheck="false"
                    />
                  </template>
                  <div v-if="field.name === 'account'" class="account-input-block">
                    <input
                      :type="field.inputType || 'text'"
                      v-model="formData[field.name]"
                      class="layui-input"
                      :placeholder="authFormShowText('formPH',field.name)"
                      @blur="validateField(field.name)"
                      spellcheck="false"
                    />
                    <!-- 针对只有账号的登录布局, 添加一个按钮 -->
                    <div
                      v-if="!memberAccountManagement"
                      class="submit-btn-inline"
                      @click="handlerUserBtn"
                    >
                      {{systemLanguage.PCLoginLoginBtn}}
                    </div>
                  </div>
                  <!-- 密码输入框 -->
                  <template v-if="field.type === 'password'&&memberAccountManagement">
                    <div class="password-wrapper">
                      <div class="input-container">
                        <input
                          :type="passwordVisible[field.name] ? 'text' : 'password'"
                          v-model.trim="formData[field.name]"
                          class="layui-input"
                          :placeholder="authFormShowText('formPH',field.name)"
                          @blur="validateField(field.name)"
                          spellcheck="false"
                        />
                        <div
                          class="eyeIcon"
                          :class="[passwordVisible[field.name] ? 'icon-eye-open' : 'icon-eye-close']"
                          @click="authFormPwdVisible(field.name)"
                        ></div>
                      </div>
                    </div>
                  </template>

                  <div class="form-item-error" :class="{'show': formErrors[field.name]}">
                    {{ formErrors[field.name] }}
                  </div>
                </div>
              </div>
            </form>

            <div class="form-actions" v-if="memberAccountManagement">
              <div class="submit-btn" @click="handlerUserBtn">
                {{systemLanguage.PCLoginLoginBtn}}
              </div>
              <div class="bottom-links">
                <span class="link-text" @click="handleRegister">
                  {{systemLanguage.PCLoginLftLnkTxt}}
                </span>
                <span class="link-text" @click="forgotPassword">
                  {{systemLanguage.PCLoginRgtLnkTxt}}
                </span>
              </div>
            </div>
          </div>
        </template>
      </div>

      <div class="user-fade" :class="{ 'show': isLogin }">
        <div v-if="isLogin" style="height: 100%">
          <div class="user-account" v-if="showAccountList">
            <div class="user-account-title">
              <i class="layui-icon layui-icon-user"></i>
              {{systemLanguage.PCLoginAccountTitle}}
            </div>
            <div class="user-account-content">
              <div class="user-account-header grid3">
                <p>
                  <i class="layui-icon layui-icon-username"></i>
                  {{systemLanguage.PCLoginAccountNumber}}
                </p>
                <p>
                  <i class="layui-icon layui-icon-diamond"></i>
                  {{systemLanguage.PCLoginAccountPoints}}
                </p>
              </div>
              <div v-for="(acc,e) in accountArray" class="user-account-list grid3">
                <p class="user-account-num">{{ acc.code }}</p>
                <p class="user-account-point">{{ acc.points0?(acc.points0 / 100) : 0 }}</p>
                <div class="select-account-btnWarp">
                  <p class="select-account-btn" @click="checkAccount(acc)">
                    <i class="layui-icon layui-icon-ok"></i>
                    {{systemLanguage.PCLoginAccountSelectBtn}}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <transition name="fade">
            <div class="user-card" v-if="!showAccountList">
              <div class="user-card-header" v-if="showCardHeader">
                <div class="user-card-name" v-if="showCardName">{{userInfo.name}}</div>
                <div class="user-recharge-btn" :disabled="disableRecharge" v-if="showRecharge" @click="onRechargePage">
                  <img :src="combinedImageUrl(imgSrc('Recharge'))" class="recharge-btn-img" />
                  充值
                </div>
              </div>
              <div class="user-card-content">
                <template v-for="(item,i) in userInfoMap" :key="i">
                  <div class="user-card-cell" v-if="isShowCell(item)">
                    <div class="card-cell-label">
                      <img :src="combinedImageUrl(imgSrc(item.type))" class="card-cell-labelImg" />
                      <span>{{systemLanguage[item.label]}}</span>
                    </div>
                    <div
                      class="card-cell-value"
                      :disabled="!userInfo.coupon"
                      @click="showBottomSheet"
                      v-if="item.type==='Coupon'"
                    >
                      {{systemLanguage.show}} &#62;
                    </div>
                    <div class="card-cell-value" v-else>{{userInfo[item.value]}}</div>
                  </div>
                </template>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>

    <div class="user-footer">
      <p class="bottom-tips" v-if="isLogin">{{systemLanguage.memberBottomTips}}</p>
      <div
        class="user-footer-btn-loggedIn"
        @click="handlerUserBtn"
        v-if="isLogin&&!showAccountList"
      >
        {{systemLanguage.PCLoginOrderBtn}}
      </div>
    </div>
  </div>
  <bottom-sheet
    :show-header="false"
    :overlay-click-close="false"
    :transition-duration="0.1"
    :z-index="********"
    :max-height="appHeight"
    :init-sheet-height="appHeight"
    ref="couponBottomSheet"
    v-if="showCoupon"
  >
    <div class="sheet-content">
      <coupons-view
        ref="coupon-page"
        :system-language="systemLanguage"
        :error-handle="handleUserErrorCode"
        :open-table="openTable"
      />
    </div>
    <template #footer>
      <div class="bottom-sheet-footer">
        <button type="button" class="close-btn" @click="closeBottomSheet">
          {{systemLanguage.closeBtn}}
        </button>
      </div>
    </template>
  </bottom-sheet>

  <bottom-sheet
    :show-header="false"
    :overlay-click-close="false"
    :transition-duration="0.1"
    :z-index="********"
    :max-height="appHeight"
    :init-sheet-height="appHeight"
    ref="rechargeBottomSheet"
    v-if="showRecharge"
  >
    <recharge-view
      ref="rechargeView"
      :system-language="systemLanguage"
      :error-handle="handleUserErrorCode"
      :open-table="openTable"
      :closeSheet="closeRechargePage"
      :logoutReset="logoutReset"
    />
  </bottom-sheet>
</div>
`,
  data() {
    return {
      labelWidth: 0,
      appHeight: 0,
      personalCenterPopupIndex: "", //弹窗的index,父组件赋值
      userInfo: {
        points_due: "", // 积分到期日
        expdate: "", // 会员到期日
        code: "", // 会员编号
        name: "", // 会员姓名
        points0: "", // 积分
        telephone: "", // 手机号
        balance: ""
      },
      passwordVisible: {
        password: false
      },
      loginStatus: false,
      showErrorLogin: false,
      showAccountList: false, //是否显示账号列表
      cancelLogin: false,
      autoLogin: false, // 这是点击图标,发起请求的标识
      resetRequestData: "", //重复请求的参数
      showInfo: [],
      features: [
        {
          icon: "layui-icon-gift",
          title: "memberFeatureTitle1",
          desc: "memberFeatureDesc1"
        },
        {
          icon: "layui-icon-star",
          title: "memberFeatureTitle2",
          desc: "memberFeatureDesc2"
        },
        {
          icon: "layui-icon-diamond",
          title: "memberFeatureTitle3",
          desc: "memberFeatureDesc3"
        },
        {
          icon: "layui-icon-date",
          title: "memberFeatureTitle4",
          desc: "memberFeatureDesc4"
        }
      ],
      userInfoMap: [
        {
          type: "Coupon",
          label: "coupons",
          value: "coupon"
        },
        {
          type: "Recharge",
          label: "recharge",
          value: "recharge"
        },
        {
          type: "Telephone",
          label: "perCenterLoginTelephone",
          value: "telephone"
        },
        {
          type: "Email",
          label: "perCenterLoginEmail",
          value: "email"
        },
        {
          type: "Points",
          label: "perCenterLoginPoints",
          value: "points0"
        },
        {
          type: "Balance",
          label: "perCenterLoginBalance",
          value: "balance"
        },
        { type: "Code", label: "perCenterLoginCode", value: "code" },
        {
          type: "Expdate",
          label: "perCenterMemExpDate",
          value: "expdate"
        },
        {
          type: "Points_due",
          label: "perCenterLoginPtsExpDate",
          value: "points_due"
        }
      ],
      accountArray: [], //账户列表
      list: [], //需要更新的flist
      resizeHandler: null, // 放置监听软键盘兼容方法
      formData: {
        account: "",
        password: ""
      },
      formErrors: {},
      baseLoginFormConfig: [
        {
          name: "account",
          type: "account",
          required: true,
          validator: function (value) {
            if (!value) return this.systemLanguage.formReqAccount
            return true
          }
        },
        {
          name: "password",
          type: "password",
          required: false,
          validator: function (value) {
            // if (!value) return this.systemLanguage.formReqPassword
            return true
          }
        }
      ],
      memberAccountManagement: undefined // 会员注册是否开启
    }
  },
  components: {
    BottomSheet
  },
  created() {},
  mounted() {
    let {
      displayCRM: { showInfo },
      memberAccountManagement = undefined
    } = this.openTable
    this.memberAccountManagement = memberAccountManagement
    this.showInfo = showInfo || []
    this.appHeight = document.querySelector("#app").clientHeight
  },
  watch: {
    loginStatus(val) {
      if (this.indexMark) return false
      // 登录登出更改时,都需要更新折扣信息
      // 在落单页登录成功,需要请求折扣
      if (app.shouldRequestDiscount()) {
        app.onRequestDiscount()
      }
      this.updateView()
    },
    isLogin: {
      handler(newVal) {
        if (!newVal) {
          this.$nextTick(() => {
            this.authFormGetLabelWidth() //解决退出登录后,label宽度不更新
          })
        }
      },
      deep: true
    }
  },
  computed: {
    disableRecharge() {
      return (
        mark === "pc_index" &&
        app.initTableNumber == "TAKEAWAY" &&
        app.storeNumber === "*" &&
        !app.urlParams.paramsObj.mode
      )
    },
    showRecharge() {
      return this.showInfo.includes("Recharge") && this.openTable.performType != 1
    },
    showCoupon() {
      return this.loginStatus && this.showInfo.includes("Coupon") && "coupon" in this.userInfo
    },
    showCardHeader() {
      return this.isShowCell({ type: "Name", value: "name" }) || this.showRecharge
    },

    showCardName() {
      return this.isShowCell({ type: "Name", value: "name" })
    },
    renderCouponText() {
      if (this.userInfo.coupon) {
        return this.systemLanguage.couponEffectiveQuantity.replace("#qty", this.userInfo.coupon)
      }
      return this.systemLanguage.couponInvalidQuantity.replace("#qty", this.userInfo.coupon)
    },
    indexMark() {
      return mark === "pc_index"
    },
    isLogin() {
      return this.showAccountList || this.loginStatus
    },
    isShowCell() {
      return item => {
        return this.showInfo.includes(item.type) && item.value in this.userInfo
      }
    },

    imgSrc() {
      return type => {
        let imgName = ""
        if (type == "Expdate" || type == "Points_due") {
          imgName = `Date.jpg`
        } else {
          imgName = `${type}.jpg`
        }
        return `static/img/userCenter/${imgName}`
      }
    },
    loginFormConfig() {
      return this.baseLoginFormConfig.filter(field => {
        if (field.name === "password") {
          return this.memberAccountManagement
        }
        return true
      })
    }
  },

  methods: {
    onRechargePage() {
      if (this.disableRecharge) {
        layer.msg(this.systemLanguage.emptyStoreRecharge) //外卖模式缺少店铺
        return
      }
      const bottomSheet = this.$refs["rechargeBottomSheet"]
      const rechargeView = this.$refs["rechargeView"]
      if (rechargeView) {
        rechargeView.initRecharge()
      }
      bottomSheet && bottomSheet.open()
    },
    closeRechargePage() {
      const bottomSheet = this.$refs["rechargeBottomSheet"]
      bottomSheet && bottomSheet.close()
    },
    showBottomSheet() {
      if (!this.userInfo["coupon"]) return false
      const bottomSheet = this.$refs["couponBottomSheet"]
      const instance = this.$refs["coupon-page"]
      if (instance && !instance.loading) instance.init()
      bottomSheet && bottomSheet.open()
    },
    closeBottomSheet() {
      const bottomSheet = this.$refs["couponBottomSheet"]
      bottomSheet && bottomSheet.close()
    },

    // 是否存在cookie信息,若在computed中會不同步
    authFormShowText(prefix, name) {
      return this.systemLanguage[`${prefix}${this.capitalize(name)}`]
    },
    capitalize(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },
    // 是否存在cookie信息,若在computed中���不同步
    LoginValid() {
      return Cookies.get("userLogged") !== undefined
    },
    // 会员退出登录后更新视图,隐藏需要会员的f/t
    updateView() {
      this.list = this.list.length ? this.list : this.getAllPointFood()
      //找到所有需要更新的fty,找到对应的ref,执行$forceUpdate
      this.list.forEach(i => {
        let target = Array.of(app.$refs["food-component"])[i]
        target && target.$forceUpdate()
      })
      this.list.length && app.$refs["custom-tabs"].$forceUpdate()
      //  还可以处理若active当前积分fty,退出会员后重设active
      if (this.list.find(e => e.code === app.tabIsActive)) {
        // 选中第一个fty
        app.tabIsActive = app.tabDataList.length && app.tabDataList[0].code
      }
    },
    getAllPointFood() {
      let indexes = []
      app.tabDataList.forEach((el, i) => {
        if (!this.displayPointsFty(el)) {
          indexes.push({ index: i, code: el.code })
        }
      })
      return indexes
    },

    // 未登录状态根据积分判断是否隐藏fty
    // app.displayMemberFtype 会判断登录状态
    displayPointsFty(typeItem) {
      if (this.openTable.memberConfig && this.openTable.memberConfig.pointsShowInLogin) {
        // 判断固定细项
        let { foodList = [], mListList = [] } = typeItem
        let list = [...foodList, ...mListList]
        // 若有一个细项没有积分,则显示该type
        if (list.length) {
          return list.some(e => !e.points)
        }
      }
      return true
    },

    handlerUserBtn() {
      if (!this.loginStatus) {
        //未登录状态,添加表单验证
        let isValid = true
        this.loginFormConfig.forEach(field => {
          if (!this.validateField(field.name)) {
            isValid = false
          }
        })

        if (!isValid) return

        //验证通过后继续原有的登录逻辑
        this.getUserInfo()
      } else {
        //关闭会员中心页面
        this.onClose()
      }
    },
    checkAccount(acc) {
      // console.log(acc)
      let requestData = {
        ...(this.resetRequestData || {}),
        cardNumber: acc.code.trim()
      }
      this.requestUserInfo(requestData)
    },
    getUserInfo() {
      let domain = sessionStorage.getItem("domain")
      let storeNumber = sessionStorage.getItem("storeNumber")
      let tableNumber = this.openTable.tableNumber
      let isEmail = this.formData.account.includes("@")
      let requestData = {
        password: this.formData.password,
        [isEmail ? "email" : "telephone"]: this.formData.account,
        domain,
        storeNumber,
        tableNumber
      }
      this.requestUserInfo(requestData)
    },
    /**
     * @description 实际请求登录的api函数
     * @param requestData
     * */
    loginMemberApi(requestData) {
      let data = JSON.stringify(requestData)
      let url = this.indexMark ? "./member/selectOneByTelephone" : "../member/selectOneByTelephone"
      return fetch(url, {
        body: data,
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })
        .then(r => r.json())
        .then(r => {
          if (r.statusCode === 200 || r.statusCode === 201) {
            //单个账号或者多个账号数据
            return r
          } else {
            throw r.statusCode
          }
        })
        .catch()
    },
    // 会员登录处理错误码
    handleUserErrorCode(code) {
      let { PCLoginReqError, PCLoginMemberTimeout, PCLoginMemberNotOpen, disableMemberTips } =
        this.systemLanguage
      switch (code) {
        case 400: // 参数错误
          // 自动登录且存在已登录字段
          if (this.autoLogin && this.LoginValid()) {
            // 则为cookie已过期;不提示400错误
          } else {
            this.setError()
          }
          break
        case 5001: // 登录状态过期
          layer.msg(PCLoginMemberTimeout)
          this.logoutMemberApi().catch()
          break
        case 5002:
          layer.msg(PCLoginMemberNotOpen)
          break
        case 5003:
          layer.msg(disableMemberTips)
          //this.logoutMemberApi().catch()
          break
        default:
          layer.msg(PCLoginReqError)
          break
      }
    },
    /**
     * @description 退出用户登录的api
     *
     * */
    logoutMemberApi() {
      let url = this.indexMark ? "./member/logout" : "../member/logout"
      return fetch(url, {
        method: "post"
      }).then(r => r.json())
    },
    requestUserInfo(requestData) {
      return this.loginMemberApi(requestData)
        .then(res => {
          this.resetRequestData = requestData //备用
          if (res.statusCode === 201) {
            //多个账号,用户自主选择账号
            this.accountArray = res.data
            this.showAccountList = true
          } else {
            // 单个账号,直接解析显示
            let {
              points_due,
              expdate,
              code,
              name,
              points0,
              tel = "",
              dob = "",
              storeNowDateTime = "",
              balance = "",
              email = "",
              coupon = 1
            } = res.data[0]
            this.userInfo = {
              points_due: points_due ? points_due.split(" ")[0] : "", // 积分到期日
              expdate: expdate ? expdate.split(" ")[0] : "", // 会员到期日
              code: code ? code.trim() : "", // 会员编号
              name: name || "", // 会员姓名
              points0: points0 ? points0 / 100 : "", // 积分(返回的数据除以100)
              telephone: tel, // 手机号
              balance: balance ? balance / 100 : "", // 余额(返回的数据除以100)
              email: email || "", // 邮箱
              coupon
            }
            this.showAccountList = false //关闭显示账号列表
            this.loginStatus = true
            this.checkBirthday(dob, storeNowDateTime)
            this.setInfoCookie(this.userInfo)
            this.checkPaymentMethod()
            return { storeNowDateTime, ...this.userInfo }
          }
        })
        .catch(err => {
          this.handleUserErrorCode(err)

          this.loginStatus = false
        })
        .finally(e => {
          this.autoLogin = false
        })
    },
    handUserLogout() {
      const { btnTxtForCancel, btnTxtForConfirm, PCLoginIsLogoutTip } = this.systemLanguage
      layer.confirm(
        PCLoginIsLogoutTip,
        {
          title: false,
          closeBtn: false,
          skin: "baseLayer payAtCashierLayer",
          area: ["68%"],
          btn: [btnTxtForCancel, btnTxtForConfirm]
        },
        (index, layero) => {
          //取消
          layer.close(index)
        },
        () => {
          this.logoutReset()
        }
      )
    },
    logoutReset() {
      return new Promise(resolve => {
        // 登出(注意:增加逻辑考虑充值页面是否兼容)
        this.logoutMemberApi()
          .catch(() => {
            // 处理登出API失败的情况,但仍继续执行后续清理
            console.warn("Logout API failed")
          })
          .finally(() => {
            // 无论API成功失败都执行清理
            this.loginStatus = false
            app.discountGroup = []
            app.taxesGroup = []

            // 退出登录后删除cookie
            Cookies.remove("memberInfo")

            // 退出执行其他逻辑
            this.loginOutCallBack()
            resolve()
          })
      })
    },
    // auto设置最新的user数据
    setUserInfo() {
      if (this.LoginValid()) {
        let domain = sessionStorage.getItem("domain")
        let storeNumber = sessionStorage.getItem("storeNumber")
        let tableNumber = this.openTable.tableNumber
        this.requestUserInfo({ domain, storeNumber, tableNumber })
        this.autoLogin = true
      } else {
        // showAccountList为true表示列表页展示过但没改变状态,重新打开为列表页
        if (!this.showAccountList) {
          this.loginStatus = false
        }
      }
    },
    setUserPopIndex(index) {
      this.personalCenterPopupIndex = index
    },
    setError() {
      let { PCLoginReqError } = this.systemLanguage
      layer.msg(PCLoginReqError)
    },
    onClose() {
      //关闭最外层layerIndex
      layer.close(this.personalCenterPopupIndex)
    },
    combinedImageUrl(url) {
      if (this.indexMark) {
        return `./${url}`
      }
      return `../${url}`
    },
    //判断是否在生日月份
    checkBirthday(dob, storeNowDateTime) {
      let { birthdayReminder = false } = this.openTable.displayCRM
      if (!dob || !storeNowDateTime || !birthdayReminder) return false
      // dob = "1999-12-08 12:12:13.0"
      // storeNowDateTime = "2021-12-08 12:12:13.0"
      let userBirthday = dob.split("-")[1]
      let currentMonth = storeNowDateTime.split("-")[1]
      // let userBirthday = dob.split("-")[1]
      // let currentMonth = userBirthday
      let showBirthdayPrompt = sessionStorage.getItem("showBirthdayPrompt") || false
      if (userBirthday == currentMonth && !showBirthdayPrompt && birthdayReminder) {
        let { birthdayConfirmBtn } = this.systemLanguage
        layer.open({
          title: false,
          closeBtn: 0,
          type: 1,
          skin: "birthdayCard", //加上边框
          shade: [0.1, "#fff"],
          area: ["80%"],
          btn: [birthdayConfirmBtn],
          content: $(".birthdayCard-warp"),
          success: (layero, index) => {
            sessionStorage.setItem("showBirthdayPrompt", true)
          },
          yes: (index, layero) => {
            layer.close(index)
            // console.log("点击确认")
          }
        })
      }
    },
    async checkPaymentMethod() {
      if (!this.indexMark) {
        let currentPayMethod = app.choosePayMethod
        if (currentPayMethod == "wallet") {
          await app.checkDisabledWallet()
          if (!app.isDisabledWallet) {
            app.sendOrderForm.phone = this.userInfo.telephone
          }
        }
      }
    },
    setInfoCookie(data) {
      Cookies.set("memberInfo", JSON.stringify(data), { expires: 100 })
    },
    loginOutCallBack() {
      if (app.choosePayMethod == "wallet" && app.sendOrderForm.phone) {
        app.sendOrderForm.phone = ""
      }
      app.isDisabledWallet = false
    },

    handleRegister() {
      this.goToRegister()
    },
    forgotPassword() {
      this.goToResetPassword()
    },

    // 软键盘弹起时，滚动到底部
    handleKeyboardShow() {
      // const isAndroid = /Android/i.test(navigator.userAgent)
      // let isPCmodel = $("body").hasClass("pc")
      // if (isAndroid && !isPCmodel) {
      //   console.log("监听")
      //   this.resizeHandler = () => {
      //     // 获取实际可见视口高度
      //     const viewportHeight = window.visualViewport.height
      //     // 获取原始视口高度
      //     const windowHeight = window.innerHeight
      //     // 计算键盘高度 = 原始视口高度 - 当前可见视口高度
      //     const keyboardHeight = windowHeight - viewportHeight
      //     console.log("🚀 ~ handleKeyboardShow ~ keyboardHeight:", keyboardHeight)
      //     if (keyboardHeight > 0) {
      //       console.log("键盘弹出，高度：", keyboardHeight)
      //       // footer.style.bottom = `${keyboardHeight}px`
      //     } else {
      //       console.log("键盘收起")
      //       footer.style.bottom = "0"
      //     }
      //     // document.activeElement.scrollIntoView(true)
      //   }
      //   window.addEventListener("resize", this.resizeHandler)
      // }
    },
    removeEventListener() {
      // if (this.resizeHandler) {
      //   console.log("移除")
      //   window.removeEventListener("resize", this.resizeHandler)
      // }
    },
    resetLoginForm() {
      this.formData = {
        account: "",
        password: ""
      }
      this.formErrors = {}
    },
    validateField(fieldName) {
      const field = this.loginFormConfig.find(f => f.name === fieldName)
      if (!field) return

      const value = this.formData[fieldName]

      // 判断字段是否必填
      const isRequired =
        typeof field.required === "function" ? field.required.call(this) : field.required

      // 如果段不是必填且值为空，则清除错误信息并返回true
      if (!isRequired && !value) {
        this.$set(this.formErrors, fieldName, "")
        return true
      }

      // 如果是必填但值为空
      if (isRequired && !value) {
        if (fieldName === "confirmPassword") {
          this.$set(this.formErrors, fieldName, this.systemLanguage.formPasswordMismatch)
        } else if (fieldName.endsWith("VerifyCode")) {
          this.$set(this.formErrors, fieldName, this.systemLanguage.formReqVerifyCode)
        } else {
          this.$set(
            this.formErrors,
            fieldName,
            this.systemLanguage[`formReq${this.capitalize(fieldName)}`]
          )
        }
        // console.log(this.formErrors, fieldName)

        return false
      }

      // 如果有值，则进行验证
      if (field.validator && value) {
        const result = field.validator.call(this, value)
        if (result !== true) {
          this.$set(this.formErrors, fieldName, result)
          return false
        }
      }

      this.$set(this.formErrors, fieldName, "")
      return true
    },
    formCellClass() {}
  }
})
