@import url(./rechargePaySuccess.css); /* 充值成功样式 */

* {
  margin: 0;
  padding: 0;
  /* -webkit-overflow-scrolling: touch; */
  font-family: Arial, Helvetica, sans-serif;
}
:root {
  --styleColor: #ed6211;
}
[v-cloak] {
  display: none;
}

.successPayBox {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  font-size: 0.35rem;
  /* background-image: linear-gradient(120deg, #fdfbfb 0%, #ebedee 100%); */
  padding: 0 0.3rem;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.successPayBox-header {
  padding: 0.2rem 0;
}
.successPayBox-header-title {
  font-size: 0.45rem;
  text-align: center;
  font-weight: 600;
}
.successPayBox-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* margin-top: 1rem; */
  width: 100%;
}
.successPayBox-center-img {
  margin-top: 0.1rem;
  width: 4.5rem;
  filter: drop-shadow(0 0.1rem 0.2rem rgba(0, 0, 0, 0.1));
  animation: floatAnimation 3s ease-in-out infinite;
}
/* 媒体查询小屏幕 */
.successPayBox-center-successTip {
  text-align: center;
  padding-top: 0.5rem;
  font-size: 0.7rem;
  color: #2c3e50;
}

.successPayBox-center-orderNumberBox {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  width: 90%;
  height: 1.3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0.5rem 0;
  border-radius: 0.3rem;
}
.successPayBox-center-orderNumberBox-title {
  font-size: 0.38rem;
  color: #777777;
  margin-right: 0.5rem;
}
.successPayBox-center-orderNumberBox-num {
  min-width: 2rem;
  min-height: 0.8rem;
  display: flex;
  align-items: center;
  font-size: 0.7rem;
  color: var(--styleColor);
  position: relative;
}
.successPayBox-center-waitTip {
  /* 超出三行自动省略 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 95%;
  text-align: center;
  /* max-height: 2.5rem; */
}

.successPayBox-boundary {
  /* margin: 1rem 0; */
  border-top: 1px solid #dedede;
}
.successPayBox-bottom {
  width: 100%;
  display: flex;
}
.successPayBox-bottom ul {
  width: 100%;
}
.successPayBox-bottom-cell-fixedTitle {
  display: flex;
}
.successPayBox-bottom ul li {
  font-size: 0.36rem;
  margin-bottom: 0.15rem;
}
.successPayBox-bottom ul li p:first-child {
  color: #7f8c8d;
  flex-shrink: 0;
  white-space: nowrap;
  margin-right: 0.4rem;
}
.successPayBox-bottom ul li p:last-child {
  color: #2c3e50;
  flex: 1;
  text-align: left;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.successPayBox-bottom-cell-param {
  margin-left: 0.2rem;
  min-width: 6rem;
}

.successPayBox-bottom-btn-box {
  width: 100%;
  text-align: center;
  /* margin-top: 2rem; */
}
.successPayBox-bottom-btn {
  width: 85%;
  padding: 0.25rem 0;
  border: 0.1px solid #409eff;
  color: #fff;
  font-size: 0.39rem;
  outline: none !important;
  font-weight: 500;
  border-radius: 0.15rem;
  background-color: #409eff;
}

/* 骨架屏动画 */
.is-loading {
  /* 宽度和高度根据要展示元素大小设定 */
  position: relative;
  /* 以下代码实现背景动画效果 */
  animation: loading 1.4s ease infinite;
  -webkit-animation: loading 1.4s ease infinite;
  background-image: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(25%, #f0f0f0),
    color-stop(37%, #e3e3e3),
    color-stop(63%, #f0f0f0)
  );
  background-image: -o-linear-gradient(left, #f0f0f0 25%, #e3e3e3 37%, #f0f0f0 63%);
  background-image: linear-gradient(90deg, #f0f0f0 25%, #e3e3e3 37%, #f0f0f0 63%);
  background-size: 400% 100%;
}

@-webkit-keyframes loading {
  0% {
    background-position: 100% 50%;
  }

  to {
    background-position: 0 50%;
  }
}

@keyframes loading {
  0% {
    background-position: 100% 50%;
  }

  to {
    background-position: 0 50%;
  }
}
.billNumber-prefix {
  color: #909399;
  font-size: 0.35rem;
  margin-right: 0.2rem;
}
@media screen and (min-width: 320px) and (max-width: 375px) {
  .successPayBox-center-img {
    width: 4rem;
  }
  .successPayBox-center-successTip {
    text-align: center;
    padding-top: 0.1rem;
    font-size: 0.65rem;
    /* 文字第三行省略 */
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90vw;
  }

  .successPayBox-center-orderNumberBox {
    margin: 0.2rem 0;
  }
  .successPayBox-center-waitTip {
    max-height: 2rem; /*-webkit-line-clamp不兼容ios溢出省略,直接截断 */
  }
}
.successPayBox-bottom.tinyDevice ul li {
  margin-bottom: 0.1rem !important;
}

.changelan,
.changeMode {
  position: fixed;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 5;
}
.changeMode {
  top: 1rem;
}

.sessionLessWarp {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  background-color: #f6f8ff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sessionLessWarp .header {
  margin-top: 2.5rem;
}
.sessionLessWarp .main {
  padding: 0rem 0.5rem;
  color: #1e243a;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.4rem;
  text-align: center;
}
.sessionLessWarp .main p {
  margin-bottom: 0.3rem;
}
.sessionLessWarp .sessionLessIcon {
  width: 4rem;
}
.sessionLessWarp .bottom-btn {
  text-align: center;
  width: 3rem;
  border-radius: 0.1rem;
  padding: 0.3rem;
  font-size: 0.37rem;
  margin-bottom: 2rem;
  background-color: #2e78d4;
  color: #fff;
}
.points-wallet-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.pointsWarp,
.walletWarp {
  flex: 1;
  position: relative;
}

.pointsWarp {
  padding-right: 0.5rem;
}

.pointsWarp,
.walletWarp,
.walletWarp li.successPayBox-bottom-cell-fixedTitle p:first-child,
.pointsWarp li.successPayBox-bottom-cell-fixedTitle p:first-child {
  /* 自动换行 */
  white-space: normal;
  color: var(--styleColor) !important;
}

.balanceWarp li.successPayBox-bottom-cell-fixedTitle p:first-child {
  margin-right: 0.3rem !important;
}

.pointsWarp-divider:after {
  content: "";
  position: absolute;
  right: 0.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 80%; /* 可以调整竖线高度 */
  background-color: #e0e0e0;
}
@keyframes floatAnimation {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-0.2rem);
  }
}
