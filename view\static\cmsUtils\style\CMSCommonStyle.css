/* .modal-dialog,
.modal-content,
.modal-body,
.modal-body-warp {
  height: 100%;
} */

/* @media (min-width: 768px) {
}
@media (min-width: 992px) {
  .modal-xl {
    max-width: 900px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1200px;
  }
} */
.modal-xl {
  max-width: 80vw;
}
.modal-xl .modal-body {
  max-height: 65vh;
}
/* .resetModalStyle {
  left: 150px;
} */

/* el-dialog */
.el-dialog {
  width: 70%;
  margin-top: 8vh !important;
}

.el-dialog .el-dialog__body {
  min-height: 30vh;
  max-height: 60vh;
}

.v-dialog {
  width: 70%;
}
@media (max-width: 768px) {
  .el-dialog {
    width: 85%;
  }
  .modal-xl {
    margin: 1.75rem auto; /* bootstrap移动端未生效居中样式 */
  }
}
