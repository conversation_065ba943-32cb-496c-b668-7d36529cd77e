.rechargeBox-success-page {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  font-size: 0.35rem;
  background: linear-gradient(135deg, #f6f8fb 0%, #f5f7fa 100%);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}
.rechargeBox-content {
  flex: 1;
  overflow-y: auto;
}
.rechargeBox-success-header {
  text-align: center;
  padding: 0.6rem 0.4rem;
  background: #fff;
  position: relative;
  overflow: hidden;
}
.rechargeBox-success-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0.4rem;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(246, 248, 251, 0.8) 100%);
}
.rechargeBox-success-icon {
  width: 4.5rem;
  margin-bottom: 0.35rem;
  filter: drop-shadow(0 0.1rem 0.2rem rgba(0, 0, 0, 0.1));
  animation: floatAnimation 3s ease-in-out infinite;
}
.rechargeBox-success-title {
  font-size: 0.55rem;
  color: #2c3e50;
  margin-bottom: 0.3rem;
  font-weight: 600;
}
.rechargeBox-success-amount {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 0.02rem 0.04rem rgba(0, 0, 0, 0.1);
}
.rechargeBox-amount-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
.rechargeBox-currency-symbol {
  font-size: 0.9rem;
  margin-right: 0.15rem;
  font-weight: normal;
  color: #2c3e50;
  opacity: 0.8;
}
.rechargeBox-success-tip {
  font-size: 0.35rem;
  color: #7f8c8d;
  line-height: 1.5;
  margin-top: 0.1rem;
}
.rechargeBox-info-container {
  margin: 0.15rem 0.3rem;
  padding: 0.15rem 0.3rem;
  background: #fff;
  border-radius: 0.2rem;
  box-shadow: 0 0.05rem 0.2rem rgba(0, 0, 0, 0.03);
}
.rechargeBox-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  position: relative;
  font-size: 0.35rem;
}
.rechargeBox-info-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0.01rem;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.03) 0%,
    rgba(0, 0, 0, 0.06) 50%,
    rgba(0, 0, 0, 0.03) 100%
  );
}
.rechargeBox-info-item:last-child::after {
  display: none;
}
.rechargeBox-info-label {
  color: #7f8c8d;
  font-size: 0.32rem;
  white-space: nowrap;
  flex: 0 0 50%;
}
.rechargeBox-info-value {
  color: #2c3e50;
  font-size: 0.31rem;
  display: flex;
  align-items: center;
  font-weight: 500;
}
.rechargeBox-loading {
  background: linear-gradient(90deg, #f6f8fb 25%, #f5f7fa 37%, #f6f8fb 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}
.rechargeBox-text-loading {
  background: linear-gradient(90deg, #f6f8fb 25%, #f5f7fa 37%, #f6f8fb 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  height: 0.35rem;
  width: 2rem;
  border-radius: 0.1rem;
}
.rechargeBox-amount-loading {
  height: 1.4rem;
  width: 3rem;
  border-radius: 0.2rem;
}
.rechargeBox-footer {
  padding: 0.3rem 0.6rem 0.4rem;
}
.rechargeBox-home-btn {
  display: block;
  height: 1.1rem;
  line-height: 1.1rem;
  text-align: center;
  background: #409eff;
  color: #fff;
  font-size: 0.39rem;
  border-radius: 0.15rem;
  border: 0.02rem solid #409eff;
  transition: all 0.3s ease;
}
.rechargeBox-home-btn:active {
  background: #f8fafc;
  transform: scale(0.98);
}
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/* 新增两列布局相关样式 */
.rechargeBox-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.2rem 0;
  position: relative;
}
.rechargeBox-info-row:not(:last-child)::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0.01rem;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.03) 0%,
    rgba(0, 0, 0, 0.06) 50%,
    rgba(0, 0, 0, 0.03) 100%
  );
}
.rechargeBox-info-col {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.rechargeBox-info-divider {
  width: 0.01rem;
  height: 0.4rem;
  background: rgba(0, 0, 0, 0.06);
  margin: 0 0.2rem;
}
/* 错误状态样式 */
.rechargeBox-error-status {
  color: #ff4d4f;
}
.rechargeBox-success-status {
  color: #52c41a;
}
/* 新增数据变化样式 */
.rechargeBox-points-change {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex: 0 0 50%;
}
.rechargeBox-original-value {
  font-size: 0.26rem;
  color: #999;
  margin-bottom: 0.05rem;
}
.rechargeBox-original-value-text {
  text-decoration: line-through;
}

.rechargeBox-points-increase {
  color: #52c41a;
  font-size: 0.26rem;
  text-decoration: none;
}
/* 添加浮动动画 */
@keyframes floatAnimation {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-0.2rem);
  }
  100% {
    transform: translateY(0);
  }
}
/* 骨架屏相关样式 */
.rechargeBox-skeleton-row {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.1rem;
}
.rechargeBox-text-loading {
  background: linear-gradient(90deg, #f6f8fb 25%, #f5f7fa 37%, #f6f8fb 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  height: 0.35rem;
  border-radius: 0.1rem;
}
.rechargeBox-small-loading {
  width: 1.5rem;
  height: 0.3rem;
  opacity: 0.6;
}
.rechargeBox-large-loading {
  width: 2.2rem;
  height: 0.35rem;
}
/* 调整info-row在loading状态下的样式 */
.rechargeBox-info-row .rechargeBox-info-label {
  color: #7f8c8d;
  opacity: 0.8;
}

@media screen and (min-width: 320px) and (max-width: 375px) {
  .rechargeBox-success-header {
    padding: 0.4rem 0.3rem;
  }
  .rechargeBox-success-icon {
    width: 4rem;
    margin-bottom: 0.3rem;
  }
  .rechargeBox-success-title {
    font-size: 0.45rem;
    margin-bottom: 0.2rem;
  }
  .rechargeBox-success-tip {
    font-size: 0.32rem;
  }
  .rechargeBox-footer {
    padding: 0.4rem 0.5rem 0.3rem;
  }
  .rechargeBox-info-item {
    padding: 0.15rem 0;
    font-size: 0.33rem;
  }
  .rechargeBox-success-amount {
    margin-bottom: 0.25rem;
    font-size: 1rem;
  }
  .rechargeBox-currency-symbol {
    font-size: 0.8rem;
  }
}
