<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <link rel="stylesheet" href="../../static/css/category.css" />
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :header-cell-style="{fontSize: '15px'}"
          :max-height="tableHeight"
          empty-text="No Data"
        >
          <el-table-column
            prop="type_name"
            label="Group"
            align="center"
            width="100"
          ></el-table-column>
          <el-table-column prop="sign" label="Types Food" align="center">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.foodTypeList"
                :key="item"
                class="typeTag_warp"
                type="success"
                style="margin: 0px 5px"
              >
                {{ item.fType_nameB || item.name2}}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="store_number" label="Store Number" align="center">
            <template slot-scope="scope">
              <el-tag>{{scope.row.store_number}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="restrictions2" label="Table Number" align="center">
            <template slot-scope="scope">
              <!-- <el-tag
                                v-for="item in scope.row.restrictions2"
                                :key="item"
                                class="ramTag_warp"
                                >{{item}}
                              </el-tag> -->
              <el-tag>{{scope.row.restrictions2}}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="sign" label="Switch" align="center" width="180">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.sign"
                active-value="true"
                inactive-value="false"
                @change="onEditSwitch($event, scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column prop label="Operation" align="center">
            <el-table-column width="100" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible = true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="onDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 類別Add弹窗 -->
      <template>
        <el-dialog title="Add Dialog" :visible.sync="addDialogVisible" @close="addCloseDialog">
          <el-form :model="addForm" ref="addForm" label-width="auto" :rules="rules">
            <el-form-item label="Group" prop="type_name">
              <el-input v-model="addForm.type_name" placeholder="Please enter category"></el-input>
            </el-form-item>
            <!-- <el-form-item label="Types Food">
                              <el-input
                                v-model="addForm.sign"
                                placeholder="请输入TypesFood"
                              ></el-input>
                            </el-form-item> -->

            <el-form-item label="Table Number" prop="restrictions2">
              <el-input
                v-model="addForm.restrictions2"
                placeholder="Input station number (use ; to separate multiple number)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Store Number" prop="store_number">
              <el-input
                v-model="addForm.store_number"
                placeholder="Please enter the store number"
              ></el-input>
            </el-form-item>
            <el-form-item label="Switch">
              <el-switch
                v-model="addForm.sign"
                active-value="true"
                inactive-value="false"
              ></el-switch>
            </el-form-item>
            <div class="dialog_footer">
              <el-button type="primary" @click="addDialogVisible=false">Cancel</el-button>
              <el-button style="margin-right: 8px" @click="onAdd('addForm')">Add</el-button>
            </div>
          </el-form>
        </el-dialog>
      </template>
      <!-- 单元格编辑弹窗 -->
      <template>
        <el-dialog title="Edit Dialog" :visible.sync="editDialogVisible">
          <el-form :model="editForm" ref="editForm" label-width="auto">
            <!-- <el-checkbox-type v-model="editForm.food">
                                  <el-checkbox
                                    v-for="(item,index) in editForm.sign"
                                    :label="item"
                                    :key="index"
                                    @change="onChange"
                                  ></el-checkbox>
                                </el-checkbox-type> -->
            <el-form-item label="Group">
              <el-input v-model="editForm.type_name" placeholder="Please enter category"></el-input>
            </el-form-item>
            <!-- <el-form-item label="Types Food">
                          <el-input
                            v-model="editForm.sign"
                            placeholder="请输入TypesFood"
                          ></el-input>
                        </el-form-item> -->
            <el-form-item label="Table Number">
              <el-input
                v-model="editForm.restrictions2"
                placeholder="Input station number (use ; to separate multiple number)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Store Number">
              <el-input
                v-model="editForm.store_number"
                placeholder="Please enter the store number"
              ></el-input>
            </el-form-item>
            <el-form-item label="Switch">
              <el-switch
                v-model="editForm.sign"
                active-value="true"
                inactive-value="false"
              ></el-switch>
            </el-form-item>
            <div class="dialog_footer">
              <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
              <el-button style="margin-right: 8px" @click="subEdit">Submit</el-button>
            </div>
          </el-form>
        </el-dialog>
      </template>
      <!-- 单元格删除弹窗 -->
      <!-- <template>
  <el-dialog title="刪除" :visible.sync="delDialogVisible" width="35%">
    <el-form :model="delsubForm" ref="delsubForm" label-width="auto">
      <el-form-item label="組別">
        <el-input v-model="delForm.type_name" disabled></el-input>
      </el-form-item>
      <el-form-item label="Types Food">
        <el-checkbox-type v-model="delsubForm.sign">
          <el-checkbox
            v-for="(item,index) in delForm.sign"
            :label="item"
            :key="index"
            @change="deltfChange"
          ></el-checkbox>
        </el-checkbox-type>
      </el-form-item>
      <el-form-item label="臺號">
        <el-checkbox-type
          v-model="delsubForm.restrictions2"
          class="check_warp"
        >
          <el-checkbox
            v-for="(item,index) in delForm.restrictions2"
            :label="item"
            :key="index"
            @change="delraChange"
          ></el-checkbox>
        </el-checkbox-type>
      </el-form-item>
      <div class="dialog_footer">
        <el-button type="primary" @click="delDialogVisible=false"
          >Cancel
        </el-button>
        <el-button style="margin-right: 8px;">刪 除</el-button>
      </div>
    </el-form>
  </el-dialog>
</template> -->
    </div>
    <script>
      var app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          value: true,
          addDialogVisible: false,
          delDialogVisible: false,
          editDialogVisible: false,
          rules: {
            type_name: [
              {
                required: true,
                message: "Please enter category",
                trigger: "blur"
              }
            ],
            restrictions2: [
              {
                required: true,
                message: "Please enter the station number",
                trigger: "blur"
              }
            ],
            store_number: [
              {
                required: true,
                message: "Please enter the store number",
                trigger: "blur"
              }
            ]
          },
          addForm: {
            type_name: "",
            sign: "false",
            restrictions1: "",
            restrictions2: "",
            store_number: ""
          },
          editForm: {
            type_name: "",
            sign: "",
            restrictions2: "",
            store_number: ""
          },
          delForm: {
            type_name: "",
            sign: "",
            restrictions2: "",
            store_number: ""
          },
          delsubForm: {
            sign: "",
            restrictions2: "",
            store_number: ""
          },
          tableData: []
        },
        created() {
          this.getData()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整
        },
        methods: {
          getData() {
            let that = this
            $.get({
              url: "../../manager_typeRestrict/getRestrict",
              success: function (res) {
                if (res.code == 200) {
                  console.log(res)
                  that.tableData = res.list
                } else {
                  that.$message.error("Fail to load！")
                }
              },
              error: function () {
                // console.log(res);
                that.$message.error("Fail to load！")
              }
            })
          },
          onAdd(addForm) {
            let data = this.addForm
            let that = this
            console.log(data)
            this.$refs[addForm].validate(valid => {
              if (valid) {
                $.post({
                  url: "../../manager_typeRestrict/saveRestrict",
                  data: { data: JSON.stringify(data) },
                  dataType: "json",
                  success: function (res) {
                    console.log(res)
                    if (res.code == 200) {
                      that.getData()
                      // console.log(res);
                      that.$message.success("Successfully added！")
                      that.addDialogVisible = false
                    } else {
                      that.$message.error("Fail to add！")
                    }
                  },
                  error: function (res) {
                    // console.log(res);
                    that.$message.error("Fail to add！")
                  }
                })
              } else {
                that.$message.error("Fail to add！")
              }
            })
          },
          onEdit(index, row) {
            this.editDialogVisible = true
            let { type_name, sign, restrictions2, store_number } = row
            this.editForm = {
              code: row.code,
              type_name: row.type_name,
              sign: row.sign,
              restrictions2: row.restrictions2,
              store_number: row.store_number
            }
          },
          subEdit() {
            let data = this.editForm
            let that = this
            $.post({
              url: "../../manager_typeRestrict/updateRestrict",
              data: { data: JSON.stringify(data) },
              dataType: "json",
              success: function (res) {
                that.getData()
                // console.log(res);
                that.$message.success("Edit success！")
                that.editDialogVisible = false
              },
              error: function (res) {
                // console.log(res);
                that.$message.error("Edit failure！")
              }
            })
          },
          onDel(index, row) {
            // this.delDialogVisible = true;
            // let { type_name, sign, restrictions2 } = row;
            // this.delForm = {
            //   type_name,
            //   sign,
            //   restrictions2,
            // };
            let that = this
            let { type_name, sign, restrictions2, store_number } = row
            this.delForm = {
              code: row.code,
              type_name: row.type_name,
              sign: row.sign,
              restrictions2: row.restrictions2,
              store_number: row.store_number
            }
            let data = this.delForm
            $.post({
              url: "../../manager_typeRestrict/deleteRestrict",
              data: { data: JSON.stringify(data) },
              dataType: "json",
              success: function (res) {
                that.getData()
                that.$message.success("Successfully delete！")
                // console.log(res);
              },
              error: function (res) {
                // console.log(res);
                that.$message.error("Fail to delete！")
              }
            })
          },
          deltfChange(e) {
            console.log(this.delsubForm.sign)
          },
          delraChange(e) {
            console.log(this.delsubForm.restrictions2)
          },
          // 开关触发
          onEditSwitch($event, row) {
            let { type_name, sign, restrictions2, store_number } = row
            this.editForm = {
              code: row.code,
              type_name: row.type_name,
              sign: row.sign,
              restrictions2: row.restrictions2,
              store_number: row.store_number
            }

            let data = this.editForm
            let that = this
            $.post({
              url: "../../manager_typeRestrict/updateRestrict",
              data: { data: JSON.stringify(data) },
              dataType: "json",
              success: function (res) {
                that.getData()
                // console.log(res);
                that.$message.success("Edit success！")
                that.editDialogVisible = false
              },
              error: function (res) {
                // console.log(res);
                that.$message.error("Edit failure！")
              }
            })
          },
          // 上传对话框关闭事件
          addCloseDialog() {
            // 点击关闭 数据重置
            // this.$refs['addForm'].resetFields();
            // this.$refs.upload.clearFiles();
            this.addForm = {
              type_name: "",
              sign: "false",
              restrictions1: "",
              restrictions2: "",
              store_number: ""
            }
          }
        }
      })
    </script>
  </body>
</html>
