<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>locales</title>
    <link rel="stylesheet" href="../../static/vuetify/vuetify.min.css" />
    <link href="../../static/vendor/materialdesignicons.css" rel="stylesheet" />
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      html {
        overflow: auto;
      }
      @media screen and (min-width: 600px) {
        html {
          overflow: hidden;
          scroll-behavior: smooth;
        }
      }
      .v-data-table-header:not(.v-data-table-header-mobile) {
        height: 60px;
      }
      .float-btn {
        position: fixed;
        bottom: 100px;
        right: 30px;
      }
      .z-1000 {
        z-index: 1000;
      }
      tr:has(.add-flag) {
        background-color: rgb(24, 121, 207, 0.1);
      }
    </style>
  </head>
  <body>
    <script src="../../static/vue/vue.min.js"></script>
    <script src="../../static/vuetify/vuetify.min.js"></script>
    <script src="../../static/vuetify/tribute.min.js"></script>
    <script src="../../static/I18n/diffI18n.workers.js"></script>
    <v-app id="app">
      <v-app-bar app flat dense height="10" ref="app-header" v-resize="onResize">
        <template v-slot:extension>
          <v-tabs
            align-with-title
            v-model="activeTab"
            icons-and-text
            @change="changeTab"
            :show-arrows="false"
            color="primary"
          >
            <v-tab
              v-for="item in tabs"
              :key="item.value"
              :href="'#' + item.value"
              class="text-none"
              style="flex-direction: row"
            >
              <v-icon left dense class="mb-0">{{item.icon}}</v-icon>
              {{ item.label }}
            </v-tab>
          </v-tabs>
        </template>
      </v-app-bar>

      <v-main>
        <v-snackbar app v-model="snackbar.show" timeout="2000" :color="snackbar.color" text top>
          {{ snackbar.text }}
          <template v-slot:action="{ attrs }">
            <v-btn
              :color="snackbar.color"
              text
              v-bind="attrs"
              class="text-none"
              @click="snackbar.show = false"
            >
              Close
            </v-btn>
          </template>
        </v-snackbar>
        <v-dialog v-model="showUploadModal" :persistent="btnLoading">
          <v-card>
            <v-card-title class="text-h6">Upload I18n History Files</v-card-title>
            <v-subheader inset>
              <v-icon left color="primary">mdi-information</v-icon>
              Three files must be selected before uploading !
            </v-subheader>
            <v-card-text>
              <v-container>
                <v-row justify="start">
                  <v-col cols="4">
                    <v-sheet class="pa-2 ma-2 d-flex justify-space-around align-center">
                      En:
                      <v-avatar :color="fileSelectionStatus.en?'success':'error'" size="30">
                        <v-icon color="white" small>
                          {{fileSelectionStatus.en?'mdi-check':'mdi-window-close'}}
                        </v-icon>
                      </v-avatar>
                    </v-sheet>
                  </v-col>
                  <v-col cols="4">
                    <v-sheet class="pa-2 ma-2 d-flex justify-space-around align-center">
                      Zh:
                      <v-avatar :color="fileSelectionStatus.zh?'success':'error'" size="30">
                        <v-icon color="white" small>
                          {{fileSelectionStatus.zh?'mdi-check':'mdi-window-close'}}
                        </v-icon>
                      </v-avatar>
                    </v-sheet>
                  </v-col>
                  <v-col cols="4">
                    <v-sheet class="pa-2 ma-2 d-flex justify-space-around align-center">
                      Multiple:
                      <v-avatar :color="fileSelectionStatus.thirdLan?'success':'error'" size="30">
                        <v-icon color="white" small>
                          {{fileSelectionStatus.thirdLan?'mdi-check':'mdi-window-close'}}
                        </v-icon>
                      </v-avatar>
                    </v-sheet>
                  </v-col>
                </v-row>
                <template v-if="showOpenFilePicker">
                  <v-btn
                    :disabled="allowSubmit"
                    class="text-none"
                    color="primary"
                    @click="openFileSelector"
                  >
                    Select Files
                  </v-btn>
                  <v-list shaped dense>
                    <v-subheader>Selected Files</v-subheader>
                    <v-list-item-group color="primary" ripple>
                      <template v-for="item in uploadFiles" :key="item.name">
                        <v-hover v-slot="{ hover }">
                          <v-list-item>
                            <v-list-item-avatar>
                              <v-icon color="primary">mdi-file</v-icon>
                            </v-list-item-avatar>
                            <v-list-item-content>
                              <v-list-item-title v-text="item.name"></v-list-item-title>
                            </v-list-item-content>
                            <v-fade-transition>
                              <v-list-item-action v-show="hover">
                                <v-btn dense text ripple x-small fab @click="removeFile(item)">
                                  <v-icon color="red">mdi-close</v-icon>
                                </v-btn>
                              </v-list-item-action>
                            </v-fade-transition>
                          </v-list-item>
                        </v-hover>
                      </template>
                    </v-list-item-group>
                  </v-list>
                </template>

                <v-file-input
                  v-else
                  v-model="uploadFiles"
                  accept=".js"
                  color="primary"
                  counter
                  label="Select Files(multiple)"
                  multiple
                  placeholder="Select your files"
                  prepend-icon="mdi-paperclip"
                  outlined
                  dense
                  :show-size="1000"
                  @change="updateFileList"
                >
                  <template v-slot:selection="{ index, text }">
                    <v-chip v-if="index < 2" color="deep-purple accent-4" dark label small>
                      {{ text }}
                    </v-chip>

                    <span
                      v-else-if="index === 2"
                      class="text-overline grey--text text--darken-3 mx-2"
                    >
                      + {{uploadFiles.length -2 }} File(s)
                    </span>
                  </template>
                </v-file-input>
              </v-container>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="warning" text class="text-none" @click="closeUploadModal">Cancel</v-btn>
              <v-btn
                class="text-none"
                color="success"
                text
                @click="onPositiveClick"
                :loading="btnLoading"
              >
                Confirm
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
        <v-dialog v-model="dialog" :persistent="btnLoading" scrollable>
          <v-card>
            <v-card-title>{{formTitle}}</v-card-title>
            <v-card-text style="max-height: 500px">
              <v-container>
                <v-form ref="form" v-model="valid" lazy-validation>
                  <v-row>
                    <v-col cols="2" class="pb-0" v-if="breakpoint!=='xs'">
                      <v-subheader>Label</v-subheader>
                    </v-col>
                    <v-col :cols="breakpoint!=='xs'? 10 : 12" class="pb-0 pl-0">
                      <v-text-field
                        v-model.trim="editedItem.label"
                        dense
                        outlined
                        label="Label"
                        :rules="rules.required"
                        required
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-fade-transition>
                    <v-row v-if="editedItem.slots.length" class="mt-0">
                      <v-col cols="2" class="py-0" v-if="breakpoint!=='xs'">
                        <v-subheader>
                          Slot
                          <v-tooltip top color="primary">
                            <template v-slot:activator="{ on, attrs }">
                              <v-icon v-bind="attrs" v-on="on" small right color="primary">
                                mdi-alert-circle
                              </v-icon>
                            </template>
                            <span>start with #</span>
                          </v-tooltip>
                        </v-subheader>
                      </v-col>
                      <v-col :cols="breakpoint!=='xs'? 10 : 12" class="py-0 pl-0">
                        <v-chip-group column>
                          <v-chip
                            @click="addSlotToValue(tag)"
                            color="primary"
                            v-for="tag in editedItem.slots"
                            :key="tag"
                          >
                            #{{ tag }}
                          </v-chip>
                        </v-chip-group>
                      </v-col>
                    </v-row>
                  </v-fade-transition>

                  <v-row>
                    <v-col cols="2" class="pb-0" v-if="breakpoint!=='xs'">
                      <v-subheader>English</v-subheader>
                    </v-col>
                    <v-col :cols="breakpoint!=='xs'? 10 : 12" class="pb-0 pl-0">
                      <v-textarea
                        label="English"
                        outlined
                        dense
                        name="en"
                        class="mention"
                        rows="2"
                        v-model.trim="editedItem.en"
                        :rules="rules.required"
                        required
                        @blur="blurField='en'"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="2" class="py-0" v-if="breakpoint!=='xs'">
                      <v-subheader>繁体中文</v-subheader>
                    </v-col>
                    <v-col :cols="breakpoint!=='xs'? 10 : 12" class="py-0 pl-0">
                      <v-textarea
                        label="繁体中文"
                        outlined
                        dense
                        name="zh"
                        class="mention"
                        rows="2"
                        v-model.trim="editedItem.zh"
                        :rules="rules.required"
                        required
                        @blur="blurField='zh'"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="2" class="py-0" v-if="breakpoint!=='xs'">
                      <v-subheader>Multiple</v-subheader>
                    </v-col>
                    <v-col :cols="breakpoint!=='xs'? 10 : 12" class="py-0 pl-0">
                      <v-textarea
                        label="Multiple"
                        outlined
                        dense
                        name="multiple"
                        class="mention"
                        rows="2"
                        v-model.trim="editedItem.thirdLan"
                        :rules="rules.required"
                        required
                        @blur="blurField='thirdLan'"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-container>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="blue darken-1" text @click="dialog = false" :disabled="btnLoading">
                Close
              </v-btn>
              <v-btn color="blue darken-1" text @click="onSave" :loading="btnLoading">Save</v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
        <v-card-title class="text-h6 py-2 align-baseline">
          <v-btn color="primary" small @click.stop="showUploadModal=true">
            <v-icon left>mdi-upload</v-icon>
            upload
          </v-btn>
          <v-spacer></v-spacer>
          <v-text-field
            v-model="search"
            append-icon="mdi-magnify"
            label="Search"
            single-line
            hide-details
          ></v-text-field>
        </v-card-title>
        <v-data-table
          :headers="headers"
          :items="tableData"
          item-key="key"
          hide-default-footer
          :items-per-page.async="tableData.length"
          fixed-header
          :height="tableHeight"
          :search="search"
          :loading="loading"
          loading-text="Loading... Please wait"
        >
          <template #item.type="{item}">
            <div class="d-flex align-center">
              <v-icon v-if="item.add" size="30" color="success" left class="add-flag">
                mdi-new-box
              </v-icon>
              {{item.type}}
            </div>
          </template>
          <template #item.actions="{ item }">
            <v-btn text fab dark x-small icon @click="editItem(item)" class="mr-2">
              <v-icon color="primary">mdi-pencil</v-icon>
            </v-btn>
            <v-btn
              text
              fab
              dark
              x-small
              icon
              :disabled="item.add"
              @click="deleteItem(item)"
              class="mr-2"
            >
              <v-icon color="error" v-if="item.label" title="delete restore">
                mdi-delete-restore
              </v-icon>
              <v-icon color="error" v-else title="delete forever">mdi-delete-forever</v-icon>
            </v-btn>
          </template>
          <template #no-data>
            <v-btn color="primary" @click="initialize">Reacquire</v-btn>
          </template>
        </v-data-table>
      </v-main>
    </v-app>

    <script>
      const app = new Vue({
        el: "#app",
        vuetify: new Vuetify(),
        data() {
          return {
            domain: sessionStorage.getItem("domain"),
            valid: true,
            activeTab: "",
            dialog: false,
            search: "",
            tableHeight: 800,
            loading: false,
            btnLoading: false,
            blurField: "",
            snackbar: {
              color: "success",
              text: "",
              show: false
            },
            headers: [
              {
                text: "Type",
                value: "type",
                align: "start",
                sortable: true,
                filterable: true
              },
              {
                text: "Label",
                value: "label",
                sortable: true,
                width: 400
              },
              { text: "English", value: "en", sortable: false },
              { text: "繁体中文", value: "zh", sortable: false },
              {
                text: "Multiple Language",
                value: "thirdLan",
                divider: true,
                sortable: false
              },
              {
                text: "Actions",
                value: "actions",
                align: "center",
                sortable: false,
                filterable: false,
                width: 150
              }
            ],
            tabs: [
              { label: "Index", value: "index", icon: "mdi-home", alias: "indexPageLan" },
              {
                label: "Location",
                value: "location",
                icon: "mdi-map-marker-multiple",
                alias: "googleMapPageLan"
              },
              { label: "Menu", value: "menu", icon: "mdi-silverware-clean", alias: "menuPageLan" },
              {
                label: "Order List",
                value: "orderList",
                icon: "mdi-clipboard-list",
                alias: ["memberOrderLan", "payOrderPageLan"]
              },
              {
                label: "Pay Done",
                value: "payDone",
                icon: "mdi-check-bold",
                alias: "paySuccessPageLan"
              },
              {
                label: "Pay Fail",
                value: "payFail",
                icon: "mdi-alert-circle",
                alias: "payFailurePageLan"
              },
              { label: "Global", value: "global", icon: "mdi-earth", alias: "publicLan" }
            ],
            tableData: [],
            allData: [],
            editedIndex: -1,
            editedItem: {
              type: null,
              slots: [],
              zh: "",
              en: "",
              thirdLan: ""
            },
            defaultItem: {
              type: null,
              slots: [],
              zh: "",
              en: "",
              thirdLan: ""
            },
            tribute: new Tribute({
              trigger: "#",
              values: [],
              containerClass:
                "v-card z-1000 overflow-hidden v-list v-sheet v-sheet--shaped theme--light v-list--dense",
              selectClass: "v-list-item--active",
              itemClass: "v-list-item v-item--active v-list-item--link theme--light",
              // selectTemplate(item) {
              //   return '#' + item.original.value
              // },
              menuItemTemplate(item) {
                return `<div class="v-list-item__content"><div class="v-list-item__title">${item.string}</div></div> `
              },
              noMatchTemplate() {
                return `<div tabindex="0" role="option" class="v-list-item v-list-item--link theme--light">
                              <div class="v-list-item__content">
                                <div class="v-list-item__title">No Match</div>
                              </div>
                            </div>`
              }
            }),
            rules: {
              required: [value => !!value?.trim() || "This field is required."]
            },
            fileSelectionStatus: {
              en: false,
              zh: false,
              thirdLan: false
            },
            funMap: {
              getEnLan: "en",
              getHkLan: "zh",
              getThirdLan: "thirdLan"
            },
            uploadFileStream: {}, // 解析完文件后的数据
            showUploadModal: false,
            showOpenFilePicker: !!window.showOpenFilePicker,
            uploadFiles: [], //选择的原始文件
            localI18n: {} //本地的语言数据
          }
        },
        methods: {
          closeUploadModal() {
            this.showUploadModal = false
            this.fileSelectionStatus = {
              en: false,
              zh: false,
              thirdLan: false
            }
            this.uploadFiles = []
            this.uploadFileStream = {}
          },
          // 确认上传按钮
          onPositiveClick() {
            if (this.allowSubmit) {
              this.btnLoading = true
              return fetch("../../manager_database/initDatabase", {
                method: "POST",
                body: this.toFormData({
                  domain: this.domain,
                  en: JSON.stringify(this.uploadFileStream.en),
                  zh: JSON.stringify(this.uploadFileStream.zh),
                  thirdLan: JSON.stringify(this.uploadFileStream.thirdLan)
                }),
                dataType: "FormData"
              })
                .finally(() => {
                  this.btnLoading = false
                })
                .then(r => r.json())
                .then(r => {
                  if (r.statusCode === 200) {
                    this.snackbar = {
                      color: "success",
                      text: "Upload Success",
                      show: true
                    }
                    this.closeUploadModal()
                    this.initialize()
                  } else {
                    this.snackbar = {
                      color: "error",
                      text: "Upload Fail",
                      show: true
                    }
                  }
                })
            } else {
              this.snackbar = {
                color: "warning",
                text: "Please select the correct file",
                show: true
              }
              return false
            }
          },
          // 打开文件选择器按钮
          async openFileSelector() {
            if (this.allowSubmit) return false
            return await window
              .showOpenFilePicker({
                multiple: true,
                types: [
                  {
                    description: "JavaScript",
                    accept: {
                      "javaScript/*": [".js"]
                    }
                  }
                ]
              })
              .then(fileHandle => {
                this.uploadFiles = fileHandle
                this.parseFiles(fileHandle)
              })
          },
          // vuetify 提供的文件选择器 列表更变
          updateFileList(fileList) {
            this.uploadFileStream = {}
            this.fileSelectionStatus = {
              en: false,
              zh: false,
              thirdLan: false
            }
            fileList.map(file => {
              let reader = new FileReader()
              reader.onload = e => {
                let contents = e.target.result
                try {
                  let funName = contents.match(/function\s+(\w+)/)[1]
                  if (!funName) return false
                  let funavow = new Function(`return ${contents}`)
                  this.uploadFileStream[this.funMap[funName]] = this.replaceSlot(funavow()())
                  this.fileSelectionStatus[this.funMap[funName]] = true
                } catch (e) {
                  console.error("error", `File does not match !: ${file.name}`)
                }
              }
              reader.readAsText(file)
            })
          },
          removeFile(item) {
            this.uploadFiles.splice(this.uploadFiles.indexOf(item), 1)
            this.parseFiles(this.uploadFiles)
          },
          /**
           * @description 运行选择的js文件,解析返回值并替换占位符,输出到uploadFileStream
           * @param {Array} files
           * */
          parseFiles(files) {
            this.fileSelectionStatus = {
              en: false,
              zh: false,
              thirdLan: false
            }
            files.map(file => {
              return file.getFile().then(file => {
                return file.text().then(text => {
                  let regex = /function\s+(\w+)\s*\(/
                  let match = text.match(regex)

                  if (match && match.length > 1) {
                    try {
                      let funName = match[1]
                      let funavow = new Function(`return ${text.toString()}`)
                      this.uploadFileStream[this.funMap[funName]] = this.replaceSlot(funavow()())
                      this.fileSelectionStatus[this.funMap[funName]] = true
                    } catch (e) {
                      console.error("error", `File does not match !: ${file.name}`)
                    }
                  }
                })
              })
            })
          },
          replaceSlot(dataSource) {
            // 替换json对象的key
            this.tabs.map(tab => {
              if (Array.isArray(tab.alias)) {
                tab.alias.map(alias => {
                  dataSource[tab.value] = { ...dataSource[tab.value], ...dataSource[alias] }
                  delete dataSource[alias]
                })
              } else {
                dataSource[tab.value] = dataSource[tab.alias]
                delete dataSource[tab.alias]
              }
            })
            // 替换占位符slot
            dataSource.menu.promptText = dataSource.menu.promptText.replace(
              "undefined",
              "#numberDishes"
            )
            return dataSource
          },
          // 向en,zh,thirdLan中添加slot字符串
          addSlotToValue(slot) {
            if (!this.blurField) return
            this.editedItem[this.blurField] += ` #${slot} `
          },
          formatRemoteData(remote) {
            const data = []
            for (const tab in remote) {
              if (Object.hasOwnProperty.call(remote, tab)) {
                let tabData = {
                  tab,
                  data: []
                }
                const tabValue = remote[tab]
                tabValue.forEach(item => {
                  let value = {
                    ...item,
                    ...item.value
                  }
                  delete value.value
                  tabData.data.push({
                    ...value
                  })
                })
                data.push(tabData)
              }
            }
            return data
          },
          async initialize() {
            this.loading = true
            return fetch(`../../i18n/getAll?domain=${this.domain}`)
              .finally(() =>
                // 解决先闪烁一下no data在渲染表格的问题
                setTimeout(() => {
                  this.loading = false
                }, 30)
              )
              .then(res => res.json())
              .then(res => {
                if (res.statusCode === 200) {
                  this.allData = this.formatRemoteData(res.data)
                  this.diffI18nWorkers()
                } else {
                  this.snackbar = {
                    color: "error",
                    text: "Failed to get data",
                    show: true
                  }
                }
              })
          },
          tabData() {
            //  根据tab获取当前tab的数据
            return this.allData.find(d => d.tab === this.activeTab)?.data || []
          },
          changeTab() {
            // 根据activeTab 获取当前tab的数据
            this.tableData = this.tabData()
          },
          getLocalByType(type) {
            return this.localI18n[this.activeTab][type]
          },
          async diffI18nWorkers() {
            const worker = new Worker("../../static/I18n/diffI18n.workers.js")
            this.localI18n = await fetch("../../static/I18n/language.json").then(res => res.json())
            worker.postMessage({
              local: this.localI18n,
              remote: this.allData,
              name: "cms-diff-i18n"
            })
            worker.onmessage = e => {
              const { diff, remote } = e.data
              //   循环data,将数据加入对应tab
              this.allData = remote
              for (const tab in diff) {
                let targetTab = this.allData.find(d => d.tab === tab)
                targetTab
                  ? targetTab.data.push(...diff[tab])
                  : this.allData.push({ tab, data: diff[tab] })
              }
              this.changeTab()
            }
          },

          editItem(item) {
            this.editedIndex = this.tableData.indexOf(item)
            item.slots = Array.isArray(item.slots) ? item.slots : []
            const slots =
              item?.slots?.map(slot => {
                return {
                  key: slot,
                  value: slot
                }
              }) || []
            this.editedItem = Object.assign({}, item)
            this.tribute.append(0, slots, true)
            this.dialog = true
          },
          deleteItem(item) {
            const index = this.tableData.indexOf(item)
            fetch("../../manager_i18n/deleteOne", {
              method: "post",
              body: this.toFormData({
                type: item.type,
                range: this.activeTab,
                domain: this.domain
              })
            })
              .then(res => res.json())
              .then(res => {
                if (res.statusCode === 200) {
                  let localItem = this.getLocalByType(item.type)
                  if (localItem) {
                    localItem = { ...localItem, ...localItem.value, type: item.type, add: true }
                    delete localItem.value
                    this.tableData.splice(index, 1, localItem)
                  } else {
                    this.tableData.splice(index, 1)
                  }
                  this.snackbar = {
                    color: "success",
                    text: "Delete success",
                    show: true
                  }
                } else {
                  this.snackbar = {
                    color: "error",
                    text: "Delete failed",
                    show: true
                  }
                }
              })
          },
          close() {
            this.dialog = false
            this.blurField = ""
            this.$nextTick(() => {
              this.editedItem = Object.assign({}, this.defaultItem)
              this.editedIndex = -1
            })
          },
          toFormData(obj) {
            let data = new FormData()
            for (const key in obj) {
              if (Object.hasOwnProperty.call(obj, key)) {
                const element = obj[key]
                data.append(key, element)
              }
            }
            return data
          },
          onSave() {
            let verify = this.$refs.form.validate()
            if (verify) {
              this.btnLoading = true
              let params = {
                ...this.editedItem,
                range: this.activeTab,
                domain: this.domain
              }

              fetch("../../manager_i18n/updateOne", {
                method: "post",
                body: this.toFormData(params),
                dataType: "JSON"
              })
                .finally(() => {
                  this.btnLoading = false
                })
                .then(res => {
                  return res.json()
                })
                .then(data => {
                  if (data.statusCode === 200) {
                    let newItem = { ...this.editedItem, add: false }
                    Object.assign(this.tableData[this.editedIndex], newItem)
                    this.close()
                    this.snackbar = {
                      text: "Update completed",
                      show: true,
                      color: "success"
                    }
                  } else {
                    this.snackbar = {
                      text: "Update failed",
                      show: true,
                      color: "error"
                    }
                  }
                })
                .catch(err => {
                  this.snackbar = {
                    text: "Update failed",
                    show: true,
                    color: "error"
                  }
                })
            }
          },
          onResize() {
            this.tableHeight = window.innerHeight - this.$refs["app-header"].$el.offsetHeight
            // this.headers[0].sortable = this.breakpoint === 'xs'
          },
          onScroll() {
            const scrollWarp = document.querySelector(".v-data-table__wrapper")
            let beforeScrollTop = scrollWarp.scrollTop
            scrollWarp.addEventListener("scroll", e => {
              let curTop = e.target.scrollTop
              if (curTop - beforeScrollTop > 3) {
                this.$nextTick(() => {
                  document.documentElement.scrollTop = 999
                })
              } else if (beforeScrollTop - curTop > 3) {
                this.$nextTick(() => {
                  document.documentElement.scrollTop = 0
                })
              }
              beforeScrollTop = curTop
            })
          }
        },
        computed: {
          formTitle() {
            return this.editedIndex === -1 ? "New Item" : "Edit Item"
          },
          breakpoint() {
            return this.$vuetify.breakpoint.name
          },
          allowSubmit() {
            return Object.values(this.fileSelectionStatus).reduce((r, c) => r + c, 0) === 3
          }
        },

        watch: {
          dialog(val) {
            if (val) {
              this.$nextTick(() => {
                let textarea = document.querySelectorAll(".mention textarea")
                if (!textarea[0].hasAttribute("data-tribute")) {
                  this.tribute.attach(document.querySelectorAll(".mention textarea"))
                }
                if (this.editedIndex === -1) {
                  // 新增弹窗: 重置slots
                  this.tribute.append(0, [], true)
                }
              })
            } else {
              this.$refs.form.reset()
              this.close()
            }
          }
        },
        created() {
          this.initialize()
        },
        mounted() {
          this.$nextTick(() => {
            document.documentElement.scrollTop = 0
          })
          this.onResize()
          this.onScroll()
        }
      })
    </script>
  </body>
</html>
