const registerAndResetMixin = {
  methods: {
    authFormPwdVisible(fieldName) {
      this.passwordVisible[fieldName] = !this.passwordVisible[fieldName]
    },
    authFormFormatNumber(event, fieldName) {
      this.formData[fieldName] = event.target.value.replace(/\D/g, "")
    },
    authFormOnPasswordInput(event, fieldName) {
      this.validateField(fieldName)
      if (fieldName !== "confirmPassword") {
        this.formData.confirmPassword && this.validateField("confirmPassword")
      }
    },
    authFormOnPasswordBlur(fieldName) {
      this.validateField(fieldName)
    },
    authFormMemCaptchaCD(areaCode) {
      let {
        aliyunSMS: { countdown: smsCountdown = 60 } = {},
        aliyunSMSGlobe: { countdown: globeCountdown = 60 } = {}
      } = this.openTable
      if (areaCode == 86) {
        //中国大陆
        this.configCodeCountingDown = smsCountdown
      } else {
        this.configCodeCountingDown = globeCountdown
      }
    },
    //首字母小写
    authFormToLowerFirstLetter(str) {
      return str.charAt(0).toLowerCase() + str.slice(1)
    },
    authFormHandlePhoneInput(event, fieldName) {
      this.formData[fieldName] = event.target.value.replace(/\D/g, "")
    },
    authFormGetLabelWidth() {
      let labelList = this.$refs.formLabel
      let maxWidth = 0
      labelList.forEach(item => {
        let width = item.offsetWidth + 25
        if (width > maxWidth) {
          maxWidth = width
        }
      })
      this.labelWidth = maxWidth
    },
    authFormHandleSelectChange(selectedItem) {
      let areaCode = `+${selectedItem.phone[0]}`
      this.formData.areaCode = areaCode
      this.authFormMemCaptchaCD(areaCode)

      // console.log("选择的项目：", selectedItem)
    },
    //失败回调
    authFormErrorCallback(err, type = "register") {
      let {
        registerDefaultError,
        resetPasswordDefaultError,
        error500,
        error5101,
        error5106,
        error5107
      } = this.systemLanguage
      switch (err.statusCode) {
        case 500:
          layer.msg(error500)
          break
        case 5101:
          layer.msg(error5101)
          break
        case 5106:
          layer.msg(error5106)
          break
        case 5107:
          layer.msg(error5107)
          break
        default:
          layer.msg(type === "register" ? registerDefaultError : resetPasswordDefaultError)
          break
      }
    },
    //验证码错误回调
    authFormErrorVerifyCode(err) {
      let { error500, error5107, error5108, error5105, verifyCodeDefaultError } =
        this.systemLanguage
      switch (err.statusCode) {
        case 500:
          layer.msg(error500)
          break
        case 5105:
          layer.msg(error5105)
          break
        case 5107:
          layer.msg(error5107)
          break
        case 5108:
          layer.msg(error5108)
          break
        default:
          layer.msg(verifyCodeDefaultError)
          break
      }
    },
    //首字母大写
    authFormCapitalize(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },
    authFormShowText(prefix, name) {
      return this.systemLanguage[`${prefix}${this.authFormCapitalize(name)}`]
    },
    authFormStartCountdown(type = "telephone") {
      const isPhone = type === "telephone"

      if (isPhone) {
        // this.codeCountingDown = this.testCountingDown
        this.codeCountingDown = this.configCodeCountingDown
        this.phoneVerifyCodeTimer = setInterval(() => {
          this.codeCountingDown--
          if (this.codeCountingDown <= 0) {
            clearInterval(this.phoneVerifyCodeTimer)
          }
        }, 1000)
      } else {
        this.emailCodeCountingDown = 3
        this.emailVerifyCodeTimer = setInterval(() => {
          this.emailCodeCountingDown--
          if (this.emailCodeCountingDown <= 0) {
            clearInterval(this.emailVerifyCodeTimer)
          }
        }, 1000)
      }
    }
  }
}
