<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>

    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/moment/moment.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>
    <style>
      html,
      body {
        padding: 0;
        margin: 0;
      }
      #app {
        padding: 8px 15px;
      }

      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .el-form-item__content {
        display: flex;
      }
      .el-form-item__content > div {
        flex: 1;
      }
      .el-form-item__content > button {
        margin-left: 10px;
      }
      .el-dialog__body {
        overflow: auto;
      }
      .table-column-div p {
        margin: 0;
        padding: 0;
      }
      .el-button + .el-button {
        margin-left: 10px;
      }
      .customDateFormItem {
        margin-right: 10px;
      }
      .customDateDiv {
        display: flex;
      }
      .customTimeFormItem .el-form-item__content {
        margin-left: 10px !important;
      }
      .customDateDiv .customDateFormItem,
      .customDateDiv .customTimeFormItem {
        flex: 1;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <template>
        <el-table
          v-if="tableHeight"
          :data="tableData"
          style="width: 100%"
          :max-height="tableHeight"
          border
          id="exportTable"
          ref="report-table"
          empty-text="No Data"
          class="tableContent"
        >
          <el-table-column
            :fixed="item.fixed"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            v-for="(item,index) in tableColumn"
            align="center"
          >
            <template slot-scope="scope">
              <template v-if="item.prop !='storeNumber'">
                <div v-if="scope.row[item.prop][0]!=''" class="table-column-div">
                  <p>{{scope.row[item.prop][0]}}</p>
                  <p>~</p>
                  <p>{{scope.row[item.prop][1]}}</p>
                </div>
              </template>
              <span v-else>{{scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
          <!-- 操作栏目 -->
          <el-table-column fixed="right" prop label="Operation" align="center" width="100">
            <el-table-column width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <!-- 除按钮类型外显示编辑按钮 -->
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-popconfirm
                  title="Are you sure to reset all times in your store?"
                  icon-color="red"
                  @confirm="onReset(scope.$index, scope.row)"
                  class="marginLeft10"
                >
                  <el-button
                    size="small"
                    type="danger"
                    icon="el-icon-refresh-right"
                    circle
                    slot="reference"
                  ></el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 弹窗 -->
      <template>
        <el-dialog title="Edit" :visible.sync="dialogVisible" @close="resetForm('form')">
          <el-form :model="form" :rules="rules" ref="form" label-width="150px">
            <!-- 遍历formItem对应生成el-form-item -->
            <el-form-item
              v-for="(item,index) in formItem"
              :label="item.label"
              :prop="item.prop"
              :key="item.prop"
            >
              <el-time-picker
                v-if="item.elementType=='timePicker'"
                is-range
                range-separator="~"
                start-placeholder="Start Hour"
                end-placeholder="End Hour"
                value-format="HH:mm:ss"
                v-model="form[item.prop]"
              ></el-time-picker>
              <!--判断item.label==Monday日期组件后面增加一个按钮用于复制数据到后面的日期组件上  -->
              <el-button v-if="item.prop=='monday'" @click="OnOverWrite" type="primary" plain>
                Copy and Overwrite
              </el-button>
              <el-input
                v-else-if="item.elementType=='input'"
                v-model="form[item.prop]"
                placeholder="Please input shop code"
                :disabled="item.disabled"
              ></el-input>
            </el-form-item>
            <div v-for="(dateItem, i) in form.customDate" :key="dateItem.key" class="customDateDiv">
              <el-form-item
                class="customDateFormItem"
                :label="'Custom Date ' + (i + 1)"
                :key="dateItem.key"
                :prop="'customDate.' + i + '.date'"
                :rules="{
              required: hasRequired(i),
              message: 'Please select date range',
              trigger: 'blur'
            }"
              >
                <el-date-picker
                  v-model="dateItem.date"
                  type="daterange"
                  range-separator="~"
                  start-placeholder="Start Date"
                  end-placeholder="End Date"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
              </el-form-item>

              <el-form-item
                :prop="'customDate.' + i + '.time'"
                :rules="{
              required: hasRequired(i),
              message: 'Please select time range',
              trigger: 'blur'
            }"
                class="customTimeFormItem"
              >
                <el-time-picker
                  is-range
                  v-model="dateItem.time"
                  range-separator="~"
                  start-placeholder="Start Time"
                  end-placeholder="End Time"
                  placeholder="Select Time Range"
                  value-format="HH:mm:ss"
                ></el-time-picker>
                <el-button
                  v-if="i==0"
                  :disabled="isCustomDateOff"
                  @click="addCustomDate"
                  type="success"
                  plain
                >
                  Add
                </el-button>
                <el-button
                  :disabled="form.customDate.length == 1"
                  @click.prevent="removeDomain(dateItem)"
                  type="danger"
                  plain
                >
                  Delete
                </el-button>
              </el-form-item>
            </div>
          </el-form>
          <div slot="footer">
            <el-button @click="dialogVisible = false">Cancel</el-button>
            <el-button type="primary" @click="onSubmit">Submit</el-button>
          </div>
        </el-dialog>
      </template>
    </div>
    <script>
      function validateDateTime(i) {
        return (rule, value, callback) => {
          if (i == 0) {
            callback()
            return
          }
          if (!value.date || !value.date[0] || !value.date[1]) {
            callback(new Error("Please select date range"))
            return
          }
          if (!value.time || !value.time[0] || !value.time[1]) {
            callback(new Error("Please select time range"))
            return
          }
          callback()
        }
      }
      var app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          date: "",
          dialogVisible: false,
          domain: sessionStorage.getItem("domain"),
          retain: {},
          form: {
            id: "",
            storeNumber: "",
            monday: ["", ""],
            tuesday: ["", ""],
            wednesday: ["", ""],
            thursday: ["", ""],
            friday: ["", ""],
            saturday: ["", ""],
            sunday: ["", ""],
            holiday: ["", ""],
            holidayEve: ["", ""],
            customDate: [
              {
                date: "",
                time: "",
                key: 1
              }
            ]
          },

          formItem: [
            {
              elementType: "input",
              label: "Shop Code",
              prop: "storeNumber",

              disabled: true
            },
            {
              elementType: "timePicker",
              label: "Monday",
              prop: "monday"
            },
            {
              elementType: "timePicker",
              label: "Tuesday",
              prop: "tuesday"
            },
            {
              elementType: "timePicker",
              label: "Wednesday",
              prop: "wednesday"
            },
            {
              elementType: "timePicker",
              label: "Thursday",
              prop: "thursday"
            },
            {
              elementType: "timePicker",
              label: "Friday",
              prop: "friday"
            },
            {
              elementType: "timePicker",
              label: "Saturday",
              prop: "saturday"
            },
            {
              elementType: "timePicker",
              label: "Sunday",
              prop: "sunday"
            },
            {
              elementType: "timePicker",
              label: "H",
              prop: "holiday"
            },
            {
              elementType: "timePicker",
              label: "H-1",
              prop: "holidayEve"
            }
          ],
          tableColumn: [
            {
              label: "Shop Code",
              prop: "storeNumber",
              width: "100",
              fixed: true
            },
            {
              label: "Monday",
              prop: "monday",
              width: "",
              fixed: false
            },
            {
              label: "Tuesday",
              prop: "tuesday",
              width: "",
              fixed: false
            },
            {
              label: "Wednesday",
              prop: "wednesday",
              width: "",
              fixed: false
            },
            {
              label: "Thursday",
              prop: "thursday",
              width: "",
              fixed: false
            },
            {
              label: "Friday",
              prop: "friday",
              width: "",
              fixed: false,
              isScope: false
            },
            {
              label: "Saturday",
              prop: "saturday",
              width: "",
              fixed: false,
              isScope: false
            },
            {
              label: "Sunday",
              prop: "sunday",
              width: "",
              fixed: false
            },
            {
              label: "H",
              prop: "holiday",
              width: "",
              fixed: false
            },
            {
              label: "H-1",
              prop: "holidayEve",
              width: "",
              fixed: false,
              isScope: false
            }
          ],

          tableData: [],
          rules: {
            storeNumber: [{ required: true, message: "Please input storeNumber", trigger: "blur" }]
          }
        },
        created() {
          this.getData()
          this.retain = JSON.parse(JSON.stringify(this.form))
        },
        mounted() {
          // 页面加载的时候设置table的高度
          this.tableHeight = this.getTableHeight(this, 0, null)
          // this.tableHeight = 700
          console.log(this.tableHeight, "this.tableHeight")
          // 页面大小该变的时候（缩放页面）设置table的高度（可加可不加）
          // window.onresize = () => {
          //   this.tableHeight = this.getTableHeight(this, 40, 'headerBox')
          //   console.log(this.tableHeight, '触发后')
          // }
        },
        computed: {
          isCustomDateOff() {
            let { customDate } = this.form
            return customDate.some(item => {
              return !item.date || !item.time
            })
          }
        },
        watch: {
          "form.customDate": {
            handler(newVal, oldVal) {
              if (newVal.length == 1) {
                if (!newVal[0].date && !newVal[0].time) {
                  this.$nextTick(() => {
                    this.$refs.form.clearValidate("customDate.0.date")
                    this.$refs.form.clearValidate("customDate.0.time")
                  })
                }
              }
            },
            deep: true
          }
        },
        methods: {
          hasRequired(i) {
            let firstItemRequired = false
            let { customDate } = this.form
            if (i == 0) {
              let { date, time } = customDate[0]
              firstItemRequired = !!(date || time)
            }
            return firstItemRequired || i > 0 || customDate.length > 1
          },
          getData() {
            $.get({
              url: "../../manager_storeNumber/selectOpeningTime",
              dataType: "json",
              data: { domain: this.domain },
              success: res => {
                if (res.statusCode === 200) {
                  // console.log(JSON.parse(JSON.stringify(res)), "初始请求数据")
                  this.tableData = res.data
                } else {
                  this.$message.error("Request failed, please try again")
                }
              },
              error: () => {
                this.$message.error("Request failed, please try again")
              }
            })
          },
          //点击按钮打开弹窗
          onEdit(index, row) {
            // this.form = JSON.parse(JSON.stringify(row))
            this.form = JSON.parse(JSON.stringify({ ...this.retain, ...row }))
            console.log("🚀 ~ onEdit ~ this.form:", this.form)
            this.dialogVisible = true
          },
          onSubmit() {
            this.toDoWithTime(this.form) //重置时间字段,替换所有null值
            let data = JSON.parse(JSON.stringify({ ...this.form, domain: this.domain }))
            //处理this.form中的时间数据,为null的时候赋值为['','']
            console.log(JSON.parse(JSON.stringify(this.form)), "提交的数据")
            this.$refs.form.validate(valid => {
              if (valid) {
                this.updateOpeningTime(data)
              } else {
                console.log("error submit!!")
                return false
              }
            })
          },
          updateOpeningTime(data) {
            if (
              !data.customDate ||
              (data.customDate.length === 1 && !data.customDate[0].date && !data.customDate[0].time)
            ) {
              delete data.customDate
            }
            $.post({
              url: "../../manager_storeNumber/updateOpeningTime",
              dataType: "json",
              data: JSON.stringify(data),
              contentType: "application/json",
              // traditional: true, // 防止深度序列号
              success: res => {
                if (res.statusCode === 200) {
                  this.getData()
                  this.dialogVisible = false
                  this.$message.success("Edit Success！")
                } else {
                  this.$message.error("Request failed, please try again")
                }
              },
              error: () => {
                this.$message.error("Request failed, please try again")
              }
            })
          },

          OnOverWrite() {
            //判断this.form.monday是否存在值,存在则将值赋给后面的所有时间字段
            const { monday, ...rest } = this.form
            for (let i in rest) {
              if (["id", "storeNumber", "customDate"].includes(i)) continue
              this.form[i] = monday
            }
          },
          onReset(index, row) {
            let data = { id: row.id, domain: this.domain }
            this.updateOpeningTime(data)
          },

          //时间组件点击清空按钮时,重新赋值过几秒会变成null,所以需要过滤掉
          toDoWithTime() {
            for (let key in this.form) {
              if (this.form[key] === null) {
                this.form[key] = ["", ""]
              }
            }
          },
          resetForm(formName) {
            this.$refs[formName].resetFields()
            //清除校验
            this.$refs[formName].clearValidate()
            console.log(this.$refs[formName], "this.$refs[formName]")
          },

          /**
           * table的max-height高度
           * @param that，解决this指向问题
           * @param val，固定的站位高度（例如：分页高度）
           * @param name，动态站位的高度获取的name（例如：查询动态的查询条件）
           * @returns {number}，返回可用的高度
           * @param fixHeight,最外层样式高度
           */
          getTableHeight(that, val = 32, name) {
            let fixHeight = 40 // padding为20,20
            let searchFormHeight = name ? that.$refs[name].clientHeight : 0 // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - 80) // 计算完之后剩余table可用的高度
            // console.log(pageHeight, searchFormHeight, tableHeight, "searchFormHeight")
            return tableHeight
          },
          removeDomain(dateItem) {
            let index = this.form.customDate.indexOf(dateItem)
            if (index !== -1) {
              this.form.customDate.splice(index, 1)
            }
          },
          addCustomDate() {
            this.form.customDate.push({
              date: "",
              time: "",
              key: Date.now() // 使用时间戳作为唯一key
            })
          }
        }
      })
    </script>
  </body>
</html>
