function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}
const os = (function () {
  let ua = navigator.userAgent,
    isWindowsPhone = /(?:Windows Phone)/.test(ua),
    isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
    isAndroid = /(?:Android)/.test(ua),
    isFireFox = /(?:Firefox)/.test(ua),
    isChrome = /(?:Chrome|CriOS)/.test(ua),
    isTablet =
      /(?:iPad|PlayBook)/.test(ua) ||
      (isAndroid && !/(?:Mobile)/.test(ua)) ||
      (isFireFox && /(?:Tablet)/.test(ua)),
    isPhone = /(?:iPhone)/.test(ua) && !isTablet,
    isPc = !isPhone && !isAndroid && !isSymbian
  return {
    isTablet: isTablet,
    isPhone: isPhone,
    isAndroid: isAndroid,
    isPc: isPc
  }
})()
function findMark() {
  let path = window.location.pathname
  let markList = [
    "map.html",
    "menuPage",
    "payOrderPage.html",
    "payOrderPage.html",
    "paySuccessPage.html",
    "payFailurePage.html",
    "historyIndex"
  ]
  let mark = null
  let src = null
  //通过url判断当前页面
  for (let i = 0; i < markList.length; i++) {
    if (path.indexOf(markList[i]) !== -1) {
      mark = "pc_" + markList[i].split(".")[0]
      src = "../static/css/pc/pc_model.css"
      break
    }
  }
  //若没有 ,则为index页(验证  /index/ 判断2个斜杠 )
  if (!mark) {
    if (path.split("/").length === 3) {
      mark = "pc_index"
      src = "./static/css/pc/pc_model.css"
    }
  }
  return { mark, src }
}
let { mark, src } = findMark()
//判断是index页面还是其他页面
let API_PATH = mark === "pc_index" ? "./" : "../"
if (!isMobile() && os.isPc) {
  //是pc端
  if (mark && src) {
    // 添加通用class : pc
    document.body.classList.add("pc")
    //为每个页面添加自定义class mark
    document.body.classList.add(mark)
    //引入pc all样式文件
    let pcLink = document.createElement("link")
    pcLink.href = src
    pcLink.rel = "stylesheet"
    document.head.appendChild(pcLink)
    let sessionZoomFontSize = sessionStorage.getItem("zoomFontSize")
    // 获取html上的data-page属性
    let pageData = window.document.documentElement.getAttribute("data-page")
    if (sessionZoomFontSize && pageData !== "index") {
      let { currentFontSize } = JSON.parse(sessionZoomFontSize)
      document.documentElement.style.fontSize = currentFontSize
    } else {
      document.documentElement.style.fontSize = "39px"
    }
    // layer.load(2)

    //移除lib-flexible依赖
    let dom = document.querySelector("script[pc-del]")
    dom && document.head.removeChild(dom)
    //移除 lib-flexible 的resize侦听事件
    window.removeEventListener("resize", window.diy_resize, false)
    //是index页面则不走下面的逻辑(避免有缓存)
    if (mark !== "pc_index") {
      //判断是否有PC端自定义的背景图和颜色
      let PCbgImg = sessionStorage.getItem("PCbgImg")
      let PCbgColor = sessionStorage.getItem("PCbgColor")
      if (PCbgImg) {
        PCbgImg = JSON.parse(PCbgImg)
        let defaultOss = "https://appwise.oss-cn-hongkong.aliyuncs.com"
        let url = `${defaultOss}/${PCbgImg.domain}/${PCbgImg.storeNumber}/image/${PCbgImg.typeName}/${PCbgImg.fileName}.jpg`
        checkImage(url, [defaultOss])
          .then(r => {
            document.documentElement.style.setProperty("background-image", `url('${r.url}')`)
            document.documentElement.style.setProperty("background-size", "cover")
          })
          .catch(e => {
            console.log(e)
          })
      }
      if (PCbgColor) {
        document.documentElement.style.setProperty("background-color", PCbgColor)
      }
    }
  }
} else if (os.isTablet) {
  // 添加通用class : 平板
  const tag = document.createElement("link")
  let url = null
  if (mark === "pc_menuPage") {
    url = "../static/css/page/menuPage-PadModel.css"
  } else {
    url = "./style/index_PadModel.css"
  }
  tag.href = url
  tag.rel = "stylesheet"
  document.head.appendChild(tag)
} else if (os.isAndroid || os.isPhone) {
  // 添加通用class : 手机
  document.body.classList.add("mobile_menuPage") //增加类名解决tab_warp盒子滚动条样式问题
}
