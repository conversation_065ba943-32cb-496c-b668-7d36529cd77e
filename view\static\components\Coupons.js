Vue.component("CouponsView", {
  props: ["systemLanguage", "errorHandle", "openTable"],
  data() {
    return {
      list: [],
      show: false,
      animation: null,
      height: 550,
      activeCoupon: null,
      qrcodeWidth: 250,
      loading: false,
      active: "available"
    }
  },
  components: {
    "bottom-sheet": BottomSheet
  },
  mounted() {
    // this.height = (document.querySelector("#app").clientHeight / 3) * 2
  },
  methods: {
    init() {
      this.active = "available"
      this.getCoupons()
    },
    getCoupons() {
      const isIndex = mark === "pc_index"
      const prefix = isIndex ? "./" : "../"
      this.loading = true
      fetch(`${prefix}memberECoupon/getEnableECoupon`, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          storeNumber: this.openTable.storeNumber
        },
        body: JSON.stringify({
          tableNumber: this.openTable.tableNumber
        })
      })
        .then(r => r.json())
        .then(r => {
          if (r.statusCode === 200) {
            this.list = r.data
            return r.data || []
          }
          this.errorHandle(r.statusCode)
          return []
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 積分允許且沒有生效日期<已生效>
    check(item) {
      return item.enableByPoints && !item.date_effective
    },
    showQrCode(data) {
      const { ecoupon_no } = data
      this.show = true
      this.$nextTick(() => {
        const dom = document.querySelector(".qrcode-content")
        if (!dom) return false
        dom.innerHTML = ``
        new QRCode(dom, {
          text: ecoupon_no,
          width: this.qrcodeWidth,
          height: this.qrcodeWidth,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H
        })
        this.open()
        this.activeCoupon = data
      })
    },
    open() {
      const sheet = this.$refs["couponQRCodeBottomSheet"]
      sheet && sheet.open()
    },
    close() {
      const sheet = this.$refs["couponQRCodeBottomSheet"]
      sheet && sheet.close()
    },
    //pos 日期格式为yyyy/mm/dd
    formatDate(date = "") {
      return moment(date).format("YYYY/MM/DD")
    },
    toggleTab(value = "available") {
      this.active = value
    },
    // 判断当前店铺日期是否在生效日期与截至日期之间
    checkDateAvailability(coupon, currentDate = new Date()) {
      const { date_effective, expdate } = coupon
      const helper = (start, end) => {
        const [startDate, startTime] = start.split(" ")
        const [endDate, endTime] = end.split(" ")
        return {
          datePeriod: [startDate, endDate],
          timePeriod: [startTime, endTime]
        }
      }
      const { datePeriod, timePeriod } = helper(date_effective, expdate)
      return verificationTimePeriod(datePeriod, timePeriod, true, currentDate)
    },
    hasProperty(obj, key) {
      return key in obj
    },
    formatUnavailableCause(item) {
      if (this.hasProperty(item, "date_effective")) {
        // 未至生效日期
        return this.systemLanguage.couponUnsatisfiedEffectiveDate.replace(
          "#date",
          this.formatDate(item.date_effective)
        )
      }
      if (!item.enableByPoints) {
        // 低于积分
        return this.systemLanguage.couponUnsatisfiedPoints.replace("#points", item.r_points)
      }
    },
    formatAvailableDesc(item) {
      const { r_points } = item
      return this.systemLanguage
    },
    formatValidityPeriod(data) {
      const { expdate, date_effective, r_points } = data
      if (this.checkAvailable) {
        const date = expdate ? this.formatDate(expdate) : this.systemLanguage.noExpirationDate
        return this.systemLanguage.couponValidityPeriod.replace("#date", date)
      }
      // 生效日期優先
      if (date_effective) {
        let { date_effective: start, expdate: end } = data
        // 未至生效日期
        let value = this.systemLanguage.effectiveDate
        value = value.replace("#start", this.formatDate(start))
        value = value.replace(
          "#end",
          (end && this.formatDate(end)) || this.systemLanguage.noExpirationDate
        )
        return value
      }
    },
    formatValidityPoint(data) {
      const { r_points, enableByPoints } = data
      if (this.checkAvailable) {
        return this.systemLanguage.validPoints.replace("#points", r_points)
      } else {
        return enableByPoints ? "" : this.systemLanguage.validPoints.replace("#points", r_points)
      }
    }
  },
  computed: {
    checkAvailable() {
      return this.active === "available"
    },
    renderList() {
      if (this.checkAvailable) {
        return this.list.filter(this.check)
      }
      return this.list.filter(it => !this.check(it))
    }
  },
  template: `
<transition name="fade">
  <div class="coupons-view">
      <div class="coupons__header">
        <div class="coupons__header-title-wrap">
          <div class="empty-c5Vus4"></div>
          <div class="coupons__header-title"><strong>{{systemLanguage.coupons}}</strong></div>
          <div class="refresh-coupon__btn" role="button" :class="{rotate:loading}"></div>
      </div>
        <div class="coupons__tab">
          <div class="tab-bar-wrapper">
            <div class="available" :class="{active:active==='available'}" @click="toggleTab('available')">
              {{systemLanguage.available}}
            </div>
            <div class="unavailable" :class="{active:active!=='available'}" @click="toggleTab('unavailable')">
              {{systemLanguage.unavailable}}
            </div>
          </div>
      </div>
      </div>
      <div class="loading-wrapping" v-if="loading">
        <span class="line-md--loading-loop"></span>
      </div>
      <template v-else>
        <div class="coupons-list" :class="{available:checkAvailable}" v-if="renderList.length">
          <div class="coupon-item" v-for="item in renderList" :key="item.ecoupon_no">
            <div class="coupon-wrap-top">  
              <div class="coupon-info" >
                <div class="coupon-info_title double-line">
                  {{item.desc1}}
                </div>
                <div class="coupon-use-terms">
                  <p class="coupon_timeout-time"> 
                      {{formatValidityPeriod(item)}}
                  </p>
                  <p class="coupon_need-points"> 
                      {{formatValidityPoint(item)}}
                  </p>
              </div>
              </div>
              <div class="coupon-control">
                  <button class="qrcode-btn" @click="showQrCode(item)">{{systemLanguage.toUseCoupon}}</button>
              </div>
            </div>
            <div class="coupon-wrap-bottom" v-if="!checkAvailable">
              <div class="unavailable-cause double-line">
                {{formatUnavailableCause(item)}}
              </div>
            </div>
          </div>
      </div>  
        <div class="empty-content" v-else>
            <span>{{systemLanguage.emptyCouponTip}}</span>
        </div>
      </template>
      <bottom-sheet
        :overlay-click-close="false"
        :transition-duration="0.5"
        :z-index="20001000"
        :init-sheet-height="height"
        ref="couponQRCodeBottomSheet"
      >
        <div class="sheet-content">
          <div class="qrcode-fn-co">
            <div class="qrcode-content"></div>
             <div class="qrcode-info">
               {{activeCoupon&&activeCoupon.desc1||''}}
             </div>
          </div>
        </div>
        <template #footer>
          <div class="bottom-sheet-footer">
            <button type="button" class="close-btn" @click="close">{{systemLanguage.closeBtn}}</button>
          </div>
        </template>
      </bottom-sheet>
  </div>
  
</transition>
  `
})
