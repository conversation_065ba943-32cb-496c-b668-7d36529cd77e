/**
 * TinyMCE version 6.1.0 (2022-06-29)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=o=e,(r=String).prototype.isPrototypeOf(n)||(null===(l=o.constructor)||void 0===l?void 0:l.name)===r.name)?"string":t;var n,o,r,l})(t)===e,n=e=>t=>typeof t===e,o=t("string"),r=t("object"),l=t("array"),a=(null,e=>null===e);const s=n("boolean"),i=n("function"),c=(e,t)=>{if(l(e)){for(let n=0,o=e.length;n<o;++n)if(!t(e[n]))return!1;return!0}return!1},u=()=>{},g=(e,t)=>e===t;class m{constructor(e,t){this.tag=e,this.value=t}static some(e){return new m(!0,e)}static none(){return m.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?m.some(e(this.value)):m.none()}bind(e){return this.tag?e(this.value):m.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:m.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return null==e?m.none():m.some(e)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}m.singletonNone=new m(!1);const d=Array.prototype.indexOf,h=Array.prototype.push,p=e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!l(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);h.apply(t,e[n])}return t},f=(e,t)=>{for(let n=0;n<e.length;n++){const o=t(e[n],n);if(o.isSome())return o}return m.none()},k=(e,t,n=g)=>e.exists((e=>n(e,t))),v=e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t},y=(e,t)=>e?m.some(t):m.none(),x=e=>t=>t.options.get(e),_=x("link_assume_external_targets"),b=x("link_context_toolbar"),w=x("link_list"),C=x("link_default_target"),O=x("link_default_protocol"),N=x("link_target_list"),A=x("link_rel_list"),T=x("link_class_list"),S=x("link_title"),E=x("allow_unsafe_link_target"),P=x("link_quicklink");var R=tinymce.util.Tools.resolve("tinymce.util.Tools");const L=e=>o(e.value)?e.value:"",M=(e,t)=>{const n=[];return R.each(e,(e=>{const r=(e=>o(e.text)?e.text:o(e.title)?e.title:"")(e);if(void 0!==e.menu){const o=M(e.menu,t);n.push({text:r,items:o})}else{const o=t(e);n.push({text:r,value:o})}})),n},D=(e=L)=>t=>m.from(t).map((t=>M(t,e))),B=e=>D(L)(e),I=D,j=(e,t)=>n=>({name:e,type:"listbox",label:t,items:n}),K=L,U=Object.keys,q=Object.hasOwnProperty,F=(e,t)=>q.call(e,t);var V=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),$=tinymce.util.Tools.resolve("tinymce.util.URI");const z=e=>e&&"a"===e.nodeName.toLowerCase(),G=e=>z(e)&&!!W(e),H=(e,t)=>{if(e.collapsed)return[];{const n=e.cloneContents(),o=new V(n.firstChild,n),r=[];let l=n.firstChild;do{t(l)&&r.push(l)}while(l=o.next());return r}},J=e=>/^\w+:/i.test(e),W=e=>e.getAttribute("data-mce-href")||e.getAttribute("href"),Q=(e,t)=>{const n=["noopener"],o=e?e.split(/\s+/):[],r=e=>e.filter((e=>-1===R.inArray(n,e))),l=t?(e=>(e=r(e)).length>0?e.concat(n):n)(o):r(o);return l.length>0?(e=>R.trim(e.sort().join(" ")))(l):""},X=(e,t)=>(t=t||e.selection.getNode(),te(t)?e.dom.select("a[href]",t)[0]:e.dom.getParent(t,"a[href]")),Y=(e,t)=>(t?t.innerText||t.textContent:e.getContent({format:"text"})).replace(/\uFEFF/g,""),Z=e=>R.grep(e,G).length>0,ee=e=>{const t=e.schema.getTextInlineElements();return 0===H(e.selection.getRng(),(e=>1===e.nodeType&&!z(e)&&!F(t,e.nodeName.toLowerCase()))).length},te=e=>e&&"FIGURE"===e.nodeName&&/\bimage\b/i.test(e.className),ne=(e,t,n)=>{const o=e.selection.getNode(),r=X(e,o),l=((e,t)=>{const n={...t};if(0===A(e).length&&!E(e)){const e=Q(n.rel,"_blank"===n.target);n.rel=e||null}return m.from(n.target).isNone()&&!1===N(e)&&(n.target=C(e)),n.href=((e,t)=>"http"!==t&&"https"!==t||J(e)?e:t+"://"+e)(n.href,_(e)),n})(e,(e=>{return t=["title","rel","class","target"],n=(t,n)=>(e[n].each((e=>{t[n]=e.length>0?e:null})),t),o={href:e.href},((e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)})(t,((e,t)=>{o=n(o,e)})),o;var t,n,o})(n));e.undoManager.transact((()=>{n.href===t.href&&t.attach(),r?(e.focus(),((e,t,n,o)=>{n.each((e=>{F(t,"innerText")?t.innerText=e:t.textContent=e})),e.dom.setAttribs(t,o),e.selection.select(t)})(e,r,n.text,l)):((e,t,n,o)=>{te(t)?se(e,t,o):n.fold((()=>{e.execCommand("mceInsertLink",!1,o)}),(t=>{e.insertContent(e.dom.createHTML("a",o,e.dom.encode(t)))}))})(e,o,n.text,l)}))},oe=e=>{const{class:t,href:n,rel:o,target:r,text:l,title:s}=e;return((e,t)=>{const n={};var o;return((e,t,n,o)=>{((e,t)=>{const n=U(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}})(e,((e,r)=>{(t(e,r)?n:o)(e,r)}))})(e,((e,t)=>!1===a(e)),(o=n,(e,t)=>{o[t]=e}),u),n})({class:t.getOrNull(),href:n,rel:o.getOrNull(),target:r.getOrNull(),text:l.getOrNull(),title:s.getOrNull()})},re=(e,t,n)=>{const o=((e,t)=>{const n=e.options.get,o={allow_html_data_urls:n("allow_html_data_urls"),allow_script_urls:n("allow_script_urls"),allow_svg_data_urls:n("allow_svg_data_urls")},r=t.href;return{...t,href:$.isDomSafe(r,"a",o)?r:""}})(e,n);e.hasPlugin("rtc",!0)?e.execCommand("createlink",!1,oe(o)):ne(e,t,o)},le=e=>{e.hasPlugin("rtc",!0)?e.execCommand("unlink"):(e=>{e.undoManager.transact((()=>{const t=e.selection.getNode();te(t)?ae(e,t):(e=>{const t=e.dom,n=e.selection,o=n.getBookmark(),r=n.getRng().cloneRange(),l=t.getParent(r.startContainer,"a[href]",e.getBody()),a=t.getParent(r.endContainer,"a[href]",e.getBody());l&&r.setStartBefore(l),a&&r.setEndAfter(a),n.setRng(r),e.execCommand("unlink"),n.moveToBookmark(o)})(e),e.focus()}))})(e)},ae=(e,t)=>{const n=e.dom.select("img",t)[0];if(n){const o=e.dom.getParents(n,"a[href]",t)[0];o&&(o.parentNode.insertBefore(n,o),e.dom.remove(o))}},se=(e,t,n)=>{const o=e.dom.select("img",t)[0];if(o){const t=e.dom.create("a",n);o.parentNode.insertBefore(t,o),t.appendChild(o)}},ie=(e,t)=>f(t,(t=>(e=>{return F(t=e,n="items")&&void 0!==t[n]&&null!==t[n];var t,n})(t)?ie(e,t.items):y(t.value===e,t))),ce=(e,t)=>{const n={text:e.text,title:e.title},o=(e,o)=>{const r=(l=t,a=o.name,"link"===a?l.link:"anchor"===a?l.anchor:m.none()).getOr([]);var l,a;return((e,t,n,o)=>{const r=o[t],l=e.length>0;return void 0!==r?ie(r,n).map((t=>({url:{value:t.value,meta:{text:l?e:t.text,attach:u}},text:l?e:t.text}))):m.none()})(n.text,o.name,r,e)};return{onChange:(e,t)=>{const r=t.name;return"url"===r?(e=>{const t=(o=e.url,y(n.text.length<=0,m.from(o.meta.text).getOr(o.value)));var o;const r=(e=>y(n.title.length<=0,m.from(e.meta.title).getOr("")))(e.url);return t.isSome()||r.isSome()?m.some({...t.map((e=>({text:e}))).getOr({}),...r.map((e=>({title:e}))).getOr({})}):m.none()})(e()):((e,t)=>d.call(e,t))(["anchor","link"],r)>-1?o(e(),t):"text"===r||"title"===r?(n[r]=e()[r],m.none()):m.none()}}};var ue=tinymce.util.Tools.resolve("tinymce.util.Delay");const ge=e=>{const t=e.href;return t.indexOf("@")>0&&-1===t.indexOf("/")&&-1===t.indexOf("mailto:")?m.some({message:"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",preprocess:e=>({...e,href:"mailto:"+t})}):m.none()},me=(e,t)=>n=>{const o=n.href;return 1===e&&!J(o)||0===e&&/^\s*www(\.|\d\.)/i.test(o)?m.some({message:`The URL you entered seems to be an external link. Do you want to add the required ${t}:// prefix?`,preprocess:e=>({...e,href:t+"://"+o})}):m.none()},de=e=>{const t=e.dom.select("a:not([href])"),n=p(((e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o})(t,(e=>{const t=e.name||e.id;return t?[{text:t,value:"#"+t}]:[]})));return n.length>0?m.some([{text:"None",value:""}].concat(n)):m.none()},he=e=>{const t=T(e);return t.length>0?B(t):m.none()},pe=e=>{try{return m.some(JSON.parse(e))}catch(e){return m.none()}},fe=(e,t)=>{const n=A(e);if(n.length>0){const o=k(t,"_blank"),r=e=>Q(K(e),o);return(!1===E(e)?I(r):B)(n)}return m.none()},ke=[{text:"Current window",value:""},{text:"New window",value:"_blank"}],ve=e=>{const t=N(e);return l(t)?B(t).orThunk((()=>m.some(ke))):!1===t?m.none():m.some(ke)},ye=(e,t,n)=>{const o=e.getAttrib(t,n);return null!==o&&o.length>0?m.some(o):m.none()},xe=(e,t)=>(e=>{const t=t=>e.convertURL(t.value||t.url,"href"),n=w(e);return new Promise((e=>{o(n)?fetch(n).then((e=>e.ok?e.text().then(pe):Promise.reject())).then(e,(()=>e(m.none()))):i(n)?n((t=>e(m.some(t)))):e(m.from(n))})).then((e=>e.bind(I(t)).map((e=>e.length>0?[{text:"None",value:""}].concat(e):e))))})(e).then((n=>{const o=((e,t)=>{const n=e.dom,o=ee(e)?m.some(Y(e.selection,t)):m.none(),r=t?m.some(n.getAttrib(t,"href")):m.none(),l=t?m.from(n.getAttrib(t,"target")):m.none(),a=ye(n,t,"rel"),s=ye(n,t,"class");return{url:r,text:o,title:ye(n,t,"title"),target:l,rel:a,linkClass:s}})(e,t);return{anchor:o,catalogs:{targets:ve(e),rels:fe(e,o.target),classes:he(e),anchor:de(e),link:n},optNode:m.from(t),flags:{titleEnabled:S(e)}}})),_e=e=>{const t=(e=>{const t=X(e);return xe(e,t)})(e);t.then((t=>{const n=((e,t)=>n=>{const o=n.getData();if(!o.url.value)return le(e),void n.close();const r=e=>m.from(o[e]).filter((n=>!k(t.anchor[e],n))),l={href:o.url.value,text:r("text"),target:r("target"),rel:r("rel"),class:r("linkClass"),title:r("title")},a={href:o.url.value,attach:void 0!==o.url.meta&&o.url.meta.attach?o.url.meta.attach:u};((e,t)=>f([ge,me(_(e),O(e))],(e=>e(t))).fold((()=>Promise.resolve(t)),(n=>new Promise((o=>{((e,t,n)=>{const o=e.selection.getRng();ue.setEditorTimeout(e,(()=>{e.windowManager.confirm(t,(t=>{e.selection.setRng(o),n(t)}))}))})(e,n.message,(e=>{o(e?n.preprocess(t):t)}))})))))(e,l).then((t=>{re(e,a,t)})),n.close()})(e,t);return((e,t,n)=>{const o=e.anchor.text.map((()=>({name:"text",type:"input",label:"Text to display"}))).toArray(),r=e.flags.titleEnabled?[{name:"title",type:"input",label:"Title"}]:[],l=((e,t)=>{const n=e.anchor,o=n.url.getOr("");return{url:{value:o,meta:{original:{value:o}}},text:n.text.getOr(""),title:n.title.getOr(""),anchor:o,link:o,rel:n.rel.getOr(""),target:n.target.or(t).getOr(""),linkClass:n.linkClass.getOr("")}})(e,m.from(C(n))),a=e.catalogs,s=ce(l,a);return{title:"Insert/Edit Link",size:"normal",body:{type:"panel",items:p([[{name:"url",type:"urlinput",filetype:"file",label:"URL"}],o,r,v([a.anchor.map(j("anchor","Anchors")),a.rels.map(j("rel","Rel")),a.targets.map(j("target","Open link in...")),a.link.map(j("link","Link list")),a.classes.map(j("linkClass","Class"))])])},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:l,onChange:(e,{name:t})=>{s.onChange(e.getData,{name:t}).each((t=>{e.setData(t)}))},onSubmit:t}})(t,n,e)})).then((t=>{e.windowManager.open(t)}))};var be=tinymce.util.Tools.resolve("tinymce.util.VK");const we=(e,t)=>e.dom.getParent(t,"a[href]"),Ce=e=>we(e,e.selection.getStart()),Oe=(e,t)=>{if(t){const n=W(t);if(/^#/.test(n)){const t=e.dom.select(n);t.length&&e.selection.scrollIntoView(t[0],!0)}else(e=>{const t=document.createElement("a");t.target="_blank",t.href=e,t.rel="noreferrer noopener";const n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),((e,t)=>{document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)})(t,n)})(t.href)}},Ne=e=>()=>{e.execCommand("mceLink",!1,{dialog:!0})},Ae=e=>()=>{Oe(e,Ce(e))},Te=(e,t)=>(e.on("NodeChange",t),()=>e.off("NodeChange",t)),Se=e=>t=>{const n=()=>t.setActive(!e.mode.isReadOnly()&&null!==X(e,e.selection.getNode()));return n(),Te(e,n)},Ee=e=>t=>{const n=()=>t.setEnabled(null!==X(e,e.selection.getNode()));return n(),Te(e,n)},Pe=e=>t=>{const n=t=>{return Z(t)||(n=e.selection.getRng(),H(n,G).length>0);var n},o=e.dom.getParents(e.selection.getStart());return t.setEnabled(n(o)),Te(e,(e=>t.setEnabled(n(e.parents))))};e.add("link",(e=>{(e=>{const t=e.options.register;t("link_assume_external_targets",{processor:e=>{const t=o(e)||s(e);return t?!0===e?{value:1,valid:t}:"http"===e||"https"===e?{value:e,valid:t}:{value:0,valid:t}:{valid:!1,message:"Must be a string or a boolean."}},default:!1}),t("link_context_toolbar",{processor:"boolean",default:!1}),t("link_list",{processor:e=>o(e)||i(e)||c(e,r)}),t("link_default_target",{processor:"string"}),t("link_default_protocol",{processor:"string",default:"https"}),t("link_target_list",{processor:e=>s(e)||c(e,r),default:!0}),t("link_rel_list",{processor:"object[]",default:[]}),t("link_class_list",{processor:"object[]",default:[]}),t("link_title",{processor:"boolean",default:!0}),t("allow_unsafe_link_target",{processor:"boolean",default:!1}),t("link_quicklink",{processor:"boolean",default:!1})})(e),(e=>{e.ui.registry.addToggleButton("link",{icon:"link",tooltip:"Insert/edit link",onAction:Ne(e),onSetup:Se(e)}),e.ui.registry.addButton("openlink",{icon:"new-tab",tooltip:"Open link",onAction:Ae(e),onSetup:Ee(e)}),e.ui.registry.addButton("unlink",{icon:"unlink",tooltip:"Remove link",onAction:()=>le(e),onSetup:Pe(e)})})(e),(e=>{e.ui.registry.addMenuItem("openlink",{text:"Open link",icon:"new-tab",onAction:Ae(e),onSetup:Ee(e)}),e.ui.registry.addMenuItem("link",{icon:"link",text:"Link...",shortcut:"Meta+K",onAction:Ne(e)}),e.ui.registry.addMenuItem("unlink",{icon:"unlink",text:"Remove link",onAction:()=>le(e),onSetup:Pe(e)})})(e),(e=>{e.ui.registry.addContextMenu("link",{update:t=>Z(e.dom.getParents(t,"a"))?"link unlink openlink":"link"})})(e),(e=>{const t=t=>{const n=e.selection.getNode();return t.setEnabled(null!==X(e,n)),u};e.ui.registry.addContextForm("quicklink",{launch:{type:"contextformtogglebutton",icon:"link",tooltip:"Link",onSetup:Se(e)},label:"Link",predicate:t=>!!X(e,t)&&b(e),initValue:()=>{const t=X(e);return t?W(t):""},commands:[{type:"contextformtogglebutton",icon:"link",tooltip:"Link",primary:!0,onSetup:t=>{const n=e.selection.getNode();return t.setActive(!!X(e,n)),Se(e)(t)},onAction:t=>{const n=t.getValue(),o=(t=>{const n=X(e),o=ee(e);if(!n&&o){const o=Y(e.selection,n);return m.some(o.length>0?o:t)}return m.none()})(n);re(e,{href:n,attach:u},{href:n,text:o,title:m.none(),rel:m.none(),target:m.none(),class:m.none()}),(e=>{e.selection.collapse(!1)})(e),t.hide()}},{type:"contextformbutton",icon:"unlink",tooltip:"Remove link",onSetup:t,onAction:t=>{le(e),t.hide()}},{type:"contextformbutton",icon:"new-tab",tooltip:"Open link",onSetup:t,onAction:t=>{Ae(e)(),t.hide()}}]})})(e),(e=>{e.on("click",(t=>{const n=we(e,t.target);n&&be.metaKeyPressed(t)&&(t.preventDefault(),Oe(e,n))})),e.on("keydown",(t=>{if(!t.isDefaultPrevented()&&13===t.keyCode&&(e=>!0===e.altKey&&!1===e.shiftKey&&!1===e.ctrlKey&&!1===e.metaKey)(t)){const n=Ce(e);n&&(t.preventDefault(),Oe(e,n))}}))})(e),(e=>{e.addCommand("mceLink",((t,n)=>{!0!==(null==n?void 0:n.dialog)&&P(e)?e.dispatch("contexttoolbar-show",{toolbarKey:"quicklink"}):_e(e)}))})(e),(e=>{e.addShortcut("Meta+K","",(()=>{e.execCommand("mceLink")}))})(e)}))}();