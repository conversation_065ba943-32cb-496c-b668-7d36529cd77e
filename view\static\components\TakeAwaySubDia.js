Vue.component("TakeAwaySubDia", {
  props: ["systemLanguage", "storePhotoUrl"],
  data() {
    return {
      dialogVisible: false,
      attachVal: "#app",
      sendParams: null,
      storeData: {},
      distanceAlert: null,
      differenceDistance: null
    }
  },
  created() {
    let {
      storeData,
      distanceAlert,
      storeAddress = {},
      locationCoordinates = "",
      storeName = {}
    } = this.$parent.openTable
    // openTable.storeData 则在地图模式下存入,customStoreData则在普通模式下存入
    let customStoreData = {
      storeAddress,
      locationCoordinates,
      storeName
    }
    this.storeData = storeData || customStoreData
    this.distanceAlert = distanceAlert || Infinity
    console.log(
      "🚀 ~ file: TakeAwaySubDia.js:19 ~ mounted ~ this.systemLanguage:",
      this.systemLanguage
    )
    this.initGoogleMapApi()
  },
  mounted() {},
  computed: {
    getStoreName() {
      let { language, storeNumber } = this.$parent.openTable
      let { storeName } = this.storeData
      // 判断storeName是否为空对象
      let val = Object.keys(storeName).length === 0 ? storeNumber : storeName[language]
      return val
    },
    getStoreAddress() {
      let { language } = this.$parent.openTable
      let { storeAddress } = this.storeData
      // 判断storeAddress是否为空对象
      let val = Object.keys(storeAddress).length === 0 ? "" : storeAddress[language]
      return val
    },
    getDistancePrompt() {
      let { takeawaySubDiaDiffDistancePrompt } = this.systemLanguage
      //  替换takeawaySubDiaDiffDistancePrompt中的 #diffDistance 字段为差距距离
      let differenceDistance = this.differenceDistance
      let val = takeawaySubDiaDiffDistancePrompt.replace("#diffDistance", differenceDistance)
      return val
    }
  },
  methods: {
    // 初始化谷歌地图api
    initGoogleMapApi() {
      let { language } = this.$parent.openTable
      let Lan = language === "zh" ? "zh-HK" : "en-US"
      const key = "AIzaSyDAjO_Ik2VVK2eypmKEKPBEx4KGOslYVj0"
      const worker = new Worker("../static/js/map/map.worker.js")
      window.initMap = () => {}
      worker.postMessage({
        key,
        lan: Lan
      })
      worker.onmessage = e => {
        let { code, res } = e.data
        if (code === "ok") {
          let script = document.createElement("script")
          script.textContent = res
          script.defer = true
          script.async = true
          document.body.appendChild(script)
          this.getDistance()
        } else {
          console.log("谷歌地图api加载失败")
        }
      }
    },
    showDialog(attachVal, params = null) {
      this.attachVal = attachVal ? false : "#app" //挂载的元素会丢失样式
      this.sendParams = params
      this.dialogVisible = true
    },
    onEditStore() {
      this.dialogVisible = false
      this.$parent.backMap()
    },
    onSubPayment() {
      this.dialogVisible = false
      this.$parent.sendOrder()
    },
    //获取店铺与当前定位两点之间的距离
    getDistance() {
      let { locationCoordinates } = this.$parent.openTable
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          position => {
            let { latitude: myLatitude, longitude: myLongitude } = position.coords // 获取当前定位
            if (locationCoordinates) {
              let [storeLatitude, storeLongitude] = locationCoordinates.split(",") //根据,分割经纬度,并转换为数字lat,lng
              let p1 = new google.maps.LatLng(storeLatitude, storeLongitude)
              let p2 = new google.maps.LatLng(myLatitude, myLongitude)
              let distance = google.maps.geometry.spherical.computeDistanceBetween(p1, p2)
              console.log("distance", distance)
              this.differenceDistance = Math.round(distance)
            }
          },
          () => {
            console.log("位置信息不可用")
          }
        )
      } else {
        this.differenceDistance = null
        console.log("浏览器不支持地理定位")
      }
    }
  },
  template: `
  <div class='takeAwayMode-subDia-warp'>
    <v-overlay :value="attachVal!==false&&dialogVisible"style="zIndex:665;"></v-overlay>
    <v-dialog v-model="dialogVisible" persistent content-class="takeAwayMode-subDia" :attach="attachVal" style="    zIndex:666;" >
    <v-card>
      <div class="v-text-center takeAwayMode-subDia-cardTitle">
        {{systemLanguage.takeawaySubDiaTitle}}
      </div>
      <div class="takeAwayMode-subDia-cardText">
        <v-img
          height="250"
          :src="storePhotoUrl"
        ></v-img>
        <div class="takeAwayMode-subDia-cardText-store">
          <div class="content-left">
            <div class="content-storeName">{{getStoreName}}</div>
            <div class="content-address" v-if='getStoreAddress'>{{getStoreAddress}}</div>
            <div v-if='differenceDistance' class="content-distance" :class="{'warningDistance':differenceDistance>distanceAlert}">
              {{getDistancePrompt}}
            </div>
          </div>
          <div class="content-right" @click='onEditStore' v-if='$parent.openTable.storeData'>
            <img src="../static/img/svg/takeawaySubDiaEditIcon.svg"  class="takeAwayMode-subDia-svg" />
            <span>{{systemLanguage.btnTxtForEdit}}</span>
          </div>
        </div>
        <div class="takeAwayMode-subDia-cardText-tip" v-if='differenceDistance&&differenceDistance>distanceAlert'>
          <img src="../static/img/svg/takeawaySubDiaWarningIcon.svg" class="takeAwayMode-subDia-svg" />
          <div style="overflowWrap: break-word;">{{systemLanguage.takeawaySubDiaWarnDistancePrompt}}</div>
        </div>
      </div>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="error darken-1" text @click="dialogVisible = false">
        {{systemLanguage.btnTxtForCancel}}
        </v-btn>
        <v-btn color="primary darken-1" text @click="onSubPayment">{{systemLanguage.takeawaySubDiaConfirmBtn}}</v-btn>
      </v-card-actions>
    </v-card>
    </v-dialog>
  </div>
  `
})
