<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- <title>牛一</title> -->
    <title></title>

    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <!-- rem布局 -->
    <script src="../static/js/page/lib-flexible.js"></script>
    <!-- 懒加载 -->
    <link rel="stylesheet" type="text/css" href="../static/css/page/historicalPage.css" />
    <script src="../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../static/vue/vue.min.js"></script>
    <!-- layer彈出框
    <link rel="stylesheet" href="../tools/layer_3/mobile/need/layer.css" />
    <script type="text/javascript" src="../tools/layer_3/layer.js"></script> -->
    <!-- vconsole 真机测试 -->
    <!-- <script src="https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js"></script> -->
    <!-- I18n国际化 -->
    <script src="../static/I18n/initI18n.js"></script>
    <!-- 精度缺失等数学计算 -->
    <script src="../static/js/page/math.js"></script>
    <!-- 引入封装js -->
    <script src="../static/byod_webUtils/public.js"></script>
    <!-- 引入 layui.css/layui.js -->
    <link href="../static/layui/css/layui.css" rel="stylesheet" type="text/css" />
    <script src="../static/layui/layui.js"></script>
  </head>

  <body>
    <div id="app">
      <!-- loading蒙层 -->
      <template>
        <div class="loader loader-3" v-show="loading">
          <div class="dot dot1"></div>
          <div class="dot dot2"></div>
          <div class="dot dot3"></div>
        </div>
      </template>
      <template>
        <div class="flexBox">
          <div class="hitspopUp_warp">
            <div class="header">
              <div class="title">{{systemLanguage.orderHistoryTitle}}</div>
              <img
                src="../static/img/page/black.jpg"
                @click="GoToHomePage"
                alt=""
                class="blackIcon"
              />
              <img
                src="../static/img/newImage/home.jpg"
                @click="goIndex"
                class="homeIcon"
                v-if="isShowGoHomeBtn"
              />
            </div>
          </div>
          <!-- 员工模式历史记录内容(有内容type==3) -->
          <div class="hits_food" v-if="!Ishistorical&&performType==3">
            <div v-if="historicalArry.length==0" class="cart_food_null">
              {{systemLanguage.nullhisfoodText}}
            </div>
            <div class="cart_food_cell" v-for="(itemWarp, i) in historicalArry" :key="i">
              <!-- 时间段 -->
              <div class="placeOrderTime">
                <i></i>
                <p>{{itemWarp.createdTime}}</p>
              </div>
              <!-- 历史订单信息 -->
              <div class="cart_info" v-for="(inner,index) in itemWarp.res" :key="index">
                <div class="cart_food_title">
                  <template v-if="openTable.language == 'en'">{{ inner.desc1 }}</template>
                  <template v-else-if="openTable.language == 'zh'">{{ inner.desc2 }}</template>
                  <template v-else>{{ inner.multi1 }}</template>
                </div>
                <!-- 细项 -->
                <div class="littleitem" v-if="inner.itemList&&inner.itemList.length != 0 ">
                  {{ showlogic( inner )}}
                </div>
                <div class="cart_food_priceNum">
                  <div class="cart_food_price">
                    <div v-if="calculatedTotal(inner)!=0">
                      {{currencyWay}}
                      <span class="cart_food_price_amount">{{ calculatedTotal(inner) }}</span>
                    </div>
                  </div>
                  <div class="cart_food_numBox">x{{inner.qty}}</div>
                </div>
              </div>
            </div>
            <div class="footnote" v-if="!loading&&!Ishistorical&&historicalArry.length!=0">
              {{systemLanguage.footNoteLan}}
            </div>
          </div>
          <!-- VYOD历史记录内容(有内容type==1) -->
          <div class="hits_food" v-else-if="!Ishistorical&&performType==1">
            <div class="cart_food_cell" v-for="(itemWarp, i) in historicalArry" :key="i">
              <!-- 时间段 -->
              <div class="placeOrderTime">
                <i></i>
                <p>{{itemWarp.createTime}}</p>
              </div>
              <!-- 历史订单信息 -->
              <div class="cart_info" v-for="(item, index) in itemWarp.res" :key="index">
                <div :class="[{'cancelOrder_title':item.qty1==0},'cart_food_title']">
                  <!-- <div class="dot_box"></div> -->
                  {{inListTitle(item)}}
                </div>
                <!-- 细项 -->
                <div class="littleitem" v-html="showlogicOne(item)">{{ showlogicOne(item)}}</div>
                <div class="cart_food_priceNum">
                  <div class="cart_food_price">
                    <div v-if="calculatedTotalOne(item)!=0">
                      <span class="cart_food_price_amount">{{ showFoodPrice(item) }}</span>
                    </div>
                  </div>
                  <div class="cart_food_numBox">x{{item.qty1}}</div>
                </div>
              </div>
            </div>
            <div class="footnote" v-if="!loading&&!Ishistorical&&historicalArry.length!=0">
              {{systemLanguage.footNoteLan}}
            </div>
          </div>
          <!-- 历史记录内容(无内容) -->
          <div class="null_hits_food" v-show="!loading&&historicalArry.length==0">
            <img src="../static/img/newImage/empty-default.jpg" alt="" class="emptyIcon" />
            {{systemLanguage.nullOrderLan}}
          </div>
          <!-- 底部总金额 -->
          <div class="footBox" v-if="!loading&&!Ishistorical&&!isShowNoData">
            <div class="price_warp" v-if="allPrice!=0">
              <div class="totalCharge" v-if="openTable.serviceCharges">
                <span>{{systemLanguage.totalChargeTxt}}</span>
                <span class="allPrice_num">{{showAllPrice(totalCharge)}}</span>
              </div>
              <div class="totalPrices">
                <span>{{systemLanguage.allPriceLan}}</span>
                <span class="allPrice_num">{{showAllPrice(allPrice)}}</span>
              </div>
            </div>
            <div class="endTime">
              <span>{{systemLanguage.orderTimeLan}}</span>
              {{endCreateTime}}
            </div>
          </div>
        </div>
      </template>
    </div>
    <script src="../static/js/index/utils.js"></script>
    <script src="../static/js/index/device.js"></script>

    <script>
      //dom加载完成后自定义title
      window.addEventListener("DOMContentLoaded", () => {
        $.getJSON("../static/utils/config.json", function (data) {
          document.title = data.BYOD.DocumentTitle || ""
        })
      })
      var app = new Vue({
        el: "#app",
        data: {
          currencyWay: "",
          defaultWay: "$",
          ishisPopup: false,
          loading: false,
          historicalArry: [],
          openTable: null,
          systemLanguage: {},
          baseUrl: null,
          allPrice: 0,
          Ishistorical: false,
          endCreateTime: null,
          isShowNoData: false,
          ppNumber: [
            {
              money: 32
            },
            {
              money: 34.72
            },
            {
              money: 8.3
            },
            {
              money: 37.55
            },
            {
              money: 21.51
            },
            {
              money: 6.42
            }
          ],
          performType: null,
          totalCharge: 0
        },

        created() {
          this.initializeThe()
          this.setCurrencyWay()
          this.getHistorical() //历史记录
          this.fixLan()
        },
        mounted() {
          $("html").css({ "--styleColor": this.openTable.color })
        },
        computed: {
          isShowGoHomeBtn() {
            let { initialTableNum } = this.openTable //自定义堂食/外卖模式
            let specialMode = ["AssistMode", "StaffMode", "EnhAssistMode"].includes(initialTableNum)
            return specialMode
          }
        },
        methods: {
          setCurrencyWay() {
            const way = this.openTable.currencyWay
            if (way) {
              this.currencyWay = way
            } else {
              this.currencyWay = this.defaultWay
            }
          },
          initializeThe() {
            this.openTable = JSON.parse(sessionStorage.getItem("openTable"))
            console.log(this.openTable)
            let domain = location.host.split(".")[0]
          },
          // 获取历史记录
          getHistorical() {
            this.loading = true
            let { companyName, storeNumber, tableNumber, tableKey, performType } = this.openTable
            // let companyName = "aipha"
            // storeNumber = "HK006"
            // tableNumber = "1A"
            // let companyName = "malaydemo"
            // storeNumber = '31'
            // tableNumber = "SZ139"
            $.get({
              url: "../store/loadTableOrder",
              data: {
                companyName,
                tableNumber,
                storeNumber,
                performType,
                tableKey
              },
              success: res => {
                var resultObj = JSON.parse(res)
                if (resultObj.statusCode !== 200) {
                  this.requestErrorTip(resultObj.statusCode)
                } else {
                  console.log(resultObj, "resultObj")
                  let resData
                  this.performType = performType
                  // let performType = this.performType
                  if (performType == 1) {
                    if (
                      (resultObj.oldOrderItem && resultObj.oldOrderItem.length == 0) ||
                      resultObj.errorCode
                    ) {
                      this.isShowNoData = true
                      this.loading = false
                      return
                    } else {
                      resData = resultObj.oldOrderItem
                    }
                  } else if (performType == 3) {
                    if (
                      (resultObj.ctiTempList && resultObj.ctiTempList.length == 0) ||
                      resultObj.errorCode
                    ) {
                      this.isShowNoData = true
                      this.loading = false
                      return
                    } else {
                      resData = resultObj.ctiTempList
                    }
                  }
                  // console.log(resData, 'resData')
                  this.executeTypeFun(performType, resData)
                }
              },
              error: () => {
                console.log("error")
              },
              complete: () => {
                this.loading = false
              }
            })
          },

          executeTypeFun(type, resData) {
            if (type == 1) {
              // 目前byod
              this.mapLoctionOne(resData)
              let allPrice = 0
              let effectiveExpenses = 0 //有效费用去除sc_able不为true的item金额
              this.historicalArry.forEach(item => {
                item.res.forEach(i => {
                  let itemMoney = this.calculatedTotalOne(i)
                  // 精度缺失外部引入js
                  allPrice = floatAdd(allPrice, itemMoney)
                  if (i.sc_able === "true") {
                    effectiveExpenses = floatAdd(effectiveExpenses, itemMoney)
                  }
                })
              })
              // 计算服务费
              let totalCharge = this.getServiceCharge(this.historicalArry, effectiveExpenses)
              this.allPrice = this.getAllPrice(floatAdd(totalCharge, allPrice), allPrice)
              console.log(
                totalCharge,
                allPrice,
                "服务费+总价;包含优惠费用",
                effectiveExpenses,
                "有效费用"
              )
              console.log(this.historicalArry, "historicalArry")
              this.endCreateTime = this.historicalArry[this.historicalArry.length - 1].createTime
            } else if (type == 3) {
              this.mergeItem(resData)
              let allPrice = 0
              this.historicalArry.forEach(item => {
                item.res.forEach(i => {
                  let itemMoney = this.calculatedTotal(i)
                  // 精度缺失外部引入js
                  allPrice = floatAdd(allPrice, itemMoney)
                })
              })
              this.allPrice = retainSignificantDecimals(allPrice)
            }
          },
          mergeItem(data) {
            // 把源数据先变成目标数据的规则
            let tempArr = []
            let newArry = []
            for (let i = 0; i < data.length; i++) {
              if (tempArr.indexOf(data[i].date + "-" + data[i].time) === -1) {
                newArry.push({
                  createdTime: data[i].date + "-" + data[i].time,
                  res: [data[i]]
                })
                tempArr.push(data[i].date + "-" + data[i].time)
              } else {
                for (let j = 0; j < newArry.length; j++) {
                  if (newArry[j].createdTime == data[i].date + "-" + data[i].time) {
                    newArry[j].res.push(data[i])
                    break
                  }
                }
              }
            }

            let endCreatedTime = newArry[newArry.length - 1].createdTime.split("-")
            this.endCreateTime = endCreatedTime[0] + " " + endCreatedTime[1]
            this.historicalArry = newArry
            console.log(newArry, "新数据")
          },

          fixLan() {
            // let language = sessionStorage.getItem(language) || 'zh';
            let { language } = this.openTable
            this.systemLanguage = window.i18n[language]
          },
          GoToHomePage() {
            window.location.href = "../order/menuPage" //encodeURIComponent:参数编码
          },
          // opentable解析
          getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i")
            var r = decodeURI(window.location.search).substr(1).match(reg)
            if (r != null) {
              return unescape(r[2])
            }
            return null
          },
          // 购物车细项文字逻辑
          showlogic(foodItem) {
            let xiItem = foodItem.itemList //细项
            let arryText = []
            if (xiItem && xiItem.length != 0) {
              xiItem.forEach(item => {
                let price =
                  item.unitPrice && item.unitPrice != 0
                    ? `(${this.currencyWay}${retainSignificantDecimals(item.unitPrice)})`
                    : ""

                if (this.openTable.language == "en") {
                  arryText.push(item.desc1 + this.cartMyxiText(item, "en") + price)
                } else if (this.openTable.language == "zh") {
                  arryText.push(item.desc2 + this.cartMyxiText(item, "zh") + price)
                } else {
                  arryText.push(item.multi1 + this.cartMyxiText(item, "thirdLan") + price)
                }
              })
            }

            let str = ""
            arryText.forEach((item, i) => {
              str += arryText[i] + "" + "+" + ""
            })
            if (str.length > 0) {
              str = str.substr(0, str.length - 1)
            }
            return str
          },
          cartMyxiText(item, type) {
            let myxiTxt = []
            let myxiItem = item.itemList
            if (myxiItem && myxiItem.length != 0) {
              console.log("计入")
              myxiItem.forEach(i => {
                if (type == "en") {
                  myxiTxt.push(i.desc1)
                } else if (type == "zh") {
                  myxiTxt.push(i.desc2)
                } else {
                  myxiTxt.push(i.multi1)
                }
              })
            }

            let str = ""
            myxiTxt.forEach((item, i) => {
              str += myxiTxt[i] + "" + "+" + ""
            })
            if (str.length > 0) {
              str = str.substr(0, str.length - 1)
              return `[${str}]`
            } else {
              return ""
            }
          },
          // 计算总价格（包含细项）
          //tip:Boolean 用于区分用于模板显示(true)还是函数逻辑处理
          calculatedTotal(item, tip) {
            let basePrice = item.unitPrice || 0
            let arry = []

            let xiItem = item.itemList //细项
            if (xiItem && xiItem.length != 0) {
              xiItem.forEach(e => {
                if (e.unitPrice && e.unitPrice != 0) {
                  basePrice = floatAdd(basePrice, e.unitPrice)
                }
                let myXiItem = e.itemList
                if (myXiItem && myXiItem.length != 0) {
                  myXiItem.forEach(i => {
                    if (i.unitPrice && i.unitPrice != 0) {
                      // basePrice += i.unitPrice
                      basePrice = floatAdd(basePrice, i.unitPrice)
                    }
                  })
                }
              })
            }
            // this.allPrice += basePrice * item.qty
            return retainSignificantDecimals(basePrice * item.qty)
          },
          calculatedTotalOne(item) {
            // 316
            // let basePrice = item.upa1 || 0;
            let baseFoodPrice = floatMultiply(item.upa1 || 0, item.qty1)
            let arry = [],
              baseXiPrice = 0
            let historyNewFoodList = item.newOrderItemFoodList
            let historyNewMList = item.newOrderItemMListList
            if (historyNewFoodList && historyNewFoodList.length != 0) {
              historyNewFoodList.forEach(e => {
                let myXiarry = this.myXiPriceArry(e)
                if (myXiarry.length != 0) arry.push(...myXiarry)
                arry.push(e)
              })
            }
            if (historyNewMList && historyNewMList.length != 0) {
              historyNewMList.forEach(e => {
                let myXiarry = this.myXiPriceArry(e)
                if (myXiarry.length != 0) arry.push(...myXiarry)
                arry.push(e)
              })
            }
            // arry.forEach((priceItem) => {
            //   if (priceItem.upa1 && priceItem.upa1 != 0) {
            //     basePrice += priceItem.upa1;
            //   } else if (priceItem.price && priceItem.price != 0) {
            //     basePrice += priceItem.price;
            //   }
            // });
            // return basePrice * item.qty1;
            // 兼容pos下单金钱计算
            arry.forEach(priceItem => {
              let qty1 = priceItem.qty1 || 1
              let price = 0
              if (priceItem.upa1 && priceItem.upa1 != 0) {
                price = priceItem.upa1
              } else if (priceItem.price && priceItem.price != 0) {
                price = priceItem.price
              }
              baseXiPrice = floatAdd(baseXiPrice, floatMultiply(price, qty1))
            })
            return floatAdd(baseFoodPrice, baseXiPrice)
          },
          myXiPriceArry(e) {
            let arry = []
            if (e.newOrderItemFoodList && e.newOrderItemFoodList.length != 0) {
              let res = this.myXiPriceArry(e.newOrderItemFoodList)
              arry.push(...e.newOrderItemFoodList, ...res)
            }
            if (e.newOrderItemMListList && e.newOrderItemMListList.length != 0) {
              let res = this.myXiPriceArry(e.newOrderItemMListList)
              arry.push(...e.newOrderItemMListList, ...res)
            }
            return arry
          },
          mapLoction(arr) {
            var newArr = []
            arr.forEach(function (oldData, i) {
              var index = -1
              var createTime = oldData.createTime.substring(0, 10)
              var alreadyExists = newArr.some(function (newData, j) {
                if (oldData.createTime.substring(0, 10) === newData.createTime.substring(0, 10)) {
                  index = j
                  return true
                }
              })
              if (!alreadyExists) {
                newArr.push({
                  createTime: oldData.createTime,
                  res: [
                    {
                      qty1: oldData.qty1,
                      createTime: oldData.createTime,
                      desc1: oldData.desc1,
                      desc2: oldData.desc2,
                      fCode: oldData.fCode,
                      upa1: oldData.upa1
                    }
                  ]
                })
              } else {
                newArr[index].res.push({
                  qty1: oldData.qty1,
                  createTime: oldData.createTime,
                  desc1: oldData.desc1,
                  desc2: oldData.desc2,
                  fCode: oldData.fCode,
                  upa1: oldData.upa1
                })
              }
            })
            // console.log(newArr)
            this.historicalArry = newArr
            // return newArr;
          },

          // 购物车细项文字逻辑
          showlogicOne(item) {
            let newOrderItemFoodList = item.newOrderItemFoodList || []
            // let newOrderItemFoodTypeList = item.newOrderItemFoodTypeList || [];
            let newOrderItemMListList = item.newOrderItemMListList || []
            // let newOrderItemMTypeList = item.newOrderItemMTypeList || [];
            // let newOrderItemSetMealList = item.newOrderItemSetMealList || [];
            let arry = []

            if (newOrderItemFoodList.length != 0) {
              newOrderItemFoodList.forEach(e => {
                arry.push(e)
                if (e.newOrderItemFoodList && e.newOrderItemFoodList.length != 0) {
                  e.newOrderItemFoodList.forEach(i => {
                    arry.push(i)
                  })
                }
                if (e.newOrderItemMListList && e.newOrderItemMListList.length != 0) {
                  e.newOrderItemMListList.forEach(i => {
                    arry.push(i)
                  })
                }
              })
            }
            if (newOrderItemMListList.length != 0) {
              newOrderItemMListList.forEach(e => {
                arry.push(e)
                if (e.newOrderItemFoodList && e.newOrderItemFoodList.length != 0) {
                  e.newOrderItemFoodList.forEach(i => {
                    arry.push(i)
                  })
                }
                if (e.newOrderItemMListList && e.newOrderItemMListList.length != 0) {
                  e.newOrderItemMListList.forEach(i => {
                    arry.push(i)
                  })
                }
              })
            }
            // if (newOrderItemMTypeList.length != 0) {
            //   console.log(newOrderItemMTypeList, "newOrderItemFoodTypeList");
            //   newOrderItemMTypeList.forEach((e) => {
            //     e.newOrderItemMListList.forEach((i) => {
            //       arry.push(i);
            //     });
            //   });
            // }

            let arryText = []
            arry.forEach(item => {
              let itemMoney = item.upa1 || item.price
              if (itemMoney) {
                itemMoney = this.showXiPrice(itemMoney)
                arryText.push(`${this.inListTitle(item)}(${itemMoney})`)
              } else {
                arryText.push(this.inListTitle(item))
              }
            })
            let str = ""
            arryText.forEach((item, i) => {
              str += `<p>${arryText[i]}</p>`
              // str += arryText[i] + '' + '+' + ''
            })
            if (str.length > 0) {
              str = str.substr(0, str.length - 1)
            }
            return str
          },
          mapLoctionOne(arr) {
            var newArr = []
            arr.forEach(function (oldData, i) {
              var index = -1
              var createTime = oldData.createTime.substring(0, 10)
              var alreadyExists = newArr.some(function (newData, j) {
                if (oldData.createTime.substring(0, 10) === newData.createTime.substring(0, 10)) {
                  index = j
                  return true
                }
              })
              if (!alreadyExists) {
                newArr.push({
                  createTime: oldData.createTime,
                  res: [{ ...oldData }]
                })
              } else {
                newArr[index].res.push({ ...oldData })
              }
            })
            // console.log(newArr)
            newArr.sort(function (a, b) {
              return a.createTime > b.createTime ? 1 : -1
            })
            this.historicalArry = newArr
            // return newArr;
          },
          // 封装显示细项tile
          inListTitle(item) {
            let { language } = this.openTable
            let xiTitle = ""
            // foodlist:desc1,desc2,multi1
            // mListList:name,name2,multi1
            let isFoodList = item.hasOwnProperty("desc1") || item.hasOwnProperty("desc2")
            const getTitle = (foodListTitle, mlistTitle) => {
              return isFoodList ? foodListTitle : mlistTitle
            }
            if (language === "en") {
              xiTitle = getTitle(item.nameA || item.desc1, item.nameA || item.name)
            } else if (language === "zh") {
              xiTitle = getTitle(item.nameB || item.desc2, item.nameB || item.name2)
            } else {
              xiTitle = item.multi1 || getTitle(item.nameA || item.desc1, item.nameA || item.name)
            }
            return xiTitle
          },

          // 细项价钱显示
          showXiPrice(price) {
            if (price > 0) {
              return `+${this.currencyWay}${retainSignificantDecimals(price)}`
            } else {
              let absPrice = Math.abs(price)
              return `-${this.currencyWay}${retainSignificantDecimals(absPrice)}`
            }
          },
          showFoodPrice(item) {
            let oneFoodPrice = this.calculatedTotalOne(item)
            if (oneFoodPrice > 0) {
              return `${this.currencyWay}${retainSignificantDecimals(oneFoodPrice)}`
            } else {
              let absPrice = Math.abs(oneFoodPrice)
              return `-${this.currencyWay}${retainSignificantDecimals(absPrice)}`
            }
          },
          showAllPrice(allPrice) {
            const price = Math.abs(allPrice)
            const sign = allPrice < 0 ? "-" : ""
            return `${sign}${this.currencyWay}${retainSignificantDecimals(price)}`
          },
          /*
        openType:
        按总价计算  Enable (per bill)
        按单个食品计算  Enable (per item)
        countType:
        保留原数  No change
        向上 Ceiling (Up)
        向下 Floor (Down)
        四舍五入 Rounding
      */
          getServiceCharge(cartList, allPrice) {
            let { serviceCharges } = this.openTable
            // 判断计算服务费是否开启
            if (serviceCharges) {
              let totalCharge = 0 //总服务费
              if (serviceCharges.openType == "perItem") {
                //perItem逻辑暂不支持,暂未开发逻辑
                // cartList.forEach((item) => {
                //   if (item.sc_able == 'true') {
                //     let totalPrices = this.calculatedTotal(item)
                //     let itemCharge = floatMultiply(totalPrices, 0.1)
                //     totalCharge = floatAdd(totalCharge, itemCharge)
                //   }
                // })
              } else {
                // 计算总价10%服务费
                totalCharge = retainSignificantDecimals(floatMultiply(allPrice, 0.1))
              }
              // 计算总价保留数据格式
              this.totalCharge = totalCharge
              return totalCharge
            } else {
              return 0
            }
          },
          // 计算总价(包含服务费/包含不计入服务费的food价格)
          getAllPrice(priceCharge, allPrice) {
            let { serviceCharges, keepDecimals } = this.openTable
            // priceCharge为服务费+总价
            const computePrice = (num, direction, decimalPlaces, shouldPad) => {
              const factor = Math.pow(10, decimalPlaces)
              let result
              switch (direction) {
                // 向上
                case "ceiling":
                  result = Math.ceil(num * factor) / factor
                  break
                // 向下
                case "floor":
                  result = Math.floor(num * factor) / factor
                  break
                // 四拾伍入
                case "rounding":
                  result = Math.round(num * factor) / factor
                  break
                default:
                  result = num //'noChanage' 不改变价格
              }
              return shouldPad ? Number(result.toFixed(decimalPlaces)) : result
            }
            const calculateServiceCharge = (serviceCharges, keepDecimals) => {
              return computePrice(
                priceCharge,
                serviceCharges.countType,
                keepDecimals ? keepDecimals.significantDigits : 0,
                keepDecimals ? keepDecimals.zeroPadding : false
              )
            }

            if (serviceCharges && keepDecimals) {
              if (serviceCharges.openType == "perItem") {
                //perItem逻辑暂不支持,以下先进行保留小数处理(后期需求再进行修改)
                return retainSignificantDecimals(allPrice)
              } else {
                return calculateServiceCharge(serviceCharges, keepDecimals)
              }
            } else if (keepDecimals) {
              return retainSignificantDecimals(allPrice)
            } else if (serviceCharges) {
              return calculateServiceCharge(serviceCharges, null)
            } else {
              return allPrice
            }
          },
          requestErrorTip(code) {
            let { errorMsg, errorTableKey, staticQRCodeNoOpenTableError } = this.systemLanguage
            let { tableNumber } = this.openTable
            let useTip = ""
            switch (code) {
              case 5003:
                useTip = errorTableKey //tableKey不正确
                break
              case 5005:
                //staticQRCodeNoOpenTableError字符串里面替换#tableNumber为当前桌号
                useTip = staticQRCodeNoOpenTableError.replace("#tableNumber", tableNumber) //未开台
                break
              default: //tableKey不正确
                useTip = errorMsg
                break
            }
            layer.msg(useTip, { time: 2000, shade: [0.01, "#fff"] }) //提示层
          }
        }
      })
    </script>
  </body>
</html>
