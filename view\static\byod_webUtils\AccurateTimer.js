class AccurateTimer {
  constructor(callback, delay) {
    if (typeof callback !== "function") {
      throw new Error("Callback must be a function.")
    }
    if (typeof delay !== "number" || delay <= 0) {
      throw new Error("Delay must be a positive number.")
    }

    this.callback = callback
    this.delay = delay // 期望的延迟时间 (毫秒)
    this.timerId = null
    this.worker = null
    this.startTime = 0 // 记录每次计时开始的时间
    this.remaining = delay // 剩余时间
    this.isRunning = false

    this._setupVisibilityChangeHandler()
  }

  start() {
    if (this.isRunning) {
      console.warn("Timer is already running.")
      return
    }

    this.isRunning = true
    this.startTime = performance.now() // 记录开始时间
    this._scheduleNextTick()
  }

  stop() {
    if (!this.isRunning) {
      return
    }

    this.isRunning = false
    if (this.timerId) {
      clearTimeout(this.timerId)
      this.timerId = null
    }
    if (this.worker) {
      this.worker.terminate() // 终止 Web Worker
      this.worker = null
    }
    this.remaining = this.delay // 重置剩余时间
  }

  /**
   * 内部方法：根据页面可见性调度下一次执行
   * @private
   */
  _scheduleNextTick() {
    if (!this.isRunning) {
      return
    }

    if (document.hidden) {
      // 页面隐藏时，使用 Web Worker
      this._startWorkerTimer()
    } else {
      // 页面可见时，使用 setTimeout 进行精确调度和校准
      this._startMainThreadTimer()
    }
  }

  /**
   * 内部方法：在主线程使用 setTimeout
   * @private
   */
  _startMainThreadTimer() {
    if (this.worker) {
      this.worker.terminate() // 终止 Web Worker
      this.worker = null
    }

    const elapsed = performance.now() - this.startTime
    this.remaining = Math.max(0, this.delay - elapsed) // 修正剩余时间

    this.timerId = setTimeout(() => {
      this._executeCallbackAndReschedule()
    }, this.remaining)
  }

  /**
   * 内部方法：使用 Web Worker 进行计时
   * @private
   */
  _startWorkerTimer() {
    // 如果已经有 Worker，不需要重复创建
    if (this.worker) {
      return
    }

    // 创建 Web Worker 代码字符串
    const workerCode = `
            let timerId = null;
            let expected = 0;
            let delay = 0;

            self.onmessage = function(e) {
                const { command, initialDelay, lastTickTime } = e.data;

                if (command === 'start') {
                    delay = initialDelay;
                    // 校准开始时间，考虑主线程暂停前的时间
                    expected = performance.now() + delay;
                    //console.log('Worker started with delay:', delay, 'Expected:', expected);
                    startLoop();
                } else if (command === 'stop') {
                    clearTimeout(timerId);
                    timerId = null;
                }
            };

            function startLoop() {
                timerId = setTimeout(() => {
                    const drift = performance.now() - expected; // 计算偏差
                    // console.log('Worker tick. Drift:', drift);

                    // 通知主线程执行回调
                    self.postMessage({ type: 'tick', drift: drift });

                    // 重新计算下一次触发时间，补偿偏差
                    expected += delay;
                    startLoop(); // 递归调用，形成循环
                }, Math.max(0, expected - performance.now())); // 确保延迟不为负数
            }
        `

    // 创建 Blob URL 作为 Worker 的源
    const blob = new Blob([workerCode], { type: "application/javascript" })
    this.worker = new Worker(URL.createObjectURL(blob))

    this.worker.onmessage = e => {
      if (e.data.type === "tick") {
        const drift = e.data.drift
        // 在主线程执行回调
        this.callback()

        // 重新设置主线程的开始时间，为了页面可见时能继续校准
        this.startTime = performance.now() - drift // 调整开始时间以补偿 worker 运行时的偏差
        this.remaining = this.delay // 重置剩余时间
      }
    }

    // 启动 Web Worker
    this.worker.postMessage({
      command: "start",
      initialDelay: this.delay,
      lastTickTime: this.startTime // 传递上次计时开始的时间，方便 Worker 校准
    })
  }

  /**
   * 内部方法：执行回调并重新调度
   * @private
   */
  _executeCallbackAndReschedule() {
    this.callback()
    // 重新设置开始时间，确保下一次计时是基于当前的
    this.startTime = performance.now()
    this.remaining = this.delay // 重置剩余时间
    this._scheduleNextTick() // 重新调度
  }

  /**
   * 内部方法：设置页面可见性变化的监听
   * @private
   */
  _setupVisibilityChangeHandler() {
    document.addEventListener("visibilitychange", () => {
      if (this.isRunning) {
        // 当可见性变化时，重新评估定时器状态
        // console.log("Visibility changed to:", document.hidden ? "hidden" : "visible")
        this._scheduleNextTick()
      }
    })
  }

  /**
   * 销毁定时器，释放资源
   */
  destroy() {
    this.stop()
    if (this.worker) {
      this.worker.terminate()
      this.worker = null
    }
    document.removeEventListener("visibilitychange", this._setupVisibilityChangeHandler)
  }
}
