Vue.component("sales-control-form", {
  name: "sales-control-form",
  props: ["list"],
  data() {
    return {
      ruleForms: [
        {
          count: 0,
          minute: 0
        }
      ],
      rules: {}
    }
  },
  computed: {},
  created() {
    let defaultForm = [
      {
        count: 0,
        minute: 0
      }
    ]
    let list = []
    if (typeof this.list === "function") {
      list = this.list.call(this.$root)
    }
    if (Array.isArray(list) && list.length) {
      this.ruleForms = list
    } else {
      this.ruleForms = defaultForm
    }
  },
  methods: {
    addItem() {
      this.ruleForms.push({
        count: 0,
        minute: 0
      })
    },
    // 删除使用时间
    del(index) {
      this.ruleForms.splice(index, 1)
    }
  },
  template: `
        <div class="container">
         <el-row gutter="20">
            <el-col :span="10">
              <el-form-item label="Order Count" ></el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="Estimated Time(minute)" ></el-form-item>
            </el-col>
         </el-row>
          <el-row gutter="20" v-for="(item, index) in ruleForms">
            <el-form
              :inline="true"
              :model="ruleForms[index]"
              ref="sales-control-rule-form"
              size="small"
              label-position="top"
              :rules="rules"
            >
             <el-col :span="9">
               <el-form-item>
                <el-input-number  v-model="ruleForms[index].count" :min="0"  style="width:100%" ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item>
               <el-input-number v-model="ruleForms[index].minute" :min="0" style="width:100%" ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-form-item>
                 <el-button
                 v-if="index != 0"
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  @click="del(index)"
                ></el-button>
                
                <el-button v-if="index == 0" type="success" @click="addItem" size="small">
                  <i class="el-icon-plus"></i>
                </el-button>
              </el-form-item>
            </el-col>
            </el-form>
          </el-row>
        </div>
  `
})
