<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link href="../../static/elementUI/index.css" rel="stylesheet" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <style>
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .el-main {
        overflow: auto;
        display: flex;
        flex-direction: column;
      }

      .card-list {
        flex: 1;
        overflow: auto;
      }

      :is(.card-item, .card-header) .el-card__body {
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 10px;
        align-items: center;
      }

      :is(.card-item, .card-header) .el-card__body:first-child {
        padding-left: 30px;
      }

      .el-card__body span {
        min-width: 33%;
      }

      .card-header strong {
        padding-left: 10px;
      }

      .card-header .operation {
        padding-right: 40px;
      }

      .filterMask {
        pointer-events: none;
        filter: blur(1px);
        background-color: aliceblue;
      }

      .editInput {
        transform: translateX(-15px);
      }

      #app {
        user-select: none;
      }

      .card-header {
        /*position: sticky;*/
        /*left: 0;*/
        /*top: 0;*/
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template id="table">
        <el-main :style="{height:tableHeight+'px'}">
          <el-card shadow="hover" class="card-header" style="padding: 10px 0">
            <strong>Store Type</strong>
            <strong style="transform: translateX(-60px)">Seq</strong>
            <div class="operation">
              <el-button type="primary" plain @click="onAddBtn">Add</el-button>
            </div>
          </el-card>
          <el-card class="card-list">
            <el-card shadow="hover" class="card-item" v-if="showAddArea">
              <span>
                <el-input
                  ref="addInput"
                  v-focus
                  v-model="addEditForm.tag_name"
                  placeholder="please enter store type"
                ></el-input>
              </span>
              <span>
                <el-input v-model="addEditForm.seq" placeholder="please enter seq"></el-input>
              </span>
              <div class="operation">
                <el-button size="mini" type="success" @click="confirmAddEdit">Confirm</el-button>
                <el-button size="mini" type="warning" @click="cancelAddEdit">Cancel</el-button>
              </div>
            </el-card>
            <el-card shadow="hover" v-for="t in tableData" :key="t.id" class="card-item">
              <span>
                <div v-show="t.id!==addEditForm.id">{{t.tag_name}}</div>
                <el-input
                  ref="editInput"
                  class="editInput"
                  v-model="addEditForm.tag_name"
                  v-if="t.id===addEditForm.id"
                ></el-input>
              </span>
              <span>
                <div v-show="t.id!==addEditForm.id">{{t.seq}}</div>
                <el-input
                  v-model="addEditForm.seq"
                  v-if="t.id===addEditForm.id"
                  placeholder="please enter seq"
                ></el-input>
              </span>
              <div class="operation" v-if="t.id!==addEditForm.id">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEditBtn($event,t)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="onDeleteBtn(t)"
                ></el-button>
              </div>
              <div class="operation" v-else>
                <el-button size="mini" type="primary" @click="confirmAddEdit($event,t)">
                  Confirm
                </el-button>
                <el-button size="mini" type="danger" @click="cancelAddEdit">Cancel</el-button>
              </div>
            </el-card>
          </el-card>
        </el-main>
      </template>
    </div>
    <script>
      const app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          tableData: [],
          showAddArea: false,
          addForm: {},
          editForm: {},
          Type: ["storeType"],
          addEditForm: {
            tag_name: "",
            seq: ""
          },
          lastEditRef: null,
          domain: sessionStorage.getItem("domain")
        },
        computed: {},
        created() {
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整\
          console.log(this.tableHeight, "查看高度配置")
          this.getData()
        },
        methods: {
          getData() {
            let data = {
              domain: this.domain
            }
            $.get({
              url: "../../manager_StoreType/selectAll",
              data,
              xhrFields: {
                responseType: "json"
              },
              success: res => {
                if (res.statusCode !== 200) {
                  this.$message.error("Query data error！")
                } else {
                  // let sortData=res.data.sort((a,b)=>a.id-b.id)
                  console.log(res.data, "数据")
                  this.tableData = res.data
                }
              },
              error: function (error) {
                console.log(error)
                // this.$message.error('Fail to load！');
              }
            })
          },
          onAddBtn() {
            if (this.showAddArea) {
              this.$message.warning("adding data !")
              this.$refs.addInput.focus()
              return
            }
            this.addEditForm = {
              tag_name: "",
              seq: ""
            }
            this.showAddArea = true
            this.$nextTick(() => {
              this.$refs.addInput.focus()
            })
          },
          confirmAddEdit(e, t) {
            //验证输入表单
            if (!this.addEditForm.tag_name.trim()) {
              this.$message.warning("please enter store type")
              this.$refs.addInput.focus()
              return
            }
            let url = "../../manager_StoreType/"
            let text = ""
            let data = {
              ...this.addEditForm
            }
            if (t && t.id === this.addEditForm.id) {
              //    edit confirm
              url += "updateOne"
              text = "Edit"
            } else {
              //    add confirm
              url += "addOne"
              text = "Add"
              data.domain = this.domain
            }
            $.post({
              url,
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode !== 200) {
                  this.$message.error(text + "  failure！")
                } else {
                  this.getData()
                  this.$message.success(text + "  success！")
                  this.editDialogVisible = false
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Edit failure！")
              }
            })
            this.showAddArea = false
            this.addEditForm = {}
            // this.getData()
          },
          cancelAddEdit() {
            this.addEditForm = {}
            this.showAddArea = false
            this.lastEditRef = null
          },
          onEditBtn(event, t) {
            if (this.showAddArea) {
              this.$message.warning("adding data !")
              this.$refs.addInput.focus()
              return
            }
            this.addEditForm = {
              ...t
            }
            // this.lastEditRef = this.getParents(event.target, 'el-card__body');
            //聚焦storeType 输入框
            this.$nextTick(() => {
              this.$refs.editInput[0].focus()
            })
          },
          onDeleteBtn(t) {
            let { id } = t
            $.post({
              url: "../../manager_StoreType/deleteOne",
              data: { id },
              dataType: "json",
              success: res => {
                if (res.statusCode !== 200) {
                  this.$message.error("Fail to delete!")
                } else {
                  this.getData()
                  this.$message.success("Successfully delete!")
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Fail to delete!")
              }
            })
          },
          //添加蒙层
          addFilter() {
            if (this.lastEditRef) {
              this.lastEditRef.classList.add("filterMask")
            }
          },
          //获取指定class的父节点
          getParents(element, className) {
            //dom.getAttribute('class')==dom.className，两者等价
            var returnParentElement = null
            function getpNode(element, className) {
              //创建父级节点的类数组
              let pClassList = element.parentNode.getAttribute("class")
              let pNode = element.parentNode
              if (!pClassList) {
                //如果未找到类名数组，就是父类无类名，则再次递归
                getpNode(pNode, className)
              } else if (pClassList && pClassList.indexOf(className) < 0) {
                //如果父类的类名中没有预期类名，则再次递归
                getpNode(pNode, className)
              } else if (pClassList && pClassList.indexOf(className) > -1) {
                returnParentElement = pNode
              }
            }
            getpNode(element, className)
            //console.log(returnParentElement);
            return returnParentElement
          }
        }
      })
    </script>
  </body>
</html>
