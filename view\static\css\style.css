﻿@charset "utf-8";

/* Basic CSS starts */

*{
	font-family: Microsoft YaHei,Helvetica,Arial,sans-serif;
	white-space: normal;
	padding:0px;
	margin:0px;
    color:#6c6c6c;
}
html.bg{
    background: #fff url(../img/home-new1.png) center center repeat-y;
    background-size: auto auto;
    height: 100%;
    background-size: 100% auto;
}
body
{
    line-height: 1.6;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    background:none;
}
h1,h2,h3,h4,h5,h6{
	font-weight: bold;
}
ul, li {
    list-style: none;
}
p{
	margin:0;
	padding:0;
}
a{
    cursor:pointer;
}
a,a:hover,a:active,a:focus{
	 text-decoration:none;
}

a:hover{

}

a:hover,a:active,a:focus {
	outline: 0;
}

hr{
	margin: 0;
	border-top: 1px solid #fff;
	border-bottom: 1px solid #dbd8d8;
}

:focus {
	outline:none;
}

::-moz-focus-inner {
	border:0;
}
.clearfix {
    _zoom: 1;
}
.font10{
    font-size:10px;
}
.font11{
    font-size:11px;
}
.font12{
    font-size:12px;
}
.font13{
    font-size:13px;
}
.font14{
    font-size:14px;
}
.font16{
    font-size:16px;
}
.font18{
    font-size:18px;
}
.font20{
    font-size:20px;
}
.font25{
    font-size:25px;
}
.font30{
    font-size:30px;
}
.font40{
    font-size:40px;
}
.font50{
    font-size:50px;
}
.left{
    text-align:left;
}
.center{
    text-align:center;
}
.right{
    text-align:right;
}
.float_left{
    float:left;
}
.float_right{
    float:right;
}
.font-weight{
    font-weight:bold;
}
.no_weight{
    font-weight:500;
}
.colorred{
    color:#E50014;
}
.color000{
    color:#000000;
}
.color333{
    color:#333333;
}
.color6c6{
    color:#6C6C6C;
}
.color666{
    color:#666666;
}
.color999{
    color:#999999;
}
.color5c7{
    color:#5C7796;
}
.colorfff{
    color:#ffffff;
}
.colorfff:hover,.colorfff:focus{
    color:#ffffff;
}
.color6c6c6c{
    color:#6c6c6c;
}
.colorff2230{
    color:#ff2230;
}
.coloraaa{
    color:#aaaaaa;
}
.P0{
    padding:0px;
}
.PT0{
    padding-top:0px;
}
.PB0{
    padding-bottom:0px;
}
.P5{
    padding:5px;
}
.P10{
    padding:10px;
}
.P15{
    padding:15px;
}
.PT0{
    padding-top:0px;
}
.PT5{
    padding-top:5px;
}
.PT10{
    padding-top:10px;
}
.PT15{
    padding-top:15px;
}
.PT20{
    padding-top:20px;
}
.PT30{
    padding-top:30px;
}
.PT40{
    padding-top:40px;
}
.PB0{
    padding-bottom:0px;
}
.PB5{
    padding-bottom:5px;
}
.PB10{
    padding-bottom:10px;
}
.PB15{
    padding-bottom:15px;
}
.PB20{
    padding-bottom:20px;
}
.PB30{
    padding-bottom:30px;
}
.PB40{
    padding-bottom:40px;
}
.PB50{
    padding-bottom:50px;
}
.PTB5{
    padding:5px 0px;
}
.PTB10{
    padding:10px 0px;
}
.PTB15{
    padding:15px 0px;
}
.PTB20{
    padding:20px 0px;
}
.PTB25{
    padding:25px 0px;
}
.PTB30{
    padding:30px 0px;
}
.PTB35{
    padding:35px 0px;
}
.PTB40{
    padding:40px 0px;
}
.PL10{
    padding-left:10px;
}
.PL15{
    padding-left:15px;
}
.PR10{
    padding-right:10px;
}
.MT0{
    margin-top:0px;
}
.MT5{
    margin-top:5px;
}
.MT10{
    margin-top:10px;
}
.MT15{
    margin-top:15px;
}
.MT20{
    margin-top:30px;
}
.MT30{
    margin-top:30px;
}
.MB10{
    margin-bottom:10px;
}
.MB15{
    margin-bottom:15px;
}
.MB20{
    margin-bottom:20px;
}
.MB30{
    margin-bottom:30px;
}
.MB40{
    margin-bottom:40px;
}
.MB50{
    margin-bottom:50px;
}
.btn {
    display: block;
    padding: 10px 12px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    white-space: nowrap;
    background-image: none;
    border: 1px solid transparent;
    color: #ffffff;
}
.btn-default {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    color: #333;
}
/*************************address********************/
.location {
	padding: 0 20px;
}
.location input:focus {
	border:1px solid #ffffff;
}
.input {
	position: relative;
	display: block;
	width: 100%;
	height: 40px;
	padding: 6px 10px;
	font-size: 14px;
	line-height: 1.42857143;
	color: #555;
	background-color: #fff;
	background-image: none;
	border: 1px solid #ccc !important;
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
	box-sizing: border-box;
}
.keywords {
	position: relative;
	opacity: 0.95;
	width: 100%;
	background: #FA2C2A url('../img/search-new.png') 10px center no-repeat;
	background-size: auto auto;
	background-size: 22px;
	font-size: 16px;
	text-align: center;
	color: #fff;
}
.branch {
	display: block;
	position: relative;
	opacity: 0.95;
	background: #fff;
}
.branch li {
	padding: 8px 10px;
	font-size: 16px;
	background: #ffffff;
	margin-bottom: 1px;
	border-bottom:1px solid #EFEEED;
}
.branch li img {
	vertical-align:middle;
}
.branch li .title {
	font-size:16px;
	color:#FA2C2A;
}
.branch li .desc {
	font-size:14px;
	color:#aaa;
	padding-left:5px;
}
.hand {
	background: #fff;
	padding: 10px 15px;
}
.hand > p {
	border: 1px solid #e5e5e5;
	padding: 6px;
	text-align: center;
	background: #f0f0f0;
	color: #FA2C2A;
	font-size: 16px;
}
.hand>p:active {
	background: #e5e5e5;
}
.tit2 {
	background: #fff;
	border-bottom: 1px solid #f0f0f0;
	padding: 5px;
	color: #aaa;
	font-weight: 500;
}
.addrselect {
	position: fixed;
	z-index: 9;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0.95;
}
[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak, .ng-hide:not(.ng-hide-animate) {
	display: none !important;
}
::-webkit-input-placeholder {
	color: #e9e9e9;
}
:-moz-placeholder {
	color: #e9e9e9;
}
::-moz-placeholder {
	color: #e9e9e9;
}
:-ms-input-placeholder {
	color: #e9e9e9;
}

.addrselect {
	position: fixed;
	z-index: 9;
	top:0;
	left:0;
	width: 100%;
	height: 100%;
	opacity: 0.95;
}
.addrselect .con {
	background: #fff;
	height: 100%;
	overflow: hidden;
}
.con .branch {
	float: right;
	width: 80%;
	height: 100%;
	padding-top:46px;
	background: #fff;
	overflow-y: scroll;
}
.con .branch li {
	border-bottom: 1px solid #eee;
}
.con .city {
	padding-top:40px;
	width: 20%;
	height: 100%;
	border-right: 1px solid #eee;
}
.con .city li {
	text-align: center;
	border-bottom: 1px solid #eee;
	line-height: 46px;
	font-size: 14px;
	color: #666;
	padding-right:10px;
}
.con .city li.active {
	background: url(../img/jt_right.png) right center no-repeat;
	background-size: 18px;
}
.con .back {
	position: absolute;
	width: 100%;
	left:0;
	top:0;
	z-index: 9999;
	background: #fff;
	padding: 0 10px;
	height: 46px;
	line-height: 46px;
	border-bottom: 1px solid #eee;
	color: #999;
	font-family: "新宋体";
}
.con .city, .con .back, .con .branch {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

/*************************index********************/
.img-responsive {
	display: inline-block;
	max-width: 100%;
	height: auto;
}
 .slide {
	width:100%;
	height:150px;
	overflow: hidden;
	position:relative;
}
.slide .swiper-slide {
	text-align:center;
	height:150px;
}
.slide .swiper-slide a {
	display:block;
	width:100%;
	height:100%;
}
.slide .swiper-slide img {
	width:auto;
	max-width:100%;
	height:150px;
}
.slide .pagination {
	position: absolute;
	z-index: 20;
	bottom:5px;
	width:100%;
	text-align: center;
	right:0;
}
.slide .swiper-pagination-bullet {
	display: inline-block;
	width:25px;
	height:3px;
	border-radius:0;
	background:#D0D0D0;
	margin: 0 2px;
	opacity: 0.8;
	cursor: pointer;
}
.slide .swiper-pagination-bullet-active {
	background:red;
}
.addWrap {
	position:relative;
	width:100%;
	background:#fff;
	margin:0;
	padding-top:15px;
}
.addWrap > .map_box{
    height:25px;
    display:block;
    text-align:center;
}
.addWrap > .map_box > img {
	display: inline-block;
	vertical-align:bottom;
}
.location_index {
	position: relative;
	padding-top: 50px;
}
.location_index .addr {
	position: absolute;
	left:0;
	top:10px;
	z-index: 10;
	width: 100%;
	text-align: center;
    padding-bottom:20px;
}
.location_index .addr > a {
	display: inline-block;
	color: #fff;
	height: 28px;
	border-radius: 14px;
	background: #FA2C2A;
	padding: 0 14px;
	line-height: 28px;
}
.location_index .addr > a p {
	display: block;
	font-size: 14px;
	color:#fff;
}
.location_index > ul{
    padding-top:10px;
}
.location_index > ul li {
	float:left;
	width: 33.333333333%;
	text-align: center;
	padding: 5px 0;
	position: relative;
}
.location_index > ul li > a > img {
	width: 65%;
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	-ms-border-radius:50%;
	border-radius:50%;
}
.location_index > ul li > a {
	display:block;
	color: #333;
	font-size: 12px;
}
.location_index > ul li p {
	line-height: 26px;
}
/*************************shop********************/
.content {
    position: relative;
    padding: 50px 0;
}
 .loca, aside, .cart, .sele {
	position: fixed;
	left: 0;
	z-index: 9;
}
.loca {
	background: #ffffff;
	height: 50px;
	top: 0;
	width: 100%;
	border-bottom: 1px solid #f0f0f0;
}
.loca a.change {
	padding-left: 12px;
	display: inline-block;
	color: #606060;
	font-size: 20px;
	line-height: 50px;
}
.loca a.change span {
	margin-left: 6px;
	font-size: 16px;
	color: #FA2C2A;
}
aside {
	width: 80px;
	position: fixed;
	top: 51px;
	border-right: 1px solid #f0f0f0;
}
aside .table {
	height: 46px;
	cursor: pointer;
	float: left;
	width: 80px;
}
aside .table p {
	margin: 0;
	padding: 0;
	text-align: center;
	width: 77px;
	font-size: 14px;
	line-height: 30px;
	margin-top: 8px;
}
aside li.active p {
	border-left: 3px solid #FA2C2A;
	color: #FA2C2A;
}
 .cate {
	margin-top: 5px;
	display: none;
    -webkit-overflow-scrolling: touch;
}
.cate .con {
	margin-left: 81px;
	padding: 10px;
	background: #fff;
}
.cate .con_no {
	padding: 0px;
}
.cate .banner {
	width: 100%;
}
.cate .title {
	background: url(../img/cate/styleline.jpg) center center no-repeat;
	background-size: 60%;
	text-align: center;
	padding: 20px 0;
	clear: both;
}
.cate li {
	width: 33%;
	float: left;
	padding-top: 18px;
}
.cate li img {
	width: 80%;
	max-width:100%;
}
.cate li p {
	text-align: center;
	font-size: 12px;
}
.hide {
	display: none;
}
.show {
	display: block;
}
 
section {
	margin-left: 86px;
    -webkit-overflow-scrolling: touch;
}
section .hide{
	display:none;
}
section .show{
	display:block;
}
.lists {
	margin-top: 10px;
	padding: 0;
}
.lists li {
	border-bottom: 1px solid #e0e0e0;
    height:105px;
}
.lists div.l {
	width: 35.99%;
	float:left;
}
.lists div.l img {
	max-width: 80px;
	margin-top: 10px;
}
.lists div.r {
	width: 64%;
	float: right;
	padding: 10px 0;
}
.lists div.r .t {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.lists div.r .price {
	color: #ff0000;
	font-size: 14px;
	margin-top: 6px;
}
.lists .jifen {
	float: left;
	margin-top: 5px;
	color: #9839FD;
	font-size: 12px;
}
.lists .showaddcart {
	float:right;
}
.lists .showaddcart > img {
	width: 28px;
	margin-right: 15px;
}
.lists .num_con{
    display:none;
	margin-right: 15px;
}
.lists .num_con img{
    width: 25px;
    vertical-align:middle;
}
.lists li.on .join {
	display:none;
}
.lists li.on .num_con {
	display:block;
}
.lists li .detail{
    display:none;
    position: absolute;
    top:0px;
    left:0px;
    height:100%;
    z-index: 9999;
    padding:10px;
    background: #fff;
}
.lists li .detail .back{
    width:30px;
    height:40px;
    cursor:pointer;
}
.lists li .detail .title{
    padding:3px 5px;
    background:#DCDCDC;
    font-size:14px;
    margin-bottom:10px;
}
.lists li .detail .desc{
    font-size:14px;
}
/************************* cart ********************/
.cartempty {
	padding-top:150px;
}
.cartempty h3,.cartempty a {
	color: #888;
	text-align: center;
}
.cartempty a {
	display: block;
	padding: 8px 0;
	border:1px solid #ddd;
	width:40%;
	margin: 30px auto;
}
.cartpay .addr {
	background-size: 22px;
	padding:10px 20px 10px 30px;
	position: relative;
}
.cartpay .addr:before {
	content:'';
	width:20px;
	height:20px;
	background: url(../img/loc2.png) no-repeat;
	display:inline-block;
	background-size: 100% 100%;
	position:absolute;
	top:30px;
	left:3px;
}
.cartpay .addr .edit {
	position:absolute;
	right: 25px;
	top:9px;
	color: #999;
}
.cartpay .addr .edit:after {
	content:'';
	width:20px;
	height:20px;
	background: url(../img/right.png) no-repeat;
	display:inline-block;
	background-size: 100%;
	position:absolute;
	top:4px;
	right:-20px;
}
.cartpay .add_addr {
	padding: 10px 20px;
	margin:10px 20px;
}
.cartpay .remark {
	width:70%;
	float:left;
	border: 1px solid #fff !important;
	box-shadow: inset 0 1px 1px rgba(0,0,0,0);
}
.cartpay .yujibox {
	background: url(../img/shijian.png) no-repeat;
	background-position: 5px 13px;
	background-size:16px;
	box-sizing:border-box;
	line-height: 40px;
	height:40px;
	width:100%;
}
.cartpay .remarkbox {
	background: url(../img/liuyan.png) no-repeat;
	background-position: 5px 13px;
	background-size:16px;
	box-sizing:border-box;
	line-height: 40px;
	height:40px;
	width:100%;
}
.prolist_section {
    margin-left:0px;
    padding-bottom:80px;
}
.prolist_section .lists li{
    min-height:120px;
}
/************************* edit address ********************/
.add_addr_title{
    border-top:none;border-left:none;border-right:none;
    font-size:16px;
}
.addr_con{
    padding:15px;
}
.addr_con .add_addr_dasha{
    font-size:15px;
    margin-bottom:20px;
}
.addr_con .aui-Address-box-input {
  width: 96%;
  border: 1px solid #ddd;
  outline: 0;
  -webkit-appearance: none;
  background-color: #fff;
  font-size: 14px;
  height: 2.5em;
  line-height: 2.5;
  padding-left: 3%;
  margin-bottom: 20px;
}
.addr_con .aui-Address-box-select {
  width: 49%;
  border: 1px solid #ddd;
  outline: 0;
  -webkit-appearance: none;
  background-color: #fff;
  font-size: 14px;
  height: 2.5em;
  line-height: 2.5;
  margin-bottom: 20px;
}
.addr_con .red-color {
    width: 100%;
    height: 38px;
    line-height: 38px;
    text-align: center;
    font-size: 14px;
    color: #fff;
    border: 1px solid #FA2C2A;
    background:#FA2C2A;
    display: block;
}
/*************************dialog********************/
.weui_mask {
    position: fixed;
    z-index: 99998;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0,0,0,0.6);
}
.weui_dialog {
    position: fixed;
    z-index: 99999;
    width: 85%;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    background-color: #fafafc;
    text-align: center;
    border-radius: 3px;
}
.weui_dialog_hd {
    padding: 1.2em 0 .5em;
}
.weui_dialog_title {
    font-weight: 400;
    font-size: 17px;
}
.weui_dialog_bd {
    padding: 0 20px;
    font-size: 15px;
    color: #888;
    word-wrap: break-word;
    word-break: break-all;
}
.weui_dialog_ft {
    position: relative;
    line-height: 42px;
    margin-top: 20px;
    font-size: 17px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    border-top:1px solid #E7E7E9;
}
.weui_dialog_ft a {
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #3cc51f;
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}
.weui_btn_dialog.primary {
    color: #FA2C2A;
}
/*************************pay********************/
.orderno {
	padding: 10px;
	border-bottom: 1px dashed #e9e9e9;
}
.top-pay {
	margin: 10px;
	border: 1px dashed #e9e9e9;
}
.top-pay p.addr {
	padding-left: 68px;
}
.top-pay, .bottom-pay {
	padding: 8px 10px;
	font-size: 14px;
}
.top-pay span {
	float: right;
}
.bottom-pay span {
	margin-right: 15px;
	color: #ff0000;
}
.clear {
	clear: both;
	height: 10px;
}
.coupon{
    padding:10px;
    font-size:12px;
}
.coupon .number{
    width:50px;
    border:1px solid #eee;
}
.coupon .red{
   color:#FA2C2A;
}
.nocoupon {
	padding: 10px;
	border: 1px dashed #e9e9e9;
}
.moneyalert {
	position: absolute;
	left: 0;
	bottom: 40px;
	color: #ddbc84;
	text-align: center;
	width: 100%;
}
.paytype h4 {
	font-weight: normal;
	padding: 6px 10px;
	background:#F0F0F0;
}
.paytype li {
	position: relative;
	margin-left: 10px;
	height: 50px;
	line-height: 50px;
	padding-left: 50px;
}
.paytype li img {
	position: absolute;
	right: 30px;
	height: 30px;
	top: 10px;
}
.paytype .wxpay {
	background: url(../img/wxpay.jpg) 0 center no-repeat;
	background-size: 36px;
}
/*************************tof********************/
.lists-tof {
	width: 100%;
	height:100%;
    overflow-y:auto;
    padding-bottom:80px;
}
.lists-tof li {
	float:left;
	width:50%;
	height:auto;
	box-sizing:border-box;
	padding:5px 8px;
    border:none;
}
.lists-tof li .img {
	margin-bottom:5px
}
.lists-tof li .img img {
	width:100%;
	height:auto
}
.lists-tof li p {
	text-align:left;
	font-size:14px;
	height:20px;
	line-height:16px;
	width:100%
}
.lists-tof li .t {
	height:40px;
}
.lists-tof li .price {
	color:#ce0205;
	font-size:16px
}
.lists-tof li p .meshop {
	float:right;
	position:relative;
	width:100%;
	height:30px
}
/*************************order********************/
header {
    display: block;
    position: fixed;
    width: 100%;
    height: 50px;
    top: 0;
    left: 0;
    background: #f9fafd;
    z-index: 9;
    border-bottom: 1px solid #e0e0e0;
}
.title-order {
	line-height:50px
}
.title-order .title-top {
    line-height: 50px;
    background: url(../img/left.png) 10px center no-repeat;
    background-size: auto auto;
    background-size: 22px;
	display: inline-block;
	width:100%;
	height:50px;
	float:left;
    text-align:center;
}
.navlist {
	box-sizing: border-box;
	z-index: 99
}
.navlist li {
	width:50%;
	float:left;
	box-sizing:border-box;
	text-align:center;
	height:35px;
	line-height:35px;
	border-bottom:1px dotted #ccc;
	background:#fff
}
.navlist .rightborder {
	border-right:1px dotted #ccc
}
.navlist .active {
	background-color:#3FAACA;
	color:#fff
}
.orderlist {
	font-size:14px;
}
.orderlist .lists {
	margin-top:70px;
	color: #666;
}
.orderlist .lists > li {
	border-bottom:15px solid #e0e0e0;
	padding: 20px 0;
	height: auto;
	padding-bottom: 45px;
}
.orderlist .lists > li p {
	padding:0px 10px;
}
.orderlist .lists > li .status {
	float:right;
	color: #FA2C2A;
}
.orderlist .lists > li .bottom {
	padding-top:5px;
}
.orderlist .lists > li .bottom .money {
	color: #FA2C2A;
}
.orderlist .lists > li .bottom a {
	float: right;
	padding:4px 12px;
	background:#FA2C2A;
	text-align:center;
	color:#fff;
	margin-right:10px;
}
.orderlist .pdtlist {
	border-top:1px solid #e9e9e9;
	border-bottom:1px solid #e9e9e9;
	padding:10px;
}
.orderlist .pdtlist > li {
	position: relative;
	padding: 3px 0;
	border:none;
	height:80px;
}
.orderlist .pdtlist .l {
	width: 20%;
	float:left;
}
.orderlist .pdtlist .l img {
	display: block;
	width: 60px;
	margin:3px auto;
}
.orderlist .pdtlist .r {
	float:right;
	width: 80%;
}
.orderlist .pdtlist .r > p {
	display: block;
}
.orderlist .pdtlist .r .t {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 14px;
}
.orderlist .pdtlist .r .money {
	font-size: 12px;
	color: #FA2C2A;
}
.orderlist .pdtlist .r .jf {
	font-size: 12px;
}
/*************************userinfo********************/
.header {
    height: 130px;
    position: relative;
    background: #FA2C2A;
    background-size: auto auto;
    background-size: 100% 100%;
    border: none;
}
.header .img {
    width: 60px;
    border-radius: 40px;
    top: 25px;
    left: 40px;
    position: absolute;
}
.header .mobile {
    color: #fff;
    position: absolute;
    left: 130px;
    top: 35px;
    font-size: 16px;
}
.header .nickname {
    color: #fff;
    position: absolute;
    left: 130px;
    top: 60px;
    font-size: 16px;
}
.headerlist {
    font-family: "Helvetica Neue", "微软雅黑";
    color: #777;
}
.headerlist > li {
    position: relative;
    padding: 5px 0;
    float: left;
    width: 49.8%;
    height: 40px;
    text-align: center;
    line-height: 40px;
    font-size: 13px;
}
.userlist{
    padding:10px 0px;
}
.userlist>li {
	position:relative;
	padding:10px 0;
	float:left;
	width:33.33%;
	text-align:center;
	border-right:none;
	box-sizing:border-box;
	font-size: 14px;
	color: #777;
}
.userlist>li:active {
	background: #e9e9e9;
}
.userlist li:first-child {
	border-left:none
}
.userlist li:nth-child(3n+1) {
	border-left:none
}
.userlist li:nth-child(n+4) {
	border-top:none;
	margin-left:0px
}
.userlist .ph {
	height:35px;
	line-height:35px
}
.userlist .ph2 {
	height:30px;
	line-height:30px
}
.userlist .ico {
	display: block;
	height:40px;
}
.userlist .ico-order {
	background: url('../img/user2.png') center center no-repeat;
	background-size: 32px;
}
.userlist .ico-customer {
	background: url('../img/user6.png') center center no-repeat;
	background-size: 32px;
}
.userlist .ico-about {
	background: url('../img/user5.png') center center no-repeat;
	background-size: 32px;
}
.userlist .ico-reward {
	background: url('../img/user1.png') center center no-repeat;
	background-size: 32px;
}
.userlist .ico-recommend {
	background: url('../img/user3.png') center center no-repeat;
	background-size: 32px;
}
.userlist .ico-coupon {
	background: url('../img/user4.png') center center no-repeat;
	background-size: 32px;
}
/*************************points********************/
.pointslist{
    padding:10px;
    font-size:12px;
}
.pointslist thead tr td{
   font-size:14px;
}
.pointslist tr td{
    text-align:center;
    padding:3px 0px;
}
/*************************comments********************/
.borderleft5o {
    margin: 10px auto;
    padding: 10px;
}
.borderleft5o span{
   font-size:12px;
}
.comment-box{
    padding:10px;
    font-size:14px;
    min-height:200px;
    overflow-y:auto;
}
.comment-box .box{
    min-height:57px;
    overflow-y:auto;
}
.comment-box label {
    display:block;
    float:left;
    width: 28%;
}
.comment-box .aui-Address-box-input,.comment-box .aui-Address-box-textarea {
    width: 67%;
    float:left;
    border: 1px solid #ddd;
    outline: 0;
    -webkit-appearance: none;
    background-color: #fff;
    font-size: 14px;
    height: 2.5em;
    line-height: 2.5;
    padding-left: 3%;
    margin-bottom: 20px;
}
.comment-box .aui-Address-box-textarea {
    height:100px;
}
.comment-box .btnSubmit {
    padding:5px 20px;
    background:#FA2C2A;
    color:#FFF;
}
/*************************activity********************/
.activity-detail{
    height:100%;
    padding:10px;
    background: #fff;
}
.activity-detail .back{
    display:block;
    width:80px;
    height:40px;
    font-size:14px;
    cursor:pointer;
}
.activity-detail .back img{
    width:16px;
    vertical-align:middle;
}
.activity-detail .title{
    padding:3px 5px;
    background:#DCDCDC;
    font-size:14px;
    margin-bottom:10px;
}
.activity-detail .desc{
    font-size:14px;
}
/*************************footer menu********************/
 .nav {
	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 9;
	height: 50px;
	width: 100%;
	background: #fff;
	border-top: 1px solid #eee;
}
.nav li {
	position: relative;
	width: 25%;
	float: left;
	font-size: 12px;
	text-align: center;
}
.nav li > a {
	display: block;
	color: #685d5a;
	padding-top: 4px;
}
.nav img {
	width: 26px;
}
.nav p {
	margin-top: -5px;
}
.nav .cartnum {
    position: absolute;
    display: inline-block;
    font-style: normal;
    background: #ff0000;
    font-size: 12px;
    height: 16px;
    left: 56%;
    top: 2px;
    color: #ffffff;
    border-radius: 10px;
    padding: 0 5px;
    z-index: 99;
    line-height: 16px;
}
/*************************footer cart********************/
 .cart {
	z-index: 18;
	bottom: 0;
	height: 50px;
	width: 100%;
	background: #3c3c3c;
}
.cart > div {
	line-height: 50px;
	font-size: 16px;
	color: #fff;
}
.cart .r {
	float: right;
	width: 36%;
	background: #FA2C2A;
	text-align: center;
}
.cart .r a{
    display:block;
    width:100%;
    height:100%;
	color:#FFF;
}
.cart .l {
	width: 42%;
	position: relative;
	padding-left: 21%;
}
.cart .l.c {
	width: 60%;
	position: relative;
	padding-left: 3%;
}
.cart .l label{
	color:#fff;
}
.cart .cartnum {
	position: absolute;
	display: inline-block;
	font-style: normal;
	background: #ff0000;
	font-size: 12px;
	height: 16px;
	left: 50px;
	top: 4px;
	color: #ffffff;
	border-radius: 10px;
	padding: 1px 6px;
	z-index: 2;
	line-height: 16px;
}
.cart img {
	position: absolute;
	padding: 5px;
	height: 35px;
	background: #3c3c3c;
	z-index: 1;
	left: 10px;
	top: 2px;
}
.cart .points{
	color:#fff;
    padding-left:10px;
}
.cart-pay{
    background: #EFEFEF;
}
.cart-pay > div{
    color: #FA2C2A;
}
.cart-pay .l label {
    color: #FA2C2A;
}



/*父元素常见属性*/
/*设置主轴的方向*/
/*flex-direction：  row行,column列*/
/*设置主轴上的子元素排列方式*/
/*justify-content： space-around平分,space-between两边贴中间平分,space-evenly每个间距相同*/
/*设置子元素是否换行*/
/*flex-wrap：wrap/nowrap*/
/*设置侧轴上的子元素排列方式（单行）*/
/*align-items：stretch 拉伸*/
/*设置侧轴上的子元素的排列方式（多行）*/
/*align-content：stretch 子元素高度平分父元素高度*/
/*flex-flow：复合属性，相当于同时设置了 flex-direction 和 flex-wrap*/
/*子元素常见属性*/
/*align-self 子元素自己在侧轴的排列方式*/
/*order 属性定义子项的排列顺序(前后顺序) */