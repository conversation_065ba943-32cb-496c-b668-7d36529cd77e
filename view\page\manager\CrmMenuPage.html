<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      #app {
        padding: 8px;
      }
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }
      .linkBtn {
        margin-right: 10px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :header-cell-style="{fontSize: '15px'}"
          :max-height="tableHeight"
          empty-text="No Data"
          border
        >
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :label="item.label"
            :prop="item.prop"
            :fixed="item.fixed"
            :width="item.width"
            align="center"
          ></el-table-column>
          <el-table-column prop label="Operation" align="center">
            <el-table-column width="180" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible = true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <el-popconfirm
                  confirm-button-text="Confirm"
                  cancel-button-text="Cancel"
                  title="Do you want to connect?"
                  @confirm="checkLink(scope.$index, scope.row)"
                >
                  <el-button
                    size="small"
                    type="success"
                    icon="el-icon-s-promotion"
                    circle
                    class="linkBtn"
                    slot="reference"
                  ></el-button>
                </el-popconfirm>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="onDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 類別Add弹窗 -->
      <template>
        <el-dialog title="Add Dialog" :visible.sync="addDialogVisible" @close="onClose('addForm')">
          <el-form :model="addForm" ref="addForm" label-width="auto" :rules="rules">
            <el-form-item label="Encode String" prop="encodeString">
              <el-input
                v-model="addForm.encodeString"
                placeholder="Please enter the encodeString"
              ></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog_footer">
            <el-button type="primary" @click="addDialogVisible=false">Cancel</el-button>
            <el-button style="margin-right: 8px" @click="onAdd('addForm')">Add</el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 单元格编辑弹窗 -->
      <template>
        <el-dialog
          title="Edit Dialog"
          :visible.sync="editDialogVisible"
          @close="onClose('editForm')"
        >
          <el-form :model="editForm" ref="editForm" label-width="auto" :rules="rules">
            <el-form-item label="Database Name" prop="databaseName">
              <el-input v-model="editForm.databaseName" disabled></el-input>
            </el-form-item>
            <el-form-item label="Encode String" prop="encodeString">
              <el-input
                v-model="editForm.encodeString"
                placeholder="Please enter the encodeString"
              ></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog_footer">
            <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
            <el-button style="margin-right: 8px" @click="subEdit">Submit</el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 单元格删除弹窗 -->
    </div>
    <script>
      var app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          domain: sessionStorage.getItem("domain"),
          addDialogVisible: false,
          editDialogVisible: false,
          tableColumn: [
            {
              label: "id",
              prop: "id",
              width: 100
            },
            {
              label: "Database Name",
              prop: "databaseName"
            }
          ],
          rules: {
            encodeString: [
              {
                required: true,
                message: "Please enter the Encode String",
                trigger: "blur"
              }
            ]
          },
          addForm: {
            encodeString: ""
          },
          editForm: {
            id: "",
            databaseName: "",
            encodeString: ""
          },
          tableData: []
        },
        created() {
          this.getData()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整
        },
        watch: {
          // editDialogVisible(val) {
          //   if (val) {
          //     this.rules.password[0].required = false
          //   } else {
          //     this.rules.password[0].required = true
          //   }
          // }
        },
        methods: {
          /**
           * @description: 接口状态码
           * @200 成功
           * @400 参数错误
           * @500 服务器错误
           * @502 创建数据库连接失败
           */
          getData() {
            $.get({
              url: "../../manager_crmConfig/selectAll",
              dataType: "json",
              success: res => {
                console.log(res, "数据")
                if (res.statusCode == 200) {
                  this.tableData = res.data
                } else {
                  this.$message.error("Fail to load！")
                }
              },
              error: function (error) {
                console.log(error)
                this.$message.error("Fail to load！")
              }
            })
          },
          onAdd(addForm) {
            let data = JSON.stringify(this.addForm)
            console.log(data)
            this.$refs[addForm].validate(valid => {
              if (valid) {
                $.post({
                  url: "../../manager_crmConfig/insertOne",
                  data,
                  dataType: "json",
                  contentType: "application/json",
                  success: res => {
                    if (res.statusCode == 200) {
                      this.getData()
                      this.$message.success("Successfully added！")
                      this.addDialogVisible = false
                    } else {
                      this.$message.error("Fail to add！")
                    }
                  },
                  error: function (error) {
                    this.$message.error("Fail to add！")
                  }
                })
              } else {
                this.$message.error("Fail to add！")
              }
            })
          },
          onEdit(index, row) {
            // console.log(row)
            let { id, databaseName } = row
            this.editForm = {
              ...this.editForm,
              id,
              databaseName
            }
            this.editDialogVisible = true
          },
          subEdit() {
            let data = JSON.stringify(this.editForm)
            $.post({
              url: "../../manager_crmConfig/updateOne",
              data,
              dataType: "json",
              contentType: "application/json",
              success: res => {
                if (res.statusCode == 200) {
                  this.getData()
                  // console.log(res);
                  this.$message.success("Edit success！")
                  this.editDialogVisible = false
                } else {
                  this.$message.error("Edit failure！")
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Edit failure！")
              }
            })
          },
          onDel(index, row) {
            $.post({
              url: "../../manager_crmConfig/deleteOne",
              data: JSON.stringify({ id: row.id }),
              dataType: "json",
              contentType: "application/json",
              success: res => {
                if (res.statusCode == 200) {
                  this.getData()
                  this.$message.success("Successfully delete！")
                } else {
                  this.$message.error("Fail to delete！")
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Fail to delete！")
              }
            })
          },

          // 测试连接是否成功
          checkLink(index, row) {
            $.post({
              url: "../../manager_database/tryCrmConnect",
              data: JSON.stringify({ domain: this.domain }),
              dataType: "json",
              contentType: "application/json",
              success: res => {
                if (res.statusCode == 200) {
                  this.$message.success("The connection is successful！")
                } else {
                  this.$message.error("The connection failed！")
                }
              },
              error: error => {
                // console.log(res);
                this.$message.error("The connection failed！")
              }
            })
          },
          onClose(type) {
            this.$refs[type].resetFields()
          }
        }
      })
    </script>
  </body>
</html>
