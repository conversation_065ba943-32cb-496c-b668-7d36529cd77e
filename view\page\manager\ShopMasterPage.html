<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>

    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/moment/moment.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>

    <style>
      html,
      body {
        padding: 0;
        margin: 0;
      }

      #app {
        padding: 8px 15px;
      }

      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      /* .el-button + .el-button {
        margin: 5px 0 0 0 !important;
      } */
      .el-dialog__body {
        padding: 30px 20px 0px;
        color: #606266;
        font-size: 14px;
        word-break: break-all;
        /* max-height: 50vh; */
        overflow: auto;
      }

      .shop-table-expand {
        display: flex;
        /*justify-content: space-around;*/
        width: 100%;
        flex-wrap: wrap;
      }

      .shop-table-expand label {
        /* width: 90px; */
        color: #99a9bf;
        white-space: nowrap;
      }

      .shop-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
        padding-left: 40px;
        display: flex;
        align-items: center;
      }

      .shop-table-expand .el-form-item .el-form-item__content {
        height: 100%;
        display: flex;
        align-items: center;
        line-height: normal;
      }

      .deployPopconfirm {
        margin-left: 10px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          v-if="tableHeight"
          :data="tableData"
          style="width: 100%"
          :max-height="tableHeight"
          border
          id="exportTable"
          ref="report-table"
          empty-text="No Data"
          class="tableContent"
        >
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-form label-position="left" class="shop-table-expand">
                <el-form-item
                  :label="item.label"
                  v-for="(item,index) in expandTableColumn"
                  class=""
                >
                  <span>{{ handleZoneView(props.row,item.prop)}}</span>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column
            :fixed="item.fixed"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            v-for="(item,index) in tableColumn"
            align="center"
          >
            <template slot-scope="scope">
              <el-switch
                v-if="item.prop=='usable'"
                v-model="scope.row.usable"
                :active-value="true"
                :inactive-value="false"
                @change="onEditSwitch($event, scope.row)"
              ></el-switch>
              <el-switch
                v-else-if="item.prop=='takeawayUsable'"
                v-model="scope.row.takeawayUsable"
                :active-value="true"
                :inactive-value="false"
                @change="onEditSwitch($event, scope.row)"
              ></el-switch>
              <el-switch
                v-else-if="item.prop=='foodCourtUsable'"
                v-model="scope.row.foodCourtUsable"
                @change="onEditSwitch($event, scope.row)"
              ></el-switch>
              <span v-else>{{showDataOrTime(scope.row[item.prop],item.prop)}}</span>
            </template>
          </el-table-column>
          <!-- 操作栏目 -->
          <el-table-column prop label="Operation" align="center">
            <el-table-column width="110" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible=true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <!-- 除按钮类型外显示编辑按钮 -->
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-popconfirm
                  title="Are you sure to delete this store?"
                  icon-color="red"
                  class="deployPopconfirm"
                  @confirm="onDel(scope.$index, scope.row)"
                >
                  <el-button
                    size="small"
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    slot="reference"
                  ></el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 新增 -->
      <template>
        <!-- @open="openDialog('addForm')"  -->
        <el-dialog
          @close="onClose('addForm')"
          title="Add Dialog"
          :show-close="false"
          :visible.sync="addDialogVisible"
          :close-on-press-escape="false"
          :close-on-click-modal="false"
          :destroy-on-close="true"
        >
          <el-form
            ref="addForm"
            :model="addForm"
            :rules="rules"
            size="small"
            label-width="auto"
            class="formBox"
          >
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="addForm.storeNumber"
                placeholder="Please enter StoreNumber"
                show-word-limit
                clearable
                :style="{width: '100%'}"
              ></el-input>
            </el-form-item>
            <el-form-item label="Store Name">
              <div style="display: flex">
                <el-input
                  v-model="addForm.storeName.en"
                  placeholder="StoreName(en-US)"
                  show-word-limit
                  clearable
                  :style="{width: '33%'}"
                ></el-input>
                <el-input
                  v-model="addForm.storeName.zh"
                  placeholder="StoreName(zh-HK)"
                  show-word-limit
                  clearable
                  :style="{width: '33%'}"
                ></el-input>
                <el-input
                  v-model="addForm.storeName.thirdLan"
                  placeholder="StoreName(thirdLan)"
                  show-word-limit
                  clearable
                  :style="{width: '33%'}"
                ></el-input>
              </div>
            </el-form-item>
            <el-form-item label="Store Type" prop="storeType">
              <el-select
                style="width: 100%"
                v-model="addForm.storeType"
                multiple
                collapse-tags
                placeholder="please select store type"
              >
                <el-option
                  v-for="item in storeTypeList"
                  :key="item.tag_name"
                  :label="item.tag_name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Opening Dates" prop="openingDates">
              <el-date-picker
                type="daterange"
                v-model="addForm.openingDates"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="Start Date"
                end-placeholder="End Date"
                range-separator="~"
                clearable
                :style="{width: '100%'}"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="Opening Hours" prop="openingHours">
              <el-time-picker
                is-range
                v-model="addForm.openingHours"
                range-separator="~"
                start-placeholder="Start Hour"
                end-placeholder="End Hour"
                value-format="HH:mm:ss"
                :style="{width: '100%'}"
              ></el-time-picker>
            </el-form-item>
            <el-form-item label="Zone" prop="zone">
              <el-cascader
                :props="{value:'id',checkStrictly: true}"
                placeholder="Please select zone"
                style="width: 100%"
                v-model="addForm.zone"
                :options="zoneList"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="Address(en)" prop="address.en">
              <el-input
                v-model="addForm.address.en"
                placeholder="Please enter address"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Address(zh)" prop="address.zh">
              <el-input
                v-model="addForm.address.zh"
                placeholder="Please enter address"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Address(thirdLan)" prop="address.thirdLan">
              <el-input
                v-model="addForm.address.thirdLan"
                placeholder="Please enter address"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item label="Dine-in Timeout Prompt(en)" prop="timeoutPrompts.en">
              <el-input
                v-model="addForm.timeoutPrompts.en"
                placeholder="Please enter timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item label="Dine-in Timeout Prompt(zh)" prop="timeoutPrompts.zh">
              <el-input
                v-model="addForm.timeoutPrompts.zh"
                placeholder="Please enter timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Dine-in Timeout Prompt(thirdLan)" prop="timeoutPrompts.thirdLan">
              <el-input
                v-model="addForm.timeoutPrompts.thirdLan"
                placeholder="Please enter timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item label="Takeaway Timeout Prompt(en)" prop="takeawayTimeoutPrompts.en">
              <el-input
                v-model="addForm.takeawayTimeoutPrompts.en"
                placeholder="Please enter takeaway timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Takeaway Timeout Prompt(zh)" prop="takeawayTimeoutPrompts.zh">
              <el-input
                v-model="addForm.takeawayTimeoutPrompts.zh"
                placeholder="Please enter takeaway timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="Takeaway Timeout Prompt(thirdLan)"
              prop="takeawayTimeoutPrompts.thirdLan"
            >
              <el-input
                v-model="addForm.takeawayTimeoutPrompts.thirdLan"
                placeholder="Please enter takeaway timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item label="LocationCoordinates" prop="locationCoordinates">
              <el-input
                v-model.trim="addForm.locationCoordinates"
                placeholder="Format : latitude,longitude (Use , to seperate multiple data)"
                clearable
                @input="addForm.locationCoordinates=addForm.locationCoordinates.replace(/\s+/g,'')"
              ></el-input>
            </el-form-item>
            <el-form-item label="Price Group" prop="group">
              <el-select v-model="addForm.group" placeholder="please choose" style="width: 100%">
                <el-option
                  v-for="groupItem in groupData"
                  :key="groupItem"
                  :label="groupItem"
                  :value="groupItem"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Desc(en)" prop="storeDesc.en">
              <el-input
                type="textarea"
                v-model="addForm.storeDesc.en"
                placeholder="Description of the store"
              ></el-input>
            </el-form-item>
            <el-form-item label="Desc(zh)" prop="storeDesc.zh">
              <el-input
                type="textarea"
                v-model="addForm.storeDesc.zh"
                placeholder="Description of the store"
              ></el-input>
            </el-form-item>
            <el-form-item label="Desc(thirdLan)" prop="storeDesc.thirdLan">
              <el-input
                type="textarea"
                v-model="addForm.storeDesc.thirdLan"
                placeholder="Description of the store"
              ></el-input>
            </el-form-item>
            <el-form-item label="Redirect Url" prop="redirectUrl">
              <el-input
                type="text"
                v-model="addForm.redirectUrl"
                placeholder="Start with http/s"
              ></el-input>
            </el-form-item>
            <el-form-item label="Takeaway Usable" prop="takeawayUsable">
              <el-switch
                v-model="addForm.takeawayUsable"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
            </el-form-item>
            <el-form-item label="FoodCourt Usable" prop="foodCourtUsable">
              <el-switch v-model="addForm.foodCourtUsable"></el-switch>
            </el-form-item>
            <el-form-item label="Usable" prop="usable">
              <el-switch
                v-model="addForm.usable"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
            </el-form-item>
          </el-form>
          <div slot="footer">
            <el-button @click="addDialogVisible = false" v-show="!btnLoading">Cancel</el-button>
            <el-button type="primary" @click="handleAddConfirm" :loading="btnLoading">
              Add
            </el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 编辑 -->
      <template>
        <el-dialog
          @close="onClose('editForm')"
          title="Edit Dialog"
          @close="onClose('editForm')"
          :visible.sync="editDialogVisible"
          :destroy-on-close="true"
        >
          <el-form ref="editForm" :model="editForm" :rules="rules" size="small" label-width="auto">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="editForm.storeNumber"
                placeholder="Please enter StoreNumber "
                show-word-limit
                disabled
                :style="{width: '100%'}"
              ></el-input>
            </el-form-item>
            <el-form-item label="Store Name">
              <div style="display: flex">
                <el-input
                  v-model="editForm.storeName.en"
                  placeholder="StoreName(en-US)"
                  show-word-limit
                  clearable
                  :style="{width: '33%'}"
                ></el-input>
                <el-input
                  v-model="editForm.storeName.zh"
                  placeholder="StoreName(zh-HK)"
                  show-word-limit
                  clearable
                  :style="{width: '33%'}"
                ></el-input>
                <el-input
                  v-model="editForm.storeName.thirdLan"
                  placeholder="StoreName(thirdLan)"
                  show-word-limit
                  clearable
                  :style="{width: '33%'}"
                ></el-input>
              </div>
            </el-form-item>
            <el-form-item label="Store Type">
              <el-select
                style="width: 100%"
                v-model="editForm.storeType"
                multiple
                collapse-tags
                placeholder="please select store type"
              >
                <el-option
                  v-for="item in storeTypeList"
                  :key="item.tag_name"
                  :label="item.tag_name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Opening Dates" prop="openingDates">
              <el-date-picker
                type="daterange"
                v-model="editForm.openingDates"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="Start Date"
                end-placeholder="End Date"
                range-separator="~"
                clearable
                :style="{width: '100%'}"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="Opening Hours" prop="openingHours">
              <el-time-picker
                is-range
                v-model="editForm.openingHours"
                range-separator="~"
                start-placeholder="Start Hour"
                end-placeholder="End Hour"
                value-format="HH:mm:ss"
                :style="{width: '100%'}"
              ></el-time-picker>
            </el-form-item>
            <el-form-item label="Zone" prop="zone">
              <el-cascader
                :props="{value:'id',checkStrictly: true}"
                v-if="editDialogVisible"
                placeholder="Please select zone"
                style="width: 100%"
                v-model="editForm.zone"
                :options="zoneList"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="Address(en)">
              <el-input
                v-model="editForm.address.en"
                placeholder="Please enter address(en-US)"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Address(zh)">
              <el-input
                v-model="editForm.address.zh"
                placeholder="Please enter address(zh-HK)"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Address(thirdLan)">
              <el-input
                v-model="editForm.address.thirdLan"
                placeholder="Please enter address(thirdLan)"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Dine-in Timeout Prompt(en)">
              <el-input
                v-model="editForm.timeoutPrompts.en"
                placeholder="Please enter timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Dine-in Timeout Prompt(zh)">
              <el-input
                v-model="editForm.timeoutPrompts.zh"
                placeholder="Please enter timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Dine-in Timeout Prompt(thirdLan)">
              <el-input
                v-model="editForm.timeoutPrompts.thirdLan"
                placeholder="Please enter timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Takeaway Timeout Prompt(en)" prop="takeawayTimeoutPrompts.en">
              <el-input
                v-model="editForm.takeawayTimeoutPrompts.en"
                placeholder="Please enter takeaway timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Takeaway Timeout Prompt(zh)" prop="takeawayTimeoutPrompts.zh">
              <el-input
                v-model="editForm.takeawayTimeoutPrompts.zh"
                placeholder="Please enter takeaway timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="Takeaway Timeout Prompt(thirdLan)"
              prop="takeawayTimeoutPrompts.thirdLan"
            >
              <el-input
                v-model="editForm.takeawayTimeoutPrompts.thirdLan"
                placeholder="Please enter takeaway timeout prompt"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item label="LocationCoordinates" prop="locationCoordinates">
              <el-input
                v-model="editForm.locationCoordinates"
                placeholder="Format : latitude,longitude (Use , to seperate multiple data)"
                clearable
                @input="editForm.locationCoordinates=editForm.locationCoordinates.replace(/\s+/g,'')"
              ></el-input>
            </el-form-item>
            <el-form-item label="Price Group">
              <el-select v-model="editForm.group" placeholder="please choose" style="width: 100%">
                <el-option
                  v-for="groupItem in groupData"
                  :key="groupItem"
                  :label="groupItem"
                  :value="groupItem"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Desc(en)" prop="storeDesc.en">
              <el-input
                type="textarea"
                v-model="editForm.storeDesc.en"
                placeholder="Description of the store"
              ></el-input>
            </el-form-item>
            <el-form-item label="Desc(zh)" prop="storeDesc.zh">
              <el-input
                type="textarea"
                v-model="editForm.storeDesc.zh"
                placeholder="Description of the store"
              ></el-input>
            </el-form-item>
            <el-form-item label="Desc(thirdLan)" prop="storeDesc.thirdLan">
              <el-input
                type="textarea"
                v-model="editForm.storeDesc.thirdLan"
                placeholder="Description of the store"
              ></el-input>
            </el-form-item>
            <el-form-item label="Redirect Url" prop="redirectUrl">
              <el-input
                type="text"
                v-model="editForm.redirectUrl"
                placeholder="Start with http/s"
              ></el-input>
            </el-form-item>
            <el-form-item label="Takeaway Usable" prop="takeawayUsable">
              <el-switch
                v-model="editForm.takeawayUsable"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
            </el-form-item>
            <el-form-item label="FoodCourt Usable" prop="foodCourtUsable">
              <el-switch v-model="editForm.foodCourtUsable"></el-switch>
            </el-form-item>
            <el-form-item label="Usable" prop="usable">
              <el-switch
                v-model="editForm.usable"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
            </el-form-item>
          </el-form>
          <div slot="footer">
            <el-button @click="editDialogVisible = false">Cancel</el-button>
            <el-button type="primary" @click="handleEditConfirm">Edit</el-button>
          </div>
        </el-dialog>
      </template>
    </div>
    <script>
      let coordRegExp =
        /^([-+]?([1-8]?\d(\.\d+)?|90(\.0+)?)),\s*([-+]?(180(\.0+)?|(1[0-7]\d|\d{1,2})(\.\d+)?))$/
      let urlRegExp =
        /^(?:(?:https?|ftp):\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i
      function validatorURL(rule, value, callback) {
        value = value.trim()
        if (!value) callback()
        try {
          new URL(value)
          callback()
        } catch {
          callback(new Error("Irregular url"))
        }
        // if (urlRegExp.test(value)) {
        //   callback()
        // } else {
        //   callback(new Error("Irregular url"))
        // }
      }
      var app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          tableData: [],
          addDialogVisible: false,
          editDialogVisible: false,
          btnLoading: false,
          domain: sessionStorage.getItem("domain"),
          expandTableColumn: [
            {
              label: "Zone",
              prop: "zone"
            },
            {
              label: "StoreType",
              prop: "storeType"
            },
            {
              label: "Address(en-US)",
              prop: "address.en"
            },
            {
              label: "Address(zh-HK)",
              prop: "address.zh"
            },
            {
              label: "Address(thirdLan)",
              prop: "address.thirdLan"
            },
            {
              label: "Dine-in Timeout Prompts(en-US)",
              prop: "timeoutPrompts.en"
            },
            {
              label: "Dine-in Timeout Prompts(zh-HK)",
              prop: "timeoutPrompts.zh"
            },
            {
              label: "Dine-in Timeout Prompts(thirdLan)",
              prop: "timeoutPrompts.thirdLan"
            },
            {
              label: "Takeaway Timeout Prompt(en-US)",
              prop: "takeawayTimeoutPrompts.en"
            },
            {
              label: "Takeaway Timeout Prompt(zh-HK)",
              prop: "takeawayTimeoutPrompts.zh"
            },
            {
              label: "Takeaway Timeout Prompt(thirdLan)",
              prop: "takeawayTimeoutPrompts.thirdLan"
            },
            {
              label: "Price group",
              prop: "group"
            },
            {
              label: "Redirect Url",
              prop: "redirectUrl"
            }
          ],
          tableColumn: [
            {
              label: "Store Number",
              prop: "storeNumber",
              width: "130",
              fixed: false
            },
            {
              label: "Store Name",
              prop: "storeName",
              fixed: false
            },
            {
              label: "Opening Dates",
              prop: "openingDates",
              width: "",
              fixed: false
            },
            {
              label: "Opening Hours",
              prop: "openingHours",
              width: "",
              fixed: false
            },

            {
              label: "LocationCoordinates",
              prop: "locationCoordinates",
              fixed: false
            },
            {
              label: "Usable",
              prop: "usable",
              width: "80",
              fixed: false
            },
            {
              label: "Takeaway",
              prop: "takeawayUsable",
              width: "100",
              fixed: false
            },
            {
              label: "FoodCourt",
              prop: "foodCourtUsable",
              width: "100",
              fixed: false
            }
          ],
          zoneList: [],
          storeTypeList: [],
          groupData: [],
          addForm: {
            storeNumber: "",
            zone: [],
            openingDates: [],
            openingHours: null,
            locationCoordinates: "",
            storeType: [],
            group: "",
            takeawayUsable: true, //控制是否在外卖模式启用该店铺
            foodCourtUsable: false, //控制是否在美食广场模式启用该店铺
            usable: true,
            storeName: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            address: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            timeoutPrompts: { en: "", zh: "", thirdLan: "" }, //堂食模式超时提示语
            takeawayTimeoutPrompts: {
              en: "",
              zh: "",
              thirdLan: ""
            }, //外卖模式超时提示语
            storeDesc: {
              en: "",
              zh: "",
              thirdLan: ""
            }, //店铺描述
            redirectUrl: "" //takeaway重定向url
          },
          editForm: {
            storeNumber: undefined,
            zone: [],
            openingDates: [],
            openingHours: null,
            locationCoordinates: undefined,
            storeType: [],
            group: "",
            takeawayUsable: null,
            foodCourtUsable: null,
            usable: null,
            storeName: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            address: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            timeoutPrompts: { en: "", zh: "", thirdLan: "" },
            takeawayTimeoutPrompts: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            storeDesc: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            redirectUrl: ""
          },
          rules: {
            storeNumber: [
              {
                required: true,
                message: "Please enter StoreNumber ",
                trigger: "change"
              }
            ],
            locationCoordinates: [
              {
                required: false,
                message: "Please enter a valid latitude and longitude",
                trigger: "blur"
              },
              { pattern: coordRegExp, message: "Please enter a valid latitude and longitude" }
            ],
            redirectUrl: [
              {
                validator: validatorURL,
                trigger: "change"
              }
            ]
          }
        },
        created() {
          this.getData()
        },
        mounted() {
          // 页面加载的时候设置table的高度
          this.tableHeight = this.getTableHeight(this, 0, null)
          // this.tableHeight = 700
          // 页面大小该变的时候（缩放页面）设置table的高度（可加可不加）
          // window.onresize = () => {
          //   this.tableHeight = this.getTableHeight(this, 40, 'headerBox')
          //   console.log(this.tableHeight, '触发后')
          // }
        },
        computed: {
          //模板显示时间日期
          showDataOrTime: () => (r, t) => {
            if (["openingHours", "openingDates"].includes(t) && r) {
              if (r instanceof Array && r.length === 2) {
                return r.toString().replace(",", "~")
              }
            } else if (["storeName"].includes(t) && typeof r === "object") {
              let str = ""
              for (const key in r) {
                str += r[key] && r[key] + " | "
              }
              str = str.substring(0, str.lastIndexOf("|"))
              return str
            } else {
              return r
            }
          },
          handleZoneView: that => (r, t) => {
            if (t === "zone") {
              const { zone1, zone2, zone3 } = r
              let res = that.findZoneName([zone1, zone2, zone3])
              return res
            } else if (t.includes(".")) {
              const arr = t.split(".")
              let propValue = r
              for (let prop of arr) {
                if (propValue.hasOwnProperty(prop)) {
                  propValue = propValue[prop]
                } else {
                  propValue = ""
                  break
                }
              }
              return propValue
            } else if (t === "storeType") {
              let { storeType } = r
              let storeArr = storeType.split(",")
              let str = ""
              storeArr.forEach(el => {
                let res = that.storeTypeList.find(item => item.id == el)
                str += (res?.tag_name && res.tag_name + " , ") || ""
              })
              return str.substring(0, str.lastIndexOf(","))
            } else {
              return r[t]
            }
          }
        },
        methods: {
          //根据zone的id反向查找name
          findZoneName(zoneList) {
            if (zoneList[0] !== null) {
              let f, s, t
              f = this.zoneList.find(f1 => f1.id === zoneList[0])
              if (zoneList[1] !== null) {
                s = f?.children.find(s1 => s1.id === zoneList[1])
                if (zoneList[2] !== null) {
                  t = s?.children.find(t1 => t1.id === zoneList[2])
                }
              }
              // return `${f?.name} - ${s?.name || ''} - ${t?.name || ''}`
              return (
                `${(f?.name && f.name + " -") || ""}` +
                `${(s?.name && s.name + " -") || ""}` +
                `${(t?.name && t.name) || ""}`
              )
            }
          },
          //格式化列表,,,将name转换为label,id转换为value
          formatZoneData(list = []) {
            return list.map(item => {
              let newItem = {
                id: item.id,
                label: item.name.en
              }
              if (item.children && item.children.length > 0) {
                newItem.children = this.formatZoneData(item.children)
              }
              return newItem
            })
          },
          //递归替换对象中的属性x为y
          replace(obj, x, y) {
            if (typeof obj === "object") {
              for (let key in obj) {
                if (key === x) {
                  obj[y] = obj[x]
                  delete obj[x]
                } else {
                  this.replace(obj[key], x, y)
                }
              }
            }
          },
          /**
           * 转换list为zone1,zone2,zone3,
           * @param form addForm\editForm
           * @param merge Boolean
           * @return merge?Array:Object
           * @use 提交请求前use,在onEdit时use
           * */
          formatZoneList(form, merge) {
            if (merge) {
              const { zone1, zone2, zone3 } = form
              const a = [zone1, zone2, zone3]
              let arr = []
              a.forEach(i => {
                if (i !== undefined && i != null) {
                  arr.push(i)
                }
              })

              return arr
            } else {
              const { zone = [] } = form
              let obj = {}
              zone.forEach((e, i) => {
                obj[`zone${i + 1}`] = e
              })
              return obj
            }
          },
          // 校验zone1/2/3是否在原列表中对应存在,返回存在的zone1/2/3
          checkValidOfZone(zoneList) {
            let zoneArr = zoneList.filter(e => e)
            const matchIds = (res, tz, level = 0, matchedIds = []) => {
              if (level >= tz.length) {
                return matchedIds
              }
              const targetId = tz[level]
              for (const item of res) {
                if (item.id === targetId) {
                  matchedIds.push(targetId)
                  if (item.children) {
                    return matchIds(item.children, tz, level + 1, matchedIds)
                  }
                }
              }
              return matchedIds
            }
            return matchIds(this.zoneList, zoneArr)
          },
          getData() {
            $.get({
              url: "../../manager_storeNumber/getStoreNumberData",
              dataType: "json",
              data: { domain: this.domain },
              success: res => {
                if (res.statusCode === 200) {
                  let { StoreNumberData, zoneData, groupData, storeTypeData } = res.data
                  this.tableData = StoreNumberData
                  this.zoneList = this.formatZoneData(zoneData)
                  console.log(this.formatZoneData(zoneData), 89)
                  this.groupData = groupData
                  this.storeTypeList = storeTypeData
                } else {
                  this.$message.error("Request failed, please try again")
                }
              },
              error: error => {
                console.log(error, "error")
                this.$message.error("Request failed, please try again")
              }
            })
          },
          // 过滤假值
          // fillerVal(tetsObj) {
          //   if (!tetsObj) return {}
          //   let filterPam = {}
          //   for (let i in tetsObj) {
          //     if (tetsObj[i]) {
          //       filterPam[i] = tetsObj[i]
          //     }
          //   }
          //   return filterPam
          // },
          resetForm(formName) {
            this.$refs[formName].resetFields()
          },

          /**
           * table的max-height高度
           * @param that，解决this指向问题
           * @param val，固定的站位高度（例如：分页高度）
           * @param name，动态站位的高度获取的name（例如：查询动态的查询条件）
           * @returns {number}，返回可用的高度
           * @param fixHeight,最外层样式高度
           */
          getTableHeight(that, val = 32, name) {
            let fixHeight = 16 // padding为8,8
            let searchFormHeight = name ? that.$refs[name].clientHeight : 0 // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - 40) // 计算完之后剩余table可用的高度
            return tableHeight
          },

          //新增确认按钮
          handleAddConfirm() {
            this.$refs["addForm"].validate(valid => {
              if (!valid) return
              console.log(JSON.parse(JSON.stringify(this.addForm)), "this.addForm")
              this.btnLoading = true
              let data = {
                ...this.addForm,
                domain: this.domain,
                ...this.formatZoneList(this.addForm, false)
              }
              data.storeType = data.storeType.join(",")
              delete data.zone
              $.post({
                url: "../../manager_storeNumber/addOne",
                dataType: "json",
                contentType: "application/json", //默认的编码方式
                data: JSON.stringify(data),
                // traditional: true, // 防止深度序列号
                success: res => {
                  // console.log(res, "添加成功")
                  if (res.statusCode == 200) {
                    this.getData()
                    // console.log(res);
                    this.$message.success("Successfully added！")
                    this.addDialogVisible = false
                  } else {
                    switch (res.statusCode) {
                      case 401:
                        this.$message.error("Data for this store already exists！")
                        break
                      default:
                        this.$message.error("Fail to add！")
                        break
                    }
                  }
                },
                error: error => {
                  // console.log(res);
                  this.$message.error("Fail to add！")
                },
                complete: () => {
                  this.btnLoading = false
                }
              })
            })
          },
          handleDataFormat(r) {
            return JSON.parse(r)
          },
          onEdit(index, row) {
            let sName = JSON.parse(JSON.stringify(row.storeName))
            let sAddress = JSON.parse(JSON.stringify(row.address))
            let sTimeoutPrompt = row.timeoutPrompts
              ? JSON.parse(JSON.stringify(row.timeoutPrompts))
              : { en: "", zh: "", thirdLan: "" }
            // 处理storeType有效数据,
            let effectStoreList = row.storeType
              ?.split(",")
              .map(el => {
                return this.storeTypeList.find(item => item.id == el) && parseInt(el)
              })
              ?.filter(el => el)
            this.editForm = {
              ...this.addForm,
              ...row,
              storeName: {
                en: sName.en,
                zh: sName.zh,
                thirdLan: sName.third || sName.thirdLan
              },
              zone: this.checkValidOfZone(this.formatZoneList(row, true)),
              address: {
                en: sAddress.en,
                zh: sAddress.zh,
                thirdLan: sAddress.third || sAddress.thirdLan
              },

              timeoutPrompts: sTimeoutPrompt,
              storeType: effectStoreList || [],
              openingHours: row.openingHours.length == 0 ? null : row.openingHours
            }

            console.log(this.editForm, "editForm")
            this.editDialogVisible = true
          },
          onDel(index, row) {
            let data = {
              id: row.id,
              domain: this.domain
            }
            $.post({
              url: "../../manager_storeNumber/deleteOne",
              data: JSON.stringify(data),
              contentType: "application/json", //默认的编码方式
              dataType: "json",
              success: res => {
                if (res.statusCode == 200) {
                  this.getData()
                  // console.log(res, "删除后请求")
                  this.$message.success("Successfully delete!")
                  this.addDialogVisible = false
                } else {
                  this.$message.error("Fail to add！")
                }
              },
              error: res => {
                this.$message.error("Fail to delete!")
              }
            })
          },
          onClose(formName) {
            this.$refs[formName].resetFields()
            if (formName == "addForm") {
              this.addForm.openingHours = null
              this.addForm.storeName = { en: "", zh: "", thirdLan: "" } //此项没有prop绑定
              console.log(this.addForm, "关闭手动重置addForm")
            }
          },
          handleEditConfirm() {
            this.$refs["editForm"].validate(valid => {
              if (!valid) return
              let data = {
                ...this.editForm,
                domain: this.domain
              }
              delete data.zone1
              delete data.zone2
              delete data.zone3
              data = { ...data, ...this.formatZoneList(this.editForm, false) }
              data.storeType = data.storeType.join(",")
              delete data.zone
              $.post({
                url: "../../manager_storeNumber/updateOne",
                data: JSON.stringify(data),
                dataType: "json",
                contentType: "application/json", //默认的编码方式
                // traditional: true, // 防止深度序列号
                success: res => {
                  if (res.statusCode == 200) {
                    this.getData()
                    this.$message.success("Edit Success！")
                    this.editDialogVisible = false
                  } else {
                    this.$message.error("Edit Failure！")
                  }
                },
                error: error => {
                  this.$message.error("Edit Failure！")
                }
              })
            })
          },
          // 开关触发
          onEditSwitch($event, row) {
            let data = {
              ...row,
              domain: this.domain
            }
            $.post({
              url: "../../manager_storeNumber/updateOne",
              data: JSON.stringify(data),
              dataType: "json",
              contentType: "application/json", //默认的编码方式
              success: res => {
                if (res.statusCode != 200) {
                  this.$message.error("Edit failure！")
                } else {
                  console.log("开关修改", res)
                  this.getData()
                  this.$message.success("Edit success！")
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Edit failure！")
              }
            })
          },
          openDialog(dialogRef) {
            this.$nextTick(() => {
              const dialogRef = this.$refs.dialogRef
              dialogRef.scrollIntoView({ behavior: "smooth", block: "start" })
            })
          }
        }
      })
    </script>
  </body>
</html>
