.coupons-view {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.coupons__header {
  position: sticky;
  left: 0;
  top: 0;
  background-color: #ffffff;
  z-index: 1;
}
.coupons__header-title-wrap {
  height: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /*border-bottom: 1px solid #cccdcf;*/
  padding: 0 0.2rem;
}
.coupons__header-title-wrap > * {
  min-width: 1rem;
}
.refresh-coupon__btn {
  font-size: 0.7rem;
  text-align: center;
}
.coupons__header-title {
  font-size: 0.4rem;
  text-align: center;
  flex: 1;
}
.coupons__tab {
  width: 100%;
  height: 1rem;
}
.tab-bar-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 0 0.5rem;
  justify-content: space-around;
  align-items: center;
}
.tab-bar-wrapper > div {
  display: flex;
  align-items: center;
  height: 100%;
  min-width: 3em;
  justify-content: center;
}
.tab-bar-wrapper > .active {
  position: relative;
}
.active::before {
  position: absolute;
  content: "";
  bottom: 0;
  width: 3em;
  height: 2px;
  border-radius: 1px;
  background-color: #409eff;
}
.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
}
.empty-content span {
  font-size: 0.4rem;
  margin-top: 3rem;
  font-weight: 400;
  color: #ccc;
}
.coupons-list {
  color: #000;
  padding: 0 0.5rem;
}
.coupon-item {
  width: 100%;
  /*height: 110px;*/
  display: flex;
  align-items: center;
  flex-direction: column;
  border-radius: 10px;
  color: currentColor;
  margin: 10px 0;
  background-color: #fff;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
  box-shadow: rgba(33, 35, 38, 0.1) 0px 10px 10px -10px;
  position: relative;
  overflow: hidden;
}
.available .coupon-item .coupon-wrap-top {
  height: 2.5rem;
}
.coupons-list:not(.available) .coupon-item::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(204, 204, 204, 0.3);
}
.coupon-wrap-top {
  display: flex;
  align-items: center;
  width: 100%;
  height: 2rem;
  padding-left: 1em;
}
.coupon-wrap-top .qrcode-btn {
  padding: 0.2rem 0.4rem;
  margin-right: 1em;
}
.coupon-wrap-bottom {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 1rem;
  border-top: 1px dotted #cccccc;
}
.unavailable-cause {
  padding-left: 1.5em;
  position: relative;
  text-indent: 0.5em;
}
.unavailable-cause::before {
  position: absolute;
  left: 0;
  content: "•";
  color: #409eff;
  font-size: 20px;
  display: flex;
  align-items: center;
  height: 100%;
}
.coupon-info {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  flex-shrink: 0;
  padding-top: 0.2rem;
}
.coupon-info .coupon-info_title {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 0.4rem;
}

.coupon-info .coupon-use-terms {
  height: fit-content;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0.2rem 0;
  color: #5a6577;
}
.coupon-use-terms {
  font-size: 0.33rem;
}
.coupon-use-limit {
  width: 2.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.coupons-list.available .coupon-use-limit {
  display: none;
}
.coupon-use-limit > div {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  font-size: 2em;
  padding-bottom: 0.2rem;
}
.coupon-use-limit .coupon-limit-detail {
  height: 0.7rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.coupon-control {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}
.coupons-list:not(.available) .coupon-control {
  display: none;
}
.qrcode-btn {
  background-color: #409eff;
  color: white;
  border-radius: 12px;
  padding: 0.1rem 0.3rem;
  box-sizing: content-box;
  margin-right: 5px;
  border: none;
}
.qrcode-btn:active {
  opacity: 0.7;
}
.qrcode-fn-co {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-content {
  margin: 1rem 0;
}
.qrcode-info {
  font-size: 0.35rem;
  padding: 0 1.3rem;
  text-align: center;
}
.rotate {
  display: inline-block;
  animation-name: rotating;
  animation-duration: 1s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
#personalCenterPopup .bottom-sheet__content {
  background: linear-gradient(20deg, #f0f6ff 0%, #ffffff 100%);
}
.bottom-sheet-footer .close-btn {
  /* width 跟随会员主页的底部按钮宽度,padding为1.9rem, */
  width: calc(100% - 1.9rem);
  display: inline-block;
  font-weight: 400;
  /*line-height: 1.5;*/
  color: #212529;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  color: #fff;
  background-color: #409eff;
  border-radius: 0.18rem;
  margin: 0 auto;
  color: #fff;
  font-size: 0.38rem;
  min-height: 42px;
}
.sheet-content {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.bottom-sheet-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-wrapping {
  width: 100%;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
}
.line-md--loading-loop {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-dasharray='16' stroke-dashoffset='16' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 3c4.97 0 9 4.03 9 9'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' dur='0.2s' values='16;0'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.double-line {
  word-break: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
