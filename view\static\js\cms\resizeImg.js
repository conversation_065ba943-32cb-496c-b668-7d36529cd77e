const ResizeImg = {
  props: {
    wrap_bcg_url: Array,
    logo_url: [Array, String],
    rect: {
      type: Object,
      default: () => {
        return {
          left: "0%",
          top: "0%",
          width: "30%"
        }
      }
    }
  },
  emits: ["rect"],
  data() {
    return {
      preview: true,
      interactInstance: null,
      logoUrl: "",
      defaultRect: {
        left: "0%",
        top: "0%",
        width: "30%"
      }
    }
  },
  created() {
    this.rect = Object.assign({}, this.defaultRect, this.rect)
  },
  mounted() {
    this.$nextTick(() => {
      this.render()
    })
  },
  methods: {
    render() {
      // this.$refs.wrap_ref.style.backgroundImage = `url("${this.wrap_bcg_url}")`
      this.logoUrl = typeof this.logo_url === "string" ? this.logo_url : this.logo_url[0]?.url || ""
      if (this.wrap_bcg_url.length) {
        let url = this.wrap_bcg_url[0].split("?")[0]
        url = encodeURI(url)
        url = url.replaceAll("*", "%2A") // background img url include '*' well be error
        this.$refs.wrap_ref.setAttribute("style", `--bg-image:url(${url})`)
      }
    },
    // 改为resize模式
    resize() {
      this.preview = false
      this.$nextTick(() => {
        this.mountInteract()
      })
    },
    // 挂载interact插件
    mountInteract() {
      this.interactInstance = interact(".resize img")
      this.interactInstance
        .draggable({
          inertia: true,
          modifiers: [
            interact.modifiers.restrictRect({
              restriction: "parent",
              endOnly: false
            })
          ],
          listeners: {
            move: this.dragMoveListener,
            end(event) {}
          }
        })
        .resizable({
          edges: { top: false, left: false, bottom: false, right: true },
          listeners: {
            move: function (event) {
              let left = event.rect.left
              let x = event.page.x
              let width = x - left
              // 最小20px
              if (width >= 20 && width <= 300) {
                event.target.width = width
              }
            }
          }
        })
    },
    destroyInteract() {
      this.interactInstance.unset()
    },
    dragMoveListener(event) {
      let target = event.target
      let x = (parseFloat(target.getAttribute("data-x")) || 0) + event.dx
      let y = (parseFloat(target.getAttribute("data-y")) || 0) + event.dy
      target.style.transform = "translate(" + x + "px, " + y + "px)"
      target.setAttribute("data-x", x)
      target.setAttribute("data-y", y)
    },
    /*
     * 点击确认按钮后，将img的位置和宽度转为百分比
     * @opt 将top的值根据宽度转为百分比,但是需要特殊处理(current ,index)
     * */
    confirmResize() {
      let img = this.$refs.wrap_ref.querySelector("img")
      // 获取img相对于wrap_ref的left和top(x,y)
      let wrap_rect = this.$refs.wrap_ref.getBoundingClientRect()
      let img_rect = img.getBoundingClientRect()
      let x = img_rect.left - wrap_rect.left
      let y = img_rect.top - wrap_rect.top
      // 获取wrap_ref的宽度和高度
      let wrap_width = this.$refs.wrap_ref.offsetWidth
      let wrap_height = this.$refs.wrap_ref.offsetHeight
      //  获取img的宽度
      let img_width = img.offsetWidth
      // 将x,y和 img_width转为 百分比
      let left = ((Math.abs(x) / wrap_width) * 100).toFixed(1) + "%"
      let top = ((Math.abs(y) / wrap_height) * 100).toFixed(1) + "%"
      let width = ((img_width / wrap_width) * 100).toFixed(1) + "%"
      this.$emit("rect", { left, top, width })
      this.destroyInteract()
      this.preview = true
      this.$nextTick(() => {
        img.style.transform = "unset"
        this.interactInstance = null
      })
    }
  },
  template: `
        <div ref="wrap_ref" class="preview img_wrap upload-demo"  @click="resize" v-if="preview">
          <img
            :src="logoUrl"
            alt="logo"
            :width="rect.width"
            :style="{
            left: rect.left,
            top: rect.top,
            }"
          />
        </div>
       <div class="wrap" @click.stop v-else>
        <div ref="wrap_ref" class="resize img_wrap" @click="resize">
          <img
            :src="logoUrl"
            alt="logo"
            :width="rect.width"
            :style="{
            left: rect.left,
            top: rect.top,
            }"
          />
<!--           <button @click.stop="confirmResize" v-if="!preview" class="confirm-resize">confirm</button> -->
          <div @click.stop="confirmResize" v-if="!preview" class="confirm-resize">
          <slot/>
          </div>
        </div>
      </div>
`
}
function findBgiUrl(allData, params) {
  let { storeNumber } = params
  let target = allData.find(
    el =>
      el.storeNumber === storeNumber &&
      el.typeName === "Background Image" &&
      el.fileName === "indexBcg" &&
      el.logoUrl.length
  )
  if (!target) {
    target =
      allData.find(
        el =>
          el.storeNumber === "*" &&
          el.typeName === "Background Image" &&
          el.fileName === "indexBcg" &&
          el.logoUrl.length
      ) || {}
  }
  return target.logoUrl || ""
}
