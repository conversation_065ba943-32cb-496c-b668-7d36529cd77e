// 预约时间拦截器弹窗,若为TakeAway模式且预约日期时间不匹配则弹窗显示
// @touchstart="contentTouchStart"
// @touchmove="contentTouchMove"
// @touchend="contentTouchEnd"

const FoodComponent = {
  props: ["foodList", "clickLayout", "contentWarpStyle", "isActionDisabled"],
  computed: {
    contentWarpLayoutclass() {
      let map = ["zeroLayout", "oneLayout", "twoLayout", "threeLayout", "sixLayout"]
      return map[+app.tabData.display_column] || ""
    }
  },

  methods: {
    displayMemberFood(item) {
      return app.displayMemberFood(item)
    },
    showOutTimeTips() {
      app.showOutTimeTips()
    },
    additem(event, item) {
      app.additem(event, item)
    },
    showAddCartBtn(item) {
      return app.showAddCartBtn(item)
    },
    onfoodInfo(item) {
      app.onfoodInfo(item)
    },
    imgTagUrl(item, extraPaths) {
      return app.imgTagUrl(item, extraPaths)
    },
    inListTitle(item) {
      return app.inListTitle(item)
    },
    configureImgUrl(e) {
      return app.configureImgUrl(e)
    },
    priceName(e) {
      return app.priceName(e)
    },
    showOuterPrice(e) {
      return app.showOuterPrice(e)
    },
    remaining(qty) {
      return app.systemLanguage.remainingQuantity.replace("#qty", qty)
    },
    showInventory(item) {
      return !item.itemCtrl && item.inventory && item.inventory !== Infinity
    }
  },
  template: `
<div
  :class="contentWarpStyle[clickLayout]"
>
  <template v-for="(item, index) in foodList" :key="item.fCode + index">
    <div
      class="contentCellStyle"
      v-show="displayMemberFood(item)"
      :class="{'bigFood':item.bigPictureDisplay}"
    >
      <div
        :class="['disableBox', contentWarpLayoutclass]"
        v-if="item.hasOwnProperty('isExpired')"
        @click="showOutTimeTips"
      ></div>
      <!-- 售罄 -->
      <div class="soldOut_warp" v-if="item.itemCtrl">
        <img src="../static/img/newImage/soldOut.jpg" alt="" class="soldOutLogo" />
      </div>
      <!-- 单点添加 -->
      <div
        :class="[ {addBtnZero:clickLayout==0},'addFoodBtn','baseaddFoodBtn',{'btnDisabledStyle':isActionDisabled(item)}]"
        @click="additem(event, item)"
        v-if="showAddCartBtn(item)"
      >
        +
      </div>
      <div @click="onfoodInfo(item)" class="foodTex_box">
        <div class="food_img_warp ">
          <!-- 图片标识(热门/新品)  ../static/img/newImage/hot.jpg-->
          <template v-if="item.imageTagName">
            <div :class="'img_tag_'+item.imageTagAzimuth">
              <img
                v-for="(extraPaths, index) in item.imageTagName.split(',')"
                :src="imgTagUrl(item,extraPaths)"
                alt=""
                class="img_tag"
                :key="index"
              />
            </div>
          </template>
          <!-- 食品图片 -->
          <img
            v-if="clickLayout!==0"
            v-lazy="item.imglink"
            alt=""
            class="food_img"
            :key="item.imglink"
          />
          <div class="imgBox" v-else>
            <img v-lazy="item.imglink" :key="item.imglink" alt="" class="layout1" />
          </div>
        </div>
        <!-- food名 -->
        <div :class="[clickLayout==0?'food_txtAndPrice_zero':'food_txtAndPrice_one']">        
          <div class="food_text">{{inListTitle(item)}}</div>
          <!-- 过敏源 -->
          <!-- <div
  class="allergen_warp"
  v-if="item.allergen_icons && item.allergen_icons.length != 0"
>
  <img
    v-for="(e, index) in item.allergen_icons"
    :key="index"
    :src="configureImgUrl[e]"
    alt=""
    class="allergen_Icon"
  />
</div> -->
          <!-- 价钱 -->
          <!--  -->
          <div class="price_warp" v-show="item[priceName('foodList')]">
            {{showOuterPrice(item[priceName('foodList')])}}
          </div>
         <div class="inventory-info food_text" v-if="showInventory(item)">
          <span>{{remaining(item.inventory)}}</span>  
         </div>
        </div>
      </div>
    </div>
  </template>
</div>
`
}
