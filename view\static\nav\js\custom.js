// Calling the function
$(function () {
  $(".toggle-nav").click(function () {
    toggleNavigation()
  })
})

// The toggleNav function itself
function toggleNavigation() {
  if ($("#container").hasClass("display-nav")) {
    // Close Nav
    $("#container").removeClass("display-nav")
  } else {
    // Open Nav
    $("#container").addClass("display-nav")
  }
}

// SLiding codes
$("#toggle > li > div").click(function () {
  if (false == $(this).next().is(":visible")) {
    $("#toggle ul").slideUp()
  }

  var $currIcon = $(this).find("span.the-btn")

  $("span.the-btn").not($currIcon).addClass("fa-plus").removeClass("fa-minus")

  $currIcon.toggleClass("fa-minus fa-plus")

  $(this).next().slideToggle()

  $("#toggle > li > div").removeClass("active")
  $(this).addClass("active")
})

// 刷新回显
// window.onbeforeunload = function (e) {
//   choseMenu = $("#toggle li div.active").attr("id")
//   console.log(choseMenu, "onbeforeunload")
// }
// $(window).on("beforeunload", function () {
//   let choseMenu = $("#toggle li div.active").attr("id")
//   sessionStorage.setItem("choseMenuObj", choseMenu)
//   console.log(choseMenu, "onbeforeunload")
// })
// 在页面或图像加载完成后立即发生
// $(window).on("load", function () {
//   let sessionChoseMenuObj = sessionStorage.getItem("choseMenuObj")
//   console.log(sessionChoseMenuObj, "load")
//   if (sessionChoseMenuObj) {
//     let choseMenuObj = JSON.parse(sessionChoseMenuObj)
//     let { parentClass, choseMenu } = choseMenuObj
//     if (parentClass) {
//       $(`#${choseMenu}`).trigger("click")
//       $(`.${parentClass}`).addClass("active")
//       $(`.${parentClass}`).trigger("click")
//     } else {
//       $(`#${choseMenuObj}`).addClass("active")
//       $(`#${choseMenuObj}`).trigger("click")
//     }
//     // sessionStorage.removeItem("choseMenuObj") //用完就删
//   } else {
//     $(`#homePage`).addClass("active")
//     $(`#homePage`).trigger("click")
//   }
// })
// // 点击储存选中状态
// $("#toggle li div").click(function () {
//   let choseMenu = $(this).attr("id")
//   if (choseMenu) sessionStorage.setItem("choseMenuObj", JSON.stringify(choseMenu))
// })
// $("#childToggle li a").click(function () {
//   // let choseMenu = $(this).attr("id")
//   let obj = {
//     parentClass: $(this).attr("parentClass"),
//     choseMenu: $(this).attr("id")
//   }
//   console.log(obj, "childMenu")
//   sessionStorage.setItem("choseMenuObj", JSON.stringify(obj))
// })
// 控制menue切换链接
$("#homePage").on("click", function () {
  $("#iframe").attr("src", "./menu.html")
})
$("#bcgPage").on("click", function () {
  $("#iframe").attr("src", "./advertisControl.html")
})

$("#catePage").on("click", function () {
  $("#iframe").attr("src", "./category.html")
})

$("#tablePage").on("click", function () {
  $("#iframe").attr("src", "./tablePage.html")
})
$("#ConfigPage").on("click", function () {
  $("#iframe").attr("src", "./ConfigPage.html")
})
$("#MtyListPage").on("click", function () {
  $("#iframe").attr("src", "./MtyListPage.html")
})

$("#PromotionPage").on("click", function () {
  $("#iframe").attr("src", "./PromotionPage.html")
})

$("#MergingRulesPage").on("click", function () {
  $("#iframe").attr("src", "./MergingRulesPage.html")
})
$("#VersionMenuPage").on("click", function () {
  $("#iframe").attr("src", "./VersionMenuPage.html")
})
$("#CrmMenuPage").on("click", function () {
  $("#iframe").attr("src", "./CrmMenuPage.html")
})
$("#RechargeMenuPage").on("click", function () {
  $("#iframe").attr("src", "./RechargeMenuPage.html")
})
$("#PaymentMethods").on("click", function () {
  $("#iframe").attr("src", "./Payment.html")
})
$("#PaymentHistory").on("click", function () {
  $("#iframe").attr("src", "./PayMentTablePage.html")
})

$("#ShopMaster").on("click", function () {
  $("#iframe").attr("src", "./ShopMasterPage.html")
})
$("#StopBusinessHours").on("click", function () {
  $("#iframe").attr("src", "./StopBusinessHours.html")
})

$("#HQModule").on("click", function () {
  $("#iframe").attr("src", "./HQModule.html")
})
$("#ZoneConfig").on("click", function () {
  $("#iframe").attr("src", "./ZoneConfig.html")
})
$("#StoreType").on("click", function () {
  $("#iframe").attr("src", "./StoreType.html")
})
$("#TacLog").on("click", function () {
  $("#iframe").attr("src", "./TacLog.html")
})

$("#PromoDiscountMenu").on("click", function () {
  $("#iframe").attr("src", "./PromoDiscountMenu.html")
})
$("#Localization").on("click", function () {
  $("#iframe").attr("src", "./locale.html")
})
