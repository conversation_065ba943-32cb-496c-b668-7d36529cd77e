body {
  position: absolute;
  left: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  overscroll-behavior-y: contain;
  overflow: hidden;
  margin: auto auto;
  width: 100%;
  height: 100%;
}
:root {
  --styleColor: #108b96;
}

#app {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  /*transform: scale(1);*/
}
#top-tip-loading {
  position: fixed;
  box-sizing: border-box;
  top: -65px;
  /*top: 0px;*/
  left: 0;
  width: 100%;
  min-height: 60px;
  background-color: #ffffff;
  border-top: 1px solid #c0c4cc;
  z-index: 9999;
  box-shadow: #c9c5c5 0 13px 27px -5px, rgba(0, 0, 0, 0.3) 0 8px 8px -8px;
  display: flex;
  align-items: center;
  padding: 5px 20px;
  opacity: 0;
}

#top-tip-loading .left-loading {
  width: 35px;
  height: 35px;
  position: relative;
}
.left-loading .loading-gif {
  width: 100%;
  height: 100%;
  text-align: center;
}
.left-loading .loading-gif > :first-child {
  vertical-align: super;
}
.left-loading .error-gif {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url("../../img/map/error-load.jpg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
#top-tip-loading .other-text {
  flex: 1;
  padding-left: 10px;
  font-size: 16px;
  color: var(--styleColor);
  opacity: 0.7;
}

/*全屏loading*/
#full-screen-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s;
}
#map {
  width: 100%;
  /*max-height: 90vh;*/
  /*min-height: 40vh;*/
}
.controls {
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid transparent;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 300;
  height: 29px;
  margin-top: 10px;
  outline: none;
  text-overflow: ellipsis;
  width: 90%;
  text-indent: 10px;
}

.controls:focus {
  border-color: #4d90fe;
}

.title {
  font-weight: bold;
}

#infowindow-content {
  display: none;
}

#map #infowindow-content {
  display: inline;
}

.on-marker-select-button {
  display: inline-block;
  width: -webkit-fill-available;
  padding: 3px 5px;
  /*border: 1px solid  var(--styleColor);*/
  border-radius: 6px;
  background-color: var(--styleColor);
  opacity: 0.8;
  color: #f4f4f4;
}
/*下半部分功能区域*/
.store-t {
  flex: 1;
  position: fixed;
  width: 100%;
  height: 66.777%;
  /*top: calc(100% - 500px);*/
  top: 33.333%;
}
#features {
  height: 500px;
  box-sizing: border-box;
  box-shadow: rgba(0, 0, 0, 0.1) 0 10px 50px;
  background-color: #f4f4f4;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.touch-content {
  padding: 10px 20px 0 20px;
}
/*小横杆*/
.NiJhQb-XPtOyb {
  background-color: #dadce0;
  border-radius: 50px;
  height: 4px;
  margin: 0 auto;
  width: 24px;
}
/*分店提示*/
.top-title {
  font-size: 20px;
  font-weight: 600;
  margin: 10px 0;
  color: var(--styleColor);
}
/*搜索input框*/

.search-box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}
.search-input::-webkit-input-placeholder {
  color: #cccccc;
}
.search-input:focus {
  outline: none;
}
.search-input::selection {
  background-color: var(--styleColor);
}
.search-t {
  border: 1px solid #cccccc;
  border-radius: 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px;
}
.search-input {
  height: 40px;
  flex: 1;
  font-size: 14px;
  box-sizing: border-box;
  outline: none;
  border: none;
  background-color: #f4f4f4;
  color: #000000;
  touch-action: none;
}
.search-icon {
  width: 13%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-icon > {
  width: 20px;
}
.search-btn {
  width: 15%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-btn img {
  width: 25px;
}
/*店铺区域*/
.store-c {
  width: initial;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /*pointer-events: none;*/
  touch-action: none;
  padding: 0 20px;
}
.store-top-bar {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 5px 0;
  overflow-x: auto;
  flex-direction: column;
  align-items: self-start;
}
.top-bar-list {
  display: flex;
  flex-wrap: nowrap;
  padding: 5px 0;
  /*width: 100%;*/
  /*flex: none;*/
}
.top-bar-item {
  width: max-content;
  text-align: center;
  margin-right: 20px;
}
.bar-item-text {
  font-size: 17px;
  color: #000000;
  font-weight: 600;
}
.active-bar {
  padding-bottom: 3px;
  border-bottom: 2px solid var(--styleColor);
  color: var(--styleColor);
  font-weight: 600;
}
.store-list-c {
  flex: 1;
  overflow: auto;
}
.loading-store-list {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}
.no-store-list {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--styleColor);
  opacity: 0.8;
}
/*.store-list-c::-webkit-scrollbar { width: 0 !important }*/
.store-list-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100px;
  padding: 10px 0;
  /*background-color: #cccccc;*/
  box-sizing: border-box;
  border-bottom: 1px solid #c8c8c8;
}
.item-top-tag {
  display: flex;
}
.item-top-tag-c {
  display: flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 5px;
  margin-right: 5px;
  justify-content: space-evenly;
}
.item-top-tag-icon {
  display: flex;
  align-items: center;
}
.item-top-tag-icon img {
  width: 15px;
  height: 15px;
  margin-right: 4px;
}
.item-top-tag-text {
  font-size: 12px;
}
.distance {
  font-size: 14px;
  margin-left: auto;
  margin-right: 10px;
  color: #999999;
}
/*内容区域:name address*/
.store-info-c {
  display: flex;
  flex: 1;
  box-sizing: border-box;
}
.s-info-l {
  flex: 1;
  align-self: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.s-info-l .s-name {
  font-size: 16px;
  font-weight: 600;
  display: block;
}
.s-info-l .s-address {
  display: block;
  font-size: 14px;
  color: #999c9e;
}
.s-info-r {
  width: 80px;
  height: 100%;
  /*align-self: center;*/
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.s-info-distance {
  font-size: 14px;
  text-align: center;
}
.s-info-button {
  padding: 5px 10px;
  background-color: var(--styleColor);
  border-radius: 15px;
  color: #f4f4f4;
  font-size: 14px;
  text-align: center;
  min-width: 40px;
  max-width: min-content;
  align-self: center;
}

/*map*/
.popup-bubble {
  /* Position the bubble centred-above its parent. */
  position: absolute;
  top: 0;
  left: 0;
  transform: translate(-50%, -100%);
  /* Style the bubble. */
  background-color: white;
  padding: 5px;
  border-radius: 5px;
  overflow-y: auto;
  max-height: 60px;
  box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.5);
}

/* The parent of the bubble. A zero-height div at the top of the tip. */
.popup-bubble-anchor {
  /* Position the div a fixed distance above the tip. */
  position: absolute;
  width: 100%;
  bottom: 8px;
  left: 0;
}

/* This element draws the tip. */
.popup-bubble-anchor::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  /* Center the tip horizontally. */
  transform: translate(-50%, 0);
  /* The tip is a https://css-tricks.com/snippets/css/css-triangle/ */
  width: 0;
  height: 0;
  /* The tip is 8px high, and 12px wide. */
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid white;
}

/* JavaScript will position this div at the bottom of the popup tip. */
.popup-container {
  cursor: auto;
  height: 0;
  position: absolute;
  /* The max width of the info window. */
  width: 200px;
}
.zone-bar-list-scroll-content {
  width: 100%;
  overflow: auto;
  display: flex;
}
