Vue.component("resetPassword", {
  props: ["systemLanguage", "openTable"],
  mixins: [registerAndResetMixin],
  template: `
<div class="member-warp" id="resetPasswordPopup">
  <div class="form-warp">
    <h2 class="form-title">{{ systemLanguage.resetPasswordTitle }}</h2>

    <div class="verify-method">
      <span class="switch-btn" @click="switchVerifyMethod">    
        {{ verifyMethod === 'email' ? systemLanguage.resetPasswordSwitchToPhone :
        systemLanguage.resetPasswordSwitchToEmail }} 
      </span>
    </div>

    <form class="layui-form"> 
      <div class="form-content">
        <template v-for="field in formConfig">
          <div
            :key="field.name"
            class="layui-form-item"
            :class="{'error-field': formErrors[field.name]}"
            v-show="shouldShowField(field)"
          >
            <label class="layui-form-label required" :style="{width: labelWidth + 'px'}">
              <span ref="formLabel">{{ authFormShowText('formLabel',field.name) }}</span>
            </label>

            <div class="layui-input-block" :style="{marginLeft: labelWidth + 'px'}">
              <!-- 普通输入框 -->
              <template v-if="field.type === 'input'">
                <input
                  :type="field.inputType || 'text'"
                  v-model="formData[field.name]"
                  class="layui-input"
                  :placeholder="authFormShowText('formPH',field.name)"
                  @blur="validateField(field.name)"
                />
              </template>
              <!-- 邮件验证码输入框 -->
              <template v-if="field.type === 'emailVerifyCode'">
                <div class="verify-code-wrapper">
                  <input
                    type="text"
                    v-model="formData[field.name]"
                    class="layui-input"
                    :placeholder="authFormShowText('formPH',field.name)"
                    maxlength="6"
                    @blur="validateField(field.name)"
                    @input="authFormFormatNumber($event, field.name)"
                  />
                  <button
                    type="button"
                    class="layui-btn verify-code-btn"
                    @click="getCode('email')"
                    :class="{
                    'layui-btn-disabled': emailCodeCountingDown > 0 || emailLoading
                  }"
                  >
                    <i
                      v-if="emailLoading"
                      class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"
                    ></i>
                    <span v-else>{{ emailVerifyCodeMsg }}</span>
                  </button>
                </div>
              </template>
              <!-- 电话输入框 -->
              <template v-if="field.name === 'telephone'">
                <div class="phone-input-warp">
                  <custom-select
                    :system-language="systemLanguage"
                    @select-change="authFormHandleSelectChange"
                    :lan="currentLang"
                    ref="resetPswCustomSelect"
                  />
                  <input
                    :type="field.inputType || 'text'"
                    v-model="formData[field.name]"
                    class="layui-input"
                    :placeholder="authFormShowText('formPH',field.name)"
                    @blur="validateField(field.name)"
                    @input="authFormFormatNumber($event, field.name)"
                  />
                </div>
              </template>
              <!-- 手机验证码输入框 -->
              <template v-if="field.type === 'phoneVerifyCode'">
                <div class="verify-code-wrapper phoneVerifyCode">
                  <input
                    type="text"
                    v-model="formData[field.name]"
                    class="layui-input"
                   :placeholder="authFormShowText('formPH',field.name)"
                    maxlength="6"
                    @blur="validateField(field.name)"
                    @input="authFormFormatNumber($event, field.name)"
                  />
                  <button
                    type="button"
                    class="layui-btn verify-code-btn"
                    :class="{'layui-btn-disabled': codeCountingDown > 0}"
                    @click="getCode('telephone')"
                  >
                    <i
                      v-if="telephoneLoading"
                      class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"
                    ></i>
                    <span v-else>{{ phoneVerifyCodeMsg }}</span>
                  </button>
                </div>
              </template>
              <!-- 账号列表 -->
              <!-- <template v-if="field.name === 'accountList'">
              <div class="account-list-wrapper">
                <div class="account-list-header">
                  <span class="account-list-title">
                    {{ systemLanguage.resetPasswordAccountsTittle }}
                  </span>
                  <div class="get-account-btn" @click="getAccountList">
                    {{ accountListLoading ? systemLanguage.resetPwdAccListLoad :
                    systemLanguage.rstPwdAccListRfsh }}
                  </div>
                </div>
                <div class="account-list-content">
                  <div v-if="accounts.length === 0" style="text-align: center; color: #999">
                    {{ systemLanguage.resetPwdNoAccounts }}
                  </div>
                  <div v-else v-for="account in accounts" :key="account.id" class="account-item">
                    <input
                      type="checkbox"
                      lay-skin="primary"
                      :value="account.id"
                      v-model="selectedAccounts"
                      :title="account.name"
                    />
                  </div>
                </div>
              </div>
            </template> -->
              <!-- 密码输入框 -->
              <template v-if="field.type === 'password'">
                <div
                  class="password-wrapper"
                  :class="[field.name === 'confirmPassword' ? confirmPasswordClass : '']"
                >
                  <div class="input-container">
                    <input
                      :type="passwordVisible[field.name] ? 'text' : 'password'"
                      v-model.trim="formData[field.name]"
                      class="layui-input"
                      autocomplete="off"
                      :placeholder="authFormShowText('formPH',field.name)"
                      @input="authFormOnPasswordInput($event, field.name)"
                      @blur="authFormOnPasswordBlur(field.name)"
                    />
                    <div
                      class="eyeIcon"
                      :class="[passwordVisible[field.name] ? 'icon-eye-open' : 'icon-eye-close']"
                      @click="authFormPwdVisible(field.name)"
                    ></div>
                  </div>
                </div>
              </template>

              <div class="form-item-error" :class="{'show': formErrors[field.name]}">
                {{ formErrors[field.name] }}
              </div>
            </div>
          </div>
        </template>
      </div>

      <div class="form-actions">
        <div class="submit-btn" lay-submit @click.prevent="submitForm">
          {{ systemLanguage.resetPasswordSubmit }}
        </div>
        <div class="bottom-links-reset">
          <p @click="onBlack">
            <i class="layui-icon layui-icon-left"></i>
            {{systemLanguage.resetPwdLinkText}}
          </p>
        </div>
      </div>
    </form>
  </div>
</div>







  `,
  data() {
    return {
      labelWidth: 0,
      layerIndex: null,
      currentLang: "",
      verifyMethod: "email",
      accounts: [],
      selectedAccounts: [],
      passwordVisible: {
        newPassword: false,
        confirmPassword: false
      },
      configCodeCountingDown: 60,
      codeCountingDown: 0,
      testCountingDown: 3,
      emailCodeCountingDown: 0,
      phoneVerifyCodeTimer: null,
      emailVerifyCodeTimer: null,
      telephoneLoading: false,
      emailLoading: false,
      formData: {
        // email: "<EMAIL>",
        // telephone: "***********",
        // newPassword: "********",
        // confirmPassword: "********"

        email: "",
        telephone: "",
        newPassword: "",
        confirmPassword: "",
        areaCode: "",
        phoneVerifyCode: "",
        emailVerifyCode: ""
      },
      resetForm: {},
      accountListLoading: false,
      formErrors: {},
      resetPage: false,
      // 表单配置
      formConfig: [
        {
          name: "email",
          type: "input",
          inputType: "email",
          required: function () {
            return this.verifyMethod === "email"
          },
          validator: function (value) {
            return /@/.test(value) || this.systemLanguage.formFormatEmail
          }
        },
        {
          name: "emailVerifyCode",
          type: "emailVerifyCode",
          required: function () {
            return this.verifyMethod === "email"
          },
          validator: function (value) {
            if (!value) {
              return this.systemLanguage.formReqVerifyCode
            }
            return /^\d{6}$/.test(value) || "请输入6位数字验证码"
          }
        },
        {
          name: "telephone",
          type: "telephone",
          required: function () {
            return this.verifyMethod === "telephone"
          },
          validator: function (value) {
            if (!value) {
              return this.systemLanguage.formReqPhone
            }
            return true
          }
        },
        {
          name: "phoneVerifyCode",
          type: "phoneVerifyCode",
          required: function () {
            return this.verifyMethod === "telephone"
          },
          validator: function (value) {
            if (!this.formData.telephone) {
              return true
            }
            if (!value) {
              return this.systemLanguage.formReqVerifyCode
            }
            return /^\d{6}$/.test(value) || this.systemLanguage.formFormatVerifyCode
          }
        },
        // {
        //   name: "accountList",
        //   type: "accountList",
        //   required: true
        // },
        {
          name: "newPassword",
          type: "password",
          required: true,
          validator: function (value) {
            return (
              (value.length >= 8 && value.length <= 20 && /^[a-zA-Z0-9]+$/.test(value)) ||
              this.systemLanguage.formFormatPassword
            )
          }
        },
        {
          name: "confirmPassword",
          type: "password",
          required: true,
          validator: function (value) {
            // if (!value) return this.systemLanguage.formReqPassword
            return value === this.formData.newPassword || this.systemLanguage.formPasswordMismatch
          }
        }
      ]
    }
  },
  mounted() {
    this.resetForm = JSON.parse(JSON.stringify(this.formData))
  },
  computed: {
    phoneVerifyCodeMsg() {
      return this.codeCountingDown > 0
        ? `${this.codeCountingDown}s`
        : this.systemLanguage.formGetCaptchaBtn
    },
    emailVerifyCodeMsg() {
      return this.emailCodeCountingDown > 0
        ? `${this.emailCodeCountingDown}s`
        : this.systemLanguage.formGetCaptchaBtn
    },

    confirmPasswordClass() {
      if (!this.formData.confirmPassword) return ""
      return this.formData.confirmPassword !== this.formData.newPassword ? "password-mismatch" : ""
    }
  },

  methods: {
    shouldShowField(field) {
      const commonFields = ["accountList", "newPassword", "confirmPassword"]
      const methodFields = {
        email: ["email", "emailVerifyCode"],
        telephone: ["telephone", "phoneVerifyCode"]
      }
      if (commonFields.includes(field.name)) {
        return true
      }
      return methodFields[this.verifyMethod].includes(field.name)
    },
    switchVerifyMethod() {
      this.verifyMethod = this.verifyMethod === "email" ? "telephone" : "email"
      this.formErrors = {}
      this.formData.verifyCode = ""
    },

    successCallback() {
      let { resetPasswordSuccess } = this.systemLanguage
      layer.msg(resetPasswordSuccess, {
        time: 1000,
        end: () => {
          let { email, telephone } = this.formData
          let accountData = this.verifyMethod == "email" ? email : telephone
          app.$root.$refs.personalCenter.formData = {
            account: accountData,
            password: this.formData.newPassword
          }
          layer.close(this.layerIndex)
          app.$root.$refs.personalCenter.getUserInfo()
        }
      })
    },

    validateField(fieldName) {
      const field = this.formConfig.find(f => f.name === fieldName)
      if (!field) return

      const value = this.formData[fieldName]

      // 判断字段是否必填
      const isRequired =
        typeof field.required === "function" ? field.required.call(this) : field.required

      // 如果段不是必填且值为空，则清除错误信息并返回true
      if (!isRequired && !value) {
        this.$set(this.formErrors, fieldName, "")
        return true
      }

      // 如果是必填但值为空
      if (isRequired && !value) {
        if (fieldName === "confirmPassword") {
          this.$set(this.formErrors, fieldName, this.systemLanguage.formReqPassword)
        } else if (fieldName.endsWith("VerifyCode")) {
          this.$set(this.formErrors, fieldName, this.systemLanguage.formReqVerifyCode)
        } else {
          this.$set(
            this.formErrors,
            fieldName,
            this.systemLanguage[`formReq${this.authFormCapitalize(fieldName)}`]
          )
        }
        // console.log(this.formErrors, fieldName)

        return false
      }

      // 如果有值，则进行验证
      if (field.validator && value) {
        const result = field.validator.call(this, value)
        if (result !== true) {
          this.$set(this.formErrors, fieldName, result)
          return false
        }
      }

      this.$set(this.formErrors, fieldName, "")
      return true
    },

    getCode(type = "telephone") {
      const isPhone = type === "telephone"
      const countingDown = isPhone ? this.codeCountingDown : this.emailCodeCountingDown
      const btnLoading = isPhone ? this.telephoneLoading : this.emailLoading
      if (countingDown > 0 || btnLoading) return   

      // 验证字段
      if (!this.validateField(type)) return

      // 重置验证码状态
      const resetVerifyCode = () => {
        clearInterval(isPhone ? this.phoneVerifyCodeTimer : this.emailVerifyCodeTimer)
        if (isPhone) {
          this.codeCountingDown = 0
          // this.telephoneLoading = false
        } else {
          this.emailCodeCountingDown = 0
          // this.emailLoading = false
        }
      }
      //错误逻辑
      const errorVerifyCodeCallback = err => {
        resetVerifyCode()
        this.authFormErrorVerifyCode(err, "resetPassword")
      }
      // 构建请求数据
      let { telephone, areaCode, email } = this.formData

      let data = {
        type: "resetPassword",
        language: this.currentLang
      }
      data = isPhone ? { ...data, telephone, areaCode } : { ...data, email }

      // 发送验证码请求
      let typeInterface = isPhone ? "telephoneVerification" : "emailVerification"
      let url = `${API_PATH}member/${typeInterface}`
      this[`${type}Loading`] = true
      $.post({
        url: url,
        dataType: "json",
        data: JSON.stringify(data),
        contentType: "application/json",
        headers: {
          storeNumber: sessionStorage.getItem("storeNumber")
        },
        success: res => {
          if (res.statusCode == 200) {
            // 开始倒计时
            this.authFormStartCountdown(type)
            let { getPhoneVerifyCodeSuccess, getEmailVerifyCodeSuccess } = this.systemLanguage
            layer.msg(isPhone ? getPhoneVerifyCodeSuccess : getEmailVerifyCodeSuccess)
          } else {
            errorVerifyCodeCallback(res)
          }
        },
        error: err => {
          errorVerifyCodeCallback(err)
        },
        complete: () => {
          this[`${type}Loading`] = false
          // console.log("complete", this[`${type}Loading`])
        }
      })
      // this.authFormStartCountdown(type)
    },

    submitForm() {
      let isValid = true
      this.formConfig.forEach(field => {
        if (!this.validateField(field.name)) {
          // console.log("验证失败", field.name)
          isValid = false
        }
      })
      // console.log("提交表单数据：", this.formData)

      if (!isValid) return

      var resetPasswordLoading = layer.load(2, {
        shade: [0.1, "#fff"] //0.1至0.8的值,支持0.1-1.0
      })

      let { telephone, areaCode, emailVerifyCode, phoneVerifyCode, newPassword, email } =
        this.formData
      let data = {
        language: this.currentLang,
        password: newPassword
      }
      data =
        this.verifyMethod === "telephone"
          ? {
              ...data,
              telephone,
              telephoneVerificationCode: phoneVerifyCode
            }
          : { ...data, email, emailVerificationCode: emailVerifyCode }

      $.post({
        url: `${API_PATH}member/resetPassword`,
        dataType: "json",
        data: JSON.stringify(data),
        contentType: "application/json",
        headers: {
          storeNumber: sessionStorage.getItem("storeNumber")
        },
        success: res => {
          if (res.statusCode === 200) {
            this.successCallback()
          } else {
            this.authFormErrorCallback(res, "resetPassword")
          }
        },
        error: err => {
          this.authFormErrorCallback(err, "resetPassword")
        },
        complete: () => {
          layer.close(resetPasswordLoading)
        }
      })
    },

    async getAccountList() {
      if (this.accountListLoading) return

      this.accountListLoading = true
      try {
        // 这里模拟API调用
        await new Promise(resolve => setTimeout(resolve, 10))

        // 模拟数据，实际应该从API获取
        this.accounts = [
          // { id: 1, name: "账号*************" },
          // { id: 2, name: "账号*************" },
          // { id: 3, name: "账号*************" }
        ]

        // layer.msg(this.t.getAccountSuccess)
        // 重新渲染表单
        this.$nextTick(() => {
          layui.form.render()
        })
      } catch (error) {
        console.error("获取账号列表失败:", error)
      } finally {
        this.accountListLoading = false
      }
    },

    resetValidation() {
      this.formErrors = {}
    },
    resetFormData() {
      this.formData = JSON.parse(JSON.stringify(this.resetForm))
    },
    onBlack() {
      layer.close(this.layerIndex)
    },
    initFun() {
      let { language } = this.openTable
      this.currentLang = language
    }
  }
})
