Vue.component("custom-select", {
  props: ["systemLanguage", "lan"],
  template: `
<div class="customSelect">
  <div
    class="customSelect-outlined"
    :class="{'focused': isFocused, 'has-value': selectedValue}"
    @click="toggleMenu"
  >
    <span class="customSelect-label" :class="lan">{{ systemLanguage.formLabelAreaCode }}</span>
    <span v-if="selectedValue" class="customSelect-value">+{{ selectedValue }}</span>
    <span class="customSelect-icon">▼</span>
  </div>
  <transition name="customSelect-fade">
    <div class="customSelect-menu" v-if="showMenu">
      <div
        class="customSelect-item"
        :class="{'selected': item.phone[0] === selectedValue}"
        v-for="item in regionList"
        :key="item.phone[0]"
        @click="selectItem(item)"
      >
        <span :class="'fi-'+item.code" class="fi fis"></span>
        <span>
          <strong>{{ item.name }}</strong>
          ({{ item.native }})
        </span>
        <span class="country-code">+{{ item.phone[0] }}</span>
      </div>
    </div>
  </transition>
</div>



  `,
  data() {
    return {
      isFocused: false,
      showMenu: false,
      selectedValue: "",
      regionList: [
        // {
        //   flag: "🇨🇳",
        //   name: "China (中国)",
        //   code: "+86",
        //   color: "#f5222d"
        // },
        // {
        //   flag: "🇭🇰",
        //   name: "Hong Kong (中国-香港)",
        //   code: "+852",
        //   color: "#ff4d4f"
        // },
        // {
        //   flag: "🇲🇴",
        //   name: "Macao (中国-澳门)",
        //   code: "+853",
        //   color: "#40a9ff"
        // }
      ],
      initialValue: ""
    }
  },
  mounted() {
    //判断是否需要请求地区数据
    // console.log(this.systemLanguage, "请求地区数据")
    $.getJSON(`${API_PATH}static/vuetify/region.json`, r => {
      this.regionList = r
      if (Object.values(r).length) {
        this.selectedValue = Object.values(r)[0].phone[0]
        this.initialValue = Object.values(r)[0].phone[0]
        this.$emit("select-change", Object.values(r)[0]) // 发送地区数据
      }
    })
  },
  computed: {},
  watch: {
    showMenu(newVal) {
      if (newVal) {
        // console.log("开启监听")
        document.addEventListener("click", this.handleClickOutside)
      } else {
        // console.log("关闭监听")
        document.removeEventListener("click", this.handleClickOutside)
      }
    }
  },
  methods: {
    toggleMenu(event) {
      event.stopPropagation()
      this.showMenu = !this.showMenu
      this.isFocused = !this.isFocused
    },
    selectItem(item) {
      this.selectedValue = item.phone[0]
      this.showMenu = false
      this.isFocused = false
      this.$emit("select-change", item)
    },
    handleClickOutside(event) {
      const select = this.$el
      if (!select.contains(event.target)) {
        this.showMenu = false
        this.isFocused = false
      }
    },
    resetAreaCode() {
      this.selectedValue = this.initialValue
    }
  }
})
