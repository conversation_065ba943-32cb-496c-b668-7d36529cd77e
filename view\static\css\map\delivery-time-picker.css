#deliveryTimePicker {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  display: none;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 20;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  font-size: 16px;
}
#deliveryTimePicker .picker-box {
  position: absolute;
  left: 0;
  bottom: 0;
  background-color: #fff;
  height: 35%;
  width: 100%;
  border-radius: 10px 10px 0 0;
}
.picker-box.show {
  animation: pickerBoxShow 0.3s ease-in-out;
  transform: translateY(0%);
}
.picker-box.hide {
  animation: pickerBoxHide 0.3s ease-in-out;
  transform: translateY(100%);
}
@keyframes pickerBoxShow {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0%);
  }
}
@keyframes pickerBoxHide {
  0% {
    transform: translateY(0%);
  }
  100% {
    transform: translateY(100%);
  }
}

#deliveryTimePicker .picker-box .picker-header {
  position: relative;
  height: 50px;
  line-height: 50px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
#deliveryTimePicker .picker-box .picker-header .warn-icon-btn {
  position: absolute;
  left: 10px;
  top: 10px;
  display: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #fff;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="%23e5a84b" d="M13 13h-2V7h2m0 10h-2v-2h2M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2Z"%2F%3E%3C%2Fsvg%3E');
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}
.warn-icon-btn .warn-tip-wrap {
  position: absolute;
  left: 15px;
  bottom: 48px;
  width: max-content;
  max-width: 200px;
  white-space: normal;
  word-break: break-all;
  background-image: linear-gradient(60deg, var(--styleColor) 0%, #3f3f3f 100%);
  color: #fff3cd;
  border-radius: 10px 10px 10px 0;
  line-height: initial;
  height: fit-content;
  padding: 10px;
  font-size: 12px;
  text-align: left;
}
.warn-icon-btn .warn-tip-wrap::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -13px;
  width: 0;
  height: 0;
  border-top: 15px solid var(--styleColor);
  border-right: 15px solid transparent;
}

#deliveryTimePicker .picker-box .picker-header .close-btn {
  width: 36px;
  height: 36px;
  position: absolute;
  right: 5px;
  top: 7px;
  cursor: pointer;
  transform: rotate(45deg);
}

#deliveryTimePicker .picker-box .picker-header .close-btn::before,
#deliveryTimePicker .picker-box .picker-header .close-btn::after {
  content: "";
  display: block;
  position: absolute;
  height: 20px;
  width: 20px;
}
#deliveryTimePicker .picker-box .picker-header .close-btn::before {
  border-top: 2px solid #444;
  left: 8px;
  top: 17px;
}
#deliveryTimePicker .picker-box .picker-header .close-btn::after {
  border-left: 2px solid #444;
  left: 17px;
  top: 8px;
}
#deliveryTimePicker .picker-box .picker-content {
  position: relative;
  height: calc(100% - 50px);
  font-size: 0;
}
#deliveryTimePicker .picker-box .picker-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: inline-block;
  height: 100%;
  font-size: 16px;
}
#deliveryTimePicker .picker-box .picker-content ul li {
  height: 50px;
  line-height: 50px;
  /* cursor: pointer; */
}
#deliveryTimePicker #pickerDay,
#deliveryTimePicker #pickerTime {
  height: 100%;
  vertical-align: top;
  overflow-y: auto;
}
#deliveryTimePicker #pickerDay {
  width: 35%;
  text-align: center;
  background-color: #f5f5f5;
}
#deliveryTimePicker #pickerDay li.active {
  color: #fff;
  /*background-image: linear-gradient(60deg, #96deda 0%, #50c9c3 100%);*/
  background-color: var(--styleColor, #96deda);
}
#deliveryTimePicker #pickerTime {
  width: 65%;
}
#deliveryTimePicker #pickerTime li {
  padding: 0 15px;
  position: relative;
}
#deliveryTimePicker #pickerTime li.active {
  /*color: #5accc6;*/
  color: var(--styleColor, #5accc6);
}
#deliveryTimePicker #pickerTime li.active::before {
  content: "";
  display: block;
  position: absolute;
  width: 8px;
  height: 14px;
  transform: rotate(45deg);
  border-bottom: 2px solid;
  border-right: 2px solid;
  right: 19px;
  top: 13px;
}
@media screen and (min-width: 320px) and (max-width: 375px) {
  #deliveryTimePicker .picker-box .picker-header .close-btn {
    width: 32px;
    height: 32px;
  }
  #deliveryTimePicker .picker-box .picker-header .headerTip {
    display: flex;
    justify-content: center;
    margin: 0 43px;
    font-size: 16px;
    padding: 0.2rem 0rem;
  }
  #deliveryTimePicker .picker-box .picker-header {
    line-height: unset;
  }
}
