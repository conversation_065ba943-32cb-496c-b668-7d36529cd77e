.dialog_footer {
  padding-top: 15px;
  text-align: right;
  font-size: 20px;
}

.upload-demo {
  display: inline-block;
  padding: 10px 20px 0 0;
}
.el-upload-list__item > img {
  width: auto !important;
  height: auto !important;
  max-width: 100% !important;
  max-height: 100% !important;
  display: block;
  margin: auto;
}
.el-upload__text {
  position: relative;
}
.upload-center-txt {
  line-height: normal;
  position: absolute;
  bottom: 30%;
  left: 50%;
  transform: translateX(-50%);
}
.el-upload-list__item {
  transition: none !important;
}

/* company logo img preview resize*/
.wrap {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  background-color: #ccc;
}

.img_wrap {
  position: relative;
}
.img_wrap img {
  position: absolute;
}

.preview {
  transition: filter 0.3s;
  position: relative;
  width: 100px;
  height: 145px;
  border: 1px dashed #c0ccda;
  padding-top: 0;
  transform: translateY(10px);
  background-repeat: no-repeat;
  background-size: 100% auto;
  cursor: pointer;
}
.img_wrap::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -1;
  background-image: var(--bg-image);
  background-repeat: no-repeat;
  background-size: 100% auto;
  overflow: hidden;
}
.preview::after {
  content: "Preview";
  position: absolute;
  width: 100%;
  top: 40%;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
  text-align: center;
  display: none;
  color: #fff;
}
.preview:hover {
  cursor: pointer;
}
.preview:hover::before {
  filter: blur(3px);
}
.preview:hover::after {
  display: block;
}
.preview:hover img {
  filter: blur(3px);
}

.resize {
  max-width: 312px;
  max-height: 675px;
  width: 312px;
  height: 675px;
  margin: 30px auto;
  border: 1px solid #fff;
}
.resize img {
}
.confirm-resize {
  position: absolute;
  bottom: 0;
  right: 0;
  transform: translateX(100%);
}
/* 禁用状态的上传组件样式 */
.upload-disabled .el-upload--picture-card {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

.upload-disabled .el-upload--picture-card:hover {
  border-color: #e4e7ed !important;
}

.upload-disabled .el-upload__text {
  color: #c0c4cc !important;
}

.upload-disabled .el-icon-plus {
  color: #c0c4cc !important;
}
