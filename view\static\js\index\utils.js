/**
 * @description: URLSearchParams 是一个 JavaScript 内置的 API，用于处理 URL 查询参数。通过 new URLSearchParams() 构造函数，我们可以创建一个 URLSearchParams 对象来操作 URL 查询参数，例如添加、获取、删除和修改参数等
 * @Fun append(name, value)：添加一个新的查询参数。
 * @Fun delete(name)：删除指定名称的查询参数。
 * @Fun get(name)：获取指定名称的查询参数的值。
 * @Fun set(name, value)：设置指定名称的查询参数的值。
 * ex: new URLSearchParams(location.search).get('type');
 **/

//解析url参数
const urlResolve = () => {
  let host = window.location.host
  let companyName = host.substring(0, host.indexOf("."))
  if (!companyName) {
    companyName = host.substring(host.indexOf(":"), host.indexOf("."))
  }
  let urlParamsStr = window.location.search.substring(1) //
  let url = window.location.toString()
  let urlPrefix = url.split("?")[0]
  //短link
  try {
    let effectBase64Str = urlParamsStr
    let lastParams = {}
    if (urlParamsStr.indexOf("&") !== -1) {
      //   可能带有staffMode 标识
      effectBase64Str = urlParamsStr.slice(0, urlParamsStr.indexOf("&"))
      let lastParamsStr = urlParamsStr.slice(urlParamsStr.indexOf("&"))
      lastParams = getUrlParams(lastParamsStr)
    }
    //若此处抛出错误,则默认为长link
    let _urlParamsStr = window.atob(effectBase64Str)
    let paramsObj = getUrlParams(_urlParamsStr)
    return { companyName, paramsObj, urlPrefix, ...lastParams }
  } catch (e) {
    let isLongLink = urlDistinguish(urlParamsStr) //判断是否为长link
    if (isLongLink) {
      let paramsObj = getUrlParams()
      paramsObj.storeNumber = paramsObj.shop
      paramsObj.tableNumber = paramsObj.tn
      delete paramsObj.shop
      delete paramsObj.tn
      return { companyName, paramsObj, urlPrefix }
    } else {
      console.log("link参数错误!")
      return false
    }
  }
}
const urlDistinguish = urlParamsStr => {
  //辨別url後面參數是否加密
  let verifyList = ["&", "tn", "shop"]
  return verifyList.every(el => urlParamsStr.search(el) !== -1)
}
//获取url参数
const getUrlParams = arg => {
  let obj = {}
  let str = arg || window.location.search || ""
  if (str) {
    let queryArray = []
    if (str[0] === "?") {
      // 去掉 ？ ，然后以 & 分割
      queryArray = str.slice(1).split("&")
    } else {
      queryArray = str.split("&")
    }
    queryArray.map(query => {
      // param=value 以 = 分割
      let temp = query.split("=")
      if (temp.length > 1) {
        // 收集参数
        obj[temp[0]] = temp[1]
      }
    })
  }
  return obj
}
//节流
const throttle = (func, wait, options) => {
  let context, args, result
  let timeout = null
  let previous = 0
  if (!options) options = {}
  let later = function () {
    previous = options.leading === false ? 0 : Date.now()
    timeout = null
    result = func.apply(context, args)
    if (!timeout) context = args = null
  }
  return function () {
    let now = Date.now()
    if (!previous && options.leading === false) previous = now
    let remaining = wait - (now - previous)
    context = this
    args = arguments
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      result = func.apply(context, args)
      if (!timeout) context = args = null
    } else if (!timeout && options.trailing !== false) {
      timeout = setTimeout(later, remaining)
    }
    return result
  }
}
//加载本地json
const getJson = async url => {
  return new Promise((res, rej) =>
    $.getJSON(url, data => {
      res(data)
    }).fail(e => {
      rej({ status: e.status, responseText: e.responseText, statusText: e.statusText })
    })
  )
}
//验证image url是否有效 ,返回img原始宽高比例,
const checkImageUrl = url => {
  return new Promise((resolve, reject) => {
    let timer,
      img = new Image()
    img.onerror = img.onabort = function () {
      clearTimeout(timer)
      reject(false)
    }
    img.onload = function () {
      clearTimeout(timer)
      let w = img.width
      let h = img.height
      resolve((w / h).toFixed(1))
    }
    img.src = url
  })
}
/**
 * @description: 验证图片url是否有效
 * @param {String} url url
 * @param {Array} oss [0] 默认oss [1] 备用oss
 * @param {String} type "menu" 类型:传入menu代表处理menu的图片url
 * @return {Promise<url:String,ratio:string>} 返回promise对象
 * */
const checkImage = (url = "", oss) => {
  let getOss = sessionStorage.getItem("backupOssUrl") || ""
  let [defoss, bkposs = getOss] = oss
  return new Promise((res, rej) => {
    checkImageUrl(url)
      .then(r => {
        // 第一次默认oss成功,返回url
        res({ ratio: r, url })
      })
      .catch(e => {
        // 第一次默认oss失败,则切换到备用oss
        if (url.includes(defoss) && bkposs) {
          let tryoss = url.replace(defoss, bkposs)
          checkImageUrl(tryoss)
            .then(r => {
              // 第二次备用oss成功,返回url
              res({ ratio: r, url: tryoss })
            })
            .catch(e => {
              // 失败不处理(不显示)
              countOssError(url, bkposs)
              rej(e)
            })
        } else {
          let isBack = sessionStorage.getItem("useBackupOss")
          if (!!isBack) {
            let tryoss = url.replace(bkposs, defoss)
            // 第一次备用oss失败(场景:已启用备用,刷新/切换页面后备用也挂了),则切换到默认oss
            sessionStorage.removeItem("useBackupOss")
            checkImageUrl(tryoss)
              .then(r => {
                // 第二次默认oss成功,返回url
                res({ ratio: r, url: tryoss })
              })
              .catch(e => {
                // 失败不处理(不显示)
                countOssError(url, bkposs)
                rej(e)
              })
          }
          // 默认oss失败且未配置备用oss
          rej(e)
        }
        countOssError(url, bkposs)
      })
  })
}
//  oss的错误累加次数
function countOssError(url, oss) {
  if (oss && url.indexOf(oss) === -1) {
    let count = +sessionStorage.getItem("ossErrorCount") || 0
    count += 1
    sessionStorage.setItem("ossErrorCount", count)
    // 超过1张图片在默认oss无法加载,则使用备用oss
    if (count > 0) {
      sessionStorage.setItem("useBackupOss", true)
    }
  }
}
/**
 * @description 校验oss是否有效
 * OSS挂了预期2种情况
 * 1:oss根目录可访问但所有文件404;
 * 2:oss根目录不可访问
 * so 2种情况都要处理
 * */
function checkOSS() {
  const DEFAULT_OSS = "https://appwise.oss-cn-hongkong.aliyuncs.com"
  let DOSS_ACCESSIBILITY = true
  const MAX_OSS_ERROR_COUNT = 3
  let useBackupOss = !!sessionStorage.getItem("useBackupOss")

  const checkWebsite = (oss = DEFAULT_OSS) => {
    fetch(oss, {
      method: "HEAD",
      mode: "no-cors"
    })
      .then(r => r.text())
      .then(r => {
        DOSS_ACCESSIBILITY = true
        return DEFAULT_OSS
      })
      .catch(e => {
        DOSS_ACCESSIBILITY = false
        return DEFAULT_OSS
      })
      .finally(callBack)
  }
  // 此函数仅在确认图片存在的情况下调用,若校验失败则计数累加,超过3次则更换oss
  const checkImage = url => {
    // 默认oss无法访问,则无需在校验url
    if (!DOSS_ACCESSIBILITY) return false
    // // 校验至错误上限,则无需再校验
    // if (useBackupOss) return false
    const OSS = (useBackupOss && sessionStorage.getItem("backupOssUrl")) || DEFAULT_OSS
    return checkImageUrl(OSS + url)
      .then(() => {
        //默认oss有一个成功,则为有效
        useBackupOss = false
        sessionStorage.removeItem("ossErrorCount")
      })
      .catch(e => {
        ossErrorCount()
        throw e
      })
  }
  const ossErrorCount = () => {
    if (useBackupOss) return false
    let count = +sessionStorage.getItem("ossErrorCount") || 0
    count += 1
    sessionStorage.setItem("ossErrorCount", count)
    if (count >= MAX_OSS_ERROR_COUNT) {
      useBackupOss = true
      callBack(true)
      sessionStorage.setItem("useBackupOss", true)
      sessionStorage.removeItem("ossErrorCount")
    }
  }

  // 严重副作用函数:
  // 更改Vue实例上的oss数据
  const callBack = useBackup => {
    const BACKUP_OSS = sessionStorage.getItem("backupOssUrl") || DEFAULT_OSS
    const INSTANCE_NAME = "app"
    let url = (useBackup && BACKUP_OSS) || (DOSS_ACCESSIBILITY ? DEFAULT_OSS : BACKUP_OSS)
    // default instance name: app
    if (app || window[INSTANCE_NAME]) {
      app.oss = window.app.oss = url
    } else {
      window.oss = url
    }
  }
  checkWebsite()
  return {
    checkImage
  }
}

/**
 * 0. ping 默认oss访问状况
 * 1. 拿到所有的未校验的url
 * 2. 默认oss,   备用oss
 * 3. 0_ok, 创建promise列表, 若默认有一条成功,则确认使用默认oss
 *       若超过3条失败,则默认oss无效,终止其余promise
 * 4. 0_err 有备用则使用备用校验<有缓存再渲染时不会请求>
 * */
/**
 * @description 控制promise的并发数,失败上限则终止
 *
 * 默认并发:3,默认失败次数:3
 * */
function controlPromise(promiseList) {
  const batchSize = 3
  let failedCount = 0
  let finishedCount = 0
  const maxFailedCount = 3

  function nextBatch() {
    if (finishedCount === promiseList.length) {
      return
    }
    if (failedCount >= maxFailedCount) {
      return
    }
    const run = promiseList.slice(finishedCount, finishedCount + batchSize)
    const nextBatchPromises = run.map(el => el.call(this))
    finishedCount += nextBatchPromises.length
    Promise.allSettled(nextBatchPromises).then(results => {
      for (const result of results) {
        if (result.status === "rejected") {
          failedCount++
        }
      }
      if (finishedCount === promiseList.length) {
        return
      }
      if (failedCount >= maxFailedCount) {
        return
      }
      nextBatch()
    })
  }
  nextBatch()
}

/**
 * SSE 连接管理器，支持自动重连和指数退避
 * @param {function} url - SSE 服务器的 URL
 * @param {object} options - 配置选项
 * @param {function} options.onMessage - 收到消息时的回调函数
 * @param {function} [options.onError] - 发生错误时的回调函数 (连接错误或 EventSource 的 error 事件)
 * @param {function} [options.onOpen] - 连接成功打开时的回调函数
 * @param {number} [options.initialRetryDelay=1000] - 初始重连延迟（毫秒）
 * @param {number} [options.maxRetryDelay=30000] - 最大重连延迟（毫秒）
 * @param {number} [options.maxRetryAttempts=Infinity] - 最大重连尝试次数
 * @param {number} [options.backoffFactor=2] - 指数退避因子
 */
function connectSSE(url, options) {
  const {
    onMessage,
    onError = err => console.error("SSE Error:", err),
    onOpen = () => console.log("SSE Connected."),
    initialRetryDelay = 1000, // 1 秒
    maxRetryDelay = 30000, // 30 秒
    maxRetryAttempts = Infinity,
    backoffFactor = 2,
    ...restEvent
  } = options

  let eventSource = null
  let retryAttempts = 0
  let currentRetryDelay = initialRetryDelay
  let connectTimeoutId = null // 用于 setTimeout 的 ID

  function disconnect() {
    if (connectTimeoutId) {
      clearTimeout(connectTimeoutId)
      connectTimeoutId = null
    }
    if (eventSource) {
      eventSource.close() // 关闭当前连接
      eventSource = null
      console.log("SSE connection closed.")
    }
    retryAttempts = 0 // 重置尝试次数
    currentRetryDelay = initialRetryDelay // 重置延迟
  }

  function attemptConnection() {
    if (eventSource) {
      console.warn("Attempting to connect while already connected or connecting.")
      return // 避免重复连接
    }

    // console.log(`Attempting SSE connection to ${url()} (Attempt: ${retryAttempts + 1})...`)
    eventSource = new EventSource(url())

    eventSource.onopen = event => {
      // console.log("SSE Connection established.")
      retryAttempts = 0 // 连接成功，重置计数器
      currentRetryDelay = initialRetryDelay // 重置延迟
      onOpen(event) // 调用成功回调
      // 清除可能存在的重连定时器
      if (connectTimeoutId) {
        clearTimeout(connectTimeoutId)
        connectTimeoutId = null
      }
    }

    eventSource.onmessage = event => {
      onMessage(event) // 调用消息处理回调
    }

    eventSource.onerror = err => {
      console.error("SSE Error occurred:", err)
      onError(err) // 调用错误处理回调

      // 关闭当前可能出错或已关闭的 EventSource 实例
      // 这是关键，防止浏览器默认重连和我们的逻辑冲突
      if (eventSource) {
        eventSource.close()
        eventSource = null
      }

      // 检查是否达到最大尝试次数
      if (retryAttempts >= maxRetryAttempts) {
        console.error(`SSE connection failed after ${maxRetryAttempts} attempts. Stopping retries.`)
        return
      }

      retryAttempts++
      const delay = Math.min(maxRetryDelay, currentRetryDelay)
      console.log(`SSE connection lost/failed. Retrying in ${delay / 1000} seconds...`)

      // 使用 setTimeout 实现带延迟的重连
      connectTimeoutId = setTimeout(attemptConnection, delay)

      // 更新下一次的延迟（指数退避）
      currentRetryDelay *= backoffFactor
    }
    // 动态添加侦听事件逻辑
    for (const eventName in restEvent) {
      if (!eventName.startsWith("on")) continue
      const name = lowercaseFirstLetter(eventName.slice(2))
      eventSource.addEventListener(name, restEvent[eventName])
    }
  }

  // 初始连接
  attemptConnection()

  // 返回一个包含断开连接方法
  return {
    disconnect: disconnect
  }
}

function groupBy(list, keyFn) {
  const groups = {}
  for (const item of list) {
    const key = keyFn(item)
    const group = groups[key]
    if (!group) {
      groups[key] = [item]
    } else {
      group.push(item)
    }
  }
  return groups
}
// 首字母小写
function lowercaseFirstLetter(str) {
  if (!str) {
    return str // 处理空字符串的情况
  }
  return str.charAt(0).toLowerCase() + str.slice(1)
}
window.menuEmptyImageBase64 =
  "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCACLAMgDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD+/iiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z"
window.menuLoadingImageBase64 =
  "data:image/svg+xml;base64,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"
/**
 * 将一个图片 URL 转换为 Base64 编码的 Promise
 * @param {string} url - 图片的 URL
 * @returns {Promise<string>} - Promise，解决时返回 Base64 编码字符串 (Data URL)
 * 注意：此函数内部会抛出错误，但 Promise.allSettled 会将其捕获为 rejected 状态的 reason
 */
async function imageUrlToBase64(url) {
  try {
    const response = await fetch(url)

    if (!response.ok) {
      // 如果 HTTP 状态码不是成功的范围 (2xx)，抛出错误
      throw new Error(`Failed to fetch image from ${url}, status: ${response.status}`)
    }

    const blob = await response.blob() // 将响应体转换为 Blob 对象

    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onloadend = () => resolve(reader.result) // 读取成功时解决 Promise
      reader.onerror = () => reject(new Error(`Failed to read blob as Data URL for ${url}`)) // 读取失败时拒绝 Promise
      reader.readAsDataURL(blob) // 读取 Blob 内容为 Data URL (Base64)
    })
  } catch (error) {
    console.error(`Error processing URL ${url}:`, error)
    throw error
  }
}

/**
 * 传入一个 URL 数组，加载并转换为 Base64 编码，完成后返回包含每个结果（成功或失败）的对象数组
 * @param {string[]} urls - 包含图片 URL 的数组
 * @returns {Promise<Array<{status: 'fulfilled', value: string} | {status: 'rejected', reason: any}>>}
 * - Promise，解决时返回一个包含每个 URL 处理结果（成功/失败）的对象数组，顺序与输入一致。
 */
async function urlsToBase64ArraySettled(urls) {
  if (!Array.isArray(urls) || urls.length === 0) {
    console.warn("Input is not a non-empty array of URLs.")
    return Promise.resolve([]) // 返回一个已解决的空数组 Promise
  }
  const promises = urls.map(url => imageUrlToBase64(url))
  return await Promise.allSettled(promises)
}
