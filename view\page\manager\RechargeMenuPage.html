<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../static/css/advertisControl.css" />
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .el-dialog__body {
        padding: 30px 20px 0px;
        color: #606266;
        font-size: 14px;
        word-break: break-all;
        /* max-height: 50vh; */
        overflow: auto;
      }

      .hideUpload .el-upload--picture-card {
        display: none;
      }
      .el-upload-list__item {
        transition: none !important; /* 取消过渡效果 */
      }
      .el-table th.el-table__cell > .cell {
        word-wrap: break-word;
        word-break: keep-all;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          :data="tableData"
          style="width: 100%"
          :max-height="tableHeight"
          border
          :header-cell-style="{fontSize: '15px'}"
          empty-text="No Data"
        >
          <el-table-column width="140" label="Image" align="center">
            <template slot-scope="scope">
              <img style="padding: 5px" :src="scope.row.logoUrl" alt="" />
            </template>
          </el-table-column>
          <el-table-column width="140" prop="code" label="Code" align="center"></el-table-column>
          <el-table-column prop="desc1" label="Name " align="center">
            <template slot-scope="scope">{{showName(scope.row)}}</template>
          </el-table-column>
          <el-table-column width="100" prop="upa" label="Upa" align="center"></el-table-column>
          <el-table-column
            prop="addDebitPoints"
            label="Add Debit Points"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="addMemberPoints"
            label="Add Member Points"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="addDebitExpiredDay"
            label="Add Debit Expired Day"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="addMemberExpiredDay"
            label="Add Member Expired Day"
            align="center"
          ></el-table-column>
          <el-table-column prop="switch" label="Switch" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.usable"
                @change="onSwitch($event, scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column prop label="Operation" align="center">
            <el-table-column width="100" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible = true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="handleEditor(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="handleDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 图片上传弹窗 -->
      <template>
        <el-dialog
          title="Add Dialog"
          :visible.sync="addDialogVisible"
          @closed="uploadCloseDialog('addForm')"
        >
          <el-form :model="addForm" ref="addForm" label-width="auto" :rules="rules">
            <el-form-item label="Code" prop="code">
              <el-input
                v-model.trim="addForm.code"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['code']"
                @input="(val) => addForm.code = val.toUpperCase()"
              ></el-input>
            </el-form-item>
            <el-form-item label="Override Name 1" prop="desc1">
              <el-input
                v-model.trim="addForm.desc1"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['desc1']"
              ></el-input>
            </el-form-item>
            <el-form-item label="Override Name 2" prop="desc2">
              <el-input
                v-model.trim="addForm.desc2"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['desc2']"
              ></el-input>
            </el-form-item>
            <el-form-item label="Override Name 3" prop="multi1">
              <el-input
                v-model.trim="addForm.multi1"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['multi1']"
              ></el-input>
            </el-form-item>
            <el-form-item label="Upa" prop="upa">
              <el-input-number
                :min="1"
                v-model.trim="addForm.upa"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['upa']"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="Add Debit Points" prop="addDebitPoints">
              <el-input
                v-model.number="addForm.addDebitPoints"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['addDebitPoints']"
                :disabled="pointsDisabled.debitGroup"
                @input="(val) => handlePointsInput('addDebitPoints', val, 'addForm')"
              ></el-input>
            </el-form-item>
            <el-form-item label="Add Member Points" prop="addMemberPoints">
              <el-input
                v-model.number="addForm.addMemberPoints"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['addMemberPoints']"
                :disabled="pointsDisabled.memberGroup"
                @input="(val) => handlePointsInput('addMemberPoints', val, 'addForm')"
              ></el-input>
            </el-form-item>
            <el-form-item label="Add Debit Expired Day" prop="addDebitExpiredDay">
              <el-input
                v-model.number="addForm.addDebitExpiredDay"
                style="width: 100%"
                :disabled="pointsDisabled.debitGroup"
                :placeholder="defaultPH+placeholderObj['addDebitExpiredDay']"
                @input="(val) => handleExpiredDayInput('addDebitExpiredDay', val, 'addForm')"
              >
                <template slot="append">Day</template>
              </el-input>
            </el-form-item>
            <el-form-item label="Add Member Expired Day" prop="addMemberExpiredDay">
              <el-input
                v-model.number="addForm.addMemberExpiredDay"
                style="width: 100%"
                :disabled="pointsDisabled.memberGroup"
                :placeholder="defaultPH+placeholderObj['addMemberExpiredDay']"
                @input="(val) => handleExpiredDayInput('addMemberExpiredDay', val, 'addForm')"
              >
                <template slot="append">Day</template>
              </el-input>
            </el-form-item>
            <el-form-item label="Seq" prop="seq">
              <el-input-number
                :min="0"
                v-model.trim="addForm.seq"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['seq']"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="Gift Information" prop="giftInformation">
              <el-checkbox-group
                v-model="addForm.giftInformation"
                @change="(val) => handleCheckboxChange(val, 'addForm')"
              >
                <el-checkbox
                  v-for="item in fieldOptions"
                  :key="item.value"
                  :label="item.value"
                  :disabled="disableCheckbox(item.value)"
                >
                  {{item.label}}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <!-- 开关 -->
            <el-form-item label="Switch">
              <el-switch v-model="addForm.usable" />
            </el-form-item>
            <el-form-item prop="imgList" label="Image">
              <el-upload
                class="upload-demo"
                ref="addUpload"
                :data="addForm"
                action="#"
                :on-change="handleUploadChange"
                :on-remove="handleRemove"
                :file-list="addForm.imgList"
                list-type="picture-card"
                :auto-upload="false"
                accept="image/jpeg,image/jpg"
                :limit="1"
                :class="{'hideUpload':isHideUpload}"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
          </el-form>
          <!-- 确认按钮 -->
          <div slot="footer">
            <el-button type="primary" @click="uploadCanvel('addForm')">Cancel</el-button>
            <el-button @click="submitForm('addForm')" style="margin-right: 8px">Confirm</el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 编辑弹窗 -->
      <template>
        <el-dialog
          title="Edit Dialog"
          :visible.sync="editDialogVisible"
          @closed="uploadCloseDialog('editForm')"
        >
          <el-form :model="editForm" ref="editForm" label-width="auto" :rules="rules">
            <el-form-item label="Code" prop="code">
              <el-input
                v-model.trim="editForm.code"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['code']"
                @input="(val) => editForm.code = val.toUpperCase()"
              ></el-input>
            </el-form-item>
            <el-form-item label="Override Name 1" prop="desc1">
              <el-input
                v-model.trim="editForm.desc1"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['desc1']"
              ></el-input>
            </el-form-item>
            <el-form-item label="Override Name 2" prop="desc2">
              <el-input
                v-model.trim="editForm.desc2"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['desc2']"
              ></el-input>
            </el-form-item>
            <el-form-item label="Override Name 3" prop="multi1">
              <el-input
                v-model.trim="editForm.multi1"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['multi1']"
              ></el-input>
            </el-form-item>
            <el-form-item label="Upa" prop="upa">
              <el-input-number
                :min="1"
                v-model.trim="editForm.upa"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['upa']"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="Add Debit Points" prop="addDebitPoints">
              <el-input
                v-model.number="editForm.addDebitPoints"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['addDebitPoints']"
                :disabled="pointsDisabled.debitGroup"
                @input="(val) => handlePointsInput('addDebitPoints', val, 'editForm')"
              ></el-input>
            </el-form-item>
            <el-form-item label="Add Member Points" prop="addMemberPoints">
              <el-input
                v-model.number="editForm.addMemberPoints"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['addMemberPoints']"
                :disabled="pointsDisabled.memberGroup"
                @input="(val) => handlePointsInput('addMemberPoints', val, 'editForm')"
              ></el-input>
            </el-form-item>
            <el-form-item label="Add Debit Expired Day" prop="addDebitExpiredDay">
              <el-input
                v-model.number="editForm.addDebitExpiredDay"
                style="width: 100%"
                :disabled="pointsDisabled.debitGroup"
                :placeholder="defaultPH+placeholderObj['addDebitExpiredDay']"
                @input="(val) => handleExpiredDayInput('addDebitExpiredDay', val, 'editForm')"
              >
                <template slot="append">Day</template>
              </el-input>
            </el-form-item>
            <el-form-item label="Add Member Expired Day" prop="addMemberExpiredDay">
              <el-input
                v-model.number="editForm.addMemberExpiredDay"
                style="width: 100%"
                :disabled="pointsDisabled.memberGroup"
                :placeholder="defaultPH+placeholderObj['addMemberExpiredDay']"
                @input="(val) => handleExpiredDayInput('addMemberExpiredDay', val, 'editForm')"
              >
                <template slot="append">Day</template>
              </el-input>
            </el-form-item>
            <el-form-item label="Seq" prop="seq">
              <el-input-number
                :min="0"
                v-model="editForm.seq"
                style="width: 100%"
                :placeholder="defaultPH+placeholderObj['seq']"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="Gift Information" prop="giftInformation">
              <el-checkbox-group
                v-model="editForm.giftInformation"
                @change="(val) => handleCheckboxChange(val, 'editForm')"
              >
                <el-checkbox
                  v-for="item in fieldOptions"
                  :key="item.value"
                  :label="item.value"
                  :disabled="disableCheckbox(item.value)"
                >
                  {{item.label}}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <!-- 开关 -->
            <el-form-item label="Switch">
              <el-switch v-model="editForm.usable" />
            </el-form-item>
            <el-form-item prop="imgList" label="Image">
              <el-upload
                class="upload-demo uploadEdit"
                ref="editUpload"
                :data="editForm"
                action="#"
                :on-change="handleUploadChange"
                :on-remove="handleRemove"
                :file-list="editForm.imgList"
                list-type="picture-card"
                :auto-upload="false"
                accept="image/jpeg,image/jpg"
                :limit="1"
                :class="{'hideUpload':isHideUpload}"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
          </el-form>
          <!-- 确认按钮 -->
          <div slot="footer">
            <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
            <el-button @click="submitForm('editForm')" style="margin-right: 8px">Confirm</el-button>
          </div>
        </el-dialog>
      </template>
    </div>
    <script>
      const validateOptionalNumber = (rule, value, callback) => {
        // 允许为空
        if (!value) {
          return callback()
        }
        // 有值时必须为数字
        let reg = /^[0-9]+$/
        if (!reg.test(value)) {
          return callback(new Error("Please enter the number"))
        }
        return callback()
      }
      const validateMemberAndCardEmpty = (rule, value, callback) => {
        let form = app.addDialogVisible ? app.addForm : app.editForm
        let { addDebitPoints, addMemberPoints, addDebitExpiredDay, addMemberExpiredDay } = form
        // 检查Member类别和Card类别是否同时为空
        if (
          addDebitPoints === "" &&
          addMemberPoints === "" &&
          addDebitExpiredDay === "" &&
          addMemberExpiredDay === ""
        ) {
          return callback(new Error("One item is required for points or date configurations"))
        }
        return callback()
      }

      const app = new Vue({
        el: "#app",
        data() {
          return {
            dialog: "",
            tableHeight: 0,
            screenHeight: 800,
            addDialogVisible: false,
            editDialogVisible: false,
            tableData: [],
            fileList: [],
            initForm: {},
            addForm: {
              code: "",
              desc1: "",
              desc2: "",
              multi1: "",
              seq: null, //排序
              upa: 0, //售卖价格
              addDebitPoints: "", //增加储蓄卡积分
              addMemberPoints: "", //增加会员积分
              addDebitExpiredDay: "", //储蓄卡过期日延长X天
              addMemberExpiredDay: "", //会员过期日延长X天
              usable: true,
              imgList: [],
              giftInformation: []
            },
            tempImgList: [],
            defaultPH: "Please enter ",
            editForm: {
              code: "",
              desc1: "",
              desc2: "",
              multi1: "",
              seq: null, //排序
              upa: 0, //售卖价格
              addDebitPoints: undefined, //增加储蓄卡积分
              addMemberPoints: undefined, //增加会员积分
              addDebitExpiredDay: undefined, //储蓄卡过期日延长X天
              addMemberExpiredDay: "", //会员过期日延长X天
              usable: true,
              imgList: [],
              giftInformation: []
            },

            rules: {
              code: [{ required: true, message: "Please enter the code", trigger: "blur" }],
              upa: [{ required: true, message: "Please enter the upa", trigger: "blur" }],
              addDebitPoints: [
                {
                  validator: validateMemberAndCardEmpty,
                  trigger: []
                }
              ],
              addMemberPoints: [
                {
                  validator: validateMemberAndCardEmpty,
                  trigger: []
                }
              ],
              addDebitExpiredDay: [
                {
                  validator: validateMemberAndCardEmpty,
                  trigger: []
                }
              ],
              addMemberExpiredDay: [
                {
                  validator: validateMemberAndCardEmpty,
                  trigger: []
                }
              ],
              seq: [
                {
                  validator: validateOptionalNumber,
                  trigger: "blur",
                  message: "Please enter the number"
                }
              ]
            },
            placeholderObj: {
              code: "code",
              desc1: "override name 1",
              desc2: "override name 2",
              multi1: " override name 3",
              upa: "upa",
              addDebitPoints: "add debit points",
              addMemberPoints: "add member points",
              addDebitExpiredDay: "add debit expired day",
              addMemberExpiredDay: "add member expired day",
              seq: "seq"
            },
            isHideUpload: false,
            fieldOptions: [
              { label: "Add Debit Points", value: "addDebitPoints" },
              { label: "Add Member Points", value: "addMemberPoints" },
              { label: "Add Debit Expired Day", value: "addDebitExpiredDay" },
              { label: "Add Member Expired Day", value: "addMemberExpiredDay" }
            ]
          }
        },
        created() {
          this.initForm = JSON.parse(JSON.stringify(this.addForm))
          this.queryData()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整
        },
        computed: {
          currentForm() {
            return this.addDialogVisible ? this.addForm : this.editForm
          },
          // 计算 Points 禁用状态
          pointsDisabled() {
            const form = this.currentForm
            return {
              debitGroup: !!form.addMemberPoints || !!form.addMemberExpiredDay,
              memberGroup: !!form.addDebitPoints || !!form.addDebitExpiredDay
            }
          }
        },
        mounted() {},
        watch: {
          addDialogVisible(val) {
            if (val) {
              this.isHideUpload = false
            }
          }
        },
        methods: {
          // 查询表格数据
          queryData() {
            $.get({
              url: "../../manager_memberRecharge/getAll",
              // url: "/api/manager_memberRecharge/getAll",
              dataType: "json",
              success: res => {
                console.log(res)
                if (res.statusCode == 200) {
                  let data = res.data
                  // data = [...data].sort((a, b) => {
                  //   // 先按seq排序
                  //   if (a.seq !== b.seq) {
                  //     return a.seq - b.seq
                  //   }
                  //   // seq相同时按upa排序
                  //   return a.upa - b.upa
                  // })

                  data.forEach(item => {
                    item.giftInformation = item.giftInformation
                      ? item.giftInformation.split(",")
                      : []
                    //有后缀和时间戳代表存在图片
                    if (item.photoSuffix && item.photoTime) {
                      const url = `http://appwise.oss-cn-hongkong.aliyuncs.com/${
                        item.domain
                      }/image/memberRecharge/${item.code}.${
                        item.photoSuffix
                      }?x-oss-process=image/resize,w_60,h_60&${new Date().getTime()}`
                      item.logoUrl = url
                    }
                  })
                  console.log(JSON.parse(JSON.stringify(data)))
                  this.tableData = data
                } else {
                  this.$message.error("Failed to query data")
                }
              },
              error: () => {
                this.$message.error("Failed to query data")
              }
            })
          },

          //点击提交按钮验证表单
          submitForm(formName) {
            console.log(JSON.parse(JSON.stringify(this[formName])), "this[formName]")

            this.$refs[formName].validate(valid => {
              if (valid) {
                try {
                  const { imgList } = this.currentForm
                  // 如果有图片，先验证图片格式
                  if (
                    imgList.length &&
                    imgList[0].raw &&
                    !this.beforeAvatarUpload(imgList, formName)
                  ) {
                    return
                  }

                  // 通过验证，提交表单
                  this.submitImage(formName)
                } catch (error) {
                  this.$message.error("Form submission error, please try again")
                }
              } else {
                this.$message.warning("Please check if the form is filled in correctly")
                return false
              }
            })
          },
          //提交新增\更新
          submitImage(formName) {
            let fd = new FormData()
            let form = this[formName]
            let { imgList } = form
            let isAddImg = imgList.length && imgList[0].raw // 是否新增图片
            for (let [key, value] of Object.entries(form)) {
              //是新增图片跳过photoSuffix和photoTime
              if (isAddImg && (key == "photoSuffix" || key == "photoTime")) {
                continue
              }
              // 过滤玄学null值
              if (value == "null") {
                fd.set(key, "")
              } else if (key == "giftInformation") {
                fd.append(key, value.join(","))
              } else if (key !== "imgList") {
                fd.append(key, value)
              }
            }

            if (isAddImg) {
              let file = imgList[0].raw
              fd.append("file", file)
              fd.append("photoSuffix", file.name.split(".")[1]) //图片后缀
              fd.append("photoTime", new Date().getTime()) //图片上传时间时间戳
            }

            let subType = formName == "addForm" ? "insertOne" : "updateOne"
            let errorMsg = formName == "addForm" ? "Fail to upload!" : "Edit failure！"
            //增加loading
            let loading = this.$message({
              message: "loading...",
              type: "warning",
              duration: 0
            })
            $.post({
              url: "../../manager_memberRecharge/" + subType,
              dataType: "json",
              processData: false,
              contentType: false,
              data: fd,
              success: res => {
                if (res.statusCode == 200) {
                  this.subSuccessCallback(res, formName)
                } else {
                  this.$message.error(errorMsg)
                }
              },
              error: () => {
                this.$message.error(errorMsg)
              },
              complete: () => {
                loading.close()
              }
            })
          },

          handleUploadChange(file, fileList) {
            this.isHideUpload = true
            let form = this.addDialogVisible ? this.addForm : this.editForm
            form.imgList = fileList
          },
          subSuccessCallback(res, formName) {
            let map = {
              addForm: {
                dialog: "addDialogVisible",
                msg: "Successfully upload ！",
                upload: "addUpload"
              },
              editForm: {
                dialog: "editDialogVisible",
                msg: "Edit success！",
                upload: "editUpload"
              }
            }
            this.$message.success(map[formName].msg)

            this.$refs[map[formName].upload].clearFiles() // 此处有变化，重新赋值给imgList
            this[map[formName].dialog] = false
            // 查询
            this.queryData()
          },

          //校验图片文件格式
          beforeAvatarUpload(list, formName) {
            // 检测图片格式是否正确
            if (list.length) {
              let allSize = 0
              for (let i = 0; i < list.length; ++i) {
                const arr = list[i].name.split(".")
                const isLt5M = list[i].size / 1024 / 1024 < 5
                allSize += isLt5M
                let testmsg = ["jpeg", "jpg"].includes(arr[arr.length - 1])
                if (!testmsg) {
                  this.$message.error("File suffixes are illegal,only support [jpg]!")
                  return false
                }
                if (!isLt5M) {
                  this.$message.error(`The image size exceeds the maximum limit : ${list[i].name}`)
                  return false
                }
              }
              if (allSize > 12) {
                this.$message.error(`The image size exceeds the maximum limit!`)
                return false
              }
              return true
            }
          },
          //移除图片逻辑
          handleRemove(file, fileList) {
            this.currentForm.imgList = fileList
            this.isHideUpload = false
          },
          //更新取消
          uploadCanvel(formName) {
            this.addDialogVisible = false
            this.$refs[formName].resetFields()
          },
          // 上传对话框关闭事件
          uploadCloseDialog(form) {
            this.$refs[form].resetFields()
            this.currentForm.imgList = []
          },
          handleEditor(index, row) {
            let copyRow = JSON.parse(JSON.stringify(row))
            let initForm = JSON.parse(JSON.stringify(this.initForm))
            this.editForm = {
              ...initForm,
              ...copyRow
            }
            let { logoUrl } = this.editForm
            if (logoUrl) {
              let imgUrl = logoUrl.split("?")[0]
              this.editForm.imgList = [
                { name: `${copyRow.code}.${copyRow.photoSuffix}`, url: imgUrl }
              ]
              this.isHideUpload = true
            } else {
              this.isHideUpload = false
            }
            // console.log(JSON.parse(JSON.stringify(this.editForm)), "this.editForm")
            this.editDialogVisible = true
          },

          handleDel(index, row) {
            let data = { code: row.code }
            let loading = this.$message({
              message: "loading...",
              type: "warning",
              duration: 0
            })
            $.post({
              url: "../../manager_memberRecharge/deleteOne",
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode != 200) {
                  this.$message.error("Fail to delete!")
                } else {
                  this.queryData()
                  this.$message.success("Successfully delete!")
                }
              },
              error: error => {
                this.$message.error("Fail to delete!")
              },
              complete: () => {
                loading.close()
              }
            })
          },
          showName(row) {
            let { desc1, desc2, multi1 } = row
            let names = [desc1, desc2, multi1].filter(name => name && name.trim())
            return names.join(" | ")
          },

          handleCheckboxChange(value, form) {
            // console.log(value, "value")
          },
          clearValidPointsAndDate() {
            //清除addDebitPoints/addMemberPoints/addDebitExpiredDay/addMemberExpiredDay错误提示
            let fields = [
              "addDebitPoints",
              "addMemberPoints",
              "addDebitExpiredDay",
              "addMemberExpiredDay"
            ]
            let form = this.addDialogVisible ? "addForm" : "editForm"
            fields.forEach(item => {
              this.$refs[form].clearValidate(item)
            })
          },
          handlePointsInput(type, value, form) {
            // 限制只能输入数字
            if (!/^\d*$/.test(value)) {
              this[form][type] = value.replace(/\D/g, "")
              return
            }
            this.clearValidPointsAndDate()
            const oppositeType = type === "addDebitPoints" ? "addMemberPoints" : "addDebitPoints"
            const oppositeField = {
              addDebitPoints: ["addDebitPoints", "addDebitExpiredDay"],
              addMemberPoints: ["addMemberPoints", "addMemberExpiredDay"]
            }
            const numValue = Number(value)
            if (numValue > 0) {
              this[form].giftInformation = this[form].giftInformation.filter(
                item => !oppositeField[oppositeType].includes(item)
              )
            }
          },
          handleExpiredDayInput(type, value, form) {
            // 移除非数字字符
            let newValue = value.replace(/\D/g, "")
            // 如果是空字符串，设置为空
            if (newValue === "") {
              this[form][type] = ""
              return
            }
            this.clearValidPointsAndDate()
            const oppositeType =
              type === "addDebitExpiredDay" ? "addMemberExpiredDay" : "addDebitExpiredDay"
            const oppositeField = {
              addDebitExpiredDay: ["addDebitPoints", "addDebitExpiredDay"],
              addMemberExpiredDay: ["addMemberPoints", "addMemberExpiredDay"]
            }
            const numValue = Number(value)
            if (numValue > 0) {
              // 取消勾选被禁用的Points选项
              this[form].giftInformation = this[form].giftInformation.filter(
                item => !oppositeField[oppositeType].includes(item)
              )
            }
          },
          onSwitch(event, row) {
            let copyRow = JSON.parse(JSON.stringify(row))
            let initForm = JSON.parse(JSON.stringify(this.initForm))
            this.editForm = {
              ...initForm,
              ...copyRow
            }
            let { logoUrl, imgList } = this.editForm
            if (logoUrl) {
              let imgUrl = logoUrl.split("?")[0]
              this.editForm.imgList = [
                { name: `${copyRow.code}.${copyRow.photoSuffix}`, url: imgUrl }
              ]
            }
            let fd = new FormData()
            for (let [key, value] of Object.entries(this.editForm)) {
              // 过滤玄学null值
              if (value == "null") {
                fd.set(key, "")
              } else if (key == "giftInformation") {
                fd.append(key, value.join(","))
              } else if (key !== "imgList") {
                fd.append(key, value)
              }
            }
            //增加loading
            let loading = this.$message({
              message: "loading...",
              type: "warning",
              duration: 0
            })
            $.post({
              url: "../../manager_memberRecharge/updateOne",
              dataType: "json",
              processData: false,
              contentType: false,
              data: fd,
              success: res => {
                if (res.statusCode == 200) {
                  this.$message.success("Successfully update!")
                  this.queryData()
                } else {
                  this.$message.error("Fail to update!")
                }
              },
              error: () => {
                this.$message.error("Fail to update!")
              },
              complete: () => {
                loading.close()
              }
            })
          },
          disableCheckbox(value) {
            let checkType = value.startsWith("addDebit") ? "debitGroup" : "memberGroup"
            return this.pointsDisabled[checkType]
          }
        }
      })
    </script>
  </body>
</html>
