// 使用 web worker来加载Google map 资源
function asyncFetch(url, options, timeout) {
  return Promise.race([
    fetch(url, options),
    new Promise((_, reject) => {
      if (timeout) {
        setTimeout(() => reject(new Error("timeout")), timeout)
      }
    })
  ])
}
onmessage = e => {
  let { key, lan, timeout } = e.data
  let url = `https://maps.googleapis.com/maps/api/js?key=${key}&callback=initMap&v=weekly&libraries=places&sensitive=true&sensor=true&language=${lan}`
  asyncFetch(url, {}, timeout)
    .then(res => res.text())
    .then(res => {
      postMessage({ code: "ok", res })
    })
    .catch(() => {
      postMessage({ code: "err", res: "" })
    })
}
