var TakeAwayStoreInfo = {
  props: ["systemLanguage"],
  data() {
    return {
      openTable: {}
    }
  },
  created() {
    this.openTable = this.$parent.openTable
  },

  computed: {
    getStoreName() {
      let { language, storeName = {}, storeNumber } = this.$parent.openTable
      // 判断storeName是否为空对象
      let val = Object.keys(storeName).length === 0 ? storeNumber : storeName[language]
      return val
    }
  },
  mounted() {},
  template: ` 
    <div class="delivery_warp">
      <div class="address">
        <img class="delivery_warp_icon" src="../static/img/page/storeAddress.jpg" alt="" />
        <span>{{systemLanguage.pickUpAddressLabel}}</span>
        <div v-if="openTable.storeData" @click="$parent.backMap" class="delivery_warp_storeData">
          <p class="delivery_warp_storeName">
            {{$parent.handleOpenTableLangEchoByStore('storeName',true)}}
          </p>
          <img class="backMapIcon" src="../static/img/page/backMap.jpg" alt="backMap" />
        </div>
        <div v-else>
         {{$parent.handleOpenTableLangEchoByStore('storeName',true)}}
        </div>
      </div>
      <div class="delivery_warp_interval">|</div>
      <div class="storeTime">
        <img class="delivery_warp_icon" src="../static/img/page/storeTime.jpg" alt="" />
        <span :style="{color:($parent.stressDate()?'var(--styleColor)':'unset')}">
          <template v-if="openTable.storeData">
            {{$parent.handleOpenTableLangEchoByStore('pickupTime')||systemLanguage.instantPickup}}
          </template>
          <template v-else>{{systemLanguage.instantPickup}}</template>
        </span>
      </div>
    </div>
    `,
  methods: {}
}
