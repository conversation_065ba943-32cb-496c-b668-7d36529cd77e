* {
  margin: 0;
  padding: 0;
}

html,
body {
  width: 100%;
  height: 100%;
}

#app {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 520;
  box-sizing: border-box;
}

:root {
  --styleColor: #be9176;
}
.v-application p {
  margin: 0;
}
.flexBox {
  /* flex: 1; */
  /* display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  position: relative; */
  display: flex;
  flex-direction: column;
  height: 100%;
}

.hitspopUp_warp {
  position: relative;
  height: 1.2rem;
  /* font-family: Arial, Helvetica, sans-serif; */
  z-index: 2;
}

.header {
  height: 1.2rem;
  line-height: 1.2rem;
  background-color: var(--styleColor);
}

.header-title {
  color: #fff;
  text-align: center;
  font-size: 0.55rem;

  /* font-size: 0.55rem; */
}

.blackIcon {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0.3rem;
  width: 0.8rem;
}

.hits_food {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  position: relative;
  padding: 0.5rem 0 1.4rem;
  overflow-y: scroll;
  position: relative;
}

.hits_food ::-webkit-scrollbar {
  display: none;
}

/* loading */
.loader {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;

  vertical-align: middle;
  z-index: 501;
}

.loader-3 .dot {
  width: 10px;
  height: 10px;
  background: var(--styleColor);
  border-radius: 50%;
  position: absolute;
  top: -webkit-calc(50% - 5px);
  top: calc(50% - 5px);
}

.loader-3 .dot1 {
  left: 0px;
  -webkit-animation: dot-jump 0.5s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate infinite;
}

.loader-3 .dot2 {
  left: 20px;
  -webkit-animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate infinite;
}

.loader-3 .dot3 {
  left: 40px;
  -webkit-animation: dot-jump 0.5s 0.37s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s 0.37s cubic-bezier(0.77, 0.377, 0.64, 0.28) alternate infinite;
}

@-webkit-keyframes dot-jump {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

@keyframes dot-jump {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

.header-left-icon {
  position: absolute;
  right: 0.4rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
}
.navHeaderIcon {
  width: 0.53rem;
  padding-right: 0.3rem;
}
.navHeaderIcon:nth-last-child(1) {
  padding-right: 0px;
}
.orderBox {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0;
}
.orderBox-cell {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 90%;
  font-size: 0.35rem;
  border-radius: 0.2rem;
  /* overflow: hidden; */
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: 0.5rem;

  /* padding: 0.2rem; */
}
.orderBox-cell:nth-last-child(1) {
  margin-bottom: 0rem;
}
.orderBox-cell-item {
  display: flex;
  justify-content: space-between;
}
.orderBox-cell-item p {
  flex-shrink: 0;
}
.orderBox-cell-item p:nth-of-type(2) {
  flex: 1;
  text-align: end;
}
.orderBox-cell-info {
  display: flex;
}
.orderBox-cell-info .orderBox-cell-info-left {
  flex: 1;
  /* display: flex;
  flex-direction: column;
  justify-content: space-between; */
}
.orderBox-cell-info-right {
  flex-shrink: 0;
  display: flex;
  align-items: end;
  flex-direction: column;
  justify-content: center;
}
/* odd表示奇数行，even表示偶数行 */
.orderBox-cell-item:nth-child(even),
.orderBox-cell-info .orderBox-cell-info-left:nth-child(even) {
  margin: 0.2rem 0;
}
.paymentPendingTip {
  color: #f56c6c;
}
.orderBox-cell-pickUpDate {
  margin-bottom: 0.2rem;
}
.orderBox-cell-deliveryStore p:nth-child(2) {
  color: var(--styleColor);
}
.orderBox-cell-merchantRef p:nth-child(2) {
  word-break: break-all;
}
.discount-col p:nth-child(even) {
  min-width: 20%;
  text-align: end;
  align-self: center;
}
.discount-col p span {
  margin-left: 0.2rem;
  display: block;
}
.littleitem {
  font-size: 0.28rem;
  margin: 0.1rem 0.1rem 0.1rem 0.2rem;
}
.littleitem-inXi {
  margin-left: 0.1rem !important;
}
.littleitem-endXi {
  margin-left: 0.2rem;
}
.orderBox-cell-date {
  width: 100%;
  background: #f0f4f3;
  height: 0.8rem;
  line-height: 0.8rem;
  text-align: center;
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
}
.orderBox-cell-content {
  padding: 0.2rem;
}

.null_hits_food {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 0.5rem;
  /* color: var(--styleColor); */
  color: #ccc;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.emptyIcon {
  width: 5rem;
  padding-bottom: 0.5rem;
}
.emptyTxt {
  text-align: center;
  padding: 0 0.5rem;
}

#app .v-snack__content {
  font-size: small;
  line-height: revert;
}

.empty-snackbar {
  position: relative;
}
.empty-snackbar .v-snack__wrapper {
  margin-top: 3rem;
}
.dovetailArrow-icon {
  position: absolute;
  right: 0;
  top: -2rem;
  width: 2rem;
}

.searchDialog-warp .v-card__title {
  line-height: 1rem;
}

.searchDialog-warp .v-text-field {
  padding: 0;
  margin: 0;
}
.searchDialog-title {
  font-size: 0.55rem;
  padding-bottom: 0.2rem;
}
.searchDialog-warp .v-btn.v-size--default {
  font-size: 0.35rem;
}
.searchDialog-warp .v-card__text {
  line-height: unset;
}
.searchDialog-bottom-tip {
  margin-top: 0.2rem;
  font-size: 0.32rem;
  word-wrap: break-word;
}
/* 定义抖动动画 */
@keyframes shake {
  0% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(0);
  }
}

/* 添加抖动动画到searchDialog-bottom-tip元素 */
.shake-animation {
  animation: shake 0.5s ease-in-out;
}
.orderBox-cell-store {
  flex-direction: column;
}
.orderBox-cell-store-title {
  font-size: 0.45rem;
  font-weight: 700;
}
.orderBox-cell-store .orderBox-cell-info.Fvx2at {
  margin-left: 0.25rem;
}
