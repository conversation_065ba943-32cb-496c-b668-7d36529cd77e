const EmailEditorToolbar = Vue.component("email-editor-toolbar", {
  props: {
    targetId: String,
    templateType: String,
    insertContent: Function,
    removeTemplate: Function,
    focusOnTinymce: Function
  },

  data() {
    return {
      buttons: []
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.buttons = [
      { label: "#amt", action: "insertContent", type: "amt" },
      { label: "#paymentMethod", action: "insertContent", type: "paymentMethod" },
      { label: "#address", action: "insertContent", type: "address" },
      { label: "pushOrderTemplate", action: "insertContent", type: this.templateType },
      { label: "removeOrderTemplate", action: "removeTemplate", type: this.templateType },
      { label: "focusTop", action: "focusOnTinymce", position: "top" },
      { label: "focusBottom", action: "focusOnTinymce", position: "bottom" }
    ]
    if (this.targetId.indexOf("pickupEmail") > -1) {
      this.buttons.unshift({ label: "#pickupTime", action: "insertContent", type: "pickupTime" })
    } else {
      this.buttons.unshift({ label: "#billNumber", action: "insertContent", type: "billNumber" })
    }
  },
  watch: {},
  methods: {
    handleButtonClick(button) {
      if (button.action === "insertContent") {
        let languages = this.targetId.split("-")[1]
        this.insertContent(button.type, this.targetId, languages)
      } else if (button.action === "removeTemplate") {
        this.removeTemplate(button.type, this.targetId)
      } else if (button.action === "focusOnTinymce") {
        this.focusOnTinymce(button.position, this.targetId)
      }
    }
  },
  template: `
  <div class="email-editor-toolbar">
    <el-button
      v-for="button in buttons"
      :key="button.label"
      type="primary"
      size="mini"
      style="margin:0px 10px 10px 0px;"
      @click="handleButtonClick(button)"
    >
      {{ button.label }}
    </el-button>
  </div>
    
  
  `
})
