/**
 * TinyMCE version 6.1.0 (2022-06-29)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=r=e,(o=String).prototype.isPrototypeOf(n)||(null===(s=r.constructor)||void 0===s?void 0:s.name)===o.name)?"string":t;var n,r,o,s})(t)===e,n=e=>t=>typeof t===e,r=t("string"),o=t("object"),s=t("array"),i=n("boolean"),a=n("function"),l=n("number"),d=()=>{},c=(e,t)=>e===t,u=e=>t=>!e(t),m=(!1,()=>false);class p{constructor(e,t){this.tag=e,this.value=t}static some(e){return new p(!0,e)}static none(){return p.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?p.some(e(this.value)):p.none()}bind(e){return this.tag?e(this.value):p.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:p.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return null==e?p.none():p.some(e)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}p.singletonNone=new p(!1);const g=Array.prototype.slice,h=Array.prototype.indexOf,f=Array.prototype.push,y=(e,t)=>{return n=e,r=t,h.call(n,r)>-1;var n,r},C=(e,t)=>{for(let n=0,r=e.length;n<r;n++)if(t(e[n],n))return!0;return!1},v=(e,t)=>{const n=e.length,r=new Array(n);for(let o=0;o<n;o++){const n=e[o];r[o]=t(n,o)}return r},b=(e,t)=>{for(let n=0,r=e.length;n<r;n++)t(e[n],n)},S=(e,t)=>{const n=[];for(let r=0,o=e.length;r<o;r++){const o=e[r];t(o,r)&&n.push(o)}return n},N=(e,t,n)=>(b(e,((e,r)=>{n=t(n,e,r)})),n),L=(e,t,n)=>{for(let r=0,o=e.length;r<o;r++){const o=e[r];if(t(o,r))return p.some(o);if(n(o,r))break}return p.none()},O=(e,t)=>L(e,t,m),T=(e,t)=>(e=>{const t=[];for(let n=0,r=e.length;n<r;++n){if(!s(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);f.apply(t,e[n])}return t})(v(e,t)),k=e=>{const t=g.call(e,0);return t.reverse(),t},A=(e,t)=>t>=0&&t<e.length?p.some(e[t]):p.none(),w=e=>A(e,0),D=e=>A(e,e.length-1),B=(e,t)=>{const n=[],r=a(t)?e=>C(n,(n=>t(n,e))):e=>y(n,e);for(let t=0,o=e.length;t<o;t++){const o=e[t];r(o)||n.push(o)}return n},x=(e,t,n=c)=>e.exists((e=>n(e,t))),E=(e,t,n)=>e.isSome()&&t.isSome()?p.some(n(e.getOrDie(),t.getOrDie())):p.none(),I=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},M=(e,t)=>{const n=(t||document).createElement(e);return I(n)},P=I,R=(e,t)=>e.dom===t.dom;"undefined"!=typeof window?window:Function("return this;")();const U=e=>e.dom.nodeName.toLowerCase(),$=(1,e=>1===(e=>e.dom.nodeType)(e));const _=e=>t=>$(t)&&U(t)===e,H=e=>p.from(e.dom.parentNode).map(P),j=e=>v(e.dom.childNodes,P),F=(e,t)=>{const n=e.dom.childNodes;return p.from(n[t]).map(P)},K=e=>F(e,0),V=e=>F(e,e.dom.childNodes.length-1),z=(e,t,n)=>{let r=e.dom;const o=a(n)?n:m;for(;r.parentNode;){r=r.parentNode;const e=P(r);if(t(e))return p.some(e);if(o(e))break}return p.none()},Q=(e,t,n)=>((e,t,n,r,o)=>r(n)?p.some(n):a(o)&&o(n)?p.none():t(n,r,o))(0,z,e,t,n),q=(e,t)=>{H(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},W=(e,t)=>{e.dom.appendChild(t.dom)},Z=(e,t)=>{b(t,(t=>{W(e,t)}))},G=e=>{e.dom.textContent="",b(j(e),(e=>{J(e)}))},J=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)};var X=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Y=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),ee=tinymce.util.Tools.resolve("tinymce.util.VK");const te=Object.keys,ne=(e,t)=>{const n=te(e);for(let r=0,o=n.length;r<o;r++){const o=n[r];t(e[o],o)}},re=(e,t)=>{const n=e.dom;ne(t,((e,t)=>{((e,t,n)=>{if(!(r(n)||i(n)||l(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")})(n,t,e)}))},oe=e=>N(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),se=e=>((e,t)=>P(e.dom.cloneNode(!0)))(e),ie=(e,t)=>{const n=((e,t)=>{const n=M(t),r=oe(e);return re(n,r),n})(e,t);((e,t)=>{const n=(e=>p.from(e.dom.nextSibling).map(P))(e);n.fold((()=>{H(e).each((e=>{W(e,t)}))}),(e=>{q(e,t)}))})(e,n);const r=j(e);return Z(n,r),J(e),n};var ae=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),le=tinymce.util.Tools.resolve("tinymce.util.Tools");const de=e=>t=>t&&t.nodeName.toLowerCase()===e,ce=e=>t=>t&&e.test(t.nodeName),ue=e=>e&&3===e.nodeType,me=ce(/^(OL|UL|DL)$/),pe=ce(/^(OL|UL)$/),ge=de("ol"),he=ce(/^(LI|DT|DD)$/),fe=ce(/^(DT|DD)$/),ye=ce(/^(TH|TD)$/),Ce=de("br"),ve=(e,t)=>t&&!!e.schema.getTextBlockElements()[t.nodeName],be=(e,t)=>e&&e.nodeName in t,Se=(e,t,n)=>{const r=e.isEmpty(t);return!(n&&e.select("span[data-mce-type=bookmark]",t).length>0)&&r},Ne=(e,t)=>e.isChildOf(t,e.getRoot()),Le=e=>t=>t.options.get(e),Oe=Le("lists_indent_on_tab"),Te=Le("forced_root_block"),ke=Le("forced_root_block_attrs"),Ae=(e,t)=>{const n=e.dom,r=e.schema.getBlockElements(),o=n.createFragment(),s=Te(e),i=ke(e);let a,l,d;for(l=n.create(s,i),be(t.firstChild,r)||o.appendChild(l);a=t.firstChild;){const e=a.nodeName;d||"SPAN"===e&&"bookmark"===a.getAttribute("data-mce-type")||(d=!0),be(a,r)?(o.appendChild(a),l=null):(l||(l=n.create(s,i),o.appendChild(l)),l.appendChild(a))}return d||l.appendChild(n.create("br",{"data-mce-bogus":"1"})),o},we=ae.DOM,De=_("dd"),Be=_("dt"),xe=e=>{Be(e)&&ie(e,"dd")},Ee=(e,t,n)=>{b(n,"Indent"===t?xe:t=>((e,t)=>{De(t)?ie(t,"dt"):Be(t)&&H(t).each((n=>((e,t,n)=>{const r=we.select('span[data-mce-type="bookmark"]',t),o=Ae(e,n),s=we.createRng();s.setStartAfter(n),s.setEndAfter(t);const i=s.extractContents();for(let t=i.firstChild;t;t=t.firstChild)if("LI"===t.nodeName&&e.dom.isEmpty(t)){we.remove(t);break}var a;e.dom.isEmpty(i)||we.insertAfter(i,t),we.insertAfter(o,t),Se(e.dom,n.parentNode)&&(a=n.parentNode,le.each(r,(e=>{a.parentNode.insertBefore(e,n.parentNode)})),we.remove(a)),we.remove(n),Se(e.dom,t)&&we.remove(t)})(e,n.dom,t.dom)))})(e,t))},Ie=(e,t)=>{if(ue(e))return{container:e,offset:t};const n=X.getNode(e,t);return ue(n)?{container:n,offset:t>=e.childNodes.length?n.data.length:0}:n.previousSibling&&ue(n.previousSibling)?{container:n.previousSibling,offset:n.previousSibling.data.length}:n.nextSibling&&ue(n.nextSibling)?{container:n.nextSibling,offset:0}:{container:e,offset:t}},Me=e=>{const t=e.cloneRange(),n=Ie(e.startContainer,e.startOffset);t.setStart(n.container,n.offset);const r=Ie(e.endContainer,e.endOffset);return t.setEnd(r.container,r.offset),t},Pe=["OL","UL","DL"],Re=Pe.join(","),Ue=(e,t)=>{const n=t||e.selection.getStart(!0);return e.dom.getParent(n,Re,He(e,n))},$e=e=>{const t=e.selection.getSelectedBlocks();return S(((e,t)=>{const n=le.map(t,(t=>e.dom.getParent(t,"li,dd,dt",He(e,t))||t));return B(n)})(e,t),he)},_e=(e,t)=>{const n=e.dom.getParents(t,"TD,TH");return n.length>0?n[0]:e.getBody()},He=(e,t)=>{const n=e.dom.getParents(t,e.dom.isBlock),r=O(n,(t=>{return n=e.schema,!me(r=t)&&!he(r)&&C(Pe,(e=>n.isValidChild(r.nodeName,e)));var n,r}));return r.getOr(e.getBody())},je=(e,t)=>{const n=e.dom.getParents(t,"ol,ul",He(e,t));return D(n)},Fe=(e,t)=>{const n=v(t,(t=>je(e,t).getOr(t)));return B(n)},Ke=(e,t,n)=>e.dispatch("ListMutation",{action:t,element:n}),Ve=(ze=/^\s+|\s+$/g,e=>e.replace(ze,""));var ze;const Qe=(e,t,n)=>{((e,t,n)=>{if(!r(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);(e=>void 0!==e.style&&a(e.style.getPropertyValue))(e)&&e.style.setProperty(t,n)})(e.dom,t,n)},qe=(e,t)=>{W(e.item,t.list)},We=(e,t)=>{const n={list:M(t,e),item:M("li",e)};return W(n.list,n.item),n},Ze=e=>((e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}})(e,"OL,UL"),Ge=e=>K(e).exists(Ze),Je=e=>e.depth>0,Xe=e=>e.isSelected,Ye=e=>{const t=j(e),n=V(e).exists(Ze)?t.slice(0,-1):t;return v(n,se)},et=e=>(b(e,((t,n)=>{((e,t)=>{const n=e[t].depth,r=e=>e.depth===n&&!e.dirty,o=e=>e.depth<n;return L(k(e.slice(0,t)),r,o).orThunk((()=>L(e.slice(t+1),r,o)))})(e,n).fold((()=>{t.dirty&&(e=>{e.listAttributes=((e,t)=>{const n={};var r;return((e,t,n,r)=>{ne(e,((e,o)=>{(t(e,o)?n:r)(e,o)}))})(e,t,(r=n,(e,t)=>{r[t]=e}),d),n})(e.listAttributes,((e,t)=>"start"!==t))})(t)}),(e=>{return r=e,(n=t).listType=r.listType,void(n.listAttributes={...r.listAttributes});var n,r}))})),e),tt=(e,t,n,r)=>K(r).filter(Ze).fold((()=>{t.each((e=>{R(e.start,r)&&n.set(!0)}));const o=((e,t,n)=>H(e).filter($).map((r=>({depth:t,dirty:!1,isSelected:n,content:Ye(e),itemAttributes:oe(e),listAttributes:oe(r),listType:U(r)}))))(r,e,n.get());t.each((e=>{R(e.end,r)&&n.set(!1)}));const s=V(r).filter(Ze).map((r=>nt(e,t,n,r))).getOr([]);return o.toArray().concat(s)}),(r=>nt(e,t,n,r))),nt=(e,t,n,r)=>T(j(r),(r=>(Ze(r)?nt:tt)(e+1,t,n,r))),rt=(e,t)=>{const n=et(t);return((e,t)=>{const n=N(t,((t,n)=>n.depth>t.length?((e,t,n)=>{const r=((e,t,n)=>{const r=[];for(let o=0;o<n;o++)r.push(We(e,t.listType));return r})(e,n,n.depth-t.length);var o;return(e=>{for(let t=1;t<e.length;t++)qe(e[t-1],e[t])})(r),((e,t)=>{for(let t=0;t<e.length-1;t++)Qe(e[t].item,"list-style-type","none");D(e).each((e=>{re(e.list,t.listAttributes),re(e.item,t.itemAttributes),Z(e.item,t.content)}))})(r,n),o=r,E(D(t),w(o),qe),t.concat(r)})(e,t,n):((e,t,n)=>{const r=t.slice(0,n.depth);return D(r).each((t=>{const r=((e,t,n)=>{const r=M("li",e);return re(r,t),Z(r,n),r})(e,n.itemAttributes,n.content);((e,t)=>{W(e.list,t),e.item=t})(t,r),((e,t)=>{U(e.list)!==t.listType&&(e.list=ie(e.list,t.listType)),re(e.list,t.listAttributes)})(t,n)})),r})(e,t,n)),[]);return w(n).map((e=>e.list))})(e.contentDocument,n).toArray()},ot=(e,t,n)=>{const r=((e,t)=>{const n=(e=>{let t=!1;return{get:()=>t,set:e=>{t=e}}})();return v(e,(e=>({sourceList:e,entries:nt(0,t,n,e)})))})(t,(e=>{const t=v($e(e),P);return E(O(t,u(Ge)),O(k(t),u(Ge)),((e,t)=>({start:e,end:t})))})(e));b(r,(t=>{((e,t)=>{b(S(e,Xe),(e=>((e,t)=>{switch(e){case"Indent":t.depth++;break;case"Outdent":t.depth--;break;case"Flatten":t.depth=0}t.dirty=!0})(t,e)))})(t.entries,n);const r=((e,t)=>T(((e,t)=>{if(0===e.length)return[];{let n=t(e[0]);const r=[];let o=[];for(let s=0,i=e.length;s<i;s++){const i=e[s],a=t(i);a!==n&&(r.push(o),o=[]),n=a,o.push(i)}return 0!==o.length&&r.push(o),r}})(t,Je),(t=>w(t).exists(Je)?rt(e,t):((e,t)=>{const n=et(t);return v(n,(t=>{const n=((e,t)=>{const n=document.createDocumentFragment();return b(e,(e=>{n.appendChild(e.dom)})),P(n)})(t.content);return P(Ae(e,n.dom))}))})(e,t))))(e,t.entries);var o;b(r,(t=>{Ke(e,"Indent"===n?"IndentList":"OutdentList",t.dom)})),o=t.sourceList,b(r,(e=>{q(o,e)})),J(t.sourceList)}))},st=(e,t)=>{const n=v((e=>{const t=(e=>{const t=je(e,e.selection.getStart()),n=S(e.selection.getSelectedBlocks(),pe);return t.toArray().concat(n)})(e);return Fe(e,t)})(e),P),r=v((e=>S($e(e),fe))(e),P);let o=!1;if(n.length||r.length){const s=e.selection.getBookmark();ot(e,n,t),Ee(e,t,r),e.selection.moveToBookmark(s),e.selection.setRng(Me(e.selection.getRng())),e.nodeChanged(),o=!0}return o},it=e=>st(e,"Indent"),at=e=>st(e,"Outdent"),lt=e=>st(e,"Flatten");var dt=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager");const ct=ae.DOM,ut=e=>{const t={},n=n=>{let r=e[n?"startContainer":"endContainer"],o=e[n?"startOffset":"endOffset"];if(1===r.nodeType){const e=ct.create("span",{"data-mce-type":"bookmark"});r.hasChildNodes()?(o=Math.min(o,r.childNodes.length-1),n?r.insertBefore(e,r.childNodes[o]):ct.insertAfter(e,r.childNodes[o])):r.appendChild(e),r=e,o=0}t[n?"startContainer":"endContainer"]=r,t[n?"startOffset":"endOffset"]=o};return n(!0),e.collapsed||n(),t},mt=e=>{const t=t=>{let n,r=n=e[t?"startContainer":"endContainer"],o=e[t?"startOffset":"endOffset"];r&&(1===r.nodeType&&(o=(e=>{let t=e.parentNode.firstChild,n=0;for(;t;){if(t===e)return n;1===t.nodeType&&"bookmark"===t.getAttribute("data-mce-type")||n++,t=t.nextSibling}return-1})(r),r=r.parentNode,ct.remove(n),!r.hasChildNodes()&&ct.isBlock(r)&&r.appendChild(ct.create("br"))),e[t?"startContainer":"endContainer"]=r,e[t?"startOffset":"endOffset"]=o)};t(!0),t();const n=ct.createRng();return n.setStart(e.startContainer,e.startOffset),e.endContainer&&n.setEnd(e.endContainer,e.endOffset),Me(n)},pt=e=>{switch(e){case"UL":return"ToggleUlList";case"OL":return"ToggleOlList";case"DL":return"ToggleDLList"}},gt=e=>/\btox\-/.test(e.className),ht=(e,t,n)=>{const r=e=>{const r=L(e.parents,me,ye).filter((e=>e.nodeName===t&&!gt(e))).isSome();n(r)},o=e.dom.getParents(e.selection.getNode());return r({parents:o}),e.on("NodeChange",r),()=>e.off("NodeChange",r)},ft=(e,t)=>{le.each(t,((t,n)=>{e.setAttribute(n,t)}))},yt=(e,t,n)=>{((e,t,n)=>{const r=n["list-style-type"]?n["list-style-type"]:null;e.setStyle(t,"list-style-type",r)})(e,t,n),((e,t,n)=>{ft(t,n["list-attributes"]),le.each(e.select("li",t),(e=>{ft(e,n["list-item-attributes"])}))})(e,t,n)},Ct=(e,t,n,r)=>{let o=t[n?"startContainer":"endContainer"];const s=t[n?"startOffset":"endOffset"];for(1===o.nodeType&&(o=o.childNodes[Math.min(s,o.childNodes.length-1)]||o),!n&&Ce(o.nextSibling)&&(o=o.nextSibling);o.parentNode!==r;){if(ve(e,o))return o;if(/^(TD|TH)$/.test(o.parentNode.nodeName))return o;o=o.parentNode}return o},vt=(e,t,n)=>{const r=e.selection.getRng();let o="LI";const s=He(e,e.selection.getStart(!0)),i=e.dom;if("false"===i.getContentEditable(e.selection.getNode()))return;"DL"===(t=t.toUpperCase())&&(o="DT");const a=ut(r),l=((e,t,n)=>{const r=[],o=e.dom,s=Ct(e,t,!0,n),i=Ct(e,t,!1,n);let a;const l=[];for(let e=s;e&&(l.push(e),e!==i);e=e.nextSibling);return le.each(l,(t=>{if(ve(e,t))return r.push(t),void(a=null);if(o.isBlock(t)||Ce(t))return Ce(t)&&o.remove(t),void(a=null);const s=t.nextSibling;dt.isBookmarkNode(t)&&(me(s)||ve(e,s)||!s&&t.parentNode===n)?a=null:(a||(a=o.create("p"),t.parentNode.insertBefore(a,t),r.push(a)),a.appendChild(t))})),r})(e,r,s);le.each(l,(r=>{let s;const a=r.previousSibling,l=r.parentNode;he(l)||(a&&me(a)&&a.nodeName===t&&((e,t,n)=>{const r=e.getStyle(t,"list-style-type");let o=n?n["list-style-type"]:"";return o=null===o?"":o,r===o})(i,a,n)?(s=a,r=i.rename(r,o),a.appendChild(r)):(s=i.create(t),r.parentNode.insertBefore(s,r),s.appendChild(r),r=i.rename(r,o)),((e,t,n)=>{le.each(["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"],(n=>e.setStyle(t,n,"")))})(i,r),yt(i,s,n),St(e.dom,s))})),e.selection.setRng(mt(a))},bt=(e,t,n)=>{return((e,t)=>e&&t&&me(e)&&e.nodeName===t.nodeName)(t,n)&&((e,t,n)=>e.getStyle(t,"list-style-type",!0)===e.getStyle(n,"list-style-type",!0))(e,t,n)&&(r=n,t.className===r.className);var r},St=(e,t)=>{let n,r;if(n=t.nextSibling,bt(e,t,n)){for(;r=n.firstChild;)t.appendChild(r);e.remove(n)}if(n=t.previousSibling,bt(e,t,n)){for(;r=n.lastChild;)t.insertBefore(r,t.firstChild);e.remove(n)}},Nt=e=>"list-style-type"in e,Lt=(e,t,n)=>{const r=Ue(e),s=(e=>{const t=Ue(e),n=e.selection.getSelectedBlocks();return((e,t)=>e&&1===t.length&&t[0]===e)(t,n)?(e=>S(e.querySelectorAll(Re),me))(t):S(n,(e=>me(e)&&t!==e))})(e),i=o(n)?n:{};s.length>0?((e,t,n,r,o)=>{const s=me(t);if(s&&t.nodeName===r&&!Nt(o))lt(e);else{vt(e,r,o);const i=ut(e.selection.getRng()),a=s?[t,...n]:n;le.each(a,(t=>{((e,t,n,r)=>{if(t.nodeName!==n){const o=e.dom.rename(t,n);yt(e.dom,o,r),Ke(e,pt(n),o)}else yt(e.dom,t,r),Ke(e,pt(n),t)})(e,t,r,o)})),e.selection.setRng(mt(i))}})(e,r,s,t,i):((e,t,n,r)=>{if(t!==e.getBody())if(t)if(t.nodeName!==n||Nt(r)||gt(t)){const o=ut(e.selection.getRng());yt(e.dom,t,r);const s=e.dom.rename(t,n);St(e.dom,s),e.selection.setRng(mt(o)),vt(e,n,r),Ke(e,pt(n),s)}else lt(e);else vt(e,n,r),Ke(e,pt(n),t)})(e,r,t,i)},Ot=ae.DOM,Tt=(e,t)=>{const n=le.grep(e.select("ol,ul",t));le.each(n,(t=>{((e,t)=>{const n=t.parentNode;if("LI"===n.nodeName&&n.firstChild===t){const r=n.previousSibling;r&&"LI"===r.nodeName?(r.appendChild(t),Se(e,n)&&Ot.remove(n)):Ot.setStyle(n,"listStyleType","none")}if(me(n)){const e=n.previousSibling;e&&"LI"===e.nodeName&&e.appendChild(t)}})(e,t)}))},kt=(e,t,n,r)=>{let o=t.startContainer;const s=t.startOffset;if(ue(o)&&(n?s<o.data.length:s>0))return o;const i=e.schema.getNonEmptyElements();1===o.nodeType&&(o=X.getNode(o,s));const a=new Y(o,r);n&&((e,t)=>!!Ce(t)&&e.isBlock(t.nextSibling)&&!Ce(t.previousSibling))(e.dom,o)&&a.next();const l=n?a.next.bind(a):a.prev2.bind(a);for(;o=l();){if("LI"===o.nodeName&&!o.hasChildNodes())return o;if(i[o.nodeName])return o;if(ue(o)&&o.data.length>0)return o}},At=(e,t)=>{const n=t.childNodes;return 1===n.length&&!me(n[0])&&e.isBlock(n[0])},wt=(e,t,n)=>{let r;const o=t.parentNode;if(!Ne(e,t)||!Ne(e,n))return;me(n.lastChild)&&(r=n.lastChild),o===n.lastChild&&Ce(o.previousSibling)&&e.remove(o.previousSibling);const s=n.lastChild;s&&Ce(s)&&t.hasChildNodes()&&e.remove(s),Se(e,n,!0)&&G(P(n)),((e,t,n)=>{let r;const o=At(e,n)?n.firstChild:n;if(((e,t)=>{At(e,t)&&e.remove(t.firstChild,!0)})(e,t),!Se(e,t,!0))for(;r=t.firstChild;)o.appendChild(r)})(e,t,n),r&&n.appendChild(r);const i=((e,t)=>{const n=e.dom,r=t.dom;return n!==r&&n.contains(r)})(P(n),P(t))?e.getParents(t,me,n):[];e.remove(t),b(i,(t=>{Se(e,t)&&t!==e.getRoot()&&e.remove(t)}))},Dt=(e,t)=>{const n=e.dom,r=e.selection,o=r.getStart(),s=_e(e,o),i=n.getParent(r.getStart(),"LI",s);if(i){const o=i.parentNode;if(o===e.getBody()&&Se(n,o))return!0;const a=Me(r.getRng()),l=n.getParent(kt(e,a,t,s),"LI",s);if(l&&l!==i)return e.undoManager.transact((()=>{var n;t?((e,t,n,r)=>{const o=e.dom;if(o.isEmpty(r))((e,t,n)=>{G(P(n)),wt(e.dom,t,n),e.selection.setCursorLocation(n,0)})(e,n,r);else{const s=ut(t);wt(o,n,r),e.selection.setRng(mt(s))}})(e,a,l,i):(n=i).parentNode.firstChild===n?at(e):((e,t,n,r)=>{const o=ut(t);wt(e.dom,n,r);const s=mt(o);e.selection.setRng(s)})(e,a,i,l)})),!0;if(!l&&!t&&0===a.startOffset&&0===a.endOffset)return e.undoManager.transact((()=>{lt(e)})),!0}return!1},Bt=e=>{const t=e.selection.getStart(),n=_e(e,t);return e.dom.getParent(t,"LI,DT,DD",n)||$e(e).length>0},xt=(e,t)=>e.selection.isCollapsed()?((e,t)=>Dt(e,t)||((e,t)=>{const n=e.dom,r=e.selection.getStart(),o=_e(e,r),s=n.getParent(r,n.isBlock,o);if(s&&n.isEmpty(s)){const r=Me(e.selection.getRng()),i=n.getParent(kt(e,r,t,o),"LI",o);if(i){const a=e=>y(["td","th","caption"],U(e)),l=e=>e.dom===o;return!!((e,t,n=c)=>E(e,t,n).getOr(e.isNone()&&t.isNone()))(Q(P(i),a,l),Q(P(r.startContainer),a,l),R)&&(e.undoManager.transact((()=>{((e,t,n)=>{const r=e.getParent(t.parentNode,e.isBlock,n);e.remove(t),r&&e.isEmpty(r)&&e.remove(r)})(n,s,o),St(n,i.parentNode),e.selection.select(i,!0),e.selection.collapse(t)})),!0)}}return!1})(e,t))(e,t):(e=>!!Bt(e)&&(e.undoManager.transact((()=>{e.execCommand("Delete"),Tt(e.dom,e.getBody())})),!0))(e),Et=e=>{const t=k(Ve(e).split("")),n=v(t,((e,t)=>{const n=e.toUpperCase().charCodeAt(0)-"A".charCodeAt(0)+1;return Math.pow(26,t)*n}));return N(n,((e,t)=>e+t),0)},It=e=>{if(--e<0)return"";{const t=e%26,n=Math.floor(e/26);return It(n)+String.fromCharCode("A".charCodeAt(0)+t)}},Mt=e=>{const t=parseInt(e.start,10);return x(e.listStyleType,"upper-alpha")?It(t):x(e.listStyleType,"lower-alpha")?It(t).toLowerCase():e.start},Pt=(e,t)=>()=>{const n=Ue(e);return n&&n.nodeName===t},Rt=e=>{e.addCommand("mceListProps",(()=>{(e=>{const t=Ue(e);ge(t)&&e.windowManager.open({title:"List Properties",body:{type:"panel",items:[{type:"input",name:"start",label:"Start list at number",inputMode:"numeric"}]},initialData:{start:Mt({start:e.dom.getAttrib(t,"start","1"),listStyleType:p.some(e.dom.getStyle(t,"list-style-type"))})},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:t=>{(e=>{switch((e=>/^[0-9]+$/.test(e)?2:/^[A-Z]+$/.test(e)?0:/^[a-z]+$/.test(e)?1:e.length>0?4:3)(e)){case 2:return p.some({listStyleType:p.none(),start:e});case 0:return p.some({listStyleType:p.some("upper-alpha"),start:Et(e).toString()});case 1:return p.some({listStyleType:p.some("lower-alpha"),start:Et(e).toString()});case 3:return p.some({listStyleType:p.none(),start:""});case 4:return p.none()}})(t.getData().start).each((t=>{e.execCommand("mceListUpdate",!1,{attrs:{start:"1"===t.start?"":t.start},styles:{"list-style-type":t.listStyleType.getOr("")}})})),t.close()}})})(e)}))};e.add("lists",(e=>((e=>{(0,e.options.register)("lists_indent_on_tab",{processor:"boolean",default:!0})})(e),!1===e.hasPlugin("rtc",!0)?((e=>{Oe(e)&&(e=>{e.on("keydown",(t=>{t.keyCode!==ee.TAB||ee.metaKeyPressed(t)||e.undoManager.transact((()=>{(t.shiftKey?at(e):it(e))&&t.preventDefault()}))}))})(e),(e=>{e.on("ExecCommand",(t=>{const n=t.command.toLowerCase();"delete"!==n&&"forwarddelete"!==n||!Bt(e)||Tt(e.dom,e.getBody())})),e.on("keydown",(t=>{t.keyCode===ee.BACKSPACE?xt(e,!1)&&t.preventDefault():t.keyCode===ee.DELETE&&xt(e,!0)&&t.preventDefault()}))})(e)})(e),(e=>{e.on("BeforeExecCommand",(t=>{const n=t.command.toLowerCase();"indent"===n?it(e):"outdent"===n&&at(e)})),e.addCommand("InsertUnorderedList",((t,n)=>{Lt(e,"UL",n)})),e.addCommand("InsertOrderedList",((t,n)=>{Lt(e,"OL",n)})),e.addCommand("InsertDefinitionList",((t,n)=>{Lt(e,"DL",n)})),e.addCommand("RemoveList",(()=>{lt(e)})),Rt(e),e.addCommand("mceListUpdate",((t,n)=>{o(n)&&((e,t)=>{const n=Ue(e);e.undoManager.transact((()=>{o(t.styles)&&e.dom.setStyles(n,t.styles),o(t.attrs)&&ne(t.attrs,((t,r)=>e.dom.setAttrib(n,r,t)))}))})(e,n)})),e.addQueryStateHandler("InsertUnorderedList",Pt(e,"UL")),e.addQueryStateHandler("InsertOrderedList",Pt(e,"OL")),e.addQueryStateHandler("InsertDefinitionList",Pt(e,"DL"))})(e)):Rt(e),(e=>{const t=t=>()=>e.execCommand(t);e.hasPlugin("advlist")||(e.ui.registry.addToggleButton("numlist",{icon:"ordered-list",active:!1,tooltip:"Numbered list",onAction:t("InsertOrderedList"),onSetup:t=>ht(e,"OL",t.setActive)}),e.ui.registry.addToggleButton("bullist",{icon:"unordered-list",active:!1,tooltip:"Bullet list",onAction:t("InsertUnorderedList"),onSetup:t=>ht(e,"UL",t.setActive)}))})(e),(e=>{const t={text:"List properties...",icon:"ordered-list",onAction:()=>e.execCommand("mceListProps"),onSetup:t=>ht(e,"OL",t.setEnabled)};e.ui.registry.addMenuItem("listprops",t),e.ui.registry.addContextMenu("lists",{update:t=>{const n=Ue(e,t);return ge(n)?["listprops"]:[]}})})(e),(e=>({backspaceDelete:t=>{xt(e,t)}}))(e))))}();