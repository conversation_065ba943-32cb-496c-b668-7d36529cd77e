onmessage = function (e) {
  let { local, remote, name } = e.data
  if (!Array.isArray(remote) && name === "byod-diff-i18n") {
    const data = []
    for (const tab in remote) {
      if (Object.hasOwnProperty.call(remote, tab)) {
        let tabData = {
          tab,
          data: []
        }
        const tabValue = remote[tab]
        tabValue.forEach(item => {
          let value = {
            ...item,
            ...item.value
          }
          delete value.value
          tabData.data.push({
            ...value
          })
        })
        data.push(tabData)
      }
    }
    remote = data
  }

  function convertLocaleToRemoteFormat(locale) {
    function convertData(data) {
      const convertedData = []
      for (const key in data) {
        const item = data[key]
        const convertedItem = {
          en: item.value.en,
          label: item.label,
          slots: item.slots || [],
          thirdLan: item.value.thirdLan,
          type: key,
          zh: item.value.zh
        }
        convertedData.push(convertedItem)
      }
      return convertedData
    }

    const res = Object.entries(locale).map(item => {
      return {
        data: convertData(item[1]),
        tab: item[0]
      }
    })
    return res
  }

  function findMissingData(locale, remote) {
    const convertedLocaleData = convertLocaleToRemoteFormat(locale)
    const missingData = {}
    // 找出convertedLocaleData中有，但是remote中没有的数据
    for (let i = 0; i < convertedLocaleData.length; i++) {
      let localeItem = convertedLocaleData[i]
      const { data: localeData, tab: localeTab } = localeItem
      const remoteTabData = remote.find(remoteItem => remoteItem.tab === localeTab)
      // 如果remote中没有这个tab，那么这个tab下的所有数据都是missing的
      if (!remoteTabData) {
        missingData[localeTab] = localeData.map(el => ({ ...el, add: true }))
        continue
      } else {
        //   将remote的数据加入label和slots 😵(┬┬﹏┬┬)
        remoteTabData.data.forEach(remoteItem => {
          const { type } = remoteItem
          const localeItem = localeData.find(item => item.type === type)
          if (localeItem) {
            remoteItem.label = localeItem.label
            remoteItem.slots = localeItem.slots
          }
        })
      }
      // 如果remote中有这个tab，继续对比remoteTabData下的数据
      const { data: remoteItem } = remoteTabData
      const remoteItemTypes = remoteItem.map(item => item.type)
      for (let j = 0; j < localeData.length; j++) {
        if (!remoteItemTypes.includes(localeData[j].type)) {
          if (!missingData[localeTab]) {
            missingData[localeTab] = []
          }
          missingData[localeTab].push({ ...localeData[j], add: true })
        }
      }
    }
    return missingData
  }
  const diff = findMissingData(local, remote)
  if (name === "byod-diff-i18n") {
    const newRemote = {}
    remote.map(tabData => {
      const { tab, data } = tabData
      const dataObj = data.map(item => {
        return {
          label: item.label,
          type: item.type,
          value: {
            en: item.en,
            zh: item.zh,
            thirdLan: item.thirdLan
          }
        }
      })
      newRemote[tab] = dataObj
    })
    remote = newRemote
  }
  postMessage({ diff, remote })
}
