/**
 * 校验是否满足最大/最小数量
 *
 *  1;获取各type下有qty限制的code
 *  2.遍历orderList,若满足qty则移除code;且获取qty总数
 *  3.递归调用,遍历foodList,mList下是否有必选项且返回code
 * */
function checkRequiredQty(foodData, orderItemData) {
  const {
    foodList = [],
    mListList = [],
    foodTypeList = [],
    mTypeList = [],
    minQty2,
    maxQty2
  } = foodData
  const { newOrderItemFoodTypeList = [], newOrderItemMTypeList = [] } = orderItemData
  let selectedQty = foodList.length + mListList.length
  let { codes: ftCodes, types: fType } = checkRequiredQtyFilterHelper(foodTypeList)
  let { codes: mtCodes, types: mType } = checkRequiredQtyFilterHelper(mTypeList)
  const qtyMap = new Map()
  newOrderItemFoodTypeList.forEach(type => {
    const qtys = type.newOrderItemFoodList.reduce((acc, item) => {
      acc += toNumber(item.qty1)
      selectedQty += toNumber(item.qty1)
      return acc
    }, 0)
    qtyMap.set(type.code, qtys)
    if (ftCodes.has(type.code)) {
      const originType = fType.find(item => item.code === type.code)
      const { minQty, maxQty } = originType
      if (qtys >= (minQty || -Infinity) && qtys <= (maxQty || Infinity)) {
        ftCodes.delete(type.code)
      }
    }
  })
  newOrderItemMTypeList.forEach(type => {
    const qtys = type.newOrderItemMListList.reduce((acc, item) => {
      acc += toNumber(item.qty1)
      selectedQty += toNumber(item.qty1)
      return acc
    }, 0)
    if (mtCodes.has(type.code)) {
      const originType = mType.find(item => item.code === type.code)
      const { minQty, maxQty } = originType
      qtyMap.set(type.code, qtys)
      if (qtys >= (minQty || -Infinity) && qtys <= (maxQty || Infinity)) {
        mtCodes.delete(type.code)
      }
    }
  })
  foodTypeList.forEach(type => {
    const exceed = qtyMap.get(type.code) > toNumber(type.maxQty)
    if (ftCodes.has(type.code) && !type.minQty && !exceed) {
      ftCodes.delete(type.code)
    }
  })
  mTypeList.forEach(type => {
    const exceed = qtyMap.get(type.code) > toNumber(type.maxQty)
    if (mtCodes.has(type.code) && !type.minQty && !exceed) {
      mtCodes.delete(type.code)
    }
  })
  fType.length = 0
  mType.length = 0
  const satisfySelf = selectedQty >= (minQty2 || -Infinity) && selectedQty <= (maxQty2 || Infinity)
  const {
    satisfy: satisfyRequired,
    fCodes,
    mCodes
  } = checkFixedRequiredHelper(foodData, orderItemData)
  const satisfyFty = ftCodes.size === 0
  const satisfyMty = mtCodes.size === 0

  return {
    satisfy: satisfySelf && satisfyFty && satisfyMty && satisfyRequired,
    ftCodes,
    mtCodes,
    fCodes,
    mCodes,
    self: satisfySelf
  }
}
function checkRequiredQtyFilterHelper(typeList) {
  const codes = new Set()
  const types = []
  for (let i = 0; i < typeList.length; i++) {
    let type = typeList[i]
    if (type.minQty || type.maxQty) {
      codes.add(type.code)
      types.push(type)
    }
  }
  return { codes, types }
}
function checkFixedRequiredHelper(foodData, orderItemData) {
  const { foodList = [], mListList = [] } = foodData
  const { newOrderItemFoodList = [], newOrderItemMListList = [] } = orderItemData
  const fCodes = new Set()
  const mCodes = new Set()

  foodList.forEach((item, idx) => {
    const { satisfy } = checkRequiredQty(item, newOrderItemFoodList[idx])
    if (!satisfy) fCodes.add(item.fCode)
  })
  mListList.forEach((item, idx) => {
    const { satisfy } = checkRequiredQty(item, newOrderItemMListList[idx])
    if (!satisfy) mCodes.add(item.code)
  })
  return { satisfy: ![...fCodes, ...mCodes].length, fCodes, mCodes }
}

function toNumber(value) {
  value = +value
  return Number.isNaN(value) ? 0 : value
}
