// 精度缺失 (加法)
function floatAdd(a, b) {
  var c, d, e
  if (undefined == a || null == a || "" == a || isNaN(a)) {
    a = 0
  }
  if (undefined == b || null == b || "" == b || isNaN(b)) {
    b = 0
  }
  try {
    c = a.toString().split(".")[1].length
  } catch (f) {
    c = 0
  }
  try {
    d = b.toString().split(".")[1].length
  } catch (f) {
    d = 0
  }
  e = Math.pow(10, Math.max(c, d))
  return (floatMul(a, e) + floatMul(b, e)) / e
}
//小数减法
function floatSub(a, b) {
  let m = 0,
    n = 0, //记录a，b的小数位数
    d = a + "", //字符串化
    e = b + ""
  try {
    m = d.split(".")[1].length
  } catch (error) {
    m = 0
  }
  try {
    n = e.split(".")[1].length
  } catch (error) {
    n = 0
  }
  let maxInt = Math.pow(10, Math.max(m, n)) //将数字转换为整数的最大倍数
  return (floatMul(a, maxInt) - floatMul(b, maxInt)) / maxInt
}

//小数乘法
function floatMul(a, b) {
  var c = 0,
    d = a.toString(),
    e = b.toString()
  try {
    c += d.split(".")[1].length
  } catch (f) {}
  try {
    c += e.split(".")[1].length
  } catch (f) {}
  return (Number(d.replace(".", "")) * Number(e.replace(".", ""))) / Math.pow(10, c)
}

function floatMultiply(arg1, arg2) {
  arg1 = Number(arg1)
  arg2 = Number(arg2)
  if ((!arg1 && arg1 !== 0) || (!arg2 && arg2 !== 0)) {
    return null
  }
  arg1 = toNonExponential(arg1)
  arg2 = toNonExponential(arg2)
  var n1, n2
  var r1, r2 // 小数位数
  try {
    r1 = arg1.toString().split(".")[1].length
  } catch (e) {
    r1 = 0
  }
  try {
    r2 = arg2.toString().split(".")[1].length
  } catch (e) {
    r2 = 0
  }
  n1 = Number(arg1.toString().replace(".", ""))
  n2 = Number(arg2.toString().replace(".", ""))
  return (n1 * n2) / Math.pow(10, r1 + r2)
}
function toNonExponential(num) {
  if (num == null) {
    return num
  }
  if (typeof num == "number") {
    var m = num.toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/)
    return num.toFixed(Math.max(0, (m[1] || "").length - m[2]))
  } else {
    return num
  }
}
function floatDiv(arg1, arg2) {
  var t1 = 0,
    t2 = 0,
    r1,
    r2
  try {
    t1 = arg1.toString().split(".")[1].length
  } catch (e) {}
  try {
    t2 = arg2.toString().split(".")[1].length
  } catch (e) {}
  with (Math) {
    r1 = Number(arg1.toString().replace(".", ""))
    r2 = Number(arg2.toString().replace(".", ""))
    return (r1 / r2) * pow(10, t2 - t1)
  }
}
/* 
 toFixed() 四舍六入五考虑，五后非零就进一，五后为零看奇偶，五前为偶应舍去，五前为奇要进一。
 1.335.toFixed(2) // 1.33 错误
  */
function formatFloat(value, n) {
  //     保留n位小数并格式化输出（不足的部分补0忽略）
  var f = Math.round(value * Math.pow(10, n)) / Math.pow(10, n)
  var s = f.toString()
  // var rs = s.indexOf('.')
  // if (rs < 0) {
  //   s += '.'
  // }
  // for (var i = s.length - s.indexOf('.'); i <= n; i++) {
  //   s += '0'
  // }
  return s
}
