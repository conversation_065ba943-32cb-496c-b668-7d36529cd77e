Vue.component("registerPage", {
  props: ["systemLanguage", "openTable"],
  mixins: [registerAndResetMixin],
  template: `
<div class="member-warp" id="registerPopup">
  <div class="form-warp">
    <h2 class="form-title">{{ systemLanguage.registerPageTitle }}</h2>

    <form class="layui-form" action="">
      <div class="form-content register-form-content">
        <template v-for="field in formConfig">
          <div
            :key="field.name"
            class="layui-form-item"
            :class="{'error-field': formErrors[field.name]}"
            v-if="shouldShowField(field)"
          >
            <label
              class="layui-form-label"
              :class="{'required': isFieldRequired(field)}"
              :style="{width: labelWidth + 'px'}"
            >
              <span ref="formLabel">{{ authFormShowText('formLabel',field.name) }}</span>
            </label>

            <div class="layui-input-block" :style="{marginLeft: labelWidth + 'px'}">
              <!-- 普通输入框 -->
              <template v-if="field.type === 'input'">
                <input
                  :type="field.inputType || 'text'"
                  v-model="formData[field.name]"
                  class="layui-input"
                  :placeholder="authFormShowText('formPH',field.name)"
                  @blur="validateField(field.name)"
                />
              </template>

              <!-- 电话输入框 -->
              <template v-if="field.name === 'telephone'">
                <div class="phone-input-warp">
                  <custom-select
                    :system-language="systemLanguage"
                    :lan="currentLang"
                    @select-change="authFormHandleSelectChange"
                    ref="registerCustomSelect"
                  />
                  <input
                    :type="field.inputType || 'text'"  
                    v-model="formData[field.name]"
                    class="layui-input"
                    :placeholder="authFormShowText('formPH',field.name)"
                    @blur="validateField(field.name)"
                    @input="authFormFormatNumber($event, field.name)"
                  />
                </div>
              </template>

              <!-- 密码输入框 -->
              <template v-if="field.type === 'password'">
                <div
                  class="password-wrapper"
                  :class="[field.name === 'confirmPassword' ? confirmPasswordClass : '']"
                >
                  <div class="input-container">  
                    <input
                      :type="passwordVisible[field.name] ? 'text' : 'password'"
                      v-model.trim="formData[field.name]" 
                      class="layui-input"  
                      :placeholder="authFormShowText('formPH',field.name)" 
                      @input="authFormOnPasswordInput($event,field.name)"
                      @blur="authFormOnPasswordBlur(field.name)"
                    />
                    <div
                      class="eyeIcon"
                      :class="[passwordVisible[field.name] ? 'icon-eye-open' : 'icon-eye-close']"
                      @click="authFormPwdVisible(field.name)"
                    ></div>
                  </div>
                </div>
              </template>

              <!-- 单选框 --> 
              <template v-if="field.type === 'radio'">
                <input
                  v-for="option in field.options"
                  :key="option.value"
                  type="radio"  
                  :name="field.name" 
                  :value="option.value"
                  :title="authFormShowText('formLabel',option.label)"  
                  v-model="formData[field.name]" 
                />
              </template>

              <!-- 日期选 -->
              <template v-if="field.type === 'date'">
                <input
                  v-show="currentLang === 'zh'"
                  type="text"
                  v-model="formData[field.name]"
                  class="layui-input"
                  id="birthday_zh"
                  :placeholder="authFormShowText('formPH',field.name)"
                />
                <input
                  v-show="currentLang != 'zh'"
                  v-model="formData[field.name]"
                  type="text"
                  id="birthday_en"
                  class="layui-input"
                  :placeholder="authFormShowText('formPH',field.name)"
                />
              </template>
              <!-- 邮箱验证码输入框 -->
              <template v-if="field.type === 'emailVerifyCode'">
                <div class="verify-code-wrapper">
                  <input
                    type="text"
                    v-model="formData[field.name]"
                    class="layui-input"
                    :placeholder="authFormShowText('formPH',field.name)"
                    maxlength="6"
                    @blur="validateField(field.name)"
                    @input="authFormFormatNumber($event, field.name)"
                  />
                  <button
                    type="button"
                    class="layui-btn verify-code-btn"
                    @click="getCode('email')"
                    :class="{
                    'layui-btn-disabled': emailCodeCountingDown > 0 || emailLoading
                  }"
                  >
                    <i
                      v-if="emailLoading"
                      class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"
                    ></i>
                    <span v-else>{{ emailVerifyCodeMsg }}</span>
                  </button>
                </div>
              </template>
              <!-- 手机验证码输入框 -->
              <template v-if="field.type === 'phoneVerifyCode'">
                <div
                  class="verify-code-wrapper phoneVerifyCode"
                  :class="{'show': shouldShowVerifyCode}"
                >
                  <input
                    type="text"
                    v-model="formData[field.name]"
                    class="layui-input"
                    :placeholder="authFormShowText('formPH',field.name)"
                    maxlength="6"
                    @blur="validateField(field.name)"
                    @input="authFormFormatNumber($event, field.name)"
                  />
                  <button
                    type="button"
                    class="layui-btn verify-code-btn"
                    @click="getCode('telephone')"
                    :class="{'layui-btn-disabled': codeCountingDown > 0 || telephoneLoading}"
                  >
                    <i
                      v-if="telephoneLoading"
                      class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"
                    ></i>
                    <span v-else>{{ phoneVerifyCodeMsg }}</span>
                  </button>
                </div>
              </template>

              <div class="form-item-error" :class="{'show': formErrors[field.name]}">
                {{ formErrors[field.name] }}
              </div>
            </div>
          </div>
        </template>
      </div>

      <div class="member-form-actions">
        <div class="submit-btn" lay-submit @click.prevent="submitForm">
          {{ systemLanguage.registerPageSubmit }}
        </div>
        <div class="bottom-links-register" @click="goToLogin">
          {{ systemLanguage.registerPageHasAccount }}
          <a href="#" class="link-text">{{ systemLanguage.registerPageLogin }}</a>
        </div>
      </div>
    </form>
  </div>
</div>



  
  `,
  data() {
    return {
      labelWidth: 0,
      layerIndex: null,
      currentLang: "",
      // registerInfo: ["email", "telephone", "name", "password", "gender", "birthday", "emailVerifyCode"],
      registerInfo: [],
      formData: {
        name: "",
        email: "",
        emailVerifyCode: "",
        telephone: "",
        areaCode: "",
        phoneVerifyCode: "",
        password: "",
        confirmPassword: "",
        gender: "M", //M男;F女
        birthday: ""
      },
      passwordVisible: {
        password: false,
        confirmPassword: false
      },
      configCodeCountingDown: 60,
      codeCountingDown: 0,
      emailCodeCountingDown: 0,
      testCountingDown: 3,
      phoneVerifyCodeTimer: null,
      emailVerifyCodeTimer: null,
      telephoneLoading: false,
      emailLoading: false,
      formErrors: {},
      // 表单配置
      formConfig: [
        {
          name: "name",
          type: "input",
          required: false,
          validator: function (value) {
            return (value.length >= 2 && value.length <= 20) || this.systemLanguage.formFormatName
          }
        },
        {
          name: "email",
          type: "input",
          inputType: "email",
          required: true,
          validator: function (value) {
            if (!this.isEmailRequired && !value) {
              return true
            }
            return /@/.test(value) || this.systemLanguage.formFormatEmail
          }
        },
        {
          name: "emailVerifyCode",
          type: "emailVerifyCode",
          required: function () {
            return true
          },
          validator: function (value) {
            if (!value) {
              return this.systemLanguage.formReqVerifyCode
            }
            return /^\d{6}$/.test(value) || this.systemLanguage.formFormatVerifyCode
          }
        },
        {
          name: "telephone",
          type: "telephone",
          required: function () {
            return !this.registerInfo.includes("email")
          },
          validator: function (value) {
            if (!value) {
              return this.systemLanguage.formReqPhone
            }
            return true
          }
        },
        {
          name: "phoneVerifyCode",
          type: "phoneVerifyCode",
          required: function () {
            return !!this.formData.telephone // 只有当手机号存在时才必填
          },
          validator: function (value) {
            // 如果手机号为空，则验证码为选填
            if (!this.formData.telephone) {
              return true
            }
            if (!value) {
              return this.systemLanguage.formReqVerifyCode
            }
            return /^\d{6}$/.test(value) || this.systemLanguage.formFormatVerifyCode
          }
        },
        {
          name: "password",
          type: "password",
          required: true,
          validator: function (value) {
            return (
              (value.length >= 8 && value.length <= 20 && /^[a-zA-Z0-9]+$/.test(value)) ||
              this.systemLanguage.formFormatPassword
            )
          }
        },
        {
          name: "confirmPassword",
          type: "password",
          required: true,
          validator: function (value) {
            return this.formData.password === value || this.systemLanguage.formPasswordMismatch
          }
        },
        {
          name: "gender",
          type: "radio",
          required: false,
          options: [
            { value: "M", label: "male" },
            { value: "F", label: "female" }
          ]
        },
        {
          name: "birthday",
          type: "date",
          required: false
        }
      ]
    }
  },
  mounted() {},
  computed: {
    phoneVerifyCodeMsg() {
      return this.codeCountingDown > 0
        ? `${this.codeCountingDown}s`
        : this.systemLanguage.formGetCaptchaBtn
    },
    emailVerifyCodeMsg() {
      return this.emailCodeCountingDown > 0
        ? `${this.emailCodeCountingDown}s`
        : this.systemLanguage.formGetCaptchaBtn
    },
    formFields() {
      return this.registerInfo
    },
    isEmailRequired() {
      return this.registerInfo.includes("email")
    },
    isPhoneRequired() {
      return !this.registerInfo.includes("email") && this.registerInfo.includes("telephone")
    },

    confirmPasswordClass() {
      if (!this.formData.confirmPassword) return ""
      return this.formData.confirmPassword !== this.formData.password ? "password-mismatch" : ""
    },
    showPasswordTip() {
      return (
        this.formData.confirmPassword && this.formData.confirmPassword !== this.formData.password
      )
    },
    confirmPasswordTip() {
      return this.systemLanguage.passwordNotMatch
    },
    shouldShowVerifyCode() {
      // && /^1[3-9]\d{9}$/.test(this.formData.phone)
      return this.formData.telephone
    }
  },
  watch: {
    shouldShowVerifyCode(newVal) {
      if (!newVal) {
        this.$set(this.formErrors, "phoneVerifyCode", "")
      }
    },
    "formData.telephone": {
      handler(newVal) {
        if (newVal == "") {
          this.formData.phoneVerifyCode = ""
        }
      },
      deep: true
    }
  },
  methods: {
    initLaydate() {
      // 创建新实例
      layui.laydate.render({
        elem: `#birthday_zh`,
        lang: "cn",
        trigger: "click",
        theme: "#1E9FFF",
        calendar: true,
        max: "now",
        done: value => {
          this.formData.birthday = value
        }
      })
      layui.laydate.render({
        elem: `#birthday_en`,
        lang: "en",
        trigger: "click",
        theme: "#1E9FFF",
        calendar: true,
        max: "now",
        done: value => {
          this.formData.birthday = value
        }
      })
    },

    setRegisterPopIndex(index) {
      this.layerIndex = index
    },

    // handleSelectChange(selectedItem) {
    //   this.formData.areaCode = `+${selectedItem.phone[0]}`
    //   console.log("选择的项目：", selectedItem)
    // },
    getCode(type = "telephone") {
      const isPhone = type === "telephone"
      const countingDown = isPhone ? this.codeCountingDown : this.emailCodeCountingDown
      const btnLoading = isPhone ? this.telephoneLoading : this.emailLoading

      if (countingDown > 0 || btnLoading) return

      // 验证字段
      if (!this.validateField(type)) return

      // 重置验证码状态
      const resetVerifyCode = () => {
        clearInterval(isPhone ? this.phoneVerifyCodeTimer : this.emailVerifyCodeTimer)
        if (isPhone) {
          this.codeCountingDown = 0
          // this.telephoneLoading = false
        } else {
          this.emailCodeCountingDown = 0
          // this.emailLoading = false
        }
      }
      //错误逻辑
      const errorVerifyCodeCallback = err => {
        resetVerifyCode()
        this.authFormErrorVerifyCode(err)
      }
      // 构建请求数据
      let { telephone, areaCode, email } = this.formData

      let data = {
        type: "register",
        language: this.currentLang
      }
      data = isPhone ? { ...data, telephone, areaCode } : { ...data, email }

      // 发送验证码请求
      let typeInterface = isPhone ? "telephoneVerification" : "emailVerification"
      let url = `${API_PATH}member/${typeInterface}`
      this[`${type}Loading`] = true
      $.post({
        url: url,
        dataType: "json",
        data: JSON.stringify(data),
        contentType: "application/json",
        headers: {
          storeNumber: sessionStorage.getItem("storeNumber")
        },
        success: res => {
          if (res.statusCode == 200) {
            // 开始倒计时
            this.authFormStartCountdown(type)
            let { getPhoneVerifyCodeSuccess, getEmailVerifyCodeSuccess } = this.systemLanguage
            layer.msg(isPhone ? getPhoneVerifyCodeSuccess : getEmailVerifyCodeSuccess)
          } else {
            errorVerifyCodeCallback(res)
          }
        },
        error: err => {
          errorVerifyCodeCallback(err)
        },
        complete: () => {
          this[`${type}Loading`] = false
          // console.log("complete", this[`${type}Loading`])
        }
      })
      // this.authFormStartCountdown(type)
    },

    submitForm() {
      let isValid = true
      this.formConfig.forEach(field => {
        if (!this.validateField(field.name)) {
          // console.log("验证失败", field.name)
          isValid = false
        }
      })
      // console.log("提交的表单数据：", this.formData)

      if (!isValid) return

      var registerLoading = layer.load(2, {
        shade: [0.1, "#fff"] //0.1至0.8的值,支持0.1-1.0
      })

      // 构建请求数据
      let { areaCode, emailVerifyCode, phoneVerifyCode, confirmPassword, ...rest } = this.formData
      const data = {
        type: "register",
        emailVerificationCode: emailVerifyCode,
        telephoneVerificationCode: phoneVerifyCode,
        language: this.currentLang,
        ...rest
      }
      $.post({
        url: `${API_PATH}member/register`,
        dataType: "json",
        data: JSON.stringify(data),
        contentType: "application/json",
        headers: {
          storeNumber: sessionStorage.getItem("storeNumber")
        },
        success: res => {
          if (res.statusCode === 200) {
            this.successCallback()
          } else {
            this.authFormErrorCallback(res, "register")
          }
        },
        error: err => {
          this.authFormErrorCallback(err, "register")
        },
        complete: () => {
          layer.close(registerLoading)
        }
      })
    },

    successCallback() {
      let { registerSuccess } = this.systemLanguage
      layer.msg(registerSuccess, {
        time: 1000,
        end: () => {
          app.$root.$refs.personalCenter.formData = {
            account: this.formData.email || this.formData.telephone,
            password: this.formData.password
          }
          layer.close(this.layerIndex)
          app.$root.$refs.personalCenter.getUserInfo()
        }
      })
    },

    validateField(fieldName) {
      const field = this.formConfig.find(f => f.name === fieldName)
      if (!field) return

      const value = this.formData[fieldName]

      // 判断字段是否必填
      const isRequired =
        typeof field.required === "function" ? field.required.call(this) : field.required

      // 如果段不是必填且值为空，则清除错误信息并返回true
      if (!isRequired && !value) {
        this.$set(this.formErrors, fieldName, "")
        return true
      }

      // 如果是必填但值为空
      if (isRequired && !value) {
        if (fieldName === "confirmPassword") {
          this.$set(this.formErrors, fieldName, this.systemLanguage.formReqPassword)
        } else if (fieldName.endsWith("VerifyCode")) {
          this.$set(this.formErrors, fieldName, this.systemLanguage.formReqVerifyCode)
        } else {
          this.$set(
            this.formErrors,
            fieldName,
            this.systemLanguage[`formReq${this.authFormCapitalize(fieldName)}`]
          )
        }
        // console.log(this.formErrors, fieldName)

        return false
      }

      // 如果有值，则进行验证
      if (field.validator && value) {
        const result = field.validator.call(this, value)
        if (result !== true) {
          this.$set(this.formErrors, fieldName, result)
          return false
        }
      }

      this.$set(this.formErrors, fieldName, "")
      return true
    },
    shouldShowField(field) {
      if (
        field.name === "password" ||
        field.name === "confirmPassword" ||
        field.name === "emailVerifyCode"
      ) {
        return true
      }
      if (field.name === "phoneVerifyCode") {
        return this.shouldShowVerifyCode // 只有在填写了正确式的手机号时才显示
      }
      return this.registerInfo.includes(field.name)
    },
    isFieldRequired(field) {
      if (field.name === "email") {
        return this.isEmailRequired
      }
      if (field.name === "telephone") {
        return this.isPhoneRequired
      }
      if (field.name === "phoneVerifyCode") {
        return true
      }
      return typeof field.required === "function" ? field.required.call(this) : field.required
    },

    goToLogin() {
      //关闭弹窗
      layer.close(this.layerIndex)
    },
    resetValidation() {
      this.formErrors = {}
    },
    initFun() {
      let {
        language,
        memberAccountManagement: { personalDetails, verifyContact }
      } = this.openTable
      this.currentLang = language
      this.registerInfo = [...personalDetails, ...verifyContact].map(item =>
        this.authFormToLowerFirstLetter(item)
      )
      layui.use(["form", "laydate"], () => {
        var form = layui.form
        var laydate = layui.laydate
        this.initLaydate()
        form.render()
      })
    }
  }
})
