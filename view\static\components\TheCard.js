var cardBox = {
  props: ["zoneList"],
  template: `   <div class="zoneCard-mainBox">
  <el-row :gutter="15">
    <el-col :xs="24" :sm="8" :md="6" :lg="4" :xl="4" v-for="(item,index) in zoneList" class='el-col-box'>
      <el-card shadow="hover" class="zoneCard">
        <div class="zoneCard-header">
          <div class="zoneCard-header-place">{{item.name.en}}</div>
          <div class="zoneCard-header-EditBtn" @click="onEdit(item)"> <i class="el-icon-edit"></i></div>
        </div>
        <div class="zoneCard-content">
          <div class="zoneCard-content-warp">
            <div class="zoneCard-content-cell-icon">
              <img src="../../static/img/cms/subsetIcon.jpg" alt="" class="contentIcon">
            </div>
            <div class="zoneCard-content-cell-warp">
              <div class="cell-header">children number</div>
              <div class="cell-bottom">{{item.children?.length}}</div>
            </div>
          </div>
        </div>
        <div class="zoneCard-bottom">
          <div class="zoneCard-bottom-left" @click="onDelete(item)">
            <i class="el-icon-delete"></i>
          </div>
          <div class="zoneCard-bottom-right" @click="onNext(item)" v-if="item.children?.length">
            View all
            <i class="el-icon-right"></i>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
<div class="noDataBox" v-if='zoneList?.length==0'>No Data</div>

</div>
`,
  mounted() {
    // console.log(this.zoneList, "zoneList")
  },
  methods: {
    onEdit(item) {
      console.log(item)
      let cloneItem = JSON.parse(JSON.stringify(item))
      cloneItem.children = []
      app.editForm = cloneItem
      let activeObj = {
        1: "Zone 2",
        2: "Zone 3"
      }
      app.nextLevel = activeObj[app.activeName] // 编辑按钮下层文字
      app.editDialogVisible = true
    },
    onDelete(item) {
      let cloneItem = JSON.parse(JSON.stringify(item))
      cloneItem.children = [] // 只需要截止到当前层数
      let data = app.SplicingData(cloneItem)
      console.log(data, "删除")
      $.post({
        url: "../../manager_Zone/deleteByOne",
        data: JSON.stringify(data),
        dataType: "json",
        contentType: "application/json;charset=UTF-8",
        // traditional: true, // 防止深度序列号
        success: res => {
          if (res.statusCode == 200) {
            app.getData()
            // console.log(res);
            app.$message.success("Edit Success！")
            app.editDialogVisible = false
          } else {
            app.$message.error("Edit Failure！")
          }
        },
        error: error => {
          // console.log(res);
          app.$message.error("Edit Failure！")
        }
      })
    },
    onNext(item) {
      app.onNextPage(item)
      console.log(item)
    }
  }
}
