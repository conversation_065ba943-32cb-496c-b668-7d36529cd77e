@import "./customSelect.css";

#registerPopup,
#resetPasswordPopup {
  display: none;
  padding: 0.6rem 0.8rem 0rem;
  height: 100%;
  box-sizing: border-box;
}
#registerPopup .form-title {
  margin-bottom: 1rem;
}

.registeredWarp.layui-layer-page .layui-layer-content,
.resetPasswordWarp.layui-layer-page .layui-layer-content {
  overflow: hidden !important;
}
/* .layui-layer-page .layui-layer-content {
  overflow: hidden !important;
} */

.member-warp .layui-btn {
  padding: 0.23rem 0.32rem;
  background: #409eff;
  line-height: unset;
}

.member-warp .form-title {
  text-align: center;
  font-size: 0.5rem;
  /* margin-bottom: 1.2rem; */
  font-weight: 600;
  letter-spacing: 0.027rem;
  background: -webkit-repeating-linear-gradient(
    -45deg,
    rgb(64, 158, 255),
    rgb(64, 158, 255) 20px,
    rgb(103, 177, 255) 20px,
    rgb(103, 177, 255) 40px,
    rgb(64, 158, 255) 40px
  );
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  margin-bottom: 0.5rem;
  flex-shrink: 0;
}

.member-warp .form-title:after {
  content: " ";
  display: block;
  width: 100%;
  height: 2px;
  margin-top: 18px;
  background: linear-gradient(
    to right,
    rgba(64, 158, 255, 0) 0%,
    rgba(64, 158, 255, 0.8) 20%,
    rgba(64, 158, 255, 1) 53%,
    rgba(64, 158, 255, 0.8) 79%,
    rgba(64, 158, 255, 0) 100%
  );
}

.member-warp .layui-form-item {
  position: relative;
  margin-bottom: unset;
}

.member-warp .account-input-block {
  display: flex;
  /* align-items: center; */
}
/* #resetPasswordPopup.member-warp .layui-form-item:not([style*="display: none"]):first-of-type {
  margin-top: 0.3rem;
} */

#personalCenterPopup .member-warp .layui-form-label {
  /* width: 2.2rem; */
}
#personalCenterPopup .member-warp .layui-input-block {
  /* margin-left: 2.2rem; */
}

.member-warp .layui-form-label {
  padding: 0.16rem 0.267rem 0.16rem 0;
  font-size: 0.34rem;
  color: #606266;
  font-weight: 500;
  box-sizing: border-box;
  white-space: nowrap !important;
  text-overflow: unset !important;
}

.member-warp .layui-input {
  padding: 0.23rem 0.32rem;
  border-radius: 0.213rem;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
  font-size: 0.32rem;
  line-height: unset;
  /* background: #f8f9fa; */
}

.member-warp .required:before {
  content: "*";
  color: #ff4d4f;
  margin-right: 0.107rem;
  font-size: 0.373rem;
}

.member-warp .password-wrapper {
  position: relative;
}

.member-warp .eyeIcon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
  width: 0.47rem;
  height: 0.47rem;
  z-index: 2;
}
.member-warp .icon-eye-close {
  background: url("../../img/newImage/invisibleEyeIcon.jpg") no-repeat;
  background-size: 100%;
}
.member-warp .icon-eye-open {
  background: url("../../img/newImage/visibleEyeIcon.jpg") no-repeat;
  background-size: 100%;
}
.member-warp .form-item-error {
  font-size: 0.3rem;
  color: #ff4d4f;
  padding-left: 0.133rem;
  margin: 0.05rem 0 0.2rem;
  transition: all 0.3s;
  opacity: 0;
  min-height: 0.1rem;
  overflow: hidden;
}

.member-warp .form-item-error.show {
  opacity: 1;
  /* height: 0.4rem; */
}

.member-warp .error-field .layui-input,
.member-warp .error-field .account-list-wrapper {
  border-color: #ff4d4f !important;
}

.member-warp .layui-form-radio:hover *,
.member-warp .layui-form-radioed,
.member-warp .layui-form-radioed > i {
  color: #409eff !important;
}
/* 提交按钮公共样式 */
.member-warp .submit-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.3rem;
  /* width: 100%; */
  cursor: pointer;
  color: #fff;
  font-size: 0.35rem;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  background: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transition: all 0.2s ease;
  margin-bottom: 0.4rem;
}

.member-warp .submit-btn:hover {
  background: #66b1ff;
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.5);
  transform: translateY(-1px);
}

.member-warp .submit-btn:active {
  background: #3a8ee6;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
  transform: translateY(1px);
}

.member-warp .submit-btn-inline {
  margin-left: 0.25rem;
  background-color: #409eff;
  color: #fff;
  text-align: center;
  /* border-radius: 0.213rem; */
  border-radius: 3px;
  cursor: pointer;
  flex-shrink: 0;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.4rem;
  font-size: 0.33rem;
}

.member-warp .submit-btn-inline:hover {
  background-color: #66b1ff;
}

.member-warp .bottom-links {
  font-size: 0.373rem;
  color: #666;
}

.member-warp .link-text {
  /* font-weight: bold; */
  color: #15a3a3;
  text-decoration: none;
}
.member-warp .verify-code-btn {
  padding: 0.23rem 0.32rem;
  width: 2.4rem;
  font-size: 0.3rem;
  border-radius: 0.213rem;
  flex-shrink: 0;
  height: unset;
  color: #fff;
}
.member-warp .verify-code-wrapper {
  display: flex;
  gap: 0.267rem;
  align-items: center;
  max-height: none;
}
.phoneVerifyCode {
  overflow: hidden;
}

.member-warp .phone-input-warp {
  display: flex;
  gap: 0.3rem;
  /* margin-top: 0.3rem; 解决区号文字被挡住问题 */
}
.member-warp .phone-input-warp + .form-item-error {
  margin-left: 2.2rem;
}
.member-warp .bottom-links-register {
  font-size: 0.373rem;
  color: #666;
  text-align: center;
}

/* ------------------------重置密码---------------- */

/* 验证方式切换 */
.member-warp .verify-method {
  text-align: right;
  margin: 0.3rem 0 0.5rem;
  font-size: 0.3rem;
}

.member-warp .verify-method .switch-btn {
  color: #1e9fff;
  cursor: pointer;
  font-size: 0.35rem;
}

.member-warp .email-verify-wrapper {
  display: flex;
  align-items: center;
  gap: 0.267rem;
}

.member-warp .email-verify-wrapper .layui-input {
  flex: 1;
}

.member-warp .email-verify-wrapper .verify-link-btn {
  white-space: nowrap;
  padding: 0.23rem 0.32rem;
  font-size: 0.3rem;
  border-radius: 0.213rem;
  flex-shrink: 0;
  /* height: unset; */
}
/* 账号列表样式 */
.member-warp .account-list-wrapper {
  border: 1px solid #e6e6e6;
  border-radius: 0.1rem;
  padding: 0.2rem 0.2rem 0.1rem;
  max-height: 3rem;
  overflow-y: auto;
}

.member-warp .account-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 1;
  margin-bottom: 0.2rem;
}

.member-warp .account-list-title {
  font-weight: bold;
  color: #666;
}

.member-warp .account-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.member-warp .account-item:last-child {
  border-bottom: none;
}
/* 自定义复选框样式 */
.member-warp .account-item .layui-form-checkbox {
  margin-right: 10px !important;
}

/* 覆盖 layui 默认的复选框样式 */
.member-warp .layui-form-checked i {
  border-color: #409eff !important;
  background-color: #409eff !important;
}

.member-warp .layui-form-checkbox:hover i {
  border-color: #409eff !important;
}

/* 确保复选框显示 */

.member-warp .layui-form-checkbox i {
  width: 0.35rem;
  height: 0.35rem;
  line-height: 0.3rem;
  border: 1px solid #d2d2d2;
  font-size: 0.3rem;
  border-radius: 2px;
  margin-top: 0.05rem;
}
.member-warp .layui-form-checkbox span {
  font-size: 0.3rem;
}
.member-warp .get-account-btn {
  font-size: 0.3rem;
  color: #409eff;
}
.member-warp .layui-form-item .layui-form-checkbox {
  margin: 0.1rem 0;
}

.member-warp .bottom-links-reset {
  /* display: flex; */
  /* justify-content: space-between; */
  font-size: 0.373rem;
  color: #666;
  padding-top: 0.2rem;
}
.member-warp .bottom-links-reset p {
  text-align: center;
  cursor: pointer;
  color: #15a3a3;
}
.member-warp .bottom-links-reset p i {
  font-size: 0.32rem;
}
/* 修改 layui-form 的样式 */
.member-warp .layui-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.member-warp .layui-form .form-content {
  /* flex: 1; */
  min-height: 0;
  position: relative;
}
.member-warp .register-form-content {
  overflow: auto;
  flex: 1;
}

/* 确保底部按钮区域不滚动 */
.member-warp .member-form-actions {
  flex-shrink: 0;
  padding: 0.5rem 0;
  background: #fff;
  position: relative;
  z-index: 1;
}

/* 调整整体容器 */
.member-warp {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.member-warp .form-warp {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}
