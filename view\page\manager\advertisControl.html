<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../static/css/advertisControl.css" />
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/js/cms/resizeImg.js"></script>
    <script src="../../static/js/cms/interact.min.js"></script>
    <script src="../../static/js/linkTypeDetector.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />
    <style>
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .el-dialog__body {
        padding: 30px 20px 0px;
        color: #606266;
        font-size: 14px;
        word-break: break-all;
        /* max-height: 50vh; */
        overflow: auto;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          :data="tableData"
          style="width: 100%"
          :max-height="tableHeight"
          border
          :header-cell-style="{fontSize: '15px'}"
          empty-text="No Data"
        >
          <el-table-column width="230" label="Media" align="center">
            <template slot-scope="scope">
              <!-- 如果有External URL，根据类型渲染对应的媒体元素 -->
              <div v-if="scope.row.externalUrl && scope.row.externalUrl.trim() !== ''">
                <div v-if="getExternalUrlType(scope.row.externalUrl) === 'video'">
                  <video
                    :src="scope.row.externalUrl"
                    controls
                    style="max-width: 200px; max-height: 120px"
                    preload="metadata"
                  >
                    Your browser does not support video.
                  </video>
                </div>
                <div
                  v-else-if="getExternalUrlType(scope.row.externalUrl) === 'gif' || getExternalUrlType(scope.row.externalUrl) === 'img'"
                >
                  <img
                    :src="scope.row.externalUrl"
                    alt="External Media"
                    style="max-width: 200px; max-height: 120px; padding: 5px"
                    @error="handleExternalUrlError"
                  />
                </div>
                <div v-else>
                  <!-- 无法识别的链接类型，显示链接文本 -->
                  <div style="word-break: break-all; font-size: 12px; color: #666">
                    🔗 {{scope.row.externalUrl}}
                  </div>
                </div>
              </div>
              <!-- 否则显示图片 -->
              <div v-else>
                <img
                  style="padding: 5px"
                  v-for="item in scope.row.logoUrl"
                  :key="item"
                  :src="item"
                  alt=""
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="storeNumber" label="Store Number" align="center"></el-table-column>
          <el-table-column label="Image Name" align="center">
            <template slot-scope="scope">
              <div v-if="someMultipleImages.includes(scope.row.typeName)">
                {{scope.row.extraPaths?.split('/')[0]||''}}
              </div>
              <div v-else="">{{scope.row.fileName}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="use_date" label="Use Date" align="center"></el-table-column>
          <el-table-column prop="use_dow" label="Use Dow" align="center"></el-table-column>
          <el-table-column prop="use_time" label="Use Time" align="center"></el-table-column>
          <el-table-column label="Popup Link" align="center" width="200">
            <template slot-scope="scope">{{scope.row.url || ''}}</template>
          </el-table-column>
          <el-table-column
            prop="typeName"
            label="Image Type"
            align="center"
            width="160"
          ></el-table-column>
          <el-table-column prop="usable" label="Switch" align="center" width="100">
            <template slot-scope="scope">
              <el-switch
                v-model.trim="scope.row.usable"
                @change="onSwitch($event, scope.row)"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column prop label="Operation" align="center">
            <el-table-column width="100" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="dialogVisible = true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="handleEditor(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="handleDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 图片上传弹窗 -->
      <template>
        <el-dialog
          title="Add Dialog"
          :visible.sync="dialogVisible"
          @close="uploadCloseDialog('uploadForm')"
        >
          <el-form :model="uploadForm" ref="uploadForm" label-width="150px" :rules="rules">
            <!--            StoreNumber-->
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model.trim="uploadForm.storeNumber"
                style="width: 100%"
                placeholder="Please enter the store number"
              ></el-input>
            </el-form-item>
            <!--            ImageType-->
            <el-form-item label="Image Type" prop="typeName">
              <el-select
                v-model="uploadForm.typeName"
                placeholder="Please select the image type"
                style="width: 100%"
                @change="handleImageTypeChange"
              >
                <el-option
                  v-for="type in ImageType"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <div v-if="uploadForm.typeName">
              <!--            图片模板-->
              <el-form-item
                label="Template"
                prop="template"
                v-if="uploadForm.typeName==='Background Image'"
              >
                <el-select
                  v-model="uploadForm.template"
                  placeholder="Please select"
                  style="width: 100%"
                >
                  <el-option
                    v-for="tem in ImageTemplate"
                    :key="'template'+tem"
                    :label="tem"
                    :value="tem"
                  ></el-option>
                </el-select>
              </el-form-item>
              <div v-else-if="someMultipleImages.includes(uploadForm.typeName)">
                <!--            Tag Type-->
                <el-form-item label="Group type" prop="tagType">
                  <el-input
                    v-model.trim="uploadForm.tagType"
                    style="width: 100%"
                    placeholder="Please enter the image  group type"
                  ></el-input>
                </el-form-item>
              </div>
              <div v-else-if="['Company Logo'].includes(uploadForm.typeName)"></div>
              <div v-else>
                <!--            ImageName-->
                <el-form-item label="Image Name" prop="fileName" :rules="fileNameRules">
                  <el-input
                    v-model.trim="uploadForm.fileName"
                    style="width: 100%"
                    placeholder="Please enter the name of the picture"
                    :disabled="fileNameDisabled"
                  ></el-input>
                </el-form-item>
                <div
                  v-if="uploadForm.typeName==='Popup Image'||uploadForm.typeName==='Popup Thumbnail'"
                >
                  <el-form-item label="Popup Link" prop="url">
                    <el-input
                      v-model.trim="uploadForm.url"
                      style="width: 100%"
                      placeholder="Please enter the ad link"
                      clearable
                    ></el-input>
                  </el-form-item>

                  <el-form-item label="Time Control (Date)" prop="use_date">
                    <el-input
                      v-model="uploadForm.use_date"
                      placeholder="Format : yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="Time Control (Dow)" prop="use_dow">
                    <el-input
                      v-model="uploadForm.use_dow"
                      placeholder="Format : 1234567H  (1=Monday,7=Sunday,H=holidays)"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="Time Control (Time)" prop="use_time">
                    <el-input
                      v-model="uploadForm.use_time"
                      placeholder="Format : hh:mm-hh:mm  (Use ; to separate multiple times)"
                    ></el-input>
                  </el-form-item>
                  <!--            延迟关闭时间 for Popup Image only-->
                  <el-form-item
                    label="Closable After Delay"
                    v-if="uploadForm.typeName==='Popup Image'"
                  >
                    <el-input-number
                      v-model="uploadForm.closeableAfterDelay"
                      :min="0"
                      :max="999"
                      style="width: 200px"
                      controls-position="right"
                    ></el-input-number>
                    <span style="margin-left: 8px; color: #909399">seconds</span>
                  </el-form-item>
                </div>
              </div>
              <!--            外部链接 for Popup Image and Background Image (Template 1)-->
              <el-form-item label="External URL" prop="externalUrl" v-if="isShowExternalUrl">
                <el-input
                  v-model.trim="uploadForm.externalUrl"
                  style="width: 100%"
                  placeholder="Please enter the external link"
                  clearable
                  :disabled="uploadExternalUrlDisabled"
                  @input="handleUploadExternalUrlChange"
                ></el-input>
              </el-form-item>
              <el-form-item prop="imgList" label="Image List" :rules="imgListRules">
                <div v-if="uploadForm.typeName==='Background Image'">
                  <el-upload
                    v-for="item in templateName[uploadForm.template]"
                    :key="'temp'+item"
                    class="upload-demo"
                    :class="['upload'+ item, uploadImgListDisabled ? 'upload-disabled' : '']"
                    :ref="'upload'+item"
                    :data="uploadForm"
                    action="#"
                    :name="item"
                    :on-change="(file, fileList) => {handleUploadChange(file, fileList, item,'bcg')} "
                    :on-remove="(file, fileList) => {handleRemove(file, fileList,item, 'uploadForm')}"
                    :file-list="templateImageList[uploadForm.template][item]"
                    list-type="picture-card"
                    :auto-upload="false"
                    accept="image/jpeg,image/jpg"
                    :limit="1"
                    :disabled="uploadImgListDisabled"
                  >
                    <div class="el-upload__text">
                      <i class="el-icon-plus"></i>
                      <div class="upload-center-txt">{{item}}</div>
                    </div>
                  </el-upload>
                </div>
                <div v-else-if="uploadForm.typeName==='Image Tag'">
                  <el-upload
                    v-for="item in imageTagName"
                    :key="'temp'+item"
                    class="upload-demo"
                    :class="['upload'+ item, uploadImgListDisabled ? 'upload-disabled' : '']"
                    :ref="'upload'+item"
                    :data="uploadForm"
                    action="#"
                    :name="item"
                    :on-change="(file, fileList) => {handleUploadChange(file, fileList, item,'tag')} "
                    :on-remove="(file, fileList) => {handleRemove(file, fileList, item,'uploadForm')}"
                    :file-list="imageTagFileList[item]"
                    list-type="picture-card"
                    :auto-upload="false"
                    accept="image/jpeg,image/jpg"
                    :limit="1"
                  >
                    <div class="el-upload__text">
                      <i class="el-icon-plus"></i>
                      <div class="upload-center-txt">{{item}}</div>
                    </div>
                  </el-upload>
                </div>

                <div v-else-if="uploadForm.typeName==='Menu Popup Image'">
                  <el-upload
                    v-for="item in menuPopupImgName"
                    :key="'temp'+item"
                    class="upload-demo"
                    :class="['upload'+ item, uploadImgListDisabled ? 'upload-disabled' : '']"
                    :ref="'upload'+item"
                    :data="uploadForm"
                    action="#"
                    :name="item"
                    :on-change="(file, fileList) => {handleUploadChange(file, fileList, item,'menuPopImg')} "
                    :on-remove="(file, fileList) => {handleRemove(file, fileList, item,'uploadForm')}"
                    :file-list="menuPopupImgList[item]"
                    list-type="picture-card"
                    :auto-upload="false"
                    accept="image/jpeg,image/jpg"
                    :limit="1"
                  >
                    <div class="el-upload__text">
                      <i class="el-icon-plus"></i>
                      <!-- <div class="upload-center-txt">{{item}}</div> -->
                    </div>
                  </el-upload>
                </div>

                <div v-else style="display: flex">
                  <el-upload
                    class="upload-demo"
                    :class="uploadImgListDisabled ? 'upload-disabled' : ''"
                    ref="upload"
                    :data="uploadForm"
                    action="#"
                    :on-change="handleUploadChange"
                    :on-remove="handleRemove"
                    :file-list="uploadForm.imgList"
                    list-type="picture-card"
                    :auto-upload="false"
                    accept="image/jpeg,image/jpg"
                    :limit="1"
                    :disabled="uploadImgListDisabled"
                  >
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <resize-img
                    :logo_url="uploadForm.imgList"
                    :wrap_bcg_url="findBgiUrl(tableData,uploadForm)"
                    :rect="uploadForm.coordinate"
                    @rect="rect=>uploadForm.coordinate=rect"
                    v-if="uploadForm.typeName==='Company Logo'&&uploadForm.imgList?.length"
                  >
                    <el-button>Confirm</el-button>
                  </resize-img>
                </div>
              </el-form-item>

              <!--            Switch-->
              <el-form-item label="Switch">
                <el-switch v-model="uploadForm.usable"></el-switch>
              </el-form-item>
            </div>
          </el-form>
          <!-- 确认按钮 -->
          <div slot="footer">
            <el-button type="primary" @click="uploadCanvel('uploadForm')">Cancel</el-button>
            <el-button @click="submitForm('uploadForm')" style="margin-right: 8px">
              Confirm
            </el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 编辑弹窗 -->
      <template>
        <el-dialog
          title="Edit Dialog"
          :visible.sync="editDialogVisible"
          @close="uploadCloseDialog('editForm')"
        >
          <el-form :model="editForm" ref="editForm" label-width="150px" :rules="rules">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model.trim="editForm.storeNumber"
                style="width: 100%"
                placeholder="Please enter the store number"
              ></el-input>
            </el-form-item>
            <el-form-item label="Image Type">
              <el-select
                v-model="editForm.typeName"
                placeholder="Please select the image type"
                style="width: 100%"
                disabled
              >
                <el-option
                  v-for="type in ImageType"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="Template"
              prop="template"
              v-if="editForm.typeName==='Background Image'"
            >
              <el-select
                v-model="editForm.template"
                placeholder="Please select"
                style="width: 100%"
                disabled
              >
                <el-option
                  v-for="tem in ImageTemplate"
                  :key="'template'+tem"
                  :label="tem"
                  :value="tem"
                ></el-option>
              </el-select>
            </el-form-item>

            <div v-else-if="someMultipleImages.includes(editForm.typeName)">
              <!--            Tag Type-->
              <el-form-item label="Group type" prop="tagType">
                <el-input
                  v-model.trim="editForm.tagType"
                  style="width: 100%"
                  disabled
                  placeholder="Please enter the image  group type"
                ></el-input>
              </el-form-item>
            </div>
            <div v-else-if="editForm.typeName==='Company Logo'"></div>
            <div v-else>
              <el-form-item label="Image Name" prop="fileName" :rules="fileNameRules">
                <el-input
                  v-model.trim="editForm.fileName"
                  style="width: 100%"
                  placeholder="Please enter the name of the picture"
                  :disabled="fileNameDisabled"
                ></el-input>
              </el-form-item>
              <div v-if="editForm.typeName==='Popup Image'||editForm.typeName==='Popup Thumbnail'">
                <el-form-item label="Popup Link" prop="url">
                  <el-input
                    v-model.trim="editForm.url"
                    style="width: 100%"
                    placeholder="Please enter the ad link"
                    clearable
                  ></el-input>
                </el-form-item>

                <el-form-item label="Time Control (Date)" prop="use_date">
                  <el-input
                    v-model="editForm.use_date"
                    placeholder="Format : yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
                  ></el-input>
                </el-form-item>
                <el-form-item label="Time Control (Dow)" prop="use_dow">
                  <el-input
                    v-model="editForm.use_dow"
                    placeholder="Format : 1234567H  (1=Monday,7=Sunday,H=holidays)"
                  ></el-input>
                </el-form-item>
                <el-form-item label="Time Control (Time)" prop="use_time">
                  <el-input
                    v-model="editForm.use_time"
                    placeholder="Format : hh:mm-hh:mm  (Use ; to separate multiple times)"
                  ></el-input>
                </el-form-item>
                <!--            延迟关闭时间 for Popup Image only-->
                <el-form-item label="Closable After Delay" v-if="editForm.typeName==='Popup Image'">
                  <el-input-number
                    v-model="editForm.closeableAfterDelay"
                    :min="0"
                    :max="999"
                    style="width: 200px"
                    controls-position="right"
                  ></el-input-number>
                  <span style="margin-left: 8px; color: #909399">seconds</span>
                </el-form-item>
              </div>
            </div>
            <!--            外部链接 for Popup Image and Background Image (Template 1)-->
            <el-form-item label="External URL" prop="externalUrl" v-if="isShowExternalUrl">
              <el-input
                v-model.trim="editForm.externalUrl"
                style="width: 100%"
                placeholder="Please enter the external link"
                clearable
                :disabled="editExternalUrlDisabled"
                @input="handleEditExternalUrlChange"
              ></el-input>
            </el-form-item>
            <el-form-item label="Image List" prop="imgList" :rules="imgListRules">
              <div v-if="editForm.typeName =='Background Image'">
                <el-upload
                  v-for="item in templateName[editForm.template]"
                  :key="'temp'+item"
                  class="upload-demo uploadEdit"
                  :class="['upload'+ item, editImgListDisabled ? 'upload-disabled' : '']"
                  :ref="'editUpload'+item"
                  :data="editForm"
                  action="#"
                  :on-change="(file, fileList) => {handleUploadChange(file, fileList, item,'bcg')}"
                  :on-remove="(file, fileList) => {editRemove(file, fileList, item,'editForm')}"
                  :file-list="templateImageList[editForm.template][item]"
                  list-type="picture-card"
                  :auto-upload="false"
                  accept="image/jpeg,image/jpg"
                  :limit="1"
                  :disabled="editImgListDisabled"
                >
                  <div class="el-upload__text">
                    <i class="el-icon-plus"></i>
                    <div class="upload-center-txt">{{item}}</div>
                  </div>
                </el-upload>
              </div>
              <div v-else-if="editForm.typeName==='Image Tag'">
                <el-upload
                  v-for="item in imageTagName"
                  :key="'temp'+item"
                  class="upload-demo upload-image-tag"
                  :class="['upload'+ item, editImgListDisabled ? 'upload-disabled' : '']"
                  :ref="'upload'+item"
                  :data="editForm"
                  action="#"
                  :name="item"
                  :on-change="(file, fileList) => {handleUploadChange(file, fileList, item,'tag')} "
                  :on-remove="(file, fileList) => {handleRemove(file, fileList, item,'editForm')}"
                  :file-list="imageTagFileList[item]"
                  list-type="picture-card"
                  :auto-upload="false"
                  accept="image/jpeg,image/jpg"
                  :limit="1"
                >
                  <div class="el-upload__text">
                    <i class="el-icon-plus"></i>
                    <div class="upload-center-txt">{{item}}</div>
                  </div>
                </el-upload>
              </div>
              <div v-else-if="editForm.typeName==='Menu Popup Image'">
                <el-upload
                  v-for="item in menuPopupImgName"
                  :key="'temp'+item"
                  class="upload-demo"
                  :class="['upload'+ item, editImgListDisabled ? 'upload-disabled' : '']"
                  :ref="'upload'+item"
                  :data="editForm"
                  action="#"
                  :name="item"
                  :on-change="(file, fileList) => {handleUploadChange(file, fileList, item,'menuPopImg')} "
                  :on-remove="(file, fileList) => {handleRemove(file, fileList, item,'editForm')}"
                  :file-list="menuPopupImgList[item]"
                  list-type="picture-card"
                  :auto-upload="false"
                  accept="image/jpeg,image/jpg"
                  :limit="1"
                >
                  <div class="el-upload__text">
                    <i class="el-icon-plus"></i>
                    <!-- <div class="upload-center-txt">{{item}}</div> -->
                  </div>
                </el-upload>
              </div>
              <div v-else style="display: flex">
                <el-upload
                  class="upload-demo uploadEdit"
                  :class="editImgListDisabled ? 'upload-disabled' : ''"
                  ref="editUpload"
                  :data="editForm"
                  action="#"
                  :on-change="editUploadChange"
                  :on-remove="editRemove"
                  :file-list="editForm.imgList"
                  list-type="picture-card"
                  :auto-upload="false"
                  accept="image/jpeg,image/jpg"
                  :limit="1"
                  :disabled="editImgListDisabled"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>
                <resize-img
                  :logo_url="editForm.imgList"
                  :wrap_bcg_url="findBgiUrl(tableData,editForm)"
                  :rect="editForm.coordinate"
                  @rect="rect=>editForm.coordinate=rect"
                  v-if="editForm.typeName==='Company Logo'&&editForm.imgList?.length"
                >
                  <el-button>Confirm</el-button>
                </resize-img>
              </div>
            </el-form-item>

            <!--            Switch-->
            <el-form-item label="Switch">
              <el-switch v-model="editForm.usable"></el-switch>
            </el-form-item>
          </el-form>
          <!-- 确认按钮 -->
          <div slot="footer">
            <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
            <el-button @click="submitForm('editForm')" style="margin-right: 8px">Confirm</el-button>
          </div>
        </el-dialog>
      </template>
    </div>
    <script>
      const app = new Vue({
        el: "#app",
        data() {
          let backImg = (rule, value, callback) => {
            console.log(value, this.uploadForm, "backImg")
            let { typeName, template } = this.uploadForm
            let valid =
              typeName == "Menu Popup Image" ? value.length >= 1 : value.length == template
            if (!valid) {
              setTimeout(() => {
                callback(new Error(`Please select an image to upload!`))
              }, 100)
            } else {
              callback()
            }
          }

          return {
            dialog: "",
            tableHeight: 0,
            screenHeight: 800,
            value: true,
            dialogVisible: false,
            editDialogVisible: false,
            domain: sessionStorage.getItem("domain"),
            tableData: [],
            fileList: [],
            // uploadForm: {
            //   imgList: [],
            // },
            ImageType: [
              { label: "Allergen icons", value: "Allergen icons" },
              { label: "Store photo", value: "Store photo" },
              { label: "Store logo", value: "Store logo" },
              { label: "Queue Waiting photo", value: "Queue Waiting photo" },
              { label: "Background Image", value: "Background Image" },
              { label: "Image Tag", value: "Image Tag" },
              { label: "Company Logo", value: "Company Logo" },
              {
                label: "PC Version Background Image",
                value: "PC Version Background Image"
              },
              { label: "Popup Thumbnail", value: "Popup Thumbnail" },
              { label: "Popup Image", value: "Popup Image" },
              { label: "Menu Popup Image", value: "Menu Popup Image" },
              { label: "Member Center Image", value: "Member Center Image" }
            ],
            allMultipleImages: ["Background Image", "Image Tag", "Menu Popup Image"], //多图上传所有type归类(具备有差异但不多)
            someMultipleImages: ["Image Tag", "Menu Popup Image"], //多图上传所有type归类(基本一样)
            ImageTemplate: [1, 2, 3],
            uploadForm: {
              domain: sessionStorage.getItem("domain"),
              fileName: "", //图片名称
              typeName: "", //图片类型
              template: 1, //模板
              usable: true, //开关状态
              storeNumber: "", //店铺名
              tagType: "", //标签类型(区分类别,不仅限用于image tag类型使用)
              imgList: [],
              use_date: "",
              use_dow: "",
              use_time: "",
              url: "", //广告链接
              externalUrl: "", //外部链接
              closeableAfterDelay: 0, //延迟关闭时间(秒)
              coordinate: {
                width: "30%",
                left: "0%",
                top: "0%"
              }
            },
            //给模板用的,存放的用于显示图片的url
            templateImageList: {
              1: {
                indexBcg: []
              },
              2: {
                top: [],
                bottom: []
              },
              3: {
                top: [],
                center: [],
                bottom: []
              }
            },
            templateName: {
              1: ["indexBcg"],
              2: ["top", "bottom"],
              3: ["top", "center", "bottom"]
            },
            imageTagName: ["en", "zh", "thirdLan"],
            imageTagFileList: {
              en: [],
              zh: [],
              thirdLan: []
            },
            menuPopupImgName: ["1", "2", "3", "4", "5"],
            menuPopupImgList: {
              1: [],
              2: [],
              3: [],
              4: [],
              5: []
            },
            tempImgList: [],
            editForm: {
              id: 6,
              domain: sessionStorage.getItem("domain"),
              fileName: "logo", //图片名称
              typeName: "Popup Image", //图片类型
              template: null, //模板
              usable: false, //开关状态
              storeNumber: "", //店铺名
              tagType: "", //标签类型
              imgList: [],
              use_date: "",
              use_dow: "",
              use_time: "",
              url: "", // 广告图链接
              externalUrl: "", //外部链接
              closeableAfterDelay: 0, //延迟关闭时间(秒)
              coordinate: {
                width: "30%",
                left: "0%",
                top: "0%"
              } // logo 坐标
            },
            rules: {
              // imgList rules 移到 computed 中实现响应式
              externalUrl: [
                {
                  required: false,
                  validator: (rule, value, callback) =>
                    this.validateExternalUrl(rule, value, callback),
                  trigger: "blur"
                }
              ],
              storeNumber: [
                { required: true, message: "Please enter store number", trigger: "change" }
              ],
              typeName: [
                {
                  required: true,
                  message: "Please select image type",
                  trigger: "change"
                }
              ],
              tagType: [
                {
                  required: true,
                  message: "Please enter image  group type",
                  trigger: "change"
                },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: "Please enter a valid image  group type",
                  trigger: "change"
                }
              ],
              // fileName rules 移到 computed 中实现响应式
              template: [
                {
                  required: true,
                  message: "Please select image template",
                  trigger: "change"
                }
              ],
              url: [
                {
                  required: false,
                  message: "Please enter url",
                  trigger: "change"
                },
                {
                  pattern:
                    /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/,
                  message: "Please enter a valid url",
                  trigger: "change"
                }
              ]
            },
            imagOption: true
          }
        },
        created() {
          this.queryData()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整

          // imgList和fileName的required规则现在通过computed属性自动响应
        },
        computed: {
          isShowExternalUrl() {
            let form = this.dialogVisible ? this.uploadForm : this.editForm
            return (
              (form.typeName === "Background Image" && form.template == 1) ||
              form.typeName === "Popup Image"
            )
          },
          isMultimediaType() {
            let form = this.dialogVisible ? this.uploadForm : this.editForm
            let typeRes = ["Background Image", "Popup Image"].includes(form.typeName)
            return typeRes && form.externalUrl.trim() !== ""
          },
          handleImageLimit() {
            if (this.uploadForm.typeName === "Background Image") {
              return this.uploadForm.template
            } else {
              return 1
            }
          },
          // 判断是否为需要互斥验证的类型（只有Background Image和Popup Image支持可选填）
          isExclusiveType() {
            let form = this.dialogVisible ? this.uploadForm : this.editForm
            return ["Background Image", "Popup Image"].includes(form.typeName)
          },
          // 上传表单的禁用状态
          uploadExternalUrlDisabled() {
            // 对于Background Image，当template不为1时禁用External URL
            if (this.uploadForm.typeName === "Background Image" && this.uploadForm.template !== 1) {
              return true
            }
            return (
              this.isExclusiveType && this.uploadForm.imgList && this.uploadForm.imgList.length > 0
            )
          },
          uploadImgListDisabled() {
            let res =
              this.isExclusiveType &&
              this.uploadForm.externalUrl &&
              this.uploadForm.externalUrl.trim() !== ""

            // console.log(res, 56)
            return !!res
            // return (
            //   this.isExclusiveType &&
            //   this.uploadForm.externalUrl &&
            //   this.uploadForm.externalUrl.trim() !== ""
            // )
          },
          // 编辑表单的禁用状态
          editExternalUrlDisabled() {
            // 对于Background Image，当template不为1时禁用External URL
            if (this.editForm.typeName === "Background Image" && this.editForm.template !== 1) {
              return true
            }
            return this.isExclusiveType && this.editForm.imgList && this.editForm.imgList.length > 0
          },
          // fileName 的响应式验证规则
          fileNameRules() {
            let form = this.dialogVisible ? this.uploadForm : this.editForm
            let isRequired = true

            // Popup Image类型：有externalUrl时不必填，无externalUrl时必填
            if (form.typeName === "Popup Image") {
              isRequired = !(form.externalUrl && form.externalUrl.trim() !== "")
            }

            return [
              {
                required: isRequired,
                message: "Please enter image name",
                trigger: "change"
              },
              {
                pattern: /^[a-zA-Z0-9_]+$/,
                message: "Please enter a valid image name",
                trigger: "change"
              }
            ]
          },
          // imgList 的响应式验证规则
          imgListRules() {
            let form = this.dialogVisible ? this.uploadForm : this.editForm
            let isRequired = true

            // 只有特定条件下才是可选的（支持互斥验证）
            if (form.typeName === "Background Image") {
              // Background Image: 只有template为1时才支持External URL，才是可选的
              isRequired = form.template != 1
            } else if (form.typeName === "Popup Image") {
              // Popup Image: 支持External URL，是可选的
              isRequired = false
            }
            // 其他类型都是必填的

            return [
              {
                required: isRequired,
                validator: (rule, value, callback) => this.validateImgList(rule, value, callback),
                trigger: "change"
              }
            ]
          },
          editImgListDisabled() {
            let res =
              this.isExclusiveType &&
              this.editForm.externalUrl &&
              this.editForm.externalUrl.trim() !== ""

            return !!res
          },
          // fileName禁用状态（合并上传和编辑表单）
          fileNameDisabled() {
            const form = this.dialogVisible ? this.uploadForm : this.editForm

            if (form.typeName === "Popup Image") {
              // Popup Image类型：有externalUrl时禁用
              return form.externalUrl != ""
            } else {
              // 其他类型：编辑表单中禁用，上传表单中不禁用
              return !this.dialogVisible
            }
          }
        },

        mounted() {
          // window.addEventListener('resize', this.throttle(function(event){
          //   this.screenHeight=event.target.innerHeight-100
          //   console.log(this.screenHeight)
          // }, 300))
        },
        components: {
          "resize-img": ResizeImg
        },
        methods: {
          // 获取External URL的类型（用于渲染判断）
          getExternalUrlType(url) {
            // 使用同步方法检测类型（基于文件扩展名）
            return detectTypeSync(url)
          },
          // 处理External URL加载错误
          handleExternalUrlError(event) {
            console.error("External URL加载失败:", event.target.src)
            // 可以在这里添加错误处理逻辑，比如显示默认图片或错误提示
          },
          // 处理Image Type变化，清除整个表单的校验状态
          handleImageTypeChange(typeName) {
            // 清除整个表单的校验状态
            this.$nextTick(() => {
              if (this.$refs.uploadForm) {
                this.$refs.uploadForm.clearValidate()
              }
            })

            // 重置相关数据（保留原有的数据重置逻辑）
            this.uploadForm.imgList = []
            this.tempImgList = []
            $(".el-upload--picture-card").show()

            // 根据类型设置默认template值
            let mapTemplate = {
              "Image Tag": 3,
              "Menu Popup Image": 5
            }
            this.uploadForm.template = mapTemplate[typeName] || 1

            // 对于不支持External URL的类型，清空External URL
            if (!["Background Image", "Popup Image"].includes(typeName)) {
              this.uploadForm.externalUrl = ""
            }

            // imgList和fileName的required规则现在通过computed属性自动响应
          },

          // 动态验证imgList
          validateImgList(rule, value, callback) {
            const form = this.dialogVisible ? this.uploadForm : this.editForm
            // 只有 Background Image 和 Popup Image 支持互斥验证（可选填）
            const isOptionalType = ["Background Image", "Popup Image"].includes(form.typeName)

            if (!isOptionalType) {
              // 非可选类型，使用统一的图片验证逻辑
              return this.validateImageUpload(rule, value, callback)
            }

            // 对于isOptionalType，只有在有externalUrl的情况下才可以不上传图片
            const hasExternalUrl = form.externalUrl && form.externalUrl.trim() !== ""

            if (hasExternalUrl) {
              // 有外部链接时，图片可以为空，但不能同时存在
              if (value && value.length > 0) {
                callback(new Error("Please choose either image upload or external URL, not both"))
              } else {
                callback()
              }
            } else {
              // 没有外部链接时，使用统一的图片验证逻辑
              return this.validateImageUpload(rule, value, callback)
            }
          },

          // 验证图片上传（统一的图片验证逻辑）
          validateImageUpload(rule, value, callback) {
            const form = this.dialogVisible ? this.uploadForm : this.editForm
            let { typeName, template } = form
            let requiredCount = 0
            let valid = false

            // 根据类型确定所需图片数量
            if (typeName == "Menu Popup Image") {
              requiredCount = 1
              valid = value.length >= requiredCount
            } else if (typeName == "Background Image") {
              requiredCount = template
              valid = value.length == requiredCount
            } else if (typeName == "Image Tag") {
              requiredCount = 3
              valid = value.length == requiredCount
            } else {
              // 其他类型（包括 Popup Thumbnail）都要求至少有一张图片
              requiredCount = 1
              valid = value.length >= requiredCount
            }

            if (!valid) {
              const uploadedCount = value ? value.length : 0
              setTimeout(() => {
                callback(
                  new Error(`Please select an image to upload! (${uploadedCount}/${requiredCount})`)
                )
              }, 100)
            } else {
              callback()
            }
          },
          // 动态验证externalUrl
          validateExternalUrl(rule, value, callback) {
            const form = this.dialogVisible ? this.uploadForm : this.editForm
            // 只有 Background Image 和 Popup Image 支持互斥验证（可选填）
            const isOptionalType = ["Background Image", "Popup Image"].includes(form.typeName)

            if (!isOptionalType) {
              // 非可选类型，不需要验证externalUrl
              callback()
              return
            }

            // Background Image的Template不为1时不校验External URL
            if (form.typeName === "Background Image" && form.template !== 1) {
              callback()
              return
            }

            // 可选类型的互斥验证逻辑
            const hasExternalUrl = value && value.trim() !== ""
            const hasImgList = form.imgList && form.imgList.length > 0

            if (!hasExternalUrl && !hasImgList) {
              callback(new Error("Please upload an image or enter an external URL"))
            } else if (hasExternalUrl && hasImgList) {
              callback(new Error("Please choose either image upload or external URL, not both"))
            } else {
              callback()
            }
          },

          // 处理上传表单的externalUrl变化
          handleUploadExternalUrlChange(value) {
            // 清除externalUrl、imgList和fileName的校验
            this.$refs.uploadForm.clearValidate(["externalUrl", "imgList", "fileName"])

            if (this.isExclusiveType && value && value.trim() !== "") {
              // 清空图片列表
              this.uploadForm.imgList = []
              // 清空模板相关的图片列表
              if (this.uploadForm.typeName === "Background Image") {
                Object.keys(this.templateImageList).forEach(template => {
                  Object.keys(this.templateImageList[template]).forEach(key => {
                    this.templateImageList[template][key] = []
                  })
                })
              }
              this.tempImgList = []
            }

            // 对于Popup Image类型，当有externalUrl时清空fileName
            if (this.uploadForm.typeName === "Popup Image" && value && value.trim() !== "") {
              this.uploadForm.fileName = ""
            }

            // fileName的required状态现在通过computed属性自动响应
          },
          // 处理编辑表单的externalUrl变化
          handleEditExternalUrlChange(value) {
            // 清除externalUrl、imgList和fileName的校验
            this.$refs.editForm.clearValidate(["externalUrl", "imgList", "fileName"])

            if (this.isExclusiveType && value && value.trim() !== "") {
              // 清空图片列表
              this.editForm.imgList = []
            }

            // 对于Popup Image类型，当有externalUrl时清空fileName
            if (this.editForm.typeName === "Popup Image" && value && value.trim() !== "") {
              this.editForm.fileName = ""
            }

            // fileName的required状态现在通过computed属性自动响应
          },
          // 处理上传表单的图片变化
          handleUploadImgChange() {
            // 清除externalUrl和imgList的校验
            this.$refs.uploadForm.clearValidate(["externalUrl", "imgList"])

            if (
              this.isExclusiveType &&
              this.uploadForm.imgList &&
              this.uploadForm.imgList.length > 0
            ) {
              // 清空外部链接
              this.uploadForm.externalUrl = ""
            }
          },
          // 处理编辑表单的图片变化
          handleEditImgChange() {
            // 清除externalUrl和imgList的校验
            this.$refs.editForm.clearValidate(["externalUrl", "imgList"])

            if (this.isExclusiveType && this.editForm.imgList && this.editForm.imgList.length > 0) {
              // 清空外部链接
              this.editForm.externalUrl = ""
            }
          },

          debounce(fn, delay) {
            let time = null
            let timer = null
            let newTime = null
            function task() {
              newTime = +new Date()
              if (newTime - time < delay) {
                timer = setTimeout(task, delay)
              } else {
                fn()
                timer = null
              }
              time = newTime
            }
            return function () {
              // 更新时间戳
              time = +new Date()
              if (!timer) {
                timer = setTimeout(task, delay)
              }
            }
          },
          throttle(func, delay) {
            var prev = Date.now()
            return function () {
              var context = this
              var args = arguments
              var now = Date.now()
              if (now - prev >= delay) {
                func.apply(context, args)
                prev = Date.now()
              }
            }
          },
          // 查询表格数据
          queryData() {
            let that = this
            let data = {
              domain: that.domain
            }

            $.get({
              url: "../../manager_photo/getAllPhotoConfig",
              dataType: "json",
              data,
              success: function (res) {
                console.log(res)

                let data = res.photoConfigList

                data = [...data].sort((a, b) => {
                  // 先按typeName排序
                  if (a.typeName !== b.typeName) {
                    return a.typeName.localeCompare(b.typeName)
                  }
                  // typeName相同时按storeNumber排序
                  return a.storeNumber.localeCompare(b.storeNumber)
                })

                for (let i = 0; i < data.length; i++) {
                  let item = data[i]
                  item.logoUrl = []
                  // 如果externalUrl字段存在且有值，则跳过本次循环
                  if (
                    (item.externalUrl && item.externalUrl.trim() !== "") ||
                    item.fileName === undefined ||
                    item.fileName === null
                  ) {
                    continue
                  }
                  // console.log(item, 56)
                  const nameArr = item.fileName.split(";")
                  // Menu Popup Image根据1;2;3排序
                  if (item.typeName == "Menu Popup Image") {
                    nameArr.sort((a, b) => {
                      return a.localeCompare(b)
                    })
                  }
                  let storeNumber = that.splitStoreNum(item.storeNumber) //根据;截取第一个数据

                  let u = that.allMultipleImages.includes(item.typeName) ? item.extraPaths : ""
                  nameArr.forEach(el => {
                    let t = item.typeName !== "Landing Image" ? ".jpg" : ".jpg"
                    const url =
                      "http://appwise.oss-cn-hongkong.aliyuncs.com" +
                      "/" +
                      item.domain +
                      "/" +
                      storeNumber +
                      "/image/" +
                      item.typeName +
                      `/${u}` +
                      el +
                      t +
                      "?x-oss-process=image/resize,w_60,h_60&" +
                      new Date().getTime()
                    item.logoUrl.push(url)
                  })
                }

                console.log(JSON.parse(JSON.stringify(data)))
                that.tableData = data

                // that.$message.success('編輯成功！');
                // that.editDialogVisible = false;
              },
              error: function () {
                // that.$message.error('編輯失敗！');
              }
            })
          },
          // 开关触发
          onSwitch($event, row) {
            let {
              id,
              fileName,
              typeName,
              extraPaths,
              usable,
              domain,
              storeNumber,
              use_date = "",
              use_dow = "",
              use_time = "",
              url = "", // 广告图链接
              externalUrl = "",
              closeableAfterDelay = 0,
              coordinate
            } = row

            var fd = new FormData()
            fd.append("id", id)
            fd.append("usable", usable)
            fd.append("typeName", typeName)
            fd.append("domain", domain)
            fd.append("storeNumber", storeNumber)
            fd.append("use_date", use_date)
            fd.append("use_dow", use_dow)
            fd.append("use_time", use_time)
            fd.append("url", url)
            fd.append("externalUrl", externalUrl)
            fd.append("closeableAfterDelay", closeableAfterDelay)
            fd.append("file", "")
            fd.append("photoSuffix", "jpg")
            if (typeName == "Background Image" || this.someMultipleImages.includes(typeName)) {
              fd.append("extraPaths", extraPaths)
            }
            if (!externalUrl) {
              fd.append("finalFileName", fileName)
            }

            if (typeName == "Company Logo") {
              // 兼容旧数据
              let defaultRect = {
                width: "30%",
                left: "0%",
                top: "0%"
              }
              let rect = typeof coordinate == "string" || !coordinate ? defaultRect : coordinate
              fd.append("coordinate", JSON.stringify(rect))
            }
            // 过滤玄学null值
            for (var [key, value] of fd.entries()) {
              if (value == "null") fd.set(key, "")
            }
            $.post({
              url: "../../manager_photo/uploadPhotoConfig",
              dataType: "json",
              processData: false,
              contentType: false,
              data: fd,
              success: res => {
                if (res.errFlag1) {
                  this.$message.error(res.errMessage1)
                } else {
                  this.$message.success("Edit success！")
                  this.editDialogVisible = false
                  this.queryData()
                }
                // 查询
              },
              error: () => {
                this.$message.error("Edit failure！")
              }
            })
          },

          //点击提交按钮验证表单
          submitForm(form) {
            console.log(this[form], form, "this.$refs[form]")
            this.$refs[form].validate(valid => {
              if (valid) {
                // 判断form信息是否有重复
                let { typeName, imgList } = form == "uploadForm" ? this.uploadForm : this.editForm
                let data = this.tableData
                let isRepeat = false //用一个状态判断是否重复
                let verifyArr = []
                switch (typeName) {
                  case "Popup Image":
                    verifyArr = ["storeNumber", "typeName", "fileName"]
                    break
                  case "PC Version Background Image":
                  case "Allergen icons":
                  case "Queue Waiting photo":
                  case "Store photo":
                  case "Store logo":
                    verifyArr = ["storeNumber", "typeName", "fileName"]
                    break
                  case "Background Image":
                    verifyArr = ["storeNumber", "typeName", "fileName", "template"]
                    break
                  case "Image Tag":
                    verifyArr = ["storeNumber", "typeName", "fileName"]
                    break
                  default:
                    verifyArr = ["storeNumber", "typeName", "fileName"]
                    break
                }
                for (let i = 0; i < data.length; i++) {
                  let o = 0
                  let item = data[i]
                  verifyArr.forEach(el => {
                    if (item[el] === this.uploadForm[el]) {
                      o++
                    }
                  })
                  if (o == verifyArr.length && form == "uploadForm") {
                    this.$message.error("The data already exists. Do not submit it again")
                    isRepeat = true
                    break
                  }
                }

                if (!isRepeat) {
                  // console.log('可以提交，不重复');
                  // this.$refs.upload.submit()
                  //满足图片文件名称
                  if (this.beforeAvatarUpload(imgList, form)) {
                    this.submitImage(form)
                  }
                }
              } else {
                console.log("error submit!!")
                return false
              }
            })
          },
          //提交新增\更新
          submitImage(form) {
            const {
              usable,
              typeName,
              template,
              tagType,
              storeNumber,
              imgList,
              fileName,
              use_date = "",
              use_dow = "",
              use_time = "",
              url = "",
              externalUrl = "",
              closeableAfterDelay = 0,
              coordinate
            } = form == "uploadForm" ? this.uploadForm : this.editForm
            const domain = sessionStorage.getItem("domain")
            let fd = new FormData()
            fd.append("usable", usable)
            fd.append("typeName", typeName)
            fd.append("domain", domain)
            fd.append("storeNumber", storeNumber)
            fd.append("use_date", use_date)
            fd.append("use_dow", use_dow)
            fd.append("use_time", use_time)
            fd.append("url", url)
            fd.append("externalUrl", externalUrl)
            fd.append("closeableAfterDelay", closeableAfterDelay)
            fd.append("photoSuffix", "jpg")
            if (form == "editForm") {
              fd.append("id", this.editForm.id)
            }
            // 过滤玄学null值
            for (var [key, value] of fd.entries()) {
              if (value == "null") fd.set(key, "")
            }
            if (typeName == "Company Logo") {
              // 兼容旧数据
              let defaultRect = {
                width: "30%",
                left: "0%",
                top: "0%"
              }
              let rect = typeof coordinate == "string" || !coordinate ? defaultRect : coordinate
              fd.append("coordinate", JSON.stringify(rect))
            }
            //判断是否需要处理fileName,例如多类型媒体参数则不需要,直接储存url
            if (!this.isMultimediaType) {
              if (this.allMultipleImages.includes(typeName)) {
                let lastArr = []
                if (form == "editForm") {
                  //找出新增加的,并排序
                  const newArr = this.tempImgList
                    .filter(el => el.file && el.file.size)
                    .sort(function (a, b) {
                      return a.sort - b.sort
                    })
                  //找出不需要的,将name保存,用于排除fileName
                  this.tempImgList
                    .filter(el => !el.file || !el.file.size)
                    .forEach(el => {
                      lastArr.push((el.file && el.file.name) || JSON.stringify(el.sort + 1))
                    })

                  newArr.forEach(el => {
                    fd.append("file", el.file.raw)
                  })
                } else {
                  this.tempImgList.sort(function (a, b) {
                    return a.sort - b.sort
                  })
                  this.tempImgList.forEach(el => {
                    if (el.file) {
                      fd.append("file", el.file.raw)
                    }
                  })
                }
                // return
                if (this.someMultipleImages.includes(typeName)) {
                  let str = this.handleRequestFileName(typeName, lastArr)
                  if (str) {
                    fd.append("fileName", str)
                  }
                  //Menu Popup Image类型添加finalFileName用于标注存在的图片(已存在的图片+新增的图片)
                  if (typeName == "Menu Popup Image") {
                    let finalFileName = str ? str.split(";") : []
                    imgList.forEach(el => {
                      if (el.file) {
                        finalFileName.push(el.sort + 1)
                      }
                    })
                    finalFileName.sort((a, b) => {
                      return a - b
                    })
                    fd.append("finalFileName", finalFileName.join(";"))
                  }
                  fd.append("extraPaths", tagType + "/")
                }
                if (typeName == "Background Image") {
                  fd.append("extraPaths", template + "/")
                  let changedFileName = this.handleRequestFileName(
                    "Background Image",
                    lastArr,
                    template
                  )

                  if (form === "editForm") {
                    if (changedFileName) {
                      fd.append("fileName", changedFileName)
                    }
                  } else {
                    switch (template) {
                      case 1:
                        fd.append("fileName", "indexBcg")
                        break
                      case 2:
                        fd.append("fileName", "top;bottom")
                        break
                      case 3:
                        fd.append("fileName", "top;center;bottom")
                        break
                    }
                  }
                }
              } else {
                let file = imgList[0].raw
                fd.append("file", file)
                if (form === "editForm") {
                  if (file) {
                    fd.append("fileName", fileName)
                  } else {
                    fd.delete("fileName")
                  }
                } else {
                  fd.append("fileName", fileName)
                }
              }
            } else {
              //Background Image存在外部链接externalUrl情况下固定模板1
              if (typeName == "Background Image") fd.append("extraPaths", "1/")
            }
            //编辑必传参数finalFileName
            this.handleFinalFileName(fd, typeName, form, template, externalUrl, fileName)
            //保底抛出异常
            if (form == "editForm" && externalUrl == "" && !fd.get("finalFileName")) {
              this.$message.error("Submit failed, please verify the submission data")
              //查看fd每个参数
              for (var [key, value] of fd.entries()) {
                console.log(key, value)
              }
              return
            }
            $.post({
              url: "../../manager_photo/uploadPhotoConfig",
              dataType: "json",
              processData: false,
              contentType: false,
              data: fd,
              success: res => {
                if (form == "uploadForm") {
                  this.handleAvatarSuccess(res)
                } else {
                  this.editAvatarSuccess(res)
                }
              },
              error: () => {
                if (form == "uploadForm") {
                  this.$message.error("Fail to upload!")
                } else {
                  this.$message.error("Edit failure！")
                }
              }
            })
          },
          /**
           * @description 判断哪张图片修改了
           * @returns {string} fileName仅在图片发生更改时传递
           * */
          handleRequestFileName(type, lastArr, template) {
            switch (type) {
              case "Menu Popup Image": {
                let editArray = ["1", "2", "3", "4", "5"]
                let addArray = this.tempImgList.reduce(function (prev, cur, index, arr) {
                  if (cur.file) {
                    return [...prev, JSON.stringify(cur.sort + 1)] //下标加一对应图片索引从1开始
                  } else {
                    return prev
                  }
                }, [])
                let array = this.dialogVisible ? addArray : editArray
                let str = array.filter(el => !lastArr.includes(el)).join(";")
                return str
              }
              case "Image Tag": {
                let lan = ["en", "zh", "thirdLan"]
                let str = lan.filter(el => !lastArr.includes(el)).join(";")
                return str
              }
              case "Background Image": {
                let str = ""
                const oo = lastArr.concat(this.templateName[template]).filter((v, i, arr) => {
                  return arr.indexOf(v) === arr.lastIndexOf(v)
                })
                oo.forEach(i => {
                  str += i + ";"
                })
                str = str.substring(0, str.lastIndexOf(";"))
                return str
              }
              default:
                return ""
            }
          },
          handleUploadChange(file, fileList, item, tap) {
            //item存在则typeName为back,反之
            if (item) {
              console.log(this.templateImageList)

              $(`.upload${item} .el-upload--picture-card`).hide()
              if (tap === "bcg") {
                console.log(this.templateImageList)
                let sort = ["indexBcg", "top", "center", "bottom"].indexOf(item)
                let i = this.tempImgList.findIndex(el => el.sort == sort)
                if (i !== -1) {
                  this.tempImgList.splice(i, 1, { sort, file })
                } else {
                  this.tempImgList.push({ sort, file })
                }
              } else if (tap === "tag") {
                let sort = this.imageTagName.indexOf(item)
                let i = this.tempImgList.findIndex(el => el.sort == sort)
                if (i !== -1) {
                  this.tempImgList.splice(i, 1, { sort, file })
                } else {
                  this.tempImgList.push({ sort, file })
                }
              } else if (tap === "menuPopImg") {
                let sort = this.menuPopupImgName.indexOf(item)
                let i = this.tempImgList.findIndex(el => el.sort == sort)
                if (i !== -1) {
                  this.tempImgList.splice(i, 1, { sort, file })
                } else {
                  this.tempImgList.push({ sort, file })
                }
              }

              // 确保dialog变量正确设置
              const currentDialog = this.dialogVisible ? "uploadForm" : "editForm"
              this[currentDialog].imgList.push(file)
            } else {
              $(".upload-demo .el-upload--picture-card").hide()
              $(".upload-one .el-upload--picture-card").hide()
              // 确保dialog变量正确设置
              const currentDialog = this.dialogVisible ? "uploadForm" : "editForm"
              this[currentDialog].imgList = fileList
            }

            // 处理互斥逻辑
            if (this.dialogVisible) {
              this.handleUploadImgChange()
            } else if (this.editDialogVisible) {
              this.handleEditImgChange()
            }
          },
          // 上传成功后的回调
          handleAvatarSuccess(res, file, fileList) {
            if (res.errMessage1) {
              this.$message.error(res.errMessage1)
            } else {
              this.$message.success("Successfully upload ！")
              setTimeout(() => {
                try {
                  this.$refs.upload.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.uploadindexBcg.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.uploadtop.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.uploadcenter.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.uploadbottom.clearFiles() // 此处有变化，重新赋值给imgList
                } catch (err) {}

                this.uploadForm.imgList = [] // 再次清空触发验证
              }, 1500)
              this.dialogVisible = false
              // 查询
              this.queryData()
            }
          },
          //校验图片文件格式
          beforeAvatarUpload(list, form) {
            const currentForm = form == "uploadForm" ? this.uploadForm : this.editForm
            const isExclusive = ["Background Image", "Popup Image", "Popup Thumbnail"].includes(
              currentForm.typeName
            )

            // 对于互斥类型，如果有externalUrl，则不需要图片
            if (isExclusive && currentForm.externalUrl && currentForm.externalUrl.trim() !== "") {
              return true
            }

            // 检测图片格式是否正确
            if (list.length) {
              let allSize = 0
              for (let i = 0; i < list.length; ++i) {
                if (!list[i].size) continue // 没有size属性，不是新增图片
                const arr = list[i].name.split(".")
                const isLt5M = list[i].size / 1024 / 1024 < 5
                allSize += isLt5M
                let testmsg = ["jpeg", "jpg"].includes(arr[arr.length - 1])
                if (!testmsg) {
                  this.$message.error(
                    `${list[i].name} : File suffixes are illegal,only support [jpg]!`
                  )
                  return false
                }
                if (!isLt5M) {
                  this.$message.error(`The image size exceeds the maximum limit : ${list[i].name}`)
                  return false
                }
              }
              if (allSize > 12) {
                this.$message.error(`The image size exceeds the maximum limit!`)
                return false
              }
              return true
            } else {
              // 对于互斥类型，如果没有图片也没有externalUrl，则不允许提交
              if (
                isExclusive &&
                (!currentForm.externalUrl || currentForm.externalUrl.trim() === "")
              ) {
                return false
              }
              return true
            }
          },
          //移除图片逻辑
          handleRemove(file, fileList, item, form) {
            //item存在则为移除的typeName为back类型
            if (item) {
              //点击移除只是移除的临时数组的url,所以要找到所以移除
              const i = this[form].imgList.findIndex(el => el.file && el.file.name == file.name)
              this[form].imgList.splice(i, 1)
              const index = this.tempImgList.findIndex(el => el.file && el.file.name == file.name)
              if (index !== -1) {
                this.tempImgList[index].file = undefined //将移除的图片置空
              }
              $(`.upload${item} .el-upload--picture-card`).show()
              console.log(this[form], "移除后", i)
            } else {
              // 根据当前弹窗状态决定显示哪个上传按钮
              if (this.dialogVisible) {
                $(".upload-demo .el-upload--picture-card").show()
                this.uploadForm.imgList = fileList
              } else if (this.editDialogVisible) {
                $(".uploadEdit .el-upload--picture-card").show()
                this.editForm.imgList = fileList
              }
            }
          },
          //更新取消
          uploadCanvel(formName) {
            this.dialogVisible = false
            this.$refs[formName].resetFields()
          },
          // 上传对话框关闭事件
          uploadCloseDialog(form) {
            // 点击关闭 数据重置
            this.uploadForm.imgList = []
            this.editForm.imgList = []
            this.tempImgList = []
            for (const key in this.templateName) {
              this.templateName[key].forEach(el => {
                this.templateImageList[key][el] = []
              })
            }
            this.$refs[form].resetFields()
            this.uploadForm.rect = {
              width: "30%",
              left: "0%",
              top: "0%"
            }
          },
          // 编辑逻辑--------------------------------------------------
          handleEditor(index, row) {
            console.log(row, "edit Row")

            // 1. 解构数据并计算基础变量
            const {
              id,
              fileName, //图片名称
              typeName, //图片类型
              usable,
              extraPaths,
              storeNumber, //店铺名
              use_date = "",
              use_dow = "",
              use_time = "",
              url = "",
              coordinate,
              externalUrl
            } = row
            console.log(row, "edit Row")
            const defaultRect = { width: "30%", left: "0%", top: "0%" }
            const _template = fileName ? fileName.split(";") : []
            const template = externalUrl ? 1 : Number(extraPaths?.split("/")[0]) || _template.length
            const domain = sessionStorage.getItem("domain")
            const _imgList = externalUrl ? [] : this.onEditShowImage(row, domain)

            // 2. 构建编辑表单
            this.editForm = {
              id,
              domain,
              tagType: extraPaths?.split("/")[0] || "", //标签类型
              fileName, //图片名称
              typeName, //图片类型
              template, //模板类型
              usable, //开关状态
              storeNumber, //店铺名
              imgList: this.allMultipleImages.includes(typeName) ? [] : _imgList, //图片列表
              use_date,
              use_dow,
              use_time,
              url,
              externalUrl: externalUrl || "",
              closeableAfterDelay: row.closeableAfterDelay || 0,
              coordinate:
                typeof coordinate === "string" ? JSON.parse(coordinate) : coordinate || defaultRect
            }

            // 3. 处理特殊图片类型
            this.tempImgList = [] // 清空临时图片列表
            this._handleImageTypeProcessing(typeName, externalUrl, _template, template, _imgList)

            // 4. 显示对话框并处理UI
            // imgList的required规则现在通过computed属性自动响应
            this.editDialogVisible = true
            this.$nextTick(() => this._handleUploadVisibility(typeName, externalUrl))

            console.log(this.editForm, "this.editForm")
          },

          // 处理不同图片类型的逻辑
          _handleImageTypeProcessing(typeName, externalUrl, _template, template, _imgList) {
            if (typeName === "Background Image") {
              this._processBackgroundImage(externalUrl, _template, template, _imgList)
            } else if (typeName === "Image Tag") {
              this._processImageList(this.imageTagName, _imgList, this.imageTagFileList)
            } else if (typeName === "Menu Popup Image") {
              this._processImageList(this.menuPopupImgName, _imgList, this.menuPopupImgList)
            }
          },

          // 处理背景图片
          _processBackgroundImage(externalUrl, _template, template, _imgList) {
            if (externalUrl) {
              this.editForm.imgList = []
              return
            }

            const sortOrder = ["indexBcg", "top", "center", "bottom"]
            _template.forEach(el => {
              this.templateImageList[template][el] = _imgList.filter(i => i.name == el)
              const sort = sortOrder.indexOf(el)
              const file = _imgList.find(i => i.name === el)
              this.tempImgList.push({ sort, file })
            })
            this.editForm.imgList = [...this.tempImgList]
          },

          // 处理图片列表（Image Tag 和 Menu Popup Image 共用）
          _processImageList(nameList, _imgList, targetList) {
            nameList.forEach(el => {
              targetList[el] = _imgList.filter(i => i.name == el)
              const sort = nameList.indexOf(el)
              const file = _imgList.find(i => i.name === el)
              this.tempImgList.push({ sort, file })
            })
            this.editForm.imgList = [...this.tempImgList]
          },

          // 处理上传组件可见性
          _handleUploadVisibility(typeName, externalUrl) {
            if (typeName === "Menu Popup Image") {
              this.tempImgList.forEach(el => {
                const selector = `.upload${el.sort + 1} .el-upload--picture-card`
                $(selector)[el.file ? "hide" : "show"]()
              })
            } else {
              const shouldShow =
                (typeName === "Background Image" || typeName === "Popup Image") && externalUrl
              if (!shouldShow) {
                $(".upload-demo .el-upload--picture-card").hide()
              }
            }
          },

          //当点击编辑按钮显示要编辑的原图片----->此方法为获取原图片的url
          onEditShowImage(row, domain) {
            let {
              fileName, //图片名称
              typeName, //图片类型
              extraPaths, //模板
              storeNumber: storeNum //店铺名
            } = row
            let storeNumber = this.splitStoreNum(storeNum) //根据;截取第一个数据
            let urlArr = []
            let imgName = fileName ? fileName.split(";") : []
            let t = typeName !== "Landing Image" ? ".jpg" : ".jpg"
            let u = this.allMultipleImages.includes(typeName) ? extraPaths : ""
            for (let i = 0; i < imgName.length; i++) {
              let _url =
                "https://appwise.oss-cn-hongkong.aliyuncs.com" +
                "/" +
                domain +
                "/" +
                storeNumber +
                "/image/" +
                typeName +
                `/${u}` +
                imgName[i] +
                t +
                "?" +
                new Date().getTime()
              urlArr.push({ name: imgName[i], url: _url })
            }
            return urlArr
          },
          //编辑表单图片更改
          editUploadChange(file, fileList, item) {
            //item存在则typeName为back,反之
            if (item) {
              $(`.upload${item} .el-upload--picture-card`).hide()
              let sort = ["indexBcg", "top", "center", "bottom"].indexOf(item)
              let i = this.tempImgList.findIndex(el => el.sort == sort)
              if (i !== -1) {
                this.tempImgList.splice(i, 1, { sort, file })
              } else {
                this.tempImgList.push({ sort, file })
              }
              this.editForm.imgList.push(file)
              console.log(this.editForm, "编辑后", i)
            } else {
              $(".uploadEdit .el-upload--picture-card").hide()
              this.editForm.imgList = fileList
            }

            // 处理互斥逻辑
            this.handleEditImgChange()
          },
          //编辑成功回调
          editAvatarSuccess(res, file, fileList) {
            if (res.errMessage1) {
              this.$message.error(res.errMessage1)
            } else {
              this.$message.success("Edit success！")
              this.editDialogVisible = false
              this.$nextTick(() => {
                try {
                  this.$refs.editUpload.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.editUploadindexBcg.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.editUploadtop.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.editUploadcenter.clearFiles() // 此处有变化，重新赋值给imgList
                  this.$refs.editUploadbottom.clearFiles() // 此处有变化，重新赋值给imgList
                  this.editForm.imgList = [] // 再次清空触发验证
                } catch (err) {}
              })
              // 查询
              this.queryData()
            }
          },
          //移除编辑图片
          editRemove(file, fileList, item) {
            //item存在则为移除的typeName为back类型
            if (item) {
              //点击移除只是移除的临时数组的url,所以要找到所以移除
              const i = this.editForm.imgList.findIndex(el => el.name === file.name)
              this.editForm.imgList.splice(i, 1)
              $(`.upload${item} .el-upload--picture-card`).show()
              // console.log(this.editForm,'移除后',i)
            } else {
              $(".el-upload--picture-card").show()
              this.editForm.imgList = fileList
            }
          },
          // Add按钮弹窗事件
          // onaddDialog() {
          //   this.dialogVisible = true;
          //   this.imagOption = true;
          // },
          // 验证店铺landImage重复逻辑
          // rusBlur(e) {
          //   let val = this.uploadForm.storeNumber;
          //   let data = this.tableData;
          //   for (let i = 0; i < data.length; i++) {
          //     if (data[i].storeNumber == val) {
          //       this.imagOption = true;
          //       break;
          //     } else {
          //       this.imagOption = false;
          //     }
          //   }
          // },
          handleDel(index, row) {
            let data = { id: row.id }
            $.post({
              url: "../../manager_photo/deleteOne",
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode != 200) {
                  this.$message.error("Fail to delete!")
                } else {
                  this.queryData()
                  this.$message.success("Successfully delete!")
                }
              },
              error: error => {
                this.$message.error("Fail to delete!")
              }
            })
          },
          // 根据;截取第一个storeNumber
          splitStoreNum(storeNum) {
            return storeNum.split(";")[0]
          },
          // 处理finalFileName
          handleFinalFileName(fd, typeName, form, template, externalUrl, fileName) {
            if (externalUrl) return
            if (form === "editForm" && typeName !== "Menu Popup Image") {
              let finalFileName = this.getFinalFileNameByType(typeName, template, fileName)
              if (finalFileName) {
                fd.append("finalFileName", finalFileName)
              }
            }
          },
          // 根据类型获取finalFileName
          getFinalFileNameByType(typeName, template, fileName) {
            switch (typeName) {
              case "Background Image":
                switch (template) {
                  case 1:
                    return "indexBcg"
                  case 2:
                    return "top;bottom"
                  case 3:
                    return "top;center;bottom"
                  default:
                    return ""
                }
              case "Image Tag":
                return "en;zh;thirdLan"
              default:
                // 其它类型使用fd中对应的fileName
                return fileName || ""
            }
          },
          // 处理uploadForm.typeName变化（通过watch触发）
          handleUploadFormTypeNameChange(e) {
            // 调用主要的处理方法，避免重复代码
            this.handleImageTypeChange(e)
          },
          // 处理uploadForm.template变化
          handleUploadFormTemplateChange(val) {
            this.uploadForm.imgList = []
            $(".el-upload--picture-card").show()

            // 当type为Background Image且Template不为1时，清空External URL
            if (this.uploadForm.typeName === "Background Image" && val !== 1) {
              this.uploadForm.externalUrl = ""
              // 清除externalUrl的校验
              this.$nextTick(() => {
                if (this.$refs.uploadForm) {
                  this.$refs.uploadForm.clearValidate(["externalUrl"])
                }
              })
            }
          },
          // 处理dialogVisible变化
          handleDialogVisibleChange(val) {
            if (!val) {
              this.dialog = ""
              this.uploadForm.typeName = ""
              this.uploadForm.storeNumber = ""
              this.uploadForm.template = 1
              this.uploadForm.imgList = []
              this.uploadForm.externalUrl = ""
              this.tempImgList = []
              this.imageTagFileList = {
                en: [],
                zh: [],
                thirdLan: []
              }
              this.menuPopupImgList = {
                1: [],
                2: [],
                3: [],
                4: [],
                5: []
              }
            } else {
              this.dialog = "uploadForm"
            }
          },
          // 处理editDialogVisible变化
          handleEditDialogVisibleChange(val) {
            if (!val) {
              this.dialog = ""
              this.editForm.typeName = ""
              this.editForm.storeNumber = ""
              this.editForm.template = 1
              this.editForm.imgList = []
              this.editForm.externalUrl = ""
              this.tempImgList = []
              this.imageTagFileList = {
                en: [],
                zh: [],
                thirdLan: []
              }
              this.menuPopupImgList = {
                1: [],
                2: [],
                3: [],
                4: [],
                5: []
              }
            } else {
              this.dialog = "editForm"
            }
          }
        },
        watch: {
          "uploadForm.typeName": {
            deep: true,
            handler(e) {
              this.handleUploadFormTypeNameChange(e)
            }
          },
          "uploadForm.template": {
            deep: true,
            handler(val) {
              this.handleUploadFormTemplateChange(val)
            }
          },
          dialogVisible: {
            handler(val) {
              this.handleDialogVisibleChange(val)
            }
          },
          editDialogVisible: {
            handler(val) {
              this.handleEditDialogVisibleChange(val)
            }
          }
        }
      })
    </script>
  </body>
</html>
