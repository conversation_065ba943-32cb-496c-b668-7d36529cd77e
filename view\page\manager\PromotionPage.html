<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .el-dialog__body {
        padding: 30px 20px 0px;
        color: #606266;
        font-size: 14px;
        word-break: break-all;
        overflow: auto;
      }

      .el-textarea__inner {
        font-family: arial !important;
      }

      .tableColumnSpan {
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        word-break: break-word !important;
      }
      .div-form-item {
        width: 200px;
        /* margin-left: 23%; */
        margin-bottom: 18px;
        word-break: keep-all; /* 保持单词完整 */
        white-space: normal; /* 允许换行 */
      }
      .form-item-title label {
        color: #409eff;
      }
      .cell-item {
        margin-left: 20px;
      }
      .el-form-item__label {
        /* 单词不截断换行 */
        word-break: keep-all; /* 保持单词完整 */
        white-space: normal; /* 允许换行 */
      }
      .el-divider--horizontal {
        height: 3px;
      }

      .el-form-item__label {
        line-height: 20px;
      }
      .ml-20 {
        margin-left: 20px;
      }
      .dividerDom {
        margin-left: -220px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          v-if="tableHeight"
          :data="tableData"
          style="width: 100%"
          :height="tableHeight"
          border
          empty-text="No Data"
          class="tableContent"
        >
          <el-table-column
            :fixed="item.fixed"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            v-for="(item,index) in tableColumn"
            align="center"
          >
            <template slot-scope="scope">
              <el-switch
                v-if="item.prop=='use_able'"
                v-model="scope.row.use_able"
                @change="onEditSwitch($event, scope.row)"
              ></el-switch>
              <template
                v-else-if="['fCodes','inoperative_ftCodes','inoperative_fCodes'].includes(item.prop)"
              >
                <el-tag
                  size="small"
                  :type="item.prop!='fCodes'?'danger':''"
                  v-for="(codeItem,codeIndex) in codeSplit(scope.row[item.prop])"
                  :key="codeIndex"
                >
                  {{codeItem}}
                </el-tag>
              </template>
              <span class="tableColumnSpan" v-else>{{scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
          <!-- 操作栏目 -->
          <el-table-column prop label="Operation" align="center">
            <el-table-column width="110" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible=true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <!-- 除按钮类型外显示编辑按钮 -->
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  slot="reference"
                  @click="onDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 類別Add弹窗 -->
      <template>
        <el-dialog
          title="Add Dialog"
          :visible.sync="addDialogVisible"
          @close="addCloseDialog"
          :lock-scroll="false"
          :append-to-body="true"
        >
          <el-form
            :model="addForm"
            ref="addForm"
            label-width="220px"
            label-position="left"
            :rules="rules"
          >
            <el-form-item
              :label="item.label"
              :prop="item.prop"
              v-for="item in addFormItem"
              :class="getClass(item)"
            >
              <template v-if="item.type == 'input'">
                <el-input v-model="addForm[item.prop]" :placeholder="item.placeholder"></el-input>
              </template>
              <template v-if="item.type == 'textarea'">
                <el-input
                  :class="item.class"
                  type="textarea"
                  v-model="addForm[item.prop]"
                  :placeholder="item.placeholder"
                ></el-input>
              </template>
              <template v-if="item.type == 'switch'">
                <el-switch v-model="addForm[item.prop]"></el-switch>
              </template>
              <template v-if="item.type == 'inputNumber'">
                <el-input-number
                  style="width: 100%"
                  :min="item.min"
                  v-model="addForm[item.prop]"
                  :placeholder="item.placeholder"
                ></el-input-number>
              </template>
              <template v-if="item.type == 'divider'">
                <el-divider></el-divider>
              </template>
            </el-form-item>
          </el-form>
          <div slot="footer">
            <el-button @click="addDialogVisible = false">Cancel</el-button>
            <el-button type="primary" @click="handleConfirm('addForm')">Add</el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 单元格编辑弹窗 -->
      <template>
        <el-dialog title="Edit Dialog" :visible.sync="editDialogVisible">
          <el-form
            :model="editForm"
            ref="editForm"
            label-width="220px"
            label-position="left"
            :rules="rules"
          >
            <el-form-item
              :label="item.label"
              :prop="item.prop"
              v-for="item in addFormItem"
              :class="getClass(item)"
            >
              <template v-if="item.type == 'input'">
                <el-input v-model="editForm[item.prop]" :placeholder="item.placeholder"></el-input>
              </template>
              <template v-if="item.type == 'textarea'">
                <el-input
                  :class="item.class"
                  type="textarea"
                  v-model="editForm[item.prop]"
                  :placeholder="item.placeholder"
                ></el-input>
              </template>
              <template v-if="item.type == 'switch'">
                <el-switch v-model="editForm[item.prop]"></el-switch>
              </template>
              <template v-if="item.type == 'inputNumber'">
                <el-input-number
                  style="width: 100%"
                  :min="item.min"
                  v-model="editForm[item.prop]"
                  :placeholder="item.placeholder"
                ></el-input-number>
              </template>
              <template v-if="item.type == 'divider'">
                <el-divider></el-divider>
              </template>
            </el-form-item>
          </el-form>
          <div slot="footer">
            <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
            <el-button style="margin-right: 8px" @click="handleConfirm('editForm')">
              Submit
            </el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 单元格删除弹窗 -->
    </div>
    <script>
      var app = new Vue({
        el: "#app",
        data: {
          tableData: [],
          tableHeight: 0,
          domain: sessionStorage.getItem("domain"),
          addDialogVisible: false,
          editDialogVisible: false,
          tableColumn: [
            {
              label: "Store Number",
              prop: "storeNumber",
              width: "130",
              fixed: false
            },
            {
              label: "Promotion Name",
              prop: "desc",
              width: "200",
              fixed: false
            },

            {
              label: "FCodes",
              prop: "fCodes",
              width: "",
              fixed: false
            },
            {
              label: "Minumum purchase",
              prop: "amount",
              width: "100",
              fixed: false
            },
            {
              label: "Date",
              prop: "use_date",
              width: "",
              fixed: false
            },
            {
              label: "Time",
              prop: "use_time",
              width: "",
              fixed: false
            },
            {
              label: "Dow",
              prop: "use_dow",
              width: "100",
              fixed: false
            },

            {
              label: "POS Food Groups",
              prop: "inoperative_ftCodes",
              width: "180",
              fixed: false
            },
            {
              label: "POS Food Codes",
              prop: "inoperative_fCodes",
              width: "180",
              fixed: false
            },
            {
              label: "Activate Promotion",
              prop: "use_able",
              width: "100",
              fixed: false
            }
          ],
          addFormItem: [
            {
              label: "Store Number",
              prop: "storeNumber",
              type: "input",
              placeholder: "Please enter the store number"
            },
            {
              label: "Promotion Name",
              prop: "desc",
              type: "textarea",
              placeholder: "Please enter the description"
            },
            {
              label: "Promotion Rules",
              type: "div"
            },
            {
              label: "1. Minumum purchase",
              prop: "amount",
              type: "inputNumber",
              min: 0,
              placeholder: "Minimum purchase amount to qualify for this promotion"
            },
            {
              label: "Exclude following from minimum purchase calculation",
              type: "div"
            },
            {
              label: "POS Food Groups",
              prop: "inoperative_ftCodes",
              type: "textarea",
              placeholder: "Enter the FoodType code (Use ; to separate multiple codes)"
            },
            {
              label: "POS Food Codes",
              prop: "inoperative_fCodes",
              type: "textarea",
              placeholder: "Enter the FoodList code (Use ; to separate multiple codes)"
            },
            {
              label: "2. Promotion availability",
              type: "div"
            },
            {
              label: "Date",
              prop: "use_date",
              type: "input",
              placeholder: "Format : yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
            },
            {
              label: "Time",
              prop: "use_time",
              type: "input",
              placeholder: "Format : hh:mm-hh:mm  (Use ; to separate multiple times)"
            },
            {
              label: "Dow",
              prop: "use_dow",
              type: "input",
              placeholder: "Format : 1234567H  (1=Monday,7=Sunday,H=holidays)"
            },

            {
              label: "3. Auto add this item(s) when above conditions are met",
              prop: "fCodes",
              type: "textarea",
              class: "ml-20",
              placeholder: "Enter POS food codes (use ; to separate multiple items)"
            },
            {
              type: "divider"
            },
            {
              label: "Activate Promotion",
              prop: "use_able",
              type: "switch"
            }
          ],
          rules: {
            storeNumber: [
              {
                required: true,
                message: "Please enter the Store Number",
                trigger: "blur"
              }
            ],
            amount: [
              {
                required: true,
                message: "Please enter amount",
                trigger: "change"
              }
            ],
            fCodes: [
              {
                required: true,
                message: "Please enter FCodes",
                trigger: "change"
              }
            ]
          },
          addForm: {
            storeNumber: "",
            desc: "",
            use_dow: "",
            use_date: "",
            use_time: "",
            use_able: true,
            amount: "",
            inoperative_ftCodes: "",
            inoperative_fCodes: "",
            fCodes: ""
          },
          editForm: {
            id: "",
            domain: "",
            storeNumber: "",
            desc: "",
            use_able: "",
            use_dow: "",
            use_date: "",
            use_time: "",
            amount: "",
            inoperative_ftCodes: "",
            inoperative_fCodes: "",
            fCodes: ""
          },
          addFormRequest: {
            url: "../../manager_promotionOffer/addOne ",
            errorMsg: "Fail to add!",
            successMsg: "Successfully added!",
            visibleName: "addDialogVisible"
          },
          editFormRequest: {
            url: "../../manager_promotionOffer/updateOne",
            successMsg: "Successfully Edit!",
            errorMsg: "Edit failure!",
            visibleName: "editDialogVisible"
          },
          delFormRequest: {
            url: "../../manager_promotionOffer/deleteOne",
            successMsg: "Successfully delete!",
            errorMsg: "Fail to delete!"
          }
        },
        mounted() {
          // 页面加载的时候设置table的高度
          this.tableHeight = this.getTableHeight(this, 0, null)
        },
        created() {
          this.getData()
        },
        computed: {
          getClass() {
            return item => {
              let classes = [] // 获取 item 自带的类名
              let arr = ["storeNumber", "desc", "amount", "fCodes", "use_able"]
              if (item.type !== "div" && !arr.includes(item.prop) && item.type !== "divider") {
                classes.push("cell-item")
              }
              if (item.type === "divider") {
                classes.push("dividerDom") // 添加 margin 的类名
              }
              if (item.label === "Promotion Rules") {
                classes.push("form-item-title") // 添加 margin 的类名
              }
              return classes.join(" ")
            }
          }
        },
        methods: {
          getData() {
            $.get({
              url: "../../manager_promotionOffer/getAll",
              data: { domain: this.domain },
              dataType: "json",
              success: res => {
                if (res.statusCode !== 200) {
                  this.$message.error("Query data error!")
                } else {
                  console.log(res, "数据")
                  this.tableData = res.data.sort((a, b) => {
                    return a["storeNumber"].localeCompare(b["storeNumber"])
                  })
                }
              },
              error: error => {
                this.$message.error("Query data error!")
              }
            })
          },

          onEdit(index, row) {
            console.log(row)
            // let { id, domain, storeNumber, use_able, use_dow, use_date, use_time, amount, inoperative_ftCodes, inoperative_fCodes, fCodes } = row
            this.editForm = { ...row }
            // this.oldEditForm = {
            //   oldNumber: row.storeNumber,
            //   oldtype: row.type
            // }
            this.editDialogVisible = true
          },

          handleConfirm(typeForm) {
            let data = {
              ...this[typeForm],
              domain: this.domain
            }
            this.$refs[typeForm].validate(valid => {
              if (valid) {
                console.log(JSON.parse(JSON.stringify(this[typeForm])), "提交")
                let { url, successMsg, errorMsg, visibleName } = this[typeForm + "Request"]
                this.requestPostFun(url, data, successMsg, errorMsg)
                this[visibleName] = false
              } else {
                this.$message.error(errorMsg)
              }
            })
          },
          onDel(index, row) {
            let { url, successMsg, errorMsg } = this.delFormRequest
            console.log(JSON.parse(JSON.stringify(row)), "删除row")
            let { id, storeNumber, domain } = row
            this.requestPostFun(url, { id, storeNumber, domain }, successMsg, errorMsg)
          },
          // 开关触发
          onEditSwitch($event, row) {
            this.editForm = { ...row }
            let { url, errorMsg, successMsg } = this.editFormRequest
            console.log(JSON.parse(JSON.stringify(this.editForm)), "开关")
            this.requestPostFun(url, this.editForm, successMsg, errorMsg)
          },
          // 上传对话框关闭事件
          addCloseDialog() {
            // 点击关闭 数据重置
            this.$refs["addForm"].resetFields()
          },
          requestPostFun(url, parm, successMsg, errorMsg) {
            $.post({
              url: url,
              data: parm,
              dataType: "json",
              success: res => {
                if (res.statusCode != 200) {
                  this.$message.error(errorMsg)
                } else {
                  this.getData()
                  this.$message.success(successMsg)
                }
              },
              error: error => {
                this.$message.error(errorMsg)
              }
            })
          },
          codeSplit(val) {
            // console.log(val.split(","));
            return val.split(",").filter(item => item != "")
          },
          unique(storeNumber, type) {
            let tableData = this.tableData
            let res = false
            if (tableData.length != 0) {
              for (let i = 0; i < tableData.length; i++) {
                const item = tableData[i]
                if (item.storeNumber == storeNumber && item.type == type) {
                  this.$message.error("Do not duplicate configuration!")
                  res = true
                  break
                }
              }
            }
            return res
          },
          editUnique(newNumber, Newtype) {
            let tableData = this.tableData
            let { oldNumber, oldtype } = this.oldEditForm
            let res = false
            if (tableData.length != 0) {
              if (newNumber != oldNumber || Newtype != oldtype) {
                for (let i = 0; i < tableData.length; i++) {
                  const item = tableData[i]
                  if (item.storeNumber == newNumber && item.type == Newtype) {
                    this.$message.error("Do not duplicate configuration!")
                    res = true
                    break
                  }
                }
              }
            }
            return res
          },
          getTableHeight(that, val = 32, name) {
            let fixHeight = 16 // padding为8,8
            let searchFormHeight = name ? this.$refs[name].clientHeight : 0 // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - 40) // 计算完之后剩余table可用的高度
            // console.log(pageHeight, searchFormHeight, tableHeight, "searchFormHeight")
            return tableHeight
          }
        }
      })
    </script>
  </body>
</html>
