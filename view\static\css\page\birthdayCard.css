.birthdayCard {
  background-color: white;
  min-height: 10rem;
  max-height: 12rem;
  border: 0.2rem solid #72705bca;
}
.birthdayCard .layui-layer-content {
  width: 100%;
  background: #f3f2ee;
}
.birthdayCard-warp {
  height: 100%;
  /* background: #f3f2ee; */
}
.birthdayCard-warp-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}
.birthdayCard-warp-txt {
  padding: 0 0.5rem;
}
.zoom-left-in-out {
  animation-name: zoom-left-in-out;
  animation-duration: 1.75s;
  animation-iteration-count: infinite;
  transform-origin: left bottom;
}
@keyframes zoom-left-in-out {
  0%,
  100% {
    transform: scale(0.95) rotate(2deg);
  }

  50% {
    transform: scale(1) rotate(-2deg);
  }
}
.balloonFloat {
  animation-name: balloonFloat;
  animation-duration: 1.9s;
  animation-iteration-count: infinite;
}
@keyframes balloonFloat {
  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(10px);
  }
}

.cakeSvg {
  width: 3.5rem;
  margin: 2rem 0;
}

.bday-decor {
  font-size: 1.6rem;
}
.bday-decor--top-left {
  position: absolute;
  top: 0.1rem;
  left: 0.1rem;
}
.bday-decor--bottom-right {
  position: absolute;
  bottom: 0.1rem;
  right: 0.1rem;
}

.text-fade-in {
  animation: textFadeIn 2.5s ease-in forwards;
}

@keyframes textFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.birthdayCard .layui-layer-btn {
  background: #f3f2ee;
}
