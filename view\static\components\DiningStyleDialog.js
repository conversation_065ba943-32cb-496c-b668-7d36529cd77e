const DiningStyleDialog = {
  props: {
    status: {
      type: Object,
      default: () => {
        return { dineIn: false, takeaway: false }
      }
    }
  },
  template: `
     <div class="modal dining-style-modal animate__animated animate__fadeIn">
      <article class="modal-container">
        <header class="modal-container-header" v-if="false">
          <h2 class="modal-container-title">
            {{$parent.arrLang.diningStyleDiaTitle}}
          </h2>
          <button class="icon-button" v-if="false">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
              <path fill="none" d="M0 0h24v24H0z" />
              <path
                fill="currentColor"
                d="M12 10.586l4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.414 1.414-4.95-4.95-4.95 4.95-1.414-1.414 4.95-4.95-4.95-4.95L7.05 5.636z"
              />
            </svg>
          </button>
        </header>
        <section class="modal-container-body dining-style">
          <form ref="form">
            <label @click="onAccept($parent.tableNumber)" :class="{disabled:!status.dineIn}">
              <input type="hidden" name="dining" :value="$parent.tableNumber"/>
              <div class="radio-wrap">
                <img src="./static/img/svg/store.svg" alt="store_icon">
                <p>
                  {{$parent.arrLang.dineIn}}
                </p>
              </div>
            </label>
            
            <div class="dividing-line"></div>
            <label @click="onAccept('TAKEAWAY')" :class="{disabled:!status.takeaway}">
              <input type="hidden" name="dining" value="TAKEAWAY" />
              <div class="radio-wrap">
                <img src="./static/img/svg/takeaway.svg" alt="takeaway_icon">
                <p>
                  {{$parent.arrLang.takeaway}}
                </p>
              </div>
            </label>
          </form>
        </section>
        <footer class="modal-container-footer" v-if="false">
<!--           <button class="button is-ghost">Decline</button> -->
          <button class="button is-primary" @click="onAccept">{{$parent.arrLang.staffModeSecondBtn}}</button>
        </footer>
      </article>
    </div>
  `,

  methods: {
    onAccept(value) {
      this.$emit("check-dining", value)
    }
  }
}
