* {
  margin: 0;
  padding: 0;
}

header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 10vh;
  width: 100vw;
  background: linear-gradient(#1091c0, #005b7f);
  padding: 0 2rem;
  box-sizing: border-box;
}
header div {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
header div section {
  display: flex;
  justify-content: center;
}

.contentBox {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  /* background: url('../img/background/defaultLogin.jpg') no-repeat center/cover;
  background-size: 100% 100%; */
}
.leftTitle {
  position: absolute;
  color: white;
}
.leftTitle section {
  flex-direction: column;
}
.version{
    font-size: 0.9rem;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}
.cms_version{
  margin-right: 0.5rem;
}
.self_order {
  font-size: 1.1rem;
}
.centerTitle {
  flex: 1;
  color: white;
  font-size: 1.3rem;
}

/* 
.loginImg {
  position: relative;
  height: 100%;
  background-image: url("../img/background/defaultLogin.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
} */

.right_content {
  width: 100vw;
  height: 90vh;
}

.loginContent {
  position: relative;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  padding: 0rem 1rem;
}

.loginContent form {
  width: 420px;
  height: 290px;
}

h3 {
  font-size: 35px;
  font-weight: 500;
  color: #000000;
  font-family: "Roboto", sans-serif;
  margin-bottom: 40px;
}

p {
  color: #333333;
  font-size: 14px;
  margin-bottom: 15px;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
  font-size: 14px;
}

.input-group {
  margin-bottom: 1.7rem;
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}
@media screen and (max-width: 800px) {
  .leftTitle {
    display: none;
  }
}
.input-group input {
  display: block;
  width: 100%;
  /* height: calc(2.25rem + 2px);
  padding: 0.375rem 0.75rem; */
  height: 0;
  padding: 1.625rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-clip: content-box;
  background-color: #fff;
  /* background-clip: padding-box; */
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  text-indent: 2.5rem;
}
.noneInput {
  height: 0;
  opacity: 0;
  padding: 0;
  border: none;
}
.logoBox {
  position: absolute;
  top: 0.9rem;
  left: 1rem;
}
.input-group input:focus {
  color: #495057;
  background-color: #fff !important;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.mb-2,
.my-2 {
  margin-bottom: 2rem;
}

.submitBtn {
  font-family: Roboto, sans-serif;
  font-size: 1rem;
  outline: none;
  padding: 0.625rem 1rem;
  min-width: 120px;
  color: #fff;
  background-color: #ff0018;
  border-color: #ff0018;
  cursor: pointer;
  width: 100% !important;
  border-radius: 4px;
  border: 1px solid transparent;
}

.submitBtn:hover {
  background-color: #bc2634;
  border-color: #bc2634;
  color: #fff;
}

/* .invalid-feedback {
  display: none;
  color: #db3c36;
  bottom: calc(100% + 10px);
  right: 0;
  text-align: right;
  position: absolute;
} */
