Vue.component("OrderStopDia", {
  // props: ["systemLanguage", "openTable", "queueTime"],
  props: {
    systemLanguage: {
      type: Object,
      required: true
    },
    storeNumber: {
      type: String,
      default: sessionStorage.getItem("storeNumber")
    },
    domain: {
      type: String,
      default: sessionStorage.getItem("domain")
    },
    // openTable: {
    //   type: Object,
    //   required: true
    // },
    queueTime: {
      type: Number,
      required: true
    },
    currentPage: {
      type: String,
      default: "menu" // 设置默认值为 'menu'
    }
  },
  //   <div @click='close'>
  //   关闭
  // </div>
  template: `
  <div class="queue-app">
      <div class="queue-header">
      <img :src="imgUrl" class="queue-header-img" alt="" />
    </div>
    <div class="queue-content">
      <div class="queue-content-tip">
        <div class="queue-content-title">{{systemLanguage.orderStopTitle}}</div>
        <div class="queue-content-text">
        {{systemLanguage.orderStopTip}}
        </div>
      </div>
      <div class="queue-content-progressBar">
        <div class="queue-content-progress" :style="{ width: progress + '%' }"></div>
      </div>

      <div class="queue-content-waitingTime">
        <span>{{systemLanguage.orderStopWaitTimeTip}}</span>
        <span>
          <span>{{queueTime}}</span>
          {{systemLanguage.minutesMsg}}
        </span>
      </div>
      <div class="queue-content-lastUpdated">
        <span>{{lastUpdatedMsg}}</span>
      </div>
    </div>
  </div>

  `,
  data() {
    return {
      progress: 0,
      interval: null,
      // lastUpdated: moment().format("HH:mm:ss"),
      imgUrl: "",
      layerIndex: null,
      animationFrameId: null
    }
  },
  computed: {
    lastUpdatedMsg() {
      let { lastUpdatedTime } = app.queuedObj
      return this.systemLanguage.orderStopLastUpdatedTip.replace("#time", lastUpdatedTime)
    }
  },
  created() {
    this.getImgUrl()
    // this.startProgress()
  },
  mounted() {
    this.startProgress()
    //获取父组件的baseUrl
    // console.log(this.$parent.baseUrl, 5678)
  },
  beforeDestroy() {
    // 组件销毁前停止进度
    this.stopProgress()
  },
  methods: {
    // startProgress() {
    //   this.progress = 0
    //   let salesControl =
    //     this.currentPage == "index" ? app.initConfigObj.salesControl : app.openTable.salesControl
    //   let { refreshTime } = salesControl
    //   let minutes = refreshTime || 1 // 估计等待时间
    //   let intervalTime = (minutes * 60 * 1000) / 100 //  转换为毫秒数

    //   this.interval = setInterval(() => {
    //     if (this.progress < 100) {
    //       this.progress += 1
    //     } else {
    //       clearInterval(this.interval)
    //       this.handleProgressComplete()
    //     }
    //   }, intervalTime) // Adjust the speed of the progress bar here
    // },

    startProgress() {
      this.progress = 0
      let salesControl =
        this.currentPage == "index" ? app.initConfigObj.salesControl : app.openTable.salesControl
      let { refreshTime } = salesControl
      let minutes = refreshTime || 1 // 估计等待时间
      let totalTime = minutes * 60 * 1000 // 总毫秒数
      let startTime = moment() // 使用 moment 记录开始时间

      const updateProgress = () => {
        let currentTime = moment()
        let elapsedTime = currentTime.diff(startTime) // 计算已经过去的时间，单位为毫秒
        this.progress = Math.min(100, (elapsedTime / totalTime) * 100) // 更新进度

        if (this.progress < 100) {
          this.animationFrameId = requestAnimationFrame(updateProgress)
        } else {
          this.handleProgressComplete()
        }
      }

      this.animationFrameId = requestAnimationFrame(updateProgress) // 启动动画帧
    },

    stopProgress() {
      if (this.animationFrameId !== null) {
        cancelAnimationFrame(this.animationFrameId)
        this.animationFrameId = null // 重置为 null
      }
    },

    handleProgressComplete() {
      checkOrderWait(this.domain, this.storeNumber, this.currentPage).then(res => {
        if (app.queuedObj.isOrderStop) {
          this.startProgress()
        } else {
          layer.close(this.layerIndex)
          this.$nextTick(() => {
            $(".orderStopDia.layer-anim-close").remove()
          })
        }
      })
    },

    getImgUrl() {
      let data = {
        domain: this.domain,
        storeNumber: this.storeNumber,
        typeNameList: ["Queue Waiting photo"]
      }

      //获取图片地址
      $.post({
        url: (this.currentPage == "index" ? "./" : "../") + "photoConfig/getSpecifiedPhotoConfig",
        dataType: "json",
        traditional: true,
        async: false,
        data,
        success: async res => {
          let imgList = res.photoConfigList
          if (imgList.length == 0) return
          let windowWidth = document.documentElement.clientWidth
          let baseUrl = ""
          console.log("🚀 ~ getImgUrl ~ res:", res)
          if (this.currentPage == "index") {
            let { defaultOss, backupOss } = this.$parent
            console.log(defaultOss, backupOss, "defaultOss, backupOss")
            let imgUrl = this.$parent.imagePath("nNT", imgList[0])
            await checkImage(imgUrl, [defaultOss, backupOss]).then(({ url }) => {
              baseUrl = encodeURI(url)
            })
          } else {
            let { typeName, fileName, storeNumber, photoSuffix = "png" } = imgList[0]
            let newBaseUrl = this.$parent.baseUrl.replace("/image/", `/${storeNumber}/image/`)
            baseUrl = `${newBaseUrl}${typeName}/${fileName}.${photoSuffix}`
          }
          this.imgUrl = `${baseUrl}?x-oss-process=image/resize,w_${windowWidth}`
        }
      })
    },
    close() {
      // layer.closeAll()
      layer.close(this.layerIndex)
    }
  }
})
