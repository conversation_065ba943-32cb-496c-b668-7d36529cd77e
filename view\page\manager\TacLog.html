<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/moment/moment.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>
    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>
    <style>
      #app {
        padding: 8px;
      }
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <el-backtop v-if="tableHeight" target=".el-table__body-wrapper" :visibility-height="10">
        UP
      </el-backtop>
      <template>
        <div class="header-box" ref="headerBox">
          <el-form
            ref="searchForm"
            :model="searchForm"
            :inline="true"
            class="searchForm"
            size="small"
          >
            <el-row :gutter="0" class="form-row">
              <el-col :span="colSpan" v-for="(item, index) in searchItems" :key="index">
                <el-form-item :label="item.label" :prop="item.prop" label-width="auto">
                  <el-input
                    clearable
                    v-model="searchForm[item.prop]"
                    :placeholder="item.placeholder"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="colSpan" v-for="(item, index) in dateItems" :key="index">
                <el-form-item :label="item.label" :prop="item.prop" label-width="auto">
                  <el-date-picker
                    v-model="searchForm[item.prop]"
                    :type="item.type"
                    :placeholder="item.placeholder"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="colSpan" v-for="(item, index) in selectItems" :key="index">
                <el-form-item :label="item.label" :prop="item.prop" label-width="auto">
                  <el-select
                    v-model="searchForm[item.prop]"
                    :placeholder="item.placeholder"
                    clearable
                  >
                    <el-option
                      :label="option.label"
                      :value="option.value"
                      v-for="(option, index) in item.options"
                      :key="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="colSpan" class="form-btn">
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="getSearch">
                    Search
                  </el-button>
                  <el-button @click="resetForm('searchForm')">Reset</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </template>
      <!-- 表格数据 -->
      <template>
        <el-table
          v-if="tableHeight"
          :data="tableData"
          stripe
          style="width: 100%"
          :header-cell-style="{fontSize: '15px'}"
          :max-height="tableHeight"
          empty-text="No Data"
          border
          v-loading="tableLoading"
        >
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :label="item.label"
            :prop="item.prop"
            :fixed="item.fixed"
            :width="item.width"
          >
            <template slot-scope="scope">
              <el-tag v-if="item.prop=='changeTo'" :type="scope.row.changeTo?'success':'danger'">
                {{scope.row.changeTo|changeToFilter}}
              </el-tag>
              <span v-else-if="item.prop=='createDatetimeUTC'">
                {{scope.row[item.prop]|momentUTC }}
              </span>
              <span v-else>{{scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </div>
    <script>
      var app = new Vue({
        el: "#app",
        data: {
          tableHeight: 0,
          domain: sessionStorage.getItem("domain"),
          tableData: [],
          tableLoading: false,
          searchForm: {
            storeNumber: "",
            application: "",
            startCreateDatetimeUTC: "",
            endCreateDatetimeUTC: "",
            changeTo: ""
          },
          tableColumn: [
            {
              label: "Store Number",
              prop: "storeNumber",
              fixed: true
            },
            {
              label: "Application",
              prop: "application"
            },
            {
              label: "Date Time",
              prop: "createDatetimeUTC",
              width: ""
            },
            {
              label: "Change To",
              prop: "changeTo"
            }
          ],
          searchItems: [
            {
              label: "Store Number",
              prop: "storeNumber",
              placeholder: "Please enter store number"
            }
          ],
          selectItems: [
            {
              label: "Application",
              prop: "application",
              placeholder: "Please select an application",
              options: [
                { label: "CMS", value: "CMS" },
                { label: "EXE ", value: "EXE" },
                { label: "WEB", value: "WEB" }
              ]
            },
            {
              label: "Change To",
              prop: "changeTo",
              placeholder: "Please select a status",
              options: [
                { label: "ON", value: "ON" },
                { label: "OFF ", value: "OFF" }
              ]
            }
          ],
          dateItems: [
            {
              label: "Start Date",
              prop: "startCreateDatetimeUTC",
              placeholder: "Select date and time",
              type: "datetime"
            },
            {
              label: "End Date",
              prop: "endCreateDatetimeUTC",
              placeholder: "Select date and time",
              type: "datetime"
            }
          ]
        },
        filters: {
          changeToFilter(value) {
            return value ? "ON" : "OFF"
          },
          momentUTC: (value, formatString) => {
            let { value: TimeZone } = JSON.parse(sessionStorage.getItem("localTimeZone"))
            formatString = formatString || "YYYY-MM-DD HH:mm:ss"
            if (value) {
              // 将时间格式数据转化为moment对象
              const date = moment(value)
              // 将时区数据格式转化为分钟数
              const timeZoneOffset = moment.duration(TimeZone).asMinutes()
              // 将时间转化为对应时区的时间
              const dateInTimeZone = date.add(timeZoneOffset, "minutes")
              // 将时间转化为对应格式
              return moment(dateInTimeZone).format(formatString)
            }
          }
        },
        created() {},
        mounted() {
          let { startTime, endTime } = this.getDayTime()
          // 默认显示当天时间数据
          this.searchForm.startCreateDatetimeUTC = startTime
          this.searchForm.endCreateDatetimeUTC = endTime
          console.log(this.searchForm, "this.searchForm")
          this.getSearch()
          // 页面加载的时候设置table的高度
          this.tableHeight = this.getTableHeight(0, "headerBox")
          // 页面大小该变的时候（缩放页面）设置table的高度（可加可不加）
          window.onresize = () => {
            this.tableHeight = this.getTableHeight(0, "headerBox")
          }
        },
        computed: {
          colSpan() {
            // return this.$screen.lg ? 6 : 4
            //获取当前屏幕的宽度
            let width = document.body.clientWidth
            //大于1400显示4列,小于1400显示3列
            return width > 1400 ? 6 : 8
          }
        },
        methods: {
          formatDateTime(form) {
            let { startCreateDatetimeUTC: start, endCreateDatetimeUTC: end } = form
            let { value } = JSON.parse(sessionStorage.getItem("localTimeZone"))
            let formatString = "YYYY-MM-DD HH:mm:ss"

            if (start) {
              let startCreateDatetimeUTC = moment(start)
                .subtract(moment.duration(value))
                .format(formatString)
              form = {
                ...form,
                startCreateDatetimeUTC
              }
            }
            if (end) {
              let endCreateDatetimeUTC = moment(end)
                .subtract(moment.duration(value))
                .format(formatString)
              form = {
                ...form,
                endCreateDatetimeUTC
              }
            }
            return form
          },
          getSearch() {
            let cloneForm = {
              ...this.searchForm,
              startCreateDatetimeUTC: this.searchForm.startCreateDatetimeUTC,
              endCreateDatetimeUTC: this.searchForm.endCreateDatetimeUTC
            }
            this.getData(cloneForm)
          },
          getData(searchForm = {}) {
            this.tableLoading = true
            let filterPam = this.fillerVal(searchForm)
            filterPam = this.formatDateTime(filterPam)
            $.get({
              url: "../../manager_tacLog/getAll",
              dataType: "json",
              data: filterPam,
              success: res => {
                if (res.statusCode == 200) {
                  console.log(res, "初始请求数据")
                  this.tableData = res.data
                } else {
                  this.$message.error("Request failed, please try again")
                }
              },
              error: error => {
                this.$message.error("Request failed, please try again")
              },
              complete: () => {
                this.tableLoading = false
              }
            })
          },
          // 获取当天时间范围
          getDayTime() {
            let startTime = moment().startOf("day").format("YYYY-MM-DD HH:mm:ss") // 当天0点的时间格式
            let endTime = moment().endOf("day").format("YYYY-MM-DD HH:mm:ss") // 当天23点59分59秒的时间格式

            return { startTime, endTime }
          },
          // 过滤假值
          fillerVal(tetsObj) {
            if (!tetsObj) return {}
            let filterPam = {}
            for (let i in tetsObj) {
              if (tetsObj[i]) {
                filterPam[i] = tetsObj[i]
              }
            }
            return filterPam
          },
          resetForm(formName) {
            this.$refs[formName].resetFields()
            // console.log(this.$refs[formName], 'this.$refs[formName]')
          },

          getTableHeight(val = 32, name) {
            // 头部导航固定65px
            let fixHeight = 65
            let searchFormHeight = this.$refs[name].clientHeight // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - fixHeight) // 计算完之后剩余table可用的高度

            return tableHeight
          }
        }
      })
    </script>
  </body>
</html>
