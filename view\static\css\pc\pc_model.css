:root {
  --pcWidth: 360px;
  --pcHeight: 740px;
}
html {
  background-color: #202124;
  width: 100%;
  height: 100vh;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
/* 隐藏滚动条 */
html::-webkit-scrollbar {
  display: none;
}
.pc {
  max-width: var(--pcWidth);
  max-height: var(--pcHeight);
  width: var(--pcWidth);
  height: var(--pcHeight);
  overflow: hidden;
  margin: auto auto;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  background-color: #f4f4f4;
}
.pc .layui-layer {
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  margin: auto !important;
  height: fit-content;
}
.pc .layui-layer-msg {
  width: fit-content;
}
.pc .layui-layer-content {
  margin: auto;
}
.pc .layui-layer-dialog:not(.defaultLayer) {
  width: fit-content;
}
/*index.html*/
/*map.html*/
.pc_map #app {
  width: 100%;
  height: 100%;
}
.pc_map #top-tip-loading {
  position: absolute;
}
.pc_map .store-t {
  position: absolute;
  overflow: hidden;
}
.pc_map .store-list-c {
  /*height: 370px !important;*/
}
.pc_map .no-store-list {
  font-size: 16px;
}
.pc_map #deliveryTimePicker {
  position: absolute;
  overflow: hidden;
}
.pc_map #deliveryTimePicker .picker-box {
  position: absolute;
}
/*menuPage.html*/
.pc_menuPage .tab_warp {
  overflow-x: scroll;
}
.pc_menuPage .dialogBox {
  width: fit-content;
}
.pc_menuPage .cart_warp {
  position: absolute;
}
.pc_menuPage .ball {
  position: absolute;
}
.pc_menuPage .viewer-canvas > img {
  margin-left: auto !important; /* 重置margin-left,原本依赖上已经存在margin-right:auto ===>margin: 高度 auto*/
}
/*payOrderPage.html*/
.pc_payOrderPage #app {
  position: absolute;
}
.pc_payOrderPage .v-application--wrap {
  min-height: unset !important;
}
/*paySuccessPage.html*/
.pc_paySuccessPage #app {
  width: 100%;
}
.pc_paySuccessPage #app .successPayBox {
  position: absolute;
}
/*payFailurePage.html*/
.pc_payFailurePage #app {
  width: 100%;
}
.pc_payFailurePage #app .errorPayBox {
  position: absolute;
}

.pc .content_warp_zero .foodTex_box .layout1 {
  width: calc(var(--pcWidth) - 50px);
  height: 210px;
}
.pc .content_warp_zero .soldOut_warp {
  position: absolute;
  left: 50%;
  top: 0;
  width: calc(var(--pcWidth) - 50px);

  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(140, 143, 152, 0.5);
  border-radius: 13px;
}
.pc .content_warp_zero .soldOut_warp .soldOutLogo {
  width: 80%;
  flex-shrink: 0;
}
.pc .content_warp_zero .disableBox {
  position: absolute;
  left: 50%;
  top: 0;
  width: calc(var(--pcWidth) - 50px);
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  z-index: 2;
  /* border-radius: 13px; */
}

.pc .food_txtAndPrice_zero {
  /* margin-right: auto;
  margin-left: 0.5rem; */
  margin: 0.05rem auto 0.05rem auto;
  width: calc(var(--pcWidth) - 50px);
}
.pc .addBtnZero {
  width: 1rem;
  height: 1rem;
}
.pc .content_warp_two .food_img {
  height: calc((var(--pcWidth) * 0.47) * (2 / 3));
}

.pc .content_warp_six .food_img,
.pc .content_warp_third .food_img {
  height: calc(var(--pcWidth) * 0.3 * (2 / 3));
}
.pc .content_warp_one .soldOut_warp {
  height: 1.6rem;
  bottom: unset;
}
/* .pc .content_warp_two .soldOutLogo {
  width: 100%;
}
.pc .content_warp_six .soldOutLogo,
.pc .content_warp_third .soldOutLogo {
  width: 2.5rem;
} */
.pc .content_warp_two .addFoodBtn {
  top: calc(
    (var(--pcWidth) * 0.47 * (2 / 3)) / 1.5
  ); /* var(--pcWidth) * 0.47 * (2 / 3)动态计算当前元素高度 */
}
.pc .content_warp_six .addFoodBtn,
.pc .content_warp_third .addFoodBtn {
  top: calc((var(--pcWidth) * 0.3 * (2 / 3)) / 1.5);
  right: 0.1rem;
}
.pc .xiNumDiaLayer {
  width: 7.5rem;
}
.pc_menuPage .v-menu__content {
  left: 20px !important;
}
.pc .v-menu__content--fixed {
  position: absolute;
}
.pc .verify-repeated-layer {
  width: 72%;
}
.pc .verify-repeated-layer .layui-layer-content {
  text-align: center;
}
.pc .table_info {
  font-size: 16px;
}
.pc .table_info_leftIcon .navHeaderIcon {
  width: 20px;
  padding-right: 9px;
}
.pc .header-tableIcon {
  width: 20px;
}
.pc .header-tableNumber {
  padding: 0px 3px;
}
/* 会员中心 */
.pc #personalCenterPopup {
  padding: 20px 20px 0;
  display: none;
  position: relative;
  background: linear-gradient(180deg, #f0f6ff 0%, #ffffff 100%);
  border-radius: 10px;
  min-height: 170px;
}

.pc .userLayer {
  border-radius: 10px;
}
.pc .userLayer.layui-layer-page .layui-layer-content {
  overflow: unset;
}

.pc .user-logout img {
  width: 12%;
}

.pc .user-info {
  display: flex;
  /* 允许换行 */
  flex-wrap: wrap;
  width: 100%;
  white-space: nowrap;
  padding-top: 10px;
}
.pc .lineH25 {
  line-height: 30px;
}

.pc .user-info > div:first-child {
  line-height: 25px;
  /* padding: 10px 0; */
}

/* 第二个div */

.pc .user-info .user-info-name .user-info-name-top {
  max-width: 180px;
}

.pc .user-info .user-info-name > div:first-child {
  font-weight: 400;
}
.pc .user-card {
  font-size: 14px;
}
.pc .user-cell {
  width: 33.33%;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  line-height: 25px;
}

.pc .noCodeStyle > div:nth-child(3n + 3) {
  text-align: end !important;
}

.pc .noCodeStyle .user-info-name .user-info-name-top {
  font-size: 25px !important;
}
.pc .baseCodeStyle > div:nth-child(n + 2) {
  text-align: left !important;
}

.pc .baseCodeStyle > div:nth-child(3n + 3) {
  text-align: left !important;
}
.pc .baseCodeStyle > div:nth-child(3n + 1) {
  text-align: end !important;
}
.pc .baseCodeStyle > div:first-child {
  font-weight: 400;
  font-size: 25px !important;
  text-align: left !important;
  padding-bottom: 20px;
}

.pc .baseCodeStyle .user-info-name > div:first-child {
  font-weight: 400;
  font-size: 25px !important;
}
.pc .baseCodeStyle > div:first-child > div:last-child {
  font-size: 13px;
}

.pc .user-info-code {
  position: absolute;
  top: 62px;
  right: 0px;
}
.pc .user-info-code div {
  color: var(--styleColor);
  padding: 0 10px;
  min-width: 75px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 30% 100%;
  border-bottom-left-radius: 30% 100%;
  white-space: nowrap;
  font-size: 12px;
  background-image: linear-gradient(to right, #e6e9f0 0%, #eef1f5 100%);
  font-size: 12px !important;
}

.pc .user-fade {
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease-out;
}

.pc .user-fade.show {
  visibility: visible;
  opacity: 1;
}
.pc .user-login-Form .layui-form-item label {
  width: 15vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border-width: 0.1px;
  border-style: solid;
  border-radius: 2px 0 0 2px;
  box-sizing: border-box;
  border: 0.1px solid #eee;
  border-right: none;
}
.pc .ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.pc .user-account-header p {
  font-size: 0.3rem;
  gap: 0px;
}
.pc .layui-laydate {
  left: 70px !important;
  top: 124.875px !important;
}
