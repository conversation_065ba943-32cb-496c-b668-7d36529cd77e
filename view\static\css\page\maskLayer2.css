#fullbg {
    background-color: black;
    left: 0;
    opacity: 0.5;
    position: absolute;
    top: 0;
    z-index: 20891017;
    filter: alpha(opacity=80);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    height: 100%;
    width: 100%;
}

#dialog {
    /* height: 300px; */
    left: 50%;
    margin: -5rem 0 0 -7.5rem;
    position: fixed !important;
    top: 50%;
    /* width: 300px; */
    z-index: 20891019;
    display: none;
}

#dialog p {
    /* margin: 0 0 12px;
    height: 24px;
    line-height: 24px;
    background: #cccccc; */
}

.maskimg_warp {
    width: 15rem;
    height: 10rem;
    border-radius: 0.5rem;
    background-color: #fff;
    font-size: 0.9rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-weight: 600;
}

/* .ad_warp {
  width: 18.75rem;
  height: 18.75rem;
} */

.close_warp {
    position: fixed;
    top: 15%;
    right: 10%;
    width: 2rem;
    height: 2rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 50%;
    background: #fff;
    color: #4f4f4f;
    font-weight: 600;
    font-size: 1.5rem;
}

.maskimg_text1 {
    margin-bottom: 0.4rem;
    color: #770239;
}

.maskimg_text1,
.maskimg_text2 {
    text-align: center;
    padding: 0 0.5rem;
    /* color: #770239; */
}
