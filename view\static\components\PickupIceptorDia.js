// 预约时间拦截器弹窗,若为TakeAway模式且预约日期时间不匹配则弹窗显示
const PickupIceptorDia = {
  props: ["showInvalidPickupDia"],
  methods: {
    close() {
      this.$emit("update:showInvalidPickupDia", false)
    },
    confirm() {
      sessionStorage.removeItem("shopCartList")
      window.location.href = "./map.html"
    }
  },
  template: `
     <v-dialog
      v-model="showInvalidPickupDia"
      persistent
    >
      <v-card>
        <v-card-title/>
        <v-card-text style="font-size: 0.4rem">
          <p v-html="$parent.invalidPickupMsg"></p>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="var(--styleColor)"
            text
            @click="confirm"
          >
            {{$parent.systemLanguage.reSelectShop}}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>`
}
