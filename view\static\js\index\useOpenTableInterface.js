//解析uiconfig配置
function dynamicConfig(data) {
  const defaultConfig = {
    limitByFcode: {
      en: "Please configure limitByFcode prompt",
      zh: "请配置limitByFcode提示"
    },
    currencyWay: "$",
    priceWay: {
      defaultPrice: "PRICE1",
      use_dow: []
    },
    page: 1
  }
  //用来存储解析的config集
  let tempObj = {}
  //是否为array\object字符串
  const isJSON = str => {
    try {
      let obj = JSON.parse(str)
      if (typeof obj == "object" && obj) {
        return obj
      } else {
        return false
      }
    } catch (e) {
      return false
    }
  }
  //是否为number
  const isNumber = str => {
    try {
      let num = Number(str)
      return typeof num == "number" && !isNaN(num)
    } catch (e) {
      return false
    }
  }
  //根据值判断类型
  const parseType = d => {
    if (Object.keys(d).includes("value")) {
      return isNumber(d.value) ? Number(d.value) : isJSON(d.value) || d.value
    } else if (Object.keys(d).includes("switchVal")) {
      //为布尔类型
      return d.switchVal
    }
  }
  //解析新增的config项目
  data.forEach(el => {
    if (!Object.keys(tempObj).includes(el.type)) {
      tempObj[el.type] = parseType(el)
    }
  })
  //解析默认配置
  Object.keys(defaultConfig).forEach(key => {
    if (!Object.keys(tempObj).includes(key) || !tempObj[key]) {
      tempObj[key] = defaultConfig[key]
    }
  })
  return tempObj
}
//发送openTable请求

function sendOpenTableRequest(data, page) {
  return new Promise(async (resolve, reject) => {
    let versionNumber = sessionStorage.getItem("versionNumber") || ""
    await $.ajax({
      //在數據未加載回來之前，禁止使用"點擊開始下單"按鈕
      type: "Get", //请求方式
      url: page === "index" ? "./store/openTable" : "../store/openTable", //请求url地址
      data,
      headers: {
        versionNumber
      },
      // xhrFields:{
      //     responseType:'json'
      // },
      success: result => {
        resolve(result)
      },
      error: error => {
        reject(error)
      }
    })
  })
}
