<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
      name="viewport"
    />
    <meta content="ie=edge" http-equiv="X-UA-Compatible" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link href="../../static/elementUI/index.css" rel="stylesheet" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>
    <!--     <script src="../../static/js/cms/VirtualList.js"></script> -->
    <!--     <script src="../../static/js/cms/SelectVirtualList.js"></script> -->
    <script src="../../static/js/cms/usePopover.js"></script>
    <!--     <script src="../../static/js/cms/VirtualItem.js"></script> -->
    <!--     <script src="../../static/js/cms/testData.js"></script> -->
    <!--     <script src="../../static/js/cms/cloneDeep.js"></script> -->
    <!--     <script src="../../static/js/cms/useElSelectInfiniteScroll.js"></script> -->
    <script src="../../static/moment/moment.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      .el-divider--horizontal {
        margin: 10px 0;
      }

      .el-popover {
        font-family: "Raleway", Arial, sans-serif;
        user-select: none;
      }

      .el-popover .el-scrollbar__wrap {
        overflow-x: hidden;
      }

      /*.el-popover .el-scrollbar__view{*/
      /*     padding-bottom: 20px;*/
      /* }*/
      .el-popper .el-link.is-underline:hover:after {
        bottom: 4px;
      }

      .el-tag {
        font-size: inherit;
      }

      .small {
        font-size: 12px;
      }

      .small .el-divider--horizontal {
        margin: 5px 0;
      }

      .small .el-tag {
        height: 26px;
        line-height: 26px;
      }

      .popper-loading .el-cascader-menu__empty-text {
        color: transparent;
        user-select: none;
      }

      .popper-loading .el-cascader-menu__empty-text:before {
        content: "NO DATA";
        color: #c0c4cc;
      }

      .el-scrollbar__view.el-select-dropdown__list {
        scroll-behavior: auto;
        scroll-margin: 1em;
      }

      .el-link.el-link--default {
        color: inherit;
      }

      /*select 虚拟滚动*/
      .virtual-item {
        font-size: 14px;
        padding: 0 20px;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #606266;
        height: 34px;
        line-height: 34px;
        box-sizing: border-box;
        cursor: pointer;
      }

      .virtual-item:hover {
        background-color: #eee;
      }

      .virtual-item.is-selected span {
        color: #409eff;
      }

      .el-popover.el-popper.select-virtual-list-popover {
        height: 300px;
        padding: 0;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #ffffff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
      }

      .el-popover.el-popper.select-virtual-list-popover .virtual-list {
        width: 100%;
        height: calc(100% - 20px);
        padding: 10px 0;
        overflow-y: auto;
      }

      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        background-color: #fff;
      }

      ::-webkit-scrollbar-thumb {
        background-color: #aaa !important;
        border-radius: 10px !important;
      }

      ::-webkit-scrollbar-track {
        background-color: transparent !important;
        border-radius: 10px !important;
        -webkit-box-shadow: none !important;
      }

      .el-input--small .el-input__inner[t="select"]:hover {
        cursor: pointer;
      }

      .marginLeft10 {
        margin-left: 10px;
      }

      .discount-table-expand {
        display: flex;
        /*justify-content: space-around;*/
        width: 100%;
        flex-wrap: wrap;
      }

      .discount-table-expand label {
        /* width: 90px; */
        color: #99a9bf;
        white-space: nowrap;
      }

      .discount-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
        padding-left: 40px;
        display: flex;
        align-items: center;
      }

      .discount-table-expand .el-form-item .el-form-item__content {
        height: 100%;
        display: flex;
        align-items: center;
        line-height: normal;
      }
      .div-form-item {
        width: 200px;
        /* margin-left: 23%; */
        margin-bottom: 18px;
        word-break: keep-all; /* 保持单词完整 */
        white-space: normal; /* 允许换行 */
      }
      .form-item-title {
        color: #409eff;
      }
      .cell-item {
        margin-left: 20px;
      }
      .el-form-item__label {
        /* 单词不截断换行 */
        word-break: keep-all; /* 保持单词完整 */
        white-space: normal; /* 允许换行 */
      }
      .el-divider--horizontal {
        height: 3px;
      }
      .el-form-item--small .el-form-item__label {
        line-height: 20px;
      }
    </style>
  </head>

  <body>
    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>
    <div id="app">
      <template>
        <!--
          expand-row-keys:expends是数组，设置你要展开行的id
          row-key:通过getRowKeys方法获取到row的行id值
        -->
        <el-table
          v-if="tableHeight"
          :data="tableData"
          :expand-row-keys="expends"
          :row-key="getRowKeys"
          style="width: 100%"
          :max-height="tableHeight"
          border
          ref="report-table"
          empty-text="No Data"
          class="tableContent"
        >
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-form label-position="left" class="discount-table-expand">
                <el-form-item label="Auto Uses">
                  <el-switch
                    v-model="props.row.defaultUses"
                    @change="onEditSwitch($event, props.row)"
                  ></el-switch>
                </el-form-item>
                <el-form-item label="Compulsory">
                  <el-switch
                    v-model="props.row.compulsory"
                    :disabled="!props.row.defaultUses"
                    @change="onEditSwitch($event, props.row)"
                  ></el-switch>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column label="Store Number" align="center">
            <tempalte slot-scope="{row}">
              <span>{{row.storeNumber}}</span>
            </tempalte>
          </el-table-column>
          <el-table-column label="Promo Code" align="center">
            <tempalte slot-scope="{row}">
              <span>{{row.code}}</span>
            </tempalte>
          </el-table-column>
          <el-table-column width="140" label="POS Discount Code" align="center">
            <tempalte slot-scope="{row}">
              <span>{{row.discountCode}}</span>
            </tempalte>
          </el-table-column>
          <el-table-column width="140" label="Name 1" align="center">
            <template slot-scope="{row}">
              <span>{{parse(row.desc).en}}</span>
            </template>
          </el-table-column>
          <el-table-column width="140" label="Name 2" align="center">
            <template slot-scope="{row}">
              <span>{{parse(row.desc).zh}}</span>
            </template>
          </el-table-column>
          <el-table-column width="160" label="Name 3" align="center">
            <template slot-scope="{row}">
              <span>{{parse(row.desc).thirdLan}}</span>
            </template>
          </el-table-column>
          <el-table-column width="140" label="Minumum purchase" align="center">
            <tempalte slot-scope="{row}">
              <span>{{row.amount}}</span>
            </tempalte>
          </el-table-column>
          <el-table-column width="140" label="Date" align="center">
            <tempalte slot-scope="{row}">
              <span>{{row.use_date}}</span>
            </tempalte>
          </el-table-column>
          <el-table-column width="140" label="Dow" align="center">
            <tempalte slot-scope="{row}">
              <span>{{row.use_dow}}</span>
            </tempalte>
          </el-table-column>
          <el-table-column width="140" label="Time" align="center">
            <tempalte slot-scope="{row}">
              <span>{{row.use_time}}</span>
            </tempalte>
          </el-table-column>

          <el-table-column label="Usable" width="140" align="center">
            <template slot-scope="{row}">
              <el-switch v-model="row.switchVal" @change="onEditSwitch($event, row)"></el-switch>
            </template>
          </el-table-column>

          <!-- 操作栏目 -->
          <el-table-column fixed="right" prop label="Operation" align="center" width="150">
            <el-table-column width="110" align="center" fixed="right">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible=true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <!-- 除按钮类型外显示编辑按钮 -->
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-popconfirm
                  title="Are you sure to delete this data?"
                  icon-color="red"
                  @confirm="onDel(scope.$index, scope.row)"
                  class="marginLeft10"
                >
                  <el-button
                    size="small"
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    slot="reference"
                  ></el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <tempalte>
        <el-dialog
          @close="onClose('addForm')"
          title="Add Dialog"
          :visible.sync="addDialogVisible"
          :close-on-click-modal="false"
        >
          <el-form
            ref="addForm"
            :model="addForm"
            :rules="rules"
            size="small"
            label-width="200px"
            class="formBox"
            label-position="left"
          >
            <el-scrollbar style="height: 50vh">
              <el-form-item label="Store Number" prop="storeNumber">
                <el-select
                  v-model="addForm.storeNumber"
                  clearable
                  placeholder="Please enter the store number"
                  @clear="addForm.discountCode=''"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in discountCodeList"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="Promo Code" prop="code">
                <el-input v-model="addForm.code" placeholder="Please enter promo code"></el-input>
              </el-form-item>
              <el-form-item label="POS Discount Code" prop="discountCode" style="width: 100%">
                <el-select
                  v-model="addForm.discountCode"
                  clearable
                  filterable
                  no-match-text="No matching data"
                  no-data-text="Please enter the store number first"
                  placeholder="Please select POS discount code"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in getDiscountCodeListAd"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"
                  >
                    <span v-if="!item.info||!Object.keys(item.info).length">{{ item.label }}</span>
                    <use-popover
                      v-else
                      :data-list="item.info"
                      placement="left"
                      :title="item.label"
                      :show-text="item.label"
                      :width="250"
                      size="small"
                    ></use-popover>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="Promotion Name 1" prop="desc.en">
                <el-input
                  v-model="addForm.desc.en"
                  placeholder="Please enter the description"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="Name 2" prop="desc.zh">
                <el-input
                  v-model="addForm.desc.zh"
                  placeholder="Please enter description"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="Name 3" prop="desc.thirdLan">
                <el-input
                  v-model="addForm.desc.thirdLan"
                  placeholder="Please enter description"
                  clearable
                ></el-input>
              </el-form-item>
              <div class="div-form-item form-item-title">Promotion Rules</div>
              <el-form-item label="1. Minumum purchase">
                <el-input-number
                  style="width: 100%"
                  :min="0"
                  v-model="addForm.amount"
                  placeholder="Minimum purchase amount to qualify for this promotion"
                ></el-input-number>
              </el-form-item>
              <div class="div-form-item">Exclude following from minimum purchase calculation</div>
              <el-form-item class="cell-item" label="POS Food Groups">
                <el-input
                  v-model="addForm.inoperative_ftCodes"
                  placeholder="Enter the FoodType code (Use ; to separate multiple codes)"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="POS Food Codes">
                <el-input
                  v-model="addForm.inoperative_fCodes"
                  placeholder="Enter the FoodList code (Use ; to separate multiple codes)"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Modifier Groups">
                <el-input
                  v-model="addForm.inoperative_mtCodes"
                  placeholder="Enter the MType code (Use ; to separate multiple)"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Modifiers">
                <el-input
                  v-model="addForm.inoperative_mlCodes"
                  placeholder="Enter the MList code (Use ; to separate multiple)"
                  clearable
                ></el-input>
              </el-form-item>
              <div class="div-form-item">2. Promotion availability</div>
              <el-form-item class="cell-item" label="Date" prop="use_date">
                <el-input
                  v-model="addForm.use_date"
                  placeholder="Format : yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Time" prop="use_time">
                <el-input
                  v-model="addForm.use_time"
                  placeholder="Format : hh:mm-hh:mm  (Use ; to separate multiple times)"
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Dow" prop="use_dow">
                <el-input
                  v-model="addForm.use_dow"
                  placeholder="Format : 1234567H  (1=Monday,7=Sunday,H=holidays)"
                ></el-input>
              </el-form-item>
              <div class="div-form-item">3. Exemptions</div>
              <el-form-item
                label="Promotion will not apply if these food items are in the order"
                prop="use_dow"
              >
                <el-input
                  style="margin-left: 18px"
                  v-model="addForm.conflict_fCodes"
                  placeholder="Enter POS food codes (use ; to separate multiple items)"
                ></el-input>
              </el-form-item>
              <div class="div-form-item">4. Conditions</div>
              <el-form-item class="cell-item" label="Dine In Model">
                <el-switch v-model="addForm.dineInTouch"></el-switch>
              </el-form-item>
              <el-form-item class="cell-item" label="Take Away Model">
                <el-switch v-model="addForm.takeawayTouch"></el-switch>
              </el-form-item>
              <el-form-item class="cell-item" label="Auto apply this promotion" prop="defaultUses">
                <el-switch v-model="addForm.defaultUses"></el-switch>
              </el-form-item>
              <el-form-item
                class="cell-item"
                label="Users cannot remove this promotion"
                prop="compulsory"
              >
                <el-switch
                  v-model="addForm.compulsory"
                  :disabled="!addForm.defaultUses"
                ></el-switch>
              </el-form-item>
              <el-form-item class="cell-item" label="After Member Discount">
                <el-switch v-model="addForm.afterMemberDiscount"></el-switch>
              </el-form-item>
              <el-divider></el-divider>
              <el-form-item class="cell-item" label="Activate Promotion" prop="switchVal">
                <el-switch v-model="addForm.switchVal"></el-switch>
              </el-form-item>
            </el-scrollbar>
          </el-form>
          <div slot="footer">
            <el-button @click="addDialogVisible = false" v-show="!btnLoading">Cancel</el-button>
            <el-button type="primary" @click="onAddConfirm" :loading="btnLoading">Add</el-button>
          </div>
        </el-dialog>
      </tempalte>
      <template>
        <el-dialog
          @close="onClose('editForm')"
          title="Edit Dialog"
          :visible.sync="editDialogVisible"
          :close-on-click-modal="false"
        >
          <el-form
            ref="editForm"
            :model="editForm"
            :rules="rules"
            size="small"
            label-width="200px"
            label-position="left"
          >
            <el-scrollbar style="height: 50vh">
              <el-form-item label="Store Number" prop="storeNumber">
                <el-select
                  disabled
                  v-model="editForm.storeNumber"
                  clearable
                  placeholder="Please enter the store number"
                  @clear="editForm.discountCode=''"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in discountCodeList"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="Promo Code" prop="code">
                <el-input v-model="editForm.code" placeholder="Please enter promo code"></el-input>
              </el-form-item>
              <el-form-item label="POS Discount Code" prop="discountCode">
                <el-select
                  v-model="editForm.discountCode"
                  clearable
                  filterable
                  no-match-text="No matching data"
                  no-data-text="Please select store number first"
                  placeholder="Please select POS discount code"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in getDiscountCodeListEd"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"
                  >
                    <span v-if="!item.info||!Object.keys(item.info).length">{{ item.label }}</span>
                    <use-popover
                      v-else
                      :data-list="item.info"
                      placement="left-start"
                      :title="item.label"
                      :show-text="item.label"
                      :width="300"
                      size="small"
                    ></use-popover>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="Promotion Name 1" prop="desc.en">
                <el-input
                  v-model="editForm.desc.en"
                  placeholder="Please enter the description"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="Name 2" prop="desc.zh">
                <el-input
                  v-model="editForm.desc.zh"
                  placeholder="Please enter description"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="Name 3" prop="desc.thirdLan">
                <el-input
                  v-model="editForm.desc.thirdLan"
                  placeholder="Please enter description"
                  clearable
                ></el-input>
              </el-form-item>
              <div class="div-form-item form-item-title">Promotion Rules</div>

              <el-form-item label="1. Minumum purchase">
                <el-input-number
                  style="width: 100%"
                  :min="0"
                  v-model="editForm.amount"
                  placeholder="Minimum purchase amount to qualify for this promotion"
                ></el-input-number>
              </el-form-item>
              <div class="div-form-item">Exclude following from minimum purchase calculation</div>
              <el-form-item class="cell-item" label="POS Food Groups">
                <el-input
                  v-model="editForm.inoperative_ftCodes"
                  placeholder="Enter the FoodType code (Use ; to separate multiple codes)"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="POS Food Codes">
                <el-input
                  v-model="editForm.inoperative_fCodes"
                  placeholder="Enter the FoodList code (Use ; to separate multiple codes)"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Modifier Groups">
                <el-input
                  v-model="editForm.inoperative_mtCodes"
                  placeholder="Enter the MType code (Use ; to separate multiple)"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Modifiers">
                <el-input
                  v-model="editForm.inoperative_mlCodes"
                  placeholder="Enter the MList code (Use ; to separate multiple)"
                  clearable
                ></el-input>
              </el-form-item>
              <div class="div-form-item">2. Promotion availability</div>
              <el-form-item class="cell-item" label="Date" prop="use_date">
                <el-input
                  v-model="editForm.use_date"
                  placeholder="Format : yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Time" prop="use_time">
                <el-input
                  v-model="editForm.use_time"
                  placeholder="Format : hh:mm-hh:mm  (Use ; to separate multiple times)"
                ></el-input>
              </el-form-item>
              <el-form-item class="cell-item" label="Dow" prop="use_dow">
                <el-input
                  v-model="editForm.use_dow"
                  placeholder="Format : 1234567H  (1=Monday,7=Sunday,H=holidays)"
                ></el-input>
              </el-form-item>
              <div class="div-form-item form-item-title">3. Exemptions</div>
              <el-form-item
                label="Promotion will not apply if these food items are in the order"
                prop="use_dow"
              >
                <el-input
                  style="margin-left: 18px"
                  v-model="editForm.conflict_fCodes"
                  placeholder="Enter POS food codes (use ; to separate multiple items)"
                ></el-input>
              </el-form-item>
              <div class="div-form-item">4. Conditions</div>
              <el-form-item class="cell-item" label="Dine In Model">
                <el-switch v-model="editForm.dineInTouch"></el-switch>
              </el-form-item>
              <el-form-item class="cell-item" label="Take Away Model">
                <el-switch v-model="editForm.takeawayTouch"></el-switch>
              </el-form-item>
              <el-form-item class="cell-item" label="Auto apply this promotion" prop="defaultUses">
                <el-switch v-model="editForm.defaultUses"></el-switch>
              </el-form-item>
              <el-form-item
                class="cell-item"
                label="Users cannot remove this promotion"
                prop="compulsory"
              >
                <el-switch
                  v-model="editForm.compulsory"
                  :disabled="!editForm.defaultUses"
                ></el-switch>
              </el-form-item>
              <el-form-item l class="cell-item" label="After Member Discount">
                <el-switch v-model="editForm.afterMemberDiscount"></el-switch>
              </el-form-item>
              <el-divider></el-divider>
              <el-form-item class="cell-item" label="Activate Promotion" prop="switchVal">
                <el-switch v-model="editForm.switchVal"></el-switch>
              </el-form-item>
            </el-scrollbar>
          </el-form>
          <div slot="footer">
            <el-button @click="editDialogVisible = false" v-show="!btnLoading">Cancel</el-button>
            <el-button type="primary" @click="onEditConfirm" :loading="btnLoading">Edit</el-button>
          </div>
        </el-dialog>
      </template>
    </div>
    <script>
      const app = new Vue({
        el: "#app",
        data() {
          return {
            tableHeight: 0,
            tableData: [],
            addDialogVisible: false,
            editDialogVisible: false,
            domain: sessionStorage.getItem("domain"),
            btnLoading: false, //弹窗的请求loading
            rules: {
              storeNumber: [
                { required: true, message: "Please enter StoreNumber", trigger: "blur" }
              ],
              code: [{ required: true, message: "Please enter promo code", trigger: "blur" }],
              discountCode: [
                { required: true, message: "Please select POS discount code", trigger: "change" }
              ],
              use_dow: [
                {
                  required: false,
                  pattern: /[1-7H]$/,
                  message: "Format : 1234567H  (1=Monday,7=Sunday,H=holidays)"
                }
              ],
              use_date: [
                {
                  required: false,
                  pattern:
                    /^((\d{4}(.)(1[0-2]|0?\d)\3([0-2]\d|\d|30|31))(-)(\d{4}(.)(1[0-2]|0?\d)\3([0-2]\d|\d|30|31))(;)?)*$/,
                  message: "Format : yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
                }
              ],
              use_time: [
                {
                  required: false,
                  pattern: /^((?:[01]\d|2[0-3]):([0-5]\d)(-)(?:[01]\d|2[0-3]):([0-5]\d)(;)?)*$/,
                  message: "Format : hh:mm-hh:mm  (Use ; to separate multiple times)"
                }
              ]
            },
            discountCodeList: [], //优惠码list
            resetForm: {},
            addForm: {
              storeNumber: "",
              discountCode: "",
              code: "",
              amount: null,
              inoperative_fCodes: "",
              inoperative_ftCodes: "",
              inoperative_mlCodes: "",
              inoperative_mtCodes: "",
              exclude: "",
              desc: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              use_date: "",
              use_dow: "",
              use_time: "",
              switchVal: true,
              defaultUses: false, //是否默认自动开启折扣券
              compulsory: false, //是否强制使用(不允许取消折扣券)
              afterMemberDiscount: false, // 是否在会员折扣之后再计算折扣信息
              conflict_fCodes: "", //与折扣冲突的food codes(类似打包盒)
              dineInTouch: true,
              takeawayTouch: true
            },
            editForm: {
              storeNumber: "",
              discountCode: "",
              code: "",
              amount: null,
              inoperative_fCodes: "",
              inoperative_ftCodes: "",
              inoperative_mlCodes: "",
              inoperative_mtCodes: "",
              desc: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              use_date: "",
              use_dow: "",
              use_time: "",
              switchVal: false,
              defaultUses: false, //是否默认自动开启折扣券
              compulsory: false, //是否强制使用(不允许取消折扣券)
              afterMemberDiscount: false,
              conflict_fCodes: "", //与折扣冲突的food codes(类似打包盒)
              dineInTouch: true,
              takeawayTouch: true
            },
            originEditData: null, //用于保存编辑源数据()
            expends: []
          }
        },
        components: {
          usePopover
          // SelectVirtualList,
          // 'virtual-list': VirtualList,
          // baseSelect:useElSelectInfiniteScroll
        },
        computed: {
          getDiscountCodeListAd: function () {
            return (
              this.discountCodeList.find(el => el.key === this.addForm.storeNumber)?.children || []
            )
          },
          getDiscountCodeListEd: function () {
            return (
              this.discountCodeList.find(el => el.key === this.editForm.storeNumber)?.children || []
            )
          },
          getStoreNumberList: function () {
            return this.discountCodeList.map(el => {
              return {
                key: el.key,
                label: el.label
              }
            })
          }
        },
        mounted() {
          this.resetForm = JSON.parse(JSON.stringify(this.addForm))
          this.tableHeight = this.getTableHeight(this, 0, null)
        },
        created() {
          this.getData()
        },
        watch: {
          "addForm.storeNumber": function (val) {
            this.log("watch => storeNumber", val)
          },
          "addForm.discountCode": function (val) {
            this.log("watch => discountCode", val)
          },
          //defaultUses开关开启状态下才能编辑compulsory开关
          "addForm.defaultUses": function (val) {
            if (!val) this.addForm.compulsory = false
          },
          "editForm.defaultUses": function (val) {
            if (!val) this.editForm.compulsory = false
          }
        },
        methods: {
          // 获取弹窗的 input/select 宽度
          getWidth() {
            let dialogDom = document.querySelector(".el-form-item__content")
            if (dialogDom?.clientWidth) {
              this.valueDomWidth = dialogDom.clientWidth
            } else {
              this.valueDomWidth = Math.round((document.body.clientWidth / 100) * 30)
            }
            return this.valueDomWidth
          },
          log(h, v) {
            console.log(`${h} : %c ${v}`, "color:#c12c1f;font-size:18px")
          },
          parse(j) {
            try {
              return JSON.parse(j)
            } catch {
              return ""
            }
          },
          getData() {
            $.get({
              url: "../../manager_promotionDiscount/getAll",
              dataType: "json",
              data: { domain: this.domain },
              success: res => {
                if (res.statusCode === 200) {
                  this.tableData = res.data.promotionDiscountList
                  this.discountCodeList = res.data.treeList
                  // this.discountCodeList=testData
                  console.log(this.tableData, "all data")
                } else {
                  this.getData()
                  this.$message.error("Request failed, please try again")
                }
              },
              error: error => {
                console.log(error, "error")
                this.$message.error("Request failed, please try again")
              }
            })
          },
          onEdit(index, row) {
            this.editDialogVisible = true
            this.$nextTick(() => {
              this.editForm = {
                ...this.resetForm,
                ...row,
                desc: JSON.parse(row.desc)
              }
            })
            this.originEditData = JSON.parse(JSON.stringify(row))
            console.log(this.originEditData, this.editForm, "edit data")
          },
          onEditConfirm() {
            this.$refs?.editForm.validate(valid => {
              if (!valid) return
              let { desc, use_date, use_time } = this.editForm
              let data = {
                ...this.editForm,
                domain: this.domain,
                desc: JSON.stringify(desc),
                originalCode: this.originEditData?.code,
                originalStoreNumber: this.originEditData?.storeNumber,
                ...this.useDateTimeFormat({ use_date, use_time })
              }
              delete data.discount
              $.post({
                url: "../../manager_promotionDiscount/updateOne",
                data,
                dataType: "json",
                timeout: 10000,
                traditional: true, // 防止深度序列号
                beforeSend: () => {
                  this.btnLoading = true
                },
                complete: () => {
                  this.btnLoading = false
                },
                success: res => {
                  if (res.statusCode === 200) {
                    this.getData()
                    // console.log(res);
                    this.$message.success("Edit Success！")
                    this.editDialogVisible = false
                  } else {
                    this.$message.error("Edit Failure！")
                  }
                },
                error: error => {
                  // console.log(res);
                  this.$message.error("Edit Failure！")
                }
              })
            })
          },
          onAddConfirm() {
            this.$refs?.addForm.validate(valid => {
              if (!valid) return
              let { desc, use_date, use_time } = this.addForm
              let data = {
                domain: this.domain,
                ...this.addForm,
                desc: JSON.stringify(desc),
                ...this.useDateTimeFormat({ use_date, use_time })
              }
              $.post({
                url: "../../manager_promotionDiscount/addOne",
                data,
                dataType: "json",
                traditional: true, // 防止深度序列号
                beforeSend: () => {
                  this.btnLoading = true
                },
                complete: () => {
                  this.btnLoading = false
                },
                success: res => {
                  // console.log(res, "添加成功")
                  if (res.statusCode === 200) {
                    this.getData()
                    // console.log(res);
                    this.$message.success("Successfully added！")
                    this.addDialogVisible = false
                  } else {
                    switch (res.statusCode) {
                      case 401:
                        this.$message.error("Data for this POS discount code  already exists！")
                        break
                      default:
                        this.$message.error("Fail to add！")
                        break
                    }
                  }
                },
                error: error => {
                  this.$message.error("Fail to add！")
                }
              })
            })
          },
          // 开关触发
          onEditSwitch($event, row) {
            let data = {
              ...row,
              domain: this.domain,
              originalCode: row.code,
              compulsory: row.defaultUses ? row.compulsory : false,
              desc: JSON.stringify(JSON.parse(row.desc)),
              originalStoreNumber: row.storeNumber
            }
            delete data.discount
            $.post({
              url: "../../manager_promotionDiscount/updateOne",
              data,
              dataType: "json",
              traditional: true, // 防止深度序列号
              success: res => {
                this.getData()
                this.getExpends(row) //回显展开行
                if (res.statusCode !== 200) {
                  this.$message.error(res.errorDesc || "Edit failure！")
                } else {
                  this.$message.success("Edit success！")
                }
              },
              error: res => {
                this.$message.error("Edit failure！")
              }
            })
          },
          onDel(index, row) {
            let data = {
              domain: this.domain,
              storeNumber: row.storeNumber,
              code: row.code
            }
            $.post({
              url: "../../manager_promotionDiscount/deleteOne",
              data,
              dataType: "json",
              success: res => {
                if (res.statusCode === 200) {
                  this.getData()
                  this.$message.success("Successfully delete!")
                  this.addDialogVisible = false
                } else {
                  this.$message.error("Fail to add！")
                }
              },
              error: res => {
                // console.log(res);
                this.$message.error("Fail to delete!")
              }
            })
          },
          onClose(formName) {
            this.$refs[formName].resetFields()
          },
          virtualClickItemCall(item) {
            if (this.editDialogVisible) {
              this.editForm.discountCode = item.key
            } else if (this.addDialogVisible) {
              this.addForm.discountCode = item.key
            }
          },
          showDisCount(row) {
            let { discountCode, storeNumber } = row
            let list = this.discountCodeList.find(el => el.key == storeNumber)?.children
            return list?.find(el => el.key == discountCode) || {}
          },
          //重置展开行
          getExpends(row) {
            let { domain, storeNumber, code } = row
            this.expends = [domain + storeNumber + code]
          },
          getRowKeys(row) {
            let { domain, storeNumber, code } = row
            return domain + storeNumber + code
          },
          useDateTimeFormat(params) {
            //params : use_date:2022.10.1-2022.12.30, use_time:12:1-14:2  不规则date/time
            let obj = {}
            for (let key in params) {
              //多个;分隔
              let res = ""
              if (!params[key]) continue
              res = params[key]?.split(";").reduce((pro, cur) => {
                let arr = cur.split("-").map(i => {
                  //日期
                  if (i.split(".").length === 3) {
                    return moment(i).format("YYYY.MM.DD")
                  } else if (i.split(":").length === 2) {
                    //时间
                    return moment(i, "hh:mm").format("HH:mm")
                  } else {
                    return i
                  }
                })
                let r = arr.join("-")
                pro += r + ";"
                return pro
              }, "")
              res = res?.substr(0, res.length - 1)
              obj[key] = res || ""
            }
            return obj
          },
          getTableHeight(that, val = 32, name) {
            let fixHeight = 16 // padding为8,8
            let searchFormHeight = name ? that.$refs[name].clientHeight : 0 // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - 40) // 计算完之后剩余table可用的高度
            // console.log(pageHeight, searchFormHeight, tableHeight, "searchFormHeight")
            return tableHeight
          }
        }
      })
    </script>
  </body>
</html>
