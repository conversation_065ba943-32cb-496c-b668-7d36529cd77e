const TotalAmount = {
  props: {
    allPrice: {
      type: Number,
      default: 0
    },
    label: {
      type: String,
      default: "Total Amount"
    },
    subtotalLabel: {
      type: String,
      default: "Subtotal"
    },
    currencyWay: {
      type: String,
      default: ""
    },
    originalPrice: {
      type: Number,
      default: 0
    },
    discountGroup: {
      type: Array,
      default: []
    },
    taxesGroup: {
      type: Array,
      default: []
    },
    lan: {
      type: String,
      default: "en"
    },
    hasDiscount: Boolean,
    loading: false,
    taxConfig: {},
    serviceCharges: {
      type: Object,
      default: {}
    },
    systemLanguage: {
      type: Object,
      default: {}
    },
    cartServiceCharges: {
      type: Number,
      default: 0
    }
  },
  computed: {
    needSort() {
      const { billTaxIncludeServiceCharge = false, billTaxIncludeDiscount = false } =
        this.taxConfig || {}
      return !(billTaxIncludeServiceCharge && billTaxIncludeDiscount)
    }
  },
  methods: {
    discountDesc(item) {
      if (!item.hasOwnProperty("promotionDiscountCode")) {
        //member discount
        return item.language[this.lan] || app.systemLanguage.memberDiscount
      }
      return item.language[this.lan]
    },
    taxDesc(item) {
      return item.language[this.lan]
    },
    formattedPrice(price = 0) {
      return `${this.currencyWay}${retainSignificantDecimals(price)}`
    }
  },
  template: `
  <div style="width: 100%; display: flex; flex-direction: column" class="totalAmount-warp">
  <v-divider class="pl-3 pr-3 d-block"></v-divider>
  <!--   小计 -->
  <div
    v-if="hasDiscount||taxesGroup.length"
    class="d-flex align-center justify-space-between pr-6 pl-6 v-picker--full-width"
    style="font-size: 0.37rem; justify-content: space-between; order: 0"
  >
    <span>{{subtotalLabel}}</span>
    <span class="font-weight-medium totalPrice">{{currencyWay}}{{originalPrice}}</span>
  </div>
  <!--   优惠 -->
  <div
    v-if="hasDiscount"
    class="d-flex pr-6 pl-6 v-picker--full-width"
    style="font-size: 0.37rem; flex-direction: column; order: 2"
    v-for="item in discountGroup"
    :key="item.price"
  >
    <div
      style="display: flex; font-size: 0.37rem; justify-content: space-between; order: 0"
      :class="{'discountFails':item.useStatus===0}"
    >
      <span style="color: var(--styleColor); max-width: 85%">{{discountDesc(item)}}</span>
      <span
        :style="{'text-decoration':(loading?'line-through':'unset')}"
        class="font-weight-medium totalPrice"
      >
        -{{formattedPrice(item.amount)}}
      </span>
    </div>
    <div class="" style="font-size: 0.37rem" v-if="item.conflictId&&item.conflictId.length">
      <p
        class="totalAreaConfMsg"
        v-for="(tip,i) in app.discountConflictTips('totalAreaConf',item)"
        :key="i"
      >
        {{tip}}
      </p>
    </div>
  </div>

  <!--   税费 -->
  <div
    v-for="item in taxesGroup"
    :key="item.price"
    :style="{order:needSort&&item.type==='tax0Price'?1:3}"
    class="d-flex align-center justify-space-between pr-6 pl-6 v-picker--full-width"
    style="font-size: 0.37rem; justify-content: space-between; order: 3"
  >
    <span style="max-width: 85%">{{taxDesc(item)}}</span>
    <span class="font-weight-medium totalPrice">+{{formattedPrice(item.price)}}</span>
  </div>
  <!-- 服务费 -->
  <div
    v-if="serviceCharges.displayInShoppingCartPage"
    class="d-flex align-center justify-space-between pr-6 pl-6 v-picker--full-width"
    style="font-size: 0.37rem; justify-content: space-between; order: 4"
  >
    <span style="max-width: 85%">{{systemLanguage.serviceCharge}}</span>
    <span class="font-weight-medium totalPrice">+{{currencyWay}}{{cartServiceCharges}}</span>
  </div>
  <!--   总计 -->
  <div
    class="d-flex align-center justify-space-between pr-6 pl-6 v-picker--full-width"
    style="font-size: 0.42rem; justify-content: space-between; order: 5"
  >
    <span>{{label}}</span>
    <div
      v-if="loading"
      role="progressbar"
      aria-valuemin="0"
      aria-valuemax="100"
      class="v-progress-circular v-progress-circular--visible v-progress-circular--indeterminate"
      style="
        height: 23px;
        width: 23px;
        margin-right: 8px;
        color: var(--styleColor);
        caret-color: var(--styleColor);
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="21.904761904761905 21.904761904761905 43.80952380952381 43.80952380952381"
        style="transform: rotate(0deg)"
      >
        <circle
          fill="transparent"
          cx="43.80952380952381"
          cy="43.80952380952381"
          r="20"
          stroke-width="3.8095238095238093"
          stroke-dasharray="125.664"
          stroke-dashoffset="125.66370614359172px"
          class="v-progress-circular__overlay"
        ></circle>
      </svg>
      <div class="v-progress-circular__info"></div>
    </div>
    <span v-else class="font-weight-medium totalPrice">
      <span>{{currencyWay}}</span>
      {{allPrice}}
    </span>
  </div>
</div>

 


  `
}
