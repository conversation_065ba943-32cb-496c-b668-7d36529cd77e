/**
 * @file menu.js
 * @description Handles frontend logic for displaying menu items based on sold-out status.
 */

/**
 * Calculates and sets the effective sold-out status for all items.
 * Adds/updates `_effectiveItemCtrl` on FoodItem and mListItem.
 *
 * 为什么有_effectiveItemCtrl字段:需要将item本身与item子级导致的售罄逻辑分离(区分初始itemCtrl[后端返回]与计算后的值);
 *
 *  _effectiveItemCtrl已包含itemCtrl自身的状态,且始终为最新的
 * @param  baseList - Root list of FoodTypeItems.
 * @param isFood - baseList Root list of FoodItems.
 * @returns {boolean} - True if any status changed.
 */
function calculateEffectiveItemCtrl(baseList, isFood = false) {
  let changed = false
  const itemCtrlSets = new Set()
  /**
   * Checks if a TypeGroup's children can collectively meet its minQty.
   * @param  typeItem - The FoodTypeItem or mTypeItem to check.
   * @param {'foodList' | 'mListList'} listKey - The key for the children list.
   * @returns {boolean} - True if minQty CANNOT be met (thus parent should be sold out), false otherwise.
   */
  const checkTypeMinQtyCondition = (typeItem, listKey) => {
    const typeMinQty = typeItem.minQty
    const hasMinQtyRequirement = typeof typeMinQty === "number" && typeMinQty > 0

    if (!hasMinQtyRequirement) {
      return false // No minQty constraint, so this rule doesn't apply.
    }

    const childrenList = typeItem[listKey]
    if (!childrenList || childrenList.length === 0) {
      // minQty > 0 and no children, so minQty cannot be met.
      return true
    }
    const typeLNoDup = !!typeItem.lNoDup // Defaults to false if undefined/null
    let totalMaxSelectableFromChildren = 0
    for (const child of childrenList) {
      // _effectiveItemCtrl must be calculated for children first.
      // This is ensured by the overall recursive calculation order.
      if (child._effectiveItemCtrl === true) {
        // Sold-out child contributes 0 to selectable quantity
        totalMaxSelectableFromChildren += 0
      } else {
        const childTakeUpQty =
          typeof child.takeUpQty === "number" && child.takeUpQty > 0 ? child.takeUpQty : 1

        if (typeLNoDup) {
          totalMaxSelectableFromChildren += childTakeUpQty
        } else {
          // Not lNoDup: this item can be selected multiple times.
          // Its potential contribution to meeting minQty is considered "infinite" for this sum.
          totalMaxSelectableFromChildren += Infinity
        }
      }

      // Optimization: if TMSQ already meets minQty, this typeGroup is satisfiable.
      if (totalMaxSelectableFromChildren >= typeMinQty) {
        return false // MinQty CAN be met, so this type does NOT make parent sold out.
      }
    }

    // After checking all children, if TMSQ < minQty, then minQty cannot be met.
    return totalMaxSelectableFromChildren < typeMinQty
  }

  const calculateRecursiveFoodItem = item => {
    // Base status from item.itemCtrl (will be set if _effectiveItemCtrl is undefined)
    let currentEffectiveCtrl = item.itemCtrl

    // 1. Process direct FoodItem children
    let isSoldOutFromDirectFoodItemChildren = false
    if (item.foodList) {
      for (const childFoodItem of item.foodList) {
        if (calculateRecursiveFoodItem(childFoodItem)) {
          // This sets child._effectiveItemCtrl
          isSoldOutFromDirectFoodItemChildren = true
        }
      }
    }
    currentEffectiveCtrl = currentEffectiveCtrl || isSoldOutFromDirectFoodItemChildren

    // 2. Process direct mListItem children
    let isSoldOutFromDirectMListItemChildren = false
    if (item.mListList) {
      for (const childMListItem of item.mListList) {
        if (calculateRecursiveMListItem(childMListItem)) {
          // Sets child._effectiveItemCtrl
          isSoldOutFromDirectMListItemChildren = true
        }
      }
    }
    currentEffectiveCtrl = currentEffectiveCtrl || isSoldOutFromDirectMListItemChildren

    // 3. Process FoodTypeItem|mTypeItem children (for minQty rule)
    // Ensure their children (_effectiveItemCtrl) are processed first by calling their calc function.
    let isSoldOutFromTypeMinQty = false
    if (item.allTypeArry) {
      for (const typeChild of item.allTypeArry) {
        if (isFoodTypeItem(typeChild)) {
          calculateRecursiveFoodTypeItem(typeChild) // Process children of foodTypeChild
          if (checkTypeMinQtyCondition(typeChild, "foodList")) {
            isSoldOutFromTypeMinQty = true
            break
          }
        } else {
          calculateRecursiveMTypeItem(typeChild) // Process children of mTypeChild
          if (checkTypeMinQtyCondition(typeChild, "mListList")) {
            isSoldOutFromTypeMinQty = true
            break
          }
        }
      }
    }
    currentEffectiveCtrl = currentEffectiveCtrl || isSoldOutFromTypeMinQty

    if (item._effectiveItemCtrl !== currentEffectiveCtrl) {
      changed = true
    }
    item._effectiveItemCtrl = currentEffectiveCtrl
    setItemCtrl(item, currentEffectiveCtrl)
    return item._effectiveItemCtrl
  }

  const calculateRecursiveMListItem = item => {
    let currentEffectiveCtrl = item.itemCtrl

    // 1. Process direct mListItem children
    let isSoldOutFromDirectMListItemChildren = false
    if (item.mListList) {
      for (const childMListItem of item.mListList) {
        if (calculateRecursiveMListItem(childMListItem)) {
          isSoldOutFromDirectMListItemChildren = true
        }
      }
    }
    currentEffectiveCtrl = currentEffectiveCtrl || isSoldOutFromDirectMListItemChildren

    // 2. Process mTypeItem children (for minQty rule)
    let isSoldOutFromMTypeMinQty = false
    if (item.allTypeArry) {
      for (const mTypeChild of item.allTypeArry) {
        calculateRecursiveMTypeItem(mTypeChild)
        if (checkTypeMinQtyCondition(mTypeChild, "mListList")) {
          isSoldOutFromMTypeMinQty = true
          break
        }
      }
    }
    currentEffectiveCtrl = currentEffectiveCtrl || isSoldOutFromMTypeMinQty

    if (item._effectiveItemCtrl !== currentEffectiveCtrl) {
      changed = true
    }
    item._effectiveItemCtrl = currentEffectiveCtrl
    setItemCtrl(item, currentEffectiveCtrl)
    return item._effectiveItemCtrl
  }

  const calculateRecursiveFoodTypeItem = foodTypeItem => {
    // Recursively calculate for children FoodItems.
    // Their _effectiveItemCtrl is needed by checkTypeMinQtyCondition.
    if (foodTypeItem.foodList) {
      for (const foodItem of foodTypeItem.foodList) {
        calculateRecursiveFoodItem(foodItem)
      }
    }
  }

  const calculateRecursiveMTypeItem = mTypeItem => {
    if (mTypeItem.mListList) {
      for (const mListItem of mTypeItem.mListList) {
        calculateRecursiveMListItem(mListItem)
      }
    }
  }

  // Initialize _effectiveItemCtrl before deep calculation.
  // This ensures every item has a starting point for its _effectiveItemCtrl,
  // which is its own base itemCtrl. The recursive functions then update it.
  const initializeAll = list => {
    for (const ft of list) {
      if (ft.foodList) ft.foodList.forEach(fi => initFoodItem(fi))
    }
  }
  const initFoodItem = fi => {
    fi._effectiveItemCtrl = fi.itemCtrl
    if (fi.foodList) fi.foodList.forEach(cfi => initFoodItem(cfi))
    if (fi.mListList) fi.mListList.forEach(cmli => initMListItem(cmli))
    if (fi.foodTypeList)
      fi.foodTypeList.forEach(cfti => {
        if (cfti.foodList) cfti.foodList.forEach(subFi => initFoodItem(subFi))
      })
    if (fi.mTypeList)
      fi.mTypeList.forEach(cmti => {
        if (cmti.mListList) cmti.mListList.forEach(subMli => initMListItem(subMli))
      })
    if (fi.allTypeArry)
      fi.allTypeArry.forEach(cti => {
        if (cti.foodList) cti.foodList.forEach(subFi => initFoodItem(subFi))
        if (cti.mListList) cti.mListList.forEach(subMli => initMListItem(subMli))
      })
  }
  const initMListItem = mli => {
    mli._effectiveItemCtrl = mli.itemCtrl
    if (mli.mListList) mli.mListList.forEach(cmli => initMListItem(cmli))
    if (mli.mTypeList)
      mli.mTypeList.forEach(cmti => {
        if (cmti.mListList) cmti.mListList.forEach(subMli => initMListItem(subMli))
      })
    if (mli.allTypeArry)
      mli.allTypeArry.forEach(cmti => {
        if (cmti.mListList) cmti.mListList.forEach(subMli => initMListItem(subMli))
      })
  }
  const isFoodTypeItem = type => "foodList" in type
  const setItemCtrl = (item, status) => {
    const initItemCtrl = item.itemCtrl && item.itemCtrlSource !== "bubble"
    item.itemCtrl = initItemCtrl || status
    if (status) {
      Vue.set(item, "itemCtrlSource", "bubble")
      itemCtrlSets.add(item.code || item.fCode)
    } else delete item.itemCtrlSource
  }

  if (!isFood && baseList) {
    initializeAll(baseList)
    for (const foodTypeItem of baseList) {
      calculateRecursiveFoodTypeItem(foodTypeItem)
    }
  } else {
    baseList.forEach(foodItem => {
      initFoodItem(foodItem)
      calculateRecursiveFoodItem(foodItem)
    })
  }
  window.itemCtrlList = [...itemCtrlSets]
  return changed
}

// determineDisplayStatus function remains the same as previous versions.
/**
 * Determines if an item should be displayed based on its effective sold-out status
 * and the controlling item_ctrl_model from its nearest typed ancestor.
 * Adds/updates a `_shouldDisplay` property on each FoodItem and mListItem.
 *
 * @param baseList - The root list of FoodTypeItems.
 */
function determineDisplayStatus(baseList) {
  const processItem = (item, currentItemCtrlModel) => {
    // item is FoodItem or mListItem
    if (item._effectiveItemCtrl === undefined) {
      console.warn(
        "Item missing _effectiveItemCtrl, ensure calculateEffectiveItemCtrl is run first:",
        item
      )
      item._effectiveItemCtrl = item.itemCtrl
    }

    if (!item._effectiveItemCtrl) {
      item._shouldDisplay = true
    } else {
      if (currentItemCtrlModel === 0) {
        item._shouldDisplay = true
      } else if (currentItemCtrlModel === 1) {
        item._shouldDisplay = false
      } else {
        console.warn("Missing currentItemCtrlModel for sold out item. Defaulting to show.", item)
        item._shouldDisplay = true
      }
    }

    // Recurse for children, passing down the relevant item_ctrl_model
    if (item.foodList) {
      item.foodList = item.foodList.filter(fi => processItem(fi, currentItemCtrlModel))
    }
    if (item.mListList) {
      item.mListList = item.mListList.filter(mli => processItem(mli, currentItemCtrlModel))
    }
    if (item.foodTypeList) {
      item.foodTypeList.forEach(fti => {
        if (fti.foodList) {
          fti.foodList = fti.foodList.filter(fi => processItem(fi, fti.item_ctrl_model))
        }
      })
    }
    if (item.mTypeList) {
      item.mTypeList.forEach(mti => {
        if (mti.mListList) {
          mti.mListList = mti.mListList.filter(mli => processItem(mli, mti.item_ctrl_model))
        }
      })
    }
    if (item.allTypeArry) {
      item.allTypeArry.forEach(ti => {
        if (ti.mListList) {
          ti.mListList = ti.mListList.filter(mli => processItem(mli, ti.item_ctrl_model))
        }
        if (ti.foodList) {
          ti.foodList = ti.foodList.filter(fi => processItem(fi, ti.item_ctrl_model))
        }
      })
    }
    return item._shouldDisplay
  }

  if (baseList) {
    baseList.forEach(foodTypeItem => {
      if (foodTypeItem.foodList) {
        foodTypeItem.foodList = foodTypeItem.foodList.filter(foodItem =>
          processItem(foodItem, foodTypeItem.item_ctrl_model)
        )
      }
    })
  }
}

/**
 * Main processing function to apply all logic.
 * @param  menuList - The full response object from the API.
 */
function handleMenuItemCtrl(menuList = []) {
  if (!Array.isArray(menuList)) return []

  calculateEffectiveItemCtrl(menuList) // Adds/updates _effectiveItemCtrl
  determineDisplayStatus(menuList) // Adds/updates _shouldDisplay
  // console.log("Processed menu data:", JSON.stringify(baseList, null, 2)); // For debugging
  return menuList
}

/**
 * 收集外层主food下的所有直系固定细项的code/fCode
 * */
function collectNestedDirectCodes(list = []) {
  const nestedCollectedCodes = {}

  if (!Array.isArray(list)) {
    return nestedCollectedCodes
  }
  const collector = (item, codes = []) => {
    const isFood = !!item.fCode
    const listKey = isFood ? "foodList" : "mListList"
    const codeKey = isFood ? "fCode" : "code"
    if (Array.isArray(item[listKey])) {
      item[listKey].forEach(it => {
        codes.push(it[codeKey])
        if (it.foodList || it.mListList) codes.push(...collector(it))
      })
    }

    return codes
  }
  list.forEach(typeItem => {
    if (Array.isArray(typeItem.foodList)) {
      nestedCollectedCodes[typeItem.code] = {}
      typeItem.foodList.forEach(item => {
        nestedCollectedCodes[typeItem.code][item.fCode] = collector(item, [])
      })
    }
  })

  return nestedCollectedCodes
}

/**
 * Traverses the data structure starting from each top-level item in the input list
 * and collects all unique codes and fCodes of its descendants.
 *
 * @returns {Object<string, string[]>} An object where keys are the codes of the top-level items
 * and values are arrays of all unique descendant codes/fCodes.
 */
function findAllAndCollectDescendants(list = []) {
  const resultMap = {}

  /**
   * Recursive helper function to collect all codes/fCodes from an array of items and their descendants.
   * @param {Array<any>} items - The current array of items to process.
   * @param {Set<string>} collectedSet - The Set to add collected codes/fCodes to.
   */
  function collectAllDescendantCodes(items, collectedSet) {
    // Base case: if items is null, not an array, or empty, stop recursion
    if (!items || !Array.isArray(items)) {
      return
    }

    for (const item of items) {
      // Add the current item's code or fCode to the set
      if (item.fCode) {
        collectedSet.add(item.fCode)
      } else if (item.code) {
        collectedSet.add(item.code)
      }

      // Recursively call this function on all potential child lists of the current item
      // FoodItem can have foodList, foodTypeList, mListList, mTypeList
      if (item.foodList && Array.isArray(item.foodList)) {
        collectAllDescendantCodes(item.foodList, collectedSet)
      }
      if (item.foodTypeList && Array.isArray(item.foodTypeList)) {
        // foodTypeList contains FoodTypeItem
        collectAllDescendantCodes(item.foodTypeList, collectedSet)
      }
      // mListItem and mTypeItem can have mListList
      if (item.mListList && Array.isArray(item.mListList)) {
        collectAllDescendantCodes(item.mListList, collectedSet)
      }
      // FoodItem and mListItem can have mTypeList
      if (item.mTypeList && Array.isArray(item.mTypeList)) {
        // mTypeList contains mTypeItem
        collectAllDescendantCodes(item.mTypeList, collectedSet)
      }
    }
  }

  // Iterate through each top-level item in the input list
  if (list && Array.isArray(list)) {
    for (const topLevelItem of list) {
      // Assuming top-level items are FoodTypeItem based on BaseResponse interface
      // The key in the result map is the code of the top-level item.
      if (topLevelItem.code) {
        const descendantCodesSet = new Set()

        // Start collecting descendants from the *children* of the top-level item.
        // For a FoodTypeItem, the direct children are in its foodList.
        collectAllDescendantCodes(topLevelItem.foodList, descendantCodesSet)

        // Convert the collected Set of unique codes/fCodes into an array
        // and assign it as the value for the current top-level item's code.
        resultMap[topLevelItem.code] = Array.from(descendantCodesSet)
      }
    }
  }

  return resultMap
}
