[{"label": "(UTC-12:00) International Date Line West", "value": "-12:00"}, {"label": "(UTC-11:00) Coordinated Universal Time-11", "value": "-11:00"}, {"label": "(UTC-10:00) Aleutian Islands", "value": "-10:00"}, {"label": "(UTC-10:00) Hawaii", "value": "-10:00"}, {"label": "(UTC-09:30) Marquesas Islands", "value": "-09:30"}, {"label": "(UTC-09:00) Alaska", "value": "-09:00"}, {"label": "(UTC-09:00) Coordinated Universal Time-09", "value": "-09:00"}, {"label": "(UTC-08:00) Baja California", "value": "-08:00"}, {"label": "(UTC-08:00) Pacific Time (US & Canada)", "value": "-08:00"}, {"label": "(UTC-07:00) Arizona", "value": "-07:00"}, {"label": "(UTC-07:00) Chihuahua, La Paz, Mazatlan", "value": "-07:00"}, {"label": "(UTC-07:00) Mountain Time (US & Canada)", "value": "-07:00"}, {"label": "(UTC-06:00) Central America", "value": "-06:00"}, {"label": "(UTC-06:00) Central Time (US & Canada)", "value": "-06:00"}, {"label": "(UTC-06:00) Guadalajara, Mexico City, Monterrey", "value": "-06:00"}, {"label": "(UTC-06:00) Saskatchewan", "value": "-06:00"}, {"label": "(UTC-05:00) Bogota, Lima, Quito, Rio Branco", "value": "-05:00"}, {"label": "(UTC-05:00) Eastern Time (US & Canada)", "value": "-05:00"}, {"label": "(UTC-05:00) Indiana (East)", "value": "-05:00"}, {"label": "(UTC-04:30) Caracas", "value": "-04:30"}, {"label": "(UTC-04:00) Asuncion", "value": "-04:00"}, {"label": "(UTC-04:00) Atlantic Time (Canada)", "value": "-04:00"}, {"label": "(UTC-04:00) Cuiaba", "value": "-04:00"}, {"label": "(UTC-04:00) Georgetown, La Paz, Manaus, San Juan", "value": "-04:00"}, {"label": "(UTC-04:00) Santiago", "value": "-04:00"}, {"label": "(UTC-03:30) Newfoundland", "value": "-03:30"}, {"label": "(UTC-03:00) Brasilia", "value": "-03:00"}, {"label": "(UTC-03:00) Buenos Aires", "value": "-03:00"}, {"label": "(UTC-03:00) Cayenne, Fortaleza", "value": "-03:00"}, {"label": "(UTC-03:00) Greenland", "value": "-03:00"}, {"label": "(UTC-03:00) Montevideo", "value": "-03:00"}, {"label": "(UTC-03:00) Salvador", "value": "-03:00"}, {"label": "(UTC-02:00) Coordinated Universal Time-02", "value": "-02:00"}, {"label": "(UTC-02:00) Mid-Atlantic - Old", "value": "-02:00"}, {"label": "(UTC-01:00) Azores", "value": "-01:00"}, {"label": "(UTC-01:00) Cabo Verde Is.", "value": "-01:00"}, {"label": "(UTC) Casablanca", "value": "+00:00"}, {"label": "(UTC) Coordinated Universal Time", "value": "+00:00"}, {"label": "(UTC) Dublin, Edinburgh, Lisbon, London", "value": "+00:00"}, {"label": "(UTC) Monrovia, Reykjavik", "value": "+00:00"}, {"label": "(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna", "value": "+01:00"}, {"label": "(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague", "value": "+01:00"}, {"label": "(UTC+01:00) Brussels, Copenhagen, Madrid, Paris", "value": "+01:00"}, {"label": "(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb", "value": "+01:00"}, {"label": "(UTC+01:00) West Central Africa", "value": "+01:00"}, {"label": "(UTC+01:00) Windhoek", "value": "+01:00"}, {"label": "(UTC+02:00) Amman", "value": "+02:00"}, {"label": "(UTC+02:00) Athens, Bucharest, Istanbul", "value": "+02:00"}, {"label": "(UTC+02:00) Beirut", "value": "+02:00"}, {"label": "(UTC+02:00) Cairo", "value": "+02:00"}, {"label": "(UTC+02:00) Damascus", "value": "+02:00"}, {"label": "(UTC+02:00) Harare, Pretoria", "value": "+02:00"}, {"label": "(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius", "value": "+02:00"}, {"label": "(UTC+02:00) Jerusalem", "value": "+02:00"}, {"label": "(UTC+02:00) Kaliningrad (RTZ 1)", "value": "+02:00"}, {"label": "(UTC+02:00) Tripoli", "value": "+02:00"}, {"label": "(UTC+03:00) Baghdad", "value": "+03:00"}, {"label": "(UTC+03:00) Istanbul", "value": "+03:00"}, {"label": "(UTC+03:00) Kuwait, Riyadh", "value": "+03:00"}, {"label": "(UTC+03:00) Minsk", "value": "+03:00"}, {"label": "(UTC+03:00) Moscow, St. Petersburg, Volgograd (RTZ 2)", "value": "+03:00"}, {"label": "(UTC+03:00) Nairobi", "value": "+03:00"}, {"label": "(UTC+03:30) Tehran", "value": "+03:30"}, {"label": "(UTC+04:00) Abu Dhabi, Muscat", "value": "+04:00"}, {"label": "(UTC+04:00) Baku", "value": "+04:00"}, {"label": "(UTC+04:00) Izhevsk, Samara (RTZ 3)", "value": "+04:00"}, {"label": "(UTC+04:00) Port Louis", "value": "+04:00"}, {"label": "(UTC+04:00) Tbilisi", "value": "+04:00"}, {"label": "(UTC+04:00) Yerevan", "value": "+04:00"}, {"label": "(UTC+04:30) Kabul", "value": "+04:30"}, {"label": "(UTC+05:00) Ashgabat, Tashkent", "value": "+05:00"}, {"label": "(UTC+05:00) Ekaterinburg (RTZ 4)", "value": "+05:00"}, {"label": "(UTC+05:00) Islamabad, Karachi", "value": "+05:00"}, {"label": "(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi", "value": "+05:30"}, {"label": "(UTC+05:30) Sri Jayawardenepura", "value": "+05:30"}, {"label": "(UTC+05:45) Kathmandu", "value": "+05:45"}, {"label": "(UTC+06:00) Astana", "value": "+06:00"}, {"label": "(UTC+06:00) Dhaka", "value": "+06:00"}, {"label": "(UTC+06:00) Novosibirsk (RTZ 5)", "value": "+06:00"}, {"label": "(UTC+06:30) Yangon (Rangoon)", "value": "+06:30"}, {"label": "(UTC+07:00) Bangkok, Hanoi, Jakarta", "value": "+07:00"}, {"label": "(UTC+07:00) Barnaul, Gorno-Altaysk", "value": "+07:00"}, {"label": "(UTC+07:00) Hovd", "value": "+07:00"}, {"label": "(UTC+07:00) Krasnoyarsk (RTZ 6)", "value": "+07:00"}, {"label": "(UTC+07:00) Tomsk", "value": "+07:00"}, {"label": "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi", "value": "+08:00"}, {"label": "(UTC+08:00) Irkutsk (RTZ 7)", "value": "+08:00"}, {"label": "(UTC+08:00) Kuala Lumpur, Singapore", "value": "+08:00"}, {"label": "(UTC+08:00) Perth", "value": "+08:00"}, {"label": "(UTC+08:00) Taipei", "value": "+08:00"}, {"label": "(UTC+08:00) Ulaanbaatar", "value": "+08:00"}, {"label": "(UTC+08:30) Pyongyang", "value": "+08:30"}, {"label": "(UTC+08:45) Eucla", "value": "+08:45"}, {"label": "(UTC+09:00) <PERSON><PERSON>", "value": "+09:00"}, {"label": "(UTC+09:00) Osaka, Sapporo, Tokyo", "value": "+09:00"}, {"label": "(UTC+09:00) Seoul", "value": "+09:00"}, {"label": "(UTC+09:00) Yakutsk (RTZ 8)", "value": "+09:00"}, {"label": "(UTC+09:30) Adelaide", "value": "+09:30"}, {"label": "(UTC+09:30) Darwin", "value": "+09:30"}, {"label": "(UTC+10:00) Brisbane", "value": "+10:00"}, {"label": "(UTC+10:00) Canberra, Melbourne, Sydney", "value": "+10:00"}, {"label": "(UTC+10:00) Guam, Port Moresby", "value": "+10:00"}, {"label": "(UTC+10:00) Hobart", "value": "+10:00"}, {"label": "(UTC+10:00) Vladivo<PERSON>k, Magadan (RTZ 9)", "value": "+10:00"}, {"label": "(UTC+10:30) Lord Howe Island", "value": "+10:30"}, {"label": "(UTC+11:00) Bougainville Island", "value": "+11:00"}, {"label": "(UTC+11:00) Chokurdakh", "value": "+11:00"}, {"label": "(UTC+11:00) Magadan", "value": "+11:00"}, {"label": "(UTC+11:00) Norfolk Island", "value": "+11:00"}, {"label": "(UTC+11:00) Sakhalin", "value": "+11:00"}, {"label": "(UTC+11:00) Solomon Is., New Caledonia", "value": "+11:00"}, {"label": "(UTC+12:00) <PERSON><PERSON><PERSON>, Petropavlovsk-Kamchatsky (RTZ 10)", "value": "+12:00"}, {"label": "(UTC+12:00) Auckland, Wellington", "value": "+12:00"}, {"label": "(UTC+12:00) Coordinated Universal Time+12", "value": "+12:00"}, {"label": "(UTC+12:00) Fiji", "value": "+12:00"}, {"label": "(UTC+12:00) Petropavlovsk-Kamchatsky - Old", "value": "+12:00"}, {"label": "(UTC+12:45) Chatham Islands", "value": "+12:45"}, {"label": "(UTC+13:00) <PERSON><PERSON><PERSON><PERSON><PERSON>a", "value": "+13:00"}, {"label": "(UTC+13:00) Samoa", "value": "+13:00"}, {"label": "(UTC+14:00) Kiritimati Island", "value": "+14:00"}]