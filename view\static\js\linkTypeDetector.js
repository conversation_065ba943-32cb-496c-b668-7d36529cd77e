/**
 * 链接类型检测工具
 * 用于检测URL是否为图片、视频或GIF
 */

// 文件扩展名正则表达式
const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i
const videoExtensions = /\.(mp4|webm|mov|ogg|avi)$/i
const gifExtensions = /\.gif$/i

/**
 * 通过HTTP HEAD请求检查Content-Type
 * @param {string} url - 要检查的URL
 * @returns {Promise<string|null>} - 返回检测到的类型或null
 */
async function checkContentType(url) {
  try {
    // 使用fetch进行HEAD请求
    const response = await fetch(url, {
      method: "HEAD",
      mode: "cors" // 处理跨域
    })

    const contentType = response.headers.get("content-type")
    if (contentType) {
      if (contentType.includes("image/gif")) {
        return "gif"
      } else if (contentType.startsWith("image/")) {
        return "img"
      } else if (contentType.startsWith("video/")) {
        return "video"
      }
    }
  } catch (e) {
    // HEAD请求失败，可能是跨域问题
    console.log("HEAD请求失败:", e.message)
  }
  return null
}

/**
 * 检测URL的媒体类型
 * @param {string} url - 要检测的URL
 * @returns {Promise<string>} - 返回 'gif', 'img', 'video', 或 'unknown'
 */
async function detectType(url) {
  if (!url || typeof url !== "string") {
    return "unknown"
  }

  try {
    // 使用 URL 的 pathname 来判断，可以忽略链接后面的参数
    const urlObj = new URL(url)
    const path = urlObj.pathname

    // 方法1: 文件扩展名识别
    if (gifExtensions.test(path)) {
      return "gif"
    } else if (imageExtensions.test(path)) {
      return "img"
    } else if (videoExtensions.test(path)) {
      return "video"
    }

    // 方法2: 没有明显后缀，通过Content-Type检查
    const contentType = await checkContentType(url)
    if (contentType) {
      return contentType
    }

    // 如果都无法识别，返回unknown
    return "unknown"
  } catch (e) {
    // 如果 URL 格式错误
    console.error("无效的URL:", e, url)
    return "unknown"
  }
}

/**
 * 同步方式检测类型（仅基于文件扩展名）
 * @param {string} url - 要检测的URL
 * @returns {string} - 返回 'gif', 'img', 'video', 或 'unknown'
 */
function detectTypeSync(url) {
  if (!url || typeof url !== "string") {
    return "unknown"
  }

  try {
    const urlObj = new URL(url)
    const path = urlObj.pathname

    if (gifExtensions.test(path)) {
      return "gif"
    } else if (imageExtensions.test(path)) {
      return "img"
    } else if (videoExtensions.test(path)) {
      return "video"
    }

    return "unknown"
  } catch (e) {
    // console.error("无效的URL:", e, url)
    return "unknown"
  }
}

/**
 * 获取类型的显示文本
 * @param {string} type - 类型 ('gif', 'img', 'video', 'unknown')
 * @returns {string} - 显示文本
 */
function getTypeDisplayText(type) {
  const typeMap = {
    gif: "GIF",
    img: "Image",
    video: "Video",
    unknown: "Link"
  }
  return typeMap[type] || "Unknown"
}

/**
 * 获取类型的图标
 * @param {string} type - 类型 ('gif', 'img', 'video', 'unknown')
 * @returns {string} - 图标类名或emoji
 */
function getTypeIcon(type) {
  const iconMap = {
    gif: "🎞️",
    img: "🖼️",
    video: "🎥",
    unknown: "🔗"
  }
  return iconMap[type] || "❓"
}
