@import "./birthdayCard.css";
@import "./registerResetPassword.css";
@import "./rechargeView.css";

/* 会员中心 */
#personalCenterPopup {
  display: none;
  height: 100%;
  height: -webkit-fill-available;
}
.personalCenterWarp {
  display: flex;
  flex-direction: column;
  /* height: 100vh;
    width: 100vw; */
  padding: 1rem 0.8rem;
  box-sizing: border-box;
  overflow: hidden;
  background: linear-gradient(20deg, #f0f6ff 0%, #ffffff 100%);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
@keyframes shake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  50% {
    transform: translateX(5px);
  }
  75% {
    transform: translateX(-3px);
  }
  100% {
    transform: translateX(0);
  }
}

.shake-once {
  animation: shake 0.5s;
}

.user-center-logout {
  position: absolute;
  top: 0.4rem;
  left: 0.4rem;
}
.user-center-close {
  position: absolute;
  top: 0.4rem;
  right: 0.4rem;
}

.user-center-close i {
  font-size: 0.8rem;
}
.user-center-logout img {
  width: 0.7rem;
}
.user-logo {
  text-align: center;
  margin-bottom: 0.2rem;
}
.user-logo img {
  max-height: 4rem;
}
.user-content {
  flex: 1;
  display: flex;
  /* align-items: center; */
}
.user-fade {
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease-out;
}
.user-fade.show {
  visibility: visible;
  opacity: 1;
  flex: 1;
}

.user-footer-btn {
  font-size: 0.38rem;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  padding: 0.27rem 0;
  width: 100%;
  background-color: #409eff;
  border-radius: 0.18rem;
  margin: 0 auto;
  color: #fff;
}
.layui-customPanel-form,
.searchFoodDia-form,
.user-login .user-login-Form .layui-form-item {
  display: flex;
}
.layui-customPanel-label,
.searchFoodDia-label {
  width: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border-width: 0.1px;
  border-style: solid;
  border-radius: 2px 0 0 2px;
  box-sizing: border-box;
  border: 0.1px solid #eee;
  border-right: none;
}
.layui-customPanel-label-icon,
.searchFoodDia-icon {
  font-size: 0.5rem !important;
  color: var(--styleColor);
}
.layui-customPanel-label-input,
.searchFoodDia-inputBox {
  flex: 1;
}

.user-account {
  background: #fff;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  max-width: 90vw;
  margin: 0 auto;
}

.user-account-title {
  text-align: center;
  position: relative;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ebeef5;
  font-size: 0.35rem;
}

.user-account-title i {
  font-size: 32px;
  color: #409eff;
  display: block;
  margin-bottom: 10px;
}

.user-account-header {
  position: relative;
  margin-top: 15px;
  padding: 15px 8px;
  background: #f7f8fa;
  border-radius: 8px;
  word-break: keep-all;
}

.user-account-header p {
  color: #606266;
  font-size: 0.35rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-account-header i {
  color: #409eff;
}

.user-account-list {
  margin: 0.2rem 0;
  padding: 0.2rem;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.user-account-num {
  font-weight: 500;
  color: #303133;
  font-size: 0.35rem;
}

.user-account-point {
  color: #67c23a;
  font-weight: 500;
  font-size: 0.35rem;
}

.select-account-btnWarp {
  display: flex;
  align-items: center;
  justify-content: end;
}
.select-account-btn {
  width: 1.8rem;
  padding: 0.15rem 0;
  background: #409eff;
  border-radius: 0.1rem;
  color: #eee;
  text-align: center;
}
.select-account-btn i {
  margin-right: 4px;
}

.user-account-content div p {
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-account-header p:nth-child(1),
.user-account-content .user-account-list .user-account-num {
  justify-content: left;
}

.user-card {
  padding: 0.4rem 0.5rem;
  margin: 0.3rem auto;
  max-width: 92vw;
  position: relative;
}

.user-card-header {
  margin-bottom: 0.4rem;
  padding-bottom: 0.2rem;
  position: relative;
  border-bottom: 1px solid #edf1f7;
  display: flex;
  align-items: center;
}
.user-card-name {
  font-size: 0.56rem;
  font-weight: 600;
  letter-spacing: 0.3px;
  color: #2c3e50;
}
.user-card-name::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 1.2rem;
  height: 0.06rem;
  background: linear-gradient(to right, #409eff, rgba(64, 158, 255, 0.3));
  border-radius: 0.1rem;
}
.user-recharge-btn {
  padding: 0.15rem 0.35rem;
  background: #409eff;
  border-radius: 0.1rem;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-left: auto;
  white-space: nowrap;
}
.user-recharge-btn[disabled] {
  background-color: #ccc;
  cursor: not-allowed;
}
.recharge-btn-img {
  width: 0.35rem;
  margin-right: 0.15rem;
  object-fit: contain;
}
.user-card-content {
  position: relative;
  max-height: 60vh;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.user-card-content::-webkit-scrollbar {
  display: none;
}

.user-card-cell {
  padding: 1vh 0;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ebedf0;
  margin-bottom: 0.1rem;
  font-size: 0.355rem;
}

.user-card-cell > .card-cell-value:empty::before {
  /*–  –*/
  content: "–";
  min-width: 40px;
  display: flex;
  justify-content: center;
}

.user-card-cell:not(:last-child) {
  border-bottom: 1px solid #f0f3f7;
}

.card-cell-label {
  display: flex;
  align-items: center;
  color: #5a6577;
}

.card-cell-labelImg {
  width: 0.38rem;
  height: 0.38rem;
  margin-right: 0.15rem;
  object-fit: contain;
}
.card-cell-value {
  color: #2c3e50;
  font-weight: 500;
}
button.card-cell-value[disabled] {
  color: #cccdcf;
}
.user-footer {
  margin-top: 0.4rem;
  padding: 0 0.15rem;
}
.user-footer-btn {
  font-size: 0.38rem;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  padding: 0.27rem 0;
  width: 100%;
  background-color: #409eff;
  border-radius: 0.18rem;
  margin: 0 auto;
  color: #fff;
}

.bottom-tips {
  padding: 0.3rem;
  text-align: center;
  color: #c7c7c7;
  font-size: 0.34rem;
}

.grid3 {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) minmax(0, 1fr);
}
.payAtCashierLayer .layui-layer-btn .layui-layer-btn1 {
  border-color: #1e9fff;
  background-color: #1e9fff;
  color: #fff;
}
.payAtCashierLayer .layui-layer-btn .layui-layer-btn0 {
  border-color: #ff5721;
  background-color: #ff5721;
  color: #fff;
}

/* 生日卡片 */

.happy-border {
  background-color: white;
  height: 850px;
  width: 550px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 8px solid #72705bca;
  box-shadow: 50px 20px 10px rgb(213, 207, 207);
}

.user-footer-btn-loggedIn {
  font-size: 0.38rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  background-color: #409eff;
  border-radius: 0.18rem;
  margin: 0 auto;
  color: #fff;
  min-height: 42px;
}
.user-footer-btn-notLoggedIn {
  font-size: 0.3rem;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  /* padding: 0.27rem 0; */
  width: 1.5rem;
  align-items: center;
  background-color: #409eff;
  border-radius: 0.1rem;
  margin: 0 auto;
  color: #fff;
  margin-left: 0.2rem;
}

/* 新加 */
.user-login-content {
  text-align: center;
}
.user-login-content1 {
  font-size: 0.75rem;
}
.user-login-content2 {
  padding-top: 0.2rem;
  font-size: 0.4rem;
}
.login-form-container {
  display: flex;
  gap: 10px;
  margin-bottom: 2vh;
}
.input-group {
  flex: 1;
}

.user-logo img {
  max-height: 3rem;
  margin-bottom: 0.3rem;
}

.user-card-content {
  max-height: 6rem;
  overflow-y: auto;
}
.no-logo {
  margin-top: 2rem;
}
/* 针对没有 logo 时的额外样式 */
/* .no-logo .user-account {
  margin-top: 5vh;
}

.no-logo .user-account-title {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-logo .user-account-title::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #409eff;
  border-radius: 2px;
} */
.member-warp .bottom-links {
  display: flex;
  justify-content: space-between;
}
.form-actions {
  margin-top: 1rem;
}
@media screen and (min-width: 320px) and (max-width: 375px) {
}
