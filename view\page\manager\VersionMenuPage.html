<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>

    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/moment/moment.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>
    <script src="../../static/js/qrcode.js"></script>
    <script src="../../static/js/jszip.min.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>
    <style>
      html,
      body {
        padding: 0;
        margin: 0;
      }

      #app {
        padding: 20px 15px;
      }

      .header-box {
        margin: 10px 0;
        float: right;
        /* width: 400px; */
      }

      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .tip-box {
        display: flex;
        /* padding: 0 20px; */
        justify-content: space-between;
      }

      .header-box .el-alert__content {
        padding: 0;
      }
      .tip-box-cell {
        display: flex;
        align-items: center;
        margin-right: 10px;
      }
      .tip-box-cell-division {
        margin: 0 5px;
      }
      /* 最后一个取消margin-right */
      .tip-box-cell:last-child {
        margin-right: 0;
      }
      .tip-box-cell-txt {
        font-weight: 600;
      }

      .el-date-editor.el-input {
        width: 100%;
      }

      .el-dialog__body {
        padding-bottom: 0;
      }

      .deployPopconfirm {
        margin: 0 10px;
      }
      .delPopconfirm {
        margin-left: 10px;
      }
      .synchronizationIcon {
        vertical-align: top;
        cursor: pointer;
      }
      #shortQrcode div,
      #longQrcode div {
        display: flex;
        max-height: 200px;
        overflow-y: auto;
        flex-wrap: wrap;
      }
      #shortQrcode img,
      #longQrcode img {
        margin-right: 20px;
        margin-bottom: 20px;
      }
      .dia-footer {
        position: relative;
        z-index: 2;
      }
      .el-form {
        max-height: 60vh;
        overflow: auto;
        overflow-x: hidden;
      }
      input[aria-hidden="true"] {
        display: none !important;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <div class="header-box" ref="headerBox">
          <el-alert type="info" :closable="false">
            <template slot="title">
              <div class="tip-box">
                <div class="tip-box-cell">
                  <img src="../../static/img/svg/synchronization.svg" class="synchronizationIcon" />
                  <span class="tip-box-cell-division">:</span>
                  <span class="tip-box-cell-txt">PROD ⇋ UAT</span>
                </div>
                <div class="tip-box-cell">
                  <el-button size="mini" type="warning" icon="el-icon-view" circle></el-button>
                  <span class="tip-box-cell-division">:</span>
                  <span class="tip-box-cell-txt">Preview BYOD</span>
                </div>
                <div class="tip-box-cell">
                  <el-button
                    size="mini"
                    type="success"
                    icon="el-icon-s-promotion"
                    circle
                  ></el-button>
                  <span class="tip-box-cell-division">:</span>
                  <span class="tip-box-cell-txt">Deployed Version</span>
                </div>
              </div>
            </template>
          </el-alert>
        </div>
        <template>
          <el-table
            v-if="tableHeight"
            :data="tableData"
            style="width: 100%"
            :max-height="tableHeight"
            border
            id="exportTable"
            ref="report-table"
            empty-text="No Data"
            class="tableContent"
          >
            <el-table-column
              :fixed="item.fixed"
              :prop="item.prop"
              :label="item.label"
              :width="item.width"
              v-for="(item,index) in tableColumn"
              align="center"
            >
              <template slot-scope="scope">
                <span v-if="item.prop=='taskCode'">
                  <el-tag :type="taskCodeTag(scope.row.taskCode)">
                    {{scope.row.taskCode|taskCodeFilter}}
                  </el-tag>
                </span>

                <span v-else-if="item.prop=='versionBackupTime'">
                  <span>{{formatVersionBT(scope.row.versionBackupTime)}}</span>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="PROD data is synchronized with UAT data"
                    placement="top"
                    v-if="scope.row.versionBackupTime===0"
                  >
                    <span>
                      <spn>|</spn>
                      <el-popconfirm
                        confirm-button-text="Confirm"
                        cancel-button-text="Cancel"
                        title="Whether to synchronize PROD data to UAT?"
                        @confirm="onSynchronization(scope.$index, scope.row)"
                      >
                        <img
                          src="../../static/img/svg/synchronization.svg"
                          alt=""
                          slot="reference"
                          class="synchronizationIcon"
                        />
                      </el-popconfirm>
                    </span>
                  </el-tooltip>
                </span>
                <span v-else-if="item.prop=='timeOfAppointment'">
                  {{scope.row.timeOfAppointment|formatVersionTOA}}
                </span>
                <span v-else-if="item.prop=='deployByVersion'">
                  <span>{{formatVersionBT(scope.row.deployByVersion)}}</span>
                </span>
                <span v-else>{{scope.row[item.prop]}}</span>
              </template>
            </el-table-column>
            <el-table-column width="200" align="center">
              <template slot="header">Operation</template>
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="warning"
                  icon="el-icon-view"
                  circle
                  @click="showRedirectDia(scope.$index, scope.row)"
                ></el-button>
                <el-popconfirm
                  confirm-button-text="Confirm"
                  cancel-button-text="Cancel"
                  title="Whether to deploy this version?"
                  class="deployPopconfirm"
                  @confirm="onDeployVersion(scope.$index, scope.row)"
                >
                  <el-button
                    size="small"
                    type="success"
                    icon="el-icon-s-promotion"
                    circle
                    :disabled="isProdVersion(scope.row)"
                    slot="reference"
                  ></el-button>
                </el-popconfirm>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                  :disabled="isProdVersion(scope.row)"
                ></el-button>
                <el-popconfirm
                  confirm-button-text="Confirm"
                  cancel-button-text="Cancel"
                  title="Are you sure to delete this data?"
                  class="delPopconfirm"
                  @confirm="onDel(scope.$index, scope.row)"
                >
                  <el-button
                    size="small"
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    slot="reference"
                    :disabled="scope.row.versionBackupTime===0||isProdVersion(scope.row)"
                  ></el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 单元格编辑弹窗 -->
        <template>
          <el-dialog
            title="Edit Dialog"
            :visible.sync="editDialogVisible"
            @close="onDiaCloseDialog('editForm')"
          >
            <el-form :model="editForm" ref="editForm" label-width="auto" :rules="formRules">
              <el-form-item label="Version Backup Time">
                <el-input v-model="editVersionBackupTime" disabled></el-input>
              </el-form-item>
              <el-form-item label="Time Of Appointment" prop="timeOfAppointment">
                <el-date-picker
                  v-model="editForm.timeOfAppointment"
                  type="datetime"
                  :picker-options="expireTimeOPtion"
                  value-format="timestamp"
                  placeholder=""
                ></el-date-picker>
              </el-form-item>
            </el-form>
            <div slot="footer">
              <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
              <el-button @click="handleConfirm('editForm')">Submit</el-button>
            </div>
          </el-dialog>
        </template>
        <!-- 填写预览BYOD弹窗 -->
        <template>
          <el-dialog
            title="Redirect Dialog"
            :visible.sync="fillInDialogVisible"
            @open="onDiaOpenDialog"
            @close="onDiaCloseDialog('redirectForm')"
            :width="redirectDialogWidth"
          >
            <el-form
              :model="redirectForm"
              ref="redirectForm"
              :rules="redirectRules"
              label-width="130px"
              class="redirectForm"
            >
              <el-form-item label="Prefix Link" prop="prefixLink">
                <el-input v-model="redirectForm.prefixLink" disabled></el-input>
              </el-form-item>
              <el-form-item label="Type" prop="type">
                <el-radio-group v-model="redirectForm.type" size="medium">
                  <el-radio-button label="short link"></el-radio-button>
                  <el-radio-button label="long link"></el-radio-button>
                </el-radio-group>
                <el-button
                  type="success"
                  style="margin-left: 10px"
                  size="medium"
                  @click="handleCodeAndSub('qrcode')"
                >
                  Generate QR code
                </el-button>
                <el-button
                  type="danger"
                  size="medium"
                  @click="clearQRcode"
                  :disabled="allowsClearAndDownload"
                >
                  Clear the QR code
                </el-button>
                <el-button
                  type="warning"
                  size="medium"
                  @click="downloadQRcode"
                  :disabled="allowsClearAndDownload"
                >
                  Download QR code
                </el-button>
              </el-form-item>
              <!-- 使用 computed 属性控制显示/隐藏 -->
              <el-form-item
                v-show="isLongLink"
                prop="suffixLink"
                label="Suffix Link"
                key="suffixLink001"
              >
                <el-input
                  type="textarea"
                  :rows="textareaRows"
                  v-model="redirectForm.suffixLink"
                  placeholder="Please enter parameters(Use ; to separate multiple link)"
                ></el-input>
              </el-form-item>
              <el-form-item v-show="isLongLink" prop="" label="QRCode" key="longQrcode">
                <div id="longQrcode"></div>
              </el-form-item>

              <el-form-item
                v-show="!isLongLink"
                prop="storeNumber"
                label="Store Number"
                key="storeNumber001"
              >
                <el-input
                  v-model="redirectForm.storeNumber"
                  placeholder="Please enter store number"
                ></el-input>
              </el-form-item>
              <el-form-item
                v-show="!isLongLink"
                prop="tableNumber"
                label="Table Number"
                key="tableNumber001"
              >
                <el-input
                  v-model="redirectForm.tableNumber"
                  placeholder="Please enter table number(Use ; to separate multiple table number)"
                ></el-input>
              </el-form-item>
              <el-form-item
                v-show="!isLongLink"
                prop="salesmode"
                label="Sales Mode"
                key="salesmode"
              >
                <el-radio v-model="redirectForm.salesmode" label="md">MD</el-radio>
                <el-radio v-model="redirectForm.salesmode" label="fb">FB</el-radio>
              </el-form-item>
              <el-form-item v-show="!isLongLink" prop="" label="QRCode" key="shortQrcode">
                <div id="shortQrcode"></div>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dia-footer">
              <el-button type="primary" @click="fillInDialogVisible=false">Cancel</el-button>
              <el-button :disabled="confirmDisabled" @click="handleCodeAndSub('submit')">
                Confirm
              </el-button>
            </div>
          </el-dialog>
        </template>
      </template>
    </div>
    <script>
      var validatePass = (rule, value, callback) => {
        if (!value) {
          callback(new Error("Please select the date"))
        } else {
          callback()
        }
      }
      var app = new Vue({
        el: "#app",
        data: {
          expireTimeOPtion: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7 //如果没有后面的-8.64e7就是不可以选择今天的
            },
            selectableRange: "00:00:00 - 23:59:59"
          },
          tableHeight: 0,
          domain: sessionStorage.getItem("domain"),
          editDialogVisible: false,
          fillInDialogVisible: false,
          editForm: {
            versionBackupTime: "",
            timeOfAppointment: null
          },
          redirectForm: {
            type: "short link",
            versionNumber: "",
            prefixLink: "", // 前缀
            suffixLink: "", // 后缀
            storeNumber: "",
            tableNumber: "",
            salesmode: "md"
          },

          tableColumn: [
            {
              label: "Version Backup Time",
              prop: "versionBackupTime",
              width: "",
              fixed: true
            },
            {
              label: "Time Of Appointment",
              prop: "timeOfAppointment",
              width: "",
              fixed: false
            },
            {
              label: "Deploy By Version",
              prop: "deployByVersion",
              width: "",
              fixed: false
            },
            {
              label: "Appointment Status",
              prop: "taskCode",
              width: "180",
              fixed: false
            }
          ],

          formRules: {
            // timeOfAppointment: [{ required: true, validator: validatePass, trigger: "change" }]
          },
          redirectRules: {
            suffixLink: [
              { required: false, message: "Please add the suffix link", trigger: "blur" }
            ],
            storeNumber: [
              { required: false, message: "Please add the store number", trigger: "blur" }
            ],
            tableNumber: [
              { required: false, message: "Please add the table number", trigger: "blur" }
            ]
          },

          tableData: [
            // {
            //   versionBackupTime: 0,
            //   timeOfAppointment: 1675308477020
            // },
            // {
            //   versionBackupTime: 1675308477020,
            //   timeOfAppointment: null
            // },
            // {
            //   versionBackupTime: 1675308477030,
            //   timeOfAppointment: 1675308477020
            // },
            // {
            //   versionBackupTime: 1675308477040,
            //   timeOfAppointment: null
            // }
          ],
          editVersionBackupTime: "", // 用作单独显示格式化后的版本日期
          qrcode: null,
          shortQrcodeNameArr: [],
          longQrcodeNameArr: [],
          textareaRows: 3,
          redirectDialogWidth: "70%"
        },
        created() {
          this.getData()
          let windowWidth = document.documentElement.clientWidth || document.body.clientWidth
          this.redirectDialogWidth = windowWidth < 1300 ? "90%" : "70%"
        },
        mounted() {
          // 页面加载的时候设置table的高度
          this.tableHeight = this.getTableHeight(this, 0, "headerBox")
          // this.tableHeight = 700
          // 页面大小该变的时候（缩放页面）设置table的高度（可加可不加）
          // window.onresize = () => {
          //   this.tableHeight = this.getTableHeight(this, 40, 'headerBox')
          //   console.log(this.tableHeight, '触发后')
          // }
        },
        computed: {
          confirmDisabled() {
            let { tableNumber, suffixLink } = this.redirectForm
            if (this.isLongLink) {
              return !(suffixLink.indexOf(";") == -1)
            } else {
              return !(tableNumber.indexOf(";") == -1)
            }
          },
          isLongLink() {
            return this.redirectForm.type === "long link"
          },
          allowsClearAndDownload() {
            if (this.isLongLink) {
              return this.longQrcodeNameArr.length === 0
            } else {
              return this.shortQrcodeNameArr.length === 0
            }
          }
        },
        watch: {
          // 监听时间选择器的值，如果选择的时间小于当前时间则赋值当前时间
          "editForm.timeOfAppointment": {
            handler(newValue, oldValue) {
              if (newValue) {
                let date = new Date() // 当前时间
                let min = date.getMinutes()
                date.setMinutes(min) // 设置最小时间，当前时间几分钟后(min+几)
                let nowDate = moment(date).format("HH:mm:ss") // 当前几分钟后的时间
                let st = ""
                if (moment(date).format("yyyy-MM-DD") === moment(newValue).format("yyyy-MM-DD")) {
                  let hh1 = moment(newValue).format("HH:mm:ss") // 选择时间的时分秒
                  // 判断当前选中的时间是不是小于几分钟后的时间，小于则赋值几分钟后的时间
                  if (hh1 < nowDate) {
                    this.editForm.timeOfAppointment = date
                  }
                  st = nowDate
                } else {
                  st = "00:00:00" 
                }
                this.expireTimeOPtion.selectableRange = st + " - 23:59:59"
                this.expireTimeOPtion = this.expireTimeOPtion
              }
            },
            deep: true,
            immediate: true
          },
          "redirectForm.salesmode": {
            handler(newValue, oldValue) {
              // 在这里处理 salesmode 变化时的逻辑
              this.clearQRcode()
            }
          },

          // 监听 `isLongLink`，动态更新验证规则
          isLongLink: {
            handler(newVal, oldVal) {
              if (newVal) {
                // 长链接时，设置 suffixLink 必填
                this.redirectRules.suffixLink[0].required = true
                this.redirectRules.storeNumber[0].required = false
                this.redirectRules.tableNumber[0].required = false
                // 手动触发验证规则的更新
                this.$refs.redirectForm.clearValidate()
              } else {
                // 非长链接时，设置 storeNumber 和 tableNumber 必填
                this.redirectRules.suffixLink[0].required = false
                this.redirectRules.storeNumber[0].required = true
                this.redirectRules.tableNumber[0].required = true
              }
            },
            deep: true,
            immediate: true
          }
        },
        filters: {
          // 转化null数据
          formatVersionTOA(value) {
            if (value) {
              let { value: offset } = JSON.parse(sessionStorage.getItem("localTimeZone"))
              return moment(value).utc().utcOffset(offset).format("YYYY-MM-DD HH:mm:ss")
            } else {
              // 可能为null或者undefined
              return ""
            }
          },
          taskCodeFilter(value) {
            let obj = {
              0: "No Reservation",
              1: "Reserved",
              200: "Run Successfully",
              500: "Run Failed"
            }
            return obj[value]
          }
        },

        methods: {
          getData() {
            const obj = {
              url: "../../manager_historyVersion/getAll",
              errorMsg: "Request failed, please try again"
            }
            this.utilAjax(obj).then(res => {
              this.tableData = [
                {
                  deployByVersion: null,
                  taskCode: 0,
                  timeOfAppointment: null,
                  versionBackupTime: "PROD"
                },
                ...res.data
              ]
              console.log(JSON.parse(JSON.stringify(res.data)), "res.data")
            })
          },

          // 过滤假值
          fillerVal(tetsObj) {
            if (!tetsObj) return {}
            let filterPam = {}
            for (let i in tetsObj) {
              if (tetsObj[i]) {
                filterPam[i] = tetsObj[i]
              }
            }
            return filterPam
          },

          onEdit(index, row) {
            // 单独显示版本号字段时间格式化后的数据(不影响原数据,编辑提交需要原数据)
            this.editVersionBackupTime = this.formatVersionBT(row.versionBackupTime)
            this.editForm = { ...row }
            console.log("🚀 ~ file: index.html:310 ~ onEdit ~  this.editForm ", this.editForm)
            this.editDialogVisible = true
          },

          handleConfirm(typeForm) {
            this.$refs[typeForm].validate(valid => {
              if (valid) {
                // 把timeOfAppointment转为时间戳格式
                let { timeOfAppointment, versionBackupTime } = this.editForm
                // 判断timeOfAppointment是否是中国标准时间，如果是则转为时间戳格式
                if (moment(timeOfAppointment).format("ZZ") == "+0800") {
                  timeOfAppointment = moment(timeOfAppointment).valueOf() // 转为时间戳格式
                }
                let requestData = {
                  versionBackupTime,
                  timeOfAppointment,
                  domain: this.domain
                }
                let obj = {
                  url: "../../manager_historyVersion/updateOne",
                  type: "post",
                  errorMsg: "Request failed, please try again",
                  data: requestData,
                  successMsg: "Edit success！",
                  errorMsg: "Fail to edit, please try again"
                }
                console.log(JSON.parse(JSON.stringify(requestData)), "提交")
                this.utilAjax(obj)
                  .then(res => {
                    this.editDialogVisible = false
                  })
                  .finally(() => {
                    // 无论成功失败都会执行
                    this.getData()
                  })
              }
            })
          },
          onDel(index, row) {
            let { versionBackupTime } = row
            let requestData = { versionBackupTime }
            let obj = {
              url: "../../manager_historyVersion/deleteOneBackup",
              type: "post",
              data: requestData,
              successMsg: "Successfully delete!",
              errorMsg: "Fail to delete!"
            }
            this.utilAjax(obj).then(res => {
              this.editDialogVisible = false
              this.getData()
              // 调用父组件的方法
              window.parent.getHistoryVersion(this.domain, "versionMenuPage") // 调取ifm父页面触发数据更新
            })
          },
          showRedirectDia(index, row) {
            // 获取当前version
            this.redirectForm.versionNumber = row.versionBackupTime
            // 获取当前页面的url
            let currentUrl = window.location.href
            // let currentUrl = "http://192.168.1.68:1700/127/page/manager/index.html#"
            // 窃取url中的page前面的部分
            this.redirectForm.prefixLink = currentUrl.split("page")[0] + "?"
            // console.log(
            //   "🚀 ~ file: index.html:389 ~ showRedirectDia ~ this.redirectForm.prefixLink",
            //   this.redirectForm.prefixLink
            // )
            this.fillInDialogVisible = true
          },
          handleCodeAndSub(clickType) {
            // 拼接redirectForm前缀后缀新起标签页打开
            this.$refs["redirectForm"].validate(valid => {
              if (valid) {
                let redirectArr = this.getRedirectArr()
                if (clickType === "qrcode") {
                  this.generateQRcode(redirectArr)
                } else {
                  this.fillInDialogVisible = false
                  setTimeout(() => {
                    window.open(redirectArr[0].url)
                  }, 200)
                }
              } else {
                console.log("Validation failed")
              }
            })
          },
          // 部署当前版本
          onDeployVersion(index, row) {
            let versionBackupTime = row.versionBackupTime
            let obj = {
              url: "../../manager_historyVersion/UpdateProduction",
              loadingMsg: "The current version is being deployed, please wait...", // 是否显示loading
              type: "post",
              data: { versionBackupTime, domain: this.domain },
              successMsg: "Successful deployment！",
              errorMsg: "Deployment failed, please try again"
            }
            this.utilAjax(obj)
              .then(res => {
                this.editDialogVisible = false
                // 调用父组件的方法
                window.parent.getHistoryVersion(this.domain, "versionMenuPage") // 调取ifm父页面触发数据更新
              })
              .finally(() => {
                // 无论成功失败都会执行
                this.getData()
              })
            console.log(versionBackupTime, "上新")
          },
          // PROD数据与UAT同步
          onSynchronization() {
            let obj = {
              url: "../../manager_historyVersion/updateInfo",
              type: "post",
              data: { domain: this.domain },
              loadingMsg: "Synchronizing data, please wait.....", // 是否显示loading
              successMsg: "Synchronization successful！", // 成功提示信息
              errorMsg: "Synchronization failed, please try again" // 失败提示信息
            }
            this.utilAjax(obj).then(res => {
              // this.editDialogVisible = false
            })
          },
          //  重置数据
          onDiaOpenDialog() {
            // this.redirectForm.storeNumber = "HK006"
            // this.redirectForm.tableNumber = "1A;test;1A;test;1A;test;1A;test;1A;test;1A;test;"
            // this.redirectForm.suffixLink = ""
            // this.redirectForm.salesmode = "md"
            // this.redirectForm.type = "short link"
          },
          onDiaCloseDialog(form) {
            //  清空校验
            this.$refs[form].resetFields()
            if (form == "redirectForm") {
              this.$refs.redirectForm.clearValidate()
            }
            if (document.getElementById("shortQrcode")) {
              document.getElementById("shortQrcode").innerHTML = ""
            }
            if (document.getElementById("longQrcode")) {
              document.getElementById("longQrcode").innerHTML = ""
            }
            this.textareaRows = 3
            this.shortQrcodeNameArr = []
            this.longQrcodeNameArr = []
          },
          // taskCodeTag
          taskCodeTag(val) {
            let obj = {
              0: "warning",
              1: "primary",
              200: "success",
              500: "danger"
            }
            return obj[val]
          },

          // 转化日期格式
          formatVersionBT(data) {
            // 转化versionBackupTime日期格式
            if (data === 0) {
              return "UAT"
            } else if (data === "PROD") {
              return "PROD"
            } else {
              let { value } = JSON.parse(sessionStorage.getItem("localTimeZone"))
              let formatDate = moment(data).utc().utcOffset(value).format("YYYY-MM-DD HH:mm:ss")
              if (formatDate !== "Invalid date") {
                return formatDate
              } else {
                return ""
              }
            }
          },
          /**
           * table的max-height高度
           * @param that，解决this指向问题
           * @param val，固定的站位高度（例如：分页高度）
           * @param name，动态站位的高度获取的name（例如：查询动态的查询条件）
           * @returns {number}，返回可用的高度
           * @param fixHeight,最外层样式高度
           */
          getTableHeight(that, val = 32, name) {
            let fixHeight = 40 // padding为20,20
            let searchFormHeight = that.$refs[name].clientHeight // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - 80) // 计算完之后剩余table可用的高度
            return tableHeight
          },
          // 二次封装jquery的ajax
          utilAjax(obj) {
            return new Promise((resolve, reject) => {
              let loadingInstance = null
              if (obj.loadingMsg) {
                loadingInstance = this.$message({
                  showClose: false,
                  message: obj.loadingMsg,
                  type: "warning",
                  duration: 0
                })
              }
              $.ajax({
                url: obj.url || "/interface",
                data: obj.data || {},
                dataType: obj.dataType || "JSON",
                type: obj.type || "GET",
                // contentType: obj.contentType || 'application/json;charset=UTF-8',
                success: res => {
                  if (loadingInstance) loadingInstance.close()
                  if (res.statusCode != 200) {
                    this.$message.error(obj.errorMsg)
                    reject(res)
                  } else {
                    if (obj.successMsg) this.$message.success(obj.successMsg)
                    resolve(res)
                  }
                },
                error: err => {
                  if (loadingInstance) loadingInstance.close()
                  this.$message.error(obj.errorMsg)
                  reject(err)
                },
                complete: () => {}
              })
            })
          },
          getRedirectArr() {
            const {
              tableNumber,
              storeNumber,
              prefixLink,
              type,
              suffixLink,
              versionNumber,
              salesmode
            } = this.redirectForm
            let redirectArr = []
            let isProdVersion = versionNumber === "PROD"
            // 短link 需要加密
            if (type == "short link") {
              // 加密storeNumber,tableNumber,versionNumber为base64
              const tableNumberArr =
                tableNumber.indexOf(";") != -1
                  ? tableNumber.split(";").filter(num => num.trim() !== "")
                  : [tableNumber]
              try {
                tableNumberArr.forEach(tableNumber => {
                  let baseLink = `storeNumber=${storeNumber}&salesmode=${salesmode}&tableNumber=${tableNumber}`
                  let versionStr = `&versionNumber=${versionNumber}`
                  let reEncryptSuffixLink = isProdVersion
                    ? window.btoa(baseLink)
                    : window.btoa(baseLink + versionStr)
                  redirectArr.push({
                    url: prefixLink + reEncryptSuffixLink,
                    fileName: `${storeNumber}_${tableNumber}`
                  })
                })
              } catch (error) {
                this.$message.error("Please enter the correct suffixLink")
                return
              }
            } else {
              const urlDistinguish = urlParamsStr => {
                //辨別url後面參數是否加密
                let verifyList = ["&", "tn", "shop"]
                return verifyList.every(el => urlParamsStr.search(el) !== -1)
              }
              const getTnAndShop = everyLink => {
                let result = {}
                let params = new URLSearchParams(everyLink)
                let tn = params.get("tn")
                let shop = params.get("shop")
                // 如果存在 `tn` 和 `shop`，则将其存储在解析对象中
                if (tn && shop) {
                  result = {
                    tn,
                    shop
                  }
                }
                return result
              }
              let isLongLink = urlDistinguish(suffixLink)
              if (isLongLink) {
                let everyLink = suffixLink.split(";").filter(link => link.trim() !== "")
                everyLink.forEach(link => {
                  let tnAndShopObj = getTnAndShop(link)
                  //明文拼接versionNumber在&data前面
                  let spliceSuffixLink = isProdVersion
                    ? link
                    : link.replace(/&data/, "&versionNumber=" + versionNumber + "&data")
                  redirectArr.push({
                    url: prefixLink + spliceSuffixLink,
                    fileName: `${tnAndShopObj.shop}_${tnAndShopObj.tn}`
                  })
                })
              } else {
                this.$message.error("Please provide a valid suffix link")
              }
            }
            return redirectArr
          },
          getCurrentQRcodeID() {
            return this.redirectForm.type == "short link" ? "shortQrcode" : "longQrcode"
          },
          generateQRcode(redirectArr) {
            if (redirectArr.length === 0) return
            this.clearQRcode()
            let id = this.getCurrentQRcodeID()
            let qrWarp = document.getElementById(id)
            let qrContainer = document.createElement("div")
            this[`${id}NameArr`] = []
            console.log("🚀 ~ generateQRcode ~ id:", redirectArr)

            redirectArr.forEach(redirectItem => {
              new QRCode(qrContainer, {
                text: redirectItem.url,
                width: id == "shortQrcode" ? 128 : 180,
                height: id == "shortQrcode" ? 128 : 180,
                // colorDark: "#000000",
                // colorLight: "#ffffff",
                correctLevel: 3 //QRCode.CorrectLevel.H
              })
              qrWarp.appendChild(qrContainer)
              // 将生成的二维码和对应的文件名存储起来
              this[`${id}NameArr`].push({
                element: qrContainer.querySelector("canvas"),
                fileName: `${redirectItem.fileName}.png`
              })
            })
          },
          clearQRcode() {
            let id = this.getCurrentQRcodeID()
            if (document.getElementById(id)) {
              document.getElementById(id).innerHTML = ""
            }
            this[`${id}NameArr`] = []
          },
          // 下载二维码
          downloadQRcode() {
            let id = this.getCurrentQRcodeID()
            let qrArr = this[`${id}NameArr`]
            if (qrArr.length) {
              console.log("🚀 ~ downloadQRcode ~ qrArr:", qrArr)
              let zip = new JSZip()
              let imgFolder = zip.folder("qrcodes")
              qrArr.forEach(item => {
                let canvas = item.element
                let dataURL = canvas.toDataURL("image/png")
                let base64Data = dataURL.split(",")[1]
                imgFolder.file(item.fileName, base64Data, { base64: true })
              })
              zip.generateAsync({ type: "blob" }).then(function (content) {
                let a = document.createElement("a")
                a.href = URL.createObjectURL(content)
                a.download = `${id}.zip`
                a.click()
              })
            }
          },
          isProdVersion(row) {
            return row.versionBackupTime === "PROD"
          },
          testFun() {
            this.$refs.redirectForm.resetFields()
          }
        }
      })
    </script>
  </body>
</html>
