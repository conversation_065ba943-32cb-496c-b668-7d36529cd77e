const SendOrderFormMixins = {
  data() {
    return {
      text: ``,
      valid: true,
      rules: {
        email: [
          value => {
            const pattern =
              /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            return pattern.test(value) || this.systemLanguage.invalidEmailText
          }
        ],
        requested: [value => !!value || this.systemLanguage.requiredText],
        phone: [
          value => !!value || this.showEmptyPhoneMsg(),
          value => /^\d+$/.test(value) || this.systemLanguage.invalidPhoneText
        ],
        number: [value => /^$|^\d+$/.test(value) || this.systemLanguage.invalidPhoneText],
        name: [
          value => !!value || this.systemLanguage.requiredText,
          value =>
            (value && value.toString().trim().length > 1) || this.systemLanguage.invalidNameText
        ],
        agreement: [value => !!value || this.systemLanguage.requiredText],
        captcha: [
          value => !!value || this.systemLanguage.requiredText,
          value => /^\d{6}$/.test(value) || this.systemLanguage.invalidCaptchaText
        ]
      },
      regionList: {}, //地区列表()
      sendOrderForm: {
        email: "",
        name: "",
        phone: "",
        areaCode: null,
        discountCode: "",
        incomingMail: false,
        agreement: false,
        choosePayMethod: "",
        verificationCode: "" //钱包支付验证码
      },
      showAgreement: false, //是否显示 免责声明弹窗
      useAgreementBtn: true, //disabled 是否可点击 确认免责声明按钮
      discountRelated: {
        //折扣相关变量
        requestDiscountLoading: false, //请求折扣loading
        requestDiscountStatus: false, //折扣是否请求成功
        isShowRemoveIcon: false, //是否显示移除折扣按钮/icon
        // discountDesc: {},//返回的折扣码 描述信息
        isShowDiscountErrorMsg: false, //是否显示判空错误信息(空提交)
        timer: null, // 折扣定时器,1000ms后更变以上状态
        previousDiscountActive: false //上一次触发折扣接口的成功状态
      },
      discountGroup: [],
      taxesGroup: [],
      cartServiceCharges: 0, //购物车显示的服务费
      tempClosedDiscount: false, // 在櫃檯支付模式時,需要暫時移除折扣信息<保存在session>
      defaultAreaCode: null,
      additionalDisConfStatus: false, //折扣码与附加项目存在冲突
      invalidAdditionalItemsList: [], //冲突的附加项目与折扣码
      captchaBtnLoading: false,
      captchaBtnDisabled: false,
      captchaCountdown: 60,
      captchaInterval: null,
      showCountdown: false, //显示倒计时
      hasCheckCodeFailed: false, // 折扣码接口特殊失败状态
      sendPromotionDiscountTime: true // 是否发送折扣码时间戳
    }
  },
  mounted() {
    $.getJSON("../static/vuetify/region.json", r => {
      this.regionList = r
      if (Object.values(r).length) {
        this.defaultAreaCode = Object.values(r)[0].phone[0]
        this.sendOrderForm.areaCode = this.defaultAreaCode
      }
    })
    this.resetCaptchaCountdown()
  },
  computed: {
    //获取最终采用价格
    finalPrice() {
      // console.log(
      //   this.discountGroup.length || this.taxesGroup.length,
      //   this.discountedPrice,
      //   this.allPriceCache
      // )
      return this.discountedPrice || this.allPriceCache
    },
    loginStatus() {
      return (
        this.openTable.displayCRM &&
        this.$refs.personalCenter &&
        this.$refs.personalCenter.loginStatus
      )
    },
    //何时禁用输入折扣码的输入框
    disabledDiscountField() {
      return (
        // 折扣loading时
        this.discountRelated.requestDiscountLoading ||
        // 显示移除折扣按钮&&存在折扣码
        (this.discountRelated.isShowRemoveIcon && this.sendOrderForm.discountCode) ||
        // 非会员折扣请求成功
        (this.discountRelated.requestDiscountStatus && !this.isMemberDiscount()) ||
        //柜台支付没有折扣
        this.choosePayMethod === "payAtCashier"
      )
    },
    discountCodeDesc() {
      return (
        (this.discountGroup.find(e => e.hasOwnProperty("promotionDiscountCode")) || {}).language ||
        {}
      )
    },

    customDiscount() {
      let hasList =
        Array.isArray(this.openTable.promotionDiscountList) &&
        this.openTable.promotionDiscountList.length
      return this.openTable.promotionDiscount && hasList
    },
    captchaBtnText() {
      return this.showCountdown
        ? this.captchaCountdown + "s"
        : this.systemLanguage.walletFormGetCaptchaText
    }
  },
  methods: {
    showEmptyPhoneMsg() {
      let { requiredText, pleaseLoginMember, pleaseBindPhone } = this.systemLanguage
      if (this.choosePayMethod == "wallet") {
        if (this.LoginValid()) {
          return pleaseBindPhone
        } else {
          return pleaseLoginMember
        }
      } else {
        return requiredText
      }
    },
    // 是否存在cookie信息,若在computed中會不同步
    LoginValid() {
      return Cookies.get("userLogged") !== undefined
    },
    combineTaxMap(data = {}) {
      return Object.entries(data || {})
        .map(it => {
          let [key, value] = it
          if (!value) return null
          return {
            type: key,
            price: +value,
            language: {
              en: this.formatTaxI18n(key, "en"),
              zh: this.formatTaxI18n(key, "zh"),
              thirdLan: this.formatTaxI18n(key, "thirdLan")
            }
          }
        })
        .filter(Boolean)
    },
    formatTaxI18n(key, lan) {
      const i18nMap = {
        tax0Price: "tax0Name",
        tax1Price: "tax1Name",
        tax2Price: "tax2Name",
        tax3Price: "tax3Name"
      }
      const k = i18nMap[key]
      const result = window.i18n[lan][k] || ""
      if (key === "tax0Price") {
        const rate = this.hasProperty("billTax", "billTaxRate")
        //基础税率>0
        if (rate) return result.replace("#billTaxRate", rate + "%")
      }
      return result
    },
    // 当前是否已禁用折扣, 柜台支付禁用折扣码折扣和减满
    closeDiscount() {
      return this.choosePayMethod === "payAtCashier" && !this.LoginValid()
    },
    isMemberDiscount() {
      return Boolean(!this.confirmDiscountCode && (this.LoginValid() || this.loginStatus))
    },
    isCodeDiscount() {
      return Boolean(this.sendOrderForm.discountCode && (!this.LoginValid() || !this.loginStatus))
    },
    isMixDiscount() {
      return Boolean(this.sendOrderForm.discountCode && (this.LoginValid() || this.loginStatus))
    },
    //是否需要请求折扣码
    shouldRequestDiscount() {
      let hasDiscount = this.isCodeDiscount() || this.isMemberDiscount() || this.isMixDiscount()
      return this.existTaxes || (!this.closeDiscount() && hasDiscount)
    },

    msgTips(text, color, always = true) {
      if (this.showSendOrderView) {
        this.$snackbar(text, 2500, color)
      } else if (always) {
        layer.msg(text)
      }
    },
    // 在更改支付方式至柜台支付时触发,避免切换导致显示异常
    clearDiscountTimer() {
      clearTimeout(this.discountRelated.timer)
      this.discountRelated = {
        requestDiscountLoading: false,
        requestDiscountStatus: false,
        isShowRemoveIcon: false,
        isShowDiscountErrorMsg: false,
        timer: null
      }
    },
    // 根据状态码处理折扣结果
    /**
     * @description 根据折扣结果处理逻辑
     * @param {Object} res 数据
     * */
    handleDiscountRes(res) {
      let { statusCode, list, invalidAdditionalItemsList = [] } = res
      let hasDiscount = list.find(e => e.hasOwnProperty("promotionDiscountCode"))
      // 若折扣码折扣生效,需要执行的函数
      let discountCodeCallback = () => {
        this.discountRelated.timer = setTimeout(() => {
          this.discountRelated.requestDiscountStatus = false
          this.discountRelated.isShowRemoveIcon = true
        }, 1000)
      }
      switch (statusCode) {
        case 2001: // 无冲突
          if (hasDiscount) discountCodeCallback()
          break
        case 2002: // 有冲突
          discountCodeCallback()
          if (invalidAdditionalItemsList.length) {
            this.invalidAdditionalItemsList = invalidAdditionalItemsList //allAdditionalItems打包盒类与折扣码冲突
          }
          break
        default:
          discountCodeCallback()
          break
      }
    },
    // 请求折扣信息
    onRequestDiscount() {
      if (!this.shopCartList.length) return false
      // 此loading 为公共loading,三种模式都需要
      this.discountRelated.requestDiscountLoading = true
      // console.log("onRequestDiscount", this.sendOrderForm.discountCode)
      this.checkDiscountCode()
        .then(r => {
          this.hasCheckCodeFailed = false // 重置折扣码接口特殊失败状态
          let isCustom = sessionStorage.getItem("couponUseType") == "custom"
          if (isCustom) {
            this.sendPromotionDiscountTime = false //手动触发不需要发优惠券时间戳
          } else {
            this.sendPromotionDiscountTime = true //自动触发需要发优惠券时间戳
          }
          this.discountRelated.previousDiscountActive = !!this.sendOrderForm.discountCode
          this.checkDiscountConflict(r)

          //购物车/送单页的底部折扣信息数据
          this.cartServiceCharges = r.scharge

          this.discountGroup = r.list.map(el => {
            if (!el.language) {
              el.language = {}
            }
            return el
          })

          this.taxesGroup = this.combineTaxMap(r.taxMap || {})
          this.discountRelated.requestDiscountLoading = false

          //是混合折扣||折扣码
          if (this.isMixDiscount() || this.isCodeDiscount()) {
            this.discountRelated.requestDiscountStatus = true
          }
          if (r.list.length) {
            this.handleDiscountRes(r)
          }
          this.discountedPrice = retainSignificantDecimals(r.totalPrice) // 总价
          r.confirmDiscountCode = this.confirmDiscountCode //请求成功折扣码回显用
        })
        .catch(e => {
          this.removeDiscount()
          this.discountRelated.requestDiscountLoading = false
          this.discountRelated.requestDiscountStatus = false
          this.discountRelated.isShowDiscountErrorMsg = true
          this.retryOnError(e)
          this.discountRelated.previousDiscountActive = false
        })
        .finally(() => {
          this.checkDisabledWallet() //判断是否禁用钱包支付
        })
    },
    // 折扣失败重试逻辑
    retryOnError(errorEvent) {
      // 若折扣码错误,且上一次手动折扣成功,则自动折扣
      const { statusCode, autoDiscount } = errorEvent
      if (typeof statusCode != "number" || statusCode == 500) {
        this.hasCheckCodeFailed = true // 设置折扣码接口特殊失败状态
        return
      }
      if (statusCode === 408) {
        if (!autoDiscount) {
          // 若上一个折扣有生效,则再次触发自动折扣
          if (this.discountRelated.previousDiscountActive) {
            this.checkDiscountAgain()
            return
          }
        } else {
          this.openTable.promotionDiscountUpdateTime = errorEvent.promotionDiscountUpdateTime //更新折扣码时间戳
          this.checkDiscountAgain()
          return
        }
      }
      // 若有税率/会员/服务费 重试
      let { serviceCharges: { displayInShoppingCartPage } = {}, billTax } = this.openTable
      if (this.existTaxes || this.LoginValid() || displayInShoppingCartPage || billTax) {
        this.onRequestDiscount()
      }
    },
    //附加选项下展示折扣码冲突错误
    matchAdditionalConfMsg(code) {
      //code为附加项目的fCode
      let { additionalDisConfTxt } = this.systemLanguage
      let targetAdditionItem = this.invalidAdditionalItemsList.find(e => e.fCode === code) || {}
      let conflictIdArr = targetAdditionItem.conflictId || [] //冲突的id
      let effDiscountName = this.inListTitle(targetAdditionItem) //有效的折扣名称
      let invalidDiscountName = this.getConflictName(conflictIdArr)

      return additionalDisConfTxt
        .replace(/#effDiscountName/g, effDiscountName)
        .replace(/#invalidDiscountName/g, invalidDiscountName)
    },
    //折扣码冲突处理(totalAreaConf:结算区域)
    discountConflictTips(type = "totalAreaConf", discountItem = {}) {
      const { discountConflict, additionalDisConfTxt, memberDiscount } = this.systemLanguage
      const arr = type === "totalAreaConf" ? [discountItem] : this.invalidAdditionalItemsList
      const targetConflictArr = arr.filter(e => e.conflictId) || []

      const conflictMsgArr = targetConflictArr.map(e => {
        const { language } = this.openTable
        let effDiscountName =
          type === "totalAreaConf" ? (e.language || {})[language] : this.inListTitle(e)
        const invalidDiscountName = this.getConflictName(e.conflictId)

        // 如果未配置会员折扣描述，则使用会员折扣
        if (type === "totalAreaConf" && !effDiscountName) {
          effDiscountName = memberDiscount
        }

        // 选择替换的消息模板
        const replaceMsg = type === "totalAreaConf" ? discountConflict : additionalDisConfTxt

        // 替换占位符
        return replaceMsg
          .replace(/#effDiscountName/g, effDiscountName)
          .replace(/#invalidDiscountName/g, invalidDiscountName)
      })

      return conflictMsgArr
    },
    checkDiscountConflict(res) {
      let { statusCode, list = [], invalidAdditionalItemsList = [] } = res
      this.additionalDisConfStatus = statusCode === 2002 && invalidAdditionalItemsList.length > 0 //折扣码与附加项目存在冲突
      // this.additionalDisConfStatus = true
    },
    //获取所有无效的折扣名称
    getConflictName(conflictIdArr = []) {
      let conflictDiscountArr = this.discountGroup.filter(e => conflictIdArr.includes(e.id))
      let conflictName = conflictDiscountArr
        .map(e => (e.language || {})[this.openTable.language])
        .join(",")
      return conflictName
    },
    // 检查折扣码是否存在
    isAdditionalConflict(code) {
      return this.invalidAdditionalItemsList.some(e => e.fCode === code)
    },
    // 点击icon 移除 discount
    onRemoveDiscount() {
      let autoDiscount = sessionStorage.getItem("couponUseType") !== "custom"
      this.sendPromotionDiscountTime = autoDiscount

      this.removeDiscount()
      let { serviceCharges: { displayInShoppingCartPage } = {} } = this.openTable
      if (this.existTaxes || this.LoginValid() || displayInShoppingCartPage) {
        this.onRequestDiscount()
      }
    },
    removeDiscount() {
      sessionStorage.removeItem("couponUseType")
      this.discountRelated.requestDiscountLoading = false
      this.discountRelated.isShowRemoveIcon = false
      this.additionalDisConfStatus = false //重置附加项目冲突状态
      this.discountCompulsory = null //重置强制折扣
      this.sendOrderForm.discountCode = ""
      this.confirmDiscountCode = ""
      // 清空底部折扣明細
      this.discountGroup = []
      this.taxesGroup = []

      //清除服务费
      this.cartServiceCharges = 0
    },
    async getCaptcha() {
      this.captchaBtnLoading = true
      //提示验证码已发送
      this.$snackbar(this.systemLanguage.captchaFetchSuccessText, 2000, "#4CAF50")

      let totalPrice =
        this.discountGroup.length || this.taxesGroup.length
          ? this.discountedPrice
          : this.allPriceCache

      let data = {
        domain: this.openTable.companyName,
        storeNumber: this.openTable.storeNumber,
        areaCode: "+" + this.sendOrderForm.areaCode,
        tableNumber: this.openTable.tableNumber,
        totalPrice
      }
      const defaultErrorCallBack = () => {
        clearInterval(this.CaptchaInterval)
        // 恢复获取验证码状态
        this.captchaBtnDisabled = false
        this.showCountdown = false
      }

      $.post({
        url: "../member/getVerificationCode",
        dataType: "json",
        traditional: true,
        data,
        timeout: 5000, // 5秒超時
        success: res => {
          // res.statusCode == 5001
          if (res.statusCode !== 200) {
            let {
              accountNotFoundText,
              lowBalanceWarningText,
              captchaFetchFailedText,
              memberExpired,
              PCLoginMemberTimeout,
              PCLoginMemberNotOpen,
              dailyLimitText,
              disableMemberTips,
              pleaseBindPhone
            } = this.systemLanguage

            defaultErrorCallBack()

            const removeLogged = errorCode => {
              Cookies.remove("userLogged")
            }

            // 定义通用处理函数
            const commonHandler = (func, msg, callback) => {
              func()
              this.$snackbar(msg, 2000, "#F44336", () => {
                if (callback) callback()
              })
            }

            // 创建一个对象，定义不同错误码对应的处理逻辑
            let errorCaptchaMsgObj = {
              5001: () =>
                commonHandler(() => removeLogged(5001), PCLoginMemberTimeout, this.showUserPopup),
              5002: () => commonHandler(() => removeLogged(5002), PCLoginMemberNotOpen),
              5003: disableMemberTips,
              5102: lowBalanceWarningText,
              5103: memberExpired,
              5104: accountNotFoundText,
              5105: dailyLimitText, //验证码达到每日上限
              5109: pleaseBindPhone
            }

            // 获取错误信息
            if (typeof errorCaptchaMsgObj[res.statusCode] === "function") {
              errorCaptchaMsgObj[res.statusCode]()
            } else {
              this.$snackbar(
                errorCaptchaMsgObj[res.statusCode] || captchaFetchFailedText,
                2000,
                "#F44336"
              )
            }
          } else {
            this.captchaBtnDisabled = true
            // 验证码获取成功
            this.resetCaptchaCountdown()
            this.showCountdown = true
            this.CaptchaInterval = setInterval(() => {
              if (this.captchaCountdown > 1) {
                this.captchaCountdown--
              } else {
                this.resetGetCaptcha()
              }
            }, 1000)
          }
        },
        error: () => {
          defaultErrorCallBack()
          this.$snackbar(this.systemLanguage.captchaFetchFailedText, 2000, "#F44336")
        },
        complete: () => {
          this.captchaBtnLoading = false
        }
      })
    },
    resetGetCaptcha() {
      clearInterval(this.CaptchaInterval)
      this.captchaBtnDisabled = false
      this.showCountdown = false
      this.resetCaptchaCountdown()
    },
    // isShowPhoneHint() {
    //   return this.choosePayMethod == "wallet" && (!this.LoginValid() || !this.sendOrderForm.phone)
    // },
    isShowPhoneHint() {
      const isWallet = this.choosePayMethod == "wallet"
      const notLoggedOrNoPhone = !this.LoginValid() || !this.sendOrderForm.phone

      return {
        show: isWallet && notLoggedOrNoPhone,
        message: !this.LoginValid()
          ? this.systemLanguage.pleaseLoginMember
          : this.systemLanguage.pleaseBindPhone
      }
    },
    captchaDisabled() {
      return !this.LoginValid() || this.captchaBtnDisabled || this.isDisabledWallet
    },
    resetCaptchaCountdown() {
      let {
        aliyunSMS: { countdown: smsCountdown = 60 } = {},
        aliyunSMSGlobe: { countdown: globeCountdown = 60 } = {}
      } = this.openTable
      if (this.sendOrderForm.areaCode == 86) {
        //中国大陆
        this.captchaCountdown = smsCountdown
      } else {
        this.captchaCountdown = globeCountdown
      }
    },
    error408CallBack(result, autoDiscount) {
      // let isAutoDiscount = sessionStorage.getItem("couponUseType") === "auto"
      let { errorDiscount } = this.systemLanguage
      if (!autoDiscount) {
        this.msgTips(errorDiscount, "#ff5252")
      } else {
        //更新自动折扣数据
        this.openTable.promotionDiscountList = result.promotionDiscountList
      }
    }
  }
}
