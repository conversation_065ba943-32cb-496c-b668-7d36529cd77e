* {
  margin: 0;
  padding: 0;
}

/* html,
body {
  width: 100%;
  height: 100%;
} */
:root {
  --nabeColor: #c7b14c;
  --ppgColor: #770239;
  --styleColor: #ED6211;
}
/* 取消a标签点击时候的背景颜色 */
a {
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  -webkit-user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none;
}

.clearfix {
  zoom: 1;
}

.clearfix:after {
  content: '';
  /*设置内容为空*/
  height: 0;
  /*高度为0*/
  line-height: 0;
  /*行高为0*/
  display: block;
  /*将文本转为块级元素*/
  visibility: hidden;
  /*将元素隐藏*/
  clear: both;
  /*清除浮动*/
}

#app {
  /* width: 100%;
  height: 100%; */
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  -webkit-overflow-scroll: touch;
  font-family: Arial, Helvetica, sans-serif;
}

.fixed {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.app_container {
  position: relative;
  height: 100%;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.header_warp {
  /* height: 4rem; */

  /* padding: 0.5rem 0 0 0; */
  background-color: var(--styleColor);
}

.table_info {
  position: relative;
  font-size: 0.35rem;
  color: #fff;
  height: 1.2rem;
  /* height: 1.5rem; */
  /* display: flex;
  align-items: center; */
}

.table_info_num {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.table_info_lan {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0.4rem;
}

.tab_warp {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: auto;
  background-color: #e4e7e9;
  /* padding-bottom: 0.2rem; */
  /* padding: 0.4rem 0 0rem; */
  /* height: 1.3rem;
  align-items: center; */
  /* border-top: 1px solid #fff; */
}

.tab_warp::-webkit-scrollbar {
  display: none;
}

.tab_cell {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  /* width: 1.8rem; */
  min-width: 2.5rem;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  padding: 0.35rem;
  /* background-color: var(--styleColor); */
  color: #666874;
  font-weight: normal;
  font-size: 0.39rem;
  margin: 0 0.1rem;
  /* border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem; */
}

.tab_cell:first-child {
  margin-left: 0rem;
}

.tab_cell:last-child {
  margin-right: 0rem;
}

.tab_active {
  background-color: #f7f8fa;
  font-weight: 550;
  color: var(--styleColor);
}

.content_warp {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
  background-color: #f7f8fa;
  padding: 0.4rem 0.4rem;
  -webkit-align-content: flex-start;
  -ms-flex-line-pack: start;
  align-content: flex-start;
}

.content_cell {
  position: relative;
  width: 47%;
  min-height: 4rem;
  background-color: #fff;
  border-radius: 0.3rem;
  margin-bottom: 0.5rem;
  -webkit-box-shadow: 0 8px 12px #ebedf0;
  box-shadow: 0 8px 12px #ebedf0;

  /* display: flex;
  flex-direction: column; */
}

.foodImage_warp {
  /* height: 50%; */
  width: 100%;
  position: relative;
}

.food_img {
  height: 3rem;
  width: 100%;
  border-radius: 0.3rem 0.3rem 0 0;
}

.addFoodBtn {
  position: absolute;
  /* bottom: 38%; */
  top: 2.1rem;
  right: 0.3rem;
  width: 0.6rem;
  height: 0.6rem;
  border-radius: 50%;
  background-color: #fff;
  color: var(--styleColor);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 0.6rem;
  z-index: 1;
  border: 1px solid var(--styleColor);
}

.food_text {
  /* width: 100%; */
  /* height: 50%; */
  /* height: 0.5rem; */
  padding: 0rem 0.2rem 0rem;
  font-size: 0.3rem;
  text-align: center;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.footer_warp {
  position: relative;
  z-index: 3;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  /* height: 1.3rem; */
  color: #fff;
  background-color: #f7f8fa;
}

/* .shopNum_warp {
  width: 0.6rem;
  height: 0.6rem;
  line-height: 0.6rem;
  border-radius: 50% 49%;
  background: rgba(255, 255, 0, 1);
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  color: rgba(244, 83, 91, 1);
  position: absolute;
  text-align: center;
  top: 0;
  right: 10%;
  font-size: 0.45rem;
  font-style: initial;
} */

.shop_car {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--styleColor);
  font-size: 0.5rem;
  border-radius: 0.2rem;
  margin: 0 0.2rem 0.3rem;
  padding: 0.25rem 0;
}

.cartImage {
  width: 0.6rem;
  padding-bottom: 0.1rem;
}

/* 购物车页面 */
.cart_warp {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #fff;
  /* display: none; */
  z-index: 520;
  display: flex;
  flex-direction: column;
}

.cart_info_warp_header {
  background-color: var(--styleColor);
  position: relative;
  font-size: 0.35rem;
  color: #fff;
  height: 1.2rem;
  display: flex;
  align-items: center;
}
.send_single {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--styleColor);
  font-size: 0.5rem;
  border-radius: 0.2rem;
  color: #fff;
  margin: 0 0.2rem 0.3rem;
  padding: 0.25rem 0;
}
.disabledSend {
  /* cursor: pointer; */
  cursor: not-allowed;
  opacity: 0.5;
}
.cart_food_box {
  flex: 1;
  /* min-height: 2rem; */
  padding: 0rem 0.4rem 0.4rem 0.4rem;
  position: relative;
  overflow: auto;
}

.hits_food {
  min-height: 2rem;
  padding: 1rem 0.4rem 0.2rem 0.4rem;
  position: relative;
}

.cart_food_null {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 0.4rem;
  color: #ccc;
}
.cart_food_warp {
  /* padding-bottom: 1.2rem;
  border-bottom: 1px solid rgb(220, 220, 220); */
}
.your_cart {
  padding: 0.8rem 0;
  font-size: 0.5rem;
  color: var(--styleColor);
}
.cart_food_cell {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0 0 0.2rem 0;
}

.cart_food_img {
  width: 1.8rem;
}
.cart_food_img_cell {
  width: 100%;

  /* height: 100%; */
  height: auto;
  background-size: cover;
  border-radius: 0.2rem;
  max-height: 1.8rem;
}

.cart_info {
  flex: 1;

  display: flex;

  justify-content: space-between;
  margin-bottom: 0.3rem;
  /* margin-left: 0.3rem; */
  color: rgba(0, 0, 0, 0.7);
  padding-left: 0.2rem;
}
.info_left {
  padding-right: 0.4rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.info_right {
  display: flex;
  align-items: center;
}
.cart_food_priceNum {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  font-size: 0.3rem;
}

.cart_food_title {
  font-size: 0.35rem;
}

.littleitem {
  font-size: 0.28rem;
}

.cart_food_price {
  color: #ee0a24;
}

.cart_food_price_amount {
  font-size: 0.5rem;
}

.cart_food_numBox {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.cart_food_numVal {
  font-size: 0.45rem;
  line-height: 0.6rem;
  width: 1rem;
  text-align: center;
  /* padding: 0 0.5rem; */
}

.cart_add_btn,
.cart_del_btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 50%;
  font-size: 0.5rem;
  width: 0.6rem;
  height: 0.6rem;
}

.cart_del_btn {
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #ccc;
}

.cart_add_btn {
  background-color: var(--styleColor);
  color: #fff;
}

.btnPosition {
  /* position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%); */
}

/* 蒙层 （后期再合并相同属性）*/
/* .mongolia {
  display: none;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 2;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
} */

#mongoliaLoading {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 500;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.mongolia2 {
  display: none;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 500;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.mongolia3 {
  display: none;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 7;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.closepopUpBtn {
  position: absolute;
  top: 0.1rem;
  right: 0.2rem;
  width: 1rem;
  height: 1rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  color: #c8c9cc;
  font-size: 0.6rem;
  z-index: 20;
}

.close_warp {
  position: fixed;
  top: 5rem;
  right: 1rem;
  width: 0.8rem;
  height: 0.8rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 50%;
  background: #fff;
  color: #4f4f4f;
  font-weight: 600;
  font-size: 0.6rem;
  z-index: 501;
}

.maskimg_warp {
  width: 7rem;
  padding: 0.5rem 0;
  border-radius: 0.3rem;
  background-color: #fff;
  font-size: 0.45rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-weight: 600;
  z-index: 500;
}

#dialog {
  left: 50%;
  position: fixed !important;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-family: microsoft yahei, arial;
  z-index: 20891019;
  display: none;
}

.maskimg_text1 {
  margin-bottom: 0.4rem;
  color: var(--styleColor);
}

.maskimg_text1,
.maskimg_text2 {
  text-align: center;
  padding: 0 0.5rem;
  /* color: var(--styleColor); */
}

/* 抛物线 */
.shop {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  /* position: fixed;
  top: 300px;
  left: 400px; */
}

.ball {
  position: fixed;
  left: 20%;
  bottom: 1.3rem;
  z-index: 200;
  -webkit-transition: all 0.5s cubic-bezier(0.49, -0.29, 0.75, 0.41);
  -o-transition: all 0.5s cubic-bezier(0.49, -0.29, 0.75, 0.41);
  transition: all 0.5s cubic-bezier(0.49, -0.29, 0.75, 0.41);

  /*贝塞尔曲线*/
  /* 解决闪烁问题 */

  -webkit-transform: translate3d(0, 0, 0);
  /*开启硬件加速*/
  -webkit-backface-visibility: hidden;
  /*元素旋转时隐藏背面*/
  -webkit-transform-style: preserve-3d;
  /*保留3D空间*/
}

.inner {
  /* width: 16px;
  height: 16px; */
  border-radius: 50%;
  /* background-color: rgb(0, 160, 220); */
  -webkit-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

/* 对应food弹窗 */
.food_info_warp {
  /* display: none; */
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;

  background-color: #fff;
  z-index: 7;
  font-size: 0.45rem;
}

.food_info_warp_header {
  position: fixed;
  height: 1.2rem;
  left: 0;
  right: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--styleColor);
  z-index: 1;
}

.blackIcon {
  width: 0.8rem;
  padding-left: 0.3rem;
}

.food_info_warp_header_title {
  text-align: center;
  width: 200px;
  color: #fff;
  white-space: nowrap;
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  overflow: hidden;
}

/* 跑马灯效果 */
.merquee-txt {
  display: inline-block;
  padding-left: 100%;
  /* 从右至左开始滚动 */
  -webkit-animation: marqueeTransform 7s linear infinite;
  animation: marqueeTransform 7s linear infinite;
}

/* 跑马灯动画 */
@-webkit-keyframes marqueeTransform {
  0% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }

  100% {
    -webkit-transform: translate(-100%, 0);
    transform: translate(-100%, 0);
  }
}

@keyframes marqueeTransform {
  0% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }

  100% {
    -webkit-transform: translate(-100%, 0);
    transform: translate(-100%, 0);
  }
}

.food_info_warp_img {
  /* float: left; */
  width: 3rem;
  /* height: 3rem; */
  -webkit-box-shadow: 3px 3px 4px #000;
  box-shadow: 3px 3px 4px #000;
  margin: 0 0.4rem 0.1rem 0;
  border-radius: 0.2rem;
  /* object-fit: cover; */
}

.food_info_warp_content {
  position: fixed;
  left: 0;
  right: 0;
  top: 1rem;
  bottom: 1rem;
  padding: 0.625rem 0.8em;
  overflow: auto;
}

.food_info_warp_content_top {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  padding-bottom: 0.5rem;
}

.food_info_warp_content_products {
  font-size: 0.4rem;
  font-family: Arial, Helvetica, sans-serif;
}

/* 保留字体格式标签 */
.pre_style {
  /* all: initial; */
  white-space: pre-line;
  /* word-break: break-all; */
  word-break: keep-all;
  /* white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap; */
  word-wrap: break-word;
  font-size: 0.3rem;
  font-family: Arial, Helvetica, sans-serif;
}

.food_info_warp_content_shopNum {
  border-top: 1px solid #ccc;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0.5rem 0rem;
}

.shopNumTitle {
  font-size: 0.35rem;
  color: var(--styleColor);
}

.food_info_warp_footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.joinCartBtn {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--styleColor);
  font-size: 0.5rem;
  border-radius: 0.2rem;
  color: #fff;
  margin: 0 0.2rem 0.3rem;
  padding: 0.25rem 0;
}

/* 细项 */
.infoPoints_lable {
  padding: 0.3rem 0rem 0.5rem;
  font-size: 0.35rem;
  border-top: 1px solid #ccc;
  color: var(--styleColor);
}

.infoPoints_addicon {
  position: absolute;
  right: -0.2rem;
  top: -0.2rem;
  width: 0.5rem;
  z-index: 3;
}

.infoPoints_content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  /* overflow: auto; */
  padding-bottom: 0.3rem;
  color: #666;
}

.infoPoints_content_cell {
  width: 45%;
  margin-bottom: 0.1rem;
}

.detailTaste {
  font-size: 0.25rem;
  margin-bottom: 0.1rem;
  min-height: 0.35rem;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.defaultlist {
  width: 45%;
}

.infoPoints_select {
  font-size: 0.3rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  /* width: 40%; */
  border: 1px solid #e6e6e6;
  /* height: 0.8rem; */
  /* margin-bottom: 3%; */
  min-height: 0.3rem;
  border-radius: 4px;
  padding: 0.2rem 0.3rem;
  color: #fff;
  background-color: var(--styleColor);
  position: relative;
}

.infoPoints_noselect {
  font-size: 0.3rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  /* width: 40%; */
  border: 1px solid #e6e6e6;
  min-height: 0.3rem;
  /* margin-bottom: 3%; */
  border-radius: 4px;
  padding: 0.2rem 0.3rem;
  color: #666;
  background-color: #fff;
  position: relative;
}

.foodDivider {
  border-top: 1px solid #ccc;
}

/* 提示窗口 */
.tip_warp {
  display: none;
  position: fixed !important;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-family: microsoft yahei, arial;
  z-index: 5005;
}

.tip_cell {
  /* width: 7rem; */
  padding: 0.3rem 0.4rem;
  border-radius: 0.1rem;
  background: rgba(21, 23, 24, 0.9);
  color: #fff;
  font-size: 0.35rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  /* font-weight: 600; */
}

.hehe-enter,
.hehe-leave-to {
  opacity: 0;
}

.hehe-enter-to,
.hehe-leave {
  opacity: 1;
}

.hehe-enter-active,
.hehe-leave-active {
  -webkit-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}

/* [v-cloak] {
  display: none !important;
} */
/* loading */
.loader {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;

  vertical-align: middle;
  z-index: 501;
}

.loader-3 .dot {
  width: 10px;
  height: 10px;
  background: var(--styleColor);
  border-radius: 50%;
  position: absolute;
  top: -webkit-calc(50% - 5px);
  top: calc(50% - 5px);
}

.loader-3 .dot1 {
  left: 0px;
  -webkit-animation: dot-jump 0.5s cubic-bezier(0.77, 0.47, 0.64, 0.28)
    alternate infinite;
  animation: dot-jump 0.5s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate
    infinite;
}

.loader-3 .dot2 {
  left: 20px;
  -webkit-animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.47, 0.64, 0.28)
    alternate infinite;
  animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate
    infinite;
}

.loader-3 .dot3 {
  left: 40px;
  -webkit-animation: dot-jump 0.5s 0.4s cubic-bezier(0.77, 0.47, 0.64, 0.28)
    alternate infinite;
  animation: dot-jump 0.5s 0.4s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate
    infinite;
}

@-webkit-keyframes dot-jump {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

@keyframes dot-jump {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

/* 向左右提示 */

.toRight_img {
  position: absolute;
  top: 2.57rem;
  right: 0.4rem;
  z-index: 2;
  -webkit-animation: bounce-up 1.4s linear infinite;
  animation: bounce-up 1.4s linear infinite;
  height: 0.6rem;
}

.toLeft_img {
  position: absolute;
  top: 2.57rem;
  left: 0.4rem;
  z-index: 1;
  -webkit-animation: bounce-up 1.4s linear infinite;
  animation: bounce-up 1.4s linear infinite;
  height: 0.6rem;
}

@-webkit-keyframes bounce-up {
  25% {
    -webkit-transform: translateX(10px);
  }

  50%,
  100% {
    -webkit-transform: translateX(0);
  }

  75% {
    -webkit-transform: translateX(-10px);
  }
}

@keyframes bounce-up {
  25% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }

  50%,
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  75% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
}

/* allergen icon */
.allergen_warp {
  padding: 0 0.2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;

  /* height: 1.8rem; */

  /* display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all; */
}

.allergen_Icon {
  /* float: left; */
  width: 0.7rem;
  height: 0.7rem;
  padding: 0.1rem 0.1rem 0.1rem 0;
}

.food_info_warp_allergen {
  border-top: 1px solid #ccc;
  padding: 0.5rem 0rem;
}

.food_info_warp_allergen_title {
  font-size: 0.35rem;
  padding-bottom: 0.2rem;
  color: var(--styleColor);
}

.food_info_warp_allergen_Icon {
  padding-right: 0.2rem;
  width: 1rem;
  height: 1rem;
}

/* 套餐细项（后改） */
.optionalTitle {
  padding: 0.3rem 0rem 0.4rem;
  font-size: 0.35rem;
  color: var(--styleColor);
}

/* 菜单food价钱 */
.foodPrice_warp {
  color: #fff;
  font-size: 0.36rem;
  position: absolute;
  top: 15px;
  padding: 8px 10px;
  background: var(--styleColor);
  -webkit-box-shadow: -1px 2px 4px rgba(0, 0, 0, 0.5);
  box-shadow: -1px 2px 4px rgba(0, 0, 0, 0.5);
  /* color: #F8463F; */
}

.foodPrice_warp:before,
.foodPrice_warp:after {
  position: absolute;
  content: '';
  display: block;
}

.foodPrice_warp:before {
  width: 7px;
  height: 100%;
  padding: 0 0 7px;
  top: 0;
  left: -7px;
  background: inherit;
  border-radius: 5px 0 0 5px;
}

.foodPrice_warp:after {
  width: 5px;
  height: 5px;
  background: rgba(0, 0, 0, 0.35);
  bottom: -5px;
  left: -5px;
  border-radius: 5px 0 0 5px;
}

/* 细项弹窗选择 */
.foodDialog {
  background-color: #fff;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 7rem;
  /* height: 5rem; */
  border-radius: 0.3rem;
  border: 1px solid #ebeef5;
  z-index: 8;
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 0.35rem;
  padding: 0.4rem 0.4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.optionalFood {
  /* flex: 1; */
  margin: 0.4rem 0.2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  max-height: 4rem;
  overflow-y: auto;
  /* justify-content: space-around; */
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-overflow-scrolling: auto;
  /* -webkit-overflow-scrolling: touch; */
}

.optionalFood::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 10px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.optionalFood::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #535353;
}

.optionalFood::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: #ededed;
}

.small_btn {
  padding: 0.2rem 0.6rem;
  font-size: 12px;
  border-radius: 3px;
}

.cencelBtn {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  margin-right: 0.3rem;
}

.defaultBtn {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}

.btns {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.radio-box {
  width: 50%;
  /* margin-right: 0.3rem; */
}

.checkLabel {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.checkSpan {
  /* font-size: 0.25rem; */
  /* width: 2.5rem; */
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input[type='checkbox'],
input[type='radio'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  position: relative;
  right: 0;
  bottom: 0;
  left: 0;
  height: 20px;
  width: 20px;
  vertical-align: -0.8rem;
  -webkit-transition: all 0.15s ease-out 0s;
  -o-transition: all 0.15s ease-out 0s;
  transition: all 0.15s ease-out 0s;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  margin: 0.1rem;
  outline: none;
  border-radius: 10%;
  vertical-align: middle;
}

/* Checkbox */
input[type='checkbox']:before,
input[type='checkbox']:after {
  position: absolute;
  content: '';
  background: #fff;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

input[type='checkbox']:before {
  left: 2px;
  top: 6px;
  width: 0;
  height: 2px;
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
}

input[type='checkbox']:after {
  right: 9px;
  bottom: 3px;
  width: 2px;
  height: 0;
  transform: rotate(40deg);
  -webkit-transform: rotate(40deg);
  -moz-transform: rotate(40deg);
  -ms-transform: rotate(40deg);
  -o-transform: rotate(40deg);
  -webkit-transition-delay: 0.2s;
  -o-transition-delay: 0.2s;
  transition-delay: 0.2s;
}

input[type='checkbox']:checked:before {
  left: 1px;
  top: 10px;
  width: 6px;
  height: 2px;
}

input[type='checkbox']:checked:after {
  right: 5px;
  bottom: 1px;
  width: 2px;
  height: 14px;
}

input[type='checkbox']:indeterminate:before,
input[type='checkbox']:indeterminate:after {
  width: 7px;
  height: 2px;
  transform: rotate(0);
  -webkit-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
}

input[type='checkbox']:indeterminate:before {
  left: 1px;
  top: 7px;
}

input[type='checkbox']:indeterminate:after {
  right: 1px;
  bottom: 7px;
}

/* Radio */
input[type='radio'] {
  vertical-align: middle;
  border-radius: 50%;
}

input[type='radio']:checked:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

input[type='radio']:before {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 3px;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transition: all ease-out 250ms;
  -o-transition: all ease-out 250ms;
  transition: all ease-out 250ms;
}

input[type='checkbox'],
input[type='radio'] {
  border: 2px solid var(--styleColor);
}

input[type='checkbox']:checked,
input[type='checkbox']:indeterminate,
input[type='radio']:checked:before {
  background: var(--styleColor);
}

.fade-enter-active,
.fade-leave-active {
  -webkit-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* layer弹出层UI */

.tipDia {
  min-width: 6rem !important;
  max-width: 8rem !important;
  background-color: #000 !important;
  filter: alpha(opacity=60) !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  color: #fff !important;
  border: none !important;
  border-radius: 0.2rem !important;
}
.tipDia .layui-layer-content {
  word-break: normal !important;
}
.tipDia .layui-layer-setwin .layui-layer-close2:hover {
  background-position: -149px -31px;
}
