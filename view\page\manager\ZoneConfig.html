<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>

    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/components/TheCard.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style>
      #app {
        padding: 0 10px;
      }

      .el-card {
        width: 260px;
        border-radius: 20px;
        color: #282058;
        background-image: linear-gradient(120deg, #fdfbfb 0%, #e<PERSON>ee 100%);
      }

      .el-card__body {
        padding: 10px 20px;
      }

      .el-card__body i {
        font-size: 18px;
      }

      .zoneCard-header {
        display: flex;
        justify-content: space-between;
      }

      .zoneCard-header-place {
        font-size: 18px;
        font-weight: 600;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 10px;
      }

      .cell-header {
        font-size: 14px;
        margin-bottom: 5px;
        white-space: nowrap;
      }

      .cell-bottom {
        font-size: 13px;
        color: #becae4;
      }

      .contentIcon {
        width: 35px;
        flex-shrink: 0;
        margin-right: 10px;
      }

      .zoneCard-content-warp {
        display: flex;
        padding: 13px 0;
      }

      .zoneCard-content-cell-icon {
        display: flex;
        align-content: center;
        height: 100%;
      }

      .zoneCard-bottom {
        display: flex;
        justify-content: space-between;
      }

      .zoneCard-content-cell-warp .cell-bottom {
        padding-left: 5px;
      }

      .zoneCard-bottom-right {
        display: flex;
        align-items: center;
        font-size: 14px;
      }

      .el-icon-right {
        padding-left: 6px;
      }

      .el-icon-delete {
        color: #f56c6c;
      }

      .zoneCard-bottom-left,
      .zoneCard-bottom-right,
      .zoneCard-header-EditBtn {
        cursor: pointer;
      }
      .zone-name-warp .el-form-item__content {
        display: flex;
      }

      @keyframes icon-bounce {
        0%,
        100% {
          -moz-transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        25% {
          -moz-transform: rotate(15deg);
          -ms-transform: rotate(15deg);
          -webkit-transform: rotate(15deg);
          transform: rotate(15deg);
        }

        50% {
          -moz-transform: rotate(-15deg);
          -ms-transform: rotate(-15deg);
          -webkit-transform: rotate(-15deg);
          transform: rotate(-15deg);
        }

        75% {
          -moz-transform: rotate(5deg);
          -ms-transform: rotate(5deg);
          -webkit-transform: rotate(5deg);
          transform: rotate(5deg);
        }

        85% {
          -moz-transform: rotate(-5deg);
          -ms-transform: rotate(-5deg);
          -webkit-transform: rotate(-5deg);
          transform: rotate(-5deg);
        }
      }

      .zoneCard-mainBox .el-icon-edit:hover,
      .zoneCard-mainBox .el-icon-delete:hover {
        -webkit-animation: icon-bounce 0.5s alternate;
        -moz-animation: icon-bounce 0.5s alternate;
        -o-animation: icon-bounce 0.5s alternate;
        animation: icon-bounce 0.5s alternate;
      }

      .el-col-box {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
        flex-shrink: 0;
      }

      .el-tabs__item {
        font-size: 18px;
      }

      .el-form-item {
        margin-bottom: 0px;
      }

      .zone2Box {
        margin-left: 40px;
        position: relative;
        z-index: 1;
      }

      .zone3Box {
        position: relative;
        margin-left: 20px;
        z-index: -1;
      }

      .elColBox {
        text-align: center;
      }

      .elColBox i {
        line-height: 33px;
        font-size: 25px;
      }

      .el-dialog__body {
        max-height: 400px;
        overflow: auto;
      }

      .addZoneBox {
        text-align: end;
        padding: 10px 0px 20px;
      }

      .tabBox .el-tabs__content {
        min-height: 100px;
      }

      .noDataBox {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #909399;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <!-- 添加按钮 -->
      <div class="addZoneBox">
        <el-button type="primary" @click="addDialogVisible=true">Add Zone</el-button>
      </div>
      <!-- <el-button type="primary" @click="search">search</el-button> -->
      <!-- 主Tab页 -->
      <el-tabs type="border-card" :before-leave="onBeforeLeave" v-model="activeName" class="tabBox">
        <el-tab-pane label="Zone 1" name="1">
          <card-box :zone-list="allZoneList"></card-box>
        </el-tab-pane>
        <el-tab-pane label="Zone 2" name="2">
          <card-box :zone-list="currentZone2"></card-box>
        </el-tab-pane>
        <el-tab-pane label="Zone 3" name="3">
          <card-box :zone-list="currentZone3"></card-box>
        </el-tab-pane>
      </el-tabs>
      <!-- 新增Dialog -->
      <template>
        <el-dialog
          @close="onClose('addForm')"
          title="Add Dialog"
          :show-close="false"
          :visible.sync="addDialogVisible"
          :close-on-press-escape="false"
          :close-on-click-modal="false"
        >
          <el-form :model="addForm" ref="addForm" label-width="auto" size="small" :rules="rules">
            <el-row :gutter="0">
              <el-col :span="18" class="elColBox">
                <el-form-item prop="name" label="Zone 1" class="zone-name-warp">
                  <el-input v-model="addForm.name.en" placeholder="Zone1(en)"></el-input>
                  <el-input v-model="addForm.name.zh" placeholder="Zone1(zh)"></el-input>
                  <el-input
                    v-model="addForm.name.thirdLan"
                    placeholder="Zone1(thirdLan)"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4" class="elColBox">
                <el-form-item label="Seq" prop="seq">
                  <el-input v-model.number="addForm.seq"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="2" class="elColBox">
                <i class="el-icon-circle-plus-outline" style="color: #409eff" @click="addZone2"></i>
              </el-col>
            </el-row>
            <!-- zone2 -->
            <el-row
              v-for="(zone2, zone2Index) in addForm.children"
              :key="getTimestamp"
              class="zone2Box"
              :gutter="10"
            >
              <el-col :span="15" class="elColBox">
                <el-form-item
                  :prop="'children.' + zone2Index + '.name'"
                  :label="'Zone 2 - ' + zone2Index"
                  :rules="[
              { required: true, validator: checkName,  trigger: 'change' },
            ]"
                  class="zone-name-warp"
                >
                  <el-input v-model="zone2.name.en" placeholder="zone2(en)"></el-input>
                  <el-input v-model="zone2.name.zh" placeholder="zone2(zh)"></el-input>
                  <el-input v-model="zone2.name.thirdLan" placeholder="zone2(thirdLan)"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4" class="elColBox">
                <el-form-item label="Seq" prop="seq">
                  <el-input v-model.number="zone2.seq"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="1" class="elColBox">
                <i class="el-icon-delete" @click.prevent="removeZone2(zone2)"></i>
              </el-col>
              <el-col :span="1" class="elColBox">
                <i class="el-icon-document-copy" style="color: #efd9a0" @click="addZone2"></i>
              </el-col>
              <el-col :span="1" class="elColBox">
                <i
                  class="el-icon-circle-plus-outline"
                  style="color: #409eff"
                  @click="addZone3(zone2)"
                ></i>
              </el-col>
              <!-- zone3 -->
              <el-row
                v-for="(zone3, index) in zone2?.children"
                :key="getTimestamp"
                class="zone3Box"
                :gutter="20"
              >
                <el-col :span="18" class="elColBox">
                  <el-form-item
                    :prop="'children.' + zone2Index + '.children.'+index+'.name'"
                    :label="'Zone 3 - ' + index"
                    :rules="[
                { required: true,validator: checkName,  trigger: 'change' },
              ]"
                    class="zone-name-warp"
                  >
                    <el-input v-model="zone3.name.en" placeholder="zone3(en)"></el-input>
                    <el-input v-model="zone3.name.zh" placeholder="zone3(zh)"></el-input>
                    <el-input
                      v-model="zone3.name.thirdLan"
                      placeholder="zone3(thirdLan)"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" class="elColBox">
                  <el-form-item label="Seq" prop="seq">
                    <el-input v-model.number="zone3.seq"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="1" class="elColBox">
                  <i class="el-icon-delete" @click.prevent="removeZone3(zone2,zone3)"></i>
                </el-col>
                <el-col :span="1" class="elColBox">
                  <i
                    class="el-icon-document-copy"
                    @click="addZone3(zone2)"
                    style="color: #efd9a0"
                  ></i>
                </el-col>
              </el-row>
            </el-row>
          </el-form>
          <div slot="footer">
            <el-button @click="addDialogVisible = false">Cancel</el-button>
            <el-button type="primary" @click="handleAddConfirm">Add</el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 编辑Dialog -->
      <template>
        <el-dialog
          title="Edit Dialog"
          :show-close="false"
          :visible.sync="editDialogVisible"
          :close-on-press-escape="false"
          :close-on-click-modal="false"
          @close="onClose('editForm')"
        >
          <el-form :model="editForm" ref="editForm" size="small" :rules="rules" label-width="100px">
            <el-row :gutter="0">
              <el-col :span="16" class="elColBox">
                <el-form-item prop="name" :label="'Zone ' + activeName" class="zone-name-warp">
                  <el-input
                    v-model="editForm.name.en"
                    :placeholder="'Zone '+activeName+'(en)'"
                  ></el-input>
                  <el-input
                    v-model="editForm.name.zh"
                    :placeholder="'Zone '+activeName+'(zh)'"
                  ></el-input>
                  <el-input
                    v-model="editForm.name.thirdLan"
                    :placeholder="'Zone '+activeName+'(thirdLan)'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" class="elColBox">
                <el-form-item label="Seq" prop="seq">
                  <el-input v-model.number="editForm.seq"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row
              v-for="(zone, zoneIndex) in editForm.children"
              :key="getTimestamp"
              class="zone2Box"
              :gutter="0"
            >
              <el-col :span="16" class="elColBox">
                <el-form-item
                  :prop="'children.' + zoneIndex + '.name'"
                  :label="nextLevel+ '-' + zoneIndex"
                  :rules="[
                { required: true, validator: checkName,trigger: 'change' },
              ]"
                  class="zone-name-warp"
                >
                  <el-input v-model="zone.name.en" :placeholder="nextLevel+'(en)'"></el-input>
                  <el-input v-model="zone.name.zh" :placeholder="nextLevel+'(zh)'"></el-input>
                  <el-input
                    v-model="zone.name.thirdLan"
                    :placeholder="nextLevel+'(thirdLan)'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" class="elColBox">
                <el-form-item label="Seq" prop="seq">
                  <el-input v-model.number="zone.seq"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="2" class="elColBox">
                <i class="el-icon-delete" @click.prevent="editRemoveZone(zone)"></i>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer">
            <el-button @click="editDialogVisible = false">Cancel</el-button>
            <el-button @click="editDiaAddZone" type="success" v-show="activeName!='3'">
              Add {{nextLevel}}
            </el-button>
            <el-button type="primary" @click="handleEditConfirm">Edit</el-button>
          </div>
        </el-dialog>
      </template>
    </div>

    <script>
      const checkName = (rule, value, callback) => {
        // console.log(value)
        if (value.en) {
          // console.log("成功")
          return callback()
        } else {
          return callback(new Error("Please enter Zone(en)"))
        }
      }
      const app = new Vue({
        el: "#app",
        components: {
          cardBox: cardBox
        },
        data: {
          diaClientWidth: "",
          domain: sessionStorage.getItem("domain"),
          allZoneList: [],
          currentZone2: [],
          currentZone3: [],
          activeName: "1",
          disablePass: true,
          addDialogVisible: false,
          editDialogVisible: false,
          addForm: {
            name: { en: "", zh: "", thirdLan: "" },
            children: [],
            seq: ""
          },
          editForm: {
            name: { en: "", zh: "", thirdLan: "" },
            children: [],
            seq: ""
          },
          rules: {
            name: [
              {
                required: true,
                validator: checkName,
                trigger: "change"
              }
            ]
            // zone2: [
            //   {
            //     required: false,
            //     message: "Please enter Zone2",
            //     trigger: "change"
            //   }
            // ],
            // zone3: [
            //   {
            //     required: false,
            //     message: "Please enter Zone3",
            //     trigger: "change"
            //   }
            // ]
          },
          nextLevel: "",
          hierarchyData: {},
          key: 0
        },
        created() {},
        mounted() {
          this.initDiaClientWidth() //  监听屏幕变化,当宽度小于常规笔记本页面宽度时,给el-dialog的宽度赋值
          let maxHeight = this.getTableHeight(this, 0, null)
          // console.log(maxHeight, "maxHeight")
          $(".tabBox .el-tabs__content").css({ "max-height": maxHeight, overflow: "auto" })
          // let { currentZone2, currentZone3 } = this;
          // currentZone2 = [5]
          // console.log(currentZone2, currentZone3);
          this.getData()
        },

        computed: {
          getTimestamp() {
            return Date.now()
          }
        },
        methods: {
          removeZone2(item) {
            var index = this.addForm.children.indexOf(item)
            if (index !== -1) {
              this.addForm.children.splice(index, 1)
            }
          },
          removeZone3(zone2, zone3) {
            console.log(zone2, "oo")
            var index = zone2.children.indexOf(zone3)
            if (index !== -1) {
              zone2.children.splice(index, 1)
            }
          },

          addZone2() {
            console.log("添加二级")
            this.addForm.children.push({
              name: { en: "", zh: "", thirdLan: "" },
              seq: "",
              children: []
            })
          },
          addZone3(item) {
            item.children.push({
              name: { en: "", zh: "", thirdLan: "" },
              seq: ""
            })
          },
          editDiaAddZone() {
            this.editForm.children.push({
              name: { en: "", zh: "", thirdLan: "" },
              seq: "",
              children: []
            })
          },
          editRemoveZone(item) {
            var index = this.editForm.children.indexOf(item)
            if (index !== -1) {
              this.editForm.children.splice(index, 1)
            }
          },
          getData() {
            let level = Number(this.activeName)
            $.get({
              url: "../../manager_Zone/getAllByDomain",
              dataType: "json",
              data: { domain: this.domain },
              success: res => {
                if (res.statusCode === 200) {
                  if (level != 1) {
                    console.log(level, "level")
                    let levelId = this.hierarchyData[level - 1].id
                    console.log(levelId, "levelId")
                    let getData = this.getDeepArrData(res.data, levelId, "id", "children")
                    this["currentZone" + this.activeName] = getData.children
                    console.log(getData, "getData")
                    this.setHierarchyData(level - 1, getData)
                  }
                  if (level == 3) {
                    let getData = this.getDeepArrData(
                      res.data,
                      this.hierarchyData[1].id,
                      "id",
                      "children"
                    )
                    this.currentZone2 = getData.children
                    this.setHierarchyData(1, getData)
                  }
                  this.allZoneList = res.data
                  console.log(JSON.parse(JSON.stringify(res.data)), " res.data")
                } else {
                  this.$message.error("Request failed, please try again")
                }
              },
              error: error => {
                console.log(error, "error")
                this.$message.error("Request failed, please try again")
              }
            })
          },
          //新增确认按钮
          handleAddConfirm() {
            this.$refs["addForm"].validate(valid => {
              if (!valid) return
              console.log(JSON.parse(JSON.stringify(this.addForm)), "this.addForm")
              let data = {
                ...this.addForm,
                domain: this.domain
              }
              $.post({
                url: "../../manager_Zone/insertAndUpdate",
                // data: { data: JSON.stringify(this.addForm) }  post上传之后，空数组会被过滤掉,使用 ,
                data: JSON.stringify(data),
                // data: data,
                dataType: "json",
                contentType: "application/json;charset=UTF-8",
                traditional: true, // 防止深度序列号
                success: res => {
                  // console.log(res, "添加成功")
                  if (res.statusCode == 200) {
                    this.getData()
                    // console.log(res);
                    this.$message.success("Successfully added！")
                    this.addDialogVisible = false
                  } else {
                    switch (res.statusCode) {
                      case 401:
                        this.$message.error("Data for this store already exists！")
                        break
                      default:
                        this.$message.error("Fail to add！")
                        break
                    }
                  }
                },
                error: error => {
                  // console.log(res);
                  this.$message.error("Fail to add！")
                },
                complete: () => {
                  // this.btnLoading = false
                }
              })
            })
          },
          handleEditConfirm() {
            this.$refs["editForm"].validate(valid => {
              if (!valid) return
              console.log(this.hierarchyData, "hierarchyData")
              console.log(JSON.parse(JSON.stringify(this.editForm)), "this.editForm")
              let data = this.SplicingData(this.editForm)
              console.log(JSON.parse(JSON.stringify(data)), "编辑的数据")
              $.post({
                url: "../../manager_Zone/insertAndUpdate",
                data: JSON.stringify(data),
                dataType: "json",
                contentType: "application/json;charset=UTF-8",
                traditional: true, // 防止深度序列号
                success: res => {
                  if (res.statusCode == 200) {
                    this.getData()
                    // console.log(res);
                    this.$message.success("Edit Success！")
                    this.editDialogVisible = false
                  } else {
                    this.$message.error("Edit Failure！")
                  }
                },
                error: error => {
                  // console.log(res);
                  this.$message.error("Edit Failure！")
                }
              })
            })
          },
          onClose(type) {
            this[type] = {
              name: { en: "", zh: "", thirdLan: "" },
              children: [],
              seq: ""
            }
            this.$refs[type].resetFields()
          },

          onBeforeLeave(activeName, oldActiveName) {
            if (activeName > oldActiveName && this.disablePass) {
              return false
            } else {
              this.disablePass = true
              // return false;
            }
          },
          onNextPage(item) {
            // this.activeName = String(Number(this.activeName) + 1);
            // this.disablePass = false
            if (item.children?.length > 0) {
              this.setHierarchyData(this.activeName, item)
              let activeName = String(Number(this.activeName) + 1)
              this["currentZone" + activeName] = item.children
              this.activeName = activeName
              this.disablePass = false
            }
          },
          //设置层数
          setHierarchyData(activeName, item) {
            this.hierarchyData[activeName] = {
              ...item,
              children: []
            }
            // 第一层数据需要增加domain字段
            if (activeName == "1") this.hierarchyData["1"]["domain"] = this.domain
          },
          // 组合数据
          SplicingData(item) {
            let activeName = this.activeName
            let upperData
            let cloneItem = JSON.parse(JSON.stringify(item))
            switch (activeName) {
              case "2":
                upperData = {
                  ...this.hierarchyData["1"],
                  children: [cloneItem]
                }
                break
              case "3":
                this.hierarchyData["2"].children = [cloneItem]
                upperData = {
                  ...this.hierarchyData["1"],
                  children: [this.hierarchyData["2"]]
                }
                break
              default:
                upperData = {
                  ...cloneItem,
                  domain: this.domain
                }
                break
            }
            return upperData
          },
          // 回显当前层数据
          getDeepArrData(arr, id, idStr = "id", child = "children") {
            cLabel = undefined
            for (var i = 0; arr && i < arr.length; i++) {
              let item = arr[i]
              if (item[idStr] == id) {
                cLabel = item
                // console.log('return id:' + id + ' cId:' + item[idStr] + ' value:' + cLabel);
                return cLabel
              }

              if (Array.isArray(item[child]) && item[child].length > 0) {
                cLabel = this.getDeepArrData(item[child], id, idStr, child, name)
                if (cLabel != undefined) return cLabel
              }
            }
          },
          search() {
            this.getData()
          },
          getTableHeight(that, val = 32, name) {
            let fixHeight = 16 // padding为8,8
            let searchFormHeight = name ? that.$refs[name].clientHeight : 0 // 可变的查询高度
            let pageHeight = document.documentElement.clientHeight // 可用窗口的高度
            let tableHeight = Number(pageHeight - (searchFormHeight + val) - 230) // 计算完之后剩余table可用的高度
            // console.log(pageHeight, searchFormHeight, tableHeight, "searchFormHeight")
            return tableHeight
          },
          // 处理弹窗宽度
          initDiaClientWidth() {
            if (document.body.clientWidth < 1300) {
              this.diaClientWidth = "70%"
            }
            window.onresize = () => {
              return (() => {
                if (document.body.clientWidth < 1300) {
                  this.diaClientWidth = "70%"
                } else {
                  this.diaClientWidth = "48%"
                }
              })()
            }
          }
        }
      })
    </script>
  </body>
</html>
