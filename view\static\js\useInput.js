const useInput = {
    props: {
        placeholder: {
            type: String,
            default: ""
        },
        errorMsg: {
            type: String,
            default: ''
        },
        verifyFn: {
            type: Function,
            default: (e) => {
                return  !!e
            }
        },
        name:{
            type:String,
            required:true,
            default:'App'
        }
    },
    data() {
        return {
            value: '',
            errorStatus: false,
            errClass:'useInputError'
        }
    },
    mounted(){
        this.init()
    },
    methods: {
        init(){
            let dom = this.$refs['useInput']
            let initStyle=`
                    display: flex;
                    flex-direction: column;
                    font-size: small;
                    width:100%;
            `
            dom.setAttribute('style',initStyle)
            dom.classList.remove(this.errClass)
            this.errorStatus=false
        },
        initData(){
          this.value=""
        },
        errorClass() {
            let dom = this.$refs['useInput']
            dom.classList.add(this.errClass)
        },
        //派发事件
        dispatch(componentName, eventName, ...rest) {
            let parent = this.$parent || this.$root;
            let name = parent.$options.name;
            while (parent && (!name || name !== componentName)) {
                parent = parent.$parent;
                if (parent) {
                    name = parent.$options.name;
                }
            }
            if (parent) {
                parent.$off(eventName)
                parent.$emit.apply(parent, [eventName].concat(rest));
            }
        },
        verify() {
            this.init()
            if (this.verifyFn(this.value)) {
                //通过校验
                this.errorStatus=false
                // this.dispatch(this.name,'verify', true)
                this.$emit('value',this.value)

            } else {
                // this.dispatch(this.name,'verify', false)
                if (this.errorMsg) {
                    this.errorStatus = true
                    this.errorClass()
                }
            }
        },
        handleInputChange(){
            if (this.value){
                this.init()
            }
        }

    },
    template: `
        <div class="useInput" ref="useInput">
            <input ref='input' type="text" @change="handleInputChange" :placeholder="placeholder" v-model="value" class="layui-input"/>   
<!--             <span  v-if="errorMsg&&errorStatus">{{errorMsg}}</span> -->
        </div>
        
        `
}
