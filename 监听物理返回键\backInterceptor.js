/**
 * 物理返回键拦截器
 * 兼容 iOS Safari、Android Chrome、微信内置浏览器
 * 默认一直拦截，无弹窗提示
 */
;(function () {
  // 设备检测
  const ua = navigator.userAgent
  const isIOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream
  const isAndroid = /Android/.test(ua)
  const isWechat = /MicroMessenger/.test(ua)

  // 状态管理
  let historyDepth = 0
  let lastPopstateTime = 0
  const interceptorKey = "backInterceptor"
  const debounceDelay = 50

  // 获取最佳历史记录深度
  function getOptimalDepth() {
    if (isIOS) {
      return isWechat ? 2 : 3 // iOS 使用较少层级
    } else if (isAndroid) {
      return isWechat ? 5 : 8 // Android 可以使用更多层级
    } else {
      return 5 // 其他设备使用中等层级
    }
  }

  // 创建历史记录堆栈
  function createHistoryStack() {
    const depth = getOptimalDepth()

    for (let i = 0; i < depth; i++) {
      historyDepth++
      const state = {
        [interceptorKey]: true,
        depth: historyDepth,
        timestamp: Date.now()
      }
      history.pushState(state, "", location.href)
    }

    console.log("返回键拦截器已初始化", {
      device: isIOS ? "iOS" : isAndroid ? "Android" : "Other",
      isWechat: isWechat,
      depth: historyDepth
    })
  }

  // 验证拦截器状态
  function verifyInterceptor() {
    // 检查当前历史状态
    const currentState = history.state
    const hasValidState = currentState && currentState[interceptorKey]

    if (!hasValidState) {
      console.warn("检测到无效的历史状态，重建拦截器")
      createHistoryStack(true)
    }

    return hasValidState
  }

  // 维护历史状态
  function maintainHistoryState() {
    setTimeout(() => {
      try {
        historyDepth++
        const newState = {
          [interceptorKey]: true,
          depth: historyDepth,
          timestamp: Date.now()
        }
        history.pushState(newState, "", location.href)
      } catch (error) {
        console.error("维护历史状态失败:", error)
        rebuildInterceptor()
      }
    }, 0)
  }

  // 重建拦截器
  function rebuildInterceptor() {
    console.log("重建拦截器...")
    historyDepth = 0

    const depth = getOptimalDepth()
    for (let i = 0; i < depth; i++) {
      historyDepth++
      const state = {
        [interceptorKey]: true,
        depth: historyDepth,
        timestamp: Date.now()
      }
      history.pushState(state, "", location.href)
    }
  }

  // 处理 popstate 事件（核心拦截逻辑）
  function handlePopstate(event) {
    // 防抖处理
    const now = Date.now()
    if (now - lastPopstateTime < debounceDelay) {
      console.log("防抖拦截，忽略快速连续触发")
      return false
    }
    lastPopstateTime = now

    console.log("物理返回键被拦截", {
      state: event.state,
      historyDepth: historyDepth,
      timestamp: new Date().toISOString()
    })

    // 检查是否是我们的拦截状态
    const isOurState = event.state && event.state[interceptorKey]

    // 立即阻止默认行为
    if (event.preventDefault) {
      event.preventDefault()
    }
    if (event.stopPropagation) {
      event.stopPropagation()
    }

    // 无论什么情况都拦截并维护历史状态
    maintainHistoryState()

    // 如果不是我们的状态，立即重建拦截器
    if (!isOurState) {
      console.warn("检测到非拦截器状态，立即重建")
      setTimeout(() => rebuildInterceptor(), 10)
    }

    // 强制返回 false 阻止导航
    return false
  }

  // 强化初始化拦截器
  function initInterceptor() {
    initAttempts++

    if (!window.history || !window.history.pushState) {
      console.warn("浏览器不支持 History API")
      return false
    }

    if (isInitialized) {
      console.log("拦截器已初始化，验证状态...")
      verifyInterceptor()
      return true
    }

    console.log(`开始初始化拦截器 (尝试 ${initAttempts}/${maxInitAttempts})`)

    try {
      // 立即添加事件监听器（优先级最高）
      window.addEventListener("popstate", handlePopstate, false)

      // 立即创建历史记录堆栈
      createHistoryStack()

      // iOS 特殊处理
      if (isIOS) {
        window.addEventListener(
          "pagehide",
          function () {
            console.log("iOS pagehide 事件")
          },
          false
        )

        window.addEventListener(
          "hashchange",
          function (event) {
            console.log("iOS hashchange 事件")
            event.preventDefault()
            maintainHistoryState()
            return false
          },
          false
        )
      }

      // 标记为已初始化
      isInitialized = true

      // 延迟验证拦截器状态
      setTimeout(() => {
        verifyInterceptor()
      }, 100)

      console.log("拦截器初始化完成")
      return true
    } catch (error) {
      console.error("拦截器初始化失败:", error)

      // 如果还有重试机会，延迟重试
      if (initAttempts < maxInitAttempts) {
        setTimeout(() => {
          console.log("重试初始化拦截器...")
          initInterceptor()
        }, 200)
      }

      return false
    }
  }

  // 多重初始化保障机制
  function ensureInitialization() {
    // 立即尝试初始化
    initInterceptor()

    // 延迟备用初始化
    setTimeout(() => {
      if (!isInitialized) {
        console.log("备用初始化触发")
        initInterceptor()
      }
    }, 50)

    // 最终保障初始化
    setTimeout(() => {
      if (!isInitialized) {
        console.log("最终保障初始化触发")
        initInterceptor()
      }
    }, 500)
  }

  // 立即开始初始化（不等待任何事件）
  ensureInitialization()

  // 页面加载事件的额外保障
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      if (!isInitialized) {
        console.log("DOMContentLoaded 触发初始化")
        initInterceptor()
      }
    })
  }

  // 页面完全加载后的最终检查
  window.addEventListener("load", () => {
    if (!isInitialized) {
      console.log("window.load 触发初始化")
      initInterceptor()
    } else {
      // 已初始化，进行状态验证
      setTimeout(() => verifyInterceptor(), 100)
    }
  })

  // 暴露全局方法
  window.clearBackInterceptor = function () {
    try {
      window.removeEventListener("popstate", handlePopstate)
      isInitialized = false
      historyDepth = 0
      console.log("返回键拦截器已清理")
      return true
    } catch (error) {
      console.error("清理拦截器失败:", error)
      return false
    }
  }

  // 暴露状态检查方法（调试用）
  window.checkBackInterceptor = function () {
    return {
      isInitialized: isInitialized,
      historyDepth: historyDepth,
      device: isIOS ? "iOS" : isAndroid ? "Android" : "Other",
      isWechat: isWechat,
      currentState: history.state,
      initAttempts: initAttempts
    }
  }

  console.log("物理返回键拦截器脚本加载完成")
})()
