!(function () {
  const n = { SPACE: 32, TAB: 9 },
    e = document.createElement("template")
  class t extends HTMLElement {
    static get observedAttributes() {
      return ["checked", "disabled"]
    }
    constructor() {
      super()
      var n = this.attachShadow({ mode: "open" })
      ;(e.innerHTML =
        ' \n        <div class="switch">\n        <label for="jelly-switch" id="content-left"><slot name="content-left"></slot></label>\n        <input type="checkbox" name="jelly-switch" id="jelly-switch"></input>\n        <label for="jelly-switch" id="switch">\n        <p id="jelly-content"></p>\n        </label>\n        \n        <label for="jelly-switch" id="content-right"><slot name="content-right"></slot></label>\n        </div>\n        <style>\n        :host(:focus)\n        {\n            outline:none;\n            --highlight-scale:12px;\n        }\n        :host([disabled]) label\n        {\n            cursor:not-allowed;\n            opacity:0.5;\n            --highlight-scale:2px;\n        }\n        input#jelly-switch[type=checkbox]{\n            height: 0;\n            width: 0;\n            visibility: hidden;\n        }\n        .switch \n        {\n            display:inline-block;\n        }\n        label\n        {\n            cursor: pointer;\n        }\n        label#switch{\n            text-indent: -9999px;\n            width: 50px;\n            height: 25px;\n            display: inline-block;\n            border-radius: 100px;\n            position: relative;\n            outline:none;\n            -ms-user-select: none; \n            -webkit-touch-callout: none;\n             -webkit-user-select: none;\n             -khtml-user-select: none;\n             -moz-user-select: none;\n             -ms-user-select: none;\n             user-select: none;\n             -webkit-tap-highlight-color: transparent;\n            background: var(--off-color,#FF4651);\n            box-shadow: 0 1px var(--highlight-scale,4px) -1px var(--off-color,#FF4651);\n            transition: .2s ease-in-out;\n        }\n        p#jelly-content {\n            box-sizing:border-box;\n            content: \'\';\n            position: absolute;\n            top: -14px;\n            left: 2px;\n            width: 21px;\n            height: 21px;\n            background:var(--offHandle-color,#ffffff);\n            border-radius: 21px;\n            transition: background  .5s ease-in;\n        }\n        label#content-left\n        {\n            position:relative;\n            display: inline-block;\n            top:-7px;\n            height: 0px;\n            transition:0.3s ease-in;\n            padding-left:0px;\n        }\n        label#content-right\n        {\n            position:relative;\n            display: inline-block;\n            top:-7px;   \n            height: 0px;\n            transition:0.3s ease-in;\n            padding-right:0px;\n        }\n        :host([aria-checked = "false"]) p#jelly-content\n        {\n            animation:expand-left 0.5s linear forwards;\n        }\n        :host([aria-checked = "true"][checked]) label#switch {\n            background: var(--on-color,#11c75d);\n            box-shadow: 0 2px var(--highlight-scale,4px) -1px var(--on-color,#11c75d);\n        }\n\n        :host([aria-checked = "true"]) p#jelly-content {\n            background:var(--onHandle-color,#ffffff);\n            \n            animation:expand-right .5s linear forwards;\n            transition: background  .5s ease-in;\n        }\n\n        @-webkit-keyframes expand-right\n        {\n            0%\n            {\n                left:2px;\n            }\n            30%,50% \n            {\n                left:2px;\n                width:46px;\n                \n            }\n            60%\n            {\n                left:34px;\n                width:14px;\n            }\n            80%\n            {\n                left:24px;\n                width:24px;   \n            }\n            90%\n            {\n                left:29px;\n                width:19px;  \n            }\n            100%\n            {\n                left:27px;\n                width:21px;\n            }\n        }\n\n        @keyframes expand-right\n        {\n            0%\n            {\n                left:2px;\n            \n            }\n            30%,50%   \n            {\n                left:2px;\n                width:46px;\n                \n            }\n            \n            60%\n            {\n                left:34px;\n                width:14px;\n            }\n            80%\n            {\n                left:24px;\n                width:24px;   \n            }\n            90%\n            {\n                left:29px;\n                width:19px;  \n            }\n            100%\n            {\n                left:27px;\n                width:21px;\n            }\n        }\n\n        @-webkit-keyframes expand-left\n        {\n            0%\n            {\n                left:27px;\n            }\n            30%,50%\n            {\n                left:2px;\n                width:46px;\n            }\n            60%\n            {\n                right:34px;\n                width:14px;\n            }\n            80%\n            {\n                right:24px;\n                width:24px;   \n            }\n            90%\n            {\n                right:29px;\n                width:19px;  \n            }\n            100%\n            {\n                left:2px;\n                width:21px;\n            }\n        }\n\n        @keyframes expand-left\n        {\n            0%\n            {\n                left:27px;\n            }\n            30%,50%\n            {\n                left:2px;\n                width:46px;\n            }\n            60%\n            {\n                right:34px;\n                width:14px;\n            }\n            80%\n            {\n                right:24px;\n                width:24px;   \n            }\n            90%\n            {\n                right:29px;\n                width:19px;  \n            }\n            100%\n            {\n                left:2px;\n                width:21px;\n            }\n        }\n        </style>    '),
        n.appendChild(e.content.cloneNode(!0)),
        (this._jellySwitchDiv = n.getElementById("jelly-switch"))
    }
    connectedCallback() {
      if (
        (this._upgradeProperty("checked"),
        this._upgradeProperty("disabled"),
        this.hasAttribute("role") || this.setAttribute("role", "switch"),
        this.hasAttribute("tabindex") || this.setAttribute("tabindex", 0),
        this._jellySwitchDiv)
      ) {
        this._jellySwitchDiv.addEventListener("click", this._handleClickAndToggle.bind(this)),
          this.addEventListener("keyup", this._handleKeyPress)
      }
    }
    disconnectedCallback() {
      this._jellySwitchDiv.removeEventListener("click", this._handleClickAndToggle),
        this.removeEventListener("keyup", this._handleKeyPress)
    }
    get checked() {
      return this._jellySwitchDiv.checked
    }
    set checked(n) {
      "boolean" == typeof n
        ? n
          ? this.setAttribute("checked", "")
          : this.removeAttribute("checked")
        : console.warn("checked function of jelly-switch.js file allows only boolean value")
    }
    get disabled() {
      return this._jellySwitchDiv.disabled
    }
    set disabled(n) {
      "boolean" == typeof n
        ? ((this._jellySwitchDiv.disabled = n),
          n ? this.setAttribute("disabled", "") : this.removeAttribute("disabled"))
        : console.warn("disabled function of jelly-switch.js file allows only boolean value")
    }
    attributeChangedCallback(n, e, t) {
      const i = null !== t
      switch (n) {
        case "checked":
          this.setAttribute("aria-checked", i), (this._jellySwitchDiv.checked = i)
          break
        case "disabled":
          this.setAttribute("aria-disabled", i),
            (this._jellySwitchDiv.disabled = i),
            i ? (this.removeAttribute("tabindex"), this.blur()) : this.setAttribute("tabindex", 0)
      }
    }
    _handleClickAndToggle() {
      this.disabled ||
        ((this.checked = this._jellySwitchDiv.checked),
        this.dispatchEvent(
          new CustomEvent("toggle", { bubbles: !0, detail: { value: this.checked } })
        ))
    }
    _handleKeyPress(e) {
      if (!this.disabled && !e.altKey)
        switch (e.keyCode) {
          case n.SPACE:
            e.preventDefault(),
              (this._jellySwitchDiv.checked = !this._jellySwitchDiv.checked),
              this._handleClickAndToggle()
        }
    }
    _upgradeProperty(n) {
      if (this.hasOwnProperty(n)) {
        let e = this[n]
        delete this[n], (this[n] = e)
      }
    }
  }
  window.customElements && customElements.define("jelly-switch", t)
})()
