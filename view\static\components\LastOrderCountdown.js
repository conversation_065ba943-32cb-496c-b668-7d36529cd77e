function toSorted(compareFn) {
  if (!Array.isArray(this)) {
    throw new TypeError("toSorted() requires an array as the first argument")
  }
  const sortedArray = [...this]
  if (!compareFn) {
    compareFn = (a, b) => {
      if (a === undefined) return 1
      if (b === undefined) return -1
      return a.toString().localeCompare(b.toString())
    }
  }
  sortedArray.sort(compareFn)
  return sortedArray
}
if (!Array.prototype.toSorted) {
  Array.prototype.toSorted = toSorted
}

Vue.component("meal-order-tips-row", {
  props: ["tips", "value", "color"],
  data() {
    return {
      countdownStyle: {
        color: "var(--styleColor)",
        fontSize: "0.35rem",
        minHeight: "24px",
        alignContent: "center",
        alignSelf: "center",
        textAlign: "center",
        width: "45%"
      }
    }
  },
  template: `
        <div :style="countdownStyle" v-if="Array.isArray(tips)">{{tips[0]}}
          <span style="font-size: 0.4rem" :style="{color:color}">
            {{value}}
          </span>
          {{tips[1]}}
        </div>
        <div v-else :style="countdownStyle">
          {{tips}}
        </div>
  `
})
Vue.component("meal-order-timer-tips", {
  props: [
    "deadline",
    "status",
    "orderStartTime",
    "countdownTips",
    "deadlineTips",
    "timeoutTips",
    "startTimeTips",
    "bgColor"
  ],
  data() {
    return {
      TIMEOUT: 0,
      WAITING: 1,
      COUNTING: 2,
      countdown: "",
      dividerStyle: {
        margin: "0 0.2rem",
        height: "80%",
        borderRight: "1px solid #c7c7c7",
        alignSelf: "center"
      }
    }
  },
  computed: {
    startTime() {
      return this.timestampToTimeString(this.orderStartTime)
    },
    endTime() {
      return this.hhMmSsToHhMm(this.deadline)
    },
    stTips() {
      let contain = this.startTimeTips.includes("#orderStartTime")
      if (contain) {
        let [before = "", after = ""] = this.startTimeTips.toString().split("#orderStartTime")
        return [before, after]
      }
      return this.startTimeTips
    },
    cdTips() {
      let contain = this.countdownTips.includes("#countdown")
      if (contain) {
        let [before = "", after = ""] = this.countdownTips.toString().split("#countdown")
        return [before, after]
      }
      return this.countdownTips
    },
    dlTips() {
      let contain = this.deadlineTips.includes("#deadline")
      if (contain) {
        let [before = "", after = ""] = this.deadlineTips.toString().split("#deadline")
        return [before, after]
      }
      return this.deadlineTips
    }
  },
  mounted() {
    // countdown不再经过parent转发<会导致每秒重新渲染根组件>
    this.$parent.$refs["meal-order-timer"].$on("countdown", value => {
      // 小于一小时,则不显示小时部分
      if (value.startsWith("00:")) {
        this.countdown = value.replace("00:", "")
      } else {
        this.countdown = value
      }
    })
  },
  methods: {
    timestampToTimeString(timestamp) {
      if (!timestamp || isNaN(timestamp)) {
        throw new Error("Invalid timestamp")
      }
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, "0")
      const minutes = date.getMinutes().toString().padStart(2, "0")
      return `${hours}:${minutes}`
    },
    hhMmSsToHhMm(timeString) {
      let match = timeString.split(":")
      if (match.length > 1) {
        const hours = match[0]
        const minutes = match[1]
        return `${hours}:${minutes}`
      } else {
        throw new Error("Invalid time format")
      }
    }
  },
  template: `
      <template v-if="status===this.TIMEOUT">
          <div style="color: var(--styleColor);
                      font-size: 0.35rem;
                      text-align: center;
                      min-height: 24px;
                      padding: 0.1rem;
                      line-height: 24px" :style="{ backgroundColor: bgColor }">
            {{ timeoutTips }}
          </div>
      </template>
      <div v-else-if="status===this.WAITING" style="display: flex;padding: 0.1rem; justify-content: space-around;" :style="{ backgroundColor: bgColor }">
        <meal-order-tips-row :tips="stTips" :value="startTime"></meal-order-tips-row>
        <span :style="dividerStyle"></span>
        <meal-order-tips-row :tips="dlTips" :value="endTime"></meal-order-tips-row>
      </div>
      <div v-else-if="status===this.COUNTING" style="display: flex;padding: 0.1rem;justify-content: space-around;" :style="{ backgroundColor: bgColor }">
        <meal-order-tips-row :tips="stTips" :value="startTime" ></meal-order-tips-row>
        <span :style="dividerStyle"></span>
        <meal-order-tips-row :tips="cdTips" :value="countdown" color="red" ></meal-order-tips-row>
      </div>
  `
})

Vue.component("meal-order-timer", {
  name: "meal-order-timer",
  props: ["orderStartTime", "orderTimeLimit", "dynamicList"],
  data() {
    return {
      waitTimer: null,
      invTimer: null,
      longestOrderTime: 0, //订单持续时间
      countdownTime: 0 //倒计时时间
    }
  },
  computed: {
    // 排序时间段
    orderPeriod() {
      const regex = /(\d{2}:\d{2})-(\d{2}:\d{2})/
      return this.orderTimeLimit
        .reduce((prev, cur) => {
          let { period } = cur
          if (!period) return prev
          let inc = period.includes(";")
          if (inc) {
            let list = period.split(";").filter(e => e.trim())
            prev.push(
              ...list.map(e => {
                return { ...cur, period: e }
              })
            )
          } else {
            prev.push(cur)
          }
          return prev
        }, [])
        .map(pd => {
          let period = pd.period.replace(regex, "$1:00-$2:00")
          return { ...pd, period }
        })
        .toSorted((t1, t2) => {
          let s1 = t1.period.split("-")[0]
          let s2 = t2.period.split("-")[0]
          return this.hhmmssToTimestamp(s1) - this.hhmmssToTimestamp(s2)
        })
    },
    // 最后允许下单时间
    lastMinute() {
      let start = this.timestampToTimeString(+this.orderStartTime || 0)
      return this.timeAdd(start, this.formatMinutes(this.longestOrderTime))
    },
    // 触发倒计时的时间
    countdownMinute() {
      let start = this.timestampToTimeString(+this.orderStartTime || 0)
      return this.timeAdd(start, this.formatMinutes(this.longestOrderTime - this.countdownTime))
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (Array.isArray(this.dynamicList) && this.dynamicList.length) {
        this.longestOrderTime = this.dynamicList[0]
        this.countdownTime = this.dynamicList[1]
      } else {
        let period = this.getPeriod()
        if (!period) return false //未在时间段内
        this.longestOrderTime = period.longestOrderTime
        this.countdownTime = period.countdown
      }
      console.log("开始订单时间:", this.timestampToTimeString(this.orderStartTime))
      console.log("开始倒计时时间:", this.countdownMinute)
      console.log("结束订单时间:", this.lastMinute)
      lifecycle.addEventListener("statechange", this.timerCall)
      this.start()
    },
    //
    start() {
      this.$emit("update:order-timer-status", this.orderTimerStatus())
      switch (this.orderTimerStatus()) {
        case 0:
          //超时
          lifecycle.removeEventListener("statechange", this.timerCall)
          break
        case 1:
          //等待进入倒计时
          this.$emit("update:order-deadline", this.lastMinute)
          this.runWaitTimer()
          break
        case 2:
          //正在倒计时
          this.runCountdownTimer()
          break
        default:
          break
      }
    },
    //执行倒计时计时器
    runCountdownTimer() {
      this.invTimer && this.invTimer.clear()
      let countdown = this.getTimeLeft()
      this.invTimer = new Timer(() => {
        this.$emit("countdown", countdown)
        countdown = this.timeDecrement(countdown, 1, "s")
      }, 1000)
    },
    //执行等待倒计时的计时器
    runWaitTimer() {
      this.waitTimer && this.waitTimer.clear()
      // let seconds = this.getSecondsToTargetTime(this.countdownMinute)
      // let list = this.splitIntegerRandomImproved(Math.abs(+seconds))
      this.waitTimer = new Timer(() => {
        let cdTimestamp = this.hhmmssToTimestamp(this.countdownMinute)
        let curTimestamp = this.hhmmssToTimestamp(this.getCurrentTimeString())
        if (curTimestamp >= cdTimestamp) {
          this.waitTimer.clear()
          this.start()
        }
      }, 1000)
    },
    //是否超时
    timeout() {
      // 当前时间 > 最后允许落单时间 === 超时
      let end = this.addTimeStringToTimestamp(
        this.orderStartTime,
        this.formatMinutes(this.longestOrderTime)
      )
      let cur = new Date().getTime()
      return cur >= end
    },
    waiting() {
      // 当前时间 < 倒计时时间 === 等待倒计时中
      let end = this.addTimeStringToTimestamp(
        this.orderStartTime,
        this.formatMinutes(this.longestOrderTime - this.countdownTime)
      )
      let cur = new Date().getTime()
      return cur < end
    },

    // 订单计时器状态
    orderTimerStatus() {
      if (this.timeout()) {
        return 0
      }
      if (this.waiting()) {
        return 1
      }
      return 2
    },
    // 找到开台时间处于的时间段
    getPeriod() {
      return this.orderPeriod.find(pi => {
        return this.betweenInPeriod(this.orderStartTime, pi.period)
      })
    },
    // 获取倒计时的最新时间
    getTimeLeft() {
      // 最后时间-当前时间 > 倒计时  ?倒计时:最后时间-当前时间
      return this.timeSub(this.lastMinute, this.getCurrentTimeString())
    },
    /**
     * Converts an HH:mm:ss string to a timestamp (milliseconds).
     * @param {string} timeString The time string in the format HH:mm:ss.
     * @returns {number} The timestamp in milliseconds.
     */
    hhmmssToTimestamp(timeString) {
      const [hours, minutes, seconds] = timeString.split(":")
      const hoursInt = parseInt(hours)
      const minutesInt = parseInt(minutes)
      const secondsInt = parseInt(seconds)
      const timestamp = (hoursInt * 3600 + minutesInt * 60 + secondsInt) * 1000
      return timestamp
    },
    /**
     * @description 判断一个时间字符串是否大于另一个时间字符串
     * @param time1 {string} 第一个时间，格式为 "HH:mm:ss"
     * @param time2 {string} 第二个时间，格式为 "HH:mm:ss"
     * @returns {boolean} True if time1 is greater than time2, False otherwise
     * @example isTime1GreaterThanTime2('23:59:59', '01:00:01') // Output: false
     */
    isTime1GreaterThanTime2(time1, time2) {
      if (!time1 || !time2) {
        throw new Error("Invalid time inputs")
      }
      const [hours1, minutes1, seconds1] = time1.split(":").map(Number)
      const [hours2, minutes2, seconds2] = time2.split(":").map(Number)
      const totalSeconds1 = hours1 * 3600 + minutes1 * 60 + seconds1
      const totalSeconds2 = hours2 * 3600 + minutes2 * 60 + seconds2

      return totalSeconds1 > totalSeconds2
    },
    /**
     * @description 将时间搓转换为时间字符串 "HH:mm:ss"
     * @param timestamp {number} Timestamp in milliseconds
     * @returns {string} Formatted time string ("HH:mm:ss")
     * @example timestampToTimeString(1671047720000) // Output: "03:55:20"
     */
    timestampToTimeString(timestamp) {
      if (!timestamp || isNaN(timestamp)) {
        throw new Error("Invalid timestamp")
      }
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, "0")
      const minutes = date.getMinutes().toString().padStart(2, "0")
      const seconds = date.getSeconds().toString().padStart(2, "0")

      return `${hours}:${minutes}:${seconds}`
    },
    /**
     * @description 判断某个时间搓是否再某个时间段中
     * @param timestamp {number} 时间搓
     * @param period {string} "hh:mm:ss-hh:mm:ss"
     * @return {boolean} 是否
     * */
    betweenInPeriod(timestamp, period) {
      let time = this.timestampToTimeString(timestamp)
      let [start, end] = period.split("-")
      return this.isTime1GreaterThanTime2(time, start) && this.isTime1GreaterThanTime2(end, time)
    },
    /**
     * @description 格式化时间,将分钟转为完整的时间
     * @param minutes {number}
     * @return {string} "HH:mm:ss"
     * @example: formatMinutes(120):02:00:00
     * */
    formatMinutes(minutes) {
      if (isNaN(minutes) || minutes < 0) {
        throw new Error("Invalid minutes value")
      }
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      const formattedHours = hours.toString().padStart(2, "0")
      const formattedMinutes = remainingMinutes.toString().padStart(2, "0")
      return `${formattedHours}:${formattedMinutes}:00`
    },
    // 将秒格式化为 hh:mm:ss
    format(seconds) {
      const newHours = Math.floor(seconds / 3600)
      const newMinutes = Math.floor((seconds % 3600) / 60)
      const newSeconds = seconds % 60
      return `${newHours.toString().padStart(2, "0")}:${newMinutes
        .toString()
        .padStart(2, "0")}:${newSeconds.toString().padStart(2, "0")}`
    },
    /**
     * @description 时间递减函数
     * @param start {string} 开始时间
     * @param step {number} 每次减少的时间
     * @param unit {'h'|'m'|'s'} h/m/s  时分秒
     * @returns 递减后的时间
     * */
    timeDecrement(start, step, unit = "s") {
      if (!start || step === 0) {
        throw new Error("Invalid parameters")
      }

      const [hours, minutes, seconds] = start.split(":").map(Number)

      let totalSeconds = hours * 3600 + minutes * 60 + seconds

      switch (unit) {
        case "h":
          totalSeconds -= step * 3600
          break
        case "m":
          totalSeconds -= step * 60
          break
        case "s":
          totalSeconds -= step
          break
        default:
          throw new Error("Invalid unit")
      }

      if (totalSeconds < 0) {
        this.invTimer.clear()
        this.$emit("update:order-timer-status", this.orderTimerStatus())
        lifecycle.removeEventListener("statechange", this.timerCall)
      }
      return this.format(totalSeconds)
    },
    /**
     * @description 时间相加
     * @param time1 {string} 第一个时间，格式为 "HH:mm:ss"
     * @param time2 {string} 第二个时间，格式为 "HH:mm:ss"
     * @param neglectDay {boolean} 是否忽略超出 24 小时的部分，默认为 true
     * @returns {string} 相加后的时间，格式为 "HH:mm:ss"
     * @example timeAdd('01:23:45', '02:34:56') // Output: '03:58:41'
     */
    timeAdd(time1, time2, neglectDay = true) {
      if (!time1 || !time2) {
        throw new Error("Invalid time inputs")
      }
      const [hours1, minutes1, seconds1] = time1.split(":").map(Number)
      const [hours2, minutes2, seconds2] = time2.split(":").map(Number)

      let totalSeconds =
        hours1 * 3600 + minutes1 * 60 + seconds1 + hours2 * 3600 + minutes2 * 60 + seconds2

      if (neglectDay) {
        totalSeconds %= 86400 // 86400 seconds in a day
      }
      return this.format(totalSeconds)
    },
    /**
     * @description 时间相减
     * @param time1 {string} 第一个时间，格式为 "HH:mm:ss"
     * @param time2 {string} 第二个时间，格式为 "HH:mm:ss"
     * @returns {string} 相减后的时间，格式为 "HH:mm:ss"
     * @example timeSub('02:34:56', '01:23:45') // Output: '01:11:11'
     * @example timeSub('23:59:59', '01:00:01') // Output: '22:59:58'
     */
    timeSub(time1, time2) {
      if (!time1 || !time2) {
        throw new Error("Invalid time inputs")
      }

      const [hours1, minutes1, seconds1] = time1.split(":").map(Number)
      const [hours2, minutes2, seconds2] = time2.split(":").map(Number)

      let totalSeconds =
        hours1 * 3600 + minutes1 * 60 + seconds1 - hours2 * 3600 - minutes2 * 60 - seconds2

      if (totalSeconds < 0) {
        throw new Error("Cannot subtract a time that is greater than the first time")
      }
      return this.format(totalSeconds)
    },
    /**
     * @description Convert the current time to "HH:mm:ss" format
     * @returns {string} Formatted time string ("HH:mm:ss")
     */
    getCurrentTimeString() {
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, "0")
      const minutes = now.getMinutes().toString().padStart(2, "0")
      const seconds = now.getSeconds().toString().padStart(2, "0")

      return `${hours}:${minutes}:${seconds}`
    },
    /**
     * @description 将时间戳与一个时间字符串相加
     * @param timestamp {number} 时间戳（毫秒）
     * @param timeString {string} 时间字符串，格式为 "HH:mm:ss"
     * @returns {number} 相加后的时间戳（毫秒）
     * @example addTimeStringToTimestamp(1671047720000, '02:34:56') // Output: 1671071176560 (timestamp of 05:57:06)
     */
    addTimeStringToTimestamp(timestamp, timeString) {
      if (!timestamp || typeof timestamp !== "number" || isNaN(timestamp)) {
        // throw new Error("Invalid timestamp")
        return new Date().getTime()
      }
      if (
        !timeString ||
        typeof timeString !== "string" ||
        !timeString.match(/^\d{2}:\d{2}:\d{2}$/)
      ) {
        throw new Error("Invalid time string")
      }

      const [hours, minutes, seconds] = timeString.split(":").map(Number)
      const totalSeconds = hours * 3600 + minutes * 60 + seconds

      return timestamp + totalSeconds * 1000
    },
    timerCall(event) {
      if (event.oldState === "hidden") {
        this.start()
      }
    },
    /**
     * @description 计算当前时间距离指定时间点（HH:mm:ss）的秒数
     * @param targetTimeString {string} 目标时间字符串，格式为 "HH:mm:ss"
     * @returns {number} 相差的秒数
     * @example getSecondsToTargetTime('02:34:56') // 假设当前时间是 00:00:00，则输出: 9056
     */
    getSecondsToTargetTime(targetTimeString) {
      if (
        !targetTimeString ||
        typeof targetTimeString !== "string" ||
        !targetTimeString.match(/^\d{2}:\d{2}:\d{2}$/)
      ) {
        throw new Error("Invalid target time string")
      }

      const [targetHours, targetMinutes, targetSeconds] = targetTimeString.split(":").map(Number)

      const now = new Date()
      const currentHours = now.getHours()
      const currentMinutes = now.getMinutes()
      const currentSeconds = now.getSeconds()

      const secondsInTargetTime = targetHours * 3600 + targetMinutes * 60 + targetSeconds
      const secondsInCurrentTime = currentHours * 3600 + currentMinutes * 60 + currentSeconds

      let secondsDiff = secondsInTargetTime - secondsInCurrentTime

      if (secondsDiff < 0) {
        secondsDiff += 86400 // Add a day if the target time has already passed for today
      }

      return secondsDiff
    },
    //贪心算法,返回数组
    splitIntegerRandomImproved(num) {
      const result = []
      let remaining = num
      while (remaining > 0) {
        const step = Math.floor(Math.random() * (remaining - 1)) + 1
        result.push(step)
        remaining -= step
      }
      if (result[result.length - 1] !== 1) {
        for (let i = result.length - 2; i >= 0; i--) {
          if (result[i] > 1) {
            result[i] -= 1
            result.push(1)
            break
          }
        }
      }
      result.sort((a, b) => b - a)
      return result
    }
  }
})
