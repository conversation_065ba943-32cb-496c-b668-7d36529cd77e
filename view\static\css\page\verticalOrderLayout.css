#vertical-component {
  display: flex;
  /* height: 100vh; */
  overflow: hidden;
  background: #fff;
}
.vertical-sidebar {
  width: 2rem;
  display: flex;
  flex-direction: column;
  color: #626365;
  overflow-y: auto;
  /* height: 100%; */
  /* align-items: center; */
}
.vertical-sidebar::after {
  content: "";
  flex-grow: 1;
  background-color: #f5f5f5;
}
.vertical-sidebar .activeStyle {
  color: var(--styleColor) !important;
  background: #ffff !important;
}
.vertical-sidebar .sidebar-item {
  text-align: center;
  padding: 0.5rem 0.2rem;
  font-size: 0.35rem;
  background-color: #f5f5f5;
  user-select: none;
}
.vertical-sidebar .sidebar-item div {
  overflow-wrap: break-word; /* 标准换行 */
}

.vertical-sidebar .btr-radius {
  border-top-right-radius: 0.2rem;
}
.vertical-sidebar .bbr-radius {
  border-bottom-right-radius: 0.2rem;
}

/* 为了消除之前activeStyle的影响，重置相邻元素的圆角 */
/* .sidebar-item:not(.activeStyle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
} */

.vertical-content {
  flex: 1;
  padding: 0rem 0.2rem 0.2rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: #fff;
}
.vertical-sidebar,
.vertical-content {
  /* Ensure they can scroll */
  overflow-y: auto;
  /* Hide the scrollbar */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.vertical-sidebar::-webkit-scrollbar,
.vertical-content::-webkit-scrollbar {
  display: none; /* Hide scrollbar for WebKit browsers */
}

.vertical-content-foodCard .food-item {
  width: 100%;
  height: 4rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 0.3rem;
  position: relative;
}

.vertical-content-foodCard .food-item:nth-last-child(1) {
  margin-bottom: 0;
}
.vertical-food-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.15rem;
  z-index: 1;
  text-align: center;
}
.vertical-food-soldOutImg {
  height: 100%;
}
.food-details-left {
  text-shadow: -1px -1px 0 var(--styleColor), 1px -1px 0 var(--styleColor),
    -1px 1px 0 var(--styleColor), 1px 1px 0 var(--styleColor);
  /* text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;  */
}
.vertical-content-foodCard .foodType-title {
  top: 0;
  position: sticky;
  font-size: 0.35rem;
  padding: 0.4rem 0 0.4rem;
  z-index: 2;
  color: #373a40;
  background: #ffff;
}

.vertical-content-foodCard .food-item .vertical-food-thumbnail {
  height: 100%;
  object-fit: cover;
  border-radius: 0.15rem;
  /* object-fit: cover; */
  /* width: 100%; */
}
.vertical-content-foodCard .food-item .food-details {
  width: 100%; /* Ensure it takes the full width of the container */
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 0.1rem 0.2rem 0.3rem;
  display: flex;
  justify-content: space-between;
  color: white; /* Optional: Change text color for visibility */
  font-weight: 600;
}
.vertical-content-foodCard .food-item .food-details div {
  max-width: 70%;
}
.vertical-content-foodCard .food-item .food-details .name {
  font-size: 0.4rem;
  margin-bottom: 0.1rem;
}
.vertical-content-foodCard .food-item .food-details .price {
  font-size: 0.35rem;
}
.vertical-content-foodCard .food-item .food-details .add-btn {
  background-color: var(--styleColor);
  color: white;
  border: none;
  border-radius: 50%;
  width: 0.75rem;
  height: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.7rem;
  margin-left: 0.5rem;
  align-self: end;
}
.vertical-content-foodCard .food-item .img_tag {
  height: 1.1rem;
}

/* .img_tag_topLeft {
  position: absolute;
  top: 0rem;
  left: 0rem;
  display: flex;
}
.img_tag_topRight {
  position: absolute;
  top: 0rem;
  right: 0.05rem;
  display: flex;
  z-index: 1;
}
.img_tag_bottomLeft {
  position: absolute;
  bottom: 0rem;
  left: 0rem;
  display: flex;
} */

/* .food-item img[lazy="error"] {
  display: none;
} */
