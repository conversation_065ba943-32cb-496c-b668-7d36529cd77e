Vue.component("recharge-view", {
  props: [
    "systemLanguage",
    "errorHandle",
    "openTable",
    "closeSheet",
    "defaultOss",
    "logoutReset",
    "language"
  ],
  mixins: [globalStoreMixin],
  filters: {},
  template: `
<div id="rechargeDia">
  <div class="rechargeBox-container">
    <div class="rechargeBox-section-title">{{systemLanguage.selectRechargeTitle}}</div>
    <div class="rechargeBox-scroll-container" ref="rechargeBoxScrollContainer">
      <!-- 充值选项部分 -->
      <template v-if="!isLoading">
        <div class="rechargeBox-recharge-options" v-if="rechargeOptions.length > 0">
          <div
            v-for="item in rechargeOptions"
            :key="item.code"
            class="rechargeBox-recharge-card"
            :class="{ 'rechargeBox-active': selectedRecharge && selectedRecharge.code === item.code }"
            @click="selectRecharge(item)"
          >
            <!-- 左侧图片 -->
            <div class="rechargeBox-card-image">
              <img
                :src="getImageUrl(item)"
                :class="{ 'rechargeBox-loaded': item.imageLoaded }"
                @load="handleImageLoad(item)"
                @error="handleImageError(item)"
              />
            </div>

            <!-- 右侧内容 -->
            <div class="rechargeBox-card-content">
              <!-- 标题和金额 -->
              <div class="rechargeBox-header-section">
                <div class="rechargeBox-title-area">
                  <div class="rechargeBox-card-title">{{ itemName(item) }}</div>
                </div>
                <div class="rechargeBox-price-area">
                  <span class="rechargeBox-currency">{{ openTable.currencyWay }}</span>
                  <span class="rechargeBox-price">{{ item.upa }}</span>
                </div>
              </div>

              <!-- 赠送信息 -->
              <div class="rechargeBox-benefits-section">
                <div
                  class="rechargeBox-benefit-item"
                  v-if="showGiftInformation(item, 'addDebitPoints')"
                >
                  <span class="rechargeBox-benefit-icon">🎁</span>
                  <div class="rechargeBox-benefit-content">
                    <span class="rechargeBox-benefit-label">
                      {{systemLanguage.benefitCardPoints}}
                    </span>
                    <span class="rechargeBox-benefit-value rechargeBox-points">
                      {{formatGiftPoints(item,'addDebitPoints')}}
                    </span>
                  </div>
                </div>
                <div
                  class="rechargeBox-benefit-item"
                  v-if="showGiftInformation(item, 'addMemberPoints')"
                >
                  <span class="rechargeBox-benefit-icon">⭐</span>
                  <div class="rechargeBox-benefit-content">
                    <span class="rechargeBox-benefit-label">
                      {{systemLanguage.benefitMemberPoints}}
                    </span>
                    <span class="rechargeBox-benefit-value rechargeBox-member-points">
                      {{formatGiftPoints(item,'addMemberPoints')}}
                    </span>
                  </div>
                </div>
                <div
                  class="rechargeBox-benefit-item"
                  v-if=" showGiftInformation(item, 'addDebitExpiredDay')"
                >
                  <span class="rechargeBox-benefit-icon">⏱️</span>
                  <div class="rechargeBox-benefit-content">
                    <span class="rechargeBox-benefit-label">
                      {{systemLanguage.benefitCardDays}}
                    </span>
                    <span class="rechargeBox-benefit-value rechargeBox-expire">
                      {{formatGiftPoints(item,'addDebitExpiredDay')}}
                    </span>
                  </div>
                </div>
                <div
                  class="rechargeBox-benefit-item"
                  v-if="showGiftInformation(item, 'addMemberExpiredDay')"
                >
                  <span class="rechargeBox-benefit-icon">⏱️</span>
                  <div class="rechargeBox-benefit-content">
                    <span class="rechargeBox-benefit-label">
                      {{systemLanguage.benefitMemberExpireDays}}
                    </span>
                    <span class="rechargeBox-benefit-value rechargeBox-expire">
                      {{formatGiftDate(item,'addMemberExpiredDay')}}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 充值选项空状态 -->
        <div class="rechargeBox-empty-state" v-else>
          <div class="rechargeBox-empty-state-icon">📦</div>
          <div class="rechargeBox-empty-state-text">{{systemLanguage.emptyRechargeText}}</div>
          <div class="rechargeBox-empty-state-subtext">{{systemLanguage.emptyRechargeSubtext}}</div>
        </div>
      </template>
      <!-- 加载状态 -->
      <div class="rechargeBox-loading-wrapper" v-else>
        <div class="rechargeBox-loading-spinner"></div>
      </div>

      <div class="rechargeBox-section-title">{{systemLanguage.selectPaymentTitle}}</div>
      <!-- 支付方式部分 -->
      <div class="rechargeBox-payment-options" v-if="allPayMethod.length > 0">
        <div
          v-for="item in allPayMethod"
          :key="item.value"
          class="rechargeBox-payment-item"
          :class="{ 'rechargeBox-active': selectedPayment&&selectedPayment.value === item.value }"
          @click="selectPayment(item)"
        >
          <img
            :src="item.imgSrc"
            class="rechargeBox-payment-icon"
            @error="handleImageError($event)"
          />
          <span class="rechargeBox-payment-name">
            <template v-if="Array.isArray(item.value)">
              {{systemLanguage[item.belong+'Txt']}}
            </template>
            <template v-else>{{systemLanguage[item.value+'Txt']}}</template>
          </span>
        </div>
      </div>
      <!-- 支付方式空状态 -->
      <div class="rechargeBox-empty-state" v-else>
        <div class="rechargeBox-empty-state-icon">💳</div>
        <div class="rechargeBox-empty-state-text">{{systemLanguage.emptyPaymentText}}</div>
        <div class="rechargeBox-empty-state-subtext">{{systemLanguage.emptyPaymentSubtext}}</div>
      </div>
    </div>
  </div>

  <div class="rechargeBox-button-group">
    <button class="rechargeBox-close-btn" @click="closeSheet">{{systemLanguage.closeBtn}}</button>
    <button class="rechargeBox-submit-btn" @click="handleSubmit" :disabled="!canSubmit">
      {{systemLanguage.rechargeSubBtn}}
    </button>
  </div>
</div>

`,
  data() {
    return {
      currencyWay: "￥",
      selectedRecharge: null,
      selectedPayment: null,
      rechargeOptions: [],
      subLoading: false,
      isLoading: true
    }
  },

  created() {
    // 初始化系统语言
  },
  computed: {
    canSubmit() {
      return (
        this.rechargeOptions.length > 0 &&
        this.allPayMethod.length > 0 &&
        this.selectedRecharge &&
        this.selectedPayment &&
        !this.subLoading
      )
    },
    allPayMethod() {
      let { payType } = this.openTable
      if (!payType) return []
      let copyPayType = JSON.parse(JSON.stringify(payType))
      let { pos, ...otherPayMethod } = copyPayType
      let newPayType = { ...otherPayMethod }
      const orderPayMethod = Object.keys(newPayType).reduce((acc, cur) => {
        if (cur === "boc" && payType[cur].length > 0) {
          return [...acc, "boc"]
        } else {
          return [...acc, ...payType[cur]]
        }
      }, [])
      let newArr = []
      orderPayMethod.forEach(item => {
        let payTypeObj = this.payTypeObj.find(e => e.value == item)
        if (payTypeObj) newArr.push(payTypeObj)
      })
      return newArr
    }
  },

  methods: {
    async initRecharge() {
      if (this.allPayMethod.length) {
        this.selectedPayment = this.allPayMethod[0]
      }
      //滚动到顶部
      this.$refs.rechargeBoxScrollContainer.scrollTop = 0
      await this.getRechargeOptions()
      if (this.rechargeOptions.length) {
        this.selectedRecharge = this.rechargeOptions[0]
      }
    },
    // 获取充值选项数据
    getRechargeOptions() {
      this.isLoading = true
      return new Promise((resolve, reject) => {
        $.get({
          url: `${API_PATH}memberRecharge/getAll`,
          dataType: "json",
          success: res => {
            let data = res.data
            let useBackupOss = sessionStorage.getItem("useBackupOss") || false
            let backupOssUrl = sessionStorage.getItem("backupOssUrl") || ""
            let oss = useBackupOss ? backupOssUrl : app.defaultOss
            data.forEach(item => {
              if (data.length) {
                const url = `${oss}/${item.domain}/image/memberRecharge/${item.code}.${item.photoSuffix}?${item.photoTime}`
                item.logoUrl = url
              }
              item.giftInformation = item.giftInformation ? item.giftInformation.split(",") : []
            })
            // console.log(data, "data")
            this.rechargeOptions = data
          },
          error: () => {},
          complete: () => {
            this.isLoading = false
            resolve()
          }
        })
      })
    },

    getImageUrl(item) {
      return item.logoUrl
    },
    handleImageLoad(item) {
      this.$set(item, "imageLoaded", true)
    },
    handleImageError(item) {
      this.$set(item, "imageLoaded", false)
    },
    selectRecharge(item) {
      this.selectedRecharge = item
    },
    selectPayment(item) {
      this.selectedPayment = item
    },
    handleSubmit() {
      if (!this.canSubmit) return
      let { currencyWay } = this.openTable
      let { rechargeConfirmTip, btnTxtForCancel, btnTxtForConfirm } = this.systemLanguage
      let tipContent = rechargeConfirmTip.replace(
        "#amount",
        `${currencyWay}${this.selectedRecharge.upa}`
      )
      layer.confirm(
        tipContent,
        {
          title: false,
          closeBtn: 0,
          skin: "baseLayer payAtCashierLayer",
          area: ["85%"],
          btn: [btnTxtForCancel, btnTxtForConfirm] //按钮
        },
        (index, layero) => {
          layer.close(index)
        },
        (index, layero) => {
          this.requestRecharge()
        }
      )
    },
    requestRecharge(params = {}) {
      const loadingIndex = layer.load(2)
      let { belong, value } = this.selectedPayment
      let { storeNumber, performType, urlPrefix, language } = this.openTable
      let { errorRecharge } = this.systemLanguage
      let isFoodCourtMode =
        mark === "pc_index" ? app.urlParams.paramsObj.mode === "FoodCourt" : app.isFoodCourtMode
      let tableNumber = mark === "pc_index" ? app.initTableNumber : app.openTable.tableNumber
      let commonData = {
        ...this.selectedRecharge,
        checkMerchantRef: true, //默认校验订单号防止存在未处理订单,重复下单则覆盖字段值为false
        storeNumber: isFoodCourtMode ? "*" : storeNumber,
        tableNumber,
        performType,
        payType: belong,
        payVal: value,
        urlPrefix,
        language,
        ...params
      }
      let { logoUrl, imageLoaded, ...otherData } = commonData
      $.post({
        url: `${API_PATH}memberRecharge/saveMemberRechargeOrder`,
        contentType: "application/json",
        data: JSON.stringify(otherData),
        dataType: "json",
        success: res => {
          this.handleSendOrderSuccess(res, commonData)
        },
        error: () => {
          layer.msg(errorRecharge)
        },
        complete: () => {
          layer.close(loadingIndex)
        }
      })
    },
    handleSendOrderSuccess(result, data) {
      const { sessionId, merchantRef, refNo, payVal, statusCode } = result
      if (statusCode != 200) {
        this.errorCallBack(result, data)
        return false
      }
      let tableNumber = mark === "pc_index" ? app.initTableNumber : app.openTable.tableNumber
      if (tableNumber == "test") {
        layer.msg(this.systemLanguage.testRechargePaySuccess)
        return false
      }
      if (mark === "pc_index") {
        sessionStorage.setItem("openTable", JSON.stringify(app.openTableData))
        sessionStorage.setItem("indexPageUrl", window.location.href)
        if (app.urlParams.paramsObj.mode === "FoodCourt" && app.storeNumber === "*") {
          let params = {
            mode: "FoodCourt",
            config: {
              color: app.openTableData.color,
              ...app.openTableData.foodCourt
            }
          }
          sessionStorage.setItem("mode", JSON.stringify(params))
        }
      }

      // EFTPOS支付
      if (sessionId) {
        this.setCookie(merchantRef)
        this.initEFTPay(sessionId)
        return false
      }
      // ipay88支付
      if (refNo) {
        this.setCookie(refNo)
        this.iPay88Online(result)
        return false
      }
      // 中国银行支付
      if (payVal === "boc") {
        this.redirectPaySuccessPage(result)
        return false
      }
      // windcave
      if (data.payType === "windcave") {
        this.redirectPaySuccessPage(result, !!result.merchantRef)
        return false
      }
    },
    errorCallBack(resultObj, data) {
      let { statusCode } = resultObj
      let {
        errorCashierNotOpenTip,
        errorServiceTip,
        errorPaymentService,
        PCLoginMemberTimeout,
        errorServiceNotOpenTip,
        errorVerifyTime,
        unprocessedOrderTip,
        memberExpired,
        accountNotFoundText,
        totalPriceTooLow,
        errorRecharge,
        rechargeDataExpired,
        theSavingsCardHasExpired
      } = this.systemLanguage
      switch (statusCode) {
        case 210: //有未处理订单，是否继续下单
          let repeatParams = { ...data, checkMerchantRef: false }
          this.orderAgainConfirm(unprocessedOrderTip, repeatParams, "recharge")
          break
        case 401:
          layer.msg(rechargeDataExpired)
          break
        case 404:
          layer.msg(errorServiceNotOpenTip)
          break
        case 407:
          layer.msg(errorCashierNotOpenTip)
          break
        case 4005:
          layer.msg(errorVerifyTime)
          break
        case 4017:
          layer.msg(totalPriceTooLow)
          break
        case 500:
          layer.msg(errorServiceTip)
          break
        case 501:
          layer.msg(errorPaymentService)
          break
        case 5001:
          // cookie过期,回退登录页重新登录
          layer.msg(PCLoginMemberTimeout, {
            shade: [0.1, "#fff"], // 设置遮罩层透明度和颜色
            time: 2000, // 显示时间，单位毫秒
            end: () => {
              this.logoutReset().then(() => {
                this.closeSheet() // 消息关闭后的回调函数
              })
            }
          })
          break
        case 5110:
          layer.msg(theSavingsCardHasExpired)
          break
        case 5103:
          layer.msg(memberExpired)
          break
        case 5104:
          layer.msg(accountNotFoundText)
          break
        default:
          layer.msg(errorRecharge)
          break
      }
    },
    showGiftInformation(item, key) {
      return item.giftInformation.includes(key)
    },
    // 添加名称过滤器
    itemName(item) {
      let nameMap = {
        en: "desc1",
        zh: "desc2",
        thirdLan: "multi1"
      }
      let { language } = this.openTable
      let key = nameMap[language]
      return item[key]
    },
    formatGiftPoints(item, key) {
      return item[key] ? `+${item[key]}` : "-"
    },
    formatGiftDate(item, key) {
      let { benefitDays } = this.systemLanguage
      return item[key] ? `+ ${item[key]} ${benefitDays}` : "-"
    }
  },

  mounted() {},
  watch: {}
})
