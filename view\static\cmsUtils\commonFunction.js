//去除RFC2822/ISO date formats的警告
moment.suppressDeprecationWarnings = true

var showCurrency = "$",
  holidayList = [],
  targetPriceName = "PRICE1"

// 获取UIConfig配置信息
async function getUIConfig(domain) {
  let storeNumber = sessionStorage.getItem("storeNumber") || "*"
  let UIConfigTypeSet = ["currencyWay", "priceWay"]
  let data = {
    domain,
    storeNumber,
    UIConfigTypeSet
  }
  return $.post({
    url: "../../manager_storeNumber/getAllItemConfig",
    dataType: "json",
    traditional: true,
    data,
    success: function (res) {
      if (res.statusCode == 200) {
        holidayList = res.holidayList || []
        // console.log(res, "成功")
        parent.setStore(res.storeNumberList)
        findUIConfig(res.uiConfigList, UIConfigTypeSet)
      }
    },
    error: function () {
      toastr.error("Failed to get uiConfig data. Please try again")
    }
  })
}
//
// 寻找店铺对应配置值
function findUIConfig(uiConfigList = [], UIConfigTypeSet) {
  for (let i = 0; i < UIConfigTypeSet.length; i++) {
    const type = UIConfigTypeSet[i]
    const config = uiConfigList.find(el => el.type == type)

    // 如果没有找到对应的配置，跳过当前循环
    if (!config) continue
    let valueToUse
    try {
      const parsedJson = JSON.parse(config.value)
      if (parsedJson && typeof parsedJson === "object") {
        valueToUse = parsedJson
      } else {
        valueToUse = config.value
      }
    } catch (parseError) {
      valueToUse = config.value
    }

    if (type == "currencyWay") {
      showCurrency = valueToUse
    } else if (type == "priceWay") {
      getPriceName(valueToUse)
    }
  }
}
// 判断jpg/png格式
function isJPG(photoSuffix) {
  let spliceSuffix
  if (photoSuffix) {
    spliceSuffix = `.${photoSuffix}`
  } else {
    spliceSuffix = ".png"
  }
  return spliceSuffix
}
// 判断价格字段
function priceName(type) {
  const priceNum = targetPriceName.replace("PRICE", "")
  let name = type === "foodList" ? `upa${priceNum}` : `price${priceNum === "1" ? "" : priceNum}`
  return name
}
// 遍历替换价格符号
function replacePrice() {
  // 遍历获取id为identificationAndPrice的元素
  $(".card-price").each(function (e) {
    // 获取元素的值
    let price = $(this).text()
    // 替换价格符号
    if (price.length) {
      price = price.trim()
      // 替换第一个字符串为#
      let replacePrice = showCurrency + price.substr(1)
      console.log(replacePrice, "replacePrice")
      $(this).text(replacePrice)
    }
  })
}

// 重新渲染当前页面
function reloadPage() {
  findUIConfig()
  // id为menu下的li的获取激活状态触发点击事件
  let active = $("#menu").find("li.active")
  // console.log(active, "active")
  active.trigger("click")
}

// 封装显示细项tile
function inListTitle(item, language = "en") {
  // const language = "en"
  let xiTitle = ""
  // foodlist:desc1,desc2,multi1
  // mListList:name,name2,multi1
  // fty=>foodlist:desc1,desc2,multi1
  // mty=>mListList:name,name2,multi1
  if (language == "en") {
    if (item.desc1) {
      // foolist或者fty下foodlist
      xiTitle = item.nameA || item.desc1
    } else {
      xiTitle = item.nameA || item.name
    }
  } else if (language == "zh") {
    if (item.desc2) {
      // foolist或者fty下foodlist
      xiTitle = item.nameB || item.desc2
    } else {
      xiTitle = item.nameB || item.name2
    }
  } else {
    if (item.multi1) {
      xiTitle = item.multi1
    } else {
      // 默认第一语言为优先级
      if (item.desc1) {
        // foolist或者fty下foodlist
        xiTitle = item.nameA || item.desc1
      } else {
        xiTitle = item.nameA || item.name
      }
    }
  }
  return xiTitle
}
// 封装显示细项外层Type的名称
function outListTitle(item, language = "en") {
  // console.log(language, 'language');
  let xiTitle = ""
  // fty:name,name2,multi1
  // mty:desc,desc2,multi1
  if (language == "en") {
    if (item.name) {
      // foolist或者fty下foodlist
      xiTitle = item.fType_nameA || item.name
    } else {
      xiTitle = item.nameA || item.desc
    }
  } else if (language == "zh") {
    if (item.name2) {
      // foolist或者fty下foodlist
      xiTitle = item.fType_nameB || item.name2
    } else {
      xiTitle = item.nameB || item.desc2
    }
  } else {
    xiTitle = item.multi1
    if (item.multi1) {
      xiTitle = item.multi1
    } else {
      // 默认第一语言为优先级
      if (item.name) {
        // foolist或者fty下foodlist
        xiTitle = item.fType_nameA || item.name
      } else {
        xiTitle = item.nameA || item.desc
      }
    }
  }
  return xiTitle
}
// 获取缓存中的storeNumber/domain/versionNumber
function getRequestHeaderParams() {
  let storeNumber = sessionStorage.getItem("storeNumber")
  let domain = sessionStorage.getItem("domain")
  let versionNumber = sessionStorage.getItem("versionNumber")
  return {
    storeNumber,
    domain,
    versionNumber: versionNumber == "PROD" ? "" : versionNumber
  }
}

function getPriceName(priceWay) {
  //判断是否加载moment
  if (!moment) {
    console.error("moment is not loaded")
    return
  }
  let { defaultPrice = "PRICE1", use_dow = [] } = priceWay
  //根据H先行判断
  let dowPriceItem = null
  let nowCurrentDate = moment().format("YYYY-MM-DD")

  if (use_dow[0] && use_dow[0].price != "") {
    //存在H排序优先判断
    use_dow.sort((a, b) => {
      const aHasH = a.dow.includes("H")
      const bHasH = b.dow.includes("H")

      if (aHasH && !bHasH) return -1 // a含H，b不含H，a排前面
      if (!aHasH && bHasH) return 1 // a不含H，b含H，b排前面
      return 0 // 两者都含H或都不含H，保持原顺序
    })
    dowPriceItem = use_dow.find(item => {
      let dow = item.dow.join("") //将dow转换为字符串
      return checkUseDowRes(dow, nowCurrentDate)
    })
  }
  targetPriceName = dowPriceItem ? dowPriceItem.price : defaultPrice
}

function checkUseDowRes(use_dow, nowCurrentDate) {
  if (use_dow) {
    let getDay = moment(nowCurrentDate).day() //獲取當天星期幾
    let replaceUse_dow = use_dow.replace("7", "0") //转换数据7=>0 一周七天
    // console.log(replaceUse_dow, '星期')
    let useDowRes = false
    inHolidayData = false //当天日期是否在假期日期内
    // 判断当天日期是否在假期日期内
    if (holidayList && holidayList.length != 0) {
      for (let i = 0; i < holidayList.length; i++) {
        const item = holidayList[i]
        let t1 = moment(nowCurrentDate).format("YYYY/MM/DD")
        if (item.fmDate && item.toDate) {
          // 两种日期都有
          let t2 = moment(item.fmDate).format("YYYY/MM/DD")
          let t3 = moment(item.toDate).format("YYYY/MM/DD")
          if (moment(t1).isBetween(t2, t3, null, "[]")) {
            inHolidayData = true
            break
          }
        } else if (item.fmDate) {
          // 只存在fmDate
          if (moment(item.fmDate).isSameOrBefore(t1)) {
            inHolidayData = true
            break
          }
        } else {
          // 只存在toDate
          if (moment(t1).isSameOrBefore(item.toDate)) {
            inHolidayData = true
            break
          }
        }
      }
    }
    if (use_dow.indexOf("H") != -1) {
      if (inHolidayData) {
        //有H且在假期日期内
        useDowRes = true
      } else {
        //有H且不在假期日期内,判断1-7
        if (replaceUse_dow.indexOf(getDay) != -1) useDowRes = true
      }
    } else {
      //无H
      if (inHolidayData) {
        useDowRes = false //在假期内的日期但没H所以要隐藏
      } else {
        // 不在假期日期内,判断当天星期几是否在设置的星期内
        if (replaceUse_dow.indexOf(getDay) != -1) useDowRes = true
      }
    }
    // console.log(useDowRes, 'useDowRes')
    return useDowRes
  } else {
    return true
  }
}

// 监听ajax请求发起回调
$(document).ajaxSend((event, xhr, options) => {
  let { storeNumber, domain, versionNumber } = getRequestHeaderParams()
  xhr.setRequestHeader("domain", domain)
  xhr.setRequestHeader("storeNumber", storeNumber)
  xhr.setRequestHeader("versionNumber", versionNumber)

  // if (options.statusCode == 4033) {
  // }
})
