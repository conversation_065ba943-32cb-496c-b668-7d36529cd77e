<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/plugins/tinymce/tinymce.min.js"></script>
    <script src="../../static/plugins/sortable/Sortable.min.js"></script>
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />
    <script src="../../static/cmsUtils/cmsComponents/EmailEditorToolbar.js"></script>
    <script src="../../static/cmsUtils/EmailOrderTemplate.js"></script>

    <style>
      @media screen and (max-width: 1300px) {
      }

      @media screen and (min-width: 1300px) {
      }

      .tinyMCEPrompt_label,
      .agreement_label {
        display: inline-block;
        margin: 15px 5px;
      }

      .el-popover ::-webkit-scrollbar {
        display: none;
      }

      .tox-tinymce-aux {
        z-index: 99999 !important;
      }

      .tox-statusbar {
        display: none !important;
      }

      .dialog_footer {
        text-align: right;
        padding-top: 20px;
      }

      .key-tag {
        margin: 3px 5px !important;
      }

      .pub_dialog {
        /* display: flex;
        justify-content: center;
        align-items: center; */
      }

      .el-dialog {
        margin-top: 5vh !important;
      }

      .el-dialog .el-dialog__body {
        max-height: 75vh;
      }

      .el-form {
        max-height: 65vh;
        overflow-y: auto;
      }

      .resizeNone .el-textarea__inner {
        resize: none;
      }

      .input-wrapper {
        height: max-content;
      }

      .tagg {
        font-size: 90%;
        height: auto;
        line-height: 28px;
        margin: 0 1px;
        padding: 0 5px;
        /* user-select: none; */
      }

      .emailForm .el-form-item {
        display: flex;
      }

      .emailForm .el-form-item__content {
        flex: 1;
        margin-left: 0 !important;
      }

      .emailForm .el-form-item__label {
        /* align-self: end; */
      }

      .demo-table-expand {
        font-size: 0;
      }

      .demo-table-expand label {
        color: #99a9bf;
        margin-left: 40px;
      }

      .demo-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 33%;
      }

      .el-form-item__content {
        margin-left: 40px;
      }

      .pre {
        white-space: break-spaces;
        word-break: break-all;
        word-wrap: break-word;
        line-height: 25px;
      }

      .smtpHostBox {
        width: 100%;
        display: flex;
        flex-direction: column;
      }

      .nowEmailRadio {
        display: flex;
        margin: 10px 0;
      }

      .choosableEmail {
        width: 100%;
      }

      .choosableEmail-el-radio {
        width: 100%;
        display: flex;
        align-items: center;
      }

      .choosableEmail-el-radio .el-radio__label {
        flex: 1;
      }

      .el-checkbox-group .el-input {
        width: 220px;
      }

      .el-checkbox-group .el-checkbox__input {
        vertical-align: -webkit-baseline-middle;
      }

      /* .form-item-prompt下的所有子元素margin为0 */
      .form-item-prompt-warp .el-form-item__content {
        display: block !important;
      }

      .dia-form-item-prompt .el-form-item__content {
        margin-left: 0 !important;
      }

      .form-item-prompt * {
        margin: 0rem !important;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :header-cell-style="{fontSize: '15px'}"
          :max-height="tableHeight"
          empty-text="No Data"
        >
          <el-table-column type="expand">
            <template slot-scope="scope">
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="Agreement(en)">
                  <el-popover
                    trigger="hover"
                    placement="right-end"
                    width="400"
                    :disabled="!scope.row.statement.en"
                  >
                    <pre
                      slot="default"
                      style="max-height: 400px; overflow: auto"
                      class="pre"
                      v-html="scope.row.statement.en"
                    ></pre>
                    <span slot="reference">
                      {{scope.row.statement.en?'display on hover':scope.row.statement.en}}
                    </span>
                  </el-popover>
                </el-form-item>
                <el-form-item label="Agreement(zh)">
                  <el-popover
                    trigger="hover"
                    placement="right-end"
                    width="400"
                    :disabled="!scope.row.statement.zh"
                  >
                    <pre
                      slot="default"
                      style="max-height: 400px; overflow: auto"
                      class="pre"
                      v-html="scope.row.statement.zh"
                    ></pre>
                    <span slot="reference">
                      {{scope.row.statement.zh?'display on hover':scope.row.statement.zh}}
                    </span>
                  </el-popover>
                </el-form-item>
                <el-form-item label="Agreement(thirdLan)">
                  <el-popover
                    trigger="hover"
                    placement="right-end"
                    width="400"
                    :disabled="!scope.row.statement.thirdLan"
                  >
                    <pre
                      slot="default"
                      style="max-height: 400px; overflow: auto"
                      class="pre"
                      v-html="scope.row.statement.thirdLan"
                    ></pre>
                    <span slot="reference">
                      {{scope.row.statement.thirdLan?'display on' +
                      'hover':scope.row.statement.thirdLan}}
                    </span>
                  </el-popover>
                </el-form-item>
                <el-form-item label="Email">
                  <span>{{ scope.row.email }}</span>
                </el-form-item>
                <el-form-item label="AuthorizationCode">
                  <span>{{ scope.row.authorizationCode }}</span>
                </el-form-item>
                <el-form-item label="EmailSubject">
                  <span>{{ scope.row.emailSubject }}</span>
                </el-form-item>

                <el-form-item label="SuccessPrompt(en)" class="form-item-prompt-warp">
                  <div class="form-item-prompt" v-html="scope.row.successPrompt.en"></div>
                </el-form-item>
                <el-form-item label="SuccessPrompt(zh)" class="form-item-prompt-warp">
                  <div class="form-item-prompt" v-html="scope.row.successPrompt.zh"></div>
                </el-form-item>
                <el-form-item label="SuccessPrompt(thirdLan)" class="form-item-prompt-warp">
                  <div class="form-item-prompt" v-html="scope.row.successPrompt.thirdLan"></div>
                </el-form-item>
                <el-form-item label="FailPrompt(en)" class="form-item-prompt-warp">
                  <div class="form-item-prompt" v-html="scope.row.failPrompt.en"></div>
                </el-form-item>
                <el-form-item label="FailPrompt(zh)" class="form-item-prompt-warp">
                  <div class="form-item-prompt" v-html="scope.row.failPrompt.zh"></div>
                </el-form-item>
                <el-form-item label="failPrompt(thirdLan)" class="form-item-prompt-warp">
                  <div class="form-item-prompt" v-html="scope.row.failPrompt.thirdLan"></div>
                </el-form-item>

                <el-form-item label="EmailMessage(en)">
                  <pre class="pre" v-html="showTag(scope.row.emailMessage.en)"></pre>
                </el-form-item>
                <el-form-item label="EmailMessage(zh)">
                  <pre class="pre" v-html="showTag(scope.row.emailMessage.zh)"></pre>
                </el-form-item>
                <el-form-item label="EmailMessage(thirdLan)">
                  <pre class="pre" v-html="showTag(scope.row.emailMessage.thirdLan)"></pre>
                </el-form-item>
                <el-form-item label="PickupEmailMessage(en)">
                  <pre class="pre" v-html="showTag(scope.row.pickupEmailMessage?.en)"></pre>
                </el-form-item>
                <el-form-item label="PickupEmailMessage(zh)">
                  <pre class="pre" v-html="showTag(scope.row.pickupEmailMessage?.zh)"></pre>
                </el-form-item>
                <el-form-item label="PickupEmailMessage(thirdLan)">
                  <pre class="pre" v-html="showTag(scope.row.pickupEmailMessage?.thirdLan)"></pre>
                </el-form-item>
                <el-form-item label="PickupEmailSubject">
                  <span>{{ scope.row.pickupEmailSubject }}</span>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column prop="storeNumber" label="Store Number" align="center" width="130">
            <template slot-scope="scope">{{scope.row.storeNumber}}</template>
          </el-table-column>
          <el-table-column prop="PaymentMethod" label="Payment method" align="center">
            <template slot-scope="scope">{{scope.row.payType|mergePayType}}</template>
          </el-table-column>

          <el-table-column prop="clientID" label="Client ID" align="center">
            <el-table-column
              prop="clientID"
              label="Merchant Code/ID"
              align="center"
            ></el-table-column>
          </el-table-column>

          <el-table-column prop="PublicKey" label="Public/Secret Key" width="170" align="center">
            <el-table-column prop="PublicKey" label="Currency" align="center" width="170">
              <template slot-scope="scope">
                <el-popover
                  trigger="hover"
                  placement="top-start"
                  title="Public Key"
                  width="400"
                  style="max-height: 600px; overflow: auto"
                  :disabled="!scope.row.spiralPublicKey"
                  :content="scope.row.spiralPublicKey"
                >
                  <span slot="reference">
                    {{scope.row.spiralPublicKey?'display on hover':scope.row.currency}}
                  </span>
                </el-popover>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column prop="myPrivateKey" label="Private/Verify Key" align="center">
            <el-table-column prop="myPrivateKey" label="Merchant Key" align="center">
              <template slot-scope="scope">
                <el-popover
                  trigger="hover"
                  placement="top-start"
                  title="My private key"
                  width="400"
                  style="max-height: 600px; overflow: auto"
                  :disabled="!scope.row.myPrivateKey"
                  :content="scope.row.myPrivateKey"
                >
                  <span slot="reference">{{scope.row.myPrivateKey?'display on hover':''}}</span>
                </el-popover>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column prop="GoodsName" label="Goods Name" align="center">
            <template slot-scope="scope">{{showParseObj(scope.row.goodsName)}}</template>
          </el-table-column>

          <el-table-column prop label="Operation" align="center">
            <el-table-column width="150" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible = true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-document-copy"
                  circle
                  @click="onCopy(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="onDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 類別Add弹窗 -->
      <template>
        <el-dialog
          class="pub_dialog add_dialog"
          title="Add Dialog"
          :visible.sync="addDialogVisible"
          :close-on-click-modal="false"
          @opened="opBaseURLWatch = true"
          @close="opBaseURLWatch = false"
        >
          <el-form :model="addForm" ref="addForm" label-width="250px" :rules="rules">
            <div v-if="!active">
              <el-alert
                title="You can drag and drop payment methods to change the sort order"
                type="warning"
                show-icon
                style="margin-bottom: 20px"
              ></el-alert>
              <!--                StoreNumber-->
              <el-form-item
                ref="addStoreNumber"
                label-width="180px"
                label="Store Number"
                prop="storeNumber"
              >
                <el-input
                  v-model.trim="addForm.storeNumber"
                  placeholder="Please enter the store number"
                ></el-input>
              </el-form-item>
              <!--                付款方式-->
              <div class="pay-type-wrap">
                <el-form-item label-width="180px" label="POSPay method" prop="posPay" data-id="pos">
                  <el-checkbox-group v-model="addForm.payType.pos">
                    <el-checkbox
                      v-for="p in posPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      @change="onPayMeChange($event,p,'pos','addForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="addForm.paymentCode['pos'][p]"
                        @input="onPayMeInput($event,p,'pos','addForm')"
                        @focus="onPayMeFocus($event,p,'pos','addForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label-width="180px" label="EFTPay method" prop="eftPay" data-id="eft">
                  <el-checkbox-group v-model="addForm.payType.eft">
                    <el-checkbox
                      v-for="p in eftPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('eft','addForm')"
                      @change="onPayMeChange($event,p,'eft','addForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="addForm.paymentCode['eft'][p]"
                        :disabled="disablePayType('eft','addForm')"
                        @input="onPayMeInput($event,p,'eft','addForm')"
                        @focus="onPayMeFocus($event,p,'eft','addForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>

                <el-form-item
                  label-width="180px"
                  label="WindcavePay method"
                  prop="windcavePay"
                  data-id="windcave"
                >
                  <el-checkbox-group v-model="addForm.payType.windcave">
                    <el-checkbox
                      v-for="p in windcavePayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('windcave','addForm',p)"
                      @change="onPayMeChange($event,p,'windcave','addForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="addForm.paymentCode['windcave'][p]"
                        :disabled="disablePayType('windcave','addForm',p)"
                        @input="onPayMeInput($event,p,'windcave','addForm')"
                        @focus="onPayMeFocus($event,p,'windcave','addForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label-width="180px" label="BOCPay method" prop="bocPay" data-id="boc">
                  <el-checkbox-group v-model="addForm.payType.boc">
                    <el-checkbox
                      v-for="p in bocPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('boc','addForm',p)"
                      @change="onPayMeChange($event,p,'boc','addForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="addForm.paymentCode['boc'][p]"
                        :disabled="disablePayType('boc','addForm',p)"
                        @input="onPayMeInput($event,p,'boc','addForm')"
                        @focus="onPayMeFocus($event,p,'boc','addForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>

                <el-form-item
                  label-width="180px"
                  label="SolelyPay method"
                  prop="solelyPay"
                  data-id="solely"
                >
                  <el-checkbox-group v-model="addForm.payType.solely" :max="1">
                    <el-checkbox
                      v-for="p in solelyPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('solely','addForm',p)"
                      @change="onPayMeChange($event,p,'solely','addForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="addForm.paymentCode['solely'][p]"
                        :disabled="disablePayType('solely','addForm',p)"
                        @input="onPayMeInput($event,p,'solely','addForm')"
                        @focus="onPayMeFocus($event,p,'solely','addForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div>
            </div>
            <div v-else style="position: relative">
              <el-steps
                align-center
                :active="active"
                finish-status="success"
                process-status="process"
                style="margin-bottom: 20px"
              >
                <el-step @click.native="changeStep(0,'addForm')" title="PayMethod"></el-step>
                <!-- eft填写 -->
                <el-step
                  @click.native="changeStep(1,'addForm')"
                  :status="status.Client"
                  title="Client"
                ></el-step>
                <el-step
                  @click.native="changeStep(2,'addForm')"
                  :status="status.GoodsName"
                  title="GoodsName"
                  v-if="showGoodsName"
                ></el-step>
                <el-step
                  @click.native="changeStep(3,'addForm')"
                  :status="status.PromptText"
                  title="PromptText"
                ></el-step>
                <el-step
                  @click.native="changeStep(4,'addForm')"
                  :status="status.Email"
                  title="Email"
                ></el-step>
                <el-step
                  @click.native="changeStep(5,'addForm')"
                  :status="status.Agreement"
                  title="Agreement"
                ></el-step>
              </el-steps>

              <div v-if="active===1&&isShowFormItem('onlinePayArr1')">
                <!--                商户id-->
                <el-form-item label-width="140px" label="Client ID" prop="clientID">
                  <el-input
                    v-model="addForm.clientID"
                    clearable
                    placeholder="Please enter the client ID"
                  ></el-input>
                </el-form-item>
                <!--                前端引用依赖前缀-->
                <el-form-item
                  label-width="140px"
                  label="Web Base Url"
                  prop="webBaseUrl"
                  v-if="!isBoc&&!isWindcave"
                >
                  <el-input
                    v-model="addForm.webBaseUrl"
                    clearable
                    placeholder="Please enter the base url"
                  ></el-input>
                </el-form-item>
                <!--                后端引用依赖前缀-->
                <el-form-item label-width="140px" label="Service Base Url" prop="serviceBaseUrl">
                  <el-input
                    v-model="addForm.serviceBaseUrl"
                    clearable
                    placeholder="Please enter the base url"
                  ></el-input>
                </el-form-item>
                <!--                公钥-->
                <el-form-item
                  label-width="140px"
                  label="Public Key"
                  prop="spiralPublicKey"
                  v-if="!isWindcave"
                >
                  <el-input
                    type="textarea"
                    v-model="addForm.spiralPublicKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter public key in correct format"
                  ></el-input>
                </el-form-item>
                <!--                私钥-->
                <el-form-item
                  label-width="140px"
                  label="My private key"
                  prop="myPrivateKey"
                  v-if="!isWindcave"
                >
                  <el-input
                    type="textarea"
                    v-model="addForm.myPrivateKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter private key in correct format"
                  ></el-input>
                </el-form-item>
                <!--                api Key -->
                <el-form-item label-width="140px" label="Api Key" prop="apiKey" v-if="isWindcave">
                  <el-input
                    type="textarea"
                    v-model="addForm.apiKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter api key in correct format"
                  ></el-input>
                </el-form-item>
                <!-- 终端号-->
                <el-form-item
                  label-width="140px"
                  label="Terminal No"
                  prop="terminalNo"
                  v-if="isBoc"
                >
                  <el-input
                    v-model="addForm.terminalNo"
                    clearable
                    placeholder="Please enter the terminal no"
                  ></el-input>
                </el-form-item>
                <!-- uiMode界面模式 -->
                <el-form-item label-width="140px" label="UI Mode" prop="uiMode" v-if="isBoc">
                  <el-select
                    v-model="addForm.uiMode"
                    style="width: 100%"
                    placeholder="Please select UI Mode"
                  >
                    <el-option
                      :label="item.label"
                      :value="item.value"
                      v-for="(item,index) in uiModeOptions"
                      :key="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <!-- 控制退款按钮-->
                <el-form-item
                  label-width="180px"
                  label="Show Refund Button"
                  prop="refundable"
                  v-if="isShowRefundBtn"
                >
                  <el-switch v-model="addForm.refundable"></el-switch>
                </el-form-item>
              </div>
              <!-- iPay88商户配置信息 -->
              <div v-else-if="active===1&&isShowFormItem('onlinePayArr2')">
                <!--                商户Merchant Code-->
                <el-form-item
                  label-width="140px"
                  :label="isRazer?'Merchant ID':'Merchant Code'"
                  prop="clientID"
                >
                  <el-input
                    v-model="addForm.clientID"
                    :placeholder="`Please enter the merchant ${isIpay88?'code':'id'}`"
                    clearable
                  ></el-input>
                </el-form-item>
                <!--   后端引用依赖前缀 ￣へ￣ Razer使用 -->
                <el-form-item
                  label-width="140px"
                  label="Service Base Url"
                  prop="serviceBaseUrl"
                  v-if="isRazer"
                >
                  <el-input
                    v-model="addForm.serviceBaseUrl"
                    clearable
                    placeholder="Please enter the base url"
                  ></el-input>
                </el-form-item>

                <!--               商户 Merchant Key-->
                <el-form-item
                  label-width="140px"
                  :label="isIpay88?'Merchant Key':'Verify key'"
                  prop="myPrivateKey"
                >
                  <el-input
                    type="textarea"
                    v-model="addForm.myPrivateKey"
                    :rows="3"
                    class="resizeNone"
                    :placeholder="`Please enter ${isIpay88?'merchant':'verify'} key in correct format`"
                  ></el-input>
                </el-form-item>
                <!--                密钥-->
                <el-form-item
                  label-width="140px"
                  label="Secret key"
                  prop="spiralPublicKey"
                  v-if="isRazer"
                >
                  <el-input
                    type="textarea"
                    v-model="addForm.spiralPublicKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter secret key in correct format"
                    required
                  ></el-input>
                </el-form-item>
                <el-form-item label-width="140px" label="Currency" prop="currency">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="Ensure that UIConfig-CurrencyWay is consistent with the current configuration"
                    placement="bottom-start"
                    :hide-after="2000"
                  >
                    <el-select
                      v-model="addForm.currency"
                      placeholder="Please select payment currency"
                      style="width: 100%"
                    >
                      <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="(item,index) in iPay88CurrencyList"
                        :key="index"
                      ></el-option>
                    </el-select>
                  </el-tooltip>
                </el-form-item>
                <!-- 控制退款按钮-->
                <el-form-item
                  label-width="180px"
                  label="Show Refund Button"
                  prop="refundable"
                  v-if="isShowRefundBtn"
                >
                  <el-switch v-model="addForm.refundable"></el-switch>
                </el-form-item>
              </div>
              <!-- 钱包支付Member Type -->
              <div v-if="active===1&&hasWallet">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="This parameter is only used for wallet payment method configuration"
                  placement="bottom-start"
                  :hide-after="2000"
                >
                  <!--                商户id-->
                  <el-form-item label-width="140px" label="Member Type" prop="memberType">
                    <el-input
                      v-model="addForm.memberType"
                      clearable
                      placeholder="Please enter the Member Type(Use ; to separate multiple Type)"
                    ></el-input>
                  </el-form-item>
                </el-tooltip>
              </div>
              <div v-else-if="active===2">
                <!--                商品name-->
                <el-form-item label="Goods Name(en)" prop="goodsName.en">
                  <el-input
                    v-model="addForm.goodsName.en"
                    placeholder="Please enter the goods name (en)"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="Goods Name(zh-hk)" prop="goodsName.zh">
                  <el-input
                    v-model="addForm.goodsName.zh"
                    placeholder="Please enter the goods name (zh-hk)"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="Goods Name(thirdLan)" prop="goodsName.thirdLan">
                  <el-input
                    v-model="addForm.goodsName.thirdLan"
                    placeholder="Please enter the goods name (thirdLan)"
                    clearable
                  ></el-input>
                </el-form-item>
              </div>
              <div v-else-if="active===3">
                <el-form-item class="dia-form-item-prompt" prop="successPrompt.en">
                  <label for="successPrompt-en-addForm">
                    <strong class="tinyMCEPrompt_label">Success Prompt(en)</strong>
                  </label>
                  <textarea id="successPrompt-en-addForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="successPrompt.zh">
                  <label for="successPrompt-zh-addForm">
                    <strong class="tinyMCEPrompt_label">Success Prompt(zh-hk) :</strong>
                  </label>
                  <textarea id="successPrompt-zh-addForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="successPrompt.thirdLan">
                  <label for="successPrompt-thirdLan-addForm">
                    <strong class="tinyMCEPrompt_label">Success Prompt(thirdLan) :</strong>
                  </label>
                  <textarea id="successPrompt-thirdLan-addForm"></textarea>
                </el-form-item>
                <!-- 失败提示 -->
                <el-form-item class="dia-form-item-prompt" prop="failPrompt.en">
                  <label for="failPrompt-en-addForm">
                    <strong class="tinyMCEPrompt_label">Fail Prompt(en)</strong>
                  </label>
                  <textarea id="failPrompt-en-addForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="failPrompt.zh">
                  <label for="failPrompt-zh-addForm">
                    <strong class="tinyMCEPrompt_label">Fail Prompt(zh-hk) :</strong>
                  </label>
                  <textarea id="failPrompt-zh-addForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="failPrompt.thirdLan">
                  <label for="failPrompt-thirdLan-addForm">
                    <strong class="tinyMCEPrompt_label">Fail Prompt(thirdLan) :</strong>
                  </label>
                  <textarea id="failPrompt-thirdLan-addForm"></textarea>
                </el-form-item>
              </div>
              <div v-else-if="active===4">
                <!--                邮箱-->
                <el-form-item label="Email" prop="email">
                  <el-input
                    v-model.trim="addForm.email"
                    placeholder="Please enter the email address"
                  ></el-input>
                </el-form-item>
                <!--              senderName-->
                <!--              :prop="handleProp('senderName','addForm')"-->
                <el-form-item label="Sender name">
                  <el-input
                    v-model="addForm.senderName"
                    placeholder="Please enter the sender name"
                  ></el-input>
                </el-form-item>

                <el-form-item label="Smtp host" prop="smtpHost">
                  <el-radio-group
                    v-model="smtpHostDiy"
                    @change="mailWayChange($event,'addForm')"
                    class="smtpHostBox"
                  >
                    <div class="nowEmailRadio">
                      <el-radio size="small" label="smtp.gmail.com">GMAIL</el-radio>
                      <el-radio size="small" label="smtpdm-ap-southeast-1.aliyun.com">
                        DIRECT MAIL
                      </el-radio>
                    </div>
                    <div class="choosableEmail">
                      <el-radio size="mini" class="choosableEmail-el-radio" label="">
                        <el-input
                          class="smtpHostDiy"
                          v-model="smtpHostVal"
                          @focus="handleSmtpHostFocus('addForm')"
                          @input="handleSmtpInput"
                          placeholder="Please enter smtpHost"
                        ></el-input>
                      </el-radio>
                    </div>
                  </el-radio-group>
                </el-form-item>
                <!--                授权码-->
                <el-form-item label="Authorization code" prop="authorizationCode">
                  <el-input
                    v-model="addForm.authorizationCode"
                    placeholder="Please enter the authorization code"
                  ></el-input>
                </el-form-item>
                <!--                邮件主题-->
                <el-form-item label="Email subject" prop="emailSubject">
                  <el-input
                    v-model="addForm.emailSubject"
                    placeholder="Please enter the email subject"
                  ></el-input>
                </el-form-item>
                <!-- 开启:只发pickup邮件,关闭:default/pickup邮件都发 -->
                <el-form-item label="Do not send for advance order">
                  <el-switch v-model="addForm.doNotSendForAdvanceOrder"></el-switch>
                </el-form-item>
                <!--                邮件消息-->
                <div class="emailForm">
                  <el-form-item
                    label="Email message(en)"
                    style="margin-bottom: 20px"
                    prop="emailMessage.en"
                    :required="addForm.email?.length"
                  >
                    <email-editor-toolbar
                      target-id="emailMessage-en-addForm"
                      template-type="default"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="emailMessage-en-addForm"></textarea>
                  </el-form-item>
                  <el-form-item
                    label="Email message(zh-hk)"
                    style="margin-bottom: 20px"
                    prop="emailMessage.zh"
                    :required="addForm.email?.length"
                  >
                    <email-editor-toolbar
                      target-id="emailMessage-zh-addForm"
                      template-type="default"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="emailMessage-zh-addForm"></textarea>
                  </el-form-item>
                  <el-form-item
                    label="Email message(thirdLan)"
                    :required="addForm.email?.length"
                    prop="emailMessage.thirdLan"
                  >
                    <email-editor-toolbar
                      target-id="emailMessage-thirdLan-addForm"
                      template-type="default"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="emailMessage-thirdLan-addForm"></textarea>
                  </el-form-item>
                </div>
                <!--  外卖自取邮件主题            -->
                <el-form-item label="Pickup Email subject">
                  <el-input
                    v-model="addForm.pickupEmailSubject"
                    placeholder="Please enter the pickup email subject"
                  ></el-input>
                </el-form-item>
                <!--                外卖邮箱信息-->
                <div class="emailForm">
                  <el-form-item label="Pickup email message(en)" style="margin-bottom: 20px">
                    <email-editor-toolbar
                      target-id="pickupEmailMessage-en-addForm"
                      template-type="pickUp"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="pickupEmailMessage-en-addForm"></textarea>
                  </el-form-item>
                  <el-form-item label="Pickup email message(zh-hk)" style="margin-bottom: 20px">
                    <email-editor-toolbar
                      target-id="pickupEmailMessage-zh-addForm"
                      template-type="pickUp"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="pickupEmailMessage-zh-addForm"></textarea>
                  </el-form-item>
                  <el-form-item label="Pickup email message(thirdLan)">
                    <email-editor-toolbar
                      target-id="pickupEmailMessage-thirdLan-addForm"
                      template-type="pickUp"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="pickupEmailMessage-thirdLan-addForm"></textarea>
                  </el-form-item>
                </div>
              </div>
              <div v-else-if="active===5">
                <!--   协议           -->
                <label for="agreement-en-addForm">
                  <strong class="agreement_label">Agreement(en) :</strong>
                </label>
                <textarea id="agreement-en-addForm"></textarea>
                <label for="agreement-zh-addForm">
                  <strong class="agreement_label">Agreement(zh-hk) :</strong>
                </label>
                <textarea id="agreement-zh-addForm"></textarea>
                <label for="agreement-thirdLan-addForm">
                  <strong class="agreement_label">Agreement(thirdLan) :</strong>
                </label>
                <textarea id="agreement-thirdLan-addForm"></textarea>
              </div>
            </div>
          </el-form>
          <!--                底部确认取消按钮-->
          <div class="dialog_footer">
            <el-button style="min-width: 80px" v-if="active!==0" @click="addDialogVisible=false">
              Close
            </el-button>
            <el-button
              style="min-width: 80px"
              type="primary"
              @click="onNext('addForm')"
              :disabled="!showSteps('addForm')"
              v-if="!showSubmitBtn()"
            >
              {{(showSteps('addForm')&&active===Type.length-1)?'Add':'Next'}}
            </el-button>
            <el-button
              style="min-width: 80px"
              type="primary"
              @click="submitDirectly('addForm')"
              v-else
            >
              Submit
            </el-button>
          </div>
        </el-dialog>
      </template>
      <!-- 单元格编辑弹窗 -->
      <template>
        <el-dialog
          title="Edit Dialog"
          class="pub_dialog edit_dialog"
          :visible.sync="editDialogVisible"
          :close-on-click-modal="false"
          @opened="opBaseURLWatch = true"
          @close="opBaseURLWatch = false"
        >
          <el-form :model="editForm" ref="editForm" label-width="250px" :rules="rules">
            <div v-if="!active">
              <el-alert
                title="You can drag and drop payment methods to change the sort order"
                type="warning"
                show-icon
                style="margin-bottom: 20px"
              ></el-alert>
              <!--                StoreNumber-->
              <el-form-item
                ref="editStoreNumber"
                label-width="180px"
                label="Store Number"
                prop="storeNumber"
              >
                <el-input
                  v-model.trim="editForm.storeNumber"
                  disabled
                  placeholder="Please enter the store number"
                ></el-input>
              </el-form-item>
              <!--                付款方式-->
              <div class="pay-type-wrap">
                <el-form-item label-width="180px" label="POSPay method" prop="posPay" data-id="pos">
                  <el-checkbox-group v-model="editForm.payType.pos">
                    <el-checkbox
                      v-for="p in posPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      @change="onPayMeChange($event,p,'pos','editForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="editForm.paymentCode['pos'][p]"
                        @input="onPayMeInput($event,p,'pos','editForm')"
                        @focus="onPayMeFocus($event,p,'pos','editForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label-width="180px" label="EFTPay method" prop="eftPay" data-id="eft">
                  <el-checkbox-group v-model="editForm.payType.eft">
                    <el-checkbox
                      v-for="p in eftPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('eft','editForm')"
                      @change="onPayMeChange($event,p,'eft','editForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="editForm.paymentCode['eft'][p]"
                        :disabled="disablePayType('eft','editForm')"
                        @input="onPayMeInput($event,p,'eft','editForm')"
                        @focus="onPayMeFocus($event,p,'eft','editForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item
                  label-width="180px"
                  label="WindcavePay method"
                  prop="windcavePay"
                  data-id="windcave"
                >
                  <el-checkbox-group v-model="editForm.payType.windcave">
                    <el-checkbox
                      v-for="p in windcavePayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('windcave','editForm',p)"
                      @change="onPayMeChange($event,p,'windcave','editForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="editForm.paymentCode['windcave'][p]"
                        :disabled="disablePayType('windcave','editForm',p)"
                        @input="onPayMeInput($event,p,'windcave','editForm')"
                        @focus="onPayMeFocus($event,p,'windcave','editForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label-width="180px" label="BOCPay method" prop="bocPay" data-id="boc">
                  <el-checkbox-group v-model="editForm.payType.boc">
                    <el-checkbox
                      v-for="p in bocPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('boc','editForm',p)"
                      @change="onPayMeChange($event,p,'boc','editForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="editForm.paymentCode['boc'][p]"
                        :disabled="disablePayType('boc','editForm',p)"
                        @input="onPayMeInput($event,p,'boc','editForm')"
                        @focus="onPayMeFocus($event,p,'boc','editForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item
                  label-width="180px"
                  label="SolelyPay method"
                  prop="solelyPay"
                  data-id="solely"
                >
                  <el-checkbox-group v-model="editForm.payType.solely" :max="1">
                    <el-checkbox
                      v-for="p in solelyPayMethodArr"
                      :label="p"
                      :key="p"
                      :data-id="p"
                      :disabled="disablePayType('solely','editForm',p)"
                      @change="onPayMeChange($event,p,'solely','editForm')"
                    >
                      <el-input
                        size="small"
                        placeholder="Payment code"
                        v-model="editForm.paymentCode['solely'][p]"
                        :disabled="editForm.payType.eft.length!=0||editForm.payType.boc.length!=0||(editForm.payType.solely.length&&editForm.payType.solely[0]!=p)"
                        @input="onPayMeInput($event,p,'solely','editForm')"
                        @focus="onPayMeFocus($event,p,'solely','editForm')"
                      >
                        <div slot="prepend">{{p}}</div>
                      </el-input>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div>
            </div>

            <div v-else style="position: relative">
              <el-steps
                align-center
                :active="active"
                finish-status="success"
                process-status="process"
                style="margin-bottom: 20px"
              >
                <el-step @click.native="changeStep(0,'editForm')" title="PayMethod"></el-step>
                <el-step
                  @click.native="changeStep(1,'editForm')"
                  :status="status.Client"
                  title="Client"
                ></el-step>
                <el-step
                  @click.native="changeStep(2,'editForm')"
                  :status="status.GoodsName"
                  title="GoodsName"
                  v-if="showGoodsName"
                ></el-step>
                <el-step
                  @click.native="changeStep(3,'editForm')"
                  :status="status.PromptText"
                  title="PromptText"
                ></el-step>
                <el-step
                  @click.native="changeStep(4,'editForm')"
                  :status="status.Email"
                  title="Email"
                ></el-step>
                <el-step
                  @click.native="changeStep(5,'editForm')"
                  :status="status.Agreement"
                  title="Agreement"
                ></el-step>
              </el-steps>
              <div v-if="active===1&&isShowFormItem('onlinePayArr1')">
                <!--                商户id-->
                <el-form-item label-width="140px" label="Client ID" prop="clientID">
                  <el-input
                    v-model="editForm.clientID"
                    clearable
                    placeholder="Please enter the client ID"
                  ></el-input>
                </el-form-item>
                <!--                前端引用依赖前缀-->
                <el-form-item
                  label-width="140px"
                  label="Web Base Url"
                  prop="webBaseUrl"
                  v-if="!isBoc&&!isWindcave"
                >
                  <el-input
                    v-model="editForm.webBaseUrl"
                    clearable
                    placeholder="Please enter the base url"
                  ></el-input>
                </el-form-item>
                <!--                后端引用依赖前缀-->
                <el-form-item label-width="140px" label="Service Base Url" prop="serviceBaseUrl">
                  <el-input
                    v-model="editForm.serviceBaseUrl"
                    clearable
                    placeholder="Please enter the base url"
                  ></el-input>
                </el-form-item>
                <!--                公钥-->
                <el-form-item
                  label-width="140px"
                  label="Public Key"
                  prop="spiralPublicKey"
                  v-if="!isWindcave"
                >
                  <el-input
                    type="textarea"
                    v-model="editForm.spiralPublicKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter public key in correct format"
                  ></el-input>
                </el-form-item>
                <!--                私钥-->
                <el-form-item
                  label-width="140px"
                  label="My private key"
                  prop="myPrivateKey"
                  v-if="!isWindcave"
                >
                  <el-input
                    type="textarea"
                    v-model="editForm.myPrivateKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter private key in correct format"
                  ></el-input>
                </el-form-item>
                <!--                api Key -->
                <el-form-item label-width="140px" label="Api Key" prop="apiKey" v-if="isWindcave">
                  <el-input
                    type="textarea"
                    v-model="editForm.apiKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter api key in correct format"
                  ></el-input>
                </el-form-item>
                <!-- 终端号-->
                <el-form-item
                  label-width="140px"
                  label="Terminal No"
                  prop="terminalNo"
                  v-if="isBoc"
                >
                  <el-input
                    v-model="editForm.terminalNo"
                    clearable
                    placeholder="Please enter the terminal no"
                  ></el-input>
                </el-form-item>
                <!-- uiMode界面模式 -->
                <el-form-item label-width="140px" label="UI Mode" prop="uiMode" v-if="isBoc">
                  <el-select
                    v-model="editForm.uiMode"
                    style="width: 100%"
                    placeholder="Please select UI Mode"
                  >
                    <el-option
                      :label="item.label"
                      :value="item.value"
                      v-for="(item,index) in uiModeOptions"
                      :key="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <!-- 控制退款按钮-->
                <el-form-item
                  label-width="180px"
                  label="Show Refund Button"
                  prop="refundable"
                  v-if="isShowRefundBtn"
                >
                  <el-switch v-model="editForm.refundable"></el-switch>
                </el-form-item>
              </div>
              <!-- iPay88商户配置信息 -->
              <div v-else-if="active===1&&isShowFormItem('onlinePayArr2')">
                <!--                商户Merchant Code-->
                <el-form-item
                  label-width="140px"
                  :label="isRazer?'Merchant ID':'Merchant Code'"
                  prop="clientID"
                >
                  <el-input
                    v-model="editForm.clientID"
                    :placeholder="`Please enter the merchant ${isIpay88?'code':'id'}`"
                    clearable
                  ></el-input>
                </el-form-item>
                <!--   后端引用依赖前缀 ￣へ￣ Razer使用 -->
                <el-form-item
                  label-width="140px"
                  label="Service Base Url"
                  prop="serviceBaseUrl"
                  v-if="isRazer"
                >
                  <el-input
                    v-model="editForm.serviceBaseUrl"
                    clearable
                    placeholder="Please enter the base url"
                  ></el-input>
                </el-form-item>

                <!--               商户 Merchant Key-->
                <el-form-item
                  label-width="140px"
                  :label="isIpay88?'Merchant Key':'Verify key'"
                  prop="myPrivateKey"
                >
                  <el-input
                    type="textarea"
                    v-model="editForm.myPrivateKey"
                    :rows="3"
                    class="resizeNone"
                    :placeholder="`Please enter ${isIpay88?'merchant':'verify'} key in correct format`"
                  ></el-input>
                </el-form-item>
                <!--            密钥    -->
                <el-form-item
                  label-width="140px"
                  label="Secret key"
                  prop="spiralPublicKey"
                  v-if="isRazer"
                >
                  <el-input
                    type="textarea"
                    v-model="editForm.spiralPublicKey"
                    :rows="3"
                    class="resizeNone"
                    placeholder="Please enter secret key in correct format"
                    required
                  ></el-input>
                </el-form-item>
                <el-form-item label-width="140px" label="Currency" prop="currency">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="Ensure that UIConfig-CurrencyWay is consistent with the current configuration"
                    placement="bottom-start"
                    :hide-after="2000"
                  >
                    <el-select
                      v-model="editForm.currency"
                      placeholder="Please select payment currency"
                      style="width: 100%"
                    >
                      <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="(item,index) in iPay88CurrencyList"
                        :key="index"
                      ></el-option>
                    </el-select>
                  </el-tooltip>
                </el-form-item>
                <!-- 控制退款按钮-->
                <el-form-item
                  label-width="180px"
                  label="Show Refund Button"
                  prop="refundable"
                  v-if="isShowRefundBtn"
                >
                  <el-switch v-model="editForm.refundable"></el-switch>
                </el-form-item>
              </div>
              <!-- 钱包支付Member Type -->
              <div v-if="active===1&&hasWallet">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="This parameter is only used for wallet payment method configuration"
                  placement="bottom-start"
                  :hide-after="2000"
                >
                  <!--                商户id-->
                  <el-form-item label-width="140px" label="Member Type" prop="memberType">
                    <el-input
                      v-model="editForm.memberType"
                      clearable
                      placeholder="Please enter the Member Type(Use ; to separate multiple Type)"
                    ></el-input>
                  </el-form-item>
                </el-tooltip>
              </div>
              <div v-else-if="active===2">
                <!--                商品name-->
                <el-form-item label="GoodsName(en)" prop="goodsName.en">
                  <el-input
                    v-model="editForm.goodsName.en"
                    placeholder="Please enter the goods name (en)"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="GoodsName(zh-hk)" prop="goodsName.zh">
                  <el-input
                    v-model="editForm.goodsName.zh"
                    placeholder="Please enter the goods name (zh-hk)"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="GoodsName(thirdLan)" prop="goodsName.thirdLan">
                  <el-input
                    v-model="editForm.goodsName.thirdLan"
                    placeholder="Please enter the goods name (thirdLan)"
                    clearable
                  ></el-input>
                </el-form-item>
              </div>
              <div v-else-if="active===3">
                <el-form-item class="dia-form-item-prompt" prop="successPrompt.en">
                  <label for="successPrompt-en-editForm">
                    <strong class="tinyMCEPrompt_label">Success Prompt(en)</strong>
                  </label>
                  <textarea id="successPrompt-en-editForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="successPrompt.zh">
                  <label for="successPrompt-zh-editForm">
                    <strong class="tinyMCEPrompt_label">Success Prompt(zh-hk) :</strong>
                  </label>
                  <textarea id="successPrompt-zh-editForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="successPrompt.thirdLan">
                  <label for="successPrompt-thirdLan-editForm">
                    <strong class="tinyMCEPrompt_label">Success Prompt(thirdLan) :</strong>
                  </label>
                  <textarea id="successPrompt-thirdLan-editForm"></textarea>
                </el-form-item>
                <!-- 失败提示 -->
                <el-form-item class="dia-form-item-prompt" prop="failPrompt.en">
                  <label for="failPrompt-en-editForm">
                    <strong class="tinyMCEPrompt_label">Fail Prompt(en)</strong>
                  </label>
                  <textarea id="failPrompt-en-editForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="failPrompt.zh">
                  <label for="failPrompt-zh-editForm">
                    <strong class="tinyMCEPrompt_label">Fail Prompt(zh-hk) :</strong>
                  </label>
                  <textarea id="failPrompt-zh-editForm"></textarea>
                </el-form-item>
                <el-form-item class="dia-form-item-prompt" prop="failPrompt.thirdLan">
                  <label for="failPrompt-thirdLan-editForm">
                    <strong class="tinyMCEPrompt_label">Fail Prompt(thirdLan) :</strong>
                  </label>
                  <textarea id="failPrompt-thirdLan-editForm"></textarea>
                </el-form-item>
              </div>
              <div v-else-if="active===4">
                <!--                邮箱-->
                <el-form-item label="Email" prop="email">
                  <el-input
                    v-model.trim="editForm.email"
                    placeholder="Please enter the email address"
                  ></el-input>
                </el-form-item>

                <!--              senderName-->
                <!--              :prop="handleProp('senderName','editForm')"-->
                <el-form-item label="Sender name">
                  <el-input
                    v-model="editForm.senderName"
                    placeholder="Please enter the sender name"
                  ></el-input>
                </el-form-item>

                <!--                  邮件方式-->
                <el-form-item label="Smtp host" prop="smtpHost">
                  <el-radio-group
                    v-model="smtpHostDiy"
                    @change="mailWayChange($event,'editForm')"
                    class="smtpHostBox"
                  >
                    <div class="nowEmailRadio">
                      <el-radio size="small" label="smtp.gmail.com">GMAIL</el-radio>
                      <el-radio size="small" label="smtpdm-ap-southeast-1.aliyun.com">
                        DIRECT MAIL
                      </el-radio>
                    </div>
                    <div class="choosableEmail">
                      <el-radio size="mini" class="choosableEmail-el-radio" label="">
                        <el-input
                          class="smtpHostDiy"
                          v-model="smtpHostVal"
                          @focus="handleSmtpHostFocus('editForm')"
                          @input="handleSmtpInput"
                          placeholder="Please enter smtpHost"
                        ></el-input>
                      </el-radio>
                    </div>
                  </el-radio-group>
                </el-form-item>
                <!--                授权码-->
                <el-form-item label="Authorization code" prop="authorizationCode">
                  <el-input
                    v-model="editForm.authorizationCode"
                    placeholder="Please enter the authorization code"
                  ></el-input>
                </el-form-item>
                <!--                邮件主题-->
                <el-form-item label="Email subject" prop="emailSubject">
                  <el-input
                    v-model="editForm.emailSubject"
                    placeholder="Please enter the email subject"
                  ></el-input>
                </el-form-item>
                <!-- 开启:只发pickup邮件,关闭:default/pickup邮件都发 -->
                <el-form-item label="Do not send for advance order">
                  <el-switch v-model="editForm.doNotSendForAdvanceOrder"></el-switch>
                </el-form-item>
                <!--                邮件消息-->
                <div class="emailForm">
                  <el-form-item
                    label="Email message(en)"
                    style="margin-bottom: 20px"
                    prop="emailMessage.en"
                    :required="editForm.email?.length"
                  >
                    <email-editor-toolbar
                      target-id="emailMessage-en-editForm"
                      template-type="default"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="emailMessage-en-editForm"></textarea>
                  </el-form-item>
                  <el-form-item
                    label="Email message(zh-hk)"
                    style="margin-bottom: 20px"
                    prop="emailMessage.zh"
                    :required="editForm.email?.length"
                  >
                    <email-editor-toolbar
                      target-id="emailMessage-zh-editForm"
                      template-type="default"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="emailMessage-zh-editForm"></textarea>
                  </el-form-item>
                  <el-form-item
                    label="Email message(thirdLan)"
                    :required="editForm.email?.length"
                    prop="emailMessage.thirdLan"
                  >
                    <email-editor-toolbar
                      target-id="emailMessage-thirdLan-editForm"
                      template-type="default"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="emailMessage-thirdLan-editForm"></textarea>
                  </el-form-item>
                </div>
                <!--  外卖自取邮件主题            -->
                <el-form-item label="Pickup Email subject">
                  <el-input
                    v-model="editForm.pickupEmailSubject"
                    placeholder="Please enter the pickup email subject"
                  ></el-input>
                </el-form-item>
                <!--                外卖邮件消息-->
                <div class="emailForm">
                  <el-form-item label="Pickup email message(en)" style="margin-bottom: 20px">
                    <email-editor-toolbar
                      target-id="pickupEmailMessage-en-editForm"
                      template-type="pickUp"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="pickupEmailMessage-en-editForm"></textarea>
                  </el-form-item>
                  <el-form-item label="Pickup email message(zh-hk)" style="margin-bottom: 20px">
                    <email-editor-toolbar
                      target-id="pickupEmailMessage-zh-editForm"
                      template-type="pickUp"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="pickupEmailMessage-zh-editForm"></textarea>
                  </el-form-item>
                  <el-form-item label="Pickup email message(thirdLan)">
                    <email-editor-toolbar
                      target-id="pickupEmailMessage-thirdLan-editForm"
                      template-type="pickUp"
                      :insert-content="insertContent"
                      :focus-on-tinymce="focusOnTinymce"
                      :remove-template="removeTemplate"
                    ></email-editor-toolbar>
                    <textarea id="pickupEmailMessage-thirdLan-editForm"></textarea>
                  </el-form-item>
                </div>
              </div>
              <div v-else-if="active===5">
                <!--   协议           -->
                <label for="agreement-en-editForm">
                  <strong class="agreement_label">Agreement(en) :</strong>
                </label>
                <textarea id="agreement-en-editForm"></textarea>
                <label for="agreement-zh-editForm">
                  <strong class="agreement_label">Agreement(zh-hk) :</strong>
                </label>
                <textarea id="agreement-zh-editForm"></textarea>
                <label for="agreement-thirdLan-editForm">
                  <strong class="agreement_label">Agreement(thirdLan) :</strong>
                </label>
                <textarea id="agreement-thirdLan-editForm"></textarea>
              </div>
            </div>
          </el-form>
          <!--                底部确认取消按钮-->
          <div class="dialog_footer">
            <el-button style="min-width: 80px" v-if="active!==0" @click="editDialogVisible=false">
              Close
            </el-button>
            <el-button
              style="min-width: 80px"
              :disabled="!showSteps('editForm')"
              type="primary"
              @click="onNext('editForm')"
              v-if="!showSubmitBtn()"
            >
              {{(showSteps('editForm')&&active===Type.length-1)?'Edit':'Next'}}
            </el-button>
            <el-button
              style="min-width: 80px"
              type="primary"
              @click="submitDirectly('editForm')"
              v-else
            >
              Submit
            </el-button>
          </div>
        </el-dialog>
      </template>
    </div>
    <script>
      const app = new Vue({
        el: "#app",
        filters: {
          showParseStr(e) {
            if (!e) return e
            return e.replaceAll(",", " | ")
          },
          mergePayType(e) {
            let sortKey = Object.keys(app.addForm.payType) //按照默认顺序显示(表单提交字段顺序会打乱)
            let showPayType = []
            sortKey.forEach(key => {
              if (e[key]) showPayType = [...showPayType, ...e[key]]
            })

            return showPayType.toString()
          }
        },
        computed: {
          //判断选择的方式是否存在在线支付true
          // showSteps: _this => form => {
          //   let checkFormPayType = _this[form].payType,
          //     hasEFT = checkFormPayType.eft.length > 0
          //   hasSolely = checkFormPayType.solely.length > 0
          //   hasBoc = checkFormPayType.boc.length > 0
          //   return hasEFT || hasSolely || hasBoc
          // },
          showSteps: _this => form => {
            const payTypes = _this[form].payType
            const typesToCheck = ["eft", "windcave", "solely", "boc"]

            return (
              typesToCheck.some(type => payTypes[type].length > 0) ||
              payTypes.pos.includes("wallet")
            )
          },

          //决定是否显示某个表单项
          isShowFormItem(type) {
            return type => {
              let mapObj = {
                onlinePayArr1: ["eft", "windcave", "boc"],
                onlinePayArr2: ["solely"]
              }
              let currentPayType = this.checkOnlinePay()
              return mapObj[type].includes(currentPayType)
            }
          },
          isShowRefundBtn() {
            const { pos, eft, windcave, boc, solely } = this[this.dialog].payType
            return boc.length || (solely.length && solely.includes("razer"))
          },

          //   当前弹窗是否选中Razer
          isRazer() {
            let checkFormPayType = this[this.dialog].payType
            let hasRazer = checkFormPayType.solely.some(item => item === "razer")
            return hasRazer
          },
          //   当前弹窗是否选中Ipay88
          isIpay88() {
            let checkFormPayType = this[this.dialog].payType
            let hasRazer = checkFormPayType.solely.some(item => item === "iPay88")
            return hasRazer
          },
          //   当前弹窗是否选中Boc
          isBoc() {
            let currentPayType = this.checkOnlinePay()
            return currentPayType === "boc"
          },
          isWindcave() {
            let currentPayType = this.checkOnlinePay()
            return currentPayType === "windcave"
          },
          disablePayType(type, form, payType = "") {
            return (type, form, payType) => {
              let baseTypeArray = this.onlinePayArray.filter(item => item !== type)
              let checkFormPayType = this[form].payType
              let hasBaseType = baseTypeArray.some(item => checkFormPayType[item].length > 0)
              if (type === "boc" && checkFormPayType.boc.includes("ALL")) {
                return payType !== "ALL"
              } else if (type === "windcave" && checkFormPayType.windcave.includes("Host Page")) {
                return payType !== "Host Page"
              } else if (type === "solely" && checkFormPayType.solely.length) {
                return !checkFormPayType.solely.includes(payType)
              } else {
                return hasBaseType
              }
            }
          },
          addFormPayType() {
            return JSON.parse(JSON.stringify(this.addForm.payType))
          },
          editFormPayType() {
            return JSON.parse(JSON.stringify(this.editForm.payType))
          },
          hasWallet() {
            return this[this.dialog].payType.pos.includes("wallet")
          },
          showGoodsName() {
            return this.checkOnlinePay() !== "pos"
          }
        },
        watch: {
          "addForm.payType": {
            deep: true,
            handler(val) {
              //重置 step 状态
            }
          },
          "editForm.payType": {
            deep: true,
            handler(val) {
              //重置 step 状态
            }
          },
          //侦听弹窗关闭,清空数据
          addDialogVisible(val) {
            if (!val) {
              this.active = 0
              this.addForm = JSON.parse(JSON.stringify(this.resetForm))
              this.editForm = JSON.parse(JSON.stringify(this.resetForm))
              this.addForm.webBaseUrl = this.webBaseUrl //手动设置默认值,避免写在resetForm中给editForm赋值
              this.addForm.currency = "MYR" //手动设置默认值,避免写在resetForm中给editForm赋值
              this.addForm.uiMode = 1
              this.resetStepStatus()
              this.dialog = ""
              this.smtpHostVal = ""
              this.smtpHostDiy = "smtp.gmail.com"
              this.payMethodSort = {
                // 恢复默认排序
                pos: [...this.posPayMethodArr],
                eft: [...this.eftPayMethodArr],
                windcave: [...this.windcavePayMethodArr],
                boc: [...this.bocPayMethodArr],
                solely: [...this.solelyPayMethodArr]
              }
              try {
                tinymce.remove()
                tinymce.EditorManager.editors = []
              } catch (e) {
                console.log(e, "销毁失败")
              }
            } else {
              this.dialog = "addForm"
              this.$nextTick(this.initSortable)
            }
          },
          editDialogVisible(val) {
            if (!val) {
              this.active = 0
              // 清空数据
              // this.addForm = JSON.parse(JSON.stringify(this.resetForm))
              this.editForm = JSON.parse(JSON.stringify(this.resetForm))
              this.resetStepStatus()
              this.dialog = ""
              this.smtpHostVal = ""
              this.smtpHostDiy = "smtp.gmail.com"
              try {
                tinymce.remove()
                tinymce.EditorManager.editors = []
              } catch (e) {
                console.log(e, "销毁失败")
              }
              this.payMethodSort = {
                // 恢复默认排序
                pos: [...this.posPayMethodArr],
                eft: [...this.eftPayMethodArr],
                windcave: [...this.windcavePayMethodArr],
                boc: [...this.bocPayMethodArr],
                solely: [...this.solelyPayMethodArr]
              }
            } else {
              this.dialog = "editForm"
            }
          },
          active(val) {
            // const type = ['payMethod', 'Client', 'GoodsName', 'PromptText','Email']
            // this.status.forEach((el, index) => {
            //   if (el == "" && el != this.Type[val]) {
            //     this.status[index] = "wait"
            //   }
            // })

            Object.entries(this.status).forEach(([key, status]) => {
              this.status[key] =
                status === "" && key !== this.Type[val]
                  ? "wait"
                  : key === this.Type[val]
                  ? "process"
                  : status
            })
            // this.status[this.Type[val]] = "process"
            // console.log(this.status, val, 8989)
            try {
              this.$refs.addForm.clearValidate()
            } catch (err) {
              this.$refs.editForm.clearValidate()
            }
          },

          "addForm.email": {
            immediate: true,
            handler(newVal) {
              const propList = ["authorizationCode", "smtpHost", "emailSubject"]
              if (newVal.length) {
                propList.forEach(el => {
                  this.rules[el][0].required = true
                })
                // this.addRule('emailSubject', [{ required: true, message: '请输入银行卡号', trigger: 'blur' }])
              } else {
                propList.forEach(el => {
                  this.rules[el][0].required = false
                })
                // this.addRule('emailSubject', [])
              }
            }
          },
          "editForm.email": {
            immediate: true,
            handler(newVal) {
              const propList = ["authorizationCode", "smtpHost", "emailSubject"]
              if (newVal.length) {
                propList.forEach(el => {
                  this.rules[el][0].required = true
                })
                // this.addRule('emailSubject', [{ required: true, message: '请输入银行卡号', trigger: 'blur' }])
              } else {
                propList.forEach(el => {
                  this.rules[el][0].required = false
                })
                // this.addRule('emailSubject', [])
              }
            }
          },

          addFormPayType(newVal, oldVal) {
            this.resetBaseUrl("addForm", newVal, oldVal)
          },

          editFormPayType(newVal, oldVal) {
            this.resetBaseUrl("editForm", newVal, oldVal)
          }
        },
        data() {
          //验证email邮箱表单
          const validateEmail = function (rule, value, callback) {
            let emailReg =
              /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
            if (value !== "") {
              if (!emailReg.test(value)) {
                callback(new Error("Please input the correct email address"))
              } else {
                callback()
              }
            } else {
              callback()
            }
          }
          const validateEmailMessageEn = function (rule, value, callback) {
            if (value !== "") {
              callback()
            } else if (app[app.dialog].email.length) {
              callback(new Error("Please enter the message(en)"))
            }
          }
          const validateEmailMessageZh = function (rule, value, callback) {
            if (value !== "") {
              callback()
            } else if (app[app.dialog].email.length) {
              callback(new Error("Please enter the message(zh-hk)"))
            }
          }
          const validateEmailMessageThird = function (rule, value, callback) {
            if (value !== "") {
              callback()
            } else if (app[app.dialog].email.length) {
              callback(new Error("Please enter the message(thirdLan)"))
            }
          }
          const validatePickupEmailMessage = function (rule, value, callback) {
            if (value !== "") {
              callback()
            } else if (app[app.dialog].email.length) {
              callback(new Error("Please enter the pickup email message"))
            }
          }
          return {
            tableHeight: 0,
            payMeCodes: {},
            dialog: "", //当前打开的弹窗
            keywords: ["#billNumber", "#amt", "#paymentMethod", "#address"],
            keywordsPickup: ["#pickupTime", "#amt", "#paymentMethod", "#address"],
            step: 0,
            active: 0, //当前步骤
            status: {
              payMethod: "success",
              Client: "",
              GoodsName: "",
              PromptText: "",
              Email: "",
              Agreement: ""
            }, //每一步骤的状态
            domain: sessionStorage.getItem("domain"),
            addDialogVisible: false,
            delDialogVisible: false,
            editDialogVisible: false,
            smtpHostDiy: "smtp.gmail.com", //自定义值
            smtpHostVal: "",
            agreement: {
              agreementTextEn: "",
              agreementTextZh: "",
              agreementTextThird: ""
            },
            rules: {
              storeNumber: [
                {
                  required: true,
                  message: "Please enter the store number",
                  trigger: "blur"
                }
              ],
              type: [
                {
                  required: true,
                  message: "Please choose the type",
                  trigger: "blur"
                }
              ],
              spiralPublicKey: [
                {
                  required: true,
                  message: "This field is required.",
                  trigger: "blur"
                }
              ],
              myPrivateKey: [
                {
                  required: true,
                  message: "This field is required.",
                  trigger: "blur"
                }
              ],
              clientID: [
                {
                  required: true,
                  message: "This field is required.",
                  trigger: "blur"
                }
              ],
              memberType: [
                {
                  required: true,
                  message: "This field is required.",
                  trigger: "blur"
                }
              ],
              webBaseUrl: [
                {
                  required: true,
                  message: "Please enter the base url",
                  trigger: "blur"
                }
              ],
              serviceBaseUrl: [
                {
                  required: true,
                  message: "Please enter the base url",
                  trigger: "blur"
                }
              ],
              terminalNo: [
                {
                  required: true,
                  message: "terminal no",
                  trigger: "blur"
                }
              ],
              currency: [
                {
                  required: true,
                  message: "Please select payment currency",
                  trigger: "blur"
                }
              ],
              uiMode: [
                {
                  required: true,
                  message: "Please select UI Mode",
                  trigger: "blur"
                }
              ],
              goodsName: {
                en: [
                  {
                    required: true,
                    message: "Please enter the goods name (en)",
                    trigger: "blur"
                  }
                ],
                zh: [
                  {
                    required: true,
                    message: "Please enter the goods name (zh-hk)",
                    trigger: "blur"
                  }
                ],
                thirdLan: [
                  {
                    required: true,
                    message: "Please enter the goods name (thirdLan)",
                    trigger: "blur"
                  }
                ]
              },

              successPrompt: {
                en: [
                  {
                    required: true,
                    message: "Please enter the payment success text prompt (en)",
                    trigger: "blur"
                  }
                ],
                zh: [
                  {
                    required: true,
                    message: "Please enter the payment success text prompt (zh-hk)",
                    trigger: "blur"
                  }
                ],
                thirdLan: [
                  {
                    required: true,
                    message: "Please enter the payment success text prompt (thirdLan)",
                    trigger: "blur"
                  }
                ]
              },
              failPrompt: {
                en: [
                  {
                    required: true,
                    message: "Please enter the payment failure text prompt (en)",
                    trigger: "blur"
                  }
                ],
                zh: [
                  {
                    required: true,
                    message: "Please enter the payment failure text prompt (zh-hk)",
                    trigger: "blur"
                  }
                ],
                thirdLan: [
                  {
                    required: true,
                    message: "Please enter the payment failure text prompt (thirdLan)",
                    trigger: "blur"
                  }
                ]
              },
              email: [{ required: false, validator: validateEmail, trigger: "blur" }],
              authorizationCode: [
                {
                  required: true,
                  message: "Please enter the authorization code",
                  trigger: "blur"
                }
              ],
              emailSubject: [
                {
                  required: true,
                  message: "Please enter the email subject",
                  trigger: "blur"
                }
              ],
              senderName: [
                {
                  required: true,
                  message: "Please enter the sender name",
                  trigger: "blur"
                }
              ],
              emailMessage: {
                en: [
                  {
                    validator: validateEmailMessageEn,
                    trigger: "blur"
                  }
                ],
                zh: [
                  {
                    validator: validateEmailMessageZh,
                    trigger: "blur"
                  }
                ],
                thirdLan: [
                  {
                    validator: validateEmailMessageThird,
                    trigger: "blur"
                  }
                ]
              },
              pickupEmailSubject: [
                {
                  required: true,
                  message: "Please enter the pickup email subject",
                  trigger: "blur"
                }
              ],
              pickupEmailMessage: {
                en: [
                  {
                    validator: validatePickupEmailMessage,
                    trigger: "blur"
                  }
                ],
                zh: [
                  {
                    validator: validatePickupEmailMessage,
                    trigger: "blur"
                  }
                ],
                thirdLan: [
                  {
                    validator: validatePickupEmailMessage,
                    trigger: "blur"
                  }
                ]
              },
              smtpHost: [
                {
                  required: true,
                  message: "Please enter custom mail smtpHost",
                  trigger: "blur"
                }
              ],
              apiKey: [
                {
                  required: true,
                  message: "Please enter api key",
                  trigger: "blur"
                }
              ]
            },
            Type: ["payMethod", "Client", "GoodsName", "PromptText", "Email", "Agreement"], //区分类型,,
            addForm: {
              storeNumber: "",
              spiralPublicKey: "", //公钥
              myPrivateKey: "", //自己生成的私钥
              terminalNo: "", //终端号
              uiMode: 1,
              goodsName: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //____未知name
              clientID: "", //id
              memberType: "12", //钱包支付用的类型
              webBaseUrl: "https://sandbox-library-checkout.spiralplatform.com/js/v2/", //前端调用依赖的前缀地址(测试版/正式版)
              serviceBaseUrl: "", //后端引用依赖的前缀地址(测试版/正式版)
              currency: "MYR", //支付货币
              refundable: true, //是否显示退款按钮
              payType: {
                pos: [],
                eft: [],
                windcave: [],
                boc: [],
                solely: []
              }, //支付方式
              paymentCode: {
                pos: {
                  payAtCashier: "",
                  wallet: ""
                },
                eft: {
                  VM: "",
                  UNIONPAY: "",
                  FPS: "",
                  ALIPAYHK: "",
                  ALIPAYCN: "",
                  WECHAT: "",
                  OCTOPUS: "",
                  PAYME: ""
                },
                windcave: {
                  "Host Page": "", 
                  card: "",
                  account2account: "",
                  alipay: "",
                  applepay: "",
                  googlepay: "",
                  paypal: "",
                  interac: "",
                  unionpay: "",
                  oxipay: "",
                  visacheckout: "",
                  wechat: ""
                },
                boc: {
                  ALL: "",
                  BOCPAY: "",
                  ALIPAY: "",
                  ALIPAYMO: "",
                  WECHATPAY: "",
                  TAIFUNGPAY: "",
                  ICBCEPAY: "",
                  MPAY: "",
                  LUSOPAY: "",
                  UEPAY: "",
                  CGBPAY: "",
                  BNUAPP: "",
                  MPGS: "",
                  UNIONPAYQR: ""
                },
                solely: {
                  iPay88: "",
                  razer: ""
                }
              }, //支付方式code
              successPrompt: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付成功提示语句
              failPrompt: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付失败提示语句
              email: "", //email
              authorizationCode: "", //校验码
              emailSubject: "", //邮件主题
              emailMessage: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              pickupEmailMessage: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //外卖邮件内容
              pickupEmailSubject: "", //外卖邮件主题
              doNotSendForAdvanceOrder: false, //只发pickup邮件
              smtpHost: "smtp.gmail.com", //邮件方式,
              senderName: "", //发送人名称
              statement: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付协议
              apiKey: ""
            },
            posPayMethodArr: ["payAtCashier", "wallet"],
            eftPayMethodArr: [
              "VM",
              "UNIONPAY",
              "FPS",
              "ALIPAYHK",
              "ALIPAYCN",
              "WECHAT",
              "OCTOPUS",
              "PAYME"
            ],
            windcavePayMethodArr: [
              "Host Page",
              "card",
              "account2account",
              "alipay",
              "applepay",
              "googlepay",
              "paypal",
              "interac",
              "unionpay",
              "oxipay",
              "visacheckout",
              "wechat"
            ],
            bocPayMethodArr: [
              "ALL",
              "BOCPAY",
              "ALIPAY",
              "ALIPAYMO",
              "WECHATPAY",
              "TAIFUNGPAY",
              "ICBCEPAY",
              "MPAY",
              "LUSOPAY",
              "UEPAY",
              "CGBPAY",
              "BNUAPP",
              "MPGS",
              "UNIONPAYQR"
            ],
            solelyPayMethodArr: ["iPay88", "razer"],
            sortEFTPayMethods: [
              "VM",
              "UNIONPAY",
              "FPS",
              "ALIPAYHK",
              "ALIPAYCN",
              "WECHAT",
              "OCTOPUS",
              "PAYME"
            ],
            editForm: {
              storeNumber: "",
              spiralPublicKey: "", //公钥
              myPrivateKey: "", //自己生成的私钥
              terminalNo: "", //终端号
              uiMode: "",
              goodsName: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //____未知name
              clientID: "", //id
              memberType: "", //钱包支付用的类型
              webBaseUrl: "", //前端引用依赖的前缀地址(测试版/正式版)
              serviceBaseUrl: "", //后端引用依赖的前缀地址(测试版/正式版)
              currency: "", //支付货币
              refundable: true, //是否显示退款按钮
              payType: {
                pos: [],
                eft: [],
                windcave: [],
                boc: [],
                solely: []
              },
              paymentCode: {
                pos: {
                  payAtCashier: "",
                  wallet: ""
                },
                eft: {
                  VM: "",
                  UNIONPAY: "",
                  FPS: "",
                  ALIPAYHK: "",
                  ALIPAYCN: "",
                  WECHAT: "",
                  OCTOPUS: "",
                  PAYME: ""
                },
                windcave: {
                  "Host Page": "",
                  card: "",
                  account2account: "",
                  alipay: "",
                  applepay: "",
                  googlepay: "",
                  paypal: "",
                  interac: "",
                  unionpay: "",
                  oxipay: "",
                  visacheckout: "",
                  wechat: ""
                },
                boc: {
                  ALL: "",
                  BOCPAY: "",
                  ALIPAY: "",
                  ALIPAYMO: "",
                  WECHATPAY: "",
                  TAIFUNGPAY: "",
                  ICBCEPAY: "",
                  MPAY: "",
                  LUSOPAY: "",
                  UEPAY: "",
                  CGBPAY: "",
                  BNUAPP: "",
                  MPGS: "",
                  UNIONPAYQR: ""
                },
                solely: {
                  iPay88: "",
                  razer: ""
                }
              },
              successPrompt: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付成功提示语句
              failPrompt: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付失败提示语句
              email: "", //email
              authorizationCode: "", //校验码
              emailSubject: "", //邮件主题
              emailMessage: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              pickupEmailMessage: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //外卖邮件内容
              pickupEmailSubject: "", //外卖邮件主题
              doNotSendForAdvanceOrder: false, //只发pickup邮件
              smtpHost: "",
              senderName: "",
              statement: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付协议
              apiKey: ""
            },
            resetForm: {
              storeNumber: "",
              spiralPublicKey: "", //公钥
              myPrivateKey: "", //自己生成的私钥
              terminalNo: "", //终端号
              uiMode: "",
              goodsName: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //____未知name
              clientID: "", //id
              memberType: "", //钱包支付用的类型
              refundable: true, //是否显示退款按钮
              webBaseUrl: "", //前端引用依赖的前缀地址(测试版/正式版)
              serviceBaseUrl: "", //后端引用依赖的前缀地址(测试版/正式版)
              currency: "", //支付货币
              payType: {
                pos: [],
                eft: [],
                windcave: [],
                boc: [],
                solely: []
              },
              paymentCode: {
                pos: {
                  payAtCashier: "",
                  wallet: ""
                },
                eft: {
                  VM: "",
                  UNIONPAY: "",
                  FPS: "",
                  ALIPAYHK: "",
                  ALIPAYCN: "",
                  WECHAT: "",
                  OCTOPUS: "",
                  PAYME: ""
                },
                windcave: {
                  "Host Page": "",
                  card: "",
                  account2account: "",
                  alipay: "",
                  applepay: "",
                  googlepay: "",
                  paypal: "",
                  interac: "",
                  unionpay: "",
                  oxipay: "",
                  visacheckout: "",
                  wechat: ""
                },
                boc: {
                  ALL: "",
                  BOCPAY: "",
                  ALIPAY: "",
                  ALIPAYMO: "",
                  WECHATPAY: "",
                  TAIFUNGPAY: "",
                  ICBCEPAY: "",
                  MPAY: "",
                  LUSOPAY: "",
                  UEPAY: "",
                  CGBPAY: "",
                  BNUAPP: "",
                  MPGS: "",
                  UNIONPAYQR: ""
                },
                solely: {
                  iPay88: ""
                }
              },
              successPrompt: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付成功提示语句
              failPrompt: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付失败提示语句
              email: "", //email
              authorizationCode: "", //校验码
              emailSubject: "", //邮件主题
              emailMessage: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              pickupEmailMessage: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //外卖邮件内容
              pickupEmailSubject: "", //外卖邮件主题
              doNotSendForAdvanceOrder: false, //只发pickup邮件
              smtpHost: "smtp.gmail.com",
              senderName: "",
              statement: {
                en: "",
                zh: "",
                thirdLan: ""
              }, //支付协议
              apiKey: ""
            },
            tableData: [],
            empty: false,
            iPay88CurrencyList: [
              { label: "Australian Dolla", value: "AUD" },
              { label: "Canadian Dollar", value: "CAD" },
              { label: "Euro", value: "EUR" },
              { label: "Pound Sterling", value: "GBP" },
              { label: "Hong Kong Dollar ", value: "HKD" },
              { label: "Malaysian Ringgit", value: "MYR" },
              { label: "Singapore Dollar", value: "SGD" },
              { label: "Thailand Baht", value: "THB" },
              { label: "US Dollar", value: "USD" }
            ],
            payMethodSort: {
              pos: ["payAtCashier", "wallet"],
              eft: ["VM", "UNIONPAY", "FPS", "ALIPAYHK", "ALIPAYCN", "WECHAT", "OCTOPUS", "PAYME"],
              windcave: [
                "Host Page",
                "card",
                "account2account",
                "alipay",
                "applepay",
                "googlepay",
                "paypal",
                "interac",
                "unionpay",
                "oxipay",
                "visacheckout",
                "wechat"
              ],
              boc: [
                "ALL",
                "BOCPAY",
                "ALIPAY",
                "ALIPAYMO",
                "WECHATPAY",
                "TAIFUNGPAY",
                "ICBCEPAY",
                "MPAY",
                "LUSOPAY",
                "UEPAY",
                "CGBPAY",
                "BNUAPP",
                "MPGS",
                "UNIONPAYQR"
              ],
              solely: ["iPay88", "razer"],
              order: ["pos", "eft", "windcave", "boc", "solely"]
            },
            sortableInstance: {},
            onlinePayArray: ["eft", "windcave", "boc", "solely"],
            uiModeOptions: [
              { value: 1, label: "Selected Box" },
              { value: 2, label: "QR Code" }
            ],
            webBaseUrl: "https://sandbox-library-checkout.spiralplatform.com/js/v2/",
            serviceBaseUrlObj: {
              eft: "https://sandbox-api-checkout.spiralplatform.com/v1/",
              windcave: "https://uat.windcave.com/api/v1",
              razer: "https://sandbox.merchant.razer.com/RMS/",
              boc: "https://cuaas.bocmacau.com/w/rsa/mercapi_ol"
            },
            lastServiceObj: {
              serviceBaseUrl: "",
              webBaseUrl: "",
              payType: ""
            }, //存储最后一次ServiceType数据
            opBaseURLWatch: false, //是否开启支付方式变化监听
            tinymceDefaultConfig: {}
          }
        },
        created() {
          this.getData()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 50 //数值"140"根据需要调整
        },

        components: {},
        methods: {
          resetBaseUrl(form, newVal, oldVal) {
            if (!this.opBaseURLWatch) return
            const getPayTypeArray = payObj => {
              let payTypeArr = []
              for (let key in payObj) {
                if (payObj[key].length && key != "pos") payTypeArr.push(key)
              }
              return payTypeArr
            }
            const resetUrl = (service, web = this.webBaseUrl) => {
              this[form].serviceBaseUrl = service
              this[form].webBaseUrl = web
            }
            let oldPayType = getPayTypeArray(oldVal)
            let newPayType = getPayTypeArray(newVal)
            // console.log(oldPayType, newPayType, 56)
            let currentPayType = newPayType.toString()
            const isChange = oldPayType.toString() != currentPayType
            if (isChange && newPayType.length) {
              // console.log("payType改变了")
              // 使用映射简化 URL 赋值逻辑
              const serviceBaseUrlMapping = {
                razer: this.serviceBaseUrlObj.razer,
                eft: this.serviceBaseUrlObj.eft,
                windcave: this.serviceBaseUrlObj.windcave,
                boc: this.serviceBaseUrlObj.boc
              }
              let defaultURL = newVal.solely.includes("razer")
                ? serviceBaseUrlMapping.razer
                : serviceBaseUrlMapping[currentPayType]
              if (form == "addForm") {
                resetUrl(defaultURL) //注意多个支付方式共用情况下的重置数据问题!
              } else {
                if (this.lastServiceObj.payType == currentPayType) {
                  // console.log("编辑重置")
                  let { serviceBaseUrl, webBaseUrl } = this.lastServiceObj
                  resetUrl(serviceBaseUrl, webBaseUrl) //注意多个支付方式共用情况下的重置数据问题!
                } else {
                  resetUrl(defaultURL) //注意多个支付方式共用情况下的重置数据问题!
                }
              }
            }
          },
          // 初始化排序插件
          initSortable() {
            let payTypes = ["pos", "eft", "windcave", "boc", "solely"]
            let payMethodNodeList =
              this.dialog === "addForm"
                ? document.querySelectorAll(".add_dialog .el-checkbox-group")
                : document.querySelectorAll(".edit_dialog .el-checkbox-group")
            payTypes.forEach((t, i) => {
              this.sortableInstance[t] = new Sortable(payMethodNodeList[i], {
                sort: true,
                animation: 150,
                onSort: e => {
                  // 重新排序
                  this.sortableInstance[t].toArray().forEach((item, index) => {
                    this.payMethodSort[t][index] = item
                  })
                }
              })
            })
            let payTypeNodeList =
              this.dialog === "addForm"
                ? document.querySelector(".add_dialog .pay-type-wrap")
                : document.querySelector(".edit_dialog .pay-type-wrap")
            this.sortableInstance["order"] = new Sortable(payTypeNodeList, {
              sort: true,
              animation: 150,
              onSort: e => {
                let sort = this.sortableInstance["order"].toArray()
                let payMethodSort = {}
                sort.forEach((item, index) => {
                  payMethodSort[item] = this.payMethodSort[item]
                })
                this.payMethodSort = payMethodSort
                this.payMethodSort["order"] = sort
              }
            })
            //对payType和 payMethods恢复排序
            for (let t in this.payMethodSort) {
              this.sortableInstance[t].sort(this.payMethodSort[t] || [])
            }
          },
          onPayMeInput(e, target, type, form) {
            // console.log(e, this[form]["paymentCode"][type][target])
          },
          onPayMeFocus(e, target, type, form) {
            // 确保 target 未被添加过
            const payType = this[form]["payType"][type]
            if (!payType.includes(target)) {
              payType.push(target)
            }

            if (e) {
              if (type === "boc") this.checkPayTypeForBoc(type)
              if (type === "windcave") this.checkPayTypeForWindcave(type)
            }
          },
          onPayMeChange(e, target, type, form) {
            if (!e) {
              // console.log("清空code", target, this[form]["paymentCode"])
              // 为false,清空code
              this[form]["paymentCode"][type][target] = ""
            } else {
              if (type === "boc") this.checkPayTypeForBoc(type)
              if (type === "windcave") this.checkPayTypeForWindcave(type)
            }
          },
          checkPayTypeForBoc(type) {
            let form = this.dialog == "addForm" ? "addForm" : "editForm"
            if (this[form].payType.boc.includes("ALL")) {
              //如果选择了ALL,则清空其他选项
              this[form].payType.boc = ["ALL"]
              //清空payType.boc其他选项的code
              this.bocPayMethodArr.forEach(item => {
                if (item !== "ALL") {
                  this[form]["paymentCode"][type][item] = ""
                }
              })
            }
          },
          checkPayTypeForWindcave(type) {
            let form = this.dialog == "addForm" ? "addForm" : "editForm"
            if (this[form].payType.windcave.includes("Host Page")) {
              //如果选择了ALL,则清空其他选项
              this[form].payType.windcave = ["Host Page"]
              //清空payType.boc其他选项的code
              this.windcavePayMethodArr.forEach(item => {
                if (item !== "Host Page") {
                  this[form]["paymentCode"][type][item] = ""
                }
              })
            }
          },
          tinymceInitForAgreement() {
            let suffix = this.dialog == "addForm" ? "-addForm" : "-editForm"

            this.$nextTick(() => {
              const languages = ["en", "zh", "thirdLan"]

              languages.forEach(lang => {
                tinymce.init({
                  selector: `#agreement-${lang}${suffix}`,
                  height: 300,
                  setup: editor => {
                    editor.on("Change", () => {
                      this[this.dialog].statement[lang] = editor.getContent()
                    })
                    editor.on("init", () => {
                      editor.setContent(this[this.dialog].statement[lang])
                    })
                  }
                })
              })
            })
          },
          tinymceInitForPrompt() {
            let suffix = this.dialog == "addForm" ? "-addForm" : "-editForm"
            this.$nextTick(() => {
              const prompts = [
                { type: "successPrompt", langs: ["en", "zh", "thirdLan"] },
                { type: "failPrompt", langs: ["en", "zh", "thirdLan"] }
              ]
              prompts.forEach(prompt => {
                prompt.langs.forEach(lang => {
                  tinymce.init({
                    selector: `#${prompt.type}-${lang}${suffix}`,
                    height: 300,
                    setup: editor => {
                      editor.on("Change", () => {
                        this[this.dialog][prompt.type][lang] = editor.getContent()
                      })
                      editor.on("init", () => {
                        editor.setContent(this[this.dialog][prompt.type][lang])
                      })
                    }
                  })
                })
              })
            })
          },

          handleSmtpInput(e) {
            this[this.dialog].smtpHost = e
          },
          //选择的mailWay改变
          mailWayChange(e, form) {
            this[form].smtpHost = e
            if (e) {
              this.smtpHostVal = ""
              try {
                this.$refs.addForm.validateField("smtpHost")
              } catch {
                this.$refs.editForm.validateField("smtpHost")
              }
            } else {
              this[form].smtpHost = ""
            }
            console.log(e, this[form])
          },
          handleSmtpHostFocus(form) {
            this.smtpHostDiy = ""
            if (
              this[form].smtpHost === "smtp.gmail.com" ||
              this[form].smtpHost === "smtpdm-ap-southeast-1.aliyun.com"
            ) {
              this[form].smtpHost = ""
            }
          },
          //用于模板回显邮箱信息,包含标签tag
          showTag(val) {
            if (val) {
              let echoStr = val
              let allTag = [...this.keywords, ...this.keywordsPickup]
              //拿到所有的标签的位置索引
              allTag.forEach(v => {
                echoStr = echoStr.replaceAll(
                  v,
                  `<span class="el-tag el-tag--danger el-tag--plain tagg" contenteditable="false">${v}</span>`
                )
              })
              // console.log(echoStr, "html")
              return echoStr
            }
            return ""
          },
          //动态的改变prop验证(填写email页的表单会一直触发事件)
          handleProp(prop, form) {
            //若email有值,则返回该prop
            if (this[form].email) {
              return prop
            } else {
              //没有值则清除验证
              // this.$refs[form].clearValidate(prop)
              try {
                this.$refs.addForm.clearValidate(prop)
              } catch (e) {
                this.$refs.editForm.clearValidate(prop)
              }
            }
            return ""
          },
          //排序付款方式(在提交请求前执行)
          sortPayMethod(arr, indexArr) {
            let _arr = []
            const overArr = [...arr]
            indexArr.forEach(item => {
              arr.forEach((ele, index) => {
                if (item === ele) {
                  _arr.push(ele)
                  overArr.splice(overArr.indexOf(ele), 1)
                }
              })
            })
            overArr.sort((a, b) => {
              return a - b
            })
            _arr = _arr.concat(overArr)
            return _arr
          },

          changeStep(step, form) {
            //重复点击当前步骤不响应
            if (step === this.active) return

            let stepTwoParams = this.checkOnlinePay() !== "pos" ? ["clientID"] : []
            if (this.hasWallet) stepTwoParams.push("memberType")

            let _key = [
              "payType",
              stepTwoParams, //"spiralPublicKey","myPrivateKey"
              "goodsName",
              ["successPrompt", "failPrompt"],
              ["email", "smtpHost", "authorizationCode", "emailMessage", "emailSubject"],
              []
            ] //senderName不需要验证
            let addParams = this.payTypeMapKey(form, "checkType")
            if (addParams) _key[1].push(...addParams) //动态添加校验参数

            //处理未顺序点击步骤跳转,步骤之间的表单判断
            this.verifyStepForm(step, _key, form)
            let checkData = _key[this.active]
            //处理当前步骤的表单验证和跳转去的表单验证:
            //对比_key,根据类型来验证,String直接验证,Array需要循环验证
            if (typeof checkData === "string") {
              this.status[this.Type[this.active]] = this.handleEmptyData([this[form][checkData]])
                ? "error"
                : "success"
            } else {
              let _a = []
              checkData.forEach(i => {
                _a.push(this[form][i] || "")
              })
              this.status[this.Type[this.active]] = this.handleEmptyData(_a) ? "error" : "success"
            }
            //如果当前步骤为email,则判断表单中的email是否已经填写
            //已填写则需要判断状态(已判断),未填写则状态为success
            if (this.Type[this.active] === "Email") {
              if (!this[form].email) {
                this.status["Email"] = "success"
              }
            }
            tinymce.remove()
            this.active = step
            const initTinymceFunctions = {
              3: this.tinymceInitForPrompt,
              4: this.tinymceInitForEmail,
              5: this.tinymceInitForAgreement
            }

            if (initTinymceFunctions[this.active]) {
              initTinymceFunctions[this.active].call(this)
            } else {
              // 判断是否存在.tox tox-tinymce，遍历删除所有的tox tox-tinymce（tinymce.remove删除无效）
              const tox = document.querySelectorAll(".tox")
              if (tox.length) {
                tox.forEach(item => {
                  item.remove()
                })
              }
            }
            // 需要重新初始化排序
            if (this.active === 0) {
              this.$nextTick(this.initSortable)
            }
          },
          //校验步骤跳转,验证跳转步骤间的表单状态
          verifyStepForm(step, key, form) {
            let arr = this.findMedianNumber(step, this.active)

            if (arr.length) {
              if (!this.showGoodsName) {
                arr = arr.filter(i => i !== 2)
              }
              arr.forEach(i => {
                if (typeof key[i] === "string") {
                  this.status[this.Type[i]] = this.handleEmptyData([this[form][key[i]]])
                    ? "error"
                    : "success"
                } else {
                  let _a = []
                  //4:邮箱 ,需要验证是否已经填写
                  if (i === 4 && !this[form].email) {
                    this.status["Email"] = "success"
                  } else {
                    //其他情况
                    key[i].forEach(q => {
                      _a.push(this[form][q] || "")
                    })
                    this.status[this.Type[i]] = this.handleEmptyData(_a) ? "error" : "success"
                  }
                }
              })
            }
          },

          //拿到2个数字之间的中间值,返回数组[]
          findMedianNumber(a, b) {
            let _a = a
            let _b = b
            let arr = []
            if (Math.abs(_a - _b) > 1) {
              while (_a > _b) {
                _b + 1 < _a ? arr.push(_b + 1) : ""
                _b++
              }
              while (_a < _b) {
                _a + 1 < _b ? arr.push(_a + 1) : ""
                _a++
              }
            }
            return arr
          },
          //用于模板显示的;解析对象用|分割
          showParseObj(e) {
            if (!e) return
            let obj = typeof e == "object" ? e : JSON.parse(e)
            if (this.handleEmptyData([obj], true)) {
              return ""
            }
            let str = ""
            for (const key in obj) {
              if (obj.hasOwnProperty(key)) {
                str += obj[key] + " | "
              }
            }
            str = str.substring(0, str.lastIndexOf("|"))
            return str
          },
          //判断对象的值是否存在空值,用于表单验证跳转

          /**
           *
           * @param  objArr  Array :放置json对象的数组(需要判断的多个/1个对象)
           * @param  areEmpty Boolean :为true,则代表验证对象下所有键值是否为空(仅仅适用于单一对象)
           * @param  exclude Array :排除对象中的keys
           * @use 判断对象是否存在键值为空('',null)
           */
          handleEmptyData(objArr = [], areEmpty = false, exclude = []) {
            this.empty = false
            if (objArr.length) {
              //存在
              if (areEmpty || exclude.length) {
                if (objArr.length === 1) {
                  let temp = JSON.parse(JSON.stringify(objArr[0]))
                  //排除不需要判断的字段
                  if (exclude.length) {
                    exclude.forEach(i => {
                      if (temp.hasOwnProperty(i)) {
                        delete temp[i]
                      }
                    })
                  }
                  // console.log(temp)
                  //判空
                  if (areEmpty) {
                    // console.log(areEmpty, "areEmpty")
                    return Object.values(temp).every(e => !e)
                  } else {
                    console.log([temp], "temp")
                    this.empty = this.hasEmpty([temp])
                  }
                } else {
                  throw new Error("需要判断的对象仅能为1个")
                }
              } else {
                //直接判空
                this.empty = this.hasEmpty(objArr)
              }
              return this.empty
            }
          },
          hasEmpty(arr) {
            arrLabel: for (let o = 0; o < arr.length; ++o) {
              if (!Object.keys(arr[o]).length) {
                if (arr[o] !== "" && arr[o] !== null && arr[o] !== undefined) {
                  console.log(arr[o], "可能为数字或其他类型,不归类error")
                } else {
                  this.empty = true
                  return true
                }
              }
              for (const i in arr[o]) {
                if (arr[o].hasOwnProperty(i)) {
                  if (arr[o][i] instanceof Object && !(arr[o][i] instanceof Array)) {
                    this.hasEmpty([arr[o][i]])
                    //  循环结束
                  }
                  if (arr[o][i] instanceof Array && !arr[o][i].length) {
                    console.log(arr[o], arr[o][i], "判空错误Array")
                    this.empty = true
                    break arrLabel
                  }
                  if (arr[o][i] === "") {
                    console.log(arr[o], i, "判空错误为空")
                    this.empty = true
                    break arrLabel
                  }
                }
              }
            }
            return this.empty
          },
          //请求所有数据
          getData() {
            $.get({
              url: "../../manager_payment/getAll",
              dataType: "json",
              success: res => {
                if (res.statusCode === 200) {
                  console.log(res, "初始请求数据")
                  this.tableData = res.data
                } else {
                  this.$message.error("Request failed, please try again")
                }
              },
              error: () => {
                this.$message.error("Request failed, please try again")
              }
            })
          },
          /**
           * @use 验证重复storeNumber或其他名称
           * @param arr:验证的数组
           * @param key:用于验证的键
           * @param value:验证的值
           */
          verifyDuplicates(arr, key, value) {
            for (let i = 0; i < arr.length; ++i) {
              if (arr[i].hasOwnProperty(key) && arr[i][key] === value) {
                return true
              }
            }
            return false
          },
          //点击submit,可以直接提交请求(验证表单后)
          submitDirectly(form) {
            //验证当前表单
            // 支付方式必须存在值,但多种支付方式不必选
            let { pos, eft, windcave, boc, solely } = this[form].payType
            let hasPayMethod = [...pos, ...eft, ...windcave, ...boc, ...solely].length !== 0
            this.$refs[form].validate(valid => {
              if (valid && hasPayMethod) {
                //判断是否存在在线支付,存在则验证全部表单,不存在则直接提交请求
                if (this.showSteps(form)) {
                  console.log(this[form], "this[form]")
                  // this.handleEmptyData([this[form]])
                  let emptyRes = this.beforeSubVerify(form)
                  emptyRes
                    ? this.$message.error("There are still unfilled forms...")
                    : form === "addForm"
                    ? this.addOne()
                    : this.editOne()
                } else {
                  form === "addForm" ? this.addOne() : this.editOne()
                }
              } else {
                this.$message.error("Please enter complete information")
              }
            })
          },
          //在提交请求前验证支付方式,是否清除表单无用数据
          validateDataBeforeRequest(form) {
            let cloneResetForm = JSON.parse(JSON.stringify(this.resetForm))
            if (this.showSteps(form)) {
              //存在在线支付,清除部分支付方式配置
              let delKey = this.payTypeMapKey(form, "ignoreType")
              if (delKey) {
                delKey.forEach(i => {
                  this[form][i] = cloneResetForm[i]
                })
              }
            } else {
              //只存在pos类别支付,清除已填无用表单信息
              this[form] = {
                ...cloneResetForm,
                storeNumber: this[form].storeNumber,
                payType: this[form].payType,
                paymentCode: {
                  ...cloneResetForm.paymentCode,
                  pos: this[form].paymentCode.pos
                }
              }
            }
          },
          //点击add\next
          onNext(form) {
            let formVerify = false //form表单验证结果
            let lastStep = false //是否到达最后一个进度
            this.$refs[form].validate(valid => {
              if (valid) {
                formVerify = true
              } else {
                console.log(this[this.dialog])
                this.$message.error("Please enter complete information")
              }
            })
            // 当在第一步时,重置step状态
            if (this.active === 0) {
              this.resetStepStatus()
            }
            //当前为email步骤页面且email未填写,则直接通过当前页表单验证
            if (this.active === 4 && !this[form].email) formVerify = true
            //判断storeNumber是否重复(仅addForm验证)
            if (
              this.active === 0 &&
              this.verifyDuplicates(this.tableData, "storeNumber", this.addForm.storeNumber) &&
              form === "addForm"
            ) {
              let errorMsg = `'${this.addForm.storeNumber}' already exists, please edit`
              this.$refs["addStoreNumber"].error = errorMsg
              this.$message.error(errorMsg)
              return
            }
            //在线支付&&通过表单验证
            if (this.showSteps(form) && formVerify) {
              if (this.active < this.Type.length - 1) {
                //改变当前步骤状态
                this.status[this.Type[this.active]] = "success"
                if (this.checkOnlinePay() == "pos" && this.hasWallet && this.active == 1) {
                  //只存在pos类中的钱包支付方式
                  this.active = 3 //手动跳过GoodsName第二步骤
                } else {
                  this.active++
                }

                const initTinymceFunctions = {
                  3: this.tinymceInitForPrompt,
                  4: this.tinymceInitForEmail,
                  5: this.tinymceInitForAgreement
                }
                if (initTinymceFunctions[this.active]) {
                  tinymce.remove()
                  initTinymceFunctions[this.active].call(this)
                }
              } else {
                lastStep = true
              }
            }
            //在线支付&&到达最后一个步骤&&表单验证  或者   柜台支付&&表单验证
            if (
              (this.showSteps(form) && lastStep && `formVerify`) ||
              (!this.showSteps(form) && formVerify)
            ) {
              //在验证所有表单 (true:存在空值) &&  到达最后一个步骤(存在在线支付)
              let emptyRes = this.beforeSubVerify(form)
              if (lastStep && emptyRes) {
                this.$message.error("There are still unfilled forms...")
              } else {
                form === "addForm" ? this.addOne() : this.editOne()
              }
            }
          },
          addOne() {
            this.sortFormData("addForm")
            this.validateDataBeforeRequest("addForm")
            // 排序 eddForm的payType
            this.sortPayTypeBeforeRequest()
            let paymentCode = this.getPaymentCode()
            let data = {
              ...this.addForm,
              paymentCode, //过滤不是当前支付方式的paymentCode
              domain: this.domain
            }
            console.log(data, "data")
            delete data.type
            // data.payType=data.payType.toString()
            $.ajax({
              url: "../../manager_payment/addOne",
              type: "post",
              dataType: "JSON",
              contentType: "application/json",
              data: JSON.stringify(data),
              success: res => {
                if (res.statusCode === 200) {
                  this.getData()
                  this.$message.success("Successfully added！")
                  this.addDialogVisible = false
                } else {
                  this.$message.error("Fail to add, please try again")
                }
              },
              error: () => {
                // console.log(res);
                this.$message.error("Fail to add, please try again")
              }
            })
          },
          onCopy(index, row) {
            switch (row.smtpHost) {
              case "smtp.gmail.com":
                this.smtpHostDiy = "smtp.gmail.com"
                break
              case "smtpdm-ap-southeast-1.aliyun.com":
                this.smtpHostDiy = "smtpdm-ap-southeast-1.aliyun.com"
                break
              default:
                this.smtpHostDiy = ""
                this.smtpHostVal = row.smtpHost
                break
            }
            let form = JSON.parse(JSON.stringify(row))
            form.storeNumber = ""
            this.addForm = this.completeMissingFields(
              form,
              JSON.parse(JSON.stringify(this.resetForm))
            ) //补齐缺失字段

            this.addDialogVisible = true
          },
          onEdit(index, row) {
            let { payType, webBaseUrl = "", serviceBaseUrl } = row
            this.lastServiceObj = {
              serviceBaseUrl,
              webBaseUrl,
              payType: this.checkPayType(payType)
            }
            this.mergeSortPayType(row.payType)
            // this.smtpHostVal=row.smtpHost
            switch (row.smtpHost) {
              case "smtp.gmail.com":
                this.smtpHostDiy = "smtp.gmail.com"
                break
              case "smtpdm-ap-southeast-1.aliyun.com":
                this.smtpHostDiy = "smtpdm-ap-southeast-1.aliyun.com"
                break
              default:
                this.smtpHostDiy = ""
                this.smtpHostVal = row.smtpHost
                break
            }
            this.editDialogVisible = true
            let form = JSON.parse(JSON.stringify(row))

            // this.editForm = { ...JSON.parse(JSON.stringify(this.resetForm)), ...form }
            this.editForm = this.completeMissingFields(
              form,
              JSON.parse(JSON.stringify(this.resetForm))
            ) //补齐缺失字段
            console.log(JSON.parse(JSON.stringify(this.editForm)), "this.editForm")
            this.$nextTick(this.initSortable)
          },
          // 拿到payType的每一项,合并至payMethodSort的每一项前面,再去重
          mergeSortPayType(payType) {
            for (let type in payType) {
              if (type === "order") continue
              this.payMethodSort[type] = [
                ...new Set([...payType[type], ...(this.payMethodSort[type] || [])])
              ]
            }
            this.payMethodSort["order"] = Object.keys(payType)
          },
          editOne() {
            console.log(this.editForm, "editForm")
            this.sortFormData("editForm")
            this.validateDataBeforeRequest("editForm")
            // 排序 editForm的payType
            this.sortPayTypeBeforeRequest()
            let paymentCode = this.getPaymentCode()
            let data = {
              ...this.editForm,
              paymentCode, //过滤不是当前支付方式的paymentCode
              domain: this.domain
            }
            $.post({
              url: "../../manager_payment/updateOne",
              dataType: "JSON",
              contentType: "application/json",
              data: JSON.stringify(data),
              success: res => {
                if (res.statusCode === 200) {
                  this.$message.success("Edit success！")
                  this.editDialogVisible = false
                } else {
                  this.$message.error("Fail to edit, please try again")
                }
                this.getData()
              },
              error: () => {
                this.$message.error("Fail to edit, please try again")
              }
            })
          },
          // 再请求前,对payType方法进行排序
          sortPayTypeBeforeRequest() {
            let { payType } = this[this.dialog]
            let sort = this.sortableInstance.order.toArray()

            // 排序payType 列表 ,移除payMethodSort中不存在的payType的每一项
            let payTypeSort = {}
            sort.forEach(type => {
              payTypeSort[type] = payType[type].sort((a, b) => {
                return this.payMethodSort[type].indexOf(a) - this.payMethodSort[type].indexOf(b)
              })
              payTypeSort[type] = payType[type].filter(item => {
                return this.payMethodSort[type].indexOf(item) !== -1
              })
            })
            payType.order = this.payMethodSort.order
            this[this.dialog].payType = payTypeSort
          },
          onDel(index, row) {
            let { domain, storeNumber } = row
            $.post({
              url: "../../manager_payment/deleteOne",
              data: { domain, storeNumber },
              dataType: "json",
              success: res => {
                if (res.statusCode === 200) {
                  this.$message.success("Successfully delete")
                } else {
                  this.$message.error("Fail to delete, please try again")
                }
                this.getData()
              },
              error: () => {
                // console.log(res);
                this.$message.error("Fail to delete, please try again")
              }
            })
          },

          //动态添加支付校验参数
          payTypeMapKey(form, type) {
            let { eft, windcave, boc, solely, pos } = this[form].payType
            let hasWallet = pos.includes("wallet")
            let onlyPos = this.checkOnlinePay() == "pos"
            const posIgnoreData = () => {
              let baseData = hasWallet ? [] : ["memberType"]
              let onlyPosData = [
                "clientID",
                "apiKey",
                "currency",
                "goodsName",
                "terminalNo",
                "uiMode",
                "refundable",
                "serviceBaseUrl",
                "spiralPublicKey",
                "myPrivateKey",
                "webBaseUrl",
                "smtpHost"
              ]
              if (onlyPos) {
                return [...onlyPosData, ...baseData]
              } else {
                return baseData
              }
            }
            let getTypeData = {
              // checkType为 跳转步骤前需要校验的字段
              checkType: {
                pos: hasWallet ? ["memberType"] : [],
                eft: ["spiralPublicKey", "myPrivateKey", "webBaseUrl", "serviceBaseUrl"], //eft需要动态添加校验的字段
                windcave: ["apiKey", "serviceBaseUrl"],
                boc: ["spiralPublicKey", "serviceBaseUrl", "myPrivateKey", "terminalNo", "uiMode"], //boc需要动态添加校验的字段
                solely: {
                  iPay88: ["currency", "myPrivateKey", "statement"],
                  razer: ["myPrivateKey", "spiralPublicKey", "serviceBaseUrl", "statement"]
                } //solely需要动态添加校验的字段
              },
              // ignoreType 为提交请求前校验全部表单时需要忽略的字段
              ignoreType: {
                pos: posIgnoreData(),
                eft: ["currency", "terminalNo", "uiMode", "refundable", "apiKey"], //eft需要动态添加忽略校验的字段
                windcave: [
                  "currency",
                  "terminalNo",
                  "uiMode",
                  "refundable",
                  "myPrivateKey",
                  "spiralPublicKey",
                  "webBaseUrl"
                ],
                boc: ["currency", "webBaseUrl", "apiKey"],
                solely: {
                  iPay88: [
                    "webBaseUrl",
                    "spiralPublicKey",
                    "serviceBaseUrl",
                    "terminalNo",
                    "uiMode",
                    "refundable",
                    "apiKey"
                  ],
                  razer: ["webBaseUrl", "terminalNo", "uiMode", "apiKey"]
                } //solely需要动态添加忽略校验的字段
              }
            }
            let result = []
            if (eft.length != 0) {
              result = getTypeData[type].eft
            } else if (windcave.length != 0) {
              result = getTypeData[type].windcave
            } else if (boc.length != 0) {
              result = getTypeData[type].boc
            } else if (solely.length != 0) {
              let methods = solely[0]
              result = getTypeData[type].solely[methods]
            }

            result = [...result, ...getTypeData[type].pos]

            return result
          },
          // 重新对数据排序(针对BYOD显示的数据用)
          sortFormData(form) {
            //重新排序payMethod数组
            let { eft } = this[form].payType
            if (eft.length != 0) {
              this[form].payType.eft = this.sortPayMethod(eft, this.sortEFTPayMethods)
            }
          },
          // 提交之前重新校验忽略字段
          beforeSubVerify(form) {
            // console.log(this[form], "最后this[form]")
            // 动态忽略参数,ipay88与eft必传参数有差异
            let defaultIgnore = ["payType", "doNotSendForAdvanceOrder"] //默认忽略
            let dynamicIgnore = [
              "senderName",
              "pickupEmailSubject",
              "pickupEmailMessage",
              "paymentCode"
            ] //动态忽略(支付方式选择性忽略)
            let emailIgnore = [
              "email",
              "smtpHost",
              "authorizationCode",
              "emailSubject",
              "emailMessage"
            ] //固定email忽略
            let windwaveIgnore = ["spiralPublicKey", "myPrivateKey"]
            let addParams = this.payTypeMapKey(form, "ignoreType")
            if (addParams) dynamicIgnore.push(...addParams)
            if (this.isWindcave) dynamicIgnore.push(...windwaveIgnore)
            console.log(dynamicIgnore, "dynamicIgnore")

            let emptyRes = this[form].email.length
              ? this.handleEmptyData([this[form]], false, [...defaultIgnore, ...dynamicIgnore])
              : this.handleEmptyData([this[form]], false, [
                  ...defaultIgnore,
                  ...dynamicIgnore,
                  ...emailIgnore
                ])
            return emptyRes
          },
          // 弹窗第一页提交按钮隐藏
          showSubmitBtn() {
            // 当active不为0时,并且支付方法只是选择了在线支付时,显示提交按钮
            // 判断打开弹窗是新增还是编辑
            let isAdd = this.addDialogVisible
            let isFirstPage = this.active == 0
            let isOnlinePay = this.showSteps(isAdd ? "addForm" : "editForm")
            // pos支付数据不为0
            let isPosPay = this[isAdd ? "addForm" : "editForm"].payType.pos.length != 0
            return isFirstPage && !isOnlinePay && isPosPay
          },
          checkOnlinePay() {
            if (this.dialog == "") return
            const { pos, eft, windcave, boc, solely } = this[this.dialog].payType
            if (eft.length) {
              return "eft"
            } else if (windcave.length) {
              return "windcave"
            } else if (boc.length) {
              return "boc"
            } else if (solely.length) {
              return "solely"
            } else if (pos.length) {
              return "pos"
            }
          },
          //数据补全
          completeMissingFields(returnedData, basicData) {
            for (let key in basicData) {
              if (basicData.hasOwnProperty(key)) {
                if (typeof basicData[key] === "object" && typeof returnedData[key] === "object") {
                  // 如果属性是对象，则递归调用 completeMissingFields 方法
                  returnedData[key] = this.completeMissingFields(returnedData[key], basicData[key])
                } else if (!returnedData.hasOwnProperty(key)) {
                  // 如果属性缺失，则将其添加到返回的数据对象中
                  returnedData[key] = basicData.hasOwnProperty(key) ? basicData[key] : "" // 可以根据需要设置默认值
                }
              }
            }
            return returnedData
          },
          checkPayType(payTypeObj = {}) {
            let { pos, eft, windcave, boc, solely } = payTypeObj
            if (eft && eft.length) {
              return "eft"
            } else if (windcave && windcave.length) {
              return "windcave"
            } else if (boc && boc.length) {
              return "boc"
            } else if (solely && solely.length) {
              return "solely"
            } else if (pos && pos.length) {
              return "pos"
            }
          },
          tinymceInitForEmail() {
            let form = this.dialog == "addForm" ? "addForm" : "editForm"

            let tinymceArr = [
              "emailMessage-en",
              "emailMessage-zh",
              "emailMessage-thirdLan",
              "pickupEmailMessage-en",
              "pickupEmailMessage-zh",
              "pickupEmailMessage-thirdLan"
            ]
            this.$nextTick(() => {
              tinymceArr.forEach(item => {
                tinymce.init({
                  selector: `#${item}-${form}`,
                  height: 700,
                  plugins: "preview code", //字符串方式
                  branding: false, //关闭底部品牌
                  elementpath: false, //关闭底部元素路径
                  content_style: `
                  .mce-item-table:not([border]) td,
                  .table:not([border]) td,
                  .mce-item-table {
                    border: unset !important;
                    padding: unset ;
                  }`,
                  setup: editor => {
                    const [mainProp, subProp] = item.split("-")
                    editor.on("Change", () => {
                      this[form][mainProp][subProp] = editor.getContent()
                    })
                    editor.on("init", () => {
                      editor.setContent(this[form][mainProp][subProp])
                      // if (form == "editForm") {
                      //   editor.setContent(this[form][mainProp][subProp])
                      // }
                    })
                  },
                  init_instance_callback: editor => {
                    // editor.setContent(this.addForm[item.split("-")[1]])
                  }
                })
              })
            })
          },
          insertTemplate(id) {
            console.log("Insert template:", id)
            // Your code to insert template
          },
          insertContent(type, id, lan) {
            const contentMap = {
              billNumber: "<span>#billNumber</span>",
              amt: "<span>#amt</span>",
              paymentMethod: "<span>#paymentMethod</span>",
              address: "<span>#address</span>",
              pickupTime: "<span>#pickupTime</span>",
              default: defaultTemplate(lan),
              pickUp: pickUpTemplate(lan)
            }

            const content = contentMap[type]

            if (content) {
              this.insertContentToEditor(id, content)
            } else {
              console.error(`Content type "${type}" is not defined.`)
            }
          },
          insertContentToEditor(editorId, content) {
            const editor = tinymce.get(editorId)
            if (editor) {
              editor.execCommand("mceInsertContent", false, content)
            } else {
              console.error("Editor with ID " + editorId + " not found.")
            }
          },
          removeTemplate(type, id) {
            const editor = tinymce.get(id)
            if (editor) {
              const dom = editor.dom
              const orderTemplateDivs = dom.select("div.orderTemplate")
              // 遍历获取orderTemplateDivs的上下兄弟元素
              orderTemplateDivs.forEach(div => {
                const prev = div.previousSibling
                const next = div.nextSibling
                // 如果上一个兄弟元素是文本节点并且内容为换行符，则删除
                //判断如果是p的class为placeholderLabel,则删除元素
                if (prev && prev.nodeName === "P" && prev.classList.contains("placeholderLabel")) {
                  dom.remove(prev)
                }
                if (next && next.nodeName === "P" && next.classList.contains("placeholderLabel")) {
                  dom.remove(next)
                }
                // 删除当前div元素
                dom.remove(div)
              })

              // 重新设置焦点以确保编辑器不会失去焦点
              editor.focus()
            } else {
              console.error("Editor with ID " + id + " not found.")
            }
          },
          focusOnTinymce(position, id) {
            // 获取编辑器实例
            var tinymceEditor = tinymce.get(id)
            // 获取body容器
            var body = tinymceEditor.getBody()

            // 创建新的<p><br></p>标签
            var newParagraph = document.createElement("p")
            newParagraph.innerHTML = "<br>"
            // 插入到body容器的最后
            if (position === "top") {
              body.insertBefore(newParagraph, body.firstChild)
            } else {
              body.appendChild(newParagraph)
            }
            // 设置光标到新的<p><br></p>标签
            this.$nextTick(() => {
              tinymceEditor.selection.setCursorLocation(newParagraph, 0)
              tinymceEditor.focus()
            })
          },
          getPaymentCode() {
            let currentPayType = this.checkOnlinePay()
            paymentCode = {
              [currentPayType]: this[this.dialog].paymentCode[currentPayType]
            }
            const { pos } = this[this.dialog].payType
            if (pos.length) {
              paymentCode.pos = this[this.dialog].paymentCode["pos"]
            }
            return paymentCode
          },
          resetStepStatus() {
            this.status = {
              payMethod: "success",
              Client: "",
              GoodsName: "",
              PromptText: "",
              Email: "",
              Agreement: ""
            }
          }
        }
      })
    </script>
  </body>
</html>
