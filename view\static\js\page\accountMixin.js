const accountMixin = {
  data() {
    return {
      showResetPassword: false, //是否显示重置密码弹窗
      showRegister: false //是否显示注册弹窗
    }
  },
  methods: {
    goToRegister() {
      app.showRegister = true
      // 确保 registerPage 组件已经渲染完成
      app.$nextTick(() => {
        layer.open({
          skin: "registeredWarp", // 添加自定义样式类
          type: 1,
          title: false,
          closeBtn: 0,
          shadeClose: false,
          area: ["100%", "100%"],
          content: $("#registerPopup"),
          success: (layero, index) => {
            let registerPage = app.$root.$refs.registerPage
            // 通过 $root 访问 registerPage 组件实例
            if (registerPage) {
              registerPage.layerIndex = index
              registerPage.initFun()
              registerPage.authFormGetLabelWidth()
            }
          },
          end: () => {
            app.showRegister = false
            // let registerPage = app.$root.$refs.registerPage
            // //移除所有校验提示
            // registerPage.resetValidation()
          }
        })
      })
    },
    goToResetPassword() {
      app.showResetPassword = true
      app.$nextTick(() => {
        layer.open({
          skin: "resetPasswordWarp", // 添加自定义样式类
          type: 1,
          title: false,
          closeBtn: 0,
          shadeClose: false,
          area: ["100%", "100%"],
          content: $("#resetPasswordPopup"),
          success: (layero, index) => {
            let resetPassword = app.$root.$refs.resetPassword
            if (resetPassword) {
              resetPassword.layerIndex = index
              resetPassword.initFun()
              resetPassword.authFormGetLabelWidth()
            }
          },
          end: () => {
            app.showResetPassword = false
            // let resetPassword = app.$root.$refs.resetPassword
            // resetPassword.closeCallback()
          }
        })
      })
    }
  }
}
