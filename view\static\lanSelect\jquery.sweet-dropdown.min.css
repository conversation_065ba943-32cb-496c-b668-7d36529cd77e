.dropdown-menu {
  position: absolute;
  z-index: 3;
  display: none;
  opacity: 0;
  top: 0;
  left: 0;
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  -webkit-transform: translateY(-20px) scale(0.93);
  transform: translateY(-20px) scale(0.93);
  border-radius: 3px;
  /* background-color: #fff; */
}
.dropdown-menu.dropdown-opened {
  opacity: 1;
  -webkit-transform: none !important;
  transform: none !important;
}
.dropdown-menu.fixed {
  position: fixed;
}
.dropdown-menu.dropdown-anchor-left-top,
.dropdown-menu.dropdown-anchor-left-center,
.dropdown-menu.dropdown-anchor-left-bottom {
  -webkit-transform: translateX(-20px) scale(0.93);
  transform: translateX(-20px) scale(0.93);
}
.dropdown-menu.dropdown-anchor-right-top,
.dropdown-menu.dropdown-anchor-right-center,
.dropdown-menu.dropdown-anchor-right-bottom {
  -webkit-transform: translateX(20px) scale(0.93);
  transform: translateX(20px) scale(0.93);
}
.dropdown-menu .dropdown-anchor {
  border: 7px solid #fff;
}
.dropdown-menu .dropdown-anchor,
.dropdown-menu .dropdown-anchor::after {
  position: absolute;
  content: "";
  display: inline-block;
}
.dropdown-menu .dropdown-anchor::after {
  border: 6px solid #fff;
}
.dropdown-menu.dark .dropdown-anchor,
.dropdown-menu.dark .dropdown-anchor::after {
  border-color: #32363f;
}
.dropdown-menu.grey-anchor .dropdown-anchor,
.dropdown-menu.grey-anchor .dropdown-anchor::after {
  border-color: #f6f6f6;
}
.dropdown-menu.accent-anchor .dropdown-anchor,
.dropdown-menu.accent-anchor .dropdown-anchor::after {
  border-color: #f57c00;
}
.dropdown-menu.dropdown-anchor-top-left:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-top-center:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-top-right:not(.dropdown-overlay-trigger) {
  margin-top: 10px;
}
.dropdown-menu.dropdown-anchor-top-left .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-right .dropdown-anchor {
  border-top-color: transparent;
  border-right-color: transparent;
  border-left-color: transparent;
  top: -14px;
}
.dropdown-menu.dropdown-anchor-top-left .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-top-center .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-top-right .dropdown-anchor::after {
  border-top-color: transparent;
  border-right-color: transparent;
  border-left-color: transparent;
  margin-top: -5px;
  margin-left: -6px;
}
.dropdown-menu.dropdown-anchor-top-left.dropdown-anchor-top-left .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-center.dropdown-anchor-top-left .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-right.dropdown-anchor-top-left .dropdown-anchor {
  left: 15px;
}
.dropdown-menu.dropdown-anchor-top-left.dropdown-anchor-top-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-center.dropdown-anchor-top-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-right.dropdown-anchor-top-center .dropdown-anchor {
  left: calc(50% - 7px);
}
.dropdown-menu.dropdown-anchor-top-left.dropdown-anchor-top-right .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-center.dropdown-anchor-top-right .dropdown-anchor,
.dropdown-menu.dropdown-anchor-top-right.dropdown-anchor-top-right .dropdown-anchor {
  left: calc(100% - 28px);
}
.dropdown-menu.dropdown-anchor-right-top:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-right-center:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-right-bottom:not(.dropdown-overlay-trigger) {
  margin-left: -10px;
}
.dropdown-menu.dropdown-anchor-right-top .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-bottom .dropdown-anchor {
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  left: 100%;
}
.dropdown-menu.dropdown-anchor-right-top .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-right-center .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-right-bottom .dropdown-anchor::after {
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  margin-left: -7px;
  margin-top: -6px;
}
.dropdown-menu.dropdown-anchor-right-top.dropdown-anchor-right-top .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-center.dropdown-anchor-right-top .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-bottom.dropdown-anchor-right-top .dropdown-anchor {
  top: 11px;
}
.dropdown-menu.dropdown-anchor-right-top.dropdown-anchor-right-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-center.dropdown-anchor-right-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-bottom.dropdown-anchor-right-center .dropdown-anchor {
  top: calc(50% - 7px);
}
.dropdown-menu.dropdown-anchor-right-top.dropdown-anchor-right-bottom .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-center.dropdown-anchor-right-bottom .dropdown-anchor,
.dropdown-menu.dropdown-anchor-right-bottom.dropdown-anchor-right-bottom .dropdown-anchor {
  top: calc(100% - 26px);
}
.dropdown-menu.dropdown-anchor-bottom-left:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-bottom-center:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-bottom-right:not(.dropdown-overlay-trigger) {
  margin-top: -10px;
}
.dropdown-menu.dropdown-anchor-bottom-left .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-right .dropdown-anchor {
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  top: 100%;
}
.dropdown-menu.dropdown-anchor-bottom-left .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-bottom-center .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-bottom-right .dropdown-anchor::after {
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  margin-top: -7px;
  margin-left: -6px;
}
.dropdown-menu.dropdown-anchor-bottom-left.dropdown-anchor-bottom-left .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-center.dropdown-anchor-bottom-left .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-right.dropdown-anchor-bottom-left .dropdown-anchor {
  left: 15px;
}
.dropdown-menu.dropdown-anchor-bottom-left.dropdown-anchor-bottom-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-center.dropdown-anchor-bottom-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-right.dropdown-anchor-bottom-center .dropdown-anchor {
  left: calc(50% - 7px);
}
.dropdown-menu.dropdown-anchor-bottom-left.dropdown-anchor-bottom-right .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-center.dropdown-anchor-bottom-right .dropdown-anchor,
.dropdown-menu.dropdown-anchor-bottom-right.dropdown-anchor-bottom-right .dropdown-anchor {
  left: calc(100% - 28px);
}
.dropdown-menu.dropdown-anchor-left-top:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-left-center:not(.dropdown-overlay-trigger),
.dropdown-menu.dropdown-anchor-left-bottom:not(.dropdown-overlay-trigger) {
  margin-left: 10px;
}
.dropdown-menu.dropdown-anchor-left-top .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-bottom .dropdown-anchor {
  border-top-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  left: -14px;
}
.dropdown-menu.dropdown-anchor-left-top .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-left-center .dropdown-anchor::after,
.dropdown-menu.dropdown-anchor-left-bottom .dropdown-anchor::after {
  border-top-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  margin-left: -5px;
  margin-top: -6px;
}
.dropdown-menu.dropdown-anchor-left-top.dropdown-anchor-left-top .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-center.dropdown-anchor-left-top .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-bottom.dropdown-anchor-left-top .dropdown-anchor {
  top: 11px;
}
.dropdown-menu.dropdown-anchor-left-top.dropdown-anchor-left-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-center.dropdown-anchor-left-center .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-bottom.dropdown-anchor-left-center .dropdown-anchor {
  top: calc(50% - 7px);
}
.dropdown-menu.dropdown-anchor-left-top.dropdown-anchor-left-bottom .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-center.dropdown-anchor-left-bottom .dropdown-anchor,
.dropdown-menu.dropdown-anchor-left-bottom.dropdown-anchor-left-bottom .dropdown-anchor {
  top: calc(100% - 26px);
}
.dropdown-menu.max-height ul {
  max-height: 184px;
}
.dropdown-menu ul {
  list-style: none;
  background: #fff;
  overflow: auto;
  padding: 0px 0;
  margin: 0;
  border-radius: 3px;
}
.dropdown-menu ul li {
  list-style: none;
  padding: 0;
  margin: 0;
  line-height: 18px;
}
.dropdown-menu ul li > a,
.dropdown-menu ul li label {
  display: block;
  color: #555;
  text-decoration: none;
  line-height: 18px;
  padding: 4px 15px;
  white-space: nowrap;
  transition: all 0.1s;
}
.dropdown-menu ul li > a svg,
.dropdown-menu ul li label svg {
  height: 14px;
  width: 18px;
  vertical-align: middle;
  margin-left: -2px;
  margin-right: 4px;
  margin-top: -3px;
}
.dropdown-menu ul li > a svg path,
.dropdown-menu ul li > a svg polygon,
.dropdown-menu ul li label svg path,
.dropdown-menu ul li label svg polygon {
  transition: fill 0.1s;
  fill: #0b0b0b;
}
.dropdown-menu ul li > a span.greenSVG svg path,
.dropdown-menu ul li > a span.greenSVG svg polygon,
.dropdown-menu ul li label span.greenSVG svg path,
.dropdown-menu ul li label span.greenSVG svg polygon {
  fill: #b7d968;
}
.dropdown-menu ul li > a .flag,
.dropdown-menu ul li label .flag {
  padding-bottom: 1px;
}
.dropdown-menu ul li > a:not(.grey):hover,
.dropdown-menu ul li > a:hover,
.dropdown-menu ul li label:not(.grey):hover,
.dropdown-menu ul li label:hover {
  background-color: #f57c00;
  color: #fff;
  cursor: pointer;
}
.dropdown-menu ul li > a:not(.grey):hover svg path,
.dropdown-menu ul li > a:not(.grey):hover svg polygon,
.dropdown-menu ul li > a:hover svg path,
.dropdown-menu ul li > a:hover svg polygon,
.dropdown-menu ul li label:not(.grey):hover svg path,
.dropdown-menu ul li label:not(.grey):hover svg polygon,
.dropdown-menu ul li label:hover svg path,
.dropdown-menu ul li label:hover svg polygon {
  fill: #fff;
}
.dropdown-menu ul li > a.active,
.dropdown-menu ul li label.active {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  cursor: default;
  pointer-events: none;
  color: #999;
  background: #f6f6f6;
}
.dropdown-menu ul li > a.grey:hover,
.dropdown-menu ul li label.grey:hover {
  cursor: default;
}
.dropdown-menu ul li > a.disabled,
.dropdown-menu ul li > a.disabled:hover,
.dropdown-menu ul li > a.disabled:active,
.dropdown-menu ul li label.disabled,
.dropdown-menu ul li label.disabled:hover,
.dropdown-menu ul li label.disabled:active {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  cursor: default;
  pointer-events: none;
  cursor: default;
  color: #999;
}
.dropdown-menu ul li.title {
  padding: 7.5px 15px;
  background: #f6f6f6;
  color: #999;
  font-family: "Roboto", "Open Sans", sans-serif;
  font-size: 12px;
  text-transform: uppercase;
  border: 0;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.dropdown-menu ul li.title.grey {
  background: #999;
  color: #f57c00;
}
.dropdown-menu ul li.title.light {
  background: #f6f6f6;
  color: #fccfa2;
}
.dropdown-menu ul li.title.accent {
  background: #f57c00;
  color: #fccfa2;
}
.dropdown-menu ul li > a:hover small.grey {
  opacity: 0.75;
  color: #fff;
}
.dropdown-menu ul li:first-child.divider {
  display: none;
}
.dropdown-menu.right-aligned {
  text-align: right;
}
.dropdown-menu.right-aligned ul li a svg {
  margin-right: 0;
  margin-left: 4px;
}
.dropdown-menu .divider {
  height: 1px;
  background: #e0e0e0;
  margin: 5px 1px;
  overflow: hidden;
}
.dropdown-menu.dark.assign-dropdown ul li a {
  color: #fff;
}
.dropdown-menu.dark.assign-dropdown ul li a:hover {
  background: #f57c00;
  color: #fff;
}
.dropdown-menu.dark ul {
  background: #32363f;
}
.dropdown-menu.dark ul li > a,
.dropdown-menu.dark ul li label {
  color: #939aaa;
}
.dropdown-menu.dark ul li > a svg path,
.dropdown-menu.dark ul li > a svg polygon,
.dropdown-menu.dark ul li label svg path,
.dropdown-menu.dark ul li label svg polygon {
  fill: #778093;
}
.dropdown-menu.dark ul li > a.active,
.dropdown-menu.dark ul li label.active {
  color: #7f889a;
  background: #393d48;
}
.dropdown-menu.dark ul li > a.active.green,
.dropdown-menu.dark ul li label.active.green {
  background: #b7d968;
  color: #fff;
}
.dropdown-menu.dark ul li > a.active.green svg path,
.dropdown-menu.dark ul li > a.active.green svg polygon,
.dropdown-menu.dark ul li label.active.green svg path,
.dropdown-menu.dark ul li label.active.green svg polygon {
  fill: #fff;
}
.dropdown-menu.dark ul li > a.active.accent,
.dropdown-menu.dark ul li label.active.accent {
  background: #f57c00;
  color: #fff;
}
.dropdown-menu.dark ul li > a.active.accent svg path,
.dropdown-menu.dark ul li > a.active.accent svg polygon,
.dropdown-menu.dark ul li label.active.accent svg path,
.dropdown-menu.dark ul li label.active.accent svg polygon {
  fill: #fff;
}
.dropdown-menu.dark ul .divider {
  background: rgba(255, 255, 255, 0.08);
}
@media screen and (max-width: 420px) {
  .dropdown-menu ul li > a {
    line-height: 1.5rem;
  }
}
