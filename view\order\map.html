<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title></title>
    <meta
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <link rel="stylesheet" href="../static/css/map/map.css" />
    <!-- I18n国际化 -->
    <script src="../static/I18n/initI18n.js"></script>
    <script src="../static/vue/vue.min.js"></script>
    <script src="../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../static/js/map/anime.min.js"></script>
    <!--    <script src="../static/js/debug/vConsole.js"></script>-->
    <script src="../static/js/index/useOpenTableInterface.js"></script>
    <script src="../static/js/versionTag.js"></script>
    <!-- 选择时间控件 -->
    <script src="../static/js/map/delivery-time-picker.js"></script>
    <link rel="stylesheet" href="../static/css/map/delivery-time-picker.css" />
    <script src="../static/js/index/utils.js"></script>

    <style>
      [v-cloak] {
        display: none !important;
      }
      /*隐藏底部信息*/
      .gm-bundled-control .gmnoprint {
        display: block;
      }

      .gmnoprint:not(.gm-bundled-control) {
        display: none;
      }

      /*隐藏街景小人*/
      .gm-bundled-control {
        display: none;
      }

      /*隐藏全屏视图*/
      button.gm-fullscreen-control {
        display: none;
      }
      /*调整Google logo 位置*/
      #map div:has(> a[rel="noopener"] img[alt="Google"]) {
        bottom: 2.1rem !important;
      }
      .pc #app div:has(> a[rel="noopener"] img[alt="Google"]) {
        bottom: 0 !important;
      }
      .store-list-item:last-of-type {
        border-bottom: none;
      }

      .store-closed {
        background-color: #cccccc;
      }
      .full-store-content {
        height: 100%;
        top: 0;
      }
    </style>
  </head>

  <body>
    <div id="app" v-cloak>
      <version-tag></version-tag>
      <div id="top-tip-loading">
        <span class="left-loading">
          <div class="loading-gif">
            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24">
              <path
                fill="none"
                stroke="var(--styleColor)"
                stroke-dasharray="15"
                stroke-dashoffset="15"
                stroke-linecap="round"
                stroke-width="3"
                d="M12 3C16.9706 3 21 7.02944 21 12"
              >
                <animate fill="freeze" attributeName="stroke-dashoffset" dur="0.3s" values="15;0" />
                <animateTransform
                  attributeName="transform"
                  dur="1.5s"
                  repeatCount="indefinite"
                  type="rotate"
                  values="0 12 12;360 12 12"
                />
              </path>
            </svg>
          </div>
          <span class="error-gif"></span>
        </span>
        <span class="other-text" v-html="autoLoadingText()"></span>
      </div>
      <div id="map" ref="map" :style="{height: mapHeight}" v-if="showMap">
        <p
          class="no-store-list"
          style="height: 100%"
          v-if="MapStatus.init=='error'&&MapStatus.loaded=='error'"
        >
          <strong>{{arrLang.initServersError}}</strong>
        </p>
      </div>
      <div class="store-t" :class="{'full-store-content':!showMap}">
        <div id="features" :style="{height:featuresHeight+'px'}" ref="features">
          <div class="touch-content">
            <div class="NiJhQb-XPtOyb" v-if="showMap"></div>
            <div class="top-title">{{arrLang.branchLocation}}</div>
            <div class="search-box">
              <div class="search-t">
                <span class="search-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 512 512"
                  >
                    <path
                      fill="none"
                      stroke="var(--styleColor)"
                      stroke-miterlimit="10"
                      stroke-width="32"
                      d="M221.09 64a157.09 157.09 0 1 0 157.09 157.09A157.1 157.1 0 0 0 221.09 64Z"
                    />
                    <path
                      fill="none"
                      stroke="var(--styleColor)"
                      stroke-linecap="round"
                      stroke-miterlimit="10"
                      stroke-width="32"
                      d="M338.29 338.29L448 448"
                    />
                  </svg>
                </span>
                <input
                  type="text"
                  @click="handleSearchFocus"
                  :disabled="loading"
                  class="search-input"
                  v-model.trim="searchInputValue"
                  :placeholder="arrLang.searchTipText"
                />
                <span class="search-btn" v-if="false">
                  <img src="../static/img/map/to-search.jpg" alt="clickToSearch" />
                </span>
              </div>
            </div>
          </div>
          <div class="store-c">
            <div class="store-top-bar" :style="{visibility:!loading?'visible':'hidden'}">
              <div class="zone-bar-list-scroll-content">
                <div class="top-bar-list">
                  <div
                    class="top-bar-item zone1-bar-item"
                    @click="onZoneTabBar(0,zone1)"
                    v-for="zone1 in topZoneLevelList"
                    :key="zone1.value"
                  >
                    <span
                      :class="{'active-bar':selectedZone1Bar===zone1.value}"
                      class="bar-item-text"
                    >
                      {{zone1.text }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="zone-bar-list-scroll-content">
                <div class="zone2-bar-list top-bar-list">
                  <div
                    class="top-bar-item zone2-bar-item"
                    @click="onZoneTabBar(1,zone2)"
                    v-for="zone2 in secondZoneLevelList"
                    :key="zone2.value"
                  >
                    <span
                      :class="{'active-bar':selectedZone2Bar===zone2.value}"
                      class="bar-item-text"
                      :key="zone2.value"
                    >
                      {{zone2.text}}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="store-list-c" :style="{height:storeListHeight,flex:'unset'}">
              <div class="loading-store-list" v-if="loading">
                <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24">
                  <g fill="none" stroke="var(--styleColor)" stroke-linecap="round" stroke-width="2">
                    <path
                      stroke-dasharray="60"
                      stroke-dashoffset="60"
                      stroke-opacity=".3"
                      d="M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z"
                    >
                      <animate
                        fill="freeze"
                        attributeName="stroke-dashoffset"
                        dur="1.3s"
                        values="60;0"
                      />
                    </path>
                    <path
                      stroke-dasharray="15"
                      stroke-dashoffset="15"
                      d="M12 3C16.9706 3 21 7.02944 21 12"
                    >
                      <animate
                        fill="freeze"
                        attributeName="stroke-dashoffset"
                        dur="0.3s"
                        values="15;0"
                      />
                      <animateTransform
                        attributeName="transform"
                        dur="1.5s"
                        repeatCount="indefinite"
                        type="rotate"
                        values="0 12 12;360 12 12"
                      />
                    </path>
                  </g>
                </svg>
              </div>
              <template v-else-if="zoneStoreList.length">
                <div class="store-list-item" v-for="item in zoneStoreList" :key="item.id">
                  <div class="store-info-c">
                    <div class="s-info-l">
                      <span class="s-name">{{handleShowText(item,'storeName')}}</span>
                      <span class="s-address">{{handleShowText(item,'address')}}</span>
                    </div>
                    <div class="s-info-r">
                      <span class="s-info-distance" v-if="selectedZone1Bar<0">
                        {{item.distance}}
                      </span>
                      <span
                        class="s-info-button"
                        @click="handleSelectStore(item)"
                        :class="{'store-closed':item.openingState}"
                      >
                        <span v-if="pickupLoading&&requestData.storeNumber===item.storeNumber">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            style="vertical-align: middle"
                          >
                            <path
                              fill="none"
                              stroke="#ffffff"
                              stroke-dasharray="15"
                              stroke-dashoffset="15"
                              stroke-linecap="round"
                              stroke-width="3"
                              d="M12 3C16.9706 3 21 7.02944 21 12"
                            >
                              <animate
                                fill="freeze"
                                attributeName="stroke-dashoffset"
                                dur="0.3s"
                                values="15;0"
                              />
                              <animateTransform
                                attributeName="transform"
                                dur="1.5s"
                                repeatCount="indefinite"
                                type="rotate"
                                values="0 12 12;360 12 12"
                              />
                            </path>
                          </svg>
                        </span>
                        <span v-else>{{arrLang.selectText}}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </template>
              <div class="no-store-list" v-else>
                <h4 style="padding-top: 20px">{{arrLang.noStoresInArea}}</h4>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="infowindow-content">
        <span id="place-name" class="title"></span>
        <br />
        <br />
        <span id="place-address"></span>
      </div>
      <!--    全屏loading  -->
      <div id="full-screen-loading" v-if="topLoading"></div>
    </div>
    <script src="../static/js/index/device.js"></script>
    <script>
      history.pushState(null, null, document.URL)
      window.addEventListener("popstate", function (e) {
        history.pushState(null, null, document.URL)
      })
      window.addEventListener("pageshow", function (event) {
        //event.persisted属性为true时，表示当前文档是从往返缓存中获取
        if (event.persisted) location.reload()
      })
      window.addEventListener("DOMContentLoaded", () => {
        $.getJSON("../static/utils/config.json", function (data) {
          document.title = data.BYOD.DocumentTitle || ""
        })
      })
      // let vConsole = new VConsole()
      const RAF = {
        intervalTimer: null,
        timeoutTimer: null,
        setTimeout(cb, interval) {
          // 实现RAF.setTimeout功能
          let now = Date.now
          let stime = now()
          let etime = stime
          let loop = () => {
            this.timeoutTimer = requestAnimationFrame(loop)
            etime = now()
            if (etime - stime >= interval) {
              cb()
              cancelAnimationFrame(this.timeoutTimer)
            }
          }
          this.timeoutTimer = requestAnimationFrame(loop)
          return this.timeoutTimer
        },
        clearTimeout() {
          cancelAnimationFrame(this.timeoutTimer)
        }
      }
    </script>
    <script>
      const app = new Vue({
        el: "#app",
        data() {
          return {
            arrLang: {},
            //中国经纬度
            CHINA_BOUNDS: {
              north: 53.55,
              south: 3.86,
              west: 73.66,
              east: 135.05
            },
            checkDate: null, //选择日期
            hourStrAndMinute: null, //时间字符串
            checkTime: "", //time
            strictBounds: null, //搜索的限制区域
            requestData: {}, //用于请求openTable的数据
            lan: "", //当前语言
            infoWindow: null,
            map: null,
            myAddress: null, //我的位置坐标
            domain: sessionStorage.getItem("domain"),
            loading: false, //获取数据时的loading(获取距离/加载map)
            topLoading: false, //全屏loading(顶部loading)
            requestStoreLoading: false, // 请求店铺openTable接口时的loading
            openTableLoaded: "init", //获取openTable接口的状态(顶部loading关闭延时)
            topLoadingTipText: "",
            animation: null, //顶部loading动画是否完成
            MapStatus: {
              init: "null", //地图初始化状态,,判断是否能请求到google Map服务
              loadPos: "null" //判断是否授权位置服务
            }, //地图加载状态
            rangingCompleted: false, // 测距是否完成
            scope: 0, //范围附近5km
            markers: [], //地图标记数组
            timer: null, //动画定时器
            storeList: [], //全部店铺列表
            currentPosStoreList: [], //当前位置店铺列表(需要渲染的列表)
            mode: [
              {
                icon: "self-extract",
                value: "自取",
                bc: "#f97898"
              },
              {
                icon: "takeaway-car",
                value: "外卖配送",
                bc: "#f4f2f5"
              }
            ], //配送模式
            featuresState: false, //滑动容器当前的状态|true: 展开 false:收回
            windowHeight: window.innerHeight, //窗口高度
            upKeyboard: false, //键盘是否升起
            isIos: false, //是否是ios
            openTable: {}, // 开台数据
            // new
            mapHeight: "33.33%", //地图高度
            featuresHeight: document.body.offsetHeight, //滑动容器默认高度
            featuresDefaultOffset: 0, //滑动容器默认偏移位置px
            featuresUpOffset: 0, //滑动容器升起时偏移位置px
            featuresMaxUpOffset: (window.innerHeight / 3) * -1, //滑动容器最大升起时偏移位置px
            initTimePickerState: false, //是否初始化时间选择器
            fillLoadByStoreClosed: false, //店铺休业后点击的提示状态
            errorOTRequestData: "", //开台接口错误返回的请求数据
            pickupLoading: false, //请求店铺的pickup的loading
            selectedZone1Bar: -1,
            selectedZone2Bar: -1,
            selectedZone3Bar: -1,
            selectedZoneLevel: 0,
            zoneAndStoreNumberMap: [],
            searchInputValue: "" // 搜索值
          }
        },
        created() {
          let openTable = sessionStorage.getItem("openTable")
          if (!openTable) {
            const indexURL = localStorage.getItem("indexPageUrl")
            window.location.replace(indexURL)
            return false
          }
          this.openTable = JSON.parse(openTable)
          const { language, shopMapSearchScope = { scope: 3 }, color = "#108b96" } = this.openTable
          document.documentElement.style.setProperty("--styleColor", color)
          this.scope = +shopMapSearchScope.scope * 1000 || 3000
          this.lan = language
          this.fixLan(language)
          if (this.showMap) {
            this.renderMap(language)
          } else {
            this.getData(false)
          }
        },
        computed: {
          showMap() {
            return !(this.openTable.mapConfig || {}).disableMap
          },
          // 地图服务是否初始化成功 && 能获取到我的位置
          mapInitStatus() {
            return this.MapStatus.init === "success" && this.MapStatus.loadPos === "success"
          },
          storeListHeight() {
            let appHeight = document.body.offsetHeight
            if (Array.from(document.body.classList).includes("pc")) {
              // 740为pc端默认高度,暂无方法直接拿到:root的--pcHeight
              appHeight = 740
            }
            // 165为店铺列表以上高度
            return this.featuresState ? "100%" : (appHeight / 3) * 2 - 200 + "px"
          },
          // zone1 的tab bar 列表
          topZoneLevelList() {
            const defaultCheck = {
              text: this.mapInitStatus ? this.arrLang.nearbyText : this.arrLang.allText,
              value: -1
            }
            let { language } = this.openTable
            let zoneName = this.zoneAndStoreNumberMap.map(e => {
              return { text: e.name[language], value: e.id }
            })
            // 地图初始化完成&&测距完成&&测距无数据
            if (!this.nearbyStoreList.length && this.mapInitStatus && this.rangingCompleted) {
              // 若存在zone列表,则仅显示zone栏目<不显示附近>,默认选中第一个zone的id
              if (this.zoneAndStoreNumberMap.length) {
                this.$nextTick(() => {
                  let id = this.zoneAndStoreNumberMap[0].id
                  this.onZoneTabBar(0, { value: id })
                })
                return [...zoneName]
              } else {
                // 反之显示 "全部" 栏目,显示所有的店铺数据
                this.$nextTick(() => {
                  this.MapStatus.loadPos = "error"
                })
                return [{ text: this.arrLang.allText, value: -1 }]
              }
            }
            return [defaultCheck, ...zoneName]
          },
          // zone2 的tab bar 列表
          secondZoneLevelList() {
            const defaultCheck = { text: this.arrLang.allText, value: -1 }
            let { language } = this.openTable
            let zone1Data = this.zoneAndStoreNumberMap.find(z => z.id === this.selectedZone1Bar)
            if (zone1Data) {
              let zoneName = zone1Data.children.map(e => {
                return { text: e.name[language], value: e.id }
              })
              if (zoneName.length) {
                return [defaultCheck, ...zoneName]
              }
            }
            return []
          },
          // zone3 的tab bar 列表
          lastZoneLevelList() {
            const defaultCheck = { text: this.arrLang.allText, value: -1 }
            let { language } = this.openTable
          },
          // 附近栏目/全部栏目应该显示的店铺数据列表
          nearbyStoreList() {
            if (this.mapInitStatus) {
              return this.currentPosStoreList.filter(e => e.distance) || []
            } else {
              return this.currentPosStoreList || []
            }
          },
          // 根据zone区分店铺列表
          zoneStoreList() {
            let list = []
            switch (this.selectedZoneLevel) {
              case 0:
                list =
                  this.selectedZone1Bar < 0
                    ? this.nearbyStoreList
                    : this.getStoreListBySN([this.selectedZone1Bar])
                break
              case 1:
                list =
                  this.selectedZone2Bar < 0
                    ? this.getStoreListBySN([this.selectedZone1Bar])
                    : this.getStoreListBySN([this.selectedZone1Bar, this.selectedZone2Bar])
                break
              case 2:
                list =
                  this.selectedZone3Bar < 0
                    ? this.getStoreListBySN([this.selectedZone1Bar, this.selectedZone2Bar])
                    : this.getStoreListBySN([
                        this.selectedZone1Bar,
                        this.selectedZone2Bar,
                        this.selectedZone3Bar
                      ])
                break
              default:
                return []
            }
            // 每次选择zone栏时,重设店铺列表高度
            this.$nextTick(this.resetStoreContentHeight)
            // 处理搜索关键字筛选
            return list.filter(item => {
              return (
                (item.address &&
                  item.address[this.lan] &&
                  item.address[this.lan].indexOf(this.searchInputValue) > -1) ||
                (item.storeName &&
                  item.storeName[this.lan] &&
                  item.storeName[this.lan].indexOf(this.searchInputValue) > -1)
              )
            })
          }
        },
        components: {
          VersionTag
        },
        mounted() {
          this.loading = true
          if (this.showMap) {
            this.listenFeatures()
            this.listenKeyboardUp()
          }
          //禁用安卓浏览器下拉刷新,已解决导致抽屉下拉异常卡顿
          //若滑动不是店铺列表下的子店铺,则禁止默认事件
          document.addEventListener(
            "touchmove",
            e => {
              // (!picker.contains(e.target)) && e.preventDefault()
            },
            { passive: false }
          )
          const { language, pickupTime = {} } = this.openTable
          if (pickupTime.pickupDayRange && pickupTime.pickupTimeInterval) {
            this.initTimePicker(language, pickupTime)
          }
          document.documentElement.style.fontSize = "unset"
        },
        watch: {
          upKeyboard(val) {
            if (!val) {
              this.resetAnime(false)
              if (!this.featuresState) {
                document.querySelector(".store-list-c").style.flex = "unset"
              }
            } else {
              document.querySelector(".store-list-c").style.flex = "1"
            }
          },
          featuresState(val) {
            if (!this.upKeyboard) {
              document.querySelector(".store-list-c").style.flex = val ? "1" : "unset"
            }
          }
        },
        methods: {
          // 根据当前选中的zone,找到其下的所有店铺号
          getStoreNumberByZone([z1id, z2id = -1, z3id = -1]) {
            let z1Data,
              z2Data,
              z3Data,
              idList = []
            const getId = data => {
              let { children = [] } = data
              if (children.length) {
                return children.reduce((pre, cur) => {
                  pre.push(...cur.storeNumberList)
                  if (cur.children && cur.children.length) {
                    pre.push(...getId(cur))
                  }
                  return pre
                }, [])
              }
              return []
            }
            z1Data = this.zoneAndStoreNumberMap.find(z => z.id === z1id)
            if (!z1Data) return []
            if (z2id < 0) {
              // 返回z1下的所有sn
              idList.push(...z1Data.storeNumberList, ...getId(z1Data))
              return [...new Set(idList)]
            } else {
              z2Data = z1Data.children.find(z => z.id === z2id)
              if (!z2Data) return []
              if (z3id < 0) {
                // 返回z2下的所有sn
                idList.push(...z2Data.storeNumberList, ...getId(z2Data))
                return [...new Set(idList)]
              } else {
                // 返回z3下的所有sn
                z3Data = z2Data.children.find(z => z.id === z3id)
                if (!z3Data) return []
                idList.push(...z3Data.storeNumberList, ...getId(z3Data))
                return [...new Set(idList)]
              }
            }
          },
          // 根据storeNumber_list找到所有店铺数据
          getStoreListBySN(val) {
            let storeNumberList = this.getStoreNumberByZone(val)
            return this.storeList.filter(s => storeNumberList.includes(s.storeNumber))
          },
          // 点击地区的name ,切换地区查看店铺
          onZoneTabBar(level, item) {
            this.selectedZoneLevel = level
            switch (level) {
              case 0:
                this.selectedZone1Bar = item.value
                // 初始化其余栏目
                this.selectedZone2Bar = -1
                this.selectedZone3Bar = -1
                break
              case 1:
                this.selectedZone2Bar = item.value
                this.selectedZone3Bar = -1
                break
              default:
                this.selectedZone3Bar = item.value
                break
            }
            // this.$nextTick(this.resetStoreContentHeight)
          },
          // 在zone列表切换显示时,重新设置店铺列表的最大高度
          resetStoreContentHeight() {
            const PMH = 20 // margin+padding+border...
            let height = ~~(
              $(".store-t").height() -
              $(".touch-content").height() -
              $(".store-top-bar").height() -
              PMH
            )
            $(".store-list-c").height(height)
            return height
          },
          /**
           * @description 控制顶部信息条的显示隐藏动画.
           * @summary 该函数不维护各种状态的开启,但是会在finished时将状态置为false; 状态:`fillLoadByStoreClosed,topLoading`
           * @param {boolean} autoClose 是否自动关闭,默认true
           * @param {number} duration 显示时长,默认3000ms
           * */
          snackbar(autoClose = true, duration = 3000) {
            if (this.animation) {
              RAF.clearTimeout()
              this.animation.restart()
            }
            this.animation = anime({
              targets: "#top-tip-loading",
              top: "0px",
              opacity: 1,
              easing: "linear",
              autoplay: false,
              duration: 300
            })
            this.animation.play()
            if (autoClose) {
              RAF.setTimeout(() => {
                this.closeSnackbar()
              }, duration)
            }
          },
          // 关闭顶部信息条动画
          closeSnackbar() {
            this.animation = anime({
              targets: "#top-tip-loading",
              top: "-100px",
              opacity: 0,
              easing: "linear",
              autoplay: false,
              duration: 300
            })
            this.animation.play()
            this.animation.finished.then(() => {
              this.animation = null
              this.topLoading = false
              this.fillLoadByStoreClosed = false
            })
          },

          initTimePicker(language = "en") {
            let { takeAwayDiffTimeZoneTip } = this.arrLang
            // 在页面加载时初始化控件，参数1是控件选项，参数2是一个dom的id字符串且可以忽略
            DeliveryTimePicker.init({
              lan: language,
              showDate: false, // 显示‘今天/明天/后天’还是全部显示日期
              showWeekday: false, // 是否显示周几
              clickOutside: true, // 点击选择器外部关闭，默认true开启
              checkTimeObj: {
                date: this.checkDate,
                hourStrAndMinute: this.hourStrAndMinute
              },
              warnText: takeAwayDiffTimeZoneTip, // 时区不同的提示语
              // 选择时间后的回调函数
              callback: (date, hourStrAndMinute, time, startTimeSecond) => {
                this.checkDate = date
                this.checkTime = time
                this.hourStrAndMinute = hourStrAndMinute
                this.requestData = {
                  ...this.requestData,
                  pickupTime: time,
                  hourStrAndMinute,
                  checkDate: date,
                  startTimeSecond
                }
                console.log(date, hourStrAndMinute, time)
                this.getOpenTableData(this.requestData)
              }
            })
            this.initTimePickerState = true
          },
          // 固定语言
          fixLan(lan = "zh") {
            let data = window.i18n[lan]
            for (let key in data) {
              this.$set(this.arrLang, key, data[key])
            }
            console.log(this.arrLang)
          },
          //处理storeName,address的多语言文字显示
          handleShowText(r, i) {
            switch (this.lan) {
              case "en":
                return r[i].en
              case "zh":
                return r[i].zh
              default:
                return r[i].thirdLan || r[i].third
            }
          },
          //顶部loading的文字,图标切换逻辑
          autoLoadingText() {
            const textDOM = document.querySelector(".other-text")
            const loadingDOM = document.querySelector(".loading-gif")
            const errorDOM = document.querySelector(".error-gif")
            // 点击按钮灰色,店铺非营业时间
            if (this.fillLoadByStoreClosed) {
              let { takeawayTimeoutPrompt } = this.arrLang
              loadingDOM.style.display = "none"
              errorDOM.style.display = "block"
              textDOM.style.color = "#dc3540"
              return takeawayTimeoutPrompt
            }
            // 请求openTable时默认文字
            if (this.topLoading && this.requestStoreLoading) {
              loadingDOM.style.display = "block"
              errorDOM.style.display = "none"
              textDOM.style.color = "var(--styleColor)"
              return this.arrLang.requestOpenTableTipText
            }
            // 默认渲染topLoadingTipText
            loadingDOM.style.display = "none"
            errorDOM.style.display = "block"
            textDOM.style.color = "#dc3540"
            return this.topLoadingTipText
          },
          //点击选择按钮
          handleSelectStore(store) {
            if (this.topLoading) return
            this.fillLoadByStoreClosed = false
            let sessionOpenTable = sessionStorage.getItem("openTable")
            //地图上的店铺点击事件
            if (typeof store === "string") {
              let d = this.storeList.find(item => item.storeNumber === store)
              if (d) {
                store = d
              } else {
                store = {
                  storeNumber: store
                }
              }
            }
            // 店铺休业
            if (store.openingState === "closed") {
              this.fillLoadByStoreClosed = true
              this.snackbar()
              return
            }

            console.log(`%c 点击的store`, "color:#dc137b", store)
            const { domain, storeNumber, openingDates, openingHours, locationCoordinates } = store
            let urlData = sessionStorage.getItem("data")
            this.requestData = {
              companyName: domain,
              storeNumber,
              tableNumber: "TAKEAWAY",
              urlData: urlData || "",
              storeAddress: store.address,
              storeName: store.storeName,
              openingDates: openingDates,
              openingHours: openingHours,
              performType: sessionOpenTable && JSON.parse(sessionOpenTable).performType,
              locationCoordinates
            }
            if (this.initTimePickerState) {
              this.getStorePickupTime(storeNumber)
                .then(r => {
                  if (store.redirectUrl) {
                    // window.open(store.redirectUrl, "_blank")
                    window.location.replace(store.redirectUrl)
                    return false
                  }
                  let { dateTimeMap, timeZone, startTimeSecond } = r
                  // 获取当前时区,与timeZoneMap对比,若时区不同则提示
                  let formatTimeZone = timeZone.replaceAll("-", "/").split("[")[0]
                  DeliveryTimePicker.show(dateTimeMap, startTimeSecond, formatTimeZone)
                  DeliveryTimePicker.toggleDisplayWarn(!this.isCurrentTimeZone(timeZone))
                })
                .catch(err => {
                  this.snackbar()
                })
            } else {
              if (store.redirectUrl) {
                window.location.replace(store.redirectUrl)
                return false
              }
              this.getOpenTableData(this.requestData)
            }
          },
          /**
           * @description 跟传入的时区对比,是否为当前时区
           * @param {`${number}-${number}-${number} ${number}:${number} [+${number}:${number}`} storeTimeZone 店铺的日期时间字符串[时区
           * @returns {boolean} 是否为当前时区
           * */
          isCurrentTimeZone(storeTimeZone) {
            if (!storeTimeZone) return true
            let [dateTime, timeZone] = storeTimeZone.split("[")
            let [hour, min] = timeZone.split(":")
            return new Date().getTimezoneOffset() === (+hour * 60 + +min) * -1
          },

          //获取openTable数据
          getOpenTableData(params) {
            this.topLoading = true
            this.requestStoreLoading = true
            this.openTableLoaded = "init"
            this.snackbar(false)
            let {
              companyName,
              storeNumber,
              urlData,
              performType,
              pickupTime,
              storeName,
              storeAddress,
              openingDates,
              openingHours,
              locationCoordinates,
              hourStrAndMinute,
              checkDate,
              startTimeSecond
            } = params

            let requestData = {
              companyName: companyName,
              storeNumber: storeNumber,
              tableNumber: "TAKEAWAY",
              urlData: urlData || "",
              performType: performType
            }
            // 不是尽快,则加入requestData,拼接上seconds,前端页面不显示秒,但是请求要带上
            if (notInstantPickup(pickupTime)) {
              requestData.pickupTime = formatPickupTime(pickupTime, startTimeSecond)
            }

            let storeData = {
              storeNumber: storeNumber || "",
              storeName: storeName || {},
              storeAddress: storeAddress || {},
              openingDates: openingDates || [],
              openingHours: openingHours || [],
              locationCoordinates: locationCoordinates || "",
              startTimeSecond
            }
            if (this.initTimePickerState) {
              storeData.pickupTime = pickupTime
              storeData.hourStrAndMinute = hourStrAndMinute
              storeData.checkDate = checkDate
            }
            sendOpenTableRequest(requestData, "map")
              .then(result => {
                if (!result) {
                  this.opTableRequestTip(401)
                  return
                }
                const res = JSON.parse(result)
                // const res=result
                const code = res.statusCode
                if (code && code !== 200) {
                  this.errorOTRequestData = res
                  this.opTableRequestTip(code)
                  return
                }
                if (result && !result.errorCode) {
                  this.openTableLoaded = "success"
                  const uiconfig = dynamicConfig(res.uiConfigList)
                  const oldOpenTable = sessionStorage.getItem("openTable")
                  if (oldOpenTable) {
                    const oldData = JSON.parse(oldOpenTable)
                    const { urlPrefix, language, initialTableNum } = oldData
                    const newOpenTable = {
                      urlPrefix: urlPrefix || "",
                      language: language || "zh",
                      companyName: this.domain,
                      initialTableNum: initialTableNum || "TAKEAWAY",
                      ...requestData,
                      ...res,
                      ...uiconfig,
                      storeData
                    }
                    delete newOpenTable.uiConfigList
                    console.log(newOpenTable, "openTable数据")
                    sessionStorage.removeItem("openTable")
                    sessionStorage.setItem("openTable", JSON.stringify(newOpenTable))
                    window.location.href = "./menuPage"
                  }
                } else if (result.errorCode) {
                  this.opTableRequestTip(401) //提示层
                }
              })
              .catch(error => {
                // this.topLoading=false
                console.log(error)
                this.opTableRequestTip(401)
              })
          },
          opTableRequestTip(code) {
            this.requestStoreLoading = false
            this.topLoadingTipText = this.getRequestMessage(code)
            this.snackbar()
          },
          /**
           * @description 根据错误状态码获取错误提示
           * @param {number} code 错误状态码
           * @returns {string} 错误提示
           * */
          getRequestMessage(code) {
            let {
              staffModelErrorTip,
              errorDesc,
              errorTxt,
              takeawayTimeoutPrompt,
              takeawayClosePrompt
            } = this.arrLang
            let { errorDesc: errorRes = {}, data: errorMsg } = this.errorOTRequestData
            switch (code) {
              case 400:
                return errorTxt
              case 404:
                return errorTxt
              case 406:
                return staffModelErrorTip
              case 4006: //外卖/堂食;店铺超时
                return errorRes[this.lan] || errorMsg || takeawayTimeoutPrompt
              case 4011: //外卖/堂食;店铺关闭
                return takeawayClosePrompt
              case 6005:
                return errorDesc
              default:
                return errorTxt
            }
          },
          //获取数据 /storeNumber/getAllByDomain 参数domain
          //params mapLoad=google地图是否加载成功,search=是否展示搜索
          // type 是否成功初始化google地图服务
          async getData(type) {
            const domain = sessionStorage.getItem("domain")
            await $.ajax({
              url: "../storeNumber/getAllByDomain",
              type: "get",
              xhrFields: {
                responseType: "json"
              },
              data: { domain },
              success: res => {
                if (res.statusCode === 200) {
                  let { storeNumberList, zoneAndStoreNumberMap } = res.data
                  this.storeList = this.verifyDataIntegrity(storeNumberList)
                  this.currentPosStoreList = this.storeList
                  this.zoneAndStoreNumberMap = zoneAndStoreNumberMap
                  console.log(zoneAndStoreNumberMap)
                  //渲染锚点
                  if (type) {
                    // map loaded success
                    this.renderPosToMap(this.storeList)
                  } else {
                    // map loaded fail, cancel loading,render store list
                    this.loading = false
                  }
                } else {
                  this.loading = false
                }
              },
              error: err => {
                this.loading = false
              }
            })
          },
          /**
           * @description 根据storeNumber获取该店铺的预约时间段列表
           * @param {string} storeNumber
           * @returns {Promise<{dataTimeMap:object,timeZoneMap:string}>}
           * */
          getStorePickupTime(storeNumber) {
            let data = {
              storeNumber,
              domain: this.domain,
              language: this.lan
            }
            return new Promise((resolve, reject) => {
              $.ajax({
                url: "../storeNumber/getDateTimeMapByStoreNumber",
                type: "get",
                xhrFields: {
                  responseType: "json"
                },
                data,
                beforeSend: () => {
                  this.pickupLoading = true
                },
                success: res => {
                  if (res.statusCode === 200) {
                    resolve(res.data || {})
                  } else {
                    this.errorOTRequestData = res
                    this.openTableLoaded = "init"
                    this.topLoadingTipText = this.getRequestMessage(res.statusCode)
                    reject(`statusCode: ${res.statusCode}`)
                  }
                },
                error: err => {
                  reject(this.arrLang.errorTxt)
                },
                complete: () => {
                  this.pickupLoading = false
                }
              })
            })
          },
          //在初始化地图后将拿到的数据渲染到地图上
          renderPosToMap(list) {
            const image = {
              url: "../static/img/map/my-location.jpg",
              size: new google.maps.Size(25, 43),
              origin: new google.maps.Point(0, 0),
              scaledSize: new google.maps.Size(25, 43)
            }
            list.forEach(i => {
              const p = i.locationCoordinates && i.locationCoordinates.split(",")
              const pos = {
                lat: Number(p[0]),
                lng: Number(p[1])
              }
              //文字框
              let contentString =
                "<div id='content' style='min-width: 100px'>" +
                "<p style='margin: 0'>" +
                "Store Name :" +
                " <strong>" +
                (this.handleShowText(i, "storeName") || "") +
                "</strong>" +
                "</p>" +
                "<p style='margin:8px 0 ;text-align: center'>" +
                "<span class='on-marker-select-button'" +
                ` onclick=app.handleSelectStore('${i.storeNumber}')>` +
                this.arrLang.selectText +
                "</span></p></div>"
              if (i.openingState) {
                let newStr = contentString.replace(
                  "on-marker-select-button",
                  "on-marker-select-button store-closed"
                )
                contentString = newStr
              }
              const info = new google.maps.InfoWindow({
                content: contentString
              })
              this.addMarker(this.map, pos, { icon: image, title: "store Number" }, info)
            })
          },
          /**
           * @description 挂载GoogleMap,渲染地图
           * @param {string} lan 当前语言
           *
           * 此前是直接$.getScript获取GoogleMap资源,会导致主线程阻塞,页面卡顿(loading dom存在,但是不显示)
           * 修改为使用web worker,用于异步加载google map,防止阻塞主线程
           * */
          renderMap(lan = "zh") {
            let Lan = lan === "zh" ? "zh-HK" : "en-US"
            const key = "AIzaSyDAjO_Ik2VVK2eypmKEKPBEx4KGOslYVj0"
            window.initMap = this.initMap
            const worker = new Worker("../static/js/map/map.worker.js")
            const { mapConfig = {} } = this.openTable
            const timeout = +mapConfig.timeout * 1000 || null
            worker.postMessage({
              key,
              lan: Lan,
              timeout
            })
            worker.onmessage = e => {
              let { code, res } = e.data
              if (code === "ok") {
                let script = document.createElement("script")
                script.textContent = res
                document.body.appendChild(script)
                this.MapStatus.init = "success"
              } else {
                this.getData(false)
                this.MapStatus = {
                  init: "error",
                  loaded: "error"
                }
              }
            }
          },
          //初始化map
          initMap() {
            this.map = new google.maps.Map(document.getElementById("map"), {
              center: { lat: 0, lng: 0 },
              // restriction: {
              //     latLngBounds: this.CHINA_BOUNDS,
              //     strictBounds: true,
              // },
              gestureHandling: "greedy",
              mapTypeId: google.maps.MapTypeId.ROADMAP,
              zoom: 12
            })
            this.infoWindow = new google.maps.InfoWindow()
            //获取当前位置
            if (navigator.geolocation) {
              navigator.geolocation.getCurrentPosition(
                async position => {
                  const pos = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                  }
                  this.myAddress = pos
                  //测距离
                  this.MapStatus = {
                    init: "success",
                    loadPos: "success"
                  }

                  //添加当前位置的标记
                  this.addMarker(this.map, pos)

                  //设置中心点
                  this.map.setCenter(pos)
                  //若数据已经获取完成,则根据当前位置渲染附近的店铺到地图上
                  await this.getData(true)
                  //获取google地图的地址
                  this.getMapDistance()
                  // 地图初始化完成,店铺排序完成后,放开loading,让其渲染
                  // this.loading = false
                },
                //获取位置失败回调
                () => {
                  this.handleLocationError(true, this.infoWindow, this.map.getCenter())
                },
                {
                  enableHighAccuracy: true,
                  timeout: 5000,
                  maximumAge: 0
                }
              )
            } else {
              // Browser doesn't support Geolocation
              this.handleLocationError(false, this.infoWindow, this.map.getCenter())
            }
          },
          //添加锚点
          addMarker(map, location, other, info) {
            // Add the marker at the clicked location, and add the next-available label
            // from the array of alphabetical characters.
            const marker = new google.maps.Marker({
              position: location,
              map,
              ...other
            })
            this.markers.push(marker)
            if (info) {
              marker.addListener("click", () => {
                info.open({
                  anchor: marker,
                  map,
                  shouldFocus: false
                })
              })
            }
          },
          //处理错误
          handleLocationError(browserHasGeolocation, infoWindow, pos) {
            this.getData(false)

            this.MapStatus = {
              init: "success",
              loadPos: "error"
            }
            console.log(
              browserHasGeolocation
                ? `${this.arrLang.LocationServiceUnAuthorizedInSetting}.`
                : `${this.arrLang.LocationServiceUnAuthorizedInBrowser}.`
            )
            infoWindow.setPosition(pos)
            if (browserHasGeolocation) {
              infoWindow.setContent(`${this.arrLang.LocationServiceUnAuthorizedInSetting}.`)
            } else {
              infoWindow.setContent(`${this.arrLang.LocationServiceUnAuthorizedInBrowser}.`)
            }

            infoWindow.open(this.map)
          },
          //获取距离....
          getMapDistance(origin) {
            //测量距离 测试锚点
            const targetList = []
            let targetStoreList = []
            this.storeList.forEach(item => {
              if (item.locationCoordinates && item.locationCoordinates.includes(",")) {
                const latLng = item.locationCoordinates.split(",")
                targetStoreList.push(item)
                targetList.push({
                  location: { lat: Number(latLng[0]), lng: Number(latLng[1]) },
                  sn: item.storeNumber
                  // query: item.address.en || item.address.zh
                })
              }
            })
            const myAddress = origin || this.myAddress
            if (!targetList.length || !Object.entries(myAddress).length) {
              this.loading = false
              return false
            }
            const chunkArray = (array, chunkSize) => {
              const result = []
              for (let i = 0; i < array.length; i += chunkSize) {
                result.push(array.slice(i, i + chunkSize))
              }
              return result
            }
            let promiseList = chunkArray(targetList, 25).map(el => this.measureDistance(el))
            Promise.all(promiseList)
              .then(res => {
                this.loading = false
                this.rangingCompleted = true
                const result = res.flat(Infinity)
                result.forEach(item => {
                  //测量状态为OK,并且距离小于限制范围时，才显示
                  if (item.distance !== null && ~~item.distance < this.scope) {
                    let store = this.storeList.find(s => s.storeNumber === item.storeNumber)
                    if (store) {
                      store.distance =
                        item.distance < 1000
                          ? ~~item.distance + "m"
                          : (~~item.distance / 1000).toFixed(1) + "km"
                      store.distancesNoUnit = item.distance
                    }
                  }
                })
                // 根据距离排序
                this.currentPosStoreList = this.sortStoreList(this.storeList)
              })
              .catch(e => {
                this.loading = false
              })
          },
          /**
           * @description 测量店铺位置到我当前位置的距离
           * @param {Array} storeList  店铺列表,最大长度25
           * @returns {Promise} 测距结果
           * */
          measureDistance(storeList) {
            let { lat, lng } = this.myAddress
            let p1 = new google.maps.LatLng(lat, lng)
            return google.maps.importLibrary("geometry").then(r => {
              let distanceList = storeList.reduce((pre, cur) => {
                let {
                  location: { lat, lng },
                  sn: storeNumber
                } = cur
                let p2 = new google.maps.LatLng(lat, lng)
                let distance = r.spherical.computeDistanceBetween(p1, p2)
                pre.push({ distance, storeNumber })
                return pre
              }, [])
              return distanceList
            })
          },
          sortStoreList(list) {
            // 根据
            return list.sort((a, b) => {
              return (a.distancesNoUnit || 0) - (b.distancesNoUnit || 0)
            })
          },
          //验证数据完整性(验证获取到的数据是否完整)(坐标是否存在),(storeNumber是否存在)
          verifyDataIntegrity(list = []) {
            const data = []
            list.forEach(item => {
              if (item.storeNumber) {
                let { openingDates, openingHours, takeawayUsable } = item
                // 未营业及关闭外卖都禁用按钮
                if (
                  !verificationTimePeriod(openingDates, openingHours, !this.initTimePickerState) ||
                  !takeawayUsable
                ) {
                  item.openingState = "closed"
                }
                data.push(item)
              }
            })
            return data
          },
          //搜索框聚焦
          handleSearchFocus() {
            let map = this.$refs.map
            // ios触发弹出键盘
            if (this.isIos) {
              if (this.featuresState) {
                // 2.在店铺列表已上滑时点击搜索按钮触发,直接弹出键盘,不做其他处理
              } else {
                // 1.在店铺列表初始化时点击input触发,在键盘还未弹出时将features滚动至最顶部,以使键盘弹出后覆盖内容区域
                this.iosKeyboardUpBySlide()
              }
            } else {
              //android触发弹出键盘
              console.log(this.featuresState, "状态")
            }
          },
          //输入键盘弹出,移动功能区域位置
          listenKeyboardUp() {
            //判断是苹果还是安卓
            this.isIos = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
            if (this.isIos) {
              //因为ios特性,键盘弹出动画是同步的
              //苹果
              window.addEventListener("focusin", e => {
                // console.log('ios检测到聚焦')
                if (document.activeElement.className === "search-input") {
                  RAF.setTimeout(() => {
                    if (this.featuresState) {
                      //升起状态弹出键盘:
                      //ios软键盘弹出
                      this.upKeyboard = true
                      document.activeElement.scrollIntoView(true)
                      console.log(
                        "ios软键盘已经弹出: ",
                        this.windowHeight,
                        "(初始化高度)",
                        window.innerHeight,
                        "(弹出后现在高度)"
                      )
                    } else {
                      // if (this.windowHeight>window.innerHeight){
                      //ios软键盘弹出
                      this.upKeyboard = true
                      document.activeElement.scrollIntoView(true)
                      console.log(
                        "ios软键盘已经弹出: ",
                        this.windowHeight,
                        "(初始化高度)",
                        window.innerHeight,
                        "(弹出后现在高度)"
                      )
                      // }
                    }
                  }, 100)
                }
              })
              window.addEventListener("focusout", e => {
                // console.log('ios检测到失焦')
                //ios软键盘收起
                if (e.target._prevClass === "search-input") {
                  RAF.setTimeout(() => {
                    if (this.windowHeight <= window.innerHeight && this.upKeyboard) {
                      this.upKeyboard = false
                      console.log("ios软键盘已经收起:", window.innerHeight, "(收回高度)")
                      // document.querySelector('.store-t').style.top='50%'
                    }
                  }, 100)
                }
              })
            } else {
              //安卓
              window.addEventListener("resize", e => {
                // Document 对象的activeElement 属性返回文档中当前获得焦点的元素。
                // if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
                this.upKeyboard = this.windowHeight > e.target.innerHeight
                // document.activeElement.scrollIntoView(true)
                this.resetStoreContent(this.upKeyboard, this.featuresState)
                // }
                // this.upKeyboard ? console.log('安卓软键盘已经弹出') : console.log('安卓软键盘已经收起')
              })
            }
          },

          //侦听features上拉下拉事件
          listenFeatures() {
            let features = this.$refs.features
            let box = document.querySelector(".touch-content")
            let input = document.querySelector(".search-input")
            let map = this.$refs.map
            let startTime = "" // 触摸开始时间
            let startDistancePageY = "" //触摸开始时的pageY
            let endDistancePageY = "" //触摸结束时的pageY
            let endTime = "" // 触摸结束时间
            let moveTime = "" // 触摸时间
            let moveDistanceY = "" // 触摸移动Y轴距离
            let moveCurrentY = "" // 上一帧的Y轴位置
            let timeStamp = "" //时间戳
            let elapsed = "" //滑动时间
            let translateOffset = "" //滑动偏移值
            let lock = false //锁

            box.addEventListener("touchstart", e => {
              // console.dir(e.target)
              if (e.target._prevClass === "search-input") {
                return
              }
              // if (this.featuresState && this.upKeyboard) {
              //   console.log("限制键盘弹出时的滑动")
              //   return
              // }
              lock = true
              startTime = new Date().getTime()
              startDistancePageY = e.touches[0].pageY
              moveCurrentY = ""
              //重置偏移值
              const offsetStr = this.$refs.features.style.transform
              // let reg=/(?<=(translateY\()).*?(?=(px))/
              //正则匹配括号中的内容,不使用断言
              let reg = new RegExp(/translateY\((.*?)px\)/)

              if (offsetStr) {
                let r = Number(reg.exec(offsetStr)[1])
                if (!isNaN(r)) {
                  translateOffset = r
                  console.log("%c 初始化偏移:", "color:#ff0000", r)
                }
              }
              console.log(startDistancePageY, "开始坐标")
            })
            //加入了节流和动画帧来减少开支
            box.addEventListener("touchmove", e => {
              e.preventDefault()
              //input聚焦时不执行
              if (e.target._prevClass === "search-input") {
                return
              }
              if (this.featuresState && this.upKeyboard) {
                console.log("限制键盘弹出时的滑动")
                return
              }
              //若键盘已经弹出,并且向上滑动,则不执行
              if (this.upKeyboard && e.touches[0].pageY < startDistancePageY) {
                return
              }
              lock = true
              window.requestAnimationFrame(() => {
                if (!timeStamp) {
                  timeStamp = new Date().getTime()
                }
                //滑动时间:elapsed
                elapsed = new Date().getTime() - timeStamp
                e.preventDefault()
                //y:当前Y坐标
                let y = e.changedTouches[0].pageY
                //若上一帧坐标为false,将开始坐标赋值
                if (moveCurrentY === "") {
                  console.log("为null")
                  moveCurrentY = startDistancePageY
                }
                // console.log(moveCurrentY,'上一帧的Y轴位置,moveCurrentY')
                //移动距离超过0.3px
                if (Math.abs(moveCurrentY - y) > 0.5) {
                  // console.log(this.featuresState,'状态')
                  let type = this.featuresState
                  if (moveCurrentY > y) {
                    // console.log('touchmove上滑')
                    let v = type ? this.featuresMaxUpOffset : startDistancePageY
                    //若为false,计算偏移值逻辑:当前Y坐标-开始Y坐标
                    //若为true, 计算偏移逻辑:当前坐标+初始化偏移坐标-开始坐标
                    translateOffset = type ? v + y - startDistancePageY : y - v
                    //如果上滑超出限制,则手动让其结束,误差1px
                    if (translateOffset < this.featuresMaxUpOffset) {
                      return
                    }
                    features.style.webkitTransform = features.style.transform =
                      "translateY(" + translateOffset + "px)"
                  } else {
                    // console.log('touchmove下滑')
                    translateOffset = translateOffset + y - moveCurrentY
                    if (translateOffset >= -1) {
                      return
                    }
                    features.style.webkitTransform = features.style.transform =
                      "translateY(" + translateOffset + "px)"
                  }
                  // console.log(moveCurrentY,'上一帧Y轴位置')
                  moveCurrentY = y
                }
                // console.log(translateOffset,'偏移值')
                // console.log(moveCurrentY,'当前的Y轴位置,y,===over')
              })
            })
            box.addEventListener("touchend", e => {
              timeStamp = ""
              endTime = new Date().getTime()
              endDistancePageY = e.changedTouches[0].pageY
              moveTime = endTime - startTime
              moveDistanceY = moveCurrentY - endDistancePageY //上一帧Y轴坐标-结束Y轴坐标,<0则为下滑,反之上滑
              // console.log(moveDistanceY)
              // 滑动的时间长度
              if ((elapsed || Math.abs(endDistancePageY - startDistancePageY) > 1) && lock) {
                console.log(endDistancePageY, "结束坐标")
                console.log(elapsed, "滑动时间")
                elapsed = "" //触发过touchmove才能进入逻辑
                lock = false //重置锁定
                // 判断X轴移动的距离是否大于Y轴移动的距离,存在移动
                if (Math.abs(moveDistanceY) > 1) {
                  if (moveDistanceY > 1) {
                    // 上滑
                    console.log("上滑")
                    this.featuresState = true
                    this.resetAnime(true, map)
                  } else {
                    // 下滑
                    console.log("下滑")
                    this.resetAnime(false, map, input)
                  }
                  //未移动,上一帧与结束帧位置相同场景: 移动后停止直接松开手指
                } else {
                  console.log("触发停止滑动")
                  //用结束帧Y轴与开始帧Y轴的位置判断上滑/下拉
                  // console.log(moveCurrentY,startDistancePageY)
                  if (moveCurrentY - startDistancePageY < 1) {
                    //上拉
                    this.featuresState = true
                    this.resetAnime(true, map)
                  } else {
                    //下滑
                    this.resetAnime(false, map, input)
                  }
                }
              }
            })
          },
          //节流
          throttle(fn, delay) {
            let timer = null
            return function () {
              let context = this
              let args = arguments
              if (!timer) {
                timer = RAF.setTimeout(function () {
                  fn.apply(context, args)
                  clearTimeout(timer)
                  timer = null
                }, delay)
              }
            }
          },
          //滑动结束,复位动画,===标准滑动:未点击输入框 而弹出输入法的滑动
          resetAnime(type, map, input) {
            if (!map || !input) {
              map = this.$refs.map
              input = document.querySelector(".search-input")
            }
            //type:boolean   true:上滑  false:下拉
            const Y = type ? this.featuresMaxUpOffset : 0
            anime({
              targets: "#features",
              translateY: `${Y}px`,
              easing: "easeInOutQuad",
              duration: 100
            })
            //手指离开,初始化map样式
            map.style.transition = "height 0.2s"
            if (!type) {
              input.blur()
              this.featuresState = false
              this.upKeyboard = false
              //下滑关闭时,更改店铺列表的高度,使其能滚动
            } else {
              // this.storeListHeight = '100%'
            }
          },
          //ios键盘升起时的滑动
          iosKeyboardUpBySlide() {
            // store.style.top='0'
            // store.style.transition='top 0.2s'
            document.activeElement.scrollIntoView(true)
            const features = document.querySelector("#features")
            RAF.setTimeout(() => {
              let rect = features.getBoundingClientRect()
              if (rect.top) {
                features.style.transform = `translateY(-${rect.top}px)`
              }
            }, 100)
          },
          //重置store区域位置
          resetStoreContent(keyBoardState, featureState) {
            let features = document.querySelector("#features")
            const DOM_storeList = document.querySelector(".store-list-c")

            const keyboardHeight = Math.abs(window.innerHeight - this.windowHeight)
            // console.log(this.featuresHeight,'featuresHeight')
            // console.log(document.body.clientHeight,'body client')
            // console.log(document.documentElement.clientHeight ,'document')
            // console.log(window.innerHeight,'当前高度',this.windowHeight,'初始化高度',)
            // console.log(keyboardHeight,'键盘高度',this.featuresMaxUpOffset,'区域高度',)
            //features区域升起时
            if (featureState) {
              //**---键盘升起状态
              if (keyBoardState) {
                console.log("显示时,升起键盘")
                anime({
                  targets: "#features",
                  translateY: `-${window.innerHeight / 2}px`,
                  easing: "easeInOutQuad",
                  duration: 100
                })
                DOM_storeList.style.height = `calc(100% - ${window.innerHeight / 2}px)`
              } else {
                // this.featuresState = false
                // DOM_storeList.style.height = "100%"
                // console.log("显示时,收起键盘")
              }
            } else {
              //features区域隐藏时
              //点击input,升起键盘
              if (keyBoardState) {
                console.log("隐藏时,升起键盘")
                // features.style.transform=`translateY(${this.featuresMaxUpOffset}px)`
                anime({
                  targets: "#features",
                  translateY: `-${window.innerHeight / 2}px`,
                  easing: "easeInOutQuad",
                  duration: 100
                })
                // this.storeListHeight = '300px'
                DOM_storeList.style.overflow = "scrollY"
                DOM_storeList.style.height = `calc(100% - ${window.innerHeight / 2}px)`
                this.featuresState = true
              } else {
                //收起键盘
                this.featuresState = false
                // console.log('隐藏时,收起键盘')
              }
            }
          }
        }
      })
    </script>
  </body>
</html>
