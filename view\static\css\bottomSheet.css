.bottom-sheet {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-transition: visibility 0.5s;
  transition: visibility 0.5s;
  overflow: hidden;
  font-size: 0.33rem;
}
.bottom-sheet * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.bottom-sheet[inert="inert"] {
  pointer-events: none;
}
.bottom-sheet__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}
.bottom-sheet__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  background: #ffffff;
  overflow-y: hidden;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  pointer-events: all;
  width: 100%;
  position: relative;
}
.bottom-sheet__header-border-radius {
  border-radius: 16px 16px 0 0;
}
.bottom-sheet__content--fullscreen {
  border-radius: 0;
}
.bottom-sheet__content:not(.bottom-sheet__content--dragging) {
  -webkit-transition: transform 0.5s ease;
  transition: transform 0.5s ease;
}
.bottom-sheet__draggable-area {
  width: 100%;
  margin: auto;
  padding: 16px;
  cursor: -webkit-grab;
  cursor: grab;
}
.bottom-sheet__draggable-thumb {
  width: 40px;
  height: 4px;
  background: #333;
  border-radius: 8px;
  margin: 0 auto;
}
.bottom-sheet__main {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow-y: scroll;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  -ms-touch-action: auto !important;
  touch-action: auto !important;
  flex: 1;
}
.bottom-sheet__main::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.bottom-sheet__main::-webkit-scrollbar-corner {
  display: none;
}
.bottom-sheet__main:hover::-webkit-scrollbar-thumb {
  /*background-color: rgba(0, 0, 0, 0.2);*/
  /*border-radius: 8px;*/
}
.bottom-sheet__main::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}
.bottom-sheet__footer {
  margin: 1rem 0;
}
.bottom-sheet__footer:empty {
  display: none;
}
.fade-enter-active,
.fade-leave-active {
  -webkit-transition: opacity 0.5s;
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
