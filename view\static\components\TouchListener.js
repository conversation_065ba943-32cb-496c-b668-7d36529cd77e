// touch-listener.js
Vue.component("touchListener", {
  template: `
  <div class="carousel-warp" id="carouselPopup">
    <v-carousel
      v-model="model"
      height="100%"
      hide-delimiter-background
      delimiter-icon="mdi-minus"
      :cycle="cycle"
      :continuous="continuous"
      :interval="interval"
      :hide-delimiters='hideDelimiters'
      :touchless="touchless"
      :show-arrows="false"
    >
      <v-carousel-item v-for="(item,i) in imgArray" :key="i" :src="item"></v-carousel-item>
    </v-carousel>
  </div>
  `,
  data() {
    return {
      model: 0,
      menuPopupConfig: null,
      touchTimer: null,
      imgArray: [], //轮播图数组
      interval: 3000, //轮播间隔
      timeout: 3000, //超时时间
      cycle: true, //是否循环
      continuous: true, //是否连续
      hideDelimiters: false, //是否隐藏分隔符
      touchless: false, //是否禁用触摸
      layerIndex: null //弹窗索引
    }
  },
  created() {},
  mounted() {
    let openTable = JSON.parse(sessionStorage.getItem("openTable"))
    this.menuPopupConfig = openTable.menuPopup
    if (this.menuPopupConfig) {
      this.getMenuPopImg()
      this.initCarouselData()
      // 添加初始计时器
      this.startTimer()
      // 添加事件监听
      document.addEventListener("touchstart", this.handleTouchStart)
      document.addEventListener("touchend", this.handleTouchEnd)
    }

    // document.addEventListener("visibilitychange", function () {
    //   if (document.visibilityState === "hidden") {
    //     console.log("页面隐藏了")
    //   } else {
    //     // 页面重新出现，清除旧的计时器
    //     console.log("页面重新出现了")
    //   }
    // })
  },
  beforeDestroy() {
    if (this.menuPopupConfig) {
      document.removeEventListener("touchstart", this.handleTouchStart)
      document.removeEventListener("touchend", this.handleTouchEnd)
      clearTimeout(this.touchTimer)
    }
  },
  methods: {
    initCarouselData() {
      let { mode, carouselInterval } = this.menuPopupConfig
      // this.timeout = moment.duration(timeout, "minutes").asSeconds()
      // console.log(this.timeout, "超时秒数")
      //判断是随机显示一张图还是轮播
      if (mode == "Random") {
        this.showRandomImg()
        this.cycle = false
        this.continuous = false
        this.hideDelimiters = true
        this.touchless = true
      } else if (mode == "Carousel") {
        this.model = 0
        this.cycle = true
        this.continuous = true
        this.interval = carouselInterval * 1000
        this.hideDelimiters = false
        this.touchless = false
      }
    },

    executionCounter() {
      // const startTime = new Date().getTime()
      let { timeout, mode } = this.menuPopupConfig
      this.touchTimer = setTimeout(() => {
        // const endTime = new Date().getTime()
        // const elapsedSeconds = (endTime - startTime) / 1000
        // console.log(`已经过了${elapsedSeconds}秒！`) // 在这里添加打印语句
        if (this.imgArray.length == 0) return
        layui.use(["layer", "carousel"], () => {
          var layer = layui.layer
          layer.ready(() => {
            layer.open({
              skin: "menuCarouselLayer",
              type: 1,
              shade: [0.1, "#fff"],
              title: false, //不显示标题
              content: $(".carousel-warp"),
              success: (layero, index) => {
                this.layerIndex = index
                if (mode == "Random") {
                  this.showRandomImg()
                } else {
                  this.model = 0
                }
                clearTimeout(this.touchTimer)
              },
              end: () => {
                this.startTimer()
              }
            })
          })
        })
      }, timeout * 1000)
    },
    startTimer() {
      // console.log("开始计时器")
      this.executionCounter()
    },
    handleTouchStart() {
      // console.log("触摸并清除计时器")
      clearTimeout(this.touchTimer) // clearTimeout 只是取消定时器的执行，并不会将定时器的引用置为 null 或者清除它的值
    },
    handleTouchEnd() {
      //判断menuCarouselLayer是否存在
      if (!$(".menuCarouselLayer").length > 0) {
        this.startTimer()
      } else {
        // console.log("已经存在弹窗")
      }
      // console.log("结束触摸")
    },
    getMenuPopImg() {
      $.post({
        url: "../photoConfig/getSpecifiedPhotoConfig",
        dataType: "json",
        traditional: true,
        async: false,
        data: {
          domain: sessionStorage.getItem("domain"),
          storeNumber: this.$parent.openTable.storeNumber,
          typeNameList: ["Menu Popup Image"]
        },
        success: res => {
          let imgList = res.photoConfigList
          if (imgList.length != 0) {
            //前置拼接
            let { fileName, domain, storeNumber, typeName, extraPaths } = imgList[0]
            let defaultOss = this.$parent.defaultOss
            let frontSplicing = `${defaultOss}/${domain}/${storeNumber}/image/${typeName}/`
            let useBackupOss = sessionStorage.getItem("useBackupOss") || false
            // 使用备用oss
            if (useBackupOss) {
              let backupOssUrl = sessionStorage.getItem("backupOssUrl")
              frontSplicing = frontSplicing.replace(defaultOss, backupOssUrl)
            }
            //后置拼接
            let tailSplice = `.${this.$parent.setImgSuffix(imgList[0])}`
            const mapFileName = fileName.split(";")
            mapFileName.forEach(mapItem => {
              let finalURL = `${frontSplicing}${extraPaths}${mapItem}${tailSplice}`
              if (!useBackupOss) {
                checkImage(finalURL, [defaultOss]).then(r => {
                  this.imgArray.push(r.url)
                  // 在所有图片URL都添加到imgArray之后进行排序操作
                  this.arrayImgSort(mapFileName.length)
                })
              } else {
                this.imgArray.push(finalURL)
                // 在所有图片URL都添加到imgArray之后进行排序操作
                this.arrayImgSort(mapFileName.length)
              }
            })
          } else {
            clearTimeout(this.touchTimer)
          }
        },
        error: function () {
          console.log("Failed to get carousel")
          clearTimeout(this.touchTimer)
        }
      })
    },
    //图片排序
    arrayImgSort(length) {
      if (this.imgArray.length == length) {
        this.imgArray.sort((a, b) => {
          let aNum = a.match(/\d+(?=.jpg)/)[0]
          let bNum = b.match(/\d+(?=.jpg)/)[0]
          return aNum - bNum
        })
      }
    },
    //轮播随机值显示
    showRandomImg() {
      let index = Math.floor(Math.random() * this.imgArray.length)
      this.model = index
    }
  }
})
