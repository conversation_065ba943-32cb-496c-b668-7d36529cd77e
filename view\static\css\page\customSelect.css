.customSelect-fade-enter-active,
.customSelect-fade-leave-active {
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
.customSelect-fade-enter,
.customSelect-fade-leave-to {
  opacity: 0;
}

.customSelect {
  position: relative;
  width: 3.3rem;
  z-index: 1;
}

.customSelect-outlined {
  position: relative;
  border: none;
  border-radius: 0.213rem;
  padding: 0 0.3rem;
  height: 38px;
  display: flex;
  align-items: center;
  cursor: pointer;
  background: white;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  outline: none;
}

.customSelect-outlined::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid rgba(0, 0, 0, 0.23);
  border-radius: inherit;
  pointer-events: none;
  transition: border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.customSelect-outlined::after {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 2px solid #1976d2;
  border-radius: inherit;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.customSelect-outlined:hover::before {
  /* border-color: rgba(0, 0, 0, 0.87); */
}

.customSelect-outlined.focused::before {
  border-color: transparent;
}

.customSelect-outlined.focused::after {
  opacity: 1;
}

.customSelect-value {
  font-size: 0.35rem;
  color: rgba(0, 0, 0, 0.87);
  padding-top: 0;
  line-height: 1rem;
  display: flex;
  align-items: center;
  min-height: 1rem;
}

.customSelect-label {
  position: absolute;
  z-index: 2;
  background: #fff;
  padding: 0 4px;
  transform: translateY(-50%);
  left: 0.2rem;
  top: 0;
  font-size: 0.32rem;
}
.customSelect-label.zh {
  left: 0.1rem;
}

.customSelect-outlined:not(.focused):not(.has-value) .customSelect-label {
  top: 50%;
}

.customSelect-outlined.focused .customSelect-label {
  color: #1976d2;
  top: 0;
  transform: translateY(-50%) scale(0.85);
}

.customSelect-outlined.has-value:not(.focused) .customSelect-label {
  color: rgba(0, 0, 0, 0.6);
  top: 0;
  transform: translateY(-50%) scale(0.85);
}

.customSelect-icon {
  position: absolute;
  right: 0.2rem;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(0, 0, 0, 0.54);
  font-size: 0.32rem;
  user-select: none;
}

.customSelect-outlined.focused .customSelect-icon {
  transform: translateY(-50%) rotate(180deg);
  color: #1976d2;
}

.customSelect-menu {
  width: 5rem;
  position: absolute;
  top: 0;
  left: 0;
  background: white;
  border-radius: 0.213rem;
  overflow-y: auto;
  z-index: 100;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.customSelect-menu::-webkit-scrollbar {
  width: 6px;
}

.customSelect-menu::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.38);
  border-radius: 3px;
}

.customSelect-item {
  padding: 0.2rem 0.25rem;
  cursor: pointer;
  font-size: 0.3rem;
  color: rgba(0, 0, 0, 0.87);
  min-height: 1rem;
  display: flex;
  align-items: center;
  gap: 0.2rem;
  transition: background-color 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  outline: none;
}

.customSelect-item:hover {
  background: rgba(25, 118, 210, 0.08);
}

.customSelect-item.selected {
  background: rgba(25, 118, 210, 0.12);
  color: #1976d2;
}

.customSelect * {
  -webkit-tap-highlight-color: transparent;
}

.country-flag {
  width: 0.45rem;
  height: 0.45rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.country-code {
  color: rgba(0, 0, 0, 0.6);
  margin-left: auto;
  flex-shrink: 0;
}
