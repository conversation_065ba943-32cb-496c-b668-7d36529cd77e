var footerNav = {
  props: ["openTable", "systemLanguage", "allshoplNumber"],
  template: `   <div
    class="footer_warp"
    @touchstart="headerTouchStart"
    @touchmove="headerTouchMove"
    @touchend="headerTouchEnd"
  >
    <!-- 历史订单 -->
    <div
      class="history_car_order history_car"
      @click="onHistoryPopUp"
      v-if="(openTable.performType==1||openTable.performType==3)&&openTable.tableNumber!='PREORDER'"
    >
      <!-- v-if="openTable.performType==1||openTable.performType==3" -->
      <img
        src="../static/img/newImage/historicalLogo.jpg"
        alt=""
        class="cartImage"
      />
      <p>{{ systemLanguage.historicalLan }}</p>
    </div>
    <!-- 支付订单 -->
    <div
      class="history_car_order history_car"
      @click="goPayOrderPage"
      v-if="openTable.performType==2"
    >
      <img
        src="../static/img/newImage/payOrderIcon.jpg"
        alt=""
        class="bottomSubmitIcon"
      />
      <span>{{ systemLanguage.payOrderLan }}</span>
    </div>
    <!-- 显示购物车页 -->
    <div class="fty_send_single" @click.once="disposable" @click="clickShowCart" >
      <div class="fty_send_single_cell">
        <div v-if="openTable.tableNumber!='PREORDER'" class="defaultSubAtTheBottom">
          <img
            src="../static/img/newImage/shopcart.jpg"
            alt=""
            class="bottomSubmitIcon"
          />
          <span>{{ systemLanguage.sendDanLan }}</span>
        </div>
        <div class="sendOrderMaxWidth preOrderSubAtTheBottom" v-else >
          {{ systemLanguage.preOrderSendOrderBtn }}
        </div>
        <div class="fty_shopNum_warp">{{allshoplNumber}}</div>
      </div>
    </div>
  </div>`,

  methods: {
    headerTouchStart(e) {
      // 固定元素滑动，浮动起来
      app.fixed = true
    },
    headerTouchMove(e) {
      // 组织默认事件，防止跳动
      e.preventDefault()
    },
    headerTouchEnd(e) {
      // 互动结束，浮动解除，防止滚动元素无法滚动
      app.fixed = false
    },
    // 历史订单记录打开关闭
    onHistoryPopUp() {
      window.location.href = "../order/historyIndex" //encodeURIComponent:参数编码、
    },
    clickShowCart() {
      if (app.loading) return
      app.addhotSaleData()
      app.showCartTab = true //显示food弹窗
      // 触发是否登录弹窗
      let personalCenter = app.$refs.personalCenter || {}
      if (app.shopCartList.length && !personalCenter.cancelLogin) {
        let notInvite = sessionStorage.getItem("notInviteLogin")
        if (!notInvite) this.$nextTick(app.verifyLoginCenter)
      }

      // window.location.href = '../order/test.html'
    },

    //只触发一次的方法
    disposable() {
      app.initGiveAway()
    },
    goPayOrderPage() {
      window.location.href = "../order/payOrderPage.html"
    }
  }
}
