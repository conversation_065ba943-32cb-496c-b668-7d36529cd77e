[{"label": "Asia Pacific", "value": "Asia Pacific", "children": [{"label": "China (Beijing)", "value": "China (Beijing)", "region": "cn-beijing", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Chengdu)", "value": "China (Chengdu)", "region": "cn-chengdu", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Hangzhou)", "value": "China (Hangzhou)", "region": "cn-hangzhou", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Hohhot)", "value": "China (Hohhot)", "region": "cn-huh<PERSON><PERSON><PERSON>", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Hong Kong)", "value": "China (Hong Kong)", "region": "cn-hongkong", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Shanghai)", "value": "China (Shanghai)", "region": "cn-shanghai", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Shenzhen)", "value": "China (Shenzhen)", "region": "cn-shenzhen", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Ulanqab)", "value": "China (Ulanqab)", "region": "cn-w<PERSON><PERSON><PERSON>u", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China (Zhangjiakou)", "value": "China (Zhangjiakou)", "region": "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "Australia (Sydney) Closing Down", "value": "Australia (Sydney) Closing Down", "region": "ap-southeast-2", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}, {"label": "Indonesia (Jakarta)", "value": "Indonesia (Jakarta)", "region": "ap-southeast-5", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}, {"label": "Japan (Tokyo)", "value": "Japan (Tokyo)", "region": "ap-northeast-1", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}, {"label": "Malaysia (Kuala Lumpur)", "value": "Malaysia (Kuala Lumpur)", "region": "ap-southeast-3", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}, {"label": "Singapore", "value": "Singapore", "region": "ap-southeast-1", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}]}, {"label": "Europe & Americas", "value": "Europe & Americas", "children": [{"label": "Germany (Frankfurt)", "value": "Germany (Frankfurt)", "region": "eu-central-1", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}, {"label": "UK (London)", "value": "UK (London)", "region": "eu-west-1", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}, {"label": "US (Silicon Valley)", "value": "US (Silicon Valley)", "region": "us-west-1", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}, {"label": "US (Virginia)", "value": "US (Virginia)", "region": "us-east-1", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}]}, {"label": "Middle East", "value": "Middle East", "children": [{"label": "UAE (Dubai)", "value": "UAE (Dubai)", "region": "me-east-1", "endpointOverride": "dysmsapi.ap-southeast-1.aliyuncs.com"}]}, {"label": "Industry Cloud", "value": "Industry Cloud", "children": [{"label": "China East 1 Finance", "value": "China East 1 Finance", "region": "cn-hangzhou-finance", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China East 2 Finance", "value": "China East 2 Finance", "region": "cn-shanghai-finance-1", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China North 2 Finance (Preview)", "value": "China North 2 Finance (Preview)", "region": "cn-beijing-finance-1", "endpointOverride": "dysmsapi.aliyuncs.com"}, {"label": "China South 1 Finance", "value": "China South 1 Finance", "region": "cn-shenzhen-finance-1", "endpointOverride": "dysmsapi.aliyuncs.com"}]}]