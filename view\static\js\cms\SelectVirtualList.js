const SelectVirtualList = {
    name:'SelectVirtualList',
    props: {
        width: {
            type: Number,
            default: 250
        },
        size: {
            type: String,
            default: "small"
        },
        placeholder: {
            type: String,
            default: "请选择"
        },
        dataList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        // 虚拟列表在真实 dom 中保持渲染的项目数量
        keeps: {
            type: Number,
            default: 30
        },
        // 每个项目的估计大小，如果更接近平均大小，则滚动条长度看起来更准确。 建议分配自己计算的平均值。
        estimateSize: {
            type: Number,
            default: 34
        },
        // input输入触发方法
        virtualInputCall: Function,
        // 点击每个项目触发方法
        virtualClickItemCall: Function,
        //传入的默认数据
        defaultKey:{
            type:String,
            default:''
        }
    },
    components: {
        'virtual-list': VirtualList
    },
    watch: {
        visibleVirtualList(n) {
            if (n) {
                // 当展示虚拟列表时，需要定位到选择的位置
                this.$nextTick(() => {
                    let temp = this.curIndex ? this.curIndex : 0;
                    // 方法一：手动设置滚动位置到指定索引。
                    this.$refs.virtualListRef.scrollToIndex(temp - 1);
                    // 方法二：手动将滚动位置设置为指定的偏移量。
                    // this.$refs.virtualListRef.scrollToOffset(this.estimateSize * (temp - 1));
                })
            }
        },
    },
    data() {
        return {
            curId: "", // 当前选择的 id
            curValue: "", // 当前选择的值
            curIndex: null, // 当前选择的索引
            visibleVirtualList: false, // 是否显示虚拟列表
            itemComponent: VirtualItem, // 由 vue 创建/声明的渲染项组件，它将使用 data-sources 中的数据对象作为渲染道具并命名为：source。
        };
    },
    created() {
        this.echoDefault()
        // 监听点击子组件
        this.$on('click-virtual-item', (item) => {
            this.curId = item.key;
            this.curValue = item.label;
            this.curIndex = this.getIndex(item.key);
            this.visibleVirtualList = false;
            // console.log("item--->", item)
            this.virtualClickItemCall && this.virtualClickItemCall(item);
        })
    },
    methods: {
        //根据传入key 回显数据
        echoDefault(){
            this.curValue=this.defaultValue
            const {data:{label,key},index}=this.findDataByKey(this.defaultKey)
            this.curValue=label
            this.curId=key
            this.curIndex=index

        },
        //根据key 找到对应item
        findDataByKey(key){
            return {data:this.dataList.find(el=>el.key===key)||{},index:this.dataList.findIndex(el=>el.key===key)||0}
        },
        // 输入框改变
        handleInput(val) {
            console.log("val--->", val);
            if (!val) {
                this.curId = "";
                this.curIndex = null;
            }
            this.virtualInputCall && this.virtualInputCall(val);
        },
        getIndex(key){
          return   this.dataList.findIndex(el=>key===el.key)||0
        }
    },
    template: `
        <el-popover
            v-model="visibleVirtualList"
            popper-class="select-virtual-list-popover"
            trigger="click"
            placement="bottom-start"
            :width="width"
            >
        <virtual-list 
            v-if="visibleVirtualList"
            ref="virtualListRef"
            class="virtual-list"
            :data-key="'key'"
            :keeps="keeps"
            :data-sources="dataList"
            :data-component="itemComponent"
            :extra-props="{ curId }"
            :estimate-size="estimateSize"
            :item-class="'list-item-custom-class'"
        ></virtual-list>
        <el-input slot="reference"
                  clearable 
                  t="select"  
                  v-model="curValue"
                  style="width: 100%"
                  :size="size"
                  readonly
                  clearable
                  :placeholder="placeholder"
                  @input="handleInput">
        </el-input>
    </el-popover>
    `
}
