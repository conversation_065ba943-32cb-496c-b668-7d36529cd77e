<!DOCTYPE html>
<html lang="en" style="padding: 0 0 80px 0">
  <head>
    <meta charset="utf-8" />
    <title><PERSON><PERSON><PERSON><PERSON>i</title>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- <meta name="description" content="Bootstrap Basic Tab Based Navigation Example" /> -->

    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <link href="../../static/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <script src="../../static/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="../../static/plugins/fontawesome-free/css/all.min.css" />
    <!-- 引入moment -->
    <script src="../../static/moment/moment.js"></script>
    <!-- 弹窗组件 -->
    <link rel="stylesheet" href="../../static/plugins/toastr/toastr.css" />
    <script src="../../static/plugins/toastr/toastr.min.js"></script>
    <!--layer彈出框-->
    <link rel="stylesheet" href="../../static/tools/layer_3/mobile/need/layer.css" />
    <script type="text/javascript" src="../../static/tools/layer_3/layer.js"></script>
    <script src="../../static/plugins/sortable/Sortable.min.js"></script>
    <script src="../../static/cmsUtils/commonFunction.js"></script>
    <!-- 防抖 -->
    <!-- <script src="../../static/js/debounce.js"></script> -->
    <!-- 搜索组件css(组件js在body标签后面加载) -->
    <link rel="stylesheet" href="../../static/cmsUtils/cmsSearchList/searchList.css" />
    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />

    <style type="text/css">
      body {
        background-color: #f3efe0;
        /*页面背景色*/
        /*padding-top: 7px;*/

        font-family: Arial, Helvetica, sans-serif;
      }
      #app {
        position: absolute;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
      }
      .breadcrumbText {
        pointer-events: none;
        color: #2c3e50;
        font-size: 16px;
      }

      input[type="checkbox"],
      input[type="radio"] {
        box-sizing: border-box;
        padding: 0;
      }

      label img {
        user-select: none;
        -webkit-user-drag: none;
      }

      .custom-control-input {
        position: absolute;
        left: 0;
        z-index: -1;
        width: 1rem;
        height: 1.25rem;
        opacity: 0;
      }

      .switchWarp {
        display: flex;
        align-items: center;
      }

      /* 追加套餐遮罩层 */
      #cover {
        display: none;
        position: fixed;
        background: #000000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0.4;
        z-index: 1;
      }

      /* 菜单 */
      #menu li {
        width: 11.1111%;
        /*占宽*/
        font-weight: bold;
        /*字体加粗*/
        background-color: #f3f3f4;
        /*背景色灰色*/
        margin: 0;
        padding: 0;
      }

      #menu li a {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 菜单卡 */
      .card {
        /*border-color: #107ABB;*/
        /*border-style: solid;*/
        /*border-width: 3px;*/
        box-sizing: border-box;
        margin-top: 10px;
        /*上边距*/
        /* margin-right: 10px; */
        /*右边距*/
        border-radius: 5px;
        /*圆角*/
        background: azure;
        /*背景色*/
        /* height: 100px; */
        width: 100%;
        padding: 10px;
        font-size: 14px;
      }

      .card table {
        table-layout: fixed;

        width: 100%;
        height: 100%;
        border-spacing: 0;
      }

      .card tr {
        height: 25px; /* tr会自动计算高度可能会导致高度不统一布局错乱 */
      }

      .upload {
        opacity: 0;
        /* margin: 10px; */
        filter: alpha(opacity=0);
        align-content: center;
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 9;
        cursor: pointer;
        /*小手指*/
        font-size: 0;
        /*按钮大小*/
      }

      .card-no-img,
      .upload {
        height: 75px;
        width: 75px;
      }

      .card-img {
        width: 75px;
        max-height: 80px;
        /* vertical-align: super; */
        border-radius: 5px;
        position: absolute;
        top: 10px;
        /* margin-top: -25px; */
      }

      .card-no-img {
        border-style: solid;
        /* 无图片时边框样式*/
        border-width: 2px;
        border-color: #107abb;
        border-radius: 5px;
        vertical-align: super;
      }

      /* 开关 */
      .switch {
        /* margin: auto; */
        /* float: right; */
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
        margin-left: 5px;
      }

      .switch input {
        display: none;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      input:checked + .slider {
        background-color: #2196f3;
      }

      input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
      }

      /* 开关--圆角 */
      .slider.round {
        border-radius: 17px;
      }

      .slider.round:before {
        border-radius: 50%;
      }

      input[type="checkbox"]:disabled + .slider.round {
        cursor: not-allowed;
      }

      /* 下一级按钮 */
      .next-type-td {
        width: 35px;
      }

      .next-type-button {
        width: 35px;
        height: 43%;
        padding: 0;
        word-wrap: break-word;
      }

      #detailEditBtn {
        width: 35px;
        height: 40px;
        padding: 0;
        color: white;
      }

      #editBtn {
        width: 35px;
        height: 43%;
        margin-bottom: 15px;
        padding: 0;
        color: white;
      }

      .next-type-button-disabled {
        background: #9d9d9d;
        width: 35px;
        height: 43%;
        padding: 0;
        word-wrap: break-word;
      }

      .card-name {
        width: 100%;
        padding-left: 2px;
      }

      .card-name-warp {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0;
        min-height: 20px;
      }

      .card-name-warp:nth-of-type(1) {
        font-size: 14px;
        font-weight: 600;
        text-align: start;
      }

      .card-name-warp:nth-of-type(2) {
        font-size: 12px;
      }

      .pad_topB {
        padding: 10px 0;
      }

      .modal-body {
        display: flex;
        align-content: center;
        flex-direction: column;
        max-height: 60vh;
        overflow-y: auto;
        font-size: 14px;
        padding: 1rem 1.5rem;
      }

      .form-control {
        font-size: 14px;
      }

      .label_title {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: 700;
        font-size: 14px;
      }

      .modal-header {
        border: 0;
        text-align: center;
      }

      .modal-footer {
        border: 0;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .Form textarea {
        display: inline-block;
        padding: 6px 12px;
        font-size: 18px;
        font-weight: 300;
        line-height: 1.4;
        color: #221919;
        background: #fff;
        border: 1px solid #a4a2a2;
        resize: none;

        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -ms-box-sizing: border-box;
        -webkit-box-sizing: border-box;

        border-radius: 6px;
        -moz-border-radius: 6px;
        -webkit-border-radius: 6px;

        box-shadow: inset 0 1px rgba(34, 25, 25, 0.15), 0 1px rgba(255, 255, 255, 0.8);
        -moz-box-shadow: inset 0 1px rgba(34, 25, 25, 0.15), 0 1px rgba(255, 255, 255, 0.8);
        -webkit-box-shadow: inset 0 1px rgba(34, 25, 25, 0.15), 0 1px rgba(255, 255, 255, 0.8);

        -webkit-transition: all 0.08s ease-in-out;
        -moz-transition: all 0.08s ease-in-out;
      }

      .Form textarea {
        min-height: 90px;
      }

      .Form textarea:focus {
        border-color: #006499;
        box-shadow: 0 1px rgba(34, 25, 25, 0.15) inset, 0 1px rgba(255, 255, 255, 0.8),
          0 0 14px rgba(82, 162, 235, 0.35);
        -moz-box-shadow: 0 1px rgba(34, 25, 25, 0.15) inset, 0 1px rgba(255, 255, 255, 0.8),
          0 0 14px rgba(82, 162, 235, 0.35);
        -webkit-box-shadow: 0 1px rgba(34, 25, 25, 0.15) inset, 0 1px rgba(255, 255, 255, 0.8),
          0 0 14px rgba(82, 162, 235, 0.35);
      }

      .wordage {
        font-size: 14px;
        color: #5e5e5e;
        margin: 0 15px 0 20px;
      }

      .foodName_warp {
        text-align: center;
      }

      .foodName {
        font-size: 1rem;
      }

      .introduction {
        display: flex;
      }

      .label_title {
        text-align: right;
      }

      .nav_div {
        padding: 10px 15px;
      }

      .nav_div:last-child {
        border-right: 2px solid #606266;
      }

      .modal-content {
        font-size: 14px;
      }

      #additionalFood,
      #additionalMlist,
      #foodAllergen,
      #imageTagList {
        display: flex;
        flex-wrap: wrap;
        max-height: 120px;
        overflow-y: auto;
      }

      .foodAllergen_warp {
        width: 22%;
        margin-bottom: 20px;
      }

      .allergen_input {
        zoom: 140%;
        vertical-align: top;
        margin-right: 5px !important;
      }

      /* 追加套餐 */
      .adOrderWarp {
        font-size: 12px;
        padding-top: 5px;
        padding-bottom: 5px;
        color: #303133;
        display: flex;
        flex-wrap: wrap;
      }

      .adOrderCell {
        padding: 10px;
        margin: 0px 10px 10px 0;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .adOrderTitle {
        max-width: 100px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .adOrderCell p {
        margin-bottom: 3px;
      }

      .process {
        width: fit-content;
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
      }

      ._load {
        width: 100%;
      }

      .processbar {
        width: 200px;
        height: 30px;
      }

      .pro,
      .sumData {
        display: inline-block;
        min-width: 2rem;
        font-size: 16px;
      }

      .processbar::-webkit-progress-bar {
        background: #d7d7d7;
      }

      .processbar::-webkit-progress-value,
      .processbar::-moz-progress-bar {
        background: orange;
      }

      .hideLoad {
        display: none;
      }

      #body_warp {
        /* padding-right: 10px; */
        height: calc(100% - 40px);
        margin: 0 10px 0 0;
      }
      .border {
        overflow: auto;
        border: 5px solid #d24735;
        border-bottom: none;
        border-radius: 5px;
      }
      .border:before {
        content: attr(version);
        position: fixed;
        left: 0;
        bottom: 9%;
        display: flex;
        align-items: center;
        visibility: unset;
        min-height: 50px;
        max-width: 200px;
        padding: 0 30px;
        /*margin-left: 15px;*/
        font-size: 25px;
        color: #fff;
        overflow-wrap: break-word;
        background-color: #d24735;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        z-index: 10;
        pointer-events: none;
      }
      #body_warp:has(.process) {
        border: none;
      }
      #body_warp:has(.process):before {
        visibility: hidden;
      }
      #body_warp > div {
        float: left;
        padding-left: 10px;
      }

      .nav > li > a {
        position: relative;
        display: block;
        padding: 10px 15px;
        color: #337ab7;
        text-decoration: none;
        font-size: 14px;
      }

      .nav-tabs > li.active > a {
        color: #555;
        cursor: default;
        background-color: #fff;
        border: 1px solid #ddd;
        border-bottom-color: transparent;
      }

      .switchBox {
        cursor: pointer;
        display: flex;
        height: 100%;
        align-items: center;
        position: absolute;
      }
      .switchBox > div {
        margin-right: 10px;
      }
      .switchBox-title {
        white-space: nowrap;
        /* padding-left: 2px; */
      }
      .card-bottom {
        position: relative;
      }
      .switchBox-cell {
        display: flex;
        align-items: center;
      }
      .switchBox-icon {
        width: 18px;
        height: 18px;
      }
      .card-bottom-useTime {
        /* flex-shrink: 0; */
      }

      .card-name-info {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding-right: 10px;
        margin: 0;
        /* 去掉bootstrap的margin样式 */
      }
      /* fty-banner上传框样式 */
      .upload-container {
        width: 160px;
        height: 90px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .upload-box {
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 6px;
        overflow: hidden;
        border: 1px dashed #c0ccda;
      }

      .upload-box.uploadActive:hover .upload-content {
        background-color: rgba(0, 0, 0, 0.5);
      }
      .upload-box.uploadActive .upload-icon {
        display: none;
      }
      .upload-icon {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .upload-icon img {
        width: 70px;
      }

      .delete-icon {
        /* width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center; */
        display: none;
      }
      .delete-icon img {
        width: 30px;
      }
      .upload-box.uploadActive {
        border: 1px solid #c0ccda;
      }
      /* .upload-content没有uploadActive时的样式 */
      .upload-box:not(.uploadActive):hover {
        border-color: #409eff;
      }

      .upload-box.uploadActive:hover .delete-icon {
        display: block;
        cursor: pointer;
      }
      .upload-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 100%;
        color: #fff;
        box-sizing: border-box;
        border-radius: 6px;
      }
      .upload-picture-card {
        display: none;
      }
      img.upload-picture-card[src=""],
      img.upload-picture-card:not([src]) {
        opacity: 0;
      }
      .upload-box.uploadActive .upload-picture-card {
        width: auto !important;
        height: auto !important;
        max-width: 100% !important;
        max-height: 100% !important;
        display: block;
        margin: auto;
      }

      .modal .modal-body .pos-base-info {
        display: flex;
        flex-direction: column;
        width: fit-content;
      }
      .pos-base-info p {
        margin: 0;
        padding: 0;
      }
      .form-type-item .type-title {
        color: #3f9ef1;
        font-size: 16px;
        margin: 10px 0;
      }
      .form-type-item label.text-start span {
        display: block;
        font-size: 12px;
      }
    </style>

    <script type="text/javascript">
      $(function () {
        //自定义参数
        toastr.options = {
          closeButton: false, //是否显示关闭按钮（提示框右上角关闭按钮）。
          debug: false, //是否为调试。
          progressBar: true, //是否显示进度条（设置关闭的超时时间进度条）
          positionClass: "toast-top-center", //消息框在页面显示的位置
          onclick: null, //点击消息框自定义事件
          showDuration: "300", //显示动作时间
          hideDuration: "1000", //隐藏动作时间
          timeOut: "2000", //自动关闭超时时间
          extendedTimeOut: "1000",
          showEasing: "swing",
          hideEasing: "linear",
          showMethod: "fadeIn", //显示的方式，和jquery相同
          hideMethod: "fadeOut" //隐藏的方式，和jquery相同
          //等其他参数o
        }
      })
      var domain, baseUrl, smallImageSuffix, ftyBannerSuffix
      var foodTypeList
      var updateTime
      var pathList = new Array() //二维数组，元素定义
      var refreshTimestamp // initData返回的时间戳用于判断是否有缓存数据
      // 获取allergen icons过敏原图片
      function queryData() {
        let domain = sessionStorage.getItem("domain")
        var allergenArry = []
        let imageTagList = []
        var storeNumber = sessionStorage.getItem("storeNumber")
        if (storeNumber && storeNumber.length != 0) {
          let data = {
            domain,
            storeNumber,
            typeNameList: ["Allergen icons", "Image Tag"]
          }
          $.get({
            url: "../../photoConfig/getSpecifiedPhotoConfig",
            dataType: "json",
            traditional: true,
            data,
            success: function (res) {
              console.log(res, "照片")
              let data = res.photoConfigList
              data.forEach(item => {
                if (item.usable) {
                  let u = item.typeName === "Allergen icons" ? "" : item.extraPaths
                  let fname = item.typeName === "Allergen icons" ? item.fileName : "en"
                  item.logoUrl =
                    "http://appwise.oss-cn-hongkong.aliyuncs.com" +
                    "/" +
                    item.domain +
                    "/" +
                    item.storeNumber +
                    "/image/" +
                    item.typeName +
                    "/" +
                    u +
                    fname +
                    ".jpg?x-oss-process=image/resize,w_80,h_80"
                  if (item.typeName === "Allergen icons") {
                    allergenArry.push(item)
                  } else if (item.typeName === "Image Tag") {
                    imageTagList.push(item)
                  }
                }
              })
              // 过敏食物逻辑
              var contentHtml = ""
              let imageTagHtml = ""
              console.log(allergenArry, "过敏食物list")
              console.log(imageTagList, "图片标签list")
              allergenArry.forEach(item => {
                contentHtml +=
                  '<div class="form-check form-check-inline foodAllergen_warp">' +
                  `<label> <input  onclick="getCheckBoxValueOne('allergenInput')" class="form-check-input allergen_input" type="checkbox" id="inlineCheckbox" value="${item.fileName}" name="allergenInput"/>` +
                  `<img src="${item.logoUrl}" alt=""  /></label>` +
                  "</div>"
              })
              imageTagList.forEach(item => {
                imageTagHtml +=
                  '<div class="form-check form-check-inline foodAllergen_warp">' +
                  `<label>
                      <input  onclick="getCheckBoxValueOne('imageTagInput','${item.extraPaths}')" class="form-check-input allergen_input" type="checkbox" id="inlineCheckbox" value="${item.extraPaths}" name="imageTagInput"/>` +
                  `<img src="${item.logoUrl}" alt=""  />
                    </label> ` +
                  "</div>"
              })
              $("#foodAllergen").html(contentHtml)
              $("#imageTagList").html(imageTagHtml)
              // console.log(allergenArry, "allergenArry");
              showCheckoutByAllergen()
              showCheckoutByImageTag()
            },
            error: function () {
              toastr.error("Failed to get allergen image")
            }
          })
        } else {
          // console.log("storeNumber没有");
        }
      }

      // 排序
      function sortAllList(arry, codeType, sortType) {
        if (arry && arry.length != 0) {
          let useSort = false
          for (let i = 0; i < arry.length; i++) {
            let item = arry[i]
            if (!item) continue
            if (item[sortType] && item[sortType] != 0) {
              useSort = true
              break
            }
          }
          // 实行排序
          if (useSort) {
            arry.sort(function (a, b) {
              if (a[sortType] == null && b[sortType] == null) {
                return a[codeType].localeCompare(b[codeType])
              } else if (a[sortType] == null) {
                return 1
              } else if (b[sortType] == null) {
                return -1
              } else if (a[sortType] == b[sortType]) {
                return a[codeType].localeCompare(b[codeType])
              } else {
                return a[sortType] - b[sortType]
              }
            })
          } else {
            arry.sort(function (a, b) {
              return a[codeType].localeCompare(b[codeType])
            })
          }
        }
      }
      // 递归排序
      function recursiveSort(sortItem) {
        // if (sortItem.foodList && sortItem.foodList.length != 0) {
        //   sortAllList(sortItem.foodList, "fCode", "seq");
        //   sortItem.foodList.forEach(item => {
        //     recursiveSort(item);
        //   })
        // }
        // if (sortItem.mListList && sortItem.mListList.length != 0) {
        //   sortAllList(sortItem.mListList, "code", "sort");
        //   sortItem.mListList.forEach(item => {
        //     recursiveSort(item);
        //   })
        // }
        if (sortItem.allTypeArry && sortItem.allTypeArry.length != 0) {
          sortAllList(sortItem.allTypeArry, "code", "finalSort")
          sortItem.allTypeArry.forEach(item => {
            recursiveSort(item)
          })
        }
      }
      function recursiveToDealWith(itemData = []) {
        if (itemData.foodList && itemData.foodList.length != 0) {
          let item = itemData.foodList
          item.forEach(foodlistItem => {
            recursiveToDealWith(foodlistItem)
          })
        }
        if (itemData.mListList && itemData.mListList.length != 0) {
          let item = itemData.mListList
          item.forEach(mlistItem => {
            recursiveToDealWith(mlistItem)
          })
        }
        let ftyItemData = itemData.foodTypeList || []
        let mtyItemData = itemData.mTypeList || []
        if (ftyItemData.length != 0) {
          toDealWithFty(ftyItemData)
        }
        if (mtyItemData.length != 0) {
          toDealWithMty(mtyItemData)
        }

        if (ftyItemData.length != 0 || mtyItemData.length != 0) {
          itemData.allTypeArry = [...ftyItemData, ...mtyItemData]
          // delete itemData.foodTypeList
          // delete itemData.mTypeList
        }

        recursiveSort(itemData) // 递归排序单个foodlist里所有数据
      }

      function toDealWithFty(item) {
        item.forEach(inItem => {
          inItem.typeName = "ftyItem" //挂上标识 混合排序用
          inItem.foodList.forEach(ftyItem => {
            if (ftyItem.foodTypeList && ftyItem.foodTypeList.length != 0) {
              ftyItem.foodTypeList.forEach(v => {
                v.typeName = "ftyItem" //挂上标识 混合排序用
              })
            }
            let ftyInfoInMty = ftyItem.mTypeList || [] //foodTypeList=>foodList=>mTypeList
            if (ftyInfoInMty.length != 0) {
              ftyInfoInMty.forEach(myXi => {
                myXi.typeName = "mtyItem"
              })
            }
            recursiveToDealWith(ftyItem)
          })
        })
      }
      function toDealWithMty(item) {
        item.forEach(inItem => {
          inItem.typeName = "mtyItem" //挂上标识 混合排序用
          inItem.mListList.forEach(mtyItem => {
            let mtyInMlisInMty = mtyItem.mTypeList || []
            if (mtyInMlisInMty.length != 0) {
              mtyInMlisInMty.forEach(myXi => {
                myXi.typeName = "mtyItem"
              })
            }
            recursiveToDealWith(mtyItem)
          })
        })
      }
      function throttle(cb, wait = 300) {
        let last = 0
        return function () {
          var now = new Date().getTime()
          if (now - last > wait) {
            cb.call(this)
            last = new Date().getTime()
          }
        }
      }
      function formatNum(num) {
        if (!/^(\+|-)?(\d+)(\.\d+)?$/.test(num)) {
          return num
        }
        var a = RegExp.$1,
          b = RegExp.$2,
          c = RegExp.$3
        var re = new RegExp().compile("(\\d)(\\d{3})(,|$)")
        while (re.test(b)) b = b.replace(re, "$1,$2$3")
        return a + "" + b + "" + c
      }

      function createdProgressElement() {
        element1 =
          "<img id='loading' type='image/svg + xml' src='../../ static/img/svg/loading1.svg' />"
        let html = `<div class=\'process\'>${element1} <div class='_load'>Network speed:
          <span class="pro">0.00</span>
          kb/s
        </div>  <div class='_load'>
          Data transmitted:
          <span class="sumData">0.00</span>
          kb
        </div> </div>`
        $(document.getElementById("body_warp")).html(html)
      }

      function initData(type) {
        window.parent.disabledDrag() //  调用父页面方法禁止拖拽开关
        window.parent.showVersionTag() // 调取ifm父页面标出当前版本
        // 若不是初始加载,重新写入进度动画
        if (type != "init") createdProgressElement()
        let process = document.querySelector(".process")
        let sumData = document.querySelector(".sumData")
        let pro = document.querySelector(".pro")
        let { storeNumber, domain, versionNumber } = getRequestHeaderParams()
        let isFoodCourt = sessionStorage.getItem("storeSelectionFoodCourtMode") || false // 获取选择台号后是否进入foodCourt模式(默认false)
        return new Promise((resolve, reject) => {
          var xhr = new XMLHttpRequest()
          xhr.open("get", `../../manager_data/initData?isFoodCourt=${isFoodCourt}`, true)
          // 设置的header头必须要放到open()后面
          xhr.setRequestHeader("domain", domain)
          xhr.setRequestHeader("storeNumber", storeNumber)
          xhr.setRequestHeader("versionNumber", versionNumber)
          xhr.responseType = "text"
          let time = 0
          let sum = 0
          let number = 0
          xhr.onprogress = function (pe) {
            number++
            let time1 = pe.timeStamp.toFixed(0)
            if (time !== 0) {
              if (time1 - time < 100) {
                pro.innerHTML = formatNum(((pe.loaded - sum) / 1024).toFixed(2))
              } else {
                pro.innerHTML = formatNum(
                  ((pe.loaded - sum) / 1.024 / ((time1 - time) * 10)).toFixed(2)
                )
              }
            } else {
              pro.innerHTML = formatNum(((pe.loaded - sum) / 1024).toFixed(2))
            }
            sumData.innerHTML = formatNum((pe.loaded / 1024).toFixed(2))
            time = time1
            sum = pe.loaded
          }
          xhr.onloadend = function () {
            resolve()
            let response = JSON.parse(xhr.response)
            if (response.hasOwnProperty("error")) {
              process.classList.add("hideLoad")
              alert("Failed to initialize data")
              reject()
            }
            // 调用getCacheData方法获取状态信息
            refreshTimestamp = response.updateTime
            getCacheData()
            response.foodTypeListJson = response.foodTypeListJson || "[]"
            foodTypeList = $.parseJSON(response.foodTypeListJson)
            console.log(JSON.parse(JSON.stringify(foodTypeList)), "初始数据返回")
            if (response.domain) {
              sessionStorage.setItem("domain", response.domain)
              baseUrl = `http://appwise.oss-cn-hongkong.aliyuncs.com/${response.domain}/`
              if (storeNumber !== "*") {
                baseUrl += `${storeNumber}/image/`
              } else {
                baseUrl += "image/"
              }
              if (versionNumber !== "") {
                //为空时为生产环境
                baseUrl += `${versionNumber}/`
              }

              smallImageSuffix = "?x-oss-process=image/resize,w_75" + "," + new Date()
              ftyBannerSuffix = "?x-oss-process=image/resize,w_160" + "," + new Date()
            }
            sortAllList(foodTypeList, "code", "finalSort")
            foodTypeList.forEach(item => {
              item.typeName = "ftyItem"
              // sortAllList(item.foodList, "fCode", "seq");
              item.foodList.forEach(foodlistItem => {
                recursiveToDealWith(foodlistItem)
              })
            })
            // 初始加载动态引入script标签
            if (type == "init") {
              $.getScript("../../static/cmsUtils/createdElm.js")
                .done(() => {
                  createFoodTypeElm()
                })
                .fail(() => {
                  console.log("加载createdElm失败")
                })
              $.getScript("../../static/cmsUtils/cmsHomePageDrag.js")
                .done(() => {})
                .fail(() => {
                  console.log("加载cmsDrag失败")
                })
            } else {
              let dataType
              if (pathList.length != 0) dataType = pathList[pathList.length - 1].type // 获取上级数据的类型
              setTimeout(() => {
                replaceData(foodTypeList)
                switch (type) {
                  case "food":
                  case 1:
                    createFoodElm(pathList[0].index, pathList[0].id, dataType)
                    break
                  case 2:
                    createSetMealElm(pathList[1].index, pathList[1].id, dataType)
                    break
                  case 3:
                    createSiElm(pathList[2].index, pathList[2].id, dataType)
                    break
                  case 4:
                    createWuElm(pathList[3].index, pathList[3].id, dataType)
                    break
                  case 5:
                    createLiuElm(pathList[4].index, pathList[4].id, dataType)
                    break
                  case 6:
                    createQiElm(pathList[5].index, pathList[5].id, dataType)
                    break
                  case 7:
                    createBaElm(pathList[6].index, pathList[6].id, dataType)
                    break
                  case 8:
                    createJiuElm(pathList[7].index, pathList[7].id, dataType)
                    break
                  default:
                    if (type !== "init") createFoodTypeElm()
                }
              }, 10)
            }
            console.log(foodTypeList, "处理好的数据")

            window.parent.passDrag("init") // 父页面开启拖拽开关
          }

          xhr.ontimeout = function () {
            reject()
            alert("Failed to initialize data")
          }
          xhr.onerror = function () {
            reject()
            alert("Failed to initialize data")
          }
          xhr.send()
        })
      }
      /**
       * @description 在input[type=file]时,校验图片的宽高
       * 宽:360 - 440; 高:240 - 292
       * @param {File} file 文件file
       * */
      function checkImageWH(file) {
        // let reader = new FileReader()
        // reader.readAsDataURL(file)
        // reader.onload = function () {
        //   let base64 = this.result
        //   let suffix = file.type.split("image/")[1]
        //   // let { width, height } = ReadBase64Dimension(base64, suffix)
        // }
        let iframe = window.top.document.querySelector("iframe")
        let sizeMap = iframe
          ? JSON.parse(iframe.dataset.imageSize)
          : {
              imageMaxWidth: 440,
              imageMinWidth: 360,
              imageMaxHigh: 292,
              imageMinHigh: 240
            }
        let url = window.URL || window.webkitURL
        let img = new Image() //手动创建一个Image对象
        img.src = url.createObjectURL(file) //创建Image的对象的url
        return new Promise(res => {
          img.onload = function () {
            let { width, height } = this
            let cw = width <= sizeMap["imageMaxWidth"] && width >= sizeMap["imageMinWidth"]
            let ch = height <= sizeMap["imageMaxHigh"] && height >= sizeMap["imageMinHigh"]
            console.log("图片width:", width, "图片height:", height)
            if (cw && ch) {
              res({ state: true })
            } else {
              res({
                state: false,
                sizeMap
              })
            }
          }
        })
      }
      /*
         id:文件名
         label:this,带FIle
         typeName:文件夹名
          */
      async function upload(id, label, typeName, uploadType) {
        let domain = sessionStorage.getItem("domain")
        let file = $(label)[0].files[0]
        if (typeName === "food" || typeName === "mlist") {
          let { state, sizeMap } = await checkImageWH(file).catch()
          if (!state) {
            let { imageMaxWidth, imageMinWidth, imageMaxHigh, imageMinHigh } = sizeMap
            toastr.error(
              `The maximum width and height is ${imageMaxWidth} x ${imageMaxHigh},and minimum  is ${imageMinWidth} x ${imageMinHigh}`
            )
            return false
          }
        }
        // 判断文件大小合法性
        if (!file) return
        // 上传图片
        var fd = new FormData()
        fd.append("id", id)
        fd.append("file", file)
        fd.append("updateTime", updateTime)
        fd.append("pathList", JSON.stringify(pathList))
        fd.append("typeName", typeName)
        fd.append("domain", domain)
        let version = sessionStorage.getItem("versionNumber")
        if (version !== "PROD") {
          fd.append("versionNumber", version)
        }
        if (uploadType == "ftyBanner") {
          fd.append("extraPaths", "banner/")
        }
        $.post({
          url: "../../manager_photo/upload",
          dataType: "json",
          processData: false,
          contentType: false,
          cache: false,
          data: fd,
          success: function (result) {
            if (result.statusCode == 200) {
              updateTime = result.updateTime
              // 更新单一数据
              if (result.updateFlag) {
                if (uploadType == "ftyBanner") {
                  //fty的banner上传
                  let bannerUrl = baseUrl + typeName + "/banner/" + id + ".jpg" + ftyBannerSuffix
                  if (!$(label)) return //初始还未加载
                  let imgDom = $(label)
                    .siblings(".upload-box")
                    .find("img[id='upload-picture-card']")
                  imgDom.attr("src", bannerUrl)
                  let uploadBox = $(label).siblings(".upload-box")
                  toastr.success("The upload is successful")
                  uploadBox.addClass("uploadActive")
                } else {
                  // 更新时间一致，只更新当前数据
                  var imgLaber = $(label.nextElementSibling)
                  imgLaber.attr("onerror", "noImg(this);")
                  imgLaber.attr(
                    "src",
                    baseUrl + typeName + "/" + id + ".jpg" + smallImageSuffix + "," + new Date()
                  )
                  imgLaber.removeClass("card-no-img")
                  imgLaber.addClass("card-img")
                  // 更新title属性
                  $(label).attr("title", id + ".jpg")
                }
              } else {
                // 更新时间不一致，初始化全部数据，并重置页面
                foodTypeList = $.parseJSON(result.foodTypeListJson)
                //TODO:弹出数据更新了的提示框，自己消失的那种
                createFoodTypeElm()
              }
            } else if (result.statusCode == 4033) {
              console.log("历史版本不可更改")
            } else {
              toastr.error(result.errMessage1)
            }
          },
          error: function (data) {
            toastr.error("Upload picture abnormal")
          }
        })
      }

      //无法加载图片时使用默认图片
      function noImg(img) {
        $(img).attr("src", "../../static/img/noimage3.jpg")
        $(img).removeAttr("onerror") //避免读取不到默认图片时死循环
        $(img).removeClass("card-img")
        $(img).addClass("card-no-img")
        // 获取兄弟元素input添加属性attr
        var input = $(img).siblings("input")
        input.attr("title", "Please upload pictures")
      }

      function noFtyBanner(img) {
        let uploadBox = $(img).parent()
        uploadBox.removeClass("uploadActive")
        $(img).attr("src", "")
      }
      function getTargetItem(typeIndex, itemHierarchy, type) {
        if (itemHierarchy != 1) {
          var foodTypeee = foodTypeList[outFtypeInex]
          var foodee = foodTypeee.foodList[outfoodInex]
        }
        // hierarchy 层级
        let targetItem
        // fType
        if (type == "foodType") {
          if (itemHierarchy == "1") {
            // 外层ftype，直接获取索引改变
            targetItem = foodTypeList[typeIndex]
          } else if (itemHierarchy == "2") {
            targetItem = foodee.allTypeArry[typeIndex]
          } else if (itemHierarchy == "3") {
            targetItem = siClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "4") {
            targetItem = wuClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "5") {
            targetItem = liuClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "6") {
            targetItem = qiClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "7") {
            targetItem = baClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "8") {
            targetItem = jiuClickItem.allTypeArry[typeIndex]
          }
        } else if (type == "foodlist") {
          // // foodlist
          if (itemHierarchy == "1") {
            // 外层food，直接获取索引改变
            targetItem = foodList[typeIndex]
          } else if (itemHierarchy == "2") {
            // food下foodlist，获取food再获取foodlist
            targetItem = foodee.foodList[typeIndex]
          } else if (itemHierarchy == "3") {
            targetItem = siClickItem.foodList[typeIndex]
          } else if (itemHierarchy == "4") {
            targetItem = wuClickItem.foodList[typeIndex]
          } else if (itemHierarchy == "5") {
            targetItem = liuClickItem.foodList[typeIndex]
          } else if (itemHierarchy == "6") {
            targetItem = qiClickItem.foodList[typeIndex]
          } else if (itemHierarchy == "7") {
            targetItem = baClickItem.foodList[typeIndex]
          } else if (itemHierarchy == "8") {
            targetItem = jiuClickItem.foodList[typeIndex]
          }
        } else if (type == "mlistItem") {
          // mType
          if (itemHierarchy == "2") {
            targetItem = foodee.mListList[typeIndex]
          } else if (itemHierarchy == "3") {
            targetItem = siClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "4") {
            targetItem = wuClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "5") {
            targetItem = liuClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "6") {
            targetItem = qiClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "7") {
            targetItem = baClickItem.mListList[typeIndex]
          } else if (itemHierarchy == "8") {
            targetItem = jiuClickItem.mListList[typeIndex]
          }
        } else if (type == "mTypeItem") {
          if (itemHierarchy == "2") {
            targetItem = foodee.allTypeArry[typeIndex]
          } else if (itemHierarchy == "3") {
            targetItem = siClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "4") {
            targetItem = wuClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "5") {
            targetItem = liuClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "6") {
            targetItem = qiClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "7") {
            targetItem = baClickItem.allTypeArry[typeIndex]
          } else if (itemHierarchy == "8") {
            targetItem = jiuClickItem.allTypeArry[typeIndex]
          }
        }
        return targetItem
      }

      // foodType弹窗
      var customfoodType
      var typeOption // 定义组别变量
      function onfTypeModal(e, type) {
        if (type == "1") {
          // 外层ftype，直接获取索引改变
          customfoodType = foodTypeList[e]
          // console.log(customfoodType, "来源foodTypeList")
        } else if (type == "2") {
          var foodTypeee = foodTypeList[outFtypeInex]
          var foodee = foodTypeee.foodList[outfoodInex]
          customfoodType = foodee.allTypeArry[e]
          // console.log(customfoodType, "来源foodTypeList点击的细项")
        } else if (type == "3") {
          customfoodType = siClickItem.allTypeArry[e]
        } else if (type == "4") {
          customfoodType = wuClickItem.allTypeArry[e]
        } else if (type == "5") {
          customfoodType = liuClickItem.allTypeArry[e]
        } else if (type == "6") {
          customfoodType = qiClickItem.allTypeArry[e]
        } else if (type == "7") {
          customfoodType = baClickItem.allTypeArry[e]
        } else if (type == "8") {
          customfoodType = jiuClickItem.allTypeArry[e]
        }
        resetModalSubtitle("ft", customfoodType)
        $("#foodTypeModal").modal("show")
        //获取全局fty和food的索引找到food
        console.log(customfoodType, "点击的fty弹窗")
        $("#fTypeNameA").val(customfoodType.fType_nameA)
        $("#fTypeNameB").val(customfoodType.fType_nameB)
        $("#fTypeMulti1").val(customfoodType.multi1)
        $("#fTypeshowInFCodes").val(customfoodType.show_In_FCodes)
        $("#fTypeNotShowInFCodes").val(customfoodType.not_Show_With_FCodes)
        $("#hotSaleFTCodes").val(customfoodType.hotSaleFTCodes)
        $("#foodTypetitle").text(customfoodType.name)
        $("#mergeFty").val(customfoodType.combine_with_foodType)
        $("#addFCode").val(customfoodType.addFCode)
        $("#hiddenFCode").val(customfoodType.hiddenFCode)
        $("#use_date_type").val(customfoodType.use_date2)
        $("#use_time_type").val(customfoodType.use_time2)
        $("#use_dow_type").val(customfoodType.use_dow2)
        // 请求组别数据
        $("#typeSelect").empty()
        $("#columnSelect").val(
          customfoodType.display_column == undefined ? "1" : customfoodType.display_column
        )
        // $("input[name='itemCtrl']:checked").val(customfoodType.item_ctrl_model||'0')
        if (customfoodType.item_ctrl_model === 0 || !customfoodType.item_ctrl_model) {
          $("#show_sold_out").prop("checked", true)
          $("#hidden").removeProp("checked")
        } else {
          $("#hidden").prop("checked", true)
          $("#show_sold_out").removeProp("checked")
        }
        $("#foodColumnSelect").val(customfoodType.food_display_column || "1") //详情页fty下的foodlist分栏

        // 此处到时候验证下 $("#individual_order").prop("checked", customfoodType.individual_order);
        let { food_show_src, individual_order } = customfoodType
        $("#foodShowImg").prop("checked", food_show_src ? food_show_src : false)
        $("#individual_order").prop("checked", individual_order ? individual_order : false)
        // $("#ckbx").attr("checked","checked");
        let data = {
          storeNumber: sessionStorage.getItem("storeNumber")
        }
        $.get({
          url: "../../manager_typeRestrict/getRestrict",
          data,
          success: function (res) {
            if (res.code == 200) {
              let data = res.list
              let typeNameObj = []
              // 手动塞数据进option
              data.forEach(item => {
                $("#typeSelect").append(
                  `<option value=${item.type_name}>${item.type_name}</option>`
                )
                // 存储组别名字用于判断回显
                typeNameObj.push(item.type_name)
              })
              // 判断是否存在type_restrict_name，有则回显，无则Please choose
              if (typeNameObj.includes(customfoodType.type_restrict_name)) {
                $("#typeSelect").prepend("<option value=''>Please choose</option>")
                $("#typeSelect")
                  .find("option[value=" + customfoodType.type_restrict_name + "]")
                  .prop("selected", true)
              } else {
                $("#typeSelect").prepend("<option value=''>Please choose</option>")
                $("#typeSelect").find('option[value=""]').prop("selected", true)
              }
            } else {
              toastr.error("Error")
            }
          },
          error: function () {
            // console.log(res);
            toastr.error("Error")
          }
        })
        //塞入bannerDom
        let uploadParams = JSON.stringify({
          code: customfoodType.code,
          typeName: "foodType",
          photoSuffix: customfoodType.photoSuffix
        })

        let bannerUrl = `${baseUrl}foodType/banner/${
          customfoodType.code
        }.jpg?x-oss-process=image/resize,w_160,${new Date()}`

        let bannerDom = `<input type="file" onchange="uploadInput('${customfoodType.code}',this,'foodType')" id="upload-input" hidden accept="image/*" />
                      <div id="upload-box" class="upload-box uploadActive">
                        <div class="upload-content">
                          <span class="upload-icon" id="uploadIcon" onclick="uploadIcon(this)">
                            <img src="../../static/img/cms/add.jpg" alt="" />
                          </span>
                          <span class="delete-icon" id="deleteIcon"  onclick="uploadDelImg(event,this)" data-params='${uploadParams}'>
                            <img src="../../static/img/cms/del.jpg" alt="" />
                          </span>
                        </div>
                        <img src="${bannerUrl}" id="upload-picture-card" class="upload-picture-card" onerror="noFtyBanner(this)" alt="" />
                      </div>`

        $("#upload-container").html(bannerDom)
      }

      function resetModalSubtitle(type, data = {}) {
        let code = ""
        let name = ""
        let name2 = ""
        let selector = ""
        switch (type) {
          case "ft":
            code = data.code
            name = data.name
            name2 = data.name2
            selector = "#foodTypeModal"
            break
          case "fl":
            code = data.fCode
            name = data.desc1
            name2 = data.desc2
            selector = "#foodModal"
            break
          case "mt":
            code = data.code
            name = data.desc
            name2 = data.desc2
            selector = "#detailModal"
            break
          case "ml":
            code = data.code
            name = data.name
            name2 = data.name2
            selector = "#detailModal"
            break
          default:
            break
        }

        let children = $(selector + " .pos-base-info").children()
        if (type === "mt") {
          $(children[1]).html("Modifier Group: <strong></strong>")
        } else if (type === "ml") {
          $(children[1]).html("Modifier Code: <strong></strong>")
        }
        $(children[1])
          .find("strong")
          .text(code || "")
        $(children[2])
          .find("strong")
          .text(name || "")
        $(children[3])
          .find("strong")
          .text(name2 || "")
      }
      // foodType提交

      // foodlist弹窗
      var customfood
      // 外层food弹窗
      function onfoodModal(e, type) {
        if (type == "1") {
          // 外层food，直接获取索引改变
          customfood = foodList[e]
          // console.log(customfood, "来源第二层food");
        } else if (type == "2") {
          // food下foodlist，获取food再获取foodlist
          var foodTypeee = foodTypeList[outFtypeInex]
          var foodee = foodTypeee.foodList[outfoodInex]
          customfood = foodee.foodList[e]
          console.log(customfood, "来源foodlist点击的细项")
        } else if (type == "3") {
          customfood = siClickItem.foodList[e]
          console.log(customfood, "来源第四层foodlist点击的细项")
        } else if (type == "4") {
          customfood = wuClickItem.foodList[e]
          console.log(customfood, "来源第五层foodlist点击的细项")
        } else if (type == "5") {
          customfood = liuClickItem.foodList[e]
        } else if (type == "6") {
          customfood = qiClickItem.foodList[e]
        } else if (type == "7") {
          customfood = baClickItem.foodList[e]
        } else if (type == "8") {
          customfood = jiuClickItem.foodList[e]
        }
        resetModalSubtitle("fl", customfood)
        console.log(customfood, "点击的foolist弹窗")
        foodAllImageTagData = null
        $("#nameA").val(customfood.nameA || "")
        $("#nameB").val(customfood.nameB || "")
        $("#foodMulti1").val(customfood.multi1 || "")
        $("#prodTextareaA").val(customfood.prod_textareaA)
        $("#prodTextareaB").val(customfood.prod_textareaB)
        $("#prodTextareaC").val(customfood.prod_textareaC)
        $("#show_In_FCodes").val(customfood.show_In_FCodes)
        $("#foodNotshowInFCodes").val(customfood.not_Show_With_FCodes)
        $("#hotSaleFCodes").val(customfood.hotSaleFCodes)
        $("#foodModaltitle").text(customfood.desc2)
        $("#minQty2").val(customfood.minQty2)
        $("#maxQty2").val(customfood.maxQty2)
        $("#foodModal").modal("show")
        $("#use_date_food").val(customfood.use_date2)
        $("#use_time_food").val(customfood.use_time2)
        $("#use_dow_food").val(customfood.use_dow2)
        $("#takeUpQty").val(customfood.takeUpQty)
        $("#maxSingleOrderCount").val(customfood.maxSingleOrderCount)
        $("#maxSingleQuantityToCart").val(customfood.maxSingleQuantityToCart)
        $("#imageTagAzimuth").val(customfood.imageTagAzimuth || "topRight")
        $("#fPackingBoxMListCode").val(customfood.packingBoxMListCode)
        console.log(customfood.bigPictureDisplay, customfood, "customfood")
        // 具有 true 和 false 两个属性的属性，如 checked, selected 或者 disabled 使用prop()，其他的使用 attr()
        $("#bigPictureDisplay").prop("checked", Boolean(customfood.bigPictureDisplay))

        $("#FexpiredBanSuperior").prop("checked", Boolean(customfood.expiredBanSuperior ?? true))

        queryData()
        queryAdOeder("fCode", customfood.fCode)
      }

      var foodAllergenData = ""
      let foodAllImageTagData = null
      var customDetail = "" //定义通用获取到的弹窗对象（修改重新赋值） ;

      // 单food\mlist查询追加套餐
      function queryAdOeder(type, typeCode) {
        // console.log(fCode, "fCode");
        let data = {}
        data[type] = typeCode
        let url = null
        if (type === "code") {
          console.log(data)
          url = "../../manager_extraMListSetMeal/selectOne"
        } else {
          url = "../../manager_extraSetMeal/selectOne"
        }
        $.get({
          url,
          data,
          dataType: "json",
          success: function (res) {
            // console.log(res, "追加套餐");
            if (res.RESULT_CODE == 0) {
              // 遍历套餐item
              let setMealItemHtml = ""
              res.list.forEach(item => {
                setMealItemHtml +=
                  ' <div class="adOrderCell">' +
                  `<p class="adOrderTitle">${item.subCode}</p>` +
                  ' <div class="adOrderItemBtn">' +
                  `<button type="button" class="btn btn-warning btn-xs" onclick="onEditSetMeal(this,'${type}')" data-setMeal-item='${JSON.stringify(
                    item
                  )}'>Edit</button>` +
                  " " +
                  `<button type="button" class="btn btn-danger btn-xs" onclick="onDelSetMeal(${item.id},'${type}')" >Delete</button>` +
                  "</div>" +
                  "</div>"
              })
              if (type === "code") {
                $("#additionalMlist").html(setMealItemHtml)
              } else {
                $("#additionalFood").html(setMealItemHtml)
              }
            } else {
              toastr.error("Set Meal data query failed")
            }
          },
          error: function () {
            toastr.error("Set Meal data query failed")
          }
        })
      }

      // 细项mListList弹窗
      // var customMlist = '';
      //mty需要显示/隐藏则mList反之的 差异
      function popModelOptionDiff(type) {
        if (type === "mty") {
          $("#onAdSetMealMlist").hide() // Add Set Meal 按钮 mList专用
          $("#detailModal #additionalMlist").parent().hide() // Set Meal Item  mList专用
          $("#mlistShowImg").parents(".row").show() // Mlist Show Img  mty专用
          $("#mlistColumnSelect").parents(".row").show() //Mlist Display Column mty专用
          $("#mPackingBoxMListCode").parents(".row").hide() // packingBoxMListCode  mList专用
          $("#mlistItemCtrlModelSelect").show()
        } else {
          $("#onAdSetMealMlist").show()
          $("#detailModal #additionalMlist").parent().show()
          $("#mlistShowImg").parents(".row").hide()
          $("#mlistColumnSelect").parents(".row").hide()
          $("#mPackingBoxMListCode").parents(".row").show() // packingBoxMListCode  mList专用
          $("#mlistItemCtrlModelSelect").hide()
        }
      }
      function onMlistModal(e, type) {
        if (type == "2") {
          var foodTypeee = foodTypeList[outFtypeInex]
          var foodee = foodTypeee.foodList[outfoodInex]
          customDetail = foodee.mListList[e]
        } else if (type == "3") {
          customDetail = siClickItem.mListList[e]
        } else if (type == "4") {
          customDetail = wuClickItem.mListList[e]
        } else if (type == "5") {
          customDetail = liuClickItem.mListList[e]
        } else if (type == "6") {
          customDetail = qiClickItem.mListList[e]
        } else if (type == "7") {
          customDetail = baClickItem.mListList[e]
        } else if (type == "8") {
          customDetail = jiuClickItem.mListList[e]
        }
        customDetail.detailType = "Mlist"
        resetModalSubtitle("ml", customDetail)
        console.log(customDetail, "customDetail")
        $("#detailNameA").val(customDetail.nameA || customDetail.name)
        $("#detailNameB").val(customDetail.nameB || customDetail.name2)
        $("#detailMulti1").val(customDetail.multi1)

        $("#detailtitle").text(customDetail.name2)
        $("#mPackingBoxMListCode").val(customDetail.packingBoxMListCode)
        $("#MexpiredBanSuperior").prop("checked", Boolean(customDetail.expiredBanSuperior ?? true))
        //mlist 新增add set meal 按钮,显示按钮与meal item选项
        //查询meal item
        queryAdOeder("code", customDetail.code)
        popModelOptionDiff("mList")
        $("#detailModal").modal("show")
        console.log(customDetail, "来源mListList点击的细项")
      }
      // 细项mTypeList弹窗
      // var customMType = '';

      function onfMtypeModal(e, type) {
        if (type == "2") {
          var foodTypeee = foodTypeList[outFtypeInex]
          var foodee = foodTypeee.foodList[outfoodInex]
          customDetail = foodee.allTypeArry[e]
        } else if (type == "3") {
          customDetail = siClickItem.allTypeArry[e]
        } else if (type == "4") {
          customDetail = wuClickItem.allTypeArry[e]
        } else if (type == "5") {
          customDetail = liuClickItem.allTypeArry[e]
        } else if (type == "6") {
          customDetail = qiClickItem.allTypeArry[e]
        } else if (type == "7") {
          customDetail = baClickItem.allTypeArry[e]
        } else if (type == "8") {
          customDetail = jiuClickItem.allTypeArry[e]
        }

        customDetail.detailType = "Mtype"
        // 赋值
        $("#detailNameA").val(customDetail.nameA || customDetail.desc)
        $("#detailNameB").val(customDetail.nameB || customDetail.desc2)
        $("#detailMulti1").val(customDetail.multi1)

        $("#detailtitle").text(customDetail.desc2)
        $("#mlistColumnSelect").val(customDetail.mList_display_column || "1") //详情页mty下的mlist分栏
        $("#mlistShowImg").prop("checked", customDetail.mList_show_src)
        if (customDetail.item_ctrl_model === 0 || !customDetail.item_ctrl_model) {
          $("#mlist_show_itemCtrl").prop("checked", true)
          $("#mlist_hidden_itemCtrl").removeProp("checked")
        } else {
          $("#mlist_hidden_itemCtrl").prop("checked", true)
          $("#mlist_show_itemCtrl").removeProp("checked")
        }
        $("#detailModal").modal("show")
        //mtype 隐藏addmeal按钮/setmealitem选项
        popModelOptionDiff("mty")
        resetModalSubtitle("mt", customDetail)
        console.log(customDetail, "来源mTypeList点击的细项")
      }

      function getCheckBoxValueOne(inputName, fileName) {
        if (inputName === "allergenInput") {
          let ids = $('input:checkbox[name="allergenInput"]:checked')
          let data = ""
          for (let i = 0; i < ids.length; i++) {
            data += ids[i].value + (i == ids.length - 1 ? "" : ",")
          }
          foodAllergenData = data
        } else if (inputName === "imageTagInput") {
          let imageTagInput = document.getElementsByName("imageTagInput")
          let checkedList = Array.from(imageTagInput)
            .filter(input => input.checked)
            .map(input => input.value)

          if (checkedList.length >= 3) {
            // 禁用其他的
            Array.from(imageTagInput)
              .filter(input => !input.checked)
              .forEach(input => (input.disabled = true))
          } else {
            // 启用其他的
            Array.from(imageTagInput)
              .filter(input => !input.checked)
              .forEach(input => (input.disabled = false))
          }

          foodAllImageTagData = checkedList.join(",")
          console.log(foodAllImageTagData, "foodAllImageTagData")
        }
      }

      // 复选回显
      function showCheckoutByAllergen() {
        let allergenIcons = customfood.allergen_icons
        if (allergenIcons && allergenIcons.length != 0) {
          let checkArray = allergenIcons.split(",")
          var checkBoxAll = $("input[name='allergenInput']")
          checkArray.forEach((item, i) => {
            $.each(checkBoxAll, function (j, checkbox) {
              var checkValue = $(checkbox).val()
              if (checkArray[i] == checkValue) {
                $(checkbox).attr("checked", true)
              }
            })
          })

          // arry.forEach(j=>{
          //   if(item.)
          // })
        }
        getCheckBoxValueOne("allergenInput")
      }
      function showCheckoutByImageTag() {
        if (!customfood.imageTagName) return
        let checkedList = customfood.imageTagName.split(",")
        let ele = $('input:checkbox[name="imageTagInput"]')
        $.each(ele, function (j, checkbox) {
          let checkValue = $(checkbox).val()
          checkedList.includes(checkValue) && $(checkbox).attr("checked", true)
        })
        getCheckBoxValueOne("imageTagInput")
      }
      // 弹出编辑追加套餐
      function onEditSetMeal(item, type) {
        let setMlealItem = JSON.parse(item.getAttribute("data-setMeal-item"))
        // JSON.parse();
        console.log(setMlealItem, "数据")

        $("#edSetMeal_fCode").val(setMlealItem[type])
        $("#edSetMeal_siType").val(setMlealItem.siType)
        $("#edSetMeal_subCode").val(setMlealItem.subCode)
        $("#edSetMeal_qty").val(setMlealItem.qty)
        $("#edSetMeal_minQty").val(setMlealItem.minQty)
        $("#edSetMeal_maxQty").val(setMlealItem.maxQty)
        $("#edSetMeal_seq").val(setMlealItem.seq)
        $("#edSetMeal_lNoDup").prop("checked", setMlealItem.lNoDup == "true")
        // $("#edSetMeal_mymodi").val(setMlealItem.mymodi);
        $("#onEdOrderSubmit").data("idNum", setMlealItem.id)
        if (type === "code") {
          $("#detailModal").modal("hide")
        } else {
          $("#foodModal").modal("hide")
        }
        $("#editSetMealModal").attr("data-bs-whatever", type === "code" ? "Mlist" : "Food")
        $("#editSetMealModal").modal("show")
      }
      // 删除追加套餐
      function onDelSetMeal(id, type) {
        console.log(id, "onDelSetMeal")
        let data = {
          id
        }
        let url = ""
        if (type === "code") {
          url = "../../manager_extraMListSetMeal/deleteOne"
        } else {
          url = "../../manager_extraSetMeal/deleteOne"
        }
        $.post({
          url,
          dataType: "json",
          data,
          success: result => {
            initData(pathList.length)
            if (type === "code") {
              queryAdOeder("code", customDetail.code) //查询追加套餐数据
            } else {
              queryAdOeder("fCode", customfood.fCode) //查询追加套餐数据
            }
            toastr.success("Success")
            console.log(result, "删除追加套餐")
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Error")
          }
        })
        // JSON.parse();
      }
      /**
       * @description:  复选框点击事件
       * @param {string} code 数据唯一code
       * @param {dom} e 按钮dom
       * @param {string} type 数据类型(foodlist/foodType/mlistItem/mTypeItem)
       * @param {number} typeIndex 数据索引
       * @param {number} itemHierarchy 数据所在层数
       * @param {string} mode 触发开关模式
       * @return {*}
       */
      function onTypeCheckbox(code, e, type, typeIndex, itemHierarchy) {
        let typeItem = getTargetItem(typeIndex, itemHierarchy, type)
        let checked = e.checked
        //获取e的data-mode
        let mode = e.getAttribute("data-mode")
        // console.log(pathList.concat([]).reverse(), '看数据');
        let hierarchy = pathList.length
        let { lastType, lastCode } = getLastCode()
        if (mode == "take-away" || mode == "dine-in") {
          let data = {
            currentType: type,
            currentCode: code,
            [mode == "take-away" ? "takeawayTouch" : "dineInTouch"]: checked
          }
          $.post({
            url: `../../manager_itemTouch/updateItemModelTouch`,
            contentType: "application/json", //默认的编码方式
            dataType: "json",
            data: JSON.stringify(data),
            success: function (result) {
              if (result.statusCode == 200) {
                typeItem[mode == "take-away" ? "takeawayTouch" : "dineInTouch"] = checked
                console.log(typeItem, "typeItem")
                toastr.success("The change was successful")
              } else {
                toastr.error("The change was failed")
                initData(hierarchy)
              }
            },
            error: function (error) {
              console.log(error, "错误")
              toastr.error("The change was failed")
              initData(hierarchy)
            }
          })
        } else {
          let data = {
            lastType,
            lastCode,
            currentType: type,
            currentCode: code,
            touch: checked
          }
          console.log("🚀 ~ file: menu.html:1529 ~ onTypeCheckbox ~ data:", data)
          $.post({
            url: `../../manager_itemTouch/updateItemTouch`,
            dataType: "json",
            data,
            success: function (result) {
              // console.log(result, "修改按钮");
              if (result.statusCode == 200) {
                typeItem.finalTouch = checked
                let siblings = $(e)
                  .closest(".switchBox")
                  .find(".switchBox-cell")
                  .not($(e).closest(".switchBox-cell"))
                siblings.each(function () {
                  let siblingMode = $(this).find('input[type="checkbox"]').attr("data-mode")
                  $(this).find('input[type="checkbox"]').prop("disabled", !checked)
                  if (!checked) {
                    $(this).find('input[type="checkbox"]').prop("checked", false)
                  } else {
                    $(this)
                      .find('input[type="checkbox"]')
                      .prop(
                        "checked",
                        siblingMode == "take-away" ? typeItem.takeawayTouch : typeItem.dineInTouch
                      )
                  }
                })

                console.log(typeItem, "typeItem")
                toastr.success("The change was successful")
              } else {
                toastr.error("The change was failed")
                initData(hierarchy)
              }
            },
            error: function (error) {
              console.log(error, "错误")
              toastr.error("The change was failed")
              initData(hierarchy)
            }
          })
        }
      }
      function getLastCode() {
        let lastType = null,
          lastCode = null
        if (pathList && pathList.length > 1) {
          let reverseArray = pathList.concat([]).reverse()
          if (reverseArray[0].type !== "food" && reverseArray[0].type !== "Mlist") {
            //找上一级的listCode
            lastType = reverseArray[1].type
            lastCode = reverseArray[1].id
          } else {
            // 要么是food要么是mlist
            lastType = reverseArray[0].type
            lastCode = reverseArray[0].id
          }

          // 统一传递值
          lastType = lastType == "food" ? "foodlist" : "mlistItem"
        }
        return { lastType, lastCode }
      }
      // 获取home页面是否存在缓存数据
      /**
       * @description:
       * @param  againRequest 是否需要重新请求标识
       * @param  isRequesting 是否已经存在当前请求标识
       *    */
      let againRequest = false,
        isRequesting = false
      function getCacheData() {
        let refreshIcon = $("#refreshIcon", parent.document) //获取父页面的刷新图标
        refreshIcon.addClass("fa-spin") // id为refreshIcon添加class为fa-spin的旋转动画
        return new Promise((resolve, reject) => {
          $.get({
            url: "../../manager_data/getDataStatus",
            success: function (response) {
              resolve()
              let res = $.parseJSON(response)
              if (res.statusCode == 200) {
                //更新缓存成功
                // id为refreshIcon删除class为fa-spin的旋转动画
                if (refreshTimestamp == res.updateTime) {
                  // console.log(refreshTimestamp, res.updateTime, 'refreshTimestamp,res.updateTime');
                  // 调用父页面开启缓存提示
                  window.parent.onCloseRefreshData()
                } else {
                  window.parent.onOpenRefreshData()
                }
              } else {
                console.log("缓存状态获取接口错误")
              }
            },
            error: function (data) {
              reject()
              console.log("缓存状态获取接口错误")
            },
            complete: function () {
              refreshIcon.removeClass("fa-spin") // id为refreshIcon删除class为fa-spin的旋转动画
            }
          })
        })
      }
      // 校验重复请求缓存数据接口
      function checkRepeatRequest() {
        if (isRequesting) {
          againRequest = true //重新请求标识
          console.log(`存在重复请求，直接返回`)
        } else {
          isRequesting = true //存在当前请求标识
          getCacheData()
            .then(res => {
              // id为refreshIcon删除class为fa-spin的旋转动画
              if (againRequest) {
                againRequest = false
                getCacheData()
              }
            })
            .catch(err => {})
            .finally(() => {
              isRequesting = false //取消正在请求标识
            })
        }
      }
    </script>
  </head>

  <body>
    <div id="app">
      <div style="position: relative; z-index: 999; width: 100%">
        <ul id="menu" class="nav nav-tabs" style="margin-bottom: 3px">
          <li id="menu_foodType" onclick="createFoodTypeElm()">
            <a>Menu Category</a>
          </li>
          <li id="menu_food" onclick="createFoodElm(pathList[0].index,pathList[0].id)">
            <a>Menu Items</a>
          </li>
          <li id="Food_detail" onclick="createSetMealElm(pathList[1].index,pathList[1].id)">
            <a>Combos & Modifiers</a>
          </li>
          <li id="Food_Indetail" onclick="createSiElm(pathList[2].index,pathList[2].id)">
            <a>Modifier Options</a>
          </li>
          <li id="Specific_detail" onclick="createWuElm(pathList[3].index,pathList[3].id)">
            <a>4th level Options</a>
          </li>
          <li id="Specific_Indetail" onclick="createLiuElm(pathList[4].index,pathList[4].id)">
            <a>5th level Options</a>
          </li>
          <li id="Specific_Indetail_six" onclick="createQiElm(pathList[5].index,pathList[5].id)">
            <a>6th level Options</a>
          </li>
          <li id="Specific_Indetail_seven" onclick="createBaElm(pathList[6].index,pathList[6].id)">
            <a>7th level Options</a>
          </li>
          <li id="Specific_Indetail_eight" onclick="createJiuElm(pathList[7].index,pathList[7].id)">
            <a>8th level Options</a>
          </li>
        </ul>
        <span
          class="layui-breadcrumb"
          id="breadcrumb"
          style="
            user-select: none;
            position: relative;
            margin-left: 18px;
            z-index: 10000000000;
            font-size: 14px;
          "
        ></span>
      </div>
      <div id="body_warp">
        <div class="process">
          <img id="loading" src="../../static/img/svg/loading1.svg" />
          <div class="_load">
            Network speed:
            <span class="pro">0.00</span>
            kb/s
          </div>
          <div class="_load">
            Data transmitted:
            <span class="sumData">0.00</span>
            kb
          </div>
        </div>
      </div>
      <!-- foodTypeModal -->
      <div
        class="modal fade"
        id="foodTypeModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="foodTypeLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <div class="pos-base-info">
                <p>POS Info</p>
                <p>
                  Screen Layout Group:
                  <strong></strong>
                </p>
                <p>
                  Name #1:
                  <strong></strong>
                </p>
                <p>
                  Name #2:
                  <strong></strong>
                </p>
              </div>
              <form>
                <!-- Display -->
                <div class="form-type-item">
                  <div class="type-title">
                    <strong>Display</strong>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title" for="fTypeNameA">
                      Override Name 1
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="fTypeNameA" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title" for="fTypeNameB">
                      Override Name 2
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="fTypeNameB" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title" for="fTypeMulti1">
                      Name 3
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="fTypeMulti1" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="columnSelect"
                    >
                      Items per row
                    </label>
                    <div class="col-sm-9">
                      <select class="form-control form-select" id="columnSelect">
                        <option>0</option>
                        <option>1</option>
                        <option>2</option>
                        <option>3</option>
                        <option>6</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      If Sold Out
                    </label>
                    <div class="col-sm-9">
                      <form id="itemCtrlModelSelect">
                        <input type="radio" name="itemCtrl" id="show_sold_out" value="0" />
                        <label for="show_sold_out">Display 'Sold Out' overlay</label>
                        <input
                          style="margin-left: 30px"
                          type="radio"
                          id="hidden"
                          name="itemCtrl"
                          value="1"
                        />
                        <label for="hidden">Hidden</label>
                      </form>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title" for="typeSelect">
                      Type Name:
                    </label>
                    <div class="col-sm-9">
                      <select id="typeSelect" class="form-select form-control"></select>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">Banner:</label>
                    <div class="col-sm-9">
                      <div class="upload-container" id="upload-container"></div>
                    </div>
                  </div>
                </div>
                <!-- When use as modifier or within combo -->
                <div class="form-type-item">
                  <div class="type-title">
                    <strong>When use as modifier or within combo</strong>
                  </div>

                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="foodColumnSelect"
                    >
                      Items per row
                    </label>
                    <div class="col-sm-9">
                      <select class="form-control form-select" id="foodColumnSelect">
                        <option>1</option>
                        <option>2</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Display visual
                    </label>
                    <div class="col-sm-9 switchWarp">
                      <label class="switch">
                        <input type="checkbox" id="foodShowImg" />
                        <div class="slider round"></div>
                      </label>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">Individual</label>
                    <div class="col-sm-9 switchWarp">
                      <label class="switch">
                        <input type="checkbox" id="individual_order" />
                        <div class="slider round"></div>
                      </label>
                      <span style="text-indent: 1em">Send to POS as individual item(s)</span>
                    </div>
                  </div>
                </div>
                <!-- Availability -->
                <div class="form-type-item">
                  <div class="type-title">
                    <strong>Availability</strong>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="use_date_type"
                    >
                      Date
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="use_date_type"
                        placeholder="Format : yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="use_time_type"
                    >
                      Time
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="use_time_type"
                        placeholder="Format : hh:mm-hh:mm  (Use ; to separate multiple times)"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="use_dow_type"
                    >
                      DOW
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="use_dow_type"
                        placeholder="Format : 1234567H  (1=Monday,7=Sunday,H=holidays)"
                      />
                    </div>
                  </div>
                </div>

                <!-- Additional Controls -->
                <div class="form-type-item">
                  <div class="type-title">
                    <strong>Additional Controls</strong>
                  </div>

                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Show this S/L Group
                      <span>only if any of these items have been ordered</span>
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        aria-label="With textarea"
                        id="fTypeshowInFCodes"
                        placeholder="Input POS food code (use ; to separate multiple codes)"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Hide this S/L Group
                      <span>if any of these items hava been ordered</span>
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        aria-label="With textarea"
                        id="fTypeNotShowInFCodes"
                        placeholder="Input POS food code (use ; to separate multiple codes)"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title" for="addFCode">
                      Add item(s) to group
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="addFCode"
                        placeholder="Input POS food code (use ; to separate multiple codes)"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title" for="hiddenFCode">
                      Hide item(s) form group
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="hiddenFCode"
                        placeholder="Input POS food code (use ; to separate multiple codes)"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title" for="mergeFty">
                      Combine other groups
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="mergeFty"
                        placeholder="Input POS S/L code (use ; to separate multiple codes)"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="hotSaleFTCodes"
                    >
                      Suggestive Selling
                      <span>
                        Recommend products from these groups if any product from this group is in
                        the shopping cart
                      </span>
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        placeholder="Input POS S/L code (use ; to separate multiple codes)"
                        aria-label="With textarea"
                        id="hotSaleFTCodes"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="onfTypeSubmit">Submit</button>
            </div>
          </div>
        </div>
      </div>
      <!-- foodModal -->
      <div
        class="modal fade"
        id="foodModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="foodModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <div class="pos-base-info">
                <p>POS Info</p>
                <p>
                  Food Code:
                  <strong></strong>
                </p>
                <p>
                  Name #1:
                  <strong></strong>
                </p>
                <p>
                  Name #2:
                  <strong></strong>
                </p>
              </div>
              <form>
                <div class="form-type-item">
                  <div class="type-title">
                    <strong>Display</strong>
                  </div>
                  <div class="form-group row">
                    <label for="nameA" class="text-start col-sm-3 col-form-label label_title">
                      Override Name 1
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="nameA" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="nameB" class="text-start col-sm-3 col-form-label label_title">
                      Override Name 2
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="nameB" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="foodMulti1" class="text-start col-sm-3 col-form-label label_title">
                      Name 3
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="foodMulti1" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Introduction 1:
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        aria-label="With textarea"
                        id="prodTextareaA"
                        placeholder="Supports HTML encoding and emojis"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Introduction 2:
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        aria-label="With textarea"
                        id="prodTextareaB"
                        placeholder="Supports HTML encoding and emojis"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                      <!-- <textarea ></textarea> -->
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Introduction 3:
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        aria-label="With textarea"
                        id="prodTextareaC"
                        placeholder="Supports HTML encoding and emojis"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                      <!-- <textarea ></textarea> -->
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">Image Tags</label>
                    <div class="col-sm-9" id="imageTagList"></div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="imageTagAzimuth"
                    >
                      Tag location
                    </label>
                    <div class="col-sm-9">
                      <select class="form-control" id="imageTagAzimuth">
                        <option value="topLeft">Top Left</option>
                        <option value="topRight">Top Right</option>
                        <option value="bottomLeft">Bottom Left</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="form-type-item">
                  <div class="type-title">
                    <strong>Availability</strong>
                  </div>
                  <div class="form-group row">
                    <label
                      for="use_date_food"
                      class="text-start col-sm-3 col-form-label label_title"
                    >
                      Date
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="use_date_food"
                        placeholder="Format ： yyyy.mm.dd-yyyy.mm.dd  (Use ; to separate multiple dates)"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      for="use_time_food"
                      class="text-start col-sm-3 col-form-label label_title"
                    >
                      Time
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="use_time_food"
                        placeholder="Format ：hh:mm-hh:mm  (Use ; to separate multiple times)"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      for="use_dow_food"
                      class="text-start col-sm-3 col-form-label label_title"
                    >
                      DOW
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="use_dow_food"
                        placeholder="Format ：1-7H  (1=Monday,7=Sunday,H=holidays)"
                      />
                    </div>
                  </div>
                </div>
                <div class="form-type-item">
                  <div class="type-title">
                    <strong>Additional Controls</strong>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Show this Product
                      <span>only if any of these items have been ordered</span>
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        aria-label="With textarea"
                        id="show_In_FCodes"
                        placeholder="Input POS food code (use ; to separate multiple codes)"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Hide this product
                      <span>if any of these items have been ordered</span>
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        aria-label="With textarea"
                        id="foodNotshowInFCodes"
                        placeholder="Input POS food code (use ; to separate multiple codes)"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-3 col-form-label label_title"
                      for="fPackingBoxMListCode"
                    >
                      Takeaway Packaging
                      <span>for automatic inclusion in takeaway orders</span>
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="text"
                        class="form-control"
                        id="fPackingBoxMListCode"
                        placeholder="input POS modifier code"
                      />
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="minQty2" class="text-start col-sm-3 col-form-label label_title">
                      Minimum Qty
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="minQty2" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="maxQty2" class="text-start col-sm-3 col-form-label label_title">
                      Maximum Qty
                    </label>
                    <div class="col-sm-9">
                      <input type="text" class="form-control" id="maxQty2" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="takeUpQty" class="text-start col-sm-3 col-form-label label_title">
                      take up Qty
                      <span>
                        Example: a combo comes with 2 drinks, choosing this item is equivalent to
                        selecting 2 drinks when set to 2
                      </span>
                    </label>
                    <div class="col-sm-9">
                      <input type="number" min="0" class="form-control" id="takeUpQty" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      for="maxSingleOrderCount"
                      class="text-start col-sm-3 col-form-label label_title"
                    >
                      Max Single Order Count
                      <span>Limit the maximum order quantity for main food items</span>
                    </label>
                    <div class="col-sm-9">
                      <input type="number" min="0" class="form-control" id="maxSingleOrderCount" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      for="maxSingleQuantityToCart"
                      class="text-start col-sm-3 col-form-label label_title"
                    >
                      Max single quantity to cart
                      <span>Limit the maximum number of add-to-cart items</span>
                    </label>
                    <div class="col-sm-9">
                      <input
                        type="number"
                        min="0"
                        class="form-control"
                        id="maxSingleQuantityToCart"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Suggestive Selling
                      <span>Recommend these products if this item is in the shopping cart</span>
                    </label>
                    <div class="col-sm-9">
                      <textarea
                        class="form_textarea form-control"
                        placeholder="Input POS food code (use ; to separate multiple codes)"
                        aria-label="With textarea"
                        id="hotSaleFCodes"
                        style="max-width: 100%; min-width: 100%"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Big Picture Display
                    </label>
                    <div class="col-sm-9 switchWarp">
                      <label class="switch">
                        <input type="checkbox" id="bigPictureDisplay" />
                        <div class="slider round"></div>
                      </label>
                    </div>
                  </div>
                  <!-- 关闭后作为固定子项时过期不影响上级 -->
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Bans up-level when expired
                    </label>
                    <div class="col-sm-9 switchWarp">
                      <label class="switch">
                        <input type="checkbox" id="FexpiredBanSuperior" />
                        <div class="slider round"></div>
                      </label>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Set Meal Item
                    </label>
                    <div class="col-sm-9 adOrderWarp" id="additionalFood"></div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-3 col-form-label label_title">
                      Allergen icons
                    </label>
                    <div class="col-sm-9" id="foodAllergen"></div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <!-- 新增套餐按钮 -->
              <button
                type="button"
                class="btn btn-info"
                id="onAdSetMeal"
                data-bs-toggle="modal"
                data-bs-target="#adSetMealModal"
                data-bs-whatever="Food"
              >
                Add Set Meal
              </button>
              <button type="button" class="btn btn-primary" id="onfoodSubmit">Submit</button>
            </div>
          </div>
        </div>
      </div>
      <!-- 细项Modal 包括mtype和mlist -->
      <div
        class="modal fade"
        id="detailModal"
        role="dialog"
        aria-labelledby="detailLabel"
        aria-hidden="true"
        tabindex="-1"
      >
        <div class="modal-dialog modal-xl modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <div class="pos-base-info">
                <p>POS Info</p>
                <p>
                  Modifier Group:
                  <strong></strong>
                </p>
                <p>
                  Name #1:
                  <strong></strong>
                </p>
                <p>
                  Name #2:
                  <strong></strong>
                </p>
              </div>
              <form>
                <div class="form-type-item">
                  <div class="type-title">
                    <strong></strong>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title" for="detailNameA">
                      Override Name 1
                    </label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control" id="detailNameA" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title" for="detailNameB">
                      Override Name 2
                    </label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control" id="detailNameB" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-4 col-form-label label_title"
                      for="detailMulti1"
                    >
                      Name 3
                    </label>
                    <div class="col-sm-8">
                      <input type="text" class="form-control" id="detailMulti1" />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-4 col-form-label label_title"
                      for="mlistColumnSelect"
                    >
                      Items per row
                    </label>
                    <div class="col-sm-8">
                      <select class="form-control form-select" id="mlistColumnSelect">
                        <option>1</option>
                        <option>2</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row" id="mlistItemCtrlModelSelect">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      If Sold Out
                    </label>
                    <div class="col-sm-8">
                      <form>
                        <input
                          type="radio"
                          name="showMlistItemCtrl"
                          id="mlist_show_itemCtrl"
                          value="0"
                        />
                        <label for="mlist_show_itemCtrl">Display 'Sold Out' overlay</label>
                        <input
                          style="margin-left: 30px"
                          type="radio"
                          id="mlist_hidden_itemCtrl"
                          name="showMlistItemCtrl"
                          value="1"
                        />
                        <label for="mlist_hidden_itemCtrl">Hidden</label>
                      </form>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      Display visual
                    </label>
                    <div class="col-sm-8 switchWarp">
                      <label class="switch" style="float: left">
                        <input type="checkbox" id="mlistShowImg" />
                        <div class="slider round"></div>
                      </label>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label
                      class="text-start col-sm-4 col-form-label label_title"
                      for="mPackingBoxMListCode"
                    >
                      Takeaway Packaging
                      <span>for automatic inclusion in takeaway orders</span>
                    </label>
                    <div class="col-sm-8">
                      <input
                        type="text"
                        class="form-control"
                        id="mPackingBoxMListCode"
                        placeholder="Input POS modifier code"
                      />
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      Bans up-level when expired
                    </label>
                    <div class="col-sm-8 switchWarp">
                      <label class="switch">
                        <input type="checkbox" id="MexpiredBanSuperior" />
                        <div class="slider round"></div>
                      </label>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="text-start col-sm-4 col-form-label label_title">
                      Set Meal Item
                    </label>
                    <div class="col-sm-8 adOrderWarp" id="additionalMlist"></div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <!-- Mlist新增套餐按钮 -->
              <button
                type="button"
                class="btn btn-info"
                id="onAdSetMealMlist"
                data-bs-toggle="modal"
                data-bs-target="#adSetMealModal"
                data-bs-whatever="Mlist"
              >
                Add Set Meal
              </button>
              <button type="button" class="btn btn-primary" id="ondetailSubmit">Submit</button>
            </div>
          </div>
        </div>
      </div>
      <!-- 追加套餐添加Modal -->
      <div
        class="modal fade"
        id="adSetMealModal"
        role="dialog"
        aria-labelledby="addOrderModalLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-xl" role="document" style="top: 15%">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Add Set Meal</h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <form id="adOrderForm">
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="adSetMeal_siType"
                  >
                    siType
                  </label>
                  <div class="col-sm-9">
                    <select class="form-control form-select" id="adSetMeal_siType">
                      <option value="">Please select</option>
                      <option value="1">Food</option>
                      <option value="2">FoodType</option>
                      <option value="3">MList</option>
                      <option value="4">MType</option>
                    </select>
                  </div>
                </div>
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="adSetMeal_subCode"
                  >
                    subCode
                  </label>
                  <div class="col-sm-9">
                    <input type="text" class="form-control" id="adSetMeal_subCode" name="subCode" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title" for="adSetMeal_qty">
                    Basic Qty
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_qty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="adSetMeal_minQty"
                  >
                    Minimum Qty
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_minQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="adSetMeal_maxQty"
                  >
                    maximum Qty
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_maxQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title" for="adSetMeal_seq">
                    Seq
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="adSetMeal_seq" />
                  </div>
                </div>
                <!--               不可重复 lNoDup -->
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">No Dup:</label>
                  <div class="col-sm-9 switchWarp">
                    <label class="switch" style="float: left">
                      <input type="checkbox" id="adSetMeal_lNoDup" />
                      <div class="slider round"></div>
                    </label>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="onAdSetMealSub">
                Save changes
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- 追加套餐编辑Modal -->
      <div
        class="modal fade"
        id="editSetMealModal"
        role="dialog"
        aria-labelledby="editSetMealLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-xl" role="document" style="top: 15%">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Edit Set Meal</h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body">
              <form id="editOrderForm">
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="edSetMeal_siType"
                  >
                    siType
                  </label>
                  <div class="col-sm-9">
                    <select class="form-control form-select" id="edSetMeal_siType">
                      <option value="1">Food</option>
                      <option value="2">FoodType</option>
                      <option value="3">MList</option>
                      <option value="4">MType</option>
                    </select>
                  </div>
                </div>
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="edSetMeal_subCode"
                  >
                    subCode
                  </label>
                  <div class="col-sm-9">
                    <input type="text" class="form-control" id="edSetMeal_subCode" name="subCode" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title" for="edSetMeal_qty">
                    Basic Qty
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_qty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="edSetMeal_minQty"
                  >
                    Minimum Qty
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_minQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label
                    class="text-start col-sm-3 col-form-label label_title"
                    for="edSetMeal_maxQty"
                  >
                    Maximum Qty
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_maxQty" />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title" for="edSetMeal_seq">
                    Seq
                  </label>
                  <div class="col-sm-9">
                    <input type="number" class="form-control" id="edSetMeal_seq" />
                  </div>
                </div>
                <!--               不可重复 lNoDup -->
                <div class="form-group row">
                  <label class="text-start col-sm-3 col-form-label label_title">No Dup</label>
                  <div class="col-sm-9 switchWarp">
                    <label class="switch" style="float: left">
                      <input type="checkbox" id="edSetMeal_lNoDup" />
                      <div class="slider round"></div>
                    </label>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="onEdOrderSubmit">
                Save changes
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- 搜索food/mlist功能弹窗 -->
      <div
        class="modal fade"
        id="searchListModal"
        role="dialog"
        aria-labelledby=""
        aria-hidden="true"
        tabindex="-1"
      >
        <div class="modal-dialog modal-xl modal-dialog-centered">
          <div class="modal-content searchListContent">
            <div class="modal-body">
              <!-- 搜索框 -->
              <form onsubmit="return false">
                <div class="form-group row">
                  <div class="input-group flex-nowrap searchListWarp input-group-lg">
                    <span class="input-group-text" id="inputGroup-sizing-lg">
                      <!-- <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                      class="bi bi-search" viewBox="0 0 16 16">
                      <path
                        d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z" />
                    </svg> -->
                      <label class="DocSearch-MagnifierLabel" id="docsearch-label">
                        <svg
                          width="20"
                          height="20"
                          class="DocSearch-Search-Icon"
                          viewBox="0 0 20 20"
                          stroke-opacity=".5"
                        >
                          <path
                            d=" M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533
                        0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418
                        2.9419 7.7115 0 10.6533z"
                            stroke="currentColor"
                            fill="none"
                            fill-rule="evenodd"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          ></path>
                        </svg>
                        <svg
                          class="DocSearch-Loading-Icon"
                          viewBox="0 0 38 38"
                          stroke="currentColor"
                          stroke-opacity=".5"
                        >
                          <g fill="none" fill-rule="evenodd">
                            <g transform="translate(1 1)" stroke-width="2">
                              <circle stroke-opacity=".3" cx="18" cy="18" r="18"></circle>
                              <path
                                d="M36 18c0-9.94-8.06-18-18-18"
                                transform="rotate(354.267 18 18)"
                              >
                                <animateTransform
                                  attributeName="transform"
                                  type="rotate"
                                  from="0 18 18"
                                  to="360 18 18"
                                  dur="1s"
                                  repeatCount="indefinite"
                                ></animateTransform>
                              </path>
                            </g>
                          </g>
                        </svg>
                      </label>
                      <div class="searchList-LoadingIndicator">
                        <svg viewBox="0 0 38 38" stroke="currentColor" stroke-opacity=".5">
                          <g fill="none" fill-rule="evenodd">
                            <g transform="translate(1 1)" stroke-width="2">
                              <circle stroke-opacity=".3" cx="18" cy="18" r="18"></circle>
                              <path d="M36 18c0-9.94-8.06-18-18-18">
                                <animateTransform
                                  attributeName="transform"
                                  type="rotate"
                                  from="0 18 18"
                                  to="360 18 18"
                                  dur="1s"
                                  repeatCount="indefinite"
                                ></animateTransform>
                              </path>
                            </g>
                          </g>
                        </svg>
                      </div>
                    </span>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Search Food Data"
                      aria-label=""
                      id="searchListInput"
                      aria-describedby="inputGroup-sizing-lg"
                      autofocus="true"
                      spellcheck="false"
                    />
                  </div>
                </div>
              </form>
              <!-- 展示搜索结果 -->
              <div class="listSearch-StartScreen">
                <ul class="searchList-Data"></ul>
                <p class="listSearch-Help">No recent searches</p>
              </div>
            </div>
            <div class="modal-footer">
              <ul class="searchList-Commands">
                <li>
                  <kbd class="searchList-Commands-Key">
                    <svg width="15" height="15" aria-label="Enter key" role="img">
                      <g
                        fill="none"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.2"
                      >
                        <path d="M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"></path>
                      </g>
                    </svg>
                  </kbd>
                  <span class="searchList-Label">to select</span>
                </li>

                <li>
                  <kbd class="searchList-Commands-Key">
                    <svg width="15" height="15" aria-label="Escape key" role="img">
                      <g
                        fill="none"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.2"
                      >
                        <path
                          d="M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"
                        ></path>
                      </g>
                    </svg>
                  </kbd>
                  <span class="searchList-Label">to close</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 遮罩 -->
      <div id="cover"></div>
    </div>
  </body>

  <script src="../../static/cmsUtils/cmsSearchList/searchList.js"></script>

  <script>
    // 追加套餐嵌套弹窗功能逻辑
    $(document).ready(function () {
      //第二层模态框弹出时，为其设置一个大于第一层模态框的z-index
      //使得第二层模态框可以在第一层模态框上面
      nestMask("adSetMealModal")
      nestMask("editSetMealModal")
    })
    // 上传fty-banner逻辑

    function uploadInput(code, element, typeName) {
      // e.target.files[0]
      const file = $(element)[0].files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = function (e) {
          // $("#upload-picture-card").attr("src", e.target.result)
          // $("#upload-box").addClass("uploadActive")
        }
        reader.readAsDataURL(file)
        // 模拟上传
        upload(code, element, "foodType", "ftyBanner")
        // simulateUpload(file)
      }
    }
    function uploadDelImg(e, dom) {
      e.stopPropagation() // 阻止事件冒泡
      var input = $(dom).parent().parent().parent().find('input[type="file"]')
      var uploadBox = $(dom).parent().parent()
      var imgDom = $(dom).parent().parent().find("img[id='upload-picture-card']")
      input.val("")
      uploadBox.removeClass("uploadActive")
      imgDom.attr("src", "")
      let params = $(dom).data("params")
      delUploadImg(params, "ftyBanner")
    }
    function uploadIcon(dom) {
      var input = $(dom).parent().parent().parent().find('input[type="file"]')
      if (input.length > 0) {
        input.click()
      }
    }
    //删除上传的图片
    function delUploadImg(params, uploadType) {
      let { code, typeName, photoSuffix } = params
      let versionNumber = sessionStorage.getItem("versionNumber") || ""
      var fd = new FormData()
      fd.append("domain", domain)
      fd.append("code", code)
      fd.append("typeName", typeName)
      fd.append("photoSuffix", photoSuffix)
      if (versionNumber != "PROD" && versionNumber != "") {
        fd.append("versionNumber", versionNumber)
      }
      if (uploadType == "ftyBanner") {
        fd.append("extraPaths", "banner/")
      }
      $.post({
        url: "../../manager_photo/deleteByCode",
        dataType: "json",
        processData: false,
        contentType: false,
        cache: false,
        data: fd,
        success: result => {
          if (result.statusCode == 200) {
            toastr.success("The deletion is successful")
          } else if (result.statusCode == 4033) {
          } else {
            toastr.error("Deletion failed")
          }
        },
        error: function (data) {
          toastr.error("Deletion failed")
        }
      })
    }
    // 多个嵌套弹窗遮罩层问题
    function nestMask(idName) {
      $(document).on("show.bs.modal", `#${idName}`, function (event) {
        var zIndex = 1040 + (10 * $(".modal:visible").length + 1)
        $(this).css("z-index", zIndex)
        //开启遮罩，遮罩的高度小于第二层模态框
        $("#cover").css("z-index", zIndex - 1)
        $("#cover").css("display", "block") //显示遮罩层
        let button = event.relatedTarget
        //button  data-bs-whatever 标识 区分是Food 或为 Mlist
        let recipient =
          button?.getAttribute("data-bs-whatever") || event.target.getAttribute("data-bs-whatever")
        //recipient 若存在,则为点击addsetmeal按钮弹出的add\edit modal
        if (recipient) {
          //  给该弹窗增加type(food\Mlist) 标识
          event.target.dialogType = recipient
          if (recipient === "Mlist") {
            //  移除 siType 下的 food  foodType options
            let sel =
              event.target.querySelector("#adSetMeal_siType") ||
              event.target.querySelector("#edSetMeal_siType")
            sel &&
              Array.from(sel.children).map(child => {
                return (child.value === "1" || child.value === "2") && sel.removeChild(child)
              })
          } else {
            //  添加 siType 下的 food  foodType options
            let sel =
              event.target.querySelector("#adSetMeal_siType") ||
              event.target.querySelector("#edSetMeal_siType")
            // 如果 sel的子元素中没有foodType,则添加到please select后面||第一个
            if (
              sel &&
              !Array.from(sel.children).some(child => child.value === "1" || child.value === "2")
            ) {
              let foodType = document.createElement("option")
              foodType.value = "1"
              foodType.innerText = "Food"
              let food = document.createElement("option")
              food.value = "2"
              food.innerText = "FoodType"
              // 判断是否有please select
              let pleaseSelect = Array.from(sel.children).find(child => child.value === "")
              if (pleaseSelect) {
                sel.insertBefore(food, pleaseSelect.nextSibling)
                sel.insertBefore(foodType, pleaseSelect.nextSibling)
              } else {
                sel.insertBefore(food, sel.children[0])
                sel.insertBefore(foodType, sel.children[1])
              }
            }
          }
        }
      })

      $(`#${idName}`).on("hide.bs.modal", function () {
        //第二层模态框关闭时，关闭遮罩
        $("#cover").css("display", "none")
      })
    }
    function versionTag() {
      let version = sessionStorage.getItem("versionNumber")
      switch (version) {
        case "0":
          return "UAT"
        case "PROD":
          return "PROD"
        default:
          let date = moment(parseInt(version)).format("YYYY-MM-DD")
          let time = moment(parseInt(version)).format("HH:mm:ss")
          if (date !== "Invalid date" && time !== "Invalid date") {
            return `${date} ${time}`
          } else {
            return "HISTORY"
          }
      }
    }

    window.onload = async function () {
      let domain = sessionStorage.getItem("domain")
      await getUIConfig(domain)
      await initData("init")
      // 获取版本信息,设置边框
      let version = sessionStorage.getItem("versionNumber")
      if (version !== "PROD") {
        $("#body_warp").addClass("border")
        $("#body_warp.border").attr("version", versionTag())
      }
      // 模态框展示
      $("#onSubmit").click(function () {
        var value = $("#TextArea1").val()
        console.log(value)
      })
      // foodtype确定
      $("#onfTypeSubmit").click(function () {
        var fTypeNameA = $("#fTypeNameA").val()
        var fTypeNameB = $("#fTypeNameB").val()
        var multi1 = $("#fTypeMulti1").val()
        var typeName = $("#typeSelect option:selected").val()
        var columnName = $("#columnSelect option:selected").val() // 点餐页弹性栏目
        var foodColumnName = $("#foodColumnSelect option:selected").val() // 点餐页弹性栏目
        var item_ctrl_model = $("input[name='itemCtrl']:checked").val() //itemCtrl 显示模式
        var hotSaleFTCodes = $("#hotSaleFTCodes").val()
        var fTypeshowInFCodes = $("#fTypeshowInFCodes").val()
        var fTypeNotShowInFCodes = $("#fTypeNotShowInFCodes").val()
        var foodShowImg = $("#foodShowImg").prop("checked") // 详情页下foolistItem是否显示缩略图
        var individual_order = $("#individual_order").prop("checked")
        var merge_fCode = $("#mergeFty").val() //需要合并的fcodes
        var addFCode = $("#addFCode").val() //需要自定义添加到fty的food
        var hiddenFCode = $("#hiddenFCode").val() //需要自定义添加到fty的food
        var use_date_type = $("#use_date_type").val()
        var use_time_type = $("#use_time_type").val()
        var use_dow_type = $("#use_dow_type").val()
        var regex = new RegExp("^[a-zA-Z0-9;]*$")
        // let isInFCodesTxt = regex.test(fTypeshowInFCodes);
        let isNotInFCodesTxt = regex.test(fTypeNotShowInFCodes)
        if (!isNotInFCodesTxt) {
          toastr.error("Error")
          return
        }
        console.log(item_ctrl_model, "item_ctrl_model")
        var foodType = {
          fType_nameA: fTypeNameA,
          fType_nameB: fTypeNameB,
          multi1,
          item_ctrl_model,
          code: customfoodType.code,
          type_restrict_name: typeName,
          display_column: columnName,
          food_display_column: foodColumnName,
          show_In_FCodes: fTypeshowInFCodes,
          hotSaleFTCodes,
          not_Show_With_FCodes: fTypeNotShowInFCodes,
          food_show_src: foodShowImg,
          individual_order,
          combine_with_foodType: merge_fCode,
          addFCode,
          hiddenFCode,
          use_date2: use_date_type,
          use_time2: use_time_type,
          use_dow2: use_dow_type
        }
        // console.log();
        $.post({
          url: "../../manager_foodType/updateOne",
          dataType: "json",
          data: foodType,
          success: function (result) {
            // 手动改变源数据，成功后需回显最新
            if (result.success) {
              console.log(result, "成功提交foodtype修改")
              toastr.success("Success")
              customfoodType.fType_nameA = fTypeNameA
              customfoodType.fType_nameB = fTypeNameB
              customfoodType.multi1 = multi1
              customfoodType.type_restrict_name = typeName
              customfoodType.display_column = columnName
              customfoodType.show_In_FCodes = fTypeshowInFCodes
              customfoodType.hotSaleFTCodes = hotSaleFTCodes
              customfoodType.not_Show_With_FCodes = fTypeNotShowInFCodes
              customfoodType.food_display_column = foodColumnName
              customfoodType.item_ctrl_model = item_ctrl_model
              customfoodType.food_show_src = foodShowImg
              customfoodType.individual_order = individual_order
              customfoodType.combine_with_foodType = merge_fCode
              customfoodType.addFCode = addFCode
              customfoodType.hiddenFCode = hiddenFCode
              customfoodType.use_date2 = use_date_type
              customfoodType.use_time2 = use_time_type
              customfoodType.use_dow2 = use_dow_type
              $("#foodTypeModal").modal("hide")
              console.log(customfoodType, "全局customfoodType")
            } else {
              toastr.error("Error")
            }
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Error")
          }
        })
      })
      // food弹窗q确定
      $("#onfoodSubmit").click(function () {
        var nameA = $("#nameA").val()
        var nameB = $("#nameB").val()
        var multi1 = $("#foodMulti1").val()
        var prod_textareaA = $("#prodTextareaA").val()
        var prod_textareaB = $("#prodTextareaB").val()
        var prod_textareaC = $("#prodTextareaC").val()
        var show_In_FCodes = $("#show_In_FCodes").val()
        var foodNotshowInFCodes = $("#foodNotshowInFCodes").val()
        var hotSaleFCodes = $("#hotSaleFCodes").val()
        var minQty2 = $("#minQty2").val()
        var maxQty2 = $("#maxQty2").val()
        var use_date_food = $("#use_date_food").val()
        var use_time_food = $("#use_time_food").val()
        var use_dow_food = $("#use_dow_food").val()
        var takeUpQty = $("#takeUpQty").val() || 1
        var maxSingleOrderCount = $("#maxSingleOrderCount").val()
        var maxSingleQuantityToCart = $("#maxSingleQuantityToCart").val()
        var bigPictureDisplay = $("#bigPictureDisplay").prop("checked")
        var expiredBanSuperior = $("#FexpiredBanSuperior").prop("checked")
        var regex = new RegExp("^[a-zA-Z0-9;]*$")
        var imageTagAzimuth = $("#imageTagAzimuth").val() || "topRight"
        var packingBoxMListCode = $("#fPackingBoxMListCode").val()
        // let isInFCodesTxt = regex.test(show_In_FCodes);
        let isNotInFCodesTxt = regex.test(foodNotshowInFCodes)
        let isNotHotSaleFCodesTxt = regex.test(hotSaleFCodes)
        if (!isNotInFCodesTxt || !isNotHotSaleFCodesTxt) {
          toastr.error("Error")
          return
        }
        var food = {
          nameA,
          nameB,
          multi1,
          prod_textareaA,
          prod_textareaB,
          prod_textareaC,
          fCode: customfood.fCode,
          show_In_FCodes: show_In_FCodes,
          not_Show_With_FCodes: foodNotshowInFCodes,
          hotSaleFCodes,
          allergen_icons: foodAllergenData,
          imageTagName: foodAllImageTagData,
          imageTagAzimuth,
          minQty2,
          maxQty2,
          use_date2: use_date_food,
          use_time2: use_time_food,
          use_dow2: use_dow_food,
          takeUpQty: takeUpQty,
          maxSingleOrderCount,
          maxSingleQuantityToCart,
          bigPictureDisplay,
          packingBoxMListCode,
          expiredBanSuperior
        }
        $.post({
          url: "../../manager_food/updateOne",
          dataType: "json",
          data: food,
          success: function (result) {
            if (result.success) {
              toastr.success("Success")
              customfood.nameA = nameA
              customfood.nameB = nameB
              customfood.multi1 = multi1
              customfood.prod_textareaA = prod_textareaA
              customfood.prod_textareaB = prod_textareaB
              customfood.prod_textareaC = prod_textareaC
              customfood.show_In_FCodes = show_In_FCodes
              customfood.not_Show_With_FCodes = foodNotshowInFCodes
              customfood.hotSaleFCodes = hotSaleFCodes
              customfood.allergen_icons = foodAllergenData
              customfood.minQty2 = minQty2
              customfood.maxQty2 = maxQty2
              customfood.use_date2 = use_date_food
              customfood.use_time2 = use_time_food
              customfood.use_dow2 = use_dow_food
              customfood.takeUpQty = takeUpQty
              customfood.maxSingleOrderCount = maxSingleOrderCount
              customfood.maxSingleQuantityToCart = maxSingleQuantityToCart
              customfood.imageTagName = foodAllImageTagData
              customfood.imageTagAzimuth = imageTagAzimuth
              customfood.bigPictureDisplay = bigPictureDisplay
              customfood.expiredBanSuperior = expiredBanSuperior
              customfood.packingBoxMListCode = packingBoxMListCode
              $("#foodModal").modal("hide")
            } else {
              toastr.error("Error")
            }
          },
          error: function (error) {
            console.log(error, "错误")
            toastr.error("Error")
          }
        })
      })
      // 弹出添加追加套餐
      // $("#onAdSetMeal").click(function () {
      //   $("#adSetMealModal").modal("show");
      // });

      // 追加套餐t添加弹窗确定
      $("#onAdSetMealSub").click(function () {
        console.log(customfood, "点击的对象")
        let sitype = $("#adSetMeal_siType option:selected").val()
        let subCode = $("#adSetMeal_subCode").val()
        console.log(sitype, subCode)
        let ModalType = document.querySelector("#adSetMealModal").dialogType
        if (!sitype || !subCode) {
          toastr.error("Please complete siType/subCode")
          return
        }
        let data = {
          siType: +$("#adSetMeal_siType option:selected").val(),
          subCode: $("#adSetMeal_subCode").val(),
          qty: $("#adSetMeal_qty").val(),
          maxQty: toNum($("#adSetMeal_maxQty").val()),
          minQty: toNum($("#adSetMeal_minQty").val()),
          seq: toNum($("#adSetMeal_seq").val()),
          // mymodi: $("#adSetMeal_mymodi").val(),
          lNoDup: !!$("#adSetMeal_lNoDup").prop("checked")
        }
        let url = null
        if (ModalType === "Food") {
          data.fCode = customfood.fCode
          url = "../../manager_extraSetMeal/addOne"
        } else {
          data.code = customDetail.code
          url = "../../manager_extraMListSetMeal/addOne"
        }
        $.post({
          url,
          dataType: "json",
          data,
          success: function (res) {
            if (res.RESULT_CODE == 0) {
              toastr.success("Success")
              // queryAdOeder(fCode)  //查询追加套餐数据
              initData(pathList.length)
              console.log(res, "追加套餐")
              if (ModalType === "Food") {
                queryAdOeder("fCode", customfood.fCode)
              } else {
                queryAdOeder("code", customDetail.code)
              }
              $("#adSetMealModal").modal("hide")
              $("#adSetMealModal").on("hidden.bs.modal", function () {
                document.querySelector("#adOrderForm").reset()
              })
            } else {
              toastr.error("Fail to Add")
            }
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Fail to add ")
          }
        })
      })
      // 追加套餐编辑弹窗确定
      $("#onEdOrderSubmit").click(function () {
        let sitype = $("#edSetMeal_siType option:selected").val()
        let subCode = $("#edSetMeal_subCode").val()
        if (!sitype || !subCode) {
          toastr.error("Please complete siType/subCode")
          return
        }
        let ModalType = document.querySelector("#editSetMealModal").dialogType
        let data = {
          id: $("#onEdOrderSubmit").data("idNum"),
          siType: +$("#edSetMeal_siType option:selected").val(),
          subCode: $("#edSetMeal_subCode").val(),
          qty: $("#edSetMeal_qty").val(),
          minQty: toNum($("#edSetMeal_minQty").val()),
          maxQty: toNum($("#edSetMeal_maxQty").val()),
          seq: toNum($("#edSetMeal_seq").val()),
          lNoDup: !!$("#edSetMeal_lNoDup").prop("checked")
          // mymodi: $("#edSetMeal_mymodi").val(),
        }
        let url = null
        if (ModalType === "Food") {
          data.fCode = customfood.fCode
          url = "../../manager_extraSetMeal/updateOne"
        } else {
          data.code = customDetail.code
          url = "../../manager_extraMListSetMeal/updateOne"
        }
        // console.log(toNum($("#edSetMeal_minQty").val()), 'edSetMeal_minQty')
        $.post({
          url,
          dataType: "json",
          data,
          success: function (res) {
            if (res.RESULT_CODE == 0) {
              toastr.success("Success")
              console.log(res, "编辑追加套餐")
              if (ModalType === "Food") {
                queryAdOeder("fCode", customfood.fCode)
              } else {
                queryAdOeder("code", customDetail.code)
              }
              $("#editSetMealModal").modal("hide")
            } else {
              toastr.error("Fail to Edit")
            }
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Error")
          }
        })
      })
      // 细项弹窗确定
      $("#ondetailSubmit").click(function () {
        // toastr.error('Error');
        let type = customDetail.detailType
        let lan1 = $("#detailNameA").val()
        let lan2 = $("#detailNameB").val()
        let lan3 = $("#detailMulti1").val()

        switch (type) {
          case "setM":
            console.log("setM")
            customDetail.name = lan1
            customDetail.name2 = lan2

            break
          case "Mlist":
            console.log("Mlist")
            var code = customDetail.code
            subMlist(code, lan1, lan2, lan3)
            // customDetail.name = lan1
            // customDetail.name2 = lan2
            break
          case "Mtype":
            console.log("Mtype")
            var code = customDetail.code

            subMtylist(code, lan1, lan2, lan3)
            break
        }
        console.log(customDetail, "提交后的对象")
        $("#detailModal").modal("hide")
      })
      // mlist第三语言提交
      function subMlist(code, lan1, lan2, lan3) {
        let expiredBanSuperior = $("#MexpiredBanSuperior").prop("checked")
        let packingBoxMListCode = $("#mPackingBoxMListCode").val()
        let obj = {
          code,
          nameA: lan1,
          nameB: lan2,
          multi1: lan3,
          packingBoxMListCode,
          expiredBanSuperior
        }
        $.post({
          url: "../../mList/update",
          dataType: "json",
          data: obj,
          success: function (res) {
            console.log(res)
            if (res.RESULT_CODE == -1) {
              toastr.error("Error")
            } else {
              toastr.success("Success")
              customDetail.nameA = lan1
              customDetail.nameB = lan2
              customDetail.multi1 = lan3
              customDetail.expiredBanSuperior = expiredBanSuperior
              customDetail.packingBoxMListCode = packingBoxMListCode
            }
            $("#foodModal").modal("hide")
            // console.log(onfoodType, 'q请求');
          },
          error: function (data) {
            console.log(data, "错误")
            toastr.error("Error")
          }
        })
      }

      function subMtylist(code, lan1, lan2, lan3) {
        let obj = {
          code,
          nameA: lan1,
          nameB: lan2,
          multi1: lan3,
          mList_display_column: $("#mlistColumnSelect option:selected").val(),
          mList_show_src: $("#mlistShowImg").prop("checked"),
          item_ctrl_model: $("input[name='showMlistItemCtrl']:checked").val()
        }

        $.post({
          url: "../../manager_mType/updateOne",
          dataType: "json",
          data: obj,
          success: function (res) {
            console.log(res)
            if (res.RESULT_CODE == -1) {
              toastr.error("Error")
            } else {
              toastr.success("Success")
              customDetail.desc = lan1
              customDetail.desc2 = lan2
              customDetail.multi1 = lan3
              customDetail.mList_display_column = obj.mList_display_column
              customDetail.mList_show_src = obj.mList_show_src
              customDetail.item_ctrl_model = obj.item_ctrl_model
            }
            $("#foodModal").modal("hide")
            // console.log(onfoodType, 'q请求');
          },
          error: function (data) {
            toastr.error("Error")
            console.log(data, "错误")
          }
        })
      }
      function toNum(val) {
        if (val) {
          return Number(val)
        } else {
          return ""
        }
      }

      var adSetMealModalElement = document.getElementById("adSetMealModal")
      adSetMealModalElement.addEventListener("hidden.bs.modal", function (event) {
        if (event.target.dialogType === "Food") {
          $("#foodModal").modal("show")
        } else {
          $("#detailModal").modal("show")
        }
      })
      var editSetMealModalElement = document.getElementById("editSetMealModal")
      editSetMealModalElement.addEventListener("hidden.bs.modal", function (event) {
        if (event.target.dialogType === "Food") {
          $("#foodModal").modal("show")
        } else {
          $("#detailModal").modal("show")
        }
      })

      // 监听ajax请求成功回调
      $(document).ajaxSuccess((event, xhr, options) => {
        // 判断请求方式是POST(暂定post方式的接口会触发缓存生成)
        if (options.type === "POST") checkRepeatRequest()
        try {
          let res = JSON.parse(xhr.responseText)
          if (res.statusCode == 4033) {
            toastr.error("Data cannot be modified")
          }
        } catch (error) {
          // console.log('invalid data');
        }
      })
    }
  </script>
</html>
