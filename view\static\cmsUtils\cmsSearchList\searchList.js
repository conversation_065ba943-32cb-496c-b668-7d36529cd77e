let allSearchArray = []
function findParent(allFoodTypeList, searchVal) {
  let { activeId } = getCurrentPageAndData()
  allSearchArray = []
  //遍历第一层foodType
  allFoodTypeList.forEach(fty => {
    let someListArray = activeId == "homePage" ? fty.foodList : fty.mListList 
    someListArray.forEach(foodItem => {
      //搜索下层food,返回数组
      let resArr = checkFood(foodItem, 1, searchVal, activeId)
      if (resArr.length !== 0) {
        resArr.forEach(element => {
          element[0] = fty
        })
        // 根据resArr数组内长度排序
        resArr.sort((a, b) => {
          return a.length - b.length
        })
        allSearchArray.push(resArr)
      }
    })
  })
}
/**
 * @description:
 * @param {*} foodItem  FoodList数据
 * @param {*} index  父级层数
 * @param {*} searchVal  搜索值
 * @param {*} activeId  判断是homePage还是mtypePage
 * @return {*} searchVal
 */
function checkFood(foodItem, index, searchVal, activeId) {
  let hierarchicalArray = [] // 搜索值的层级数组
  if (!foodItem) return hierarchicalArray
  const key = searchVal.replace(/([\$\^\*\(\)\+\?\{\}\[\]\.\\])/g, "\\$1")
  const reg = new RegExp(`(${key})`, "igm")
  let EnName = inListTitle(foodItem),
    ZhName = inListTitle(foodItem, "zh"),
    thirdLanName = inListTitle(foodItem, "thirdLan")
  // 避免中英混合搜索显示无法确定一种语言,暂时不用中文搜索
  listCode = foodItem.fCode || foodItem.code
  // 匹配中英文名称/标识,返回匹配结果
  let res = reg.exec(EnName) || reg.exec(ZhName) || reg.exec(thirdLanName) || reg.exec(listCode)
  if (res) {
    let newArray = []
    newArray[index] = foodItem // 当前层级索引赋值,从第1层开始
    hierarchicalArray.push(newArray)
  }
  // 动态根据页面切换搜索foodList或mList
  let { foodList = [], mListList = [] } = foodItem

  // let targetList = activeId == "homePage" ? [...foodList, ...mListList] : mListList
  let targetList = activeId == "homePage" ? foodList : mListList

  if (targetList && targetList.length != 0) {
    targetList.forEach(food => {
      //搜索下层food,返回符合搜索结果的数组
      let resArr = checkFood(food, index + 1, searchVal, activeId)
      if (resArr.length > 0) {
        //有符合搜索结果的数据,遍历符合搜索结果的数组
        //如index=2,[[null,null,null,food],[null,null,null,food,foodType,food]]
        resArr.forEach(fightArr => {
          //设置当前索引值
          fightArr[index] = foodItem
          //如index=2,[[null,null,food,food],[null,null,food,food,foodType,food]]
          hierarchicalArray.push(fightArr)
        })
      }
    })
  }
  if (foodItem.allTypeArry && activeId == "homePage") {
    // 检查foodList(i.g. allTypeArry这个合并type字段只有foodList存在)
    let resArr = checkType(foodItem.allTypeArry, index + 1, searchVal, activeId)
    if (resArr.length > 0) {
      resArr.forEach(fightArr => {
        fightArr[index] = foodItem
        hierarchicalArray.push(fightArr)
      })
    }
  } else {
    // 检查mListList
    let resArr = checkType(foodItem.mTypeList, index + 1, searchVal, activeId)
    if (resArr.length > 0) {
      resArr.forEach(fightArr => {
        fightArr[index] = foodItem
        hierarchicalArray.push(fightArr)
      })
    }
  }

  return hierarchicalArray
}

function checkType(allTypeArry, index, searchVal, activeId) {
  let hierarchicalArray = []
  if (!allTypeArry) return hierarchicalArray
  allTypeArry.forEach(typeItem => {
    let { foodList = [], mListList = [] } = typeItem
    // let targetList = activeId == "homePage" ? [...foodList, ...mListList] : mListList
    let targetList = activeId == "homePage" ? foodList : mListList
    if (!targetList) return
    targetList.forEach(food => {
      let resArr = checkFood(food, index + 1, searchVal, activeId)
      if (resArr.length > 0) {
        resArr.forEach(fightArr => {
          fightArr[index] = typeItem
          hierarchicalArray.push(fightArr)
        })
      }
    })
  })
  return hierarchicalArray
}

function searchListDataClick(typeIndex, Hindex) {
  let { activeId, initData } = getCurrentPageAndData()
  let targetArray = allSearchArray[typeIndex][Hindex],
    targetFood = targetArray[targetArray.length - 1],
    targetParent = targetArray[targetArray.length - 2],
    targetArrayLength = targetArray.length

  console.log("🚀 ~ file: searchList.js:110 ~ searchListDataClick ~ targetArray", targetArray)
  targetArray.forEach((item, index) => {
    // index为0时为foodType,大于0时候获取上一个对象的foodList数据找出索引
    manualAssignment(item, index)
    if (index > 0) {
      let parentArray, parentIndex
      if (item.typeName == "ftyItem" || item.detailType == "Mtype") {
        parentArray =
          activeId == "homePage"
            ? targetArray[index - 1].allTypeArry
            : targetArray[index - 1].mTypeList
        parentIndex = parentArray.findIndex(item => {
          return item.code === targetArray[index].code
        })
        pathList[index] = {
          id: item.code,
          type: activeId == "homePage" ? item.typeName : item.detailType,
          name: outListTitle(item)
        }
      } else {
        parentArray =
          activeId == "homePage"
            ? targetArray[index - 1].foodList
            : targetArray[index - 1].mListList
        let convertCode = activeId == "homePage" ? "fCode" : "code"
        parentIndex = parentArray.findIndex(item => {
          return item[convertCode] === targetArray[index][convertCode]
        })
        pathList[index] = {
          id: item[convertCode],
          type: activeId == "homePage" ? "food" : "Mlist",
          name: inListTitle(item)
        }
      }
      pathList[index] = {
        ...pathList[index],
        index: parentIndex
      }
    } else if (index == 0) {
      let i = initData.findIndex(item => {
        return item.code === targetArray[0].code
      })
      pathList[0] = {
        id: item.code,
        index: i,
        type: activeId == "homePage" ? "ftyItem" : "Mtype",
        name: outListTitle(item)
      }
    }
  })
  let defaultTestII = targetArrayLength - 1
  let dataType
  if (pathList.length != 0) dataType = targetParent.typeName == "ftyItem" ? "fty" : "food"
  console.log("🚀 ~ file: cmsSearchList.js:142 ~ searchListDataClick ~ pathList", pathList)
  if (activeId == "homePage") {
    switch (defaultTestII) {
      case 1:
        createFoodElm(pathList[0].index, pathList[0].id, dataType)
        break
      case 2:
        createSetMealElm(pathList[1].index, pathList[1].id, dataType)
        break
      case 3:
        createSiElm(pathList[2].index, pathList[2].id, dataType)
        break
      case 4:
        createWuElm(pathList[3].index, pathList[3].id, dataType)
        break
      case 5:
        createLiuElm(pathList[4].index, pathList[4].id, dataType)
        break
      case 6:
        createQiElm(pathList[5].index, pathList[5].id, dataType)
        break
      case 7:
        createBaElm(pathList[6].index, pathList[6].id, dataType)
        break
      case 8:
        createJiuElm(pathList[7].index, pathList[7].id, dataType)
        break
      default:
      // if (type !== "init") createFoodTypeElm()
    }
  } else {
    switch (defaultTestII) {
      case 1:
        createSiElm(pathList[0].index, pathList[0].id, pathList[0].type)
        break
      case 2:
        createWuElm(pathList[1].index, pathList[1].id, pathList[1].type)
        break
      case 3:
        createLiuElm(pathList[2].index, pathList[2].id, pathList[2].type)
        break
      case 4:
        createQiElm(pathList[3].index, pathList[3].id, pathList[3].type)
        break
      case 5:
        createBaElm(pathList[4].index, pathList[4].id, pathList[4].type)
        break
      default:
      // createSetMealElm()
    }
  }

  // 关闭弹窗
  $("#searchListModal").modal("hide")
  console.log(allSearchArray[typeIndex][Hindex], "点击的搜索数据")
}
// 不同层对象手动赋值
function manualAssignment(item, index) {
  let { activeId } = getCurrentPageAndData()
  if (activeId == "homePage") {
    switch (index) {
      case 2:
        siClickItem = item
        break
      case 3:
        wuClickItem = item
        break
      case 4:
        liuClickItem = item
        break
      case 5:
        qiClickItem = item
        break
      case 6:
        baClickItem = item
        break
      case 7:
        jiuClickItem = item
        break
      default:
        break
    }
  } else {
    switch (index) {
      case 0:
        siClickItem = item
        break
      case 1:
        wuClickItem = item
        break
      case 2:
        liuClickItem = item
        break
      case 3:
        qiClickItem = item
        break
      case 4:
        baClickItem = item
        break
      default:
        break
    }
  }
}
// 打开搜索list弹窗
function showSearchListModal() {
  let { initData } = getCurrentPageAndData()
  // 检查总数据foodTypeList数据是否已经加载
  if (!initData) {
    // 提示等待数据加载完成再启用搜索
    toastr.warning("Please wait for the data to load before searching")
  } else {
    $("#searchListModal").modal("show")
  }
}

/**
 * @description:
 * @param {*} allSearchArray  搜索出来的总数组
 * @param {*} everyType  第一层开始每个Type数据匹配到的结果
 * @param {*} everyHierarchy  每个Type里面的每个层级数据匹配到的结果
 */
//执行搜索结果
function getSearchList(content = "") {
  content = content.trim()
  if (content) {
    let { initData } = getCurrentPageAndData()
    console.log("🚀 ~ file: menu.html:2972 ~ getSearchList ~ initData", initData)
    findParent(initData, content)
    console.log("🚀 ~ file: menu.html:2972 ~ getSearchList ~ allSearchArray", allSearchArray)
    if (allSearchArray.length) {
      // jq隐藏.listSearch-Help
      $(".listSearch-Help").hide()
      // 遍历allSearchArray里面的数组拼接成字符串在.searchList-Data上
      let divDom = ""
      allSearchArray.forEach((everyType, typeIndex) => {
        everyType.forEach((everyHierarchy, Hindex) => {
          // 获取everyHierarchy的最后一个对象匹配语言
          let lastItem = everyHierarchy[everyHierarchy.length - 1],
            lastItemEnName = inListTitle(lastItem),
            thirdLanName = inListTitle(lastItem, "thirdLan")
          const key = content.replace(/([\$\^\*\(\)\+\?\{\}\[\]\.\\])/g, "\\$1")
          const reg = new RegExp(`(${key})`, "igm")
          let lan = reg.exec(thirdLanName) ? "thirdLan" : reg.exec(lastItemEnName) ? "en" : "zh" // 符合的语言用于下面名称传递
          let str = ""
          everyHierarchy.forEach((item, index) => {
            // 判断item是fty还是mtype
            if (item.typeName == "ftyItem" || item.detailType == "Mtype") {
              str += `<span class="searchList-Data-name">${outListTitle(item, lan)}(${
                item.code
              })\u00A0/\u00A0</span>`
            } else {
              // 匹配 inListTitle(item, lan)或者item.fCode || item.code 看谁匹配上加高亮
              let matchName = inListTitle(item, lan)
              let matchCode = item.fCode || item.code

              let matchNameDom = matchName.replace(reg, '<span class="matchContent">$1</span>')
              let matchCodeDom = matchCode.replace(reg, '<span class="matchContent">$1</span>')

              str += `<span class="searchList-Data-name">${matchNameDom}(${matchCodeDom})\u00A0/\u00A0</span>`
            }
          })
          // 删除str最后空格到"/"字符
          str = str.substring(0, str.lastIndexOf("\u00A0/\u00A0"))
          // 点击事件绑定索引
          divDom += `<li class="searchList-Data-li" onclick="searchListDataClick(${typeIndex},${Hindex})">
              <div class="searchList-Data-li-left">
                <span style="margin-right: 7px;">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" 
                         fill="currentColor" class="bi bi-file-earmark" viewBox="0 0 16 16">
                    <path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z"/>
                </svg>
              </span>
                <p>${str}</p> 
              </div>
              <span class='searchList-Data-selectSvg'>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-return-left" viewBox="0 0 16 16">
                      <path fill-rule="evenodd" d="M14.5 1.5a.5.5 0 0 1 .5.5v4.8a2.5 2.5 0 0 1-2.5 2.5H2.707l3.347 3.346a.5.5 0 0 1-.708.708l-4.2-4.2a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 8.3H12.5A1.5 1.5 0 0 0 14 6.8V2a.5.5 0 0 1 .5-.5z"/>
                  </svg>
              </span>
              </li>`
        })
      })
      document.querySelector(".searchList-Data").innerHTML = divDom
    } else {
      // jq显示.listSearch-Help
      document.querySelector(".searchList-Data").innerHTML = ""
      $(".listSearch-Help").show()
    }
    // console.log('ajax request ', allData)
  } else {
    document.querySelector(".searchList-Data").innerHTML = ""
    $(".listSearch-Help").show()
  }
  $(".DocSearch-Loading-Icon").hide()
  $(".DocSearch-Search-Icon").show()
}
// 输入框防抖
function debounce(fun, delay) {
  return function (args) {
    let that = this
    let _args = args
    clearTimeout(fun.id)
    fun.id = setTimeout(function () {
      fun.call(that, _args)
    }, delay)
  }
}

let inputb = document.getElementById("searchListInput")

let debounceAjax = debounce(getSearchList, 1000)

inputb.addEventListener("keyup", function (e) {
  //jq .DocSearch-Loading-Icon显示,.DocSearch-Search-Icon隐藏
  $(".DocSearch-Search-Icon").hide()
  $(".DocSearch-Loading-Icon").show()
  debounceAjax(e.target.value)
})
//获取当前页面的数据和当前页面的id
function getCurrentPageAndData() {
  // 获取外层iframe中的标签属性
  var toggle = window.parent.document.getElementById("toggle")
  // 获取toggle下li div.active的标签id属性
  var activeId = toggle.querySelector("li div.active").id
  let initData = activeId == "homePage" ? foodTypeList : mtyAllList
  return { initData, activeId }
}
// jq监听打开搜索弹窗
$("#searchListModal").on("shown.bs.modal", function () {
  document.getElementById("searchListInput").focus()
})
// jq监听关闭搜索弹窗
$("#searchListModal").on("hide.bs.modal", function () {
  // 清空搜索框
  $("#searchListInput").val("")
  document.querySelector(".searchList-Data").innerHTML = ""
  $(".listSearch-Help").show()
})
