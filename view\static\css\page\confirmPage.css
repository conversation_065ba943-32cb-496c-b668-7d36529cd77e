* {
  margin: 0;
  padding: 0;
  font-family: Arial, Helvetica, sans-serif;
}

:root {
  --nabeColor: #c7b14c;
  --ppgColor: #770239;
}
/* .container {
  position: fixed;

  top: 0px;
  left: 0px;
  bottom: 0px;
  right: 0px;
  background: rgb(255, 255, 255);
} */
.suclayer_content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: fixed;
  top: 0px;
  left: 0px;
  bottom: 0px;
  right: 0px;
  /* height: 100vh; */
}

.suclayer_header {
  width: 100%;
  padding: 0.56rem 0;
  background-color: var(--ppgColor);
  color: #fff;
  text-align: center;
}

.suclayer_header h4,
.suclayer_header p {
  padding: 0 0.56rem;
}

.suclayer_header h4 {
  margin-bottom: 0.5rem;
  font-size: 0.56rem;
  font-weight: 600;
}

.suclayer_header p {
  font-size: 0.45rem;
}

.suclayer_middle {
  box-sizing: border-box;
  padding: 1.1rem 0.57rem;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-justify-content: space-around;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.suclayer_bcgstyle {
  width: 100%;
}

.suclayer_earn_warp {
  /* padding: 1rem 0; */
}

.suclayer_earn_title {
  font-weight: 600;
  font-size: 0.39rem;
  padding: 0.1rem 0;
  color: black;
}
.suclayer_earn_text {
  /* font-weight: 600; */
  font-size: 0.39rem;
  color: rgba(0, 0, 0, 0.7);
}

.suclayer_earn_button {
  text-align: center;
  width: 15rem;
  height: 1.8rem;
  line-height: 1.8rem;
  border-radius: 1rem;
  color: var(--ppgColor);
  font-weight: 600;
  background-color: #ccc;
  font-size: 0.8rem;
}

.suclayer_footer_warp {
  width: 100%;
  height: 2rem;
  background-color: var(--ppgColor);
  text-align: center;
  line-height: 2rem;
  font-weight: 600;
  color: #fff;
  border-radius: 2rem;
  font-size: 1rem;
}
