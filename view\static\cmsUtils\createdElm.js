//创建FoodType卡
function createFoodTypeElm() {
  initBreadcrumb([])
  // 清空路径信息
  pathList.length = 0
  // 清空快速链接
  $(parent.document.getElementById("navigation")).html("")

  // 激活菜单
  $("#menu_foodType").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#menu_foodType").addClass("active") // 添加当前元素的样式

  var html = ""
  $.each(foodTypeList, function (i, foodType) {
    html += createFoodTypeCard(i, foodType, "1")
  })
  $(document.getElementById("body_warp")).html(html)
}

//创建Food卡（foodType所在索引foodTypeList[?]，该foodType的id）
var foodList

function createFoodElm(foodTypeIndex, id) {
  // 清空多余路径信息
  pathList.length = 1
  var foodType = foodTypeList[foodTypeIndex]
  pathList[0] = {
    index: foodTypeIndex,
    id: id
  } //记录foodType的索引和id
  savePathList(0, "fty", foodType)
  // 激活菜单
  $("#menu_food").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#menu_food").addClass("active") // 添加当前元素的样式
  // 设置外层导航
  var navigation = $(parent.document.getElementById("navigation"))
  var navHtml =
    '<a target="iframe" onclick="jumpIframe(1,\'' +
    id +
    '\')" style="text-decoration-line: underline;cursor: pointer">' +
    foodType.name2 +
    "</a>"
  navigation.html(navHtml)
  // 设置主体页面内容
  foodList = foodType.foodList
  var html = ""
  $.each(foodList, function (foodIndex, food) {
    html += createFoodCard(foodIndex, food, "1")
  })
  $(document.getElementById("body_warp")).html(html)
}

//创建第三层卡（food所在索引foodTypeList[pathList[0].index].foodList[?]，该food的id）
var outFtypeInex, outfoodInex //储存外层索引，解决传值只能拷贝无法改变元数据问题
function createSetMealElm(foodIndex, id) {
  // 清空多余路径信息
  pathList.length = 2
  var foodType = foodTypeList[pathList[0].index]
  var food = foodType.foodList[foodIndex]
  outFtypeInex = pathList[0].index //储存第一层fty
  outfoodInex = foodIndex //储存footype下的food
  pathList[1] = {
    index: foodIndex,
    id: id
  } //记录food的索引和id
  savePathList(1, "food", food)

  // 激活菜单
  $("#Food_detail").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#Food_detail").addClass("active") // 添加当前元素的样式

  // 设置外层导航
  var navigation = $(parent.document.getElementById("navigation"))
  var navHtml1 =
    '<a target="iframe" onclick="jumpIframe(1,\'' +
    foodType.code +
    '\')" style="text-decoration-line: underline;cursor: pointer">' +
    foodType.name2 +
    "</a>"
  var navHtml2 =
    '<a target="iframe" onclick="jumpIframe(2,\'' +
    id +
    '\')" style="text-decoration-line: underline;cursor: pointer">' +
    food.desc2 +
    "</a>"
  navigation.html(navHtml1 + " / " + navHtml2)
  // 设置主体页面内容
  var html = ""

  if (food.foodList && food.foodList.length > 0) {
    $.each(food.foodList, function (i, foodItem) {
      html += createFoodCard(i, foodItem, "2")
    })
  }
  if (food.mListList && food.mListList.length > 0) {
    $.each(food.mListList, function (i, mListListItem) {
      html += createMListListCard(i, mListListItem, "2")
    })
  }
  if (food.allTypeArry && food.allTypeArry.length != 0) {
    food.allTypeArry.forEach((item, index) => {
      if (item.typeName == "ftyItem") {
        html += createFoodTypeCard(index, item, "2")
      }
      if (item.typeName == "mtyItem") {
        html += createMTypeCard(index, item, "2")
      }
    })
  }

  $(document.getElementById("body_warp")).html(html)
}
// 创建第四层细项卡
var siClickItem = {} //第四层点击的item
function createSiElm(clickIndex, id, type) {
  // 清空多余路径信息
  pathList.length = 3
  var foodType = foodTypeList[pathList[0].index]
  var food = foodType.foodList[pathList[1].index] //第二层food;
  pathList[2] = {
    index: clickIndex,
    id: id
  } //记录food的索引和id
  switch (type) {
    case "food":
      siClickItem = food.foodList[clickIndex]
      console.log("第三层food点击")
      break
    case "fty":
      siClickItem = food.allTypeArry[clickIndex]
      console.log(food.allTypeArry[clickIndex], "第三层fty下food点击")
      break
    case "Mlist":
      siClickItem = food.mListList[clickIndex]
      console.log("第三层Mlist点击")
      break
    case "Mtype":
      siClickItem = food.allTypeArry[clickIndex]
      console.log(food.allTypeArry[clickIndex], "第三层Mtype下mlist点击")
      break
  }
  savePathList(2, type, siClickItem)
  console.log(siClickItem, "第四层food点击进入")

  // 激活菜单
  $("#Food_Indetail").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#Food_Indetail").addClass("active") // 添加当前元素的样式

  var html = ""
  $(parent.document.getElementById("navigation")).html("")

  if (siClickItem.foodList && siClickItem.foodList.length > 0) {
    $.each(siClickItem.foodList, function (i, foodItem) {
      html += createFoodCard(i, foodItem, "3")
    })
  }
  if (siClickItem.mListList && siClickItem.mListList.length > 0) {
    $.each(siClickItem.mListList, function (i, mListListItem) {
      html += createMListListCard(i, mListListItem, "3")
    })
  }
  if (siClickItem.allTypeArry && siClickItem.allTypeArry.length != 0) {
    siClickItem.allTypeArry.forEach((item, index) => {
      if (item.typeName == "ftyItem") {
        html += createFoodTypeCard(index, item, "3")
      }
      if (item.typeName == "mtyItem") {
        html += createMTypeCard(index, item, "3")
      }
    })
  }
  // if (siClickItem.foodTypeList && siClickItem.foodTypeList.length > 0) {
  //   $.each(siClickItem.foodTypeList, function (i, foodTypeItem) {
  //     html += createFoodTypeCard(i, foodTypeItem, "3");
  //   });
  // }
  // if (siClickItem.mTypeList && siClickItem.mTypeList.length > 0) {
  //   $.each(siClickItem.mTypeList, function (i, mTypeItem) {
  //     html += createMTypeCard(i, mTypeItem, "3");
  //   });
  // }

  $(document.getElementById("body_warp")).html(html)
}
// 第五层细项卡
var wuClickItem = {} //第五层点击的item
function createWuElm(clickIndex, id, type) {
  // var foodType = foodTypeList[pathList[0].index];
  // var food = foodType.foodList[pathList[1].index] //第二层food;
  // 清空多余路径信息
  pathList.length = 4
  pathList[3] = {
    index: clickIndex,
    id: id
  } //记录food的索引和id
  switch (type) {
    case "food":
      wuClickItem = siClickItem.foodList[clickIndex]
      console.log("第四层food点击")
      break
    case "fty":
      console.log(siClickItem.allTypeArry[clickIndex], "第四层fty下food点击")
      wuClickItem = siClickItem.allTypeArry[clickIndex]
      break

    case "Mlist":
      wuClickItem = siClickItem.mListList[clickIndex]
      console.log("第四层Mlist点击")
      break
    case "Mtype":
      wuClickItem = siClickItem.allTypeArry[clickIndex]
      console.log(siClickItem.allTypeArry[clickIndex], "第四层Mtype下mlist点击")
      break
  }
  savePathList(3, type, wuClickItem)

  console.log(wuClickItem, "第四层点击进入")

  // 激活菜单
  $("#Specific_detail").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#Specific_detail").addClass("active") // 添加当前元素的样式

  var html = ""

  $(parent.document.getElementById("navigation")).html("")

  if (wuClickItem.foodList && wuClickItem.foodList.length > 0) {
    $.each(wuClickItem.foodList, function (i, foodItem) {
      html += createFoodCard(i, foodItem, "4")
    })
  }
  if (wuClickItem.mListList && wuClickItem.mListList.length > 0) {
    $.each(wuClickItem.mListList, function (i, mListListItem) {
      html += createMListListCard(i, mListListItem, "4")
    })
  }

  if (wuClickItem.allTypeArry && wuClickItem.allTypeArry.length != 0) {
    wuClickItem.allTypeArry.forEach((item, index) => {
      if (item.typeName == "ftyItem") {
        html += createFoodTypeCard(index, item, "4")
      }
      if (item.typeName == "mtyItem") {
        html += createMTypeCard(index, item, "4")
      }
    })
  }

  // if (wuClickItem.foodTypeList && wuClickItem.foodTypeList.length > 0) {
  //   $.each(wuClickItem.foodTypeList, function (i, foodTypeItem) {
  //     html += createFoodTypeCard(i, foodTypeItem, "4");
  //   });
  // }
  // if (wuClickItem.mTypeList && wuClickItem.mTypeList.length > 0) {
  //   $.each(wuClickItem.mTypeList, function (i, mTypeItem) {
  //     html += createMTypeCard(i, mTypeItem, "4");
  //   });
  // }

  $(document.getElementById("body_warp")).html(html)
}
// 第六层细项卡
var liuClickItem = {} //第六层点击的item

function createLiuElm(clickIndex, id, type) {
  var foodType = foodTypeList[pathList[0].index]
  var food = foodType.foodList[pathList[1].index] //第二层food;
  // 清空多余路径信息
  pathList.length = 5
  pathList[4] = {
    index: clickIndex,
    id: id
  } //记录food的索引和id
  switch (type) {
    case "food":
      liuClickItem = wuClickItem.foodList[clickIndex]
      console.log("第五层food点击")
      break
    case "fty":
      liuClickItem = wuClickItem.allTypeArry[clickIndex]
      console.log(wuClickItem.allTypeArry[clickIndex], "第五层fty下food点击")
      break

    case "Mlist":
      liuClickItem = wuClickItem.mListList[clickIndex]
      console.log("第五层Mlist点击")
      break
    case "Mtype":
      liuClickItem = wuClickItem.allTypeArry[clickIndex]
      console.log(wuClickItem.allTypeArry[clickIndex], "第五层Mtype下mlist点击")
      break
  }
  savePathList(4, type, liuClickItem)

  console.log(liuClickItem, "第五层点击进入")

  $("#Specific_Indetail").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#Specific_Indetail").addClass("active") // 添加当前元素的样式
  var html = ""

  $(parent.document.getElementById("navigation")).html("")

  if (liuClickItem.foodList && liuClickItem.foodList.length > 0) {
    $.each(liuClickItem.foodList, function (i, foodItem) {
      html += createFoodCard(i, foodItem, "5")
    })
  }
  if (liuClickItem.mListList && liuClickItem.mListList.length > 0) {
    $.each(liuClickItem.mListList, function (i, mListListItem) {
      html += createMListListCard(i, mListListItem, "5")
    })
  }
  if (liuClickItem.allTypeArry && liuClickItem.allTypeArry.length != 0) {
    liuClickItem.allTypeArry.forEach((item, index) => {
      if (item.typeName == "ftyItem") {
        html += createFoodTypeCard(index, item, "5")
      }
      if (item.typeName == "mtyItem") {
        html += createMTypeCard(index, item, "5")
      }
    })
  }
  // if (liuClickItem.foodTypeList && liuClickItem.foodTypeList.length > 0) {
  //   $.each(liuClickItem.foodTypeList, function (i, foodTypeItem) {
  //     html += createFoodTypeCard(i, foodTypeItem, '5');
  //   });
  // }
  // if (liuClickItem.mTypeList && liuClickItem.mTypeList.length > 0) {
  //   $.each(liuClickItem.mTypeList, function (i, mTypeItem) {
  //     html += createMTypeCard(i, mTypeItem, '5');
  //   });
  // }

  $(document.getElementById("body_warp")).html(html)
}
// 第七层细项卡
var qiClickItem = {} //第七层点击的item

function createQiElm(clickIndex, id, type) {
  var foodType = foodTypeList[pathList[0].index]
  var food = foodType.foodList[pathList[1].index] //第二层food;
  // 清空多余路径信息
  pathList.length = 6
  pathList[5] = {
    index: clickIndex,
    id: id
  } //记录food的索引和id
  switch (type) {
    case "food":
      qiClickItem = liuClickItem.foodList[clickIndex]
      console.log("第六层food点击")
      break
    case "fty":
      qiClickItem = liuClickItem.allTypeArry[clickIndex]
      console.log(liuClickItem.allTypeArry[clickIndex], "第六层fty下food点击")
      break

    case "Mlist":
      qiClickItem = liuClickItem.mListList[clickIndex]
      console.log("第六层Mlist点击")
      break
    case "Mtype":
      qiClickItem = liuClickItem.allTypeArry[clickIndex]
      console.log(liuClickItem.allTypeArry[clickIndex], "第六层Mtype下mlist点击")
      break
  }
  savePathList(5, type, qiClickItem)

  // 激活菜单
  $("#Specific_Indetail_six").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#Specific_Indetail_six").addClass("active") // 添加当前元素的样式
  var html = ""

  $(parent.document.getElementById("navigation")).html("")

  if (qiClickItem.foodList && qiClickItem.foodList.length > 0) {
    $.each(qiClickItem.foodList, function (i, foodItem) {
      html += createFoodCard(i, foodItem, "6")
    })
  }
  if (qiClickItem.mListList && qiClickItem.mListList.length > 0) {
    $.each(qiClickItem.mListList, function (i, mListListItem) {
      html += createMListListCard(i, mListListItem, "6")
    })
  }

  if (qiClickItem.allTypeArry && qiClickItem.allTypeArry.length != 0) {
    qiClickItem.allTypeArry.forEach((item, index) => {
      if (item.typeName == "ftyItem") {
        html += createFoodTypeCard(index, item, "6")
      }
      if (item.typeName == "mtyItem") {
        html += createMTypeCard(index, item, "6")
      }
    })
  }

  // if (qiClickItem.foodTypeList && qiClickItem.foodTypeList.length > 0) {
  //   $.each(qiClickItem.foodTypeList, function (i, foodTypeItem) {
  //     html += createFoodTypeCard(i, foodTypeItem, '6');
  //   });
  // }
  // if (qiClickItem.mTypeList && qiClickItem.mTypeList.length > 0) {
  //   $.each(qiClickItem.mTypeList, function (i, mTypeItem) {
  //     html += createMTypeCard(i, mTypeItem, '6');
  //   });
  // }

  $(document.getElementById("body_warp")).html(html)
}
// 第八层细项卡
var baClickItem = {} //第七层点击的item

function createBaElm(clickIndex, id, type) {
  // 清空多余路径信息
  pathList.length = 7
  pathList[6] = {
    index: clickIndex,
    id: id
  } //记录food的索引和id
  switch (type) {
    case "food":
      baClickItem = qiClickItem.foodList[clickIndex]
      break
    case "fty":
      baClickItem = qiClickItem.allTypeArry[clickIndex]
      break
    case "Mlist":
      baClickItem = qiClickItem.mListList[clickIndex]
      break
    case "Mtype":
      baClickItem = qiClickItem.allTypeArry[clickIndex]
      break
  }
  savePathList(6, type, baClickItem)

  // 激活菜单
  $("#Specific_Indetail_seven").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#Specific_Indetail_seven").addClass("active") // 添加当前元素的样式
  var html = ""

  $(parent.document.getElementById("navigation")).html("")

  if (baClickItem.foodList && baClickItem.foodList.length > 0) {
    $.each(baClickItem.foodList, function (i, foodItem) {
      html += createFoodCard(i, foodItem, "7")
    })
  }
  if (baClickItem.mListList && baClickItem.mListList.length > 0) {
    $.each(baClickItem.mListList, function (i, mListListItem) {
      html += createMListListCard(i, mListListItem, "7")
    })
  }

  if (baClickItem.allTypeArry && baClickItem.allTypeArry.length != 0) {
    baClickItem.allTypeArry.forEach((item, index) => {
      if (item.typeName == "ftyItem") {
        html += createFoodTypeCard(index, item, "7")
      }
      if (item.typeName == "mtyItem") {
        html += createMTypeCard(index, item, "7")
      }
    })
  }
  // if (baClickItem.foodTypeList && baClickItem.foodTypeList.length > 0) {
  //   $.each(baClickItem.foodTypeList, function (i, foodTypeItem) {
  //     html += createFoodTypeCard(i, foodTypeItem, '7');
  //   });
  // }
  // if (baClickItem.mTypeList && baClickItem.mTypeList.length > 0) {
  //   $.each(baClickItem.mTypeList, function (i, mTypeItem) {
  //     html += createMTypeCard(i, mTypeItem, '7');
  //   });
  // }

  $(document.getElementById("body_warp")).html(html)
}
// 第九层细项卡
var jiuClickItem = {} //第七层点击的item

function createJiuElm(clickIndex, id, type) {
  // 清空多余路径信息
  pathList.length = 8
  pathList[7] = {
    index: clickIndex,
    id: id
  } //记录food的索引和id
  switch (type) {
    case "food":
      jiuClickItem = baClickItem.foodList[clickIndex]
      break
    case "fty":
      jiuClickItem = baClickItem.allTypeArry[clickIndex]
      break
    case "Mlist":
      jiuClickItem = baClickItem.mListList[clickIndex]
      break
    case "Mtype":
      jiuClickItem = baClickItem.allTypeArry[clickIndex]
      break
  }
  savePathList(7, type, jiuClickItem)

  // 激活菜单
  $("#Specific_Indetail_eight").siblings("li").removeClass("active") // 删除其他兄弟元素的样式
  $("#Specific_Indetail_eight").addClass("active") // 添加当前元素的样式
  var html = ""

  $(parent.document.getElementById("navigation")).html("")

  if (jiuClickItem.foodList && jiuClickItem.foodList.length > 0) {
    $.each(jiuClickItem.foodList, function (i, foodItem) {
      html += createFoodCard(i, foodItem, "8")
    })
  }
  if (jiuClickItem.mListList && jiuClickItem.mListList.length > 0) {
    $.each(jiuClickItem.mListList, function (i, mListListItem) {
      html += createMListListCard(i, mListListItem, "8")
    })
  }
  if (jiuClickItem.allTypeArry && jiuClickItem.allTypeArry.length != 0) {
    jiuClickItem.allTypeArry.forEach((item, index) => {
      if (item.typeName == "ftyItem") {
        html += createFoodTypeCard(index, item, "8")
      }
      if (item.typeName == "mtyItem") {
        html += createMTypeCard(index, item, "8")
      }
    })
  }

  // if (jiuClickItem.foodTypeList && jiuClickItem.foodTypeList.length > 0) {
  //   $.each(jiuClickItem.foodTypeList, function (i, foodTypeItem) {
  //     html += createFoodTypeCard(i, foodTypeItem, '8');
  //   });
  // }
  // if (jiuClickItem.mTypeList && jiuClickItem.mTypeList.length > 0) {
  //   $.each(jiuClickItem.mTypeList, function (i, mTypeItem) {
  //     html += createMTypeCard(i, mTypeItem, '8');
  //   });
  // }

  $(document.getElementById("body_warp")).html(html)
}

//保存pathList
function savePathList(index, type, data) {
  let tempPath = sessionStorage.getItem("pathList")
  let name = ""
  if (type === "fty" || type === "Mtype") {
    name = outListTitle(data)
  } else {
    name = inListTitle(data)
  }
  if (type) {
    pathList[index] = {
      ...pathList[index],
      type,
      name
    }
    sessionStorage.setItem("pathList", JSON.stringify(pathList))
  } else if (tempPath && !type) {
    tempPath = JSON.parse(tempPath)
    pathList = tempPath.splice(0, index + 1)
    sessionStorage.setItem("pathList", JSON.stringify(pathList))
  }
  initBreadcrumb(pathList)
  console.log(pathList, "pathList")
}
window.onbeforeunload = () => {
  sessionStorage.removeItem("pathList")
}
//生成单个Food卡
function createFoodCard(foodIndex, food, type) {
  // var jsonFood = JSON.stringify(food).replace(/\"/g, "'");
  let spliceSuffix = isJPG(food.photoSuffix) // 图片后缀
  let photoNaming = food.fCode + spliceSuffix
  let showPrice = food[priceName("foodList")] == 0 ? "" : showCurrency + food[priceName("foodList")]
  var modalHtml = `<button id="editBtn"   class="btn btn-info" onclick="onfoodModal(${foodIndex},${type});">Info</button>`
  var html =
    '<div class=" col-xl-2 col-lg-3 col-md-4 col-sm-6 col-xs-6" >\n' +
    '        <div class="card" card-type="food">\n' +
    '            <table id="' +
    "card_" +
    food.fCode +
    '" cellspacing="0" cellpadding="0">\n' +
    "                <tr>\n" +
    '                    <td rowspan="3" width="75px" >' +
    '                       <input type="file"' +
    `title=\"${photoNaming}"\"` + // 图片后缀提示
    'id="' +
    food.fCode +
    '" name="file" class="upload" onclick="this.value=null" onchange="upload(\'' +
    food.fCode +
    "',this ,'food')\" accept=\"image/*\"/>" +
    '                           <img class="card-img" src="' +
    baseUrl +
    "food/" +
    food.fCode +
    spliceSuffix + // 图片后缀
    smallImageSuffix + // 图片规格参数
    '" onerror="noImg(this);"/></td>\n' +
    '                    <td colspan="2" class="card-name" title="' +
    food.desc2 +
    '">' +
    `<p class="card-name-warp">${food.nameA || food.desc1 || "&nbsp;"}</p>` +
    `<p class="card-name-warp">${food.nameB || food.desc2 || "&nbsp;"}</p>` +
    "</td>\n" +
    '                    <td rowspan="4" class="next-type-td">\n' +
    modalHtml

  var len = 0
  if (food.foodTypeList) len += food.foodTypeList.length
  if (food.foodList) len += food.foodList.length
  if (food.setMealList) len += food.setMealList.length
  if (food.mTypeList) len += food.mTypeList.length
  if (food.mListList) len += food.mListList.length

  if (len !== 0) {
    html += '  <button class="next-type-button btn btn-primary" '
    if (pathList.length <= 1) {
      html += `onclick=createSetMealElm(${foodIndex},'${food.fCode}')`
    } else if (pathList.length == 2) {
      html += `onclick=createSiElm(${foodIndex},'${food.fCode}','food')`
    } else if (pathList.length == 3) {
      html += `onclick=createWuElm(${foodIndex},'${food.fCode}','food')`
    } else if (pathList.length == 4) {
      html += `onclick=createLiuElm(${foodIndex},'${food.fCode}','food')`
    } else if (pathList.length == 5) {
      html += `onclick=createQiElm(${foodIndex},'${food.fCode}','food')`
    } else if (pathList.length == 6) {
      html += `onclick=createBaElm(${foodIndex},'${food.fCode}','food')`
    } else if (pathList.length == 7) {
      html += `onclick=createJiuElm(${foodIndex},'${food.fCode}','food')`
    }
  } else {
    html += '<button class="next-type-button-disabled" disabled="disabled"'
  }
  html +=
    ">\n" +
    '                            <font size="1" color="white">' +
    len +
    "</font><br/>\n" +
    "                        </button>\n" +
    "                    </td>\n" +
    "                </tr>\n" +
    "                <tr>\n" +
    '                    <td colspan="2" style="color: #9d9d9d" class="card-name">' +
    `<p class="card-name-info" id="identificationAndPrice">
    <span> ${food.fCode} </span> 
    <span class="card-price"> ${showPrice} </span>
    </p>` +
    "</td>\n" +
    "                </tr>\n" +
    `<tr></tr>` +
    "                <tr>\n" +
    `  <td class="card-bottom-useTime" >
    <i class="fa fa-clock-o"></i>
    ${!food.use_dow && !food.use_date && !food.use_time ? "全日" : "自定義"}
  </td>` +
    "                </tr>\n"
  // let priorityDisabled = food.priorityDisabled === true
  /**
   * @description: 开关描述
   * @param {boolean} priorityDisabled //是否隐藏的food，由上层的Hide item(s) form group控制
   */
  let { priorityDisabled, finalTouch, takeawayTouch, dineInTouch } = food
  //堂食/外帶/總開關
  const switchBoxCells = [
    {
      mode: "take-away",
      imgSrc: "../../static/img/cms/take-away.jpg",
      checked: finalTouch && takeawayTouch,
      disabled: !priorityDisabled && finalTouch ? "" : "disabled"
    },
    {
      mode: "dine-in",
      imgSrc: "../../static/img/cms/dine-in.jpg",
      checked: finalTouch && dineInTouch,
      disabled: !priorityDisabled && finalTouch ? "" : "disabled"
    },
    {
      mode: "master-switch",
      imgSrc: "../../static/img/cms/master-switch.jpg",
      checked: !priorityDisabled && finalTouch,
      disabled: !priorityDisabled ? "" : "disabled"
    }
  ]

  const switchBoxHtml = switchBoxCells
    .map(
      cell => `
    <div class='switchBox-cell'>
      <img src="${cell.imgSrc}" alt="" class="switchBox-icon" />
      <label class="switch">
        <input data-mode="${cell.mode}" type="checkbox" onclick="onTypeCheckbox('${
        food.fCode
      }', this, 'foodlist', '${foodIndex}', '${type}')" ${cell.checked ? "checked" : ""} ${
        cell.disabled
      }>
        <div class="slider round"></div>
      </label>
    </div>
  `
    )
    .join("")

  html += `
    <tr class="card-bottom">
      <td class="switchBox">
        ${switchBoxHtml}
      </td>
    </tr>
  `

  html += "</table>\n" + " </div>\n" + "</div>"
  return html
}
//生成单个Mlist卡
function createMListListCard(mlistIndex, mlistItem, type) {
  let spliceSuffix = isJPG(mlistItem.photoSuffix)
  let photoNaming = mlistItem.code + spliceSuffix
  let showPrice =
    mlistItem[priceName("mlist")] == 0 ? "" : showCurrency + mlistItem[priceName("mlist")]

  var mListModalHtml = `<button id="editBtn"   class="btn btn-info" onclick="onMlistModal(${mlistIndex},${type});">Info</button>`
  var html =
    '<div class=" col-xl-2 col-lg-3 col-md-4 col-sm-6 col-xs-6" >\n' +
    '        <div class="card" card-type="mlist">\n' +
    '            <table id="' +
    "card_" +
    mlistItem.code +
    '" cellspacing="0" cellpadding="0">\n' +
    "                <tr>\n" +
    '                    <td rowspan="3" width="75px" >' +
    '                       <input type="file"' +
    `title=\"${photoNaming}"\"` + // 图片后缀提示
    'id="' +
    mlistItem.code +
    '" name="file" class="upload" onclick="this.value=null" onchange="upload(\'' +
    mlistItem.code +
    "',this ,'mlist')\" accept=\"image/*\"/>" +
    '                           <img class="card-img" src="' +
    baseUrl +
    "mlist/" +
    mlistItem.code +
    spliceSuffix + // 图片后缀
    smallImageSuffix + // 图片规格参数
    '" onerror="noImg(this);"/></td>\n' +
    '                    <td  colspan="2" class="card-name" title="' +
    mlistItem.name2 +
    '">' +
    `<p class="card-name-warp">${mlistItem.nameA || mlistItem.name || "&nbsp;"}</p>` +
    `<p class="card-name-warp">${mlistItem.nameB || mlistItem.name2 || "&nbsp;"}</p>` +
    "</td>\n" +
    '                    <td rowspan="4" class="next-type-td">\n' +
    mListModalHtml
  var len = 0
  if (mlistItem.foodTypeList) len += mlistItem.foodTypeList.length
  if (mlistItem.foodList) len += mlistItem.foodList.length
  if (mlistItem.setMealList) len += mlistItem.setMealList.length
  if (mlistItem.mTypeList) len += mlistItem.mTypeList.length
  if (mlistItem.mListList) len += mlistItem.mListList.length
  if (len !== 0) {
    html += '<button class="next-type-button btn btn-primary" '
    if (pathList.length <= 2) {
      // 创建第3项，我的细项
      html += `onclick=createSiElm(${mlistIndex},'${mlistItem.code}','Mlist')`
    } else if (pathList.length == 3) {
      html += `onclick=createWuElm(${mlistIndex},'${mlistItem.code}','Mlist')`
    } else if (pathList.length == 4) {
      html += `onclick=createLiuElm(${mlistIndex},'${mlistItem.code}','Mlist')`
    } else if (pathList.length == 5) {
      html += `onclick=createQiElm(${mlistIndex},'${mlistItem.code}','Mlist')`
    } else if (pathList.length == 6) {
      html += `onclick=createBaElm(${mlistIndex},'${mlistItem.code}','Mlist')`
    } else if (pathList.length == 7) {
      html += `onclick=createJiuElm(${mlistIndex},'${mlistItem.code}','Mlist')`
    }
  } else {
    html += '<button class="next-type-button-disabled" disabled="disabled"'
  }
  html +=
    ">\n" +
    '                            <font size="1" color="white">' +
    len +
    "</font><br/>\n" +
    "                        </button>\n" +
    "                    </td>\n" +
    "                </tr>\n" +
    "                <tr>\n" +
    '                    <td colspan="2" style="color: #9d9d9d" class="card-name">' +
    `<p class="card-name-info" id="identificationAndPrice">
    <span> ${mlistItem.code} </span> 
    <span> ${showPrice} </span>
    </p>` +
    "</td>\n" +
    "                </tr>\n" +
    `<tr></tr>` +
    "                <tr>\n" +
    `  <td class="card-bottom-useTime" >
    <i class="fa fa-clock-o"></i>
    ${!mlistItem.use_dow && !mlistItem.use_date && !mlistItem.use_time ? "全日" : "自定義"}
  </td>` +
    "                </tr>\n"

  //堂食/外帶/總開關
  const switchBoxCells = [
    {
      mode: "take-away",
      imgSrc: "../../static/img/cms/take-away.jpg",
      checked: mlistItem.finalTouch && mlistItem.takeawayTouch,
      disabled: mlistItem.finalTouch ? "" : "disabled"
    },
    {
      mode: "dine-in",
      imgSrc: "../../static/img/cms/dine-in.jpg",
      checked: mlistItem.finalTouch && mlistItem.dineInTouch,
      disabled: mlistItem.finalTouch ? "" : "disabled"
    },
    {
      mode: "master-switch",
      imgSrc: "../../static/img/cms/master-switch.jpg",
      checked: mlistItem.finalTouch,
      disabled: ""
    }
  ]

  const switchBoxHtml = switchBoxCells
    .map(
      cell => `
    <div class='switchBox-cell'>
      <img src="${cell.imgSrc}" alt="" class="switchBox-icon" />
      <label class="switch">
        <input data-mode="${cell.mode}" type="checkbox" onclick="onTypeCheckbox('${
        mlistItem.code
      }', this, 'mlistItem', '${mlistIndex}', '${type}')" ${cell.checked ? "checked" : ""} ${
        cell.disabled
      }>
        <div class="slider round"></div>
      </label>
    </div>
  `
    )
    .join("")

  html += `
    <tr class="card-bottom">
      <td class="switchBox">
        ${switchBoxHtml}
      </td>
    </tr>
  `

  html += "</table>\n" + " </div>\n" + "</div>"
  return html
}
//生成单个FoodType卡
function createFoodTypeCard(foodTypeIndex, foodType, type) {
  // console.log(foodType, "遍历的foodType");
  let spliceSuffix = isJPG(foodType.photoSuffix)
  // let defaultTest = ["BA", "AX", "AA", "AC"]
  // if (defaultTest.includes(foodType.code)) {
  //   spliceSuffix = ".png"
  // }
  let photoNaming = foodType.code + spliceSuffix
  var fTypeModalHtml = `<button id="editBtn"   class="btn btn-info" onclick="onfTypeModal(${foodTypeIndex},${type});">Info</button>`
  var html =
    '<div class=" col-xl-2 col-lg-3 col-md-4 col-sm-6 col-xs-6" >\n' +
    '        <div class="card"  card-type="foodType">\n' +
    '            <table id="' +
    "card_" +
    foodType.code +
    '" cellspacing="0" cellpadding="0">\n' +
    "                <tr>\n" +
    '                    <td rowspan="3" width="75px" >' +
    '                   <input type="file"' +
    `title=\"${photoNaming}"\"` + // 图片后缀提示
    'id="' +
    foodType.code +
    '" name="file" class="upload" onclick="this.value=null" onchange="upload(\'' +
    foodType.code +
    "',this,'foodType')\" accept=\"image/*\"/>" +
    '                           <img class="card-img" src="' +
    baseUrl +
    "foodType/" +
    foodType.code +
    spliceSuffix + // 图片后缀
    smallImageSuffix + // 图片规格参数
    ' "onerror="noImg(this);"/></td>\n' +
    '                    <td  colspan="2" class="card-name" title="' +
    foodType.name +
    '">' +
    `<p class="card-name-warp">${foodType.fType_nameA || foodType.name || "&nbsp;"}</p>` +
    `<p class="card-name-warp">${foodType.fType_nameB || foodType.name2 || "&nbsp;"}</p>` +
    // foodType.name +
    "</td>\n" +
    '                   <td rowspan="4" class="next-type-td">\n' +
    fTypeModalHtml

  if (foodType.foodList.length != 0) {
    html += '<button class="next-type-button btn btn-primary" '
    if (pathList.length <= 1) {
      // 创建第2项，Food
      html += 'onclick="createFoodElm(' + foodTypeIndex + ",'" + foodType.code + "')\""
    } else if (pathList.length == 2) {
      html += `onclick=createSiElm(${foodTypeIndex},'${foodType.code}','fty')`
    } else if (pathList.length == 3) {
      html += `onclick=createWuElm(${foodTypeIndex},'${foodType.code}','fty')`
    } else if (pathList.length == 4) {
      html += `onclick=createLiuElm(${foodTypeIndex},'${foodType.code}','fty')`
    } else if (pathList.length == 5) {
      html += `onclick=createQiElm(${foodTypeIndex},'${foodType.code}','fty')`
    } else if (pathList.length == 6) {
      html += `onclick=createBaElm(${foodTypeIndex},'${foodType.code}','fty')`
    } else if (pathList.length == 7) {
      html += `onclick=createJiuElm(${foodTypeIndex},'${foodType.code}','fty')`
    }
  } else {
    html += '<button class="next-type-button-disabled" disabled="disabled"'
  }

  html +=
    ">\n" +
    '                            <font size="1" color="white">' +
    foodType.foodList.length +
    "</font><br/>\n" +
    "                        </button>\n" +
    "                    </td>\n" +
    "                </tr>\n" +
    "                <tr>\n" +
    '                    <td colspan="2" style="color: #9d9d9d" class="card-name">' +
    foodType.code +
    "</td>\n" +
    "  </tr>\n" +
    `<tr></tr>` +
    "                <tr>\n" +
    `  <td class="card-bottom-useTime" >
    <i class="fa fa-clock-o"></i>
    ${!foodType.use_dow && !foodType.use_date && !foodType.use_time ? "全日" : "自定義"}
  </td>` +
    "                </tr>\n"

  // html += `
  //     <tr class="card-bottom">
  //       <td class="switchBox">
  //         <div class='switchBox-cell'>
  //           <img src="../../static/img/cms/take-away.jpg" alt="" class="switchBox-icon" />
  //           <label class="switch">
  //                 <input data-mode='takeAway' type="checkbox" onclick="onTypeCheckbox('${foodType.code}', this, 'foodType', '${foodTypeIndex}', '${type}')"${foodType.finalTouch && foodType.takeawayTouch ? 'checked' : ''}
  //                 ${foodType.finalTouch ? "" : "disabled"}>
  //                 <div class="slider round"></div>
  //             </label>
  //         </div>
  //         <div class='switchBox-cell'>
  //           <img src="../../static/img/cms/dine-in.jpg" alt="" class="switchBox-icon" />
  //           <label class="switch">
  //               <input data-mode='dineIn' type="checkbox" onclick="onTypeCheckbox('${foodType.code}', this, 'foodType', '${foodTypeIndex}', '${type}')" ${foodType.finalTouch && foodType.dineInTouch ? 'checked' : ''}
  //               ${foodType.finalTouch ? "" : "disabled"}>
  //                 <div class="slider round"></div>
  //             </label>
  //         </div>
  //         <div class='switchBox-cell'>
  //           <img src="../../static/img/cms/master-switch.jpg" alt="" class="switchBox-icon" />
  //           <label class="switch">
  //                 <input data-mode='masterSwitch' type="checkbox" onclick="onTypeCheckbox('${foodType.code}', this, 'foodType', '${foodTypeIndex}', '${type}')" ${foodType.finalTouch ? "checked" : ""}>
  //                 <div class="slider round"></div>
  //             </label>
  //         </div>
  //       </td>
  //      </tr>
  //      `
  const switchBoxCells = [
    {
      mode: "take-away",
      imgSrc: "../../static/img/cms/take-away.jpg",
      checked: foodType.finalTouch && foodType.takeawayTouch,
      disabled: foodType.finalTouch ? "" : "disabled"
    },
    {
      mode: "dine-in",
      imgSrc: "../../static/img/cms/dine-in.jpg",
      checked: foodType.finalTouch && foodType.dineInTouch,
      disabled: foodType.finalTouch ? "" : "disabled"
    },
    {
      mode: "master-switch",
      imgSrc: "../../static/img/cms/master-switch.jpg",
      checked: foodType.finalTouch,
      disabled: ""
    }
  ]

  const switchBoxHtml = switchBoxCells
    .map(
      cell => `
    <div class='switchBox-cell'>
      <img src="${cell.imgSrc}" alt="" class="switchBox-icon" />
      <label class="switch">
        <input data-mode="${cell.mode}" type="checkbox" onclick="onTypeCheckbox('${
        foodType.code
      }', this, 'foodType', '${foodTypeIndex}', '${type}')" ${cell.checked ? "checked" : ""} ${
        cell.disabled
      }>
        <div class="slider round"></div>
      </label>
    </div>
  `
    )
    .join("")

  html += `
    <tr class="card-bottom">
      <td class="switchBox">
        ${switchBoxHtml}
      </td>
    </tr>
  `

  html += "</table>\n" + " </div>\n" + "</div>"
  return html
}

//生成单个细项MType卡
function createMTypeCard(mTypeIndex, mTypeItem, type) {
  let spliceSuffix = isJPG(mTypeItem.photoSuffix)
  let photoNaming = mTypeItem.code + spliceSuffix
  var mTypeModalHtml = `<button button id = "editBtn"  class="btn btn-info" onclick="onfMtypeModal(${mTypeIndex},${type});">Info</ >`
  var html =
    '<div class=" col-xl-2 col-lg-3 col-md-4 col-sm-6 col-xs-6" >\n' +
    '        <div class="card" card-type="mType">\n' +
    '            <table id="' +
    "card_" +
    mTypeItem.code +
    '" cellspacing="0" cellpadding="0">\n' +
    "                <tr>\n" +
    '                    <td rowspan="3" width="75px" >' +
    '                       <input type="file"' +
    `title =\"${photoNaming}"\"` + // 图片后缀提示
    'id="' +
    mTypeItem.code +
    '" name="file" class="upload" onclick="this.value=null" onchange="upload(\'' +
    mTypeItem.code +
    "',this ,'mtype')\" accept=\"image/*\"/>" +
    '                           <img class="card-img" src="' +
    baseUrl +
    "mtype/" +
    mTypeItem.code +
    spliceSuffix + // 图片后缀
    smallImageSuffix + // 图片规格参数
    '" onerror="noImg(this);"/></td>\n' +
    '                    <td  colspan="2" class="card-name" title="' +
    mTypeItem.desc2 +
    '">' +
    `<p class="card-name-warp">${mTypeItem.nameA || mTypeItem.desc || "&nbsp;"}</p>` +
    `<p class="card-name-warp">${mTypeItem.nameB || mTypeItem.desc2 || "&nbsp;"}</p>` +
    "</td>\n" +
    '                    <td rowspan="4" class="next-type-td">\n' +
    mTypeModalHtml

  var len = 0
  if (mTypeItem.foodTypeList) len += mTypeItem.foodTypeList.length
  if (mTypeItem.foodList) len += mTypeItem.foodList.length
  if (mTypeItem.setMealList) len += mTypeItem.setMealList.length
  if (mTypeItem.mTypeList) len += mTypeItem.mTypeList.length
  if (mTypeItem.mListList) len += mTypeItem.mListList.length
  if (len !== 0) {
    html += '<button class="next-type-button btn btn-primary" '
    if (pathList.length == 2) {
      html += `onclick=createSiElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
    } else if (pathList.length == 3) {
      html += `onclick=createWuElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
    } else if (pathList.length == 4) {
      html += `onclick=createLiuElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
    } else if (pathList.length == 5) {
      html += `onclick=createQiElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
    } else if (pathList.length == 6) {
      html += `onclick=createBaElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
    } else if (pathList.length == 7) {
      html += `onclick=createJiuElm(${mTypeIndex},'${mTypeItem.code}','Mtype')`
    }
  } else {
    html += '<button class="next-type-button-disabled" disabled="disabled"'
  }

  html +=
    ">\n" +
    '                            <font size="1" color="white">' +
    len +
    "</font><br/>\n" +
    "                        </button>\n" +
    "                    </td>\n" +
    "                </tr>\n" +
    "                <tr>\n" +
    '                    <td colspan="2" style="color: #9d9d9d" class="card-name">' +
    mTypeItem.code +
    "</td>\n" +
    "  </tr>\n" +
    `<tr></tr>` +
    "                <tr>\n" +
    `  <td class="card-bottom-useTime" >
    <i class="fa fa-clock-o"></i>
    ${!mTypeItem.use_dow && !mTypeItem.use_date && !mTypeItem.use_time ? "全日" : "自定義"}
  </td>` +
    "                </tr>\n"

  //堂食/外帶/總開關
  const switchBoxCells = [
    {
      mode: "take-away",
      imgSrc: "../../static/img/cms/take-away.jpg",
      checked: mTypeItem.finalTouch && mTypeItem.takeawayTouch,
      disabled: mTypeItem.finalTouch ? "" : "disabled"
    },
    {
      mode: "dine-in",
      imgSrc: "../../static/img/cms/dine-in.jpg",
      checked: mTypeItem.finalTouch && mTypeItem.dineInTouch,
      disabled: mTypeItem.finalTouch ? "" : "disabled"
    },
    {
      mode: "master-switch",
      imgSrc: "../../static/img/cms/master-switch.jpg",
      checked: mTypeItem.finalTouch,
      disabled: ""
    }
  ]

  const switchBoxHtml = switchBoxCells
    .map(
      cell => `
    <div class='switchBox-cell'>
      <img src="${cell.imgSrc}" alt="" class="switchBox-icon" />
      <label class="switch">
        <input data-mode="${cell.mode}" type="checkbox" onclick="onTypeCheckbox('${
        mTypeItem.code
      }', this, 'mTypeItem', '${mTypeIndex}', '${type}')" ${cell.checked ? "checked" : ""} ${
        cell.disabled
      }>
        <div class="slider round"></div>
      </label>
    </div>
  `
    )
    .join("")

  html += `
    <tr class="card-bottom">
      <td class="switchBox">
        ${switchBoxHtml}
      </td>
    </tr>
  `
  html += "</table>\n" + " </div>\n" + "</div>"
  return html
}

function initBreadcrumb(list) {
  let html = ""
  if (list.length) {
    list.forEach(el => {
      html += `<a class="breadcrumbText">${el.name} (${el.id}) </a>  &ensp; <span style="color: #C9C5C5">/</span> &ensp; `
    })
    $("#breadcrumb").show().html(html).children("span:last-child").remove()
    $("#breadcrumb")
      .children("a:last-child")
      .html(
        `<strong><a class="breadcrumbText">${list[list.length - 1].name} (${
          list[list.length - 1].id
        })</a><strong>`
      )
  } else {
    $("#breadcrumb").hide()
  }
}
