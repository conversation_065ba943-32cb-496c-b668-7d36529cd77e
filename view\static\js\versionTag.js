//立即执行函数
const VersionTag = {
  props: {
    version: ""
  },
  data() {
    return {
      version_diff_warp: {
        position: "absolute",
        left: 0,
        display: "flex",
        alignItems: "center",
        zIndex: 1,
        bottom: "10%",
        backgroundColor: "var(--styleColor,#d24735)",
        minHeight: "40px",
        color: "white",
        fontSize: "20px",
        padding: "0 10px",
        boxSizing: "content-box",
        borderBottomRightRadius: "10px",
        borderTopRightRadius: "10px"
      },
      container_border: {
        position: "fixed",
        left: 0,
        top: 0,
        width: "100%",
        height: "100%",
        borderRight: "5px solid var(--styleColor,#d24735)",
        borderLeft: "5px solid var(--styleColor,#d24735)",
        zIndex: 555,
        boxSizing: "border-box",
        pointerEvents: "none"
      },
      versionNumber: ""
    }
  },
  template: `<div  v-if='!!versionNumber' :style='container_border'>
                <!-- 标识 区别版本标识   -->
                <div :style='version_diff_warp' v-html='versionTag' class="version-number">
                </div>
             </div>`,

  mounted() {
    this.$nextTick(() => {
      let version = this.version || sessionStorage.getItem("versionNumber")
      this.versionNumber = version
    })
  },
  computed: {
    versionTag() {
      let version = this.versionNumber
      switch (version) {
        case "0":
          return "UAT"
        case "PROD":
          return "PROD"
        default:
          version = +version
          let dataTime = new Date(version)
          let date = dataTime.toLocaleString("default", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit"
          })
          date = date.replace(new RegExp("/", "g"), "-")
          let time = dataTime.toLocaleTimeString("default")
          if (dataTime !== "Invalid Date") {
            return `${date} <br/> ${time}`
          } else {
            return "HISTORY"
          }
      }
    }
  }
}
