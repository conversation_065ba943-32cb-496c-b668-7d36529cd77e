/**
 * TinyMCE version 6.1.0 (2022-06-29)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,x=e=>e,w=(e,t)=>e===t;function S(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),C=e=>()=>{throw new Error(e)},O=e=>e(),_=y(!1),T=y(!0);var E=tinymce.util.Tools.resolve("tinymce.ThemeManager");class B{constructor(e,t){this.tag=e,this.value=t}static some(e){return new B(!0,e)}static none(){return B.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?B.some(e(this.value)):B.none()}bind(e){return this.tag?e(this.value):B.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:B.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?B.some(e):B.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}B.singletonNone=new B(!1);const M=Array.prototype.slice,A=Array.prototype.indexOf,D=Array.prototype.push,F=(e,t)=>A.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?B.none():B.some(o)},R=(e,t)=>F(e,t)>-1,V=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},z=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},H=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},P=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},L=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},W=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return B.some(s);if(o(s,n))break}return B.none()})(e,t,_),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return B.some(o);return B.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);D.apply(t,e[o])}return t},X=(e,t)=>q(P(e,t)),K=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>W(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?B.some(e[t]):B.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return B.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>(le(e,((e,s)=>{(t(e,s)?o:n)(e,s)})),{}),ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return B.some(r)}return B.none()},fe=e=>pe(e,x),be=(e,t)=>ve(e,t)?B.from(e[t]):B.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=w)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?B.some(o(e.getOrDie(),t.getOrDie())):B.none(),ke=(e,t)=>e?B.some(t):B.none(),Ce=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Oe=(e,t)=>-1!==e.indexOf(t),_e=(e,t)=>Ce(e,t,e.length-t.length),Te=(Re=/^\s+|\s+$/g,e=>e.replace(Re,"")),Ee=e=>e.length>0,Be=e=>void 0!==e.style&&p(e.style.getPropertyValue),Me=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Ae=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Me(o.childNodes[0])},De=(e,t)=>{const o=(t||document).createElement(e);return Me(o)},Fe=(e,t)=>{const o=(t||document).createTextNode(e);return Me(o)},Ie=Me;var Re;"undefined"!=typeof window?window:Function("return this;")();const Ve=e=>e.dom.nodeName.toLowerCase(),ze=e=>t=>(e=>e.dom.nodeType)(t)===e,He=ze(1),Pe=ze(3),Ne=ze(9),Le=ze(11),We=e=>t=>He(t)&&Ve(t)===e,Ue=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},je=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Ge=(e,t)=>e.dom===t.dom,$e=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},qe=e=>Ie(e.dom.ownerDocument),Xe=e=>Ne(e)?e:qe(e),Ke=e=>Ie(Xe(e).dom.documentElement),Ye=e=>Ie(Xe(e).dom.defaultView),Je=e=>B.from(e.dom.parentNode).map(Ie),Ze=e=>B.from(e.dom.parentElement).map(Ie),Qe=e=>B.from(e.dom.offsetParent).map(Ie),et=e=>P(e.dom.childNodes,Ie),tt=(e,t)=>{const o=e.dom.childNodes;return B.from(o[t]).map(Ie)},ot=(e,t)=>({element:e,offset:t}),nt=(e,t)=>{const o=et(e);return o.length>0&&t<o.length?ot(o[t],0):ot(e,t)},st=e=>Le(e)&&g(e.dom.host),rt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),at=y(rt),it=rt?e=>Ie(e.dom.getRootNode()):Xe,lt=e=>st(e)?e:Ie(Xe(e).dom.body),ct=e=>{const t=it(e);return st(t)?B.some(t):B.none()},dt=e=>Ie(e.dom.host),ut=e=>{const t=Pe(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return ct(Ie(t)).fold((()=>o.body.contains(t)),(n=ut,s=dt,e=>n(s(e))));var n,s},mt=()=>gt(Ie(document)),gt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Ie(t)},pt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ht=(e,t,o)=>{pt(e.dom,t,o)},ft=(e,t)=>{const o=e.dom;le(t,((e,t)=>{pt(o,t,e)}))},bt=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},vt=(e,t)=>B.from(bt(e,t)),yt=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},xt=(e,t)=>{e.dom.removeAttribute(t)},wt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Be(e)&&e.style.setProperty(t,o)},St=(e,t)=>{Be(e)&&e.style.removeProperty(t)},kt=(e,t,o)=>{const n=e.dom;wt(n,t,o)},Ct=(e,t)=>{const o=e.dom;le(t,((e,t)=>{wt(o,t,e)}))},Ot=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{St(o,t)}),(e=>{wt(o,t,e)}))}))},_t=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||ut(e)?n:Tt(o,t)},Tt=(e,t)=>Be(e)?e.style.getPropertyValue(t):"",Et=(e,t)=>{const o=e.dom,n=Tt(o,t);return B.from(n).filter((e=>e.length>0))},Bt=e=>{const t={},o=e.dom;if(Be(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Mt=(e,t,o)=>{const n=De(e);return kt(n,t,o),Et(n,t).isSome()},At=(e,t)=>{const o=e.dom;St(o,t),xe(vt(e,"style").map(Te),"")&&xt(e,"style")},Dt=e=>e.dom.offsetWidth,Ft=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=_t(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=_t(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Be(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},It=Ft("height",(e=>{const t=e.dom;return ut(e)?t.getBoundingClientRect().height:t.offsetHeight})),Rt=e=>It.get(e),Vt=e=>It.getOuter(e),zt=(e,t)=>({left:e,top:t,translate:(o,n)=>zt(e+o,t+n)}),Ht=zt,Pt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Nt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return Ht(o.offsetLeft,o.offsetTop);const r=Pt(null==n?void 0:n.pageYOffset,s.scrollTop),a=Pt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Pt(s.clientTop,o.clientTop),l=Pt(s.clientLeft,o.clientLeft);return Lt(e).translate(a-l,r-i)},Lt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Ht(o.offsetLeft,o.offsetTop):ut(e)?(e=>{const t=e.getBoundingClientRect();return Ht(t.left,t.top)})(t):Ht(0,0)},Wt=Ft("width",(e=>e.dom.offsetWidth)),Ut=e=>Wt.get(e),jt=e=>Wt.getOuter(e),Gt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},$t=()=>qt(0,0),qt=(e,t)=>({major:e,minor:t}),Xt={nu:qt,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?$t():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return qt(n(1),n(2))})(e,o)},unknown:$t},Kt=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},Yt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Jt=e=>t=>Oe(t,e),Zt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Oe(e,"edge/")&&Oe(e,"chrome")&&Oe(e,"safari")&&Oe(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Yt],search:e=>Oe(e,"chrome")&&!Oe(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Oe(e,"msie")||Oe(e,"trident")},{name:"Opera",versionRegexes:[Yt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Jt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Jt("firefox")},{name:"Safari",versionRegexes:[Yt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Oe(e,"safari")||Oe(e,"mobile/"))&&Oe(e,"applewebkit")}],Qt=[{name:"Windows",search:Jt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Oe(e,"iphone")||Oe(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Jt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Jt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Jt("linux"),versionRegexes:[]},{name:"Solaris",search:Jt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Jt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Jt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],eo={browsers:y(Zt),oses:y(Qt)},to="Edge",oo="Chromium",no="Opera",so="Firefox",ro="Safari",ao=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(to),isChromium:n(oo),isIE:n("IE"),isOpera:n(no),isFirefox:n(so),isSafari:n(ro)}},io=()=>ao({current:void 0,version:Xt.unknown()}),lo=ao,co=(y(to),y(oo),y("IE"),y(no),y(so),y(ro),"Windows"),uo="Android",mo="Linux",go="macOS",po="Solaris",ho="FreeBSD",fo="ChromeOS",bo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(co),isiOS:n("iOS"),isAndroid:n(uo),isMacOS:n(go),isLinux:n(mo),isSolaris:n(po),isFreeBSD:n(ho),isChromeOS:n(fo)}},vo=()=>bo({current:void 0,version:Xt.unknown()}),yo=bo,xo=(y(co),y("iOS"),y(uo),y(mo),y(go),y(po),y(ho),y(fo),e=>window.matchMedia(e).matches);let wo=Gt((()=>((e,t,o)=>{const n=eo.browsers(),s=eo.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Xt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Kt(e,t).map((e=>{const o=Xt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(io,lo),a=((e,t)=>Kt(e,t).map((e=>{const o=Xt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(vo,yo),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(s),isiPhone:y(r),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,B.from(navigator.userAgentData),xo)));const So=()=>wo(),ko=e=>{const t=Ie((e=>{if(at()&&g(e.target)){const t=Ie(e.target);if(He(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return B.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=v(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Co=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(ko(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:S(Oo,e,t,r,s)}},Oo=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},_o=(e,t)=>{Je(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},To=(e,t)=>{const o=(e=>B.from(e.dom.nextSibling).map(Ie))(e);o.fold((()=>{Je(e).each((e=>{Bo(e,t)}))}),(e=>{_o(e,t)}))},Eo=(e,t)=>{const o=(e=>tt(e,0))(e);o.fold((()=>{Bo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Bo=(e,t)=>{e.dom.appendChild(t.dom)},Mo=(e,t)=>{N(t,(t=>{Bo(e,t)}))},Ao=e=>{e.dom.textContent="",N(et(e),(e=>{Do(e)}))},Do=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Fo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Ht(o,n)},Io=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},Ro=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Vo=e=>{const t=void 0===e?window:e,o=t.document,n=Fo(Ie(o));return(e=>{const t=void 0===e?window:e;return So().browser.isFirefox()?B.none():B.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return Ro(n.left,n.top,o,s)}),(e=>Ro(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},zo=()=>Ie(document),Ho=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=Ho(e,o);return[t].concat(n)}));var Po=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?B.none():B.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Ie)},owner:e=>qe(e)});const No=e=>{const t=zo(),o=Fo(t),n=((e,t)=>{const o=t.owner(e),n=Ho(t,o);return B.some(n)})(e,Po);return n.fold(S(Nt,e),(t=>{const n=Lt(e),s=U(t,((e,t)=>{const o=Lt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Ht(s.left+n.left+o.left,s.top+n.top+o.top)}))},Lo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Wo=e=>{const t=Nt(e),o=jt(e),n=Vt(e);return Lo(t.left,t.top,o,n)},Uo=e=>{const t=No(e),o=jt(e),n=Vt(e);return Lo(t.left,t.top,o,n)},jo=()=>Vo(window),Go=e=>{const t=t=>t(e),o=y(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:T,isError:_,map:t=>qo.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>B.some(e)};return s},$o=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:_,isError:T,map:t,mapError:t=>qo.error(t(e)),bind:t,exists:_,forall:T,getOr:x,or:x,getOrThunk:O,orThunk:O,getOrDie:C(String(e)),each:b,toOptional:B.none};return o},qo={value:Go,error:$o,fromOption:(e,t)=>e.fold((()=>$o(t)),Go)};var Xo;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(Xo||(Xo={}));const Ko=(e,t,o)=>e.stype===Xo.Error?t(e.serror):o(e.svalue),Yo=e=>({stype:Xo.Value,svalue:e}),Jo=e=>({stype:Xo.Error,serror:e}),Zo=Yo,Qo=Jo,en=Ko,tn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),on=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},nn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},sn=nn(((e,t)=>i(e)&&i(t)?sn(e,t):t)),rn=nn(((e,t)=>t)),an=e=>({tag:"defaultedThunk",process:e}),ln=e=>an(y(e)),cn=e=>({tag:"mergeWithThunk",process:e}),dn=e=>{const t=(e=>{const t=[],o=[];return N(e,(e=>{Ko(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,v(Qo,q)(o)):Zo(t.values);var o},un=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),mn=(e,t)=>Qo([{path:e,getErrorInfo:t}]),gn=e=>({extract:(t,o)=>{return n=e(o),s=e=>((e,t)=>mn(e,y(t)))(t,e),n.stype===Xo.Error?s(n.serror):n;var n,s},toString:y("val")}),pn=gn(Zo),hn=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),fn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>Zo(B.none())),(e=>{const o=s.extract(t.concat([n]),e);return r=o,a=B.some,r.stype===Xo.Value?{stype:Xo.Value,svalue:a(r.svalue)}:r;var r,a}));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>mn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+un(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return hn(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return hn(o,n,y({}),(t=>{const n=sn(e.process(o),t);return r(n)}))}},bn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),vn=e=>ae(ge(e,g)),yn=e=>{const t=xn(e),o=U(e,((e,t)=>on(t,(t=>sn(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:vn(n),r=W(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>mn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},xn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)on(r,((o,r,a,i)=>{const l=fn(a,e,t,o,i);en(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?Qo(s):Zo(n)})(t,o,e),toString:()=>{const t=P(e,(e=>on(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),wn=e=>({extract:(t,o)=>{const n=P(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return dn(n)},toString:()=>"array("+e.toString()+")"}),Sn=e=>({extract:(t,o)=>{const n=[];for(const s of e){const e=s.extract(t,o);if(e.stype===Xo.Value)return e;n.push(e)}return dn(n)},toString:()=>"oneOf("+P(e,(e=>e.toString())).join(", ")+")"}),kn=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>wn(gn(e)).extract(t,o))(o,s);return i=e=>{const s=P(e,(e=>tn(e,e,{tag:"required",process:{}},t)));return xn(s).extract(o,n)},(a=r).stype===Xo.Value?i(a.svalue):a;var a,i},toString:()=>"setOf("+t.toString()+")"}),Cn=v(wn,xn),On=y(pn),_n=(e,t)=>gn((o=>{const n=typeof o;return e(o)?Zo(o):Qo(`Expected type: ${t} but got: ${n}`)})),Tn=_n(h,"number"),En=_n(r,"string"),Bn=_n(d,"boolean"),Mn=_n(p,"function"),An=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>An(e[t])));default:return!1}},Dn=gn((e=>An(e)?Zo(e):Qo("Expected value to be acceptable for sending via postMessage"))),Fn=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>mn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>mn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+un(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),In=e=>gn((t=>e(t).fold(Qo,Zo))),Rn=(e,t)=>kn((t=>e(t).fold(Jo,Yo)),t),Vn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===Xo.Error?{stype:Xo.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),Ko(n,qo.error,qo.value);var n},zn=e=>e.fold((e=>{throw new Error(Pn(e))}),x),Hn=(e,t,o)=>zn(Vn(e,t,o)),Pn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return P(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+un(e.input),Nn=(e,t)=>Fn(e,ce(t,xn)),Ln=tn,Wn=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),Un=e=>In((t=>R(e,t)?qo.value(t):qo.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),jn=e=>Ln(e,e,{tag:"required",process:{}},On()),Gn=(e,t)=>Ln(e,e,{tag:"required",process:{}},t),$n=e=>Gn(e,Tn),qn=e=>Gn(e,En),Xn=(e,t)=>Ln(e,e,{tag:"required",process:{}},Un(t)),Kn=e=>Gn(e,Mn),Yn=(e,t)=>Ln(e,e,{tag:"required",process:{}},xn(t)),Jn=(e,t)=>Ln(e,e,{tag:"required",process:{}},Cn(t)),Zn=(e,t)=>Ln(e,e,{tag:"required",process:{}},wn(t)),Qn=e=>Ln(e,e,{tag:"option",process:{}},On()),es=(e,t)=>Ln(e,e,{tag:"option",process:{}},t),ts=e=>es(e,Tn),os=e=>es(e,En),ns=(e,t)=>es(e,Un(t)),ss=e=>es(e,Mn),rs=(e,t)=>es(e,wn(t)),as=(e,t)=>es(e,xn(t)),is=(e,t)=>Ln(e,e,ln(t),On()),ls=(e,t,o)=>Ln(e,e,ln(t),o),cs=(e,t)=>ls(e,t,Tn),ds=(e,t)=>ls(e,t,En),us=(e,t,o)=>ls(e,t,Un(o)),ms=(e,t)=>ls(e,t,Bn),gs=(e,t)=>ls(e,t,Mn),ps=(e,t,o)=>ls(e,t,wn(o)),hs=(e,t,o)=>ls(e,t,xn(o)),fs=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},bs=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!K(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};bs([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const vs=(e,t)=>((e,t)=>({[e]:t}))(e,t),ys=e=>(e=>{const t={};return N(e,(e=>{t[e.key]=e.value})),t})(e),xs=e=>p(e)?e:_,ws=(e,t,o)=>{let n=e.dom;const s=xs(o);for(;n.parentNode;){n=n.parentNode;const e=Ie(n),o=t(e);if(o.isSome())return o;if(s(e))break}return B.none()},Ss=(e,t,o)=>{const n=t(e),s=xs(o);return n.orThunk((()=>s(e)?B.none():ws(e,t,s)))},ks=(e,t)=>Ge(e.element,t.event.target),Cs={can:T,abort:_,run:b},Os=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Cs,...e}},_s=y,Ts=_s("touchstart"),Es=_s("touchmove"),Bs=_s("touchend"),Ms=_s("touchcancel"),As=_s("mousedown"),Ds=_s("mousemove"),Fs=_s("mouseout"),Is=_s("mouseup"),Rs=_s("mouseover"),Vs=_s("focusin"),zs=_s("focusout"),Hs=_s("keydown"),Ps=_s("keyup"),Ns=_s("input"),Ls=_s("change"),Ws=_s("click"),Us=_s("transitioncancel"),js=_s("transitionend"),Gs=_s("transitionstart"),$s=_s("selectstart"),qs=e=>y("alloy."+e),Xs={tap:qs("tap")},Ks=qs("focus"),Ys=qs("blur.post"),Js=qs("paste.post"),Zs=qs("receive"),Qs=qs("execute"),er=qs("focus.item"),tr=Xs.tap,or=qs("longpress"),nr=qs("sandbox.close"),sr=qs("typeahead.cancel"),rr=qs("system.init"),ar=qs("system.touchmove"),ir=qs("system.touchend"),lr=qs("system.scroll"),cr=qs("system.resize"),dr=qs("system.attached"),ur=qs("system.detached"),mr=qs("system.dismissRequested"),gr=qs("system.repositionRequested"),pr=qs("focusmanager.shifted"),hr=qs("slotcontainer.visibility"),fr=qs("change.tab"),br=qs("dismiss.tab"),vr=qs("highlight"),yr=qs("dehighlight"),xr=(e,t)=>{Cr(e,e.element,t,{})},wr=(e,t,o)=>{Cr(e,e.element,t,o)},Sr=e=>{xr(e,Qs())},kr=(e,t,o)=>{Cr(e,t,o,{})},Cr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Or=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},_r=e=>ys(e),Tr=(e,t)=>({key:e,value:Os({abort:t})}),Er=e=>({key:e,value:Os({run:(e,t)=>{t.event.prevent()}})}),Br=(e,t)=>({key:e,value:Os({run:t})}),Mr=(e,t,o)=>({key:e,value:Os({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Ar=e=>t=>({key:e,value:Os({run:(e,o)=>{ks(e,o)&&t(e,o)}})}),Dr=(e,t,o)=>((e,t)=>Br(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Or(t,t.element,e,n)}))})))(e,t.partUids[o]),Fr=(e,t)=>Br(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Ss(n.target,(t=>e.getSystem().getByDom(t).toOptional()),_).getOr(e)));t(e,s,o)})),Ir=e=>Br(e,((e,t)=>{t.cut()})),Rr=e=>Br(e,((e,t)=>{t.stop()})),Vr=(e,t)=>Ar(e)(t),zr=Ar(dr()),Hr=Ar(ur()),Pr=Ar(rr()),Nr=($r=Qs(),e=>Br($r,e)),Lr=e=>e.dom.innerHTML,Wr=(e,t)=>{const o=qe(e).dom,n=Ie(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,et(Ie(o))})(t,o);Mo(n,s),Ao(e),Bo(e,n)},Ur=e=>st(e)?"#shadow-root":(e=>{const t=De("div"),o=Ie(e.dom.cloneNode(!0));return Bo(t,o),Lr(t)})((e=>((e,t)=>Ie(e.dom.cloneNode(!1)))(e))(e)),jr=e=>Ur(e),Gr=_r([((e,t)=>({key:e,value:Os({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Ge(t,e.element)&&!Ge(t,o))(e,n,s)||(console.warn(Ks()+" did not get interpreted by the desired target. \nOriginator: "+jr(n)+"\nTarget: "+jr(s)+"\nCheck the "+Ks()+" event handlers"),!1)}})}))(Ks())]);var $r,qr=Object.freeze({__proto__:null,events:Gr});let Xr=0;const Kr=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return Xr++,e+"_"+o+Xr+String(t)},Yr=y("alloy-id-"),Jr=y("data-alloy-id"),Zr=Yr(),Qr=Jr(),ea=(e,t)=>{Object.defineProperty(e.dom,Qr,{value:t,writable:!0})},ta=e=>{const t=He(e)?e.dom[Qr]:null;return B.from(t)},oa=e=>Kr(e),na=x,sa=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+jr(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:_}},ra=sa(),aa=e=>P(e,(e=>_e(e,"/*")?e.substring(0,e.length-"/*".length):e)),ia=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:aa(r)}),e},la=Kr("alloy-premade"),ca=e=>(Object.defineProperty(e.element.dom,la,{value:e.uid,writable:!0}),vs(la,e)),da=e=>be(e,la),ua=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:aa(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),ma={init:()=>ga({readState:y("No State required")})},ga=e=>e,pa=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},ha=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),fa=e=>e.cHandler,ba=(e,t)=>({name:e,handler:t}),va=(e,t)=>{const o={};return N(e,(e=>{o[e.name()]=e.handlers(t)})),o},ya=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const t=ee(o,((t,o)=>{const s=t.name,r=o.name,a=n.indexOf(s),i=n.indexOf(r);if(-1===a)throw new Error("The ordering for "+e+" does not have an entry for "+s+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));return a<i?-1:i<a?1:0}));return qo.value(t)}catch(e){return qo.error([e])}})("Event: "+o,0,e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{N(e,(e=>{e.run.apply(void 0,t)}))}}})(P(e,(e=>e.handler))))):((e,t)=>qo.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(P(t,(e=>e.name)),null,2)]))(o,e)},xa=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return N(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,qo.error(q(n))):((e,t)=>0===e.length?qo.value(t):qo.value(sn(t,rn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?qo.value(e[0].handler):ya(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:T,abort:_,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?W(t[o],(t=>V(e,(e=>e.name===t)))).join(" > "):e[0].name;return vs(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),wa="alloy.base.behaviour",Sa=xn([Ln("dom","dom",{tag:"required",process:{}},xn([jn("tag"),is("styles",{}),is("classes",[]),is("attributes",{}),Qn("value"),Qn("innerHtml")])),jn("components"),jn("uid"),is("events",{}),is("apis",{}),Ln("eventOrder","eventOrder",(qa={[Qs()]:["disabling",wa,"toggling","typeaheadevents"],[Ks()]:[wa,"focusing","keying"],[rr()]:[wa,"disabling","toggling","representing"],[Ns()]:[wa,"representing","streaming","invalidating"],[ur()]:[wa,"representing","item-events","tooltipping"],[As()]:["focusing",wa,"item-type-events"],[Ts()]:["focusing",wa,"item-type-events"],[Rs()]:["item-type-events","tooltipping"],[Zs()]:["receiving","reflecting","tooltipping"]},cn(y(qa))),On()),Qn("domModification")]),ka=e=>e.events,Ca=(e,t)=>{const o=bt(e,t);return void 0===o||""===o?[]:o.split(" ")},Oa=e=>void 0!==e.dom.classList,_a=e=>Ca(e,"class"),Ta=(e,t)=>{Oa(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=Ca(e,t).concat([o]);ht(e,t,n.join(" "))})(e,"class",t)})(e,t)},Ea=(e,t)=>{Oa(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=W(Ca(e,t),(e=>e!==o));n.length>0?ht(e,t,n.join(" ")):xt(e,t)})(e,"class",t)})(e,t),(e=>{0===(Oa(e)?e.dom.classList:_a(e)).length&&xt(e,"class")})(e)},Ba=(e,t)=>Oa(e)&&e.dom.classList.contains(t),Ma=(e,t)=>{N(t,(t=>{Ta(e,t)}))},Aa=(e,t)=>{N(t,(t=>{Ea(e,t)}))},Da=e=>e.dom.value,Fa=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Ia=(e,t,o)=>{o.fold((()=>Bo(e,t)),(e=>{Ge(e,t)||(_o(e,t),Do(e))}))},Ra=(e,t,o)=>{const n=P(t,o),s=et(e);return N(s.slice(n.length),Do),n},Va=(e,t,o,n)=>{const s=tt(e,t),r=n(o,s),a=((e,t,o)=>tt(e,t).map((e=>{if(o.exists((t=>!Ge(t,e)))){const t=o.map(Ve).getOr("span"),n=De(t);return _o(e,n),n}return e})))(e,t,s);return Ia(e,r.element,a),r},za=(e,t)=>{const o=ae(e),n=ae(t);return{toRemove:J(n,o),toSet:((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t}},Ha=(e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=za(e.attributes,s),i=Bt(t),{toSet:l,toRemove:c}=za(e.styles,i),d=(e=>Oa(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):_a(e))(t),u=J(d,e.classes),m=J(e.classes,d);return N(a,(e=>xt(t,e))),ft(t,r),Ma(t,m),Aa(t,u),N(c,(e=>At(t,e))),Ct(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Ra(e,t,((t,o)=>{const n=tt(e,o);return Ia(e,t,n),t}))})(t,o)}),(e=>{Wr(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==Da(o)&&Fa(o,null!=n?n:"")})(),t},Pa=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=P(t,(e=>as(e.name(),[jn("config"),is("state",ma)]))),n=Vn("component.behaviours",xn(o),e.behaviours).fold((t=>{throw new Error(Pn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),x);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},Na=(e,t)=>{const o=()=>m,n=fs(ra),s=zn((e=>Vn("custom.definition",Sa,e))(e)),r=Pa(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:P(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>ha({})),ha))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};N(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=pa(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return ha({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=((e,t)=>{const o=t.filter((t=>Ve(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,la))(t))).bind((t=>((e,t)=>{try{const o=Ha(e,t);return B.some(o)}catch(e){return B.none()}})(e,t))).getOrThunk((()=>(e=>{const t=De(e.tag);ft(t,e.attributes),Ma(t,e.classes),Ct(t,e.styles),e.innerHtml.each((e=>Wr(t,e)));const o=e.domChildren;return Mo(t,o),e.value.each((e=>{Fa(t,e)})),t})(e)));return ea(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":ka(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...va(t,e)};return pa(n,ba)})(e,o,n);return xa(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=fs(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(sa(o))},element:c,syncComponents:()=>{const e=et(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},La=e=>{const t=Fe(e);return Wa({element:t})},Wa=e=>{const t=Hn("external.component",yn([jn("element"),Qn("uid")]),e),o=fs(sa()),n=t.uid.getOrThunk((()=>oa("external")));ea(t.element,n);const s={uid:n,getSystem:o.get,config:B.none,hasConfigured:_,connect:e=>{o.set(e)},disconnect:()=>{o.set(sa((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return ca(s)},Ua=oa,ja=(e,t)=>da(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=na(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>P(o,Ga)),(e=>P(o,((t,o)=>ja(t,tt(e,o))))))})(n,t),r={...n,events:{...qr,...o},components:s};return qo.value(Na(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:Ua(""),...e},t).getOrDie())),Ga=e=>ja(e,B.none()),$a=ca;var qa,Xa=(e,t,o,n,s)=>e(o,n)?B.some(o):p(s)&&s(o)?B.none():t(o,n,s);const Ka=(e,t,o)=>{let n=e.dom;const s=p(o)?o:_;for(;n.parentNode;){n=n.parentNode;const e=Ie(n);if(t(e))return B.some(e);if(s(e))break}return B.none()},Ya=(e,t,o)=>Xa(((e,t)=>t(e)),Ka,e,t,o),Ja=(e,t,o)=>Ya(e,t,o).isSome(),Za=(e,t,o)=>Ka(e,(e=>Ue(e,t)),o),Qa=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Ie(e),Ue(o,t);var o})).map(Ie))(e),ei=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return je(o)?B.none():B.from(o.querySelector(e)).map(Ie)})(t,e),ti=(e,t,o)=>Xa(((e,t)=>Ue(e,t)),Za,e,t,o),oi="aria-controls",ni=()=>{const e=Kr(oi);return{id:e,link:t=>{ht(t,oi,e)},unlink:e=>{xt(e,oi)}}},si=(e,t)=>Ja(t,(t=>Ge(t,e.element)),_)||((e,t)=>(e=>Ya(e,(e=>{if(!He(e))return!1;const t=bt(e,"id");return void 0!==t&&t.indexOf(oi)>-1})).bind((e=>{const t=bt(e,"id"),o=it(e);return ei(o,`[${oi}="${t}"]`)})))(t).exists((t=>si(e,t))))(e,t);var ri;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ri||(ri={}));const ai=fs({}),ii=["alloy/data/Fields","alloy/debugging/Debugging"],li=(e,t,o)=>((e,t,o)=>{switch(be(ai.get(),e).orThunk((()=>{const t=ae(ai.get());return re(t,(t=>e.indexOf(t)>-1?B.some(ai.get()[t]):B.none()))})).getOr(ri.NORMAL)){case ri.NORMAL:return o(ci());case ri.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();R(["mousemove","mouseover","mouseout",rr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:P(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+jr(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case ri.STOP:return!0}})(e,t,o),ci=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),di=y([jn("menu"),jn("selectedMenu")]),ui=y([jn("item"),jn("selectedItem")]);y(xn(ui().concat(di())));const mi=y(xn(ui())),gi=Yn("initSize",[jn("numColumns"),jn("numRows")]),pi=()=>Yn("markers",[jn("backgroundMenu")].concat(di()).concat(ui())),hi=e=>Yn("markers",P(e,jn)),fi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!V(ii,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Ln(t,t,o,In((e=>qo.value(((...t)=>e.apply(void 0,t))))))),bi=e=>fi(0,e,ln(b)),vi=e=>fi(0,e,ln(B.none)),yi=e=>fi(0,e,{tag:"required",process:{}}),xi=e=>fi(0,e,{tag:"required",process:{}}),wi=(e,t)=>Wn(e,y(t)),Si=e=>Wn(e,x),ki=y(gi),Ci=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Oi=bs([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),_i=Oi.southeast,Ti=Oi.southwest,Ei=Oi.northeast,Bi=Oi.northwest,Mi=Oi.south,Ai=Oi.north,Di=Oi.east,Fi=Oi.west,Ii=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Ri=(e,t,o)=>Math.min(Math.max(e,t),o),Vi=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),zi="layout",Hi=e=>e.x,Pi=(e,t)=>e.x+e.width/2-t.width/2,Ni=(e,t)=>e.x+e.width-t.width,Li=(e,t)=>e.y-t.height,Wi=e=>e.y+e.height,Ui=(e,t)=>e.y+e.height/2-t.height/2,ji=(e,t,o)=>Ci(Hi(e),Wi(e),o.southeast(),_i(),"southeast",Vi(e,{left:1,top:3}),zi),Gi=(e,t,o)=>Ci(Ni(e,t),Wi(e),o.southwest(),Ti(),"southwest",Vi(e,{right:0,top:3}),zi),$i=(e,t,o)=>Ci(Hi(e),Li(e,t),o.northeast(),Ei(),"northeast",Vi(e,{left:1,bottom:2}),zi),qi=(e,t,o)=>Ci(Ni(e,t),Li(e,t),o.northwest(),Bi(),"northwest",Vi(e,{right:0,bottom:2}),zi),Xi=(e,t,o)=>Ci(Pi(e,t),Li(e,t),o.north(),Ai(),"north",Vi(e,{bottom:2}),zi),Ki=(e,t,o)=>Ci(Pi(e,t),Wi(e),o.south(),Mi(),"south",Vi(e,{top:3}),zi),Yi=(e,t,o)=>Ci((e=>e.x+e.width)(e),Ui(e,t),o.east(),Di(),"east",Vi(e,{left:0}),zi),Ji=(e,t,o)=>Ci(((e,t)=>e.x-t.width)(e,t),Ui(e,t),o.west(),Fi(),"west",Vi(e,{right:1}),zi),Zi=()=>[ji,Gi,$i,qi,Ki,Xi,Yi,Ji],Qi=()=>[Gi,ji,qi,$i,Ki,Xi,Yi,Ji],el=()=>[$i,qi,ji,Gi,Xi,Ki],tl=()=>[qi,$i,Gi,ji,Xi,Ki],ol=()=>[ji,Gi,$i,qi,Ki,Xi],nl=()=>[Gi,ji,qi,$i,Ki,Xi];var sl=Object.freeze({__proto__:null,events:e=>_r([Br(Zs(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:W(e,(e=>R(t.channels,e))))(s,r);N(a,(e=>{const o=n[e],s=o.schema,a=Hn("channel["+e+"] data\nReceiver: "+jr(t.element),s,r.data);o.onReceive(t,a)}))}))])}),rl=[Gn("channels",Rn(qo.value,yn([yi("onReceive"),is("schema",On())])))];const al=(e,t,o)=>Pr(((n,s)=>{o(n,e,t)})),il=e=>({key:e,value:void 0}),ll=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():B.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:aa(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>ia(e,t))),...l,revoke:S(il,o),config:t=>{const n=Hn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Gt((()=>Hn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>ha({}))),name:y(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},cl=e=>ys(e),dl=yn([jn("fields"),jn("name"),is("active",{}),is("apis",{}),is("state",ma),is("extra",{})]),ul=e=>{const t=Hn("Creating behaviour: "+e.name,dl,e);return((e,t,o,n,s,r)=>{const a=yn(e),i=as(t,[("config",l=e,es("config",yn(l)))]);var l;return ll(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},ml=yn([jn("branchKey"),jn("branches"),jn("name"),is("active",{}),is("apis",{}),is("state",ma),is("extra",{})]),gl=e=>{const t=Hn("Creating behaviour: "+e.name,ml,e);return((e,t,o,n,s,r)=>{const a=e,i=as(t,[es("config",e)]);return ll(a,i,t,o,n,s,r)})(Nn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},pl=y(void 0),hl=ul({fields:rl,name:"receiving",active:sl});var fl=Object.freeze({__proto__:null,exhibit:(e,t)=>ha({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const bl=e=>e.dom.focus(),vl=e=>{const t=it(e).dom;return e.dom===t.activeElement},yl=(e=zo())=>B.from(e.dom.activeElement).map(Ie),xl=e=>yl(it(e)).filter((t=>e.dom.contains(t.dom))),wl=(e,t)=>{const o=it(t),n=yl(o).bind((e=>{const o=t=>Ge(e,t);return o(t)?B.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=Ie(e.childNodes[n]);if(t(s))return B.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return B.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{yl(o).filter((t=>Ge(t,e))).fold((()=>{bl(e)}),b)})),s},Sl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},kl=(e,t)=>{Ot(e,(e=>({...e,position:B.some(e.position)}))(t))},Cl=bs([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Ol=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=B.some(i),m=B.some(l),g=B.some(c),p=B.some(d),h=B.none();return t.direction.fold((()=>Sl(e,u,m,h,h)),(()=>Sl(e,h,m,g,h)),(()=>Sl(e,u,h,h,p)),(()=>Sl(e,h,h,g,p)),(()=>Sl(e,u,m,h,h)),(()=>Sl(e,u,h,h,p)),(()=>Sl(e,u,m,h,h)),(()=>Sl(e,h,m,g,h)))},_l=(e,t)=>e.fold((()=>{const e=t.rect;return Sl("absolute",B.some(e.x),B.some(e.y),B.none(),B.none())}),((e,o,n,s)=>Ol("absolute",t,e,o,n,s)),((e,o,n,s)=>Ol("fixed",t,e,o,n,s))),Tl=(e,t)=>{const o=S(No,t),n=e.fold(o,o,(()=>{const e=Fo();return No(t).translate(-e.left,-e.top)})),s=jt(t),r=Vt(t);return Lo(n.left,n.top,s,r)},El=(e,t)=>t.fold((()=>e.fold(jo,jo,Lo)),(t=>e.fold(t,t,(()=>{const o=t(),n=Bl(e,o.x,o.y);return Lo(n.left,n.top,o.width,o.height)})))),Bl=(e,t,o)=>{const n=Ht(t,o);return e.fold(y(n),y(n),(()=>{const e=Fo();return n.translate(-e.left,-e.top)}))};Cl.none;const Ml=Cl.relative,Al=Cl.fixed,Dl="data-alloy-placement",Fl=e=>vt(e,Dl),Il=bs([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Rl=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Ri(i,e.y,e.bottom):Ri(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Lo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Lo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Ri(a,o,d),g=Ri(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Lo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),s=y(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Il.fit(x):Il.nofit(x,m,g,f)},Vl=e=>{const t=fs(B.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(B.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(B.some(e))}}},zl=()=>Vl((e=>e.unbind())),Hl=()=>{const e=Vl(b);return{...e,on:t=>e.get().each(t)}},Pl=T,Nl=(e,t,o)=>((e,t,o,n)=>Co(e,t,o,n,!1))(e,t,Pl,o),Ll=(e,t,o)=>((e,t,o,n)=>Co(e,t,o,n,!0))(e,t,Pl,o),Wl=ko,Ul=["top","bottom","right","left"],jl="data-alloy-transition-timer",Gl=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>K(t,(t=>Ba(e,t))))(e,t.classes))(e,n)){kt(e,"position",o.position);const a=Tl(t,e),l=_l(t,{...s,rect:a}),c=Z(Ul,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=w)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ot(e,c),i&&((e,t)=>{Ma(e,t.classes),vt(e,jl).each((t=>{clearTimeout(parseInt(t,10)),xt(e,jl)})),((e,t)=>{const o=zl(),n=zl();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Ge(t.target,e)&&!Ee(n)&&R(Ul,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===js())&&(clearTimeout(s),xt(e,jl),Aa(e,t.classes))}},l=Nl(e,Gs(),(t=>{a(t)&&(l.unbind(),o.set(Nl(e,js(),i)),n.set(Nl(e,Us(),i)))})),c=(e=>{const t=t=>{const o=_t(e,t).split(/\s*,\s*/);return W(o,Ee)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return _e(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),ht(e,jl,s)}))})(e,t)})(e,n),Dt(e))}else Aa(e,n.classes)},$l=(e,t)=>{((e,t)=>{const o=It.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);kt(e,"max-height",o+"px")})(e,Math.floor(t))},ql=y(((e,t)=>{$l(e,t),Ct(e,{"overflow-x":"hidden","overflow-y":"auto"})})),Xl=y(((e,t)=>{$l(e,t)})),Kl=(e,t,o)=>void 0===e[t]?o:e[t],Yl=(e,t,o,n)=>{const s=((e,t,o,n)=>{At(t,"max-height"),At(t,"max-width");const s={width:jt(r=t),height:Vt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Rl(m,a,i,r);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Il.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=S(l,t);return e.fold(y(e),o)}),Il.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:_i(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(x,x)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=_l(o.origin,t);o.transition.each((s=>{Gl(e,o.origin,n,s,t,o.lastPlacement)})),kl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{ht(e,Dl,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Aa(e,o.off),Ma(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},Jl=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],Zl=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(Jl,o);return{offset:Ht(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},Ql=()=>Zl(0,0,{}),ec=x,tc=(e,t)=>o=>"rtl"===oc(o)?t:e,oc=e=>"rtl"===_t(e,"direction")?"rtl":"ltr";var nc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(nc||(nc={}));const sc="data-alloy-vertical-dir",rc=e=>Ja(e,(e=>He(e)&&bt(e,"data-alloy-vertical-dir")===nc.BottomToTop)),ac=()=>as("layouts",[jn("onLtr"),jn("onRtl"),Qn("onBottomLtr"),Qn("onBottomRtl")]),ic=(e,t,o,n,s,r,a)=>{const i=a.map(rc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return tc(d,u)(e)};var lc=[jn("hotspot"),Qn("bubble"),is("overrides",{}),ac(),wi("placement",((e,t,o)=>{const n=t.hotspot,s=Tl(o,n.element),r=ic(e.element,t,ol(),nl(),el(),tl(),B.some(t.hotspot.element));return B.some(ec({anchorBox:s,bubble:t.bubble.getOr(Ql()),overrides:t.overrides,layouts:r,placer:B.none()}))}))],cc=[jn("x"),jn("y"),is("height",0),is("width",0),is("bubble",Ql()),is("overrides",{}),ac(),wi("placement",((e,t,o)=>{const n=Bl(o,t.x,t.y),s=Lo(n.left,n.top,t.width,t.height),r=ic(e.element,t,Zi(),Qi(),Zi(),Qi(),B.none());return B.some(ec({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r,placer:B.none()}))}))];const dc=bs([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),uc=e=>e.fold(x,((e,t,o)=>e.translate(-t,-o))),mc=e=>e.fold(x,x),gc=e=>j(e,((e,t)=>e.translate(t.left,t.top)),Ht(0,0)),pc=e=>{const t=P(e,mc);return gc(t)},hc=dc.screen,fc=dc.absolute,bc=(e,t,o)=>{const n=qe(e.element),s=Fo(n),r=((e,t,o)=>{const n=Ye(o.root).dom;return B.from(n.frameElement).map(Ie).filter((t=>{const o=qe(t),n=qe(e.element);return Ge(o,n)})).map(Nt)})(e,0,o).getOr(s);return fc(r,s.left,s.top)},vc=(e,t,o,n)=>{const s=hc(Ht(e,t));return B.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},yc=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>pc(r),l=()=>pc(r),c=()=>(e=>{const t=P(e,uc);return gc(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?el():ol(),m=o.showAbove?tl():nl(),g=ic(s,o,u,m,u,m,B.none());var p,h,f,b;return ec({anchorBox:d,bubble:o.bubble.getOr(Ql()),overrides:o.overrides,layouts:g,placer:B.none()})}));var xc=[jn("node"),jn("root"),Qn("bubble"),ac(),is("overrides",{}),is("showAbove",!1),wi("placement",((e,t,o)=>{const n=bc(e,0,t);return t.node.filter(ut).bind((s=>{const r=s.dom.getBoundingClientRect(),a=vc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return yc(a,n,t,o,i)}))}))];const wc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Sc=bs([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),kc=(Sc.before,Sc.on,Sc.after,e=>e.fold(x,x,x)),Cc=bs([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Oc={domRange:Cc.domRange,relative:Cc.relative,exact:Cc.exact,exactFromRange:e=>Cc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Ie(e.startContainer),relative:(e,t)=>kc(e),exact:(e,t,o,n)=>e}))(e);return Ye(t)},range:wc},_c=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Tc=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Ec=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Bc=bs([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Mc=(e,t,o)=>t(Ie(o.startContainer),o.startOffset,Ie(o.endContainer),o.endOffset),Ac=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:B.none}),relative:(t,o)=>({ltr:Gt((()=>_c(e,t,o))),rtl:Gt((()=>B.some(_c(e,o,t))))}),exact:(t,o,n,s)=>({ltr:Gt((()=>Tc(e,t,o,n,s))),rtl:Gt((()=>B.some(Tc(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Bc.rtl(Ie(e.endContainer),e.endOffset,Ie(e.startContainer),e.startOffset))).getOrThunk((()=>Mc(0,Bc.ltr,o))):Mc(0,Bc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});Bc.ltr,Bc.rtl;const Dc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return je(o)?[]:P(o.querySelectorAll(e),Ie)})(t,e),Fc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return B.some(wc(Ie(t.startContainer),t.startOffset,Ie(o.endContainer),o.endOffset))}return B.none()},Ic=e=>{if(null===e.anchorNode||null===e.focusNode)return Fc(e);{const t=Ie(e.anchorNode),o=Ie(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=qe(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Ge(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?B.some(wc(t,e.anchorOffset,o,e.focusOffset)):Fc(e)}},Rc=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?B.some(o).map(Ec):B.none()})(Ac(e,t)),Vc=((e,t)=>{const o=t=>e(t)?B.from(t.dom.nodeValue):B.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(Pe),zc=(e,t)=>({element:e,offset:t}),Hc=(e,t)=>Pe(e)?zc(e,t):((e,t)=>{const o=et(e);if(0===o.length)return zc(e,t);if(t<o.length)return zc(o[t],0);{const e=o[o.length-1],t=Pe(e)?(e=>Vc.get(e))(e).length:et(e).length;return zc(e,t)}})(e,t),Pc=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>B.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Ic))(e)))().map((e=>{const t=Hc(e.start,e.soffset),o=Hc(e.finish,e.foffset);return Oc.range(t.element,t.offset,o.element,o.offset)}));var Nc=[Qn("getSelection"),jn("root"),Qn("bubble"),ac(),is("overrides",{}),is("showAbove",!1),wi("placement",((e,t,o)=>{const n=Ye(t.root).dom,s=bc(e,0,t),r=Pc(n,t).bind((e=>{const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?B.some(t).map(Ec):B.none()})(Ac(e,t)))(n,Oc.exactFromRange(e)).orThunk((()=>{const t=Fe("\ufeff");_o(e.start,t);const o=Rc(n,Oc.exact(t,0,t,1));return Do(t),o}));return t.bind((e=>vc(e.left,e.top,e.width,e.height)))})),a=Pc(n,t).bind((e=>He(e.start)?B.some(e.start):Ze(e.start))).getOr(e.element);return yc(r,s,t,o,a)}))];const Lc="link-layout",Wc=e=>e.x+e.width,Uc=(e,t)=>e.x-t.width,jc=(e,t)=>e.y-t.height+e.height,Gc=e=>e.y,$c=(e,t,o)=>Ci(Wc(e),Gc(e),o.southeast(),_i(),"southeast",Vi(e,{left:0,top:2}),Lc),qc=(e,t,o)=>Ci(Uc(e,t),Gc(e),o.southwest(),Ti(),"southwest",Vi(e,{right:1,top:2}),Lc),Xc=(e,t,o)=>Ci(Wc(e),jc(e,t),o.northeast(),Ei(),"northeast",Vi(e,{left:0,bottom:3}),Lc),Kc=(e,t,o)=>Ci(Uc(e,t),jc(e,t),o.northwest(),Bi(),"northwest",Vi(e,{right:1,bottom:3}),Lc),Yc=()=>[$c,qc,Xc,Kc],Jc=()=>[qc,$c,Kc,Xc];var Zc=[jn("item"),ac(),is("overrides",{}),wi("placement",((e,t,o)=>{const n=Tl(o,t.item.element),s=ic(e.element,t,Yc(),Jc(),Yc(),Jc(),B.none());return B.some(ec({anchorBox:n,bubble:Ql(),overrides:t.overrides,layouts:s,placer:B.none()}))}))],Qc=Nn("type",{selection:Nc,node:xc,hotspot:lc,submenu:Zc,makeshift:cc});const ed=[Zn("classes",En),us("mode","all",["all","layout","placement"])],td=[is("useFixed",_),Qn("getBounds")],od=[Gn("anchor",Qc),as("transition",ed)],nd=(e,t,o,n,s,r,a)=>((e,t,o,n,s,r,a,i)=>{const l=Kl(a,"maxHeightFunction",ql()),c=Kl(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:El(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return Yl(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(o.anchorBox,t),s.element,o.bubble,o.layouts,r,n,o.overrides,a),sd=(e,t,o,n,s,r)=>{const a=r.map(Wo);return rd(e,t,o,n,s,a)},rd=(e,t,o,n,s,r)=>{const a=Hn("placement.info",xn(od),s),i=a.anchor,l=n.element,c=o.get(n.uid);wl((()=>{kt(l,"position","fixed");const s=Et(l,"visibility");kt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Al(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Nt(e.element),o=e.element.dom.getBoundingClientRect();return Ml(t.left,t.top,o.width,o.height)})(e),u=i.placement,m=r.map(y).or(t.getBounds);u(e,i,d).each((t=>{const s=t.placer.getOr(nd)(e,d,t,m,n,c,a.transition);o.set(n.uid,s)})),s.fold((()=>{At(l,"visibility")}),(e=>{kt(l,"visibility",e)})),Et(l,"left").isNone()&&Et(l,"top").isNone()&&Et(l,"right").isNone()&&Et(l,"bottom").isNone()&&xe(Et(l,"position"),"fixed")&&At(l,"position")}),l)};var ad=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{sd(e,t,o,n,s,B.none())},positionWithin:sd,positionWithinBounds:rd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;N(["position","left","right","top","bottom"],(e=>At(s,e))),(e=>{xt(e,Dl)})(s),o.clear(n.uid)}});const id=ul({fields:td,name:"positioning",active:fl,apis:ad,state:Object.freeze({__proto__:null,init:()=>{let e={};return ga({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),ld=e=>e.getSystem().isConnected(),cd=e=>{xr(e,ur());const t=e.components();N(t,cd)},dd=e=>{const t=e.components();N(t,dd),xr(e,dr())},ud=(e,t)=>{e.getSystem().addToWorld(t),ut(e.element)&&dd(t)},md=e=>{cd(e),e.getSystem().removeFromWorld(e)},gd=(e,t)=>{Bo(e.element,t.element)},pd=(e,t)=>{hd(e,t,Bo)},hd=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),ut(e.element)&&dd(t),e.syncComponents()},fd=e=>{cd(e),Do(e.element),e.getSystem().removeFromWorld(e)},bd=e=>{const t=Je(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));fd(e),t.each((e=>{e.syncComponents()}))},vd=e=>{const t=e.components();N(t,fd),Ao(e.element),e.syncComponents()},yd=(e,t)=>{xd(e,t,Bo)},xd=(e,t,o)=>{o(e,t.element);const n=et(t.element);N(n,(e=>{t.getByDom(e).each(dd)}))},wd=e=>{const t=et(e.element);N(t,(t=>{e.getByDom(t).each(cd)})),Do(e.element)},Sd=(e,t,o,n)=>{o.get().each((t=>{vd(e)}));const s=t.getAttachPoint(e);pd(s,e);const r=e.getSystem().build(n);return pd(e,r),o.set(r),r},kd=(e,t,o,n)=>{const s=Sd(e,t,o,n);return t.onOpen(e,s),s},Cd=(e,t,o)=>{o.get().each((n=>{vd(e),bd(e),t.onClose(e,n),o.clear()}))},Od=(e,t,o)=>o.isOpen(),_d=(e,t,o)=>{const n=t.getAttachPoint(e);kt(e.element,"position",id.getMode(n)),((e,t,o,n)=>{Et(e.element,t).fold((()=>{xt(e.element,o)}),(t=>{ht(e.element,o,t)})),kt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Td=(e,t,o)=>{(e=>V(["top","left","right","bottom"],(t=>Et(e,t).isSome())))(e.element)||At(e.element,"position"),((e,t,o)=>{vt(e.element,o).fold((()=>At(e.element,t)),(o=>kt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var Ed=Object.freeze({__proto__:null,cloak:_d,decloak:Td,open:kd,openWhileCloaked:(e,t,o,n,s)=>{_d(e,t),kd(e,t,o,n),s(),Td(e,t)},close:Cd,isOpen:Od,isPartOf:(e,t,o,n)=>Od(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>Sd(e,t,o,n)))}),Bd=Object.freeze({__proto__:null,events:(e,t)=>_r([Br(nr(),((o,n)=>{Cd(o,e,t)}))])}),Md=[bi("onOpen"),bi("onClose"),jn("isPartOf"),jn("getAttachPoint"),is("cloakVisibilityAttr","data-precloak-visibility")],Ad=Object.freeze({__proto__:null,init:()=>{const e=Hl(),t=y("not-implemented");return ga({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Dd=ul({fields:Md,name:"sandboxing",active:Bd,apis:Ed,state:Ad}),Fd=y("dismiss.popups"),Id=y("reposition.popups"),Rd=y("mouse.released"),Vd=yn([is("isExtraPart",_),as("fireEventInstead",[is("event",mr())])]),zd=e=>{const t=Hn("Dismissal",Vd,e);return{[Fd()]:{schema:yn([jn("target")]),onReceive:(e,o)=>{Dd.isOpen(e)&&(Dd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Dd.close(e)),(t=>xr(e,t.event))))}}}},Hd=yn([as("fireEventInstead",[is("event",gr())]),Kn("doReposition")]),Pd=e=>{const t=Hn("Reposition",Hd,e);return{[Id()]:{onReceive:e=>{Dd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>xr(e,t.event)))}}}},Nd=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Ld=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var Wd=Object.freeze({__proto__:null,onLoad:Nd,onUnload:Ld,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),Ud=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[zr(((o,n)=>{Nd(o,e,t)})),Hr(((o,n)=>{Ld(o,e,t)}))]:[al(e,t,Nd)];return _r(o)}});const jd=()=>{const e=fs(null);return ga({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Gd=()=>{const e=fs({}),t=fs({});return ga({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};N(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var $d=Object.freeze({__proto__:null,memory:jd,dataset:Gd,manual:()=>ga({readState:b}),init:e=>e.store.manager.state(e)});const qd=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var Xd=[Qn("initialValue"),jn("getFallbackEntry"),jn("getDataKey"),jn("setValue"),wi("manager",{setValue:qd,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{qd(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Gd})],Kd=[jn("getValue"),is("setValue",b),Qn("initialValue"),wi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:ma.init})],Yd=[Qn("initialValue"),wi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:jd})],Jd=[ls("store",{mode:"memory"},Nn("mode",{memory:Yd,manual:Kd,dataset:Xd})),bi("onSetValue"),is("resetOnDom",!1)];const Zd=ul({fields:Jd,name:"representing",active:Ud,apis:Wd,extra:{setValueFrom:(e,t)=>{const o=Zd.getValue(t);Zd.setValue(e,o)}},state:$d}),Qd=(e,t)=>hs(e,{},P(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Ln(o,o,{tag:"option",process:{}},gn((e=>Qo("The field: "+o+" is forbidden. "+n))));var o,n})).concat([Wn("dump",x)])),eu=e=>e.dump,tu=(e,t)=>({...cl(t),...e.dump}),ou=Qd,nu=tu,su="placeholder",ru=bs([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),au=e=>ve(e,"uiType"),iu=(e,t,o,n)=>((e,t,o,n)=>au(o)&&o.uiType===su?((e,t,o,n)=>e.exists((e=>e!==o.owner))?ru.single(!0,y(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):ru.single(!1,y(o)))(e,0,o,n).fold(((s,r)=>{const a=au(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=X(i,(o=>iu(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(au(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(x)(e)}return n(t)})),lu=ru.single,cu=ru.multiple,du=y(su),uu=bs([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),mu=is("factory",{sketch:x}),gu=is("schema",[]),pu=jn("name"),hu=Ln("pname","pname",an((e=>"<alloy."+Kr(e.name)+">")),On()),fu=Wn("schema",(()=>[Qn("preprocess")])),bu=is("defaults",y({})),vu=is("overrides",y({})),yu=xn([mu,gu,pu,hu,bu,vu]),xu=xn([mu,gu,pu,bu,vu]),wu=xn([mu,gu,pu,hu,bu,vu]),Su=xn([mu,fu,pu,jn("unit"),hu,bu,vu]),ku=e=>e.fold(B.some,B.none,B.some,B.some),Cu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Ou=(e,t)=>o=>{const n=Hn("Converting part type",t,o);return e(n)},_u=Ou(uu.required,yu),Tu=Ou(uu.external,xu),Eu=Ou(uu.optional,wu),Bu=Ou(uu.group,Su),Mu=y("entirety");var Au=Object.freeze({__proto__:null,required:_u,external:Tu,optional:Eu,group:Bu,asNamedPart:ku,name:Cu,asCommon:e=>e.fold(x,x,x,x),original:Mu});const Du=(e,t,o,n)=>sn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Fu=(e,t)=>{const o={};return N(t,(t=>{ku(t).each((t=>{const n=Iu(e,t.pname);o[t.name]=o=>{const s=Hn("Part: "+t.name+" in "+e,xn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Iu=(e,t)=>({uiType:du(),owner:e,name:t}),Ru=(e,t,o)=>({uiType:du(),owner:e,name:t,config:o,validated:{}}),Vu=e=>X(e,(e=>e.fold(B.none,B.some,B.none,B.none).map((e=>Yn(e.name,e.schema.concat([Si(Mu())])))).toArray())),zu=e=>P(e,Cu),Hu=(e,t,o)=>((e,t,o)=>{const n={},s={};return N(o,(e=>{e.fold((e=>{n[e.pname]=lu(!0,((t,o,n)=>e.factory.sketch(Du(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=y(e.factory.sketch(Du(t,e,o[Mu()]),o))}),(e=>{n[e.pname]=lu(!1,((t,o,n)=>e.factory.sketch(Du(t,e,o,n))))}),(e=>{n[e.pname]=cu(!0,((t,o,n)=>{const s=t[e.name];return P(s,(o=>e.factory.sketch(sn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(s)}})(0,t,o),Pu=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>X(o,(o=>iu(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(B.some(e),t,t.components,o),Nu=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Lu=(e,t,o)=>Nu(e,t,o).getOrDie("Could not find part: "+o),Wu=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]))})),n},Uu=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>y(o.getByUid(e))))},ju=e=>ae(e.partUids),Gu=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]).getOrDie())})),n},$u=(e,t)=>{const o=zu(t);return ys(P(o,(t=>({key:t,value:e+"-"+t}))))},qu=e=>Ln("partUids","partUids",cn((t=>$u(t.uid,e))),On());var Xu=Object.freeze({__proto__:null,generate:Fu,generateOne:Ru,schemas:Vu,names:zu,substitutes:Hu,components:Pu,defaultUids:$u,defaultUidsSchema:qu,getAllParts:Uu,getAllPartNames:ju,getPart:Nu,getPartOrDie:Lu,getParts:Wu,getPartsOrDie:Gu});const Ku=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[Yn("parts",e)]:[]).concat([jn("uid"),is("dom",{}),is("components",[]),Si("originalSpec"),is("debug.sketcher",{})]).concat(t))(n,s);return Hn(e+" [SpecSchema]",yn(r.concat(t)),o)},Yu=(e,t,o,n,s)=>{const r=Ju(s),a=Vu(o),i=qu(o),l=Ku(e,t,r,a,[i]),c=Hu(0,l,o);return n(l,Pu(e,l,c.internals()),r,c.externals())},Ju=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:oa("uid")},Zu=yn([jn("name"),jn("factory"),jn("configFields"),is("apis",{}),is("extraApis",{})]),Qu=yn([jn("name"),jn("factory"),jn("configFields"),jn("partFields"),is("apis",{}),is("extraApis",{})]),em=e=>{const t=Hn("Sketcher for "+e.name,Zu,e),o=ce(t.apis,ua),n=ce(t.extraApis,((e,t)=>ia(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=Ju(n);return o(Ku(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},tm=e=>{const t=Hn("Sketcher for "+e.name,Qu,e),o=Fu(t.name,t.partFields),n=ce(t.apis,ua),s=ce(t.extraApis,((e,t)=>ia(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>Yu(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},om=e=>We("input")(e)&&"radio"!==bt(e,"type")||We("textarea")(e);var nm=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const sm=[jn("find")],rm=ul({fields:sm,name:"composing",apis:nm}),am=["input","button","textarea","select"],im=(e,t,o)=>{(t.disabled()?gm:pm)(e,t)},lm=(e,t)=>!0===t.useNative&&R(am,Ve(e.element)),cm=e=>{ht(e.element,"disabled","disabled")},dm=e=>{xt(e.element,"disabled")},um=e=>{ht(e.element,"aria-disabled","true")},mm=e=>{ht(e.element,"aria-disabled","false")},gm=(e,t,o)=>{t.disableClass.each((t=>{Ta(e.element,t)})),(lm(e,t)?cm:um)(e),t.onDisabled(e)},pm=(e,t,o)=>{t.disableClass.each((t=>{Ea(e.element,t)})),(lm(e,t)?dm:mm)(e),t.onEnabled(e)},hm=(e,t)=>lm(e,t)?(e=>yt(e.element,"disabled"))(e):(e=>"true"===bt(e.element,"aria-disabled"))(e);var fm=Object.freeze({__proto__:null,enable:pm,disable:gm,isDisabled:hm,onLoad:im,set:(e,t,o,n)=>{(n?gm:pm)(e,t)}}),bm=Object.freeze({__proto__:null,exhibit:(e,t)=>ha({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>_r([Tr(Qs(),((t,o)=>hm(t,e))),al(e,t,im)])}),vm=[gs("disabled",_),is("useNative",!0),Qn("disableClass"),bi("onDisabled"),bi("onEnabled")];const ym=ul({fields:vm,name:"disabling",active:bm,apis:fm}),xm=(e,t,o,n)=>{const s=Dc(e.element,"."+t.highlightClass);N(s,(o=>{V(n,(e=>e.element===o))||(Ea(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),xr(o,yr())})))}))},wm=(e,t,o,n)=>{xm(e,t,0,[n]),Sm(e,t,o,n)||(Ta(n.element,t.highlightClass),t.onHighlight(e,n),xr(n,vr()))},Sm=(e,t,o,n)=>Ba(n.element,t.highlightClass),km=(e,t,o)=>ei(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Cm=(e,t,o)=>{const n=Dc(e.element,"."+t.itemClass);return(n.length>0?B.some(n[n.length-1]):B.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Om=(e,t,o,n)=>{const s=Dc(e.element,"."+t.itemClass);return $(s,(e=>Ba(e,t.highlightClass))).bind((t=>{const o=Ii(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},_m=(e,t,o)=>{const n=Dc(e.element,"."+t.itemClass);return we(P(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Tm=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>xm(e,t,0,[]),dehighlight:(e,t,o,n)=>{Sm(e,t,o,n)&&(Ea(n.element,t.highlightClass),t.onDehighlight(e,n),xr(n,yr()))},highlight:wm,highlightFirst:(e,t,o)=>{km(e,t).each((n=>{wm(e,t,o,n)}))},highlightLast:(e,t,o)=>{Cm(e,t).each((n=>{wm(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Dc(e.element,"."+t.itemClass);return B.from(s[n]).fold((()=>qo.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{wm(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=_m(e,t);G(s,n).each((n=>{wm(e,t,o,n)}))},isHighlighted:Sm,getHighlighted:(e,t,o)=>ei(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:km,getLast:Cm,getPrevious:(e,t,o)=>Om(e,t,0,-1),getNext:(e,t,o)=>Om(e,t,0,1),getCandidates:_m}),Em=[jn("highlightClass"),jn("itemClass"),bi("onHighlight"),bi("onDehighlight")];const Bm=ul({fields:Em,name:"highlighting",apis:Tm}),Mm=[8],Am=[9],Dm=[13],Fm=[27],Im=[32],Rm=[37],Vm=[38],zm=[39],Hm=[40],Pm=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return G(n.concat(s),o)},Nm=(e,t,o)=>{const n=Y(e.slice(0,t));return G(n,o)},Lm=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},Wm=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},Um=e=>t=>{const o=t.raw;return R(e,o.which)},jm=e=>t=>K(e,(e=>e(t))),Gm=e=>!0===e.raw.shiftKey,$m=e=>!0===e.raw.ctrlKey,qm=k(Gm),Xm=(e,t)=>({matches:e,classification:t}),Km=(e,t,o)=>{t.exists((e=>o.exists((t=>Ge(t,e)))))||wr(e,pr(),{prevFocus:t,newFocus:o})},Ym=()=>{const e=e=>xl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);Km(t,n,s)}}},Jm=()=>{const e=e=>Bm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Bm.highlight(t,e)}));const s=e(t);Km(t,n,s)}}};var Zm;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(Zm||(Zm={}));const Qm=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([is("focusManager",Ym()),ls("focusInside","onFocus",In((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?qo.value(e):qo.error("Invalid value for focusInside")))),wi("handler",a),wi("state",t),wi("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==Zm.OnFocusMode?B.none():s(e).map((o=>Br(Ks(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Br(Hs(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Um(Im.concat(Dm))(n.event);e.focusInside===Zm.OnEnterOrSpaceMode&&r&&ks(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Br(Ps(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return _r(a.toArray().concat(i))}};return a},eg=e=>{const t=[Qn("onEscape"),Qn("onEnter"),is("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),is("firstTabstop",0),is("useTabstopAt",T),Qn("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>ti(t,e))).getOr(t);return Rt(o)>0},n=(e,t,n)=>{((e,t)=>{const n=Dc(e.element,t.selector),s=W(n,(e=>o(t,e)));return B.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},s=(e,t,n,s)=>{const r=Dc(e.element,n.selector);return((e,t)=>t.focusManager.get(e).bind((e=>ti(e,t.selector))))(e,n).bind((t=>$(r,S(Ge,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?B.some(!0):B.none()),(t=>(s.focusManager.set(e,t),B.some(!0)))))(e,r,t,n,s)))))},r=y([Xm(jm([Gm,Um(Am)]),((e,t,o)=>{const n=o.cyclic?Pm:Nm;return s(e,0,o,n)})),Xm(Um(Am),((e,t,o)=>{const n=o.cyclic?Lm:Wm;return s(e,0,o,n)})),Xm(jm([qm,Um(Dm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),a=y([Xm(Um(Fm),((e,t,o)=>o.onEscape.bind((o=>o(e,t)))))]);return Qm(t,ma.init,r,a,(()=>B.some(n)))};var tg=eg(Wn("cyclic",_)),og=eg(Wn("cyclic",T));const ng=(e,t,o)=>om(o)&&Um(Im)(t.event)?B.none():((e,t,o)=>(kr(e,o,Qs()),B.some(!0)))(e,0,o),sg=(e,t)=>B.some(!0),rg=[is("execute",ng),is("useSpace",!1),is("useEnter",!0),is("useControlEnter",!1),is("useDown",!1)],ag=(e,t,o)=>o.execute(e,t,e.element);var ig=Qm(rg,ma.init,((e,t,o,n)=>{const s=o.useSpace&&!om(e.element)?Im:[],r=o.useEnter?Dm:[],a=o.useDown?Hm:[],i=s.concat(r).concat(a);return[Xm(Um(i),ag)].concat(o.useControlEnter?[Xm(jm([$m,Um(Dm)]),ag)]:[])}),((e,t,o,n)=>o.useSpace&&!om(e.element)?[Xm(Um(Im),sg)]:[]),(()=>B.none()));const lg=()=>{const e=Hl();return ga({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var cg=Object.freeze({__proto__:null,flatgrid:lg,init:e=>e.state(e)});const dg=e=>(t,o,n,s)=>{const r=e(t.element);return pg(r,t,o,n,s)},ug=(e,t)=>{const o=tc(e,t);return dg(o)},mg=(e,t)=>{const o=tc(t,e);return dg(o)},gg=e=>(t,o,n,s)=>pg(e,t,o,n,s),pg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),hg=gg,fg=gg,bg=gg,vg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),yg=(e,t,o)=>{const n=Dc(e,o);return((e,o)=>$(e,(e=>Ge(e,t))).map((t=>({index:t,candidates:e}))))(W(n,vg))},xg=(e,t)=>$(e,(e=>Ge(t,e))),wg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?B.some(e[n]):B.none()})),Sg=(e,t,o,n,s)=>wg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Ii(r,s,0,a-1);return B.some({row:t,column:i})})),kg=(e,t,o,n,s)=>wg(e,t,n,((t,r)=>{const a=Ii(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Ri(r,0,i-1);return B.some({row:a,column:l})})),Cg=[jn("selector"),is("execute",ng),vi("onEscape"),is("captureTab",!1),ki()],Og=(e,t,o)=>{ei(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},_g=e=>(t,o,n,s)=>yg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Tg=(e,t,o)=>o.captureTab?B.some(!0):B.none(),Eg=_g(((e,t,o,n)=>Sg(e,t,o,n,-1))),Bg=_g(((e,t,o,n)=>Sg(e,t,o,n,1))),Mg=_g(((e,t,o,n)=>kg(e,t,o,n,-1))),Ag=_g(((e,t,o,n)=>kg(e,t,o,n,1))),Dg=y([Xm(Um(Rm),ug(Eg,Bg)),Xm(Um(zm),mg(Eg,Bg)),Xm(Um(Vm),hg(Mg)),Xm(Um(Hm),fg(Ag)),Xm(jm([Gm,Um(Am)]),Tg),Xm(jm([qm,Um(Am)]),Tg),Xm(Um(Im.concat(Dm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>ti(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Fg=y([Xm(Um(Fm),((e,t,o)=>o.onEscape(e,t))),Xm(Um(Im),sg)]);var Ig=Qm(Cg,lg,Dg,Fg,(()=>B.some(Og)));const Rg=(e,t,o,n)=>{const s=(e,t,o)=>{const r=Ii(t,n,0,o.length-1);return r===e?B.none():(a=o[r],"button"===Ve(a)&&"disabled"===bt(a,"disabled")?s(e,r,o):B.from(o[r]));var a};return yg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return s(t,t,o)}))},Vg=[jn("selector"),is("getInitial",B.none),is("execute",ng),vi("onEscape"),is("executeOnMove",!1),is("allowVertical",!0)],zg=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>ti(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Hg=(e,t,o)=>{t.getInitial(e).orThunk((()=>ei(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Pg=(e,t,o)=>Rg(e,o.selector,t,-1),Ng=(e,t,o)=>Rg(e,o.selector,t,1),Lg=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?zg(t,o,n):B.some(!0))),Wg=y([Xm(Um(Im),sg),Xm(Um(Fm),((e,t,o)=>o.onEscape(e,t)))]);var Ug=Qm(Vg,ma.init,((e,t,o,n)=>{const s=Rm.concat(o.allowVertical?Vm:[]),r=zm.concat(o.allowVertical?Hm:[]);return[Xm(Um(s),Lg(ug(Pg,Ng))),Xm(Um(r),Lg(mg(Pg,Ng))),Xm(Um(Dm),zg),Xm(Um(Im),zg)]}),Wg,(()=>B.some(Hg)));const jg=(e,t,o)=>B.from(e[t]).bind((e=>B.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Gg=(e,t,o,n)=>{const s=e[t].length,r=Ii(o,n,0,s-1);return jg(e,t,r)},$g=(e,t,o,n)=>{const s=Ii(o,n,0,e.length-1),r=e[s].length,a=Ri(t,0,r-1);return jg(e,s,a)},qg=(e,t,o,n)=>{const s=e[t].length,r=Ri(o+n,0,s-1);return jg(e,t,r)},Xg=(e,t,o,n)=>{const s=Ri(o+n,0,e.length-1),r=e[s].length,a=Ri(t,0,r-1);return jg(e,s,a)},Kg=[Yn("selectors",[jn("row"),jn("cell")]),is("cycles",!0),is("previousSelector",B.none),is("execute",ng)],Yg=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return ei(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Jg=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return ti(n,s.selectors.row).bind((e=>{const t=Dc(e,s.selectors.cell);return xg(t,n).bind((t=>{const n=Dc(o,s.selectors.row);return xg(n,e).bind((e=>{const o=((e,t)=>P(e,(e=>Dc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},Zg=Jg(((e,t,o)=>Gg(e,t,o,-1)),((e,t,o)=>qg(e,t,o,-1))),Qg=Jg(((e,t,o)=>Gg(e,t,o,1)),((e,t,o)=>qg(e,t,o,1))),ep=Jg(((e,t,o)=>$g(e,o,t,-1)),((e,t,o)=>Xg(e,o,t,-1))),tp=Jg(((e,t,o)=>$g(e,o,t,1)),((e,t,o)=>Xg(e,o,t,1))),op=y([Xm(Um(Rm),ug(Zg,Qg)),Xm(Um(zm),mg(Zg,Qg)),Xm(Um(Vm),hg(ep)),Xm(Um(Hm),fg(tp)),Xm(Um(Im.concat(Dm)),((e,t,o)=>xl(e.element).bind((n=>o.execute(e,t,n)))))]),np=y([Xm(Um(Im),sg)]);var sp=Qm(Kg,ma.init,op,np,(()=>B.some(Yg)));const rp=[jn("selector"),is("execute",ng),is("moveOnTab",!1)],ap=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),ip=(e,t,o)=>{ei(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},lp=(e,t,o)=>Rg(e,o.selector,t,-1),cp=(e,t,o)=>Rg(e,o.selector,t,1),dp=y([Xm(Um(Vm),bg(lp)),Xm(Um(Hm),bg(cp)),Xm(jm([Gm,Um(Am)]),((e,t,o,n)=>o.moveOnTab?bg(lp)(e,t,o,n):B.none())),Xm(jm([qm,Um(Am)]),((e,t,o,n)=>o.moveOnTab?bg(cp)(e,t,o,n):B.none())),Xm(Um(Dm),ap),Xm(Um(Im),ap)]),up=y([Xm(Um(Im),sg)]);var mp=Qm(rp,ma.init,dp,up,(()=>B.some(ip)));const gp=[vi("onSpace"),vi("onEnter"),vi("onShiftEnter"),vi("onLeft"),vi("onRight"),vi("onTab"),vi("onShiftTab"),vi("onUp"),vi("onDown"),vi("onEscape"),is("stopSpaceKeyup",!1),Qn("focusIn")];var pp=Qm(gp,ma.init,((e,t,o)=>[Xm(Um(Im),o.onSpace),Xm(jm([qm,Um(Dm)]),o.onEnter),Xm(jm([Gm,Um(Dm)]),o.onShiftEnter),Xm(jm([Gm,Um(Am)]),o.onShiftTab),Xm(jm([qm,Um(Am)]),o.onTab),Xm(Um(Vm),o.onUp),Xm(Um(Hm),o.onDown),Xm(Um(Rm),o.onLeft),Xm(Um(zm),o.onRight),Xm(Um(Im),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Xm(Um(Im),sg)]:[],Xm(Um(Fm),o.onEscape)]),(e=>e.focusIn));const hp=tg.schema(),fp=og.schema(),bp=Ug.schema(),vp=Ig.schema(),yp=sp.schema(),xp=ig.schema(),wp=mp.schema(),Sp=pp.schema(),kp=gl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:hp,cyclic:fp,flow:bp,flatgrid:vp,matrix:yp,execution:xp,menu:wp,special:Sp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:cg}),Cp=(e,t)=>{wl((()=>{((e,t,o)=>{const n=e.components();(e=>{N(e.components(),(e=>Do(e.element))),Ao(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);N(r,(t=>{cd(t),e.getSystem().removeFromWorld(t)})),N(s,(t=>{ld(t)?gd(e,t):(e.getSystem().addToWorld(t),gd(e,t),ut(e.element)&&dd(t))})),e.syncComponents()})(e,t,(()=>P(t,e.getSystem().build)))}),e.element)},Op=(e,t)=>{wl((()=>{((o,n,s)=>{const r=o.components(),a=X(n,(e=>da(e).toArray()));N(r,(e=>{R(a,e)||md(e)}));const i=((e,t,o)=>Ra(e,t,((t,n)=>Va(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);N(l,(e=>{ld(e)&&md(e)})),N(i,(e=>{ld(e)||ud(o,e)})),o.syncComponents()})(e,t)}),e.element)},_p=(e,t,o,n)=>{md(t);const s=Va(e.element,o,n,e.getSystem().buildOrPatch);ud(e,s),e.syncComponents()},Tp=(e,t,o)=>{const n=e.getSystem().build(o);hd(e,n,t)},Ep=(e,t,o,n)=>{bd(t),Tp(e,((e,t)=>((e,t,o)=>{tt(e,o).fold((()=>{Bo(e,t)}),(e=>{_o(e,t)}))})(e,t,o)),n)},Bp=(e,t)=>e.components(),Mp=(e,t,o,n,s)=>{const r=Bp(e);return B.from(r[n]).map((o=>(s.fold((()=>bd(o)),(s=>{(t.reuseDom?_p:Ep)(e,o,n,s)})),o)))};var Ap=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Tp(e,Bo,n)},prepend:(e,t,o,n)=>{Tp(e,Eo,n)},remove:(e,t,o,n)=>{const s=Bp(e),r=G(s,(e=>Ge(n.element,e.element)));r.each(bd)},replaceAt:Mp,replaceBy:(e,t,o,n,s)=>{const r=Bp(e);return $(r,n).bind((o=>Mp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Op:Cp)(e,n),contents:Bp});const Dp=ul({fields:[ms("reuseDom",!0)],name:"replacing",apis:Ap}),Fp=(e,t)=>{const o=((e,t)=>{const o=_r(t);return ul({fields:[jn("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:ma}}},Ip=(e,t)=>{t.ignore||(bl(e.element),t.onFocus(e))};var Rp=Object.freeze({__proto__:null,focus:Ip,blur:(e,t)=>{t.ignore||(e=>{e.dom.blur()})(e.element)},isFocused:e=>vl(e.element)}),Vp=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return ha(o)},events:e=>_r([Br(Ks(),((t,o)=>{Ip(t,e),o.stop()}))].concat(e.stopMousedown?[Br(As(),((e,t)=>{t.event.prevent()}))]:[]))}),zp=[bi("onFocus"),is("stopMousedown",!1),is("ignore",!1)];const Hp=ul({fields:zp,name:"focusing",active:Vp,apis:Rp}),Pp=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?Ta(e.element,t):Ea(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},Np=(e,t,o)=>{Pp(e,t,o,!o.get())},Lp=(e,t,o)=>{Pp(e,t,o,t.selected)};var Wp=Object.freeze({__proto__:null,onLoad:Lp,toggle:Np,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{Pp(e,t,o,!0)},off:(e,t,o)=>{Pp(e,t,o,!1)},set:Pp}),Up=Object.freeze({__proto__:null,exhibit:()=>ha({}),events:(e,t)=>{const o=(n=e,s=t,r=Np,Nr((e=>{r(e,n,s)})));var n,s,r;const a=al(e,t,Lp);return _r(q([e.toggleOnExecute?[o]:[],[a]]))}});const jp=(e,t,o)=>{ht(e.element,"aria-expanded",o)};var Gp=[is("selected",!1),Qn("toggleClass"),is("toggleOnExecute",!0),bi("onToggled"),ls("aria",{mode:"none"},Nn("mode",{pressed:[is("syncWithExpanded",!1),wi("update",((e,t,o)=>{ht(e.element,"aria-pressed",o),t.syncWithExpanded&&jp(e,0,o)}))],checked:[wi("update",((e,t,o)=>{ht(e.element,"aria-checked",o)}))],expanded:[wi("update",jp)],selected:[wi("update",((e,t,o)=>{ht(e.element,"aria-selected",o)}))],none:[wi("update",b)]}))];const $p=ul({fields:Gp,name:"toggling",active:Up,apis:Wp,state:(!1,{init:()=>{const e=fs(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const qp=()=>{const e=(e,t)=>{t.stop(),Sr(e)};return[Br(Ws(),e),Br(tr(),e),Ir(Ts()),Ir(As())]},Xp=e=>_r(q([e.map((e=>Nr(((t,o)=>{e(t),o.stop()})))).toArray(),qp()])),Kp="alloy.item-hover",Yp="alloy.item-focus",Jp="alloy.item-toggled",Zp=e=>{(xl(e.element).isNone()||Hp.isFocused(e))&&(Hp.isFocused(e)||Hp.focus(e),wr(e,Kp,{item:e}))},Qp=e=>{wr(e,Yp,{item:e})},eh=y(Kp),th=y(Yp),oh=y(Jp),nh=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),sh=[jn("data"),jn("components"),jn("dom"),is("hasSubmenu",!1),Qn("toggling"),ou("itemBehaviours",[$p,Hp,kp,Zd]),is("ignoreFocus",!1),is("domModification",{}),wi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:nh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:nu(e.itemBehaviours,[e.toggling.fold($p.revoke,(e=>$p.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{wr(e,Jp,{item:e,state:t})})(t,o)}}))(e)))),Hp.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Qp(e)}}),kp.config({mode:"execution"}),Zd.config({store:{mode:"memory",initialValue:e.data}}),Fp("item-type-events",[...qp(),Br(Rs(),Zp),Br(er(),Hp.focus)])]),components:e.components,eventOrder:e.eventOrder}))),is("eventOrder",{})],rh=[jn("dom"),jn("components"),wi("builder",(e=>({dom:e.dom,components:e.components,events:_r([Rr(er())])})))],ah=y("item-widget"),ih=y([_u({name:"widget",overrides:e=>({behaviours:cl([Zd.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),lh=[jn("uid"),jn("data"),jn("components"),jn("dom"),is("autofocus",!1),is("ignoreFocus",!1),ou("widgetBehaviours",[Zd,Hp,kp]),is("domModification",{}),qu(ih()),wi("builder",(e=>{const t=Hu(ah(),e,ih()),o=Pu(ah(),e,t.internals()),n=t=>Nu(t,e,"widget").map((e=>(kp.focusIn(e),e))),s=(t,o)=>om(o.event.target)?B.none():e.autofocus?(o.setSource(t.element),B.none()):B.none();return{dom:e.dom,components:o,domModification:e.domModification,events:_r([Nr(((e,t)=>{n(e).each((e=>{t.stop()}))})),Br(Rs(),Zp),Br(er(),((t,o)=>{e.autofocus?n(t):Hp.focus(t)}))]),behaviours:nu(e.widgetBehaviours,[Zd.config({store:{mode:"memory",initialValue:e.data}}),Hp.config({ignore:e.ignoreFocus,onFocus:e=>{Qp(e)}}),kp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:pl(),onLeft:s,onRight:s,onEscape:(t,o)=>Hp.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),B.none()):B.none():(Hp.focus(t),B.some(!0))})])}}))],ch=Nn("type",{widget:lh,item:sh,separator:rh}),dh=y([Bu({factory:{sketch:e=>{const t=Hn("menu.spec item",ch,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:oa("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),uh=y([jn("value"),jn("items"),jn("dom"),jn("components"),is("eventOrder",{}),Qd("menuBehaviours",[Bm,Zd,rm,kp]),ls("movement",{mode:"menu",moveOnTab:!0},Nn("mode",{grid:[ki(),wi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[wi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},focusManager:e.focusManager}))),jn("rowSelector")],menu:[is("moveOnTab",!0),wi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),Gn("markers",mi()),is("fakeFocus",!1),is("focusManager",Ym()),bi("onHighlight")]),mh=y("alloy.menu-focus"),gh=tm({name:"Menu",configFields:uh(),partFields:dh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:tu(e.menuBehaviours,[Bm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight}),Zd.config({store:{mode:"memory",initialValue:e.value}}),rm.config({find:B.some}),kp.config(e.movement.config(e,e.movement))]),events:_r([Br(th(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Bm.highlight(e,o),t.stop(),wr(e,mh(),{menu:e,item:o})}))})),Br(eh(),((e,t)=>{const o=t.event.item;Bm.highlight(e,o)})),Br(oh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===bt(o.element,"role")&&((e,t)=>{const o=Dc(e.element,'[role="menuitemradio"][aria-checked="true"]');N(o,(o=>{Ge(o,t.element)||e.getSystem().getByDom(o).each((e=>{$p.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),ph=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=ph(e,t,o,n);return B.some([n].concat(s))})))).getOr([]),hh=e=>"prepared"===e.type?B.some(e.menu):B.none(),fh=()=>{const e=fs({}),t=fs({}),o=fs({}),n=Hl(),s=fs({}),r=e=>a(e).bind(hh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{N(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(ph(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?B.some(e.slice(1)):B.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=W(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return B.none();t.push(n.getOrDie())}return B.some(t)})(X(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[B.none()]),(e=>[B.some(e)])))))}))}}},bh=hh,vh=y("collapse-item"),yh=em({name:"TieredMenu",configFields:[xi("onExecute"),xi("onEscape"),yi("onOpenMenu"),yi("onOpenSubmenu"),bi("onRepositionMenu"),bi("onCollapseMenu"),is("highlightImmediately",!0),Yn("data",[jn("primary"),jn("menus"),jn("expansions")]),is("fakeFocus",!1),bi("onHighlight"),bi("onHover"),pi(),jn("dom"),is("navigateOnHover",!0),is("stayInDom",!1),Qd("tmenuBehaviours",[kp,Bm,rm,Dp]),is("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Hl(),n=fh(),s=e=>Zd.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=(e,t)=>{Bm.highlight(e,t),Bm.getHighlighted(t).orThunk((()=>Bm.getFirst(t))).each((t=>{kr(e,t.element,er())}))},i=(e,t)=>we(P(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?B.some(e.menu):B.none()))))),l=(t,o,n)=>{const s=i(o,o.otherMenus(n));N(s,(o=>{Aa(o.element,[e.markers.backgroundMenu]),e.stayInDom||Dp.remove(t,o)}))},c=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Dc(t.element,`.${e.markers.item}`),a=W(r,(e=>"true"===bt(e,"aria-haspopup")));return N(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=R(n,t);ht(e.element,"aria-expanded",o)}))},d=(t,o,n)=>B.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return B.none();{const r=s.menu,c=i(o,n.slice(1));return N(c,(t=>{Ta(t.element,e.markers.backgroundMenu)})),ut(r.element)||Dp.append(t,$a(r)),Aa(r.element,[e.markers.backgroundMenu]),a(t,r),l(t,o,n),B.some(r)}}))));let u;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(u||(u={}));const m=(t,o,r=u.HighlightSubmenu)=>{if(o.hasConfigured(ym)&&ym.isDisabled(o))return B.some(o);{const a=s(o);return n.expand(a).bind((s=>(c(t,s),B.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return ut(l.element)||Dp.append(t,$a(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===u.HighlightSubmenu?(Bm.highlightFirst(l),d(t,n,s)):(Bm.dehighlightAll(l),B.some(o))})))))))}},g=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(c(t,s),d(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},p=t=>(o,n)=>ti(n.getSource(),"."+e.markers.item).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(T))))),h=_r([Br(mh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Bm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>l(e,n,t)))}))})),Nr(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&g(t,o),m(t,o,u.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),zr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>gh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:e.onHighlight,focusManager:e.fakeFocus?Jm():Ym()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Dp.append(t,$a(o)),e.onOpenMenu(t,o),e.highlightImmediately&&a(t,o)}))}))].concat(e.navigateOnHover?[Br(eh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(c(e,t),d(e,n,t))))})(t,r),m(t,r,u.HighlightParent),e.onHover(t,r)}))]:[])),f=e=>Bm.getHighlighted(e).bind(Bm.getHighlighted),v={collapseMenu:e=>{f(e).each((t=>{g(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{a(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>f(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(P(o,bh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return B.none();const t=Bm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>B.from(e.components()[0]).filter((e=>"menu"===bt(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:tu(e.tmenuBehaviours,[kp.config({mode:"special",onRight:p(((e,t)=>om(t.element)?B.none():m(e,t,u.HighlightSubmenu))),onLeft:p(((e,t)=>om(t.element)?B.none():g(e,t))),onEscape:p(((t,o)=>g(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{kr(e,t.element,er())}))}}),Bm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),rm.config({find:e=>Bm.getHighlighted(e)}),Dp.config({})]),eventOrder:e.eventOrder,apis:v,events:h}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:vs(e,t),expansions:{}}),collapseItem:e=>({value:Kr(vh()),meta:{text:e}})}}),xh=em({name:"InlineView",configFields:[jn("lazySink"),bi("onShow"),bi("onHide"),ss("onEscape"),Qd("inlineBehaviours",[Dd,Zd,hl]),as("fireDismissalEventInstead",[is("event",mr())]),as("fireRepositionEventInstead",[is("event",gr())]),is("getRelated",B.none),is("isExtraPart",_),is("eventOrder",B.none)],factory:(e,t)=>{const o=(e,t,o,s)=>{n(e,t,o,(()=>s.map((e=>Wo(e)))))},n=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Dd.openWhileCloaked(t,o,(()=>id.positionWithinBounds(r,t,n,s()))),Zd.setValue(t,B.some({mode:"position",config:n,getBounds:s}))},s=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>ol(),onRtl:()=>nl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return yh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightImmediately:n.menu.highlightImmediately,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Dd.close(t),e.onEscape.map((e=>e(t))),B.some(!0)),onExecute:()=>B.some(!0),onOpenMenu:(e,t)=>{id.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();id.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();id.positionWithinBounds(a,t,o,s()),N(n,(e=>{const t=i(e.triggeringPath);id.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Dd.open(t,r),Zd.setValue(t,B.some({mode:"menu",menu:r}))},r=t=>{Dd.isOpen(t)&&Zd.getValue(t).each((o=>{switch(o.mode){case"menu":Dd.getState(t).each(yh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();id.positionWithinBounds(n,t,o.config,o.getBounds())}}))},a={setContent:(e,t)=>{Dd.setContent(e,t)},showAt:(e,t,n)=>{o(e,t,n,B.none())},showWithin:o,showWithinBounds:n,showMenuAt:(e,t,o)=>{s(e,t,o,B.none)},showMenuWithinBounds:s,hide:e=>{Dd.isOpen(e)&&(Zd.setValue(e,B.none()),Dd.close(e))},getContent:e=>Dd.getState(e),reposition:r,isOpen:Dd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:tu(e.inlineBehaviours,[Dd.config({isPartOf:(t,o,n)=>si(o,n)||((t,o)=>e.getRelated(t).exists((e=>si(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),Zd.config({store:{mode:"memory",initialValue:B.none()}}),hl.config({channels:{...zd({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Pd({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:r})}})]),eventOrder:e.eventOrder,apis:a}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithin:(e,t,o,n,s)=>{e.showWithin(t,o,n,s)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var wh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Sh=em({name:"Button",factory:e=>{const t=Xp(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:nu(e.buttonBehaviours,[Hp.config({}),kp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:n("role").getOr("button")}},eventOrder:e.eventOrder}},configFields:[is("uid",void 0),jn("dom"),is("components",[]),ou("buttonBehaviours",[Hp,kp]),Qn("action"),Qn("role"),is("eventOrder",{})]}),kh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:oa("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Ch=tinymce.util.Tools.resolve("tinymce.util.I18n");const Oh={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},_h="temporary-placeholder",Th=e=>()=>be(e,_h).getOr("!not found!"),Eh=(e,t)=>{const o=e.toLowerCase();if(Ch.isRtl()){const e=((e,t)=>_e(e,t)?e:((e,t)=>e+"-rtl")(e))(o,"-rtl");return ve(t,e)?e:o}return o},Bh=(e,t)=>be(t,Eh(e,t)),Mh=(e,t)=>{const o=t();return Bh(e,o).getOrThunk(Th(o))},Ah=()=>Fp("add-focusable",[zr((e=>{Qa(e.element,"svg").each((e=>ht(e,"focusable","false")))}))]),Dh=(e,t,o,n)=>{var s,r;const a=(e=>!!Ch.isRtl()&&ve(Oh,e))(t)?["tox-icon--flip"]:[],i=be(o,Eh(t,o)).or(n).getOrThunk(Th(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:cl([...null!==(r=e.behaviours)&&void 0!==r?r:[],Ah()])}},Fh=(e,t,o,n=B.none())=>Dh(t,e,o(),n),Ih={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Rh=em({name:"Notification",factory:e=>{const t=kh({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:cl([Dp.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=kh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:cl([Dp.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{Dp.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Dp.set(n,[La(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>B.from(Ih[e]))).toArray()]),i=kh(Sh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Fh("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,Eh(e,n))));return Dh({tag:"div",classes:["tox-notification__icon"]},s.getOr(_h),n,B.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:cl([Dp.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:cl([Hp.config({}),Fp("notification-events",[Br(Vs(),(e=>{i.getOpt(e).each(Hp.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[Qn("level"),jn("progress"),jn("icon"),jn("onAction"),jn("text"),jn("iconProvider"),jn("translationProvider"),ms("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var Vh,zh,Hh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Ph=tinymce.util.Tools.resolve("tinymce.EditorManager"),Nh=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(Vh||(Vh={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(zh||(zh={}));const Lh=e=>t=>t.options.get(e),Wh=e=>t=>B.from(e(t)),Uh=e=>{const t=Nh.deviceType.isPhone(),o=Nh.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:Hh.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),z(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:zh.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!Nh.deviceType.isTouch()}),n("sidebar_show",{processor:"string"})},jh=Lh("readonly"),Gh=Lh("height"),$h=Lh("width"),qh=Wh(Lh("min_width")),Xh=Wh(Lh("min_height")),Kh=Wh(Lh("max_width")),Yh=Wh(Lh("max_height")),Jh=Wh(Lh("style_formats")),Zh=Lh("style_formats_merge"),Qh=Lh("style_formats_autohide"),ef=Lh("content_langs"),tf=Lh("removed_menuitems"),of=Lh("toolbar_mode"),nf=Lh("toolbar_groups"),sf=Lh("toolbar_location"),rf=Lh("fixed_toolbar_container"),af=Lh("fixed_toolbar_container_target"),lf=Lh("toolbar_persist"),cf=Lh("toolbar_sticky_offset"),df=Lh("menubar"),uf=Lh("toolbar"),mf=Lh("file_picker_callback"),gf=Lh("file_picker_validator_handler"),pf=Lh("file_picker_types"),hf=Lh("typeahead_urls"),ff=Lh("anchor_top"),bf=Lh("anchor_bottom"),vf=Lh("draggable_modal"),yf=Lh("statusbar"),xf=Lh("elementpath"),wf=Lh("branding"),Sf=Lh("resize"),kf=Lh("paste_as_text"),Cf=Lh("sidebar_show"),Of=e=>!1===e.options.get("skin"),_f=e=>!1!==e.options.get("menubar"),Tf=e=>{const t=e.options.get("skin_url");if(Of(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return Ph.baseURL+"/skins/ui/"+t}},Ef=e=>e.options.get("line_height_formats").split(" "),Bf=e=>{const t=uf(e),o=r(t),n=l(t)&&t.length>0;return!Af(e)&&(n||o||!0===t)},Mf=e=>{const t=z(9,(t=>e.options.get("toolbar"+(t+1)))),o=W(t,r);return ke(o.length>0,o)},Af=e=>Mf(e).fold((()=>{const t=uf(e);return f(t,r)&&t.length>0}),T),Df=e=>sf(e)===zh.bottom,Ff=e=>{if(!e.inline)return B.none();const t=rf(e);if(t.length>0)return ei(mt(),t);const o=af(e);return g(o)?B.some(Ie(o)):B.none()},If=e=>e.inline&&Ff(e).isSome(),Rf=e=>Ff(e).getOrThunk((()=>lt(it(Ie(e.getElement()))))),Vf=e=>e.inline&&!_f(e)&&!Bf(e)&&!Af(e),zf=e=>(e.options.get("toolbar_sticky")||e.inline)&&!If(e)&&!Vf(e),Hf=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var Pf=Object.freeze({__proto__:null,get ToolbarMode(){return Vh},get ToolbarLocation(){return zh},register:Uh,getSkinUrl:Tf,isReadOnly:jh,isSkinDisabled:Of,getHeightOption:Gh,getWidthOption:$h,getMinWidthOption:qh,getMinHeightOption:Xh,getMaxWidthOption:Kh,getMaxHeightOption:Yh,getUserStyleFormats:Jh,shouldMergeStyleFormats:Zh,shouldAutoHideStyleFormats:Qh,getLineHeightFormats:Ef,getContentLanguages:ef,getRemovedMenuItems:tf,isMenubarEnabled:_f,isMultipleToolbars:Af,isToolbarEnabled:Bf,isToolbarPersist:lf,getMultipleToolbarsOption:Mf,getUiContainer:Rf,useFixedContainer:If,getToolbarMode:of,isDraggableModal:vf,isDistractionFree:Vf,isStickyToolbar:zf,getStickyToolbarOffset:cf,getToolbarLocation:sf,isToolbarLocationBottom:Df,getToolbarGroups:nf,getMenus:Hf,getMenubar:df,getToolbar:uf,getFilePickerCallback:mf,getFilePickerTypes:pf,useTypeaheadUrls:hf,getAnchorTop:ff,getAnchorBottom:bf,getFilePickerValidatorHandler:gf,useStatusBar:yf,useElementPath:xf,useBranding:wf,getResize:Sf,getPasteAsText:kf,getSidebarShow:Cf});const Nf="[data-mce-autocompleter]",Lf=e=>ti(e,Nf);var Wf;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(Wf||(Wf={}));var Uf=Wf;const jf="tox-menu-nav__js",Gf="tox-collection__item",$f={normal:jf,color:"tox-swatch"},qf="tox-collection__item--enabled",Xf="tox-collection__item-icon",Kf="tox-collection__item-label",Yf="tox-collection__item-caret",Jf="tox-collection__item--active",Zf="tox-collection__item-container",Qf="tox-collection__item-container--row",eb=e=>be($f,e).getOr(jf),tb=e=>"color"===e?"tox-swatches":"tox-menu",ob=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:tb(e),tieredMenu:"tox-tiered-menu"}),nb=e=>{const t=ob(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:eb(e)}},sb=(e,t,o)=>{const n=ob(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},rb=[gh.parts.items({})],ab=(e,t,o)=>{const n=ob(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:nb(o)}},ib=(e,t)=>o=>{const n=H(o,t);return P(n,(t=>({dom:e,components:t})))},lb=(e,t)=>{const o=[];let n=[];return N(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),P(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},cb=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[gh.parts.items({preprocess:o=>"auto"!==e&&e>1?ib({tag:"div",classes:["tox-collection__group"]},e)(o):lb(o,((e,o)=>"separator"===t[o].type))})]}),db=e=>V(e,(e=>"icon"in e&&void 0!==e.icon)),ub=e=>(console.error(Pn(e)),console.log(e),B.none()),mb=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[gh.parts.items({preprocess:e=>lb(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},gb=(e,t,o,n,s)=>{if("color"===s){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[gh.parts.items({preprocess:"auto"!==e?ib({tag:"div",classes:["tox-swatches__row"]},e):x})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s&&"auto"===n){const t=cb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s&&1===n){const t=cb(1,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s){const t=cb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[gh.parts.items({preprocess:ib({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:sb(t,n,s),components:rb,items:o}},pb=qn("type"),hb=qn("name"),fb=qn("label"),bb=qn("text"),vb=qn("title"),yb=qn("icon"),xb=qn("value"),wb=Kn("fetch"),Sb=Kn("getSubmenuItems"),kb=Kn("onAction"),Cb=Kn("onItemAction"),Ob=gs("onSetup",(()=>b)),_b=os("name"),Tb=os("text"),Eb=os("icon"),Bb=os("tooltip"),Mb=os("label"),Ab=os("shortcut"),Db=ss("select"),Fb=ms("active",!1),Ib=ms("borderless",!1),Rb=ms("enabled",!0),Vb=ms("primary",!1),zb=e=>is("columns",e),Hb=is("meta",{}),Pb=gs("onAction",b),Nb=e=>ds("type",e),Lb=e=>Ln("name","name",an((()=>Kr(`${e}-name`))),En),Wb=xn([pb,Tb]),Ub=xn([Nb("autocompleteitem"),Fb,Rb,Hb,xb,Tb,Eb]),jb=[Rb,Bb,Eb,Tb,Ob],Gb=xn([pb,kb].concat(jb)),$b=e=>Vn("toolbarbutton",Gb,e),qb=[Fb].concat(jb),Xb=xn(qb.concat([pb,kb])),Kb=e=>Vn("ToggleButton",Xb,e),Yb=[gs("predicate",_),us("scope","node",["node","editor"]),us("position","selection",["node","selection","line"])],Jb=jb.concat([Nb("contextformbutton"),Vb,kb,Wn("original",x)]),Zb=qb.concat([Nb("contextformbutton"),Vb,kb,Wn("original",x)]),Qb=jb.concat([Nb("contextformbutton")]),ev=qb.concat([Nb("contextformtogglebutton")]),tv=Nn("type",{contextformbutton:Jb,contextformtogglebutton:Zb}),ov=xn([Nb("contextform"),gs("initValue",y("")),Mb,Zn("commands",tv),es("launch",Nn("type",{contextformbutton:Qb,contextformtogglebutton:ev}))].concat(Yb)),nv=xn([Nb("contexttoolbar"),qn("items")].concat(Yb)),sv=[pb,qn("src"),os("alt"),ps("classes",[],En)],rv=xn(sv),av=[pb,bb,_b,ps("classes",["tox-collection__item-label"],En)],iv=xn(av),lv=bn((()=>Fn("type",{cardimage:rv,cardtext:iv,cardcontainer:cv}))),cv=xn([pb,ds("direction","horizontal"),ds("align","left"),ds("valign","middle"),Zn("items",lv)]),dv=[Rb,Tb,Ab,("menuitem",Ln("value","value",an((()=>Kr("menuitem-value"))),On())),Hb];const uv=xn([pb,Mb,Zn("items",lv),Ob,Pb].concat(dv)),mv=xn([pb,Fb,Eb].concat(dv)),gv=[pb,qn("fancytype"),Pb],pv=[is("initData",{})].concat(gv),hv=[hs("initData",{},[ms("allowCustomColors",!0),rs("colors",On())])].concat(gv),fv=Nn("fancytype",{inserttable:pv,colorswatch:hv}),bv=xn([pb,Ob,Pb,Eb].concat(dv)),vv=xn([pb,Sb,Ob,Eb].concat(dv)),yv=xn([pb,Eb,Fb,Ob,kb].concat(dv)),xv=(e,t,o)=>{const n=Dc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return B.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return B.none()},wv=e=>((e,t)=>cl([Fp(e,t)]))(Kr("unnamed-events"),e),Sv=Kr("tooltip.exclusive"),kv=Kr("tooltip.show"),Cv=Kr("tooltip.hide"),Ov=(e,t,o)=>{e.getSystem().broadcastOn([Sv],{})};var _v=Object.freeze({__proto__:null,hideAllExclusive:Ov,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Dp.set(e,n)}))}}),Tv=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{bd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return _r(q([[Br(kv,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){Ov(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:_r("normal"===e.mode?[Br(Rs(),(e=>{xr(o,kv)})),Br(Fs(),(e=>{xr(o,Cv)}))]:[]),behaviours:cl([Dp.config({})])});t.setTooltip(s),pd(n,s),e.onShow(o,s),id.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Br(Cv,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Br(Zs(),((e,t)=>{const n=t;n.universal||R(n.channels,Sv)&&o(e)})),Hr((e=>{o(e)}))],"normal"===e.mode?[Br(Vs(),(e=>{xr(e,kv)})),Br(Ys(),(e=>{xr(e,Cv)})),Br(Rs(),(e=>{xr(e,kv)})),Br(Fs(),(e=>{xr(e,Cv)}))]:[Br(vr(),((e,t)=>{xr(e,kv)})),Br(yr(),(e=>{xr(e,Cv)}))]]))}}),Ev=[jn("lazySink"),jn("tooltipDom"),is("exclusive",!0),is("tooltipComponents",[]),is("delay",300),us("mode","normal",["normal","follow-highlight"]),is("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([Ki,Xi,ji,$i,Gi,qi]),onRtl:y([Ki,Xi,ji,$i,Gi,qi])}}))),bi("onHide"),bi("onShow")];const Bv=ul({fields:Ev,name:"tooltipping",active:Tv,state:Object.freeze({__proto__:null,init:()=>{const e=Hl(),t=Hl(),o=()=>{e.on(clearTimeout)},n=y("not-implemented");return ga({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}}),apis:_v}),Mv="silver.readonly",Av=xn([("readonly",Gn("readonly",Bn))]);const Dv=(e,t)=>{const o=e.outerContainer.element;t&&(e.mothership.broadcastOn([Fd()],{target:o}),e.uiMothership.broadcastOn([Fd()],{target:o})),e.mothership.broadcastOn([Mv],{readonly:t}),e.uiMothership.broadcastOn([Mv],{readonly:t})},Fv=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&Dv(t,!0)})),e.on("SwitchMode",(()=>Dv(t,e.mode.isReadOnly()))),jh(e)&&e.mode.set("readonly")},Iv=()=>hl.config({channels:{[Mv]:{schema:Av,onReceive:(e,t)=>{ym.set(e,t.readonly)}}}}),Rv=e=>ym.config({disabled:e}),Vv=e=>ym.config({disabled:e,disableClass:"tox-tbtn--disabled"}),zv=e=>ym.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),Hv=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Pv=(e,t)=>zr((o=>{Hv(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Nv=(e,t)=>Hr((o=>Hv(e,o)(t.get()))),Lv=(e,t)=>Nr(((o,n)=>{Hv(e,o)(e.onAction),e.triggersSubmenu||t!==Uf.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&xr(o,nr()),n.stop())})),Wv={[Qs()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Uv=we,jv=(e,t,o,n)=>{const s=fs(b);return{type:"item",dom:t.dom,components:Uv(t.optComponents),data:e.data,eventOrder:Wv,hasSubmenu:e.triggersSubmenu,itemBehaviours:cl([Fp("item-events",[Lv(e,o),Pv(e,s),Nv(e,s)]),(r=()=>!e.enabled||n.isDisabled(),ym.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),Iv(),Dp.config({})].concat(e.itemBehaviours))};var r},Gv=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),$v=e=>{const t=Nh.os.isMacOS()||Nh.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=P(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},qv=(e,t,o=[Xf])=>Fh(e,{tag:"div",classes:o},t),Xv=e=>({dom:{tag:"div",classes:[Kf]},components:[La(Ch.translate(e))]}),Kv=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),Yv=(e,t)=>({dom:{tag:"div",classes:[Kf]},components:[{dom:{tag:e.tag,styles:e.styles},components:[La(Ch.translate(t))]}]}),Jv=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[La($v(e))]}),Zv=e=>qv("checkmark",e,["tox-collection__item-checkmark"]),Qv=e=>{const t=e.map((e=>({attributes:{title:Ch.translate(e)}}))).getOr({});return{tag:"div",classes:[jf,Gf],...t}},ey=(e,t,o,n=B.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Bh(e,n).or(o).getOrThunk(Th(n))})(e,t.icons,o)));return{dom:(()=>{const e=r.getOr(""),o=n.map((e=>({title:t.translate(e)}))).getOr({}),a={tag:"div",attributes:o,classes:["tox-swatch"]};return"custom"===s?{...a,tag:"button",classes:[...a.classes,"tox-swatches__picker-btn"],innerHtml:e}:"remove"===s?{...a,classes:[...a.classes,"tox-swatch--remove"],innerHtml:e}:{...a,attributes:{...a.attributes,"data-mce-color":s},styles:{"background-color":s}}})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[Xf]},r=o?e.iconContent.map((e=>Fh(e,s,t.icons,n))).orThunk((()=>B.some({dom:s}))):B.none(),a=e.checkMark,i=B.from(e.meta).fold((()=>Xv),(e=>ve(e,"style")?S(Yv,e.style):Xv)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>B.some(Kv(e,[Kf]))));return{dom:Qv(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Jv),a,e.caret]}})(e,t,o,n),ty=(e,t)=>be(e,"tooltipWorker").map((e=>[Bv.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:Xl}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{Bv.setComponents(t,[Wa({element:Ie(e)})])}))}})])).getOr([]),oy=(e,t)=>{const o=(e=>Hh.DOM.encode(e))(Ch.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},ny=(e,t)=>P(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Qf,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[Zf,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,ny(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?B.from(t.cardText.matchText).getOr(""):"";return Kv(oy(e.text,n),e.classes)}})),sy=Fu(ah(),ih()),ry=e=>({value:e}),ay=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,iy=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ly=e=>ay.test(e)||iy.test(e),cy=e=>{return(t=e,((e,t)=>Ce(e,t,0))(t,"#")?((e,t)=>e.substring(t))(t,"#".length):t).toUpperCase();var t},dy=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},uy=e=>{const t=dy(e.red)+dy(e.green)+dy(e.blue);return ry(t)},my=Math.min,gy=Math.max,py=Math.round,hy=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,fy=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,by=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),vy=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},yy=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=gy(0,my(r,1)),a=gy(0,my(a,1)),0===r)return t=o=n=py(255*a),by(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=py(255*(t+d)),o=py(255*(o+d)),n=py(255*(n+d)),by(t,o,n,1)},xy=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(ay,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=iy.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return by(o,n,s,1)},wy=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return by(s,r,a,i)},Sy=e=>{if("transparent"===e)return B.some(by(0,0,0,0));const t=hy.exec(e);if(null!==t)return B.some(wy(t[1],t[2],t[3],"1"));const o=fy.exec(e);return null!==o?B.some(wy(o[1],o[2],o[3],o[4])):B.none()},ky=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,Cy=by(255,0,0,1),Oy=(e,t)=>e.dispatch("ResizeContent",t),_y=(e,t,o)=>({hue:e,saturation:t,value:o}),Ty=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,_y(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,_y(Math.round(t),Math.round(100*o),Math.round(100*n)))},Ey=e=>uy(yy(e)),By=e=>{return(t=e,ly(t)?B.some({value:cy(t)}):B.none()).orThunk((()=>Sy(e).map(uy))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return uy(by(s,r,a,i))}));var t};var My=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const Ay="tinymce-custom-colors",Dy=((e=10)=>{const t=My.getItem(Ay),o=r(t)?JSON.parse(t):[],n=e-(s=o).length<0?s.slice(0,e):s;var s;const a=e=>{n.splice(e,1)};return{add:t=>{I(n,t).each(a),n.unshift(t),n.length>e&&n.pop(),My.setItem(Ay,JSON.stringify(n))},state:()=>n.slice(0)}})(10),Fy=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+By(e[o]).value,type:"choiceitem"});return t},Iy=e=>t=>t.options.get(e),Ry=Iy("color_cols"),Vy=Iy("custom_colors"),zy=Iy("color_map"),Hy=e=>{Dy.add(e)},Py="#000000",Ny=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},Ly=(e,t,o,n)=>{"custom"===o?qy(e)((o=>{o.each((o=>{Hy(o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Py):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Wy=(e,t)=>e.concat(P(Dy.state(),(e=>({type:"choiceitem",text:e,value:e}))).concat(Ny(t))),Uy=(e,t)=>o=>{o(Wy(e,t))},jy=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},Gy=(e,t,o,n,s)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:t=>{const n=((e,t)=>{let o;return e.dom.getParents(e.selection.getStart(),(e=>{let n;(n=e.style["forecolor"===t?"color":"background-color"])&&(o=o||n)})),B.from(o)})(e,o);return n.bind((e=>Sy(e).map((e=>{const o=uy(e).value;return Oe(t.toLowerCase(),o)})))).getOr(!1)},columns:Ry(e),fetch:Uy(zy(e),Vy(e)),onAction:t=>{Ly(e,o,s.get(),b)},onItemAction:(n,r)=>{Ly(e,o,r,(o=>{s.set(o),((e,t)=>{e.dispatch("TextColorChange",t)})(e,{name:t,color:o})}))},onSetup:o=>{jy(o,t,s.get());const n=e=>{e.name===t&&jy(o,e.name,e.color)};return e.on("TextColorChange",n),()=>{e.off("TextColorChange",n)}}})},$y=(e,t,o,n)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",onAction:t=>{Ly(e,o,t.value,b)}}]})},qy=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(B.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(B.none())}})},Xy=(e,t,o,n,s,r,a,i)=>{const l=db(t),c=Ky(t,o,n,"color"!==s?"normal":"color",r,a,i);return gb(e,l,c,n,s)},Ky=(e,t,o,n,s,r,a)=>we(P(e,(i=>{return"choiceitem"===i.type?(l=i,Vn("choicemenuitem",mv,l)).fold(ub,(l=>B.some(((e,t,o,n,s,r,a,i=!0)=>{const l=ey({presets:o,textContent:t?e.text:B.none(),htmlContent:B.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:B.none(),checkMark:t?B.some(Zv(a.icons)):B.none(),caret:B.none(),value:e.value},a,i);return sn(jv({data:Gv(e),enabled:e.enabled,getApi:e=>({setActive:t=>{$p.set(e,t)},isActive:()=>$p.isOn(e),isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:qf,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(l,1===o,n,t,r(i.value),s,a,db(e))))):B.none();var l}))),Yy=(e,t)=>{const o=nb(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}},Jy=Kr("cell-over"),Zy=Kr("cell-execute"),Qy=(e,t,o)=>{const n=o=>wr(o,Zy,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return Ga({dom:{tag:"div",attributes:{role:"button","aria-labelledby":o}},behaviours:cl([Fp("insert-table-picker-cell",[Br(Rs(),Hp.focus),Br(Qs(),n),Br(Ws(),s),Br(tr(),s)]),$p.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Hp.config({onFocus:o=>wr(o,Jy,{row:e,col:t})})])})},ex=e=>X(e,(e=>P(e,$a))),tx=(e,t)=>La(`${t}x${e}`),ox={inserttable:e=>{const t=Kr("size-label"),o=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++)o.push(Qy(t,n,e));n.push(o)}return n})(t),n=tx(0,0),s=kh({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[n],behaviours:cl([Dp.config({})])});return{type:"widget",data:{value:Kr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[sy.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:ex(o).concat(s.asSpec()),behaviours:cl([Fp("insert-table-picker",[zr((e=>{Dp.set(s.get(e),[n])})),Fr(Jy,((e,t,n)=>{const{row:r,col:a}=n.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)$p.set(e[n][s],n<=t&&s<=o)})(o,r,a),Dp.set(s.get(e),[tx(r+1,a+1)])})),Fr(Zy,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),xr(t,nr())}))]),kp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Wy(t.colorinput.getColors(),o)),(e=>e.concat(Ny(o))))})(e,t),n=t.colorinput.getColorCols(),s="color",r={...Xy(Kr("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,Uf.CLOSE_ON_EXECUTE,_,t.shared.providers),markers:nb(s),movement:Yy(n,s)};return{type:"widget",data:{value:Kr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[sy.widget(gh.sketch(r))]}}},nx=e=>({type:"separator",dom:{tag:"div",classes:[Gf,"tox-collection__group-heading"]},components:e.text.map(La).toArray()});var sx;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(sx||(sx={}));const rx=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:B.none(),icon:e.text.isSome()?B.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Vn("menuitem",bv,i)).fold(ub,(e=>B.some(((e,t,o,n=!0)=>{const s=ey({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,caret:B.none(),checkMark:B.none(),shortcutContent:e.shortcut},o,n);return jv({data:Gv(e),getApi:e=>({isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Vn("nestedmenuitem",vv,e))(e).fold(ub,(e=>B.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,qv("chevron-down",a,[Yf])):(e=>qv("chevron-right",e,[Yf]))(o.icons);var a;const i=ey({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,caret:B.some(r),checkMark:B.none(),shortcutContent:e.shortcut},o,n);return jv({data:Gv(e),getApi:e=>({isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t)}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Vn("togglemenuitem",yv,e))(e).fold(ub,(e=>B.some(((e,t,o,n=!0)=>{const s=ey({iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,checkMark:B.some(Zv(o.icons)),caret:B.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return sn(jv({data:Gv(e),enabled:e.enabled,getApi:e=>({setActive:t=>{$p.set(e,t)},isActive:()=>$p.isOn(e),isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:qf,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>Vn("separatormenuitem",Wb,e))(e).fold(ub,(e=>B.some(nx(e))));case"fancymenuitem":return(e=>Vn("fancymenuitem",fv,e))(e).fold(ub,(e=>((e,t)=>be(ox,e.fancytype).map((o=>o(e,t))))(a(e),o)));default:return console.error("Unknown item in general menu",e),B.none()}var i},ax=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||db(e);return we(P(e,(e=>{switch(e.type){case"separator":return(n=e,Vn("Autocompleter.Separator",Wb,n)).fold(ub,(e=>B.some(nx(e))));case"cardmenuitem":return(e=>Vn("cardmenuitem",uv,e))(e).fold(ub,(e=>B.some(((e,t,o,n)=>{const s={dom:Qv(e.label),optComponents:[B.some({dom:{tag:"div",classes:[Zf,Qf]},components:ny(e.items,n)})]};return jv({data:Gv({text:B.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>{ym.set(e,!t),N(Dc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(ym)&&ym.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:B.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:ty(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Vn("Autocompleter.Item",Ub,e))(e).fold(ub,(e=>B.some(((e,t,o,n,s,r,a,i=!0)=>{const l=ey({presets:n,textContent:B.none(),htmlContent:o?e.text.map((e=>oy(e,t))):B.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:B.none(),checkMark:B.none(),caret:B.none(),value:e.value},a.providers,i,e.icon);return jv({data:Gv(e),enabled:e.enabled,getApi:y({}),onAction:t=>s(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:ty(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},ix=(e,t,o,n,s)=>{const r=db(t),a=we(P(t,(e=>{const t=e=>rx(e,o,n,(e=>s?!ve(e,"text"):r)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)})));return(s?mb:gb)(e,r,a,1,"normal")},lx=e=>yh.singleData(e.value,e),cx=(e,t)=>{const o=fs(!1),n=fs(!1),s=Ga(xh.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:cl([Fp("dismissAutocompleter",[Br(mr(),(()=>c()))])]),lazySink:t.getSink})),r=()=>xh.isOpen(s),a=n.get,i=()=>{r()&&xh.hide(s)},l=()=>xh.getContent(s).bind((e=>te(e.components(),0))),c=()=>e.execCommand("mceAutocompleterClose"),d=n=>{const r=(n=>{const s=re(n,(e=>B.from(e.columns))).getOr(1);return X(n,(n=>{const r=n.items;return ax(r,n.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>Lf(Ie(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const a={hide:()=>c(),reload:t=>{i(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(a,r,t,s),o.set(!1)}))}),s,Uf.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);r.length>0?((t,o)=>{var n;(n=Ie(e.getBody()),ei(n,Nf)).each((n=>{const r=re(t,(e=>B.from(e.columns))).getOr(1);xh.showMenuAt(s,{anchor:{type:"node",root:Ie(e.getBody()),node:B.from(n)}},((e,t,o,n)=>{const s=Yy(t,n),r=nb(n);return{data:lx({...e,movement:s,menuBehaviours:wv("auto"!==t?[]:[zr(((e,t)=>{xv(e,4,r.item).each((({numColumns:t,numRows:o})=>{kp.setGridSize(e,o,t)}))}))])}),menu:{markers:nb(n),fakeFocus:o===sx.ContentFocus}}})(gb("autocompleter-value",!0,o,r,"normal"),r,sx.ContentFocus,"normal"))})),l().each(Bm.highlightFirst)})(n,r):i()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),d(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>d(e))),e.on("AutocompleterEnd",(()=>{i(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{wr(e,Hs(),{raw:t})},n=()=>e.getMenu().bind(Bm.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Sr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Bm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&Lf(Ie(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:c,isMenuOpen:r,isActive:a,isProcessingAction:o.get,getMenu:l},e)},dx=(e,t,o)=>ti(e,t,o).isSome(),ux=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},mx=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?B.none():B.some(t.touches[0])},gx=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Hl(),o=fs(!1),n=ux((t=>{e.triggerEvent(or(),t),o.set(!0)}),400),s=ys([{key:Ts(),value:e=>(mx(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),B.none())},{key:Es(),value:e=>(n.cancel(),mx(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),B.none())},{key:Bs(),value:s=>(n.cancel(),t.get().filter((e=>Ge(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(tr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=P(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Nl(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Hl(),a=Nl(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(Js(),e)}),0))})),i=Nl(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Mm[0]&&!R(["input","textarea"],Ve(e.target))&&!dx(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Nl(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Hl(),d=Nl(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(Ys(),e)}),0))}));return{unbind:()=>{N(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},px=(e,t)=>{const o=be(e,"target").getOr(t);return fs(o)},hx=bs([{stopped:[]},{resume:["element"]},{complete:[]}]),fx=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=fs(!1),n=fs(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),hx.complete())),(e=>{const o=e.descHandler;return fa(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),hx.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),hx.complete()):Je(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),hx.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),hx.resume(n))))}))},bx=(e,t,o,n,s,r)=>fx(e,t,o,n,s,r).fold(T,(n=>bx(e,t,o,n,s,r)),_),vx=(e,t,o,n,s)=>{const r=px(o,n);return bx(e,t,o,n,r,s)},yx=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:S.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>Ss(n,(t=>((e,t)=>ta(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{ta(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return ta(t).getOrThunk((()=>((e,t)=>{const o=Kr(Zr+"uid-");return ea(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+jr(s.element)+"\nCannot use it for: "+jr(e.element)+"\nThe conflicting element is"+(ut(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},xx=em({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:eu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[is("components",[]),Qd("containerBehaviours",[]),is("events",{}),is("domModification",{}),is("eventOrder",{})]}),wx=e=>{const t=t=>Je(e.element).fold(T,(e=>Ge(t,e))),o=yx(),n=(e,n)=>o.find(t,e,n),s=gx(e.element,{triggerEvent:(e,t)=>li(e,t.target,(o=>((e,t,o,n)=>vx(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:y("real"),triggerEvent:(e,t,o)=>{li(e,t,(s=>vx(n,e,o,t,s)))},triggerFocus:(e,t)=>{ta(e).fold((()=>{bl(e)}),(o=>{li(Ks(),e,(o=>(((e,t,o,n,s)=>{const r=px(o,n);fx(e,t,o,n,r,s)})(n,Ks(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:Ga,buildOrPatch:ja,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:T},a=e=>{e.connect(r),Pe(e.element)||(o.register(e),N(e.components(),a),r.triggerEvent(rr(),e.element,{target:e.element}))},i=e=>{Pe(e.element)||(N(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{pd(e,t)},c=e=>{bd(e)},d=e=>{const t=o.filter(Zs());N(t,(t=>{const o=t.descHandler;fa(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=fs(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:_,event:e,setSource:C("Cannot set source of a broadcasted event"),getSource:C("Cannot get source of a broadcasted event")}})(t);return N(e,(e=>{const t=e.descHandler;fa(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>qo.error(new Error('Could not find component with uid: "'+e+'" in system.'))),qo.value),h=e=>{const t=ta(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Do(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},Sx=y([is("prefix","form-field"),Qd("fieldBehaviours",[rm,Zd])]),kx=y([Eu({schema:[jn("dom")],name:"label"}),Eu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[jn("text")],name:"aria-descriptor"}),_u({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[jn("factory")],name:"field"})]),Cx=tm({name:"FormField",configFields:Sx(),partFields:kx(),factory:(e,t,o,n)=>{const s=tu(e.fieldBehaviours,[rm.config({find:t=>Nu(t,e,"field")}),Zd.config({store:{mode:"manual",getValue:e=>rm.getCurrent(e).bind(Zd.getValue),setValue:(e,t)=>{rm.getCurrent(e).each((e=>{Zd.setValue(e,t)}))}}})]),r=_r([zr(((t,o)=>{const n=Wu(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Kr(e.prefix);n.label().each((e=>{ht(e.element,"for",o),ht(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Kr(e.prefix);ht(o.element,"id",n),ht(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>Nu(t,e,"field"),getLabel:t=>Nu(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var Ox=Object.freeze({__proto__:null,exhibit:(e,t)=>ha({attributes:ys([{key:t.tabAttr,value:"true"}])})}),_x=[is("tabAttr","data-alloy-tabstop")];const Tx=ul({fields:_x,name:"tabstopping",active:Ox});var Ex=tinymce.util.Tools.resolve("tinymce.html.Entities");const Bx=(e,t,o,n)=>{const s=Mx(e,t,o,n);return Cx.sketch(s)},Mx=(e,t,o,n)=>({dom:Ax(o),components:e.toArray().concat([t]),fieldBehaviours:cl(n)}),Ax=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),Dx=(e,t)=>Cx.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[La(t.translate(e))]}),Fx=Kr("form-component-change"),Ix=Kr("form-close"),Rx=Kr("form-cancel"),Vx=Kr("form-action"),zx=Kr("form-submit"),Hx=Kr("form-block"),Px=Kr("form-unblock"),Nx=Kr("form-tabchange"),Lx=Kr("form-resize"),Wx=y([Qn("data"),is("inputAttributes",{}),is("inputStyles",{}),is("tag","input"),is("inputClasses",[]),bi("onSetValue"),is("styles",{}),is("eventOrder",{}),Qd("inputBehaviours",[Zd,Hp]),is("selectOnFocus",!0)]),Ux=e=>cl([Hp.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Da(t);t.dom.setSelectionRange(0,o.length)}:b})]),jx=e=>({...Ux(e),...tu(e.inputBehaviours,[Zd.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Da(e.element),setValue:(e,t)=>{Da(e.element)!==t&&Fa(e.element,t)}},onSetValue:e.onSetValue})])}),Gx=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),$x=em({name:"Input",configFields:Wx(),factory:(e,t)=>({uid:e.uid,dom:Gx(e),components:[],behaviours:jx(e),eventOrder:e.eventOrder})}),qx=e=>{let t=B.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=B.some(e),N(o,r),o=[])})),{get:n,map:e=>qx((t=>{n((o=>{t(e(o))}))})),isReady:s}},Xx={nu:qx,pure:e=>qx((t=>{t(e)}))},Kx=e=>{setTimeout((()=>{throw e}),0)},Yx=e=>{const t=t=>{e().then(t,Kx)};return{map:t=>Yx((()=>e().then(t))),bind:t=>Yx((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>Yx((()=>e().then((()=>t.toPromise())))),toLazy:()=>Xx.nu(t),toCached:()=>{let t=null;return Yx((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},Jx=e=>Yx((()=>new Promise(e))),Zx=e=>Yx((()=>Promise.resolve(e))),Qx=["input","textarea"],ew=e=>{const t=Ve(e);return R(Qx,t)},tw=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Ea(o,t.invalidClass),t.notify.each((t=>{ew(e.element)&&ht(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{Wr(e,t.validHtml)})),t.onValid(e)}))},ow=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);Ta(s,t.invalidClass),t.notify.each((t=>{ew(e.element)&&ht(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{Wr(e,n)})),t.onInvalid(e,n)}))},nw=(e,t,o)=>t.validator.fold((()=>Zx(qo.value(!0))),(t=>t.validate(e))),sw=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),nw(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(ow(e,t,0,o),qo.error(o))),(o=>(tw(e,t),qo.value(o)))):qo.error("No longer in system"))));var rw=Object.freeze({__proto__:null,markValid:tw,markInvalid:ow,query:nw,run:sw,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Ba(o,t.invalidClass)}}),aw=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>_r([Br(t.onEvent,(t=>{sw(t,e).get(x)}))].concat(t.validateOnLoad?[zr((t=>{sw(t,e).get(b)}))]:[])))).getOr({})}),iw=[jn("invalidClass"),is("getRoot",B.none),as("notify",[is("aria","alert"),is("getContainer",B.none),is("validHtml",""),bi("onValid"),bi("onInvalid"),bi("onValidate")]),as("validator",[jn("validate"),is("onEvent","input"),is("validateOnLoad",!0)])];const lw=ul({fields:iw,name:"invalidating",active:aw,apis:rw,extra:{validation:e=>t=>{const o=Zd.getValue(t);return Zx(e(o))}}});var cw=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n)}),dw=[Gn("others",Rn(qo.value,On()))],uw=Object.freeze({__proto__:null,init:()=>{const e={},t=y({});return ga({readState:t,getOrCreate:(t,o,n)=>{const s=ae(o.others);if(s)return be(e,n).getOrThunk((()=>{const s=be(o.others,n).getOrDie("No information found for coupled component: "+n)(t),r=t.getSystem().build(s);return e[n]=r,r}));throw new Error("Cannot find coupled component: "+n+". Known coupled components: "+JSON.stringify(s,null,2))}})}});const mw=ul({fields:dw,name:"coupling",apis:cw,state:uw}),gw=y("sink"),pw=y(Eu({name:gw(),overrides:y({dom:{tag:"div"},behaviours:cl([id.config({useFixed:T})]),events:_r([Ir(Hs()),Ir(As()),Ir(Ws())])})}));var hw;!function(e){e[e.HighlightFirst=0]="HighlightFirst",e[e.HighlightNone=1]="HighlightNone"}(hw||(hw={}));const fw=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},bw=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=xw(n,e);return i.map((e=>e.bind((e=>B.from(yh.sketch({...r.menu(),uid:oa(""),data:e,highlightImmediately:a===hw.HighlightFirst,onOpenMenu:(e,t)=>{const n=l().getOrDie();id.position(n,t,{anchor:o}),Dd.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();id.position(n,o,{anchor:{type:"submenu",item:t}}),Dd.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();id.position(s,t,{anchor:o}),N(n,(e=>{id.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(Hp.focus(n),Dd.close(s),B.some(!0))}))))))})(e,t,fw(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Dd.isOpen(n)&&Dd.close(n)}),(e=>{Dd.cloak(n),Dd.open(n,e),r(n)})),n)))},vw=(e,t,o,n,s,r,a)=>(Dd.close(n),Zx(n)),yw=(e,t,o,n,s,r)=>{const a=mw.getCoupled(o,"sandbox");return(Dd.isOpen(a)?vw:bw)(e,t,o,a,n,s,r)},xw=(e,t)=>e.getSystem().getByUid(t.uid+"-"+gw()).map((e=>()=>qo.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>qo.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),ww=e=>{Dd.getState(e).each((e=>{yh.repositionMenus(e)}))},Sw=(e,t,o)=>{const n=ni(),s=xw(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:nu(e.sandboxBehaviours,[Zd.config({store:{mode:"memory",initialValue:t}}),Dd.config({onOpen:(s,r)=>{const a=fw(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=rm.getCurrent(t).getOr(t),s=Ut(e.element);o?kt(n.element,"min-width",s+"px"):((e,t)=>{Wt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>si(o,n)||si(t,n),getAttachPoint:()=>s().getOrDie()}),rm.config({find:e=>Dd.getState(e).bind((e=>rm.getCurrent(e)))}),hl.config({channels:{...zd({isExtraPart:_}),...Pd({doReposition:ww})}})])}},kw=e=>{const t=mw.getCoupled(e,"sandbox");ww(t)},Cw=()=>[is("sandboxClasses",[]),ou("sandboxBehaviours",[rm,hl,Dd,Zd])],Ow=y([jn("dom"),jn("fetch"),bi("onOpen"),vi("onExecute"),is("getHotspot",B.some),is("getAnchorOverrides",y({})),ac(),Qd("dropdownBehaviours",[$p,mw,kp,Hp]),jn("toggleClass"),is("eventOrder",{}),Qn("lazySink"),is("matchWidth",!1),is("useMinWidth",!1),Qn("role")].concat(Cw())),_w=y([Tu({schema:[pi()],name:"menu",defaults:e=>({onExecute:e.onExecute})}),pw()]),Tw=tm({name:"Dropdown",configFields:Ow(),partFields:_w(),factory:(e,t,o,n)=>{const s=e=>{Dd.getState(e).each((e=>{yh.highlightPrimary(e)}))},r={expand:t=>{$p.isOn(t)||yw(e,x,t,n,b,hw.HighlightNone).get(b)},open:t=>{$p.isOn(t)||yw(e,x,t,n,b,hw.HighlightFirst).get(b)},isOpen:$p.isOn,close:t=>{$p.isOn(t)&&yw(e,x,t,n,b,hw.HighlightFirst).get(b)},repositionMenus:e=>{$p.isOn(e)&&kw(e)}},a=(e,t)=>(Sr(e),B.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:tu(e.dropdownBehaviours,[$p.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),mw.config({others:{sandbox:t=>Sw(e,t,{onOpen:()=>$p.on(t),onClose:()=>$p.off(t)})}}),kp.config({mode:"special",onSpace:a,onEnter:a,onDown:(e,t)=>{if(Tw.isOpen(e)){const t=mw.getCoupled(e,"sandbox");s(t)}else Tw.open(e);return B.some(!0)},onEscape:(e,t)=>Tw.isOpen(e)?(Tw.close(e),B.some(!0)):B.none()}),Hp.config({})]),events:Xp(B.some((t=>{yw(e,x,t,n,s,hw.HighlightFirst).get(b)}))),eventOrder:{...e.eventOrder,[Qs()]:["disabling","toggling","alloy.base.behaviour"]},apis:r,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Ew=ul({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>_r([Tr($s(),T)]),exhibit:()=>ha({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Bw=Kr("color-input-change"),Mw=Kr("color-swatch-change"),Aw=Kr("color-picker-cancel"),Dw=Eu({schema:[jn("dom")],name:"label"}),Fw=e=>Eu({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:_r([Mr(Ts(),((t,o,n)=>e(t,n)),[t]),Mr(As(),((t,o,n)=>e(t,n)),[t]),Mr(Ds(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Iw=Fw("top-left"),Rw=Fw("top"),Vw=Fw("top-right"),zw=Fw("right"),Hw=Fw("bottom-right"),Pw=Fw("bottom"),Nw=Fw("bottom-left");var Lw=[Dw,Fw("left"),zw,Rw,Pw,Iw,Vw,Nw,Hw,_u({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:_r([Dr(Ts(),e,"spectrum"),Dr(Es(),e,"spectrum"),Dr(Bs(),e,"spectrum"),Dr(As(),e,"spectrum"),Dr(Ds(),e,"spectrum"),Dr(Is(),e,"spectrum")])})}),_u({schema:[Wn("mouseIsDown",(()=>fs(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:cl([kp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),Hp.config({})]),events:_r([Br(Ts(),o),Br(Es(),o),Br(As(),o),Br(Ds(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const Ww=y("slider.change.value"),Uw=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?B.some(e.touches[0]).map((e=>Ht(e.clientX,e.clientY))):B.none()}{const e=t;return void 0!==e.clientX?B.some(e).map((e=>Ht(e.clientX,e.clientY))):B.none()}},jw=e=>e.model.minX,Gw=e=>e.model.minY,$w=e=>e.model.minX-1,qw=e=>e.model.minY-1,Xw=e=>e.model.maxX,Kw=e=>e.model.maxY,Yw=e=>e.model.maxX+1,Jw=e=>e.model.maxY+1,Zw=(e,t,o)=>t(e)-o(e),Qw=e=>Zw(e,Xw,jw),eS=e=>Zw(e,Kw,Gw),tS=e=>Qw(e)/2,oS=e=>eS(e)/2,nS=e=>e.stepSize,sS=e=>e.snapToGrid,rS=e=>e.snapStart,aS=e=>e.rounded,iS=(e,t)=>void 0!==e[t+"-edge"],lS=e=>iS(e,"left"),cS=e=>iS(e,"right"),dS=e=>iS(e,"top"),uS=e=>iS(e,"bottom"),mS=e=>e.model.value.get(),gS=(e,t)=>({x:e,y:t}),pS=(e,t)=>{wr(e,Ww(),{value:t})},hS=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),fS=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),bS=(e,t,o)=>Math.max(t,Math.min(o,e)),vS=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=bS(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return bS(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},yS=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},xS="top",wS="right",SS="bottom",kS="left",CS=e=>e.element.dom.getBoundingClientRect(),OS=(e,t)=>e[t],_S=e=>{const t=CS(e);return OS(t,kS)},TS=e=>{const t=CS(e);return OS(t,wS)},ES=e=>{const t=CS(e);return OS(t,xS)},BS=e=>{const t=CS(e);return OS(t,SS)},MS=e=>{const t=CS(e);return OS(t,"width")},AS=e=>{const t=CS(e);return OS(t,"height")},DS=(e,t,o)=>(e+t)/2-o,FS=(e,t)=>{const o=CS(e),n=CS(t),s=OS(o,kS),r=OS(o,wS),a=OS(n,kS);return DS(s,r,a)},IS=(e,t)=>{const o=CS(e),n=CS(t),s=OS(o,xS),r=OS(o,SS),a=OS(n,xS);return DS(s,r,a)},RS=(e,t)=>{wr(e,Ww(),{value:t})},VS=(e,t,o)=>{const n={min:jw(t),max:Xw(t),range:Qw(t),value:o,step:nS(t),snap:sS(t),snapStart:rS(t),rounded:aS(t),hasMinEdge:lS(t),hasMaxEdge:cS(t),minBound:_S(e),maxBound:TS(e),screenRange:MS(e)};return vS(n)},zS=e=>(t,o)=>((e,t,o)=>{const n=(e>0?fS:hS)(mS(o),jw(o),Xw(o),nS(o));return RS(t,n),B.some(n)})(e,t,o).map(T),HS=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=MS(e),a=n.bind((t=>B.some(FS(t,e)))).getOr(0),i=s.bind((t=>B.some(FS(t,e)))).getOr(r),l={min:jw(t),max:Xw(t),range:Qw(t),value:o,hasMinEdge:lS(t),hasMaxEdge:cS(t),minBound:_S(e),minOffset:0,maxBound:TS(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return yS(l)})(t,r,o,n,s);return _S(t)-_S(e)+a},PS=zS(-1),NS=zS(1),LS=B.none,WS=B.none,US={"top-left":B.none(),top:B.none(),"top-right":B.none(),right:B.some(((e,t)=>{pS(e,Yw(t))})),"bottom-right":B.none(),bottom:B.none(),"bottom-left":B.none(),left:B.some(((e,t)=>{pS(e,$w(t))}))};var jS=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=VS(e,t,o);return RS(e,n),n},setToMin:(e,t)=>{const o=jw(t);RS(e,o)},setToMax:(e,t)=>{const o=Xw(t);RS(e,o)},findValueOfOffset:VS,getValueFromEvent:e=>Uw(e).map((e=>e.left)),findPositionOfValue:HS,setPositionFromValue:(e,t,o,n)=>{const s=mS(o),r=HS(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Ut(t.element)/2;kt(t.element,"left",r-a+"px")},onLeft:PS,onRight:NS,onUp:LS,onDown:WS,edgeActions:US});const GS=(e,t)=>{wr(e,Ww(),{value:t})},$S=(e,t,o)=>{const n={min:Gw(t),max:Kw(t),range:eS(t),value:o,step:nS(t),snap:sS(t),snapStart:rS(t),rounded:aS(t),hasMinEdge:dS(t),hasMaxEdge:uS(t),minBound:ES(e),maxBound:BS(e),screenRange:AS(e)};return vS(n)},qS=e=>(t,o)=>((e,t,o)=>{const n=(e>0?fS:hS)(mS(o),Gw(o),Kw(o),nS(o));return GS(t,n),B.some(n)})(e,t,o).map(T),XS=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=AS(e),a=n.bind((t=>B.some(IS(t,e)))).getOr(0),i=s.bind((t=>B.some(IS(t,e)))).getOr(r),l={min:Gw(t),max:Kw(t),range:eS(t),value:o,hasMinEdge:dS(t),hasMaxEdge:uS(t),minBound:ES(e),minOffset:0,maxBound:BS(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return yS(l)})(t,r,o,n,s);return ES(t)-ES(e)+a},KS=B.none,YS=B.none,JS=qS(-1),ZS=qS(1),QS={"top-left":B.none(),top:B.some(((e,t)=>{pS(e,qw(t))})),"top-right":B.none(),right:B.none(),"bottom-right":B.none(),bottom:B.some(((e,t)=>{pS(e,Jw(t))})),"bottom-left":B.none(),left:B.none()};var ek=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=$S(e,t,o);return GS(e,n),n},setToMin:(e,t)=>{const o=Gw(t);GS(e,o)},setToMax:(e,t)=>{const o=Kw(t);GS(e,o)},findValueOfOffset:$S,getValueFromEvent:e=>Uw(e).map((e=>e.top)),findPositionOfValue:XS,setPositionFromValue:(e,t,o,n)=>{const s=mS(o),r=XS(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Rt(t.element)/2;kt(t.element,"top",r-a+"px")},onLeft:KS,onRight:YS,onUp:JS,onDown:ZS,edgeActions:QS});const tk=(e,t)=>{wr(e,Ww(),{value:t})},ok=(e,t)=>({x:e,y:t}),nk=(e,t)=>(o,n)=>((e,t,o,n)=>{const s=e>0?fS:hS,r=t?mS(n).x:s(mS(n).x,jw(n),Xw(n),nS(n)),a=t?s(mS(n).y,Gw(n),Kw(n),nS(n)):mS(n).y;return tk(o,ok(r,a)),B.some(r)})(e,t,o,n).map(T),sk=nk(-1,!1),rk=nk(1,!1),ak=nk(-1,!0),ik=nk(1,!0),lk={"top-left":B.some(((e,t)=>{pS(e,gS($w(t),qw(t)))})),top:B.some(((e,t)=>{pS(e,gS(tS(t),qw(t)))})),"top-right":B.some(((e,t)=>{pS(e,gS(Yw(t),qw(t)))})),right:B.some(((e,t)=>{pS(e,gS(Yw(t),oS(t)))})),"bottom-right":B.some(((e,t)=>{pS(e,gS(Yw(t),Jw(t)))})),bottom:B.some(((e,t)=>{pS(e,gS(tS(t),Jw(t)))})),"bottom-left":B.some(((e,t)=>{pS(e,gS($w(t),Jw(t)))})),left:B.some(((e,t)=>{pS(e,gS($w(t),oS(t)))}))};var ck=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=VS(e,t,o.left),s=$S(e,t,o.top),r=ok(n,s);return tk(e,r),r},setToMin:(e,t)=>{const o=jw(t),n=Gw(t);tk(e,ok(o,n))},setToMax:(e,t)=>{const o=Xw(t),n=Kw(t);tk(e,ok(o,n))},getValueFromEvent:e=>Uw(e),setPositionFromValue:(e,t,o,n)=>{const s=mS(o),r=HS(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=XS(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Ut(t.element)/2,l=Rt(t.element)/2;kt(t.element,"left",r-i+"px"),kt(t.element,"top",a-l+"px")},onLeft:sk,onRight:rk,onUp:ak,onDown:ik,edgeActions:lk});const dk=tm({name:"Slider",configFields:[is("stepSize",1),is("onChange",b),is("onChoose",b),is("onInit",b),is("onDragStart",b),is("onDragEnd",b),is("snapToGrid",!1),is("rounded",!0),Qn("snapStart"),Gn("model",Nn("mode",{x:[is("minX",0),is("maxX",100),Wn("value",(e=>fs(e.mode.minX))),jn("getInitialValue"),wi("manager",jS)],y:[is("minY",0),is("maxY",100),Wn("value",(e=>fs(e.mode.minY))),jn("getInitialValue"),wi("manager",ek)],xy:[is("minX",0),is("maxX",100),is("minY",0),is("maxY",100),Wn("value",(e=>fs({x:e.mode.minX,y:e.mode.minY}))),jn("getInitialValue"),wi("manager",ck)]})),Qd("sliderBehaviours",[kp,Zd]),Wn("mouseIsDown",(()=>fs(!1)))],partFields:Lw,factory:(e,t,o,n)=>{const s=t=>Lu(t,e,"thumb"),r=t=>Lu(t,e,"spectrum"),a=t=>Nu(t,e,"left-edge"),i=t=>Nu(t,e,"right-edge"),l=t=>Nu(t,e,"top-edge"),c=t=>Nu(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&Nu(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:tu(e.sliderBehaviours,[kp.config({mode:"special",focusIn:t=>Nu(t,e,"spectrum").map(kp.focusIn).map(T)}),Zd.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),hl.config({channels:{[Rd()]:{onReceive:p}}})]),events:_r([Br(Ww(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),B.some(!0)})(t,o.event.value)})),zr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Br(Ts(),h),Br(Bs(),f),Br(As(),h),Br(Is(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),uk=Kr("rgb-hex-update"),mk=Kr("slider-update"),gk=Kr("palette-update"),pk="form",hk=[Qd("formBehaviours",[Zd])],fk=e=>"<alloy.field."+e+">",bk=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:tu(e.formBehaviours,[Zd.config({store:{mode:"manual",getValue:t=>{const o=Uu(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=rm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+jr(e.element)),o.fold((()=>qo.error(n)),qo.value);var o,n})).map(Zd.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{Nu(t,e,n).each((e=>{rm.getCurrent(e).each((e=>{Zd.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>Nu(t,e,o).bind(rm.getCurrent)}}),vk={getField:ua(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Ru(pk,fk(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>_u({name:e,pname:fk(e)})));return Yu(pk,hk,s,bk,o)}},yk=Kr("valid-input"),xk=Kr("invalid-input"),wk=Kr("validating-input"),Sk="colorcustom.rgb.",kk=(e,t,o,n)=>{const s=(o,n)=>lw.config({invalidClass:t("invalid"),notify:{onValidate:e=>{wr(e,wk,{type:o})},onValid:e=>{wr(e,yk,{type:o,value:Zd.getValue(e)})},onInvalid:e=>{wr(e,xk,{type:o,value:Zd.getValue(e)})}},validator:{validate:t=>{const o=Zd.getValue(t),s=n(o)?qo.value(!0):qo.error(e("aria.input.invalid"));return Zx(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e("colorcustom.rgb.range"),c=Cx.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[La(r)]}),d=Cx.parts.field({data:i,factory:$x,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:cl([s(n,o),Tx.config({})]),onSetValue:e=>{lw.isInvalid(e)&&lw.run(e).get(b)}}),u=[c,d],m="hex"!==n?[Cx.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;Zd.setValue(e,{red:o,green:n,blue:s})},i=kh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{kt(e.element,"background-color","#"+t.value)}))},c=em({factory:()=>{const s={red:fs(B.some(255)),green:fs(B.some(255)),blue:fs(B.some(255)),hex:fs(B.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",B.some(t)),d("green",B.some(o)),d("blue",B.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,B.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=ry(t);d("hex",B.some(t));const s=xy(n);a(e,s),u(s),wr(e,uk,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,B.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>by(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=uy(t);return vk.getField(e,"hex").each((t=>{Hp.isFocused(t)||Zd.setValue(e,{hex:o.value})})),o})(e,t);wr(e,uk,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(Sk+t+".label"),description:e(Sk+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return sn(vk.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",Cx.sketch(r(vy,"red",h.label,h.description,255))),o.field("green",Cx.sketch(r(vy,"green",f.label,f.description,255))),o.field("blue",Cx.sketch(r(vy,"blue",b.label,b.description,255))),o.field("hex",Cx.sketch(r(ly,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:cl([lw.config({invalidClass:t("form-invalid")}),Fp("rgb-form-events",[Br(yk,g),Br(xk,m),Br(wk,m)])])}))),{apis:{updateHex:(e,t)=>{Zd.setValue(e,{hex:t.value}),((e,t)=>{const o=xy(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},Ck=(e,t)=>{const o=em({name:"ColourPicker",configFields:[jn("dom"),is("onValidHex",b),is("onInvalidHex",b)],factory:o=>{const n=kk(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=dk.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=dk.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return em({factory:e=>{const r=y({x:0,y:0}),a=cl([rm.config({find:B.some}),Hp.config({})]);return dk.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:r},rounded:!1,components:[o,n],onChange:(e,t,o)=>{wr(e,gk,{value:o})},onInit:(e,t,o,n)=>{s(o.element.dom,ky(Cy))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=_y(t,100,100),r=yy(n);s(o,ky(r))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=Ty(xy(t));dk.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),r={paletteRgba:fs(Cy),paletteHue:fs(0)},a=kh(((e,t)=>{const o=dk.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=dk.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return dk.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:cl([Hp.config({})]),onChange:(e,t,o)=>{wr(e,mk,{value:o})}})})(0,t)),i=kh(s.sketch({})),l=kh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{dk.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=xy(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),N(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:cl([Fp("colour-picker-events",[Br(uk,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Ty(xy(e)))(n);g(t,n,s.hue,e)}})()),Br(gk,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=_y(s,n.x,100-n.y),i=Ey(a);g(t,i,s,e)}})()),Br(mk,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Ty(s),i=_y(n,a.saturation,a.value),l=Ey(i);g(t,l,n,e)}})())]),rm.config({find:e=>l.getOpt(e)}),kp.config({mode:"acyclic"})])}}});return o},Ok=()=>rm.config({find:B.some}),_k=e=>rm.config({find:t=>tt(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),Tk=xn([is("preprocess",x),is("postprocess",x)]),Ek=(e,t,o)=>Zd.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),Bk=(e,t,o)=>Ek(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),Mk=(e,t)=>{const o=Hn("RepresentingConfigs.memento processors",Tk,t);return Zd.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=Zd.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);Zd.setValue(r,s)}}})},Ak=Bk,Dk=Ek,Fk=e=>Zd.config({store:{mode:"memory",initialValue:e}}),Ik={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var Rk=tinymce.util.Tools.resolve("tinymce.Resource"),Vk=tinymce.util.Tools.resolve("tinymce.util.Tools");const zk=Kr("alloy-fake-before-tabstop"),Hk=Kr("alloy-fake-after-tabstop"),Pk=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:cl([Hp.config({ignore:!0}),Tx.config({})])}),Nk=e=>({dom:{tag:"div",classes:["tox-navobj"]},components:[Pk([zk]),e,Pk([Hk])],behaviours:cl([_k(1)])}),Lk=(e,t)=>{wr(e,Hs(),{raw:{which:9,shiftKey:t}})},Wk=(e,t)=>{const o=t.element;Ba(o,zk)?Lk(e,!0):Ba(o,Hk)&&Lk(e,!1)},Uk=e=>dx(e,["."+zk,"."+Hk].join(","),_),jk=Kr("toolbar.button.execute"),Gk={[Qs()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},$k=(e,t,o)=>Fh(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),qk=(e,t)=>$k(e,t,[]),Xk=(e,t)=>$k(e,t,[Dp.config({})]),Kk=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[La(o.translate(e))],behaviours:cl([Dp.config({})])}),Yk=Kr("update-menu-text"),Jk=Kr("update-menu-icon"),Zk=(e,t,o)=>{const n=fs(b),s=e.text.map((e=>kh(Kk(e,t,o.providers)))),r=e.icon.map((e=>kh(Xk(e,o.providers.icons)))),a=(e,t)=>{const o=Zd.getValue(e);return Hp.focus(o),wr(o,"keydown",{raw:t.event.raw}),Tw.close(o),B.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=Fh("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons);return kh(Tw.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(P(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:Uv([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),B.some(c)]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:cl([...e.dropdownBehaviours,Rv((()=>e.disabled||o.providers.isDisabled())),Iv(),Ew.config({}),Dp.config({}),Fp("dropdown-events",[Pv(e,n),Nv(e,n)]),Fp("menubutton-update-display-text",[Br(Yk,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Dp.set(e,[La(o.providers.translate(t.event.text))])}))})),Br(Jk,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Dp.set(e,[Xk(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:sn(Gk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:cl([kp.config({mode:"special",onLeft:a,onRight:a})]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:ab(0,e.columns,e.presets)},fetch:t=>Jx(S(e.fetch,t))})).asSpec()},Qk=e=>"separator"===e.type,eC={type:"separator"},tC=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!Qk(e[e.length-1])?e.concat([eC]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&Qk(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{const n=(e=>{if(Qk(e))return e;{const t=be(e,"value").getOrThunk((()=>Kr("generated-menu-item")));return sn({value:t},e)}})(o),s=((e,t)=>(e=>ve(e,"getSubmenuItems"))(e)?((e,t)=>{const o=e.getSubmenuItems(),n=tC(o,t);return{item:e,menus:sn(n.menus,vs(e.value,n.items)),expansions:sn(n.expansions,vs(e.value,e.value))}})(e,t):{item:e,menus:{},expansions:{}})(n,t);return{menus:sn(e.menus,s.menus),items:[s.item].concat(e.items),expansions:sn(e.expansions,s.expansions)}}),{menus:{},expansions:{},items:[]})},oC=(e,t,o,n)=>{const s=Kr("primary-menu"),r=tC(e,o.shared.providers.menuItems());if(0===r.items.length)return B.none();const a=ix(s,r.items,t,o,n),i=ce(r.menus,((e,n)=>ix(n,e,t,o,!1))),l=sn(i,vs(s,a));return B.from(yh.tieredData(s,l,r.expansions))},nC=e=>!ve(e,"items"),sC="data-value",rC=(e,t,o,n)=>P(o,(o=>nC(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{Zd.setValue(e,o.value),wr(e,Fx,{name:t}),Hp.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>rC(e,t,o.items,n)})),aC=(e,t)=>re(e,(e=>nC(e)?ke(e.value===t,e):aC(e.items,t))),iC=em({name:"HtmlSelect",configFields:[jn("options"),Qd("selectBehaviours",[Hp,Zd]),is("selectClasses",[]),is("selectAttributes",{}),Qn("data")],factory:(e,t)=>{const o=P(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>vs("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:tu(e.selectBehaviours,[Hp.config({}),Zd.config({store:{mode:"manual",getValue:e=>Da(e.element),setValue:(t,o)=>{G(e.options,(e=>e.value===o)).isSome()&&Fa(t.element,o)},...n}})])}}}),lC=y([is("field1Name","field1"),is("field2Name","field2"),yi("onLockedChange"),hi(["lockClass"]),is("locked",!1),ou("coupledFieldBehaviours",[rm,Zd])]),cC=(e,t)=>_u({factory:Cx,name:e,overrides:e=>({fieldBehaviours:cl([Fp("coupled-input-behaviour",[Br(Ns(),(o=>{((e,t,o)=>Nu(e,t,o).bind(rm.getCurrent))(o,e,t).each((t=>{Nu(o,e,"lock").each((n=>{$p.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),dC=y([cC("field1","field2"),cC("field2","field1"),_u({factory:Sh,schema:[jn("dom")],name:"lock",overrides:e=>({buttonBehaviours:cl([$p.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),uC=tm({name:"FormCoupledInputs",configFields:lC(),partFields:dC(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:nu(e.coupledFieldBehaviours,[rm.config({find:B.some}),Zd.config({store:{mode:"manual",getValue:t=>{const o=Gu(t,e,["field1","field2"]);return{[e.field1Name]:Zd.getValue(o.field1()),[e.field2Name]:Zd.getValue(o.field2())}},setValue:(t,o)=>{const n=Gu(t,e,["field1","field2"]);ye(o,e.field1Name)&&Zd.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&Zd.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>Nu(t,e,"field1"),getField2:t=>Nu(t,e,"field2"),getLock:t=>Nu(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),mC=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return qo.value({value:e,unit:o})}return qo.error(e)},gC=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?B.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?B.some(e.value):B.some(e.value/o[e.unit]*o[t]):B.none()},pC=e=>B.none(),hC=(e,t)=>{const o=e.label.map((e=>Dx(e,t))),n=[ym.config({disabled:()=>e.disabled||t.isDisabled()}),Iv(),kp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(xr(e,zx),B.some(!0))}),Fp("textfield-change",[Br(Ns(),((t,o)=>{wr(t,Fx,{name:e.name})})),Br(Js(),((t,o)=>{wr(t,Fx,{name:e.name})}))]),Tx.config({})],s=e.validation.map((e=>lw.config({getRoot:e=>Ze(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=Zd.getValue(t),n=e.validator(o);return Zx(!0===n?qo.value(o):qo.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(y({}),(e=>({inputmode:e})))},a=Cx.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:cl(q([n,s])),selectOnFocus:!1,factory:$x}),i=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[ym.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{Cx.getField(e).each(ym.disable)},onEnabled:e=>{Cx.getField(e).each(ym.enable)}}),Iv()];return Bx(o,a,i,l)};var fC=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return _r([Br(e.event,o),Hr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Br(e,(()=>t.cancel()))])).getOr([])))}});const bC=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},vC=e=>{const t=fs(null);return ga({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var yC=Object.freeze({__proto__:null,throttle:vC,init:e=>e.stream.streams.state(e)}),xC=[Gn("stream",Nn("mode",{throttle:[jn("delay"),is("stopEvent",!0),wi("streams",{setup:(e,t)=>{const o=e.stream,n=bC(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:vC})]})),is("event","input"),Qn("cancelEvent"),yi("onStream")];const wC=ul({fields:xC,name:"streaming",active:fC,state:yC}),SC=(e,t,o)=>{const n=Zd.getValue(o);Zd.setValue(t,n),CC(t)},kC=(e,t)=>{const o=e.element,n=Da(o),s=o.dom;"number"!==bt(o,"type")&&t(s,n)},CC=e=>{kC(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},OC=y("alloy.typeahead.itemexecute"),_C=y([Qn("lazySink"),jn("fetch"),is("minChars",5),is("responseTime",1e3),bi("onOpen"),is("getHotspot",B.some),is("getAnchorOverrides",y({})),is("layouts",B.none()),is("eventOrder",{}),hs("model",{},[is("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),is("selectsOver",!0),is("populateFromBrowse",!0)]),bi("onSetValue"),vi("onExecute"),bi("onItemExecute"),is("inputClasses",[]),is("inputAttributes",{}),is("inputStyles",{}),is("matchWidth",!0),is("useMinWidth",!1),is("dismissOnBlur",!0),hi(["openClass"]),Qn("initialData"),Qd("typeaheadBehaviours",[Hp,Zd,wC,kp,$p,mw]),Wn("previewing",(()=>fs(!0)))].concat(Wx()).concat(Cw())),TC=y([Tu({schema:[pi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlight:(t,o)=>{e.previewing.get()?t.getSystem().getByUid(e.uid).each((n=>{((e,t,o)=>{if(e.selectsOver){const n=Zd.getValue(t),s=e.getDisplayText(n),r=Zd.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?B.some((()=>{SC(0,t,o),((e,t)=>{kC(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):B.none()}return B.none()})(e.model,n,o).fold((()=>Bm.dehighlight(t,o)),(e=>e()))})):t.getSystem().getByUid(e.uid).each((t=>{e.model.populateFromBrowse&&SC(e.model,t,o)})),e.previewing.set(!1)},onExecute:(t,o)=>t.getSystem().getByUid(e.uid).toOptional().map((e=>(wr(e,OC(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),t.getSystem().getByUid(e.uid).each((t=>{e.model.populateFromBrowse&&SC(e.model,t,o)}))}})})]),EC=tm({name:"Typeahead",configFields:_C(),partFields:TC(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=mw.getCoupled(t,"sandbox");if(Dd.isOpen(r))rm.getCurrent(r).each((e=>{Bm.getHighlighted(e).fold((()=>{s(e)}),(()=>{Or(r,e.element,"keydown",o)}))}));else{const o=e=>{rm.getCurrent(e).each(s)};bw(e,a(t),t,r,n,o,hw.HighlightFirst).get(b)}},r=Ux(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=X(o,(e=>W(e.items,(e=>"item"===e.type))));return Zd.getState(e).update(P(n,(e=>e.data))),t})),i=[Hp.config({}),Zd.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Da(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{Fa(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>vs("initialValue",e))).getOr({})}}),wC.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=mw.getCoupled(t,"sandbox");if(Hp.isFocused(t)&&Da(t.element).length>=e.minChars){const o=rm.getCurrent(s).bind((e=>Bm.getHighlighted(e).map(Zd.getValue)));e.previewing.set(!0);const r=t=>{rm.getCurrent(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Bm.highlightFirst(t)}),(e=>{Bm.highlightBy(t,(t=>Zd.getValue(t).value===e.value)),Bm.getHighlighted(t).orThunk((()=>(Bm.highlightFirst(t),B.none())))}))}))};bw(e,a(t),t,s,n,r,hw.HighlightFirst).get(b)}},cancelEvent:sr()}),kp.config({mode:"special",onDown:(e,t)=>(s(e,t,Bm.highlightFirst),B.some(!0)),onEscape:e=>{const t=mw.getCoupled(e,"sandbox");return Dd.isOpen(t)?(Dd.close(t),B.some(!0)):B.none()},onUp:(e,t)=>(s(e,t,Bm.highlightLast),B.some(!0)),onEnter:t=>{const o=mw.getCoupled(t,"sandbox"),n=Dd.isOpen(o);if(n&&!e.previewing.get())return rm.getCurrent(o).bind((e=>Bm.getHighlighted(e))).map((e=>(wr(t,OC(),{item:e}),!0)));{const s=Zd.getValue(t);return xr(t,sr()),e.onExecute(o,t,s),n&&Dd.close(o),B.some(!0)}}}),$p.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),mw.config({others:{sandbox:t=>Sw(e,t,{onOpen:()=>$p.on(t),onClose:()=>$p.off(t)})}}),Fp("typeaheadevents",[Nr((t=>{const o=b;yw(e,a(t),t,n,o,hw.HighlightFirst).get(b)})),Br(OC(),((t,o)=>{const n=mw.getCoupled(t,"sandbox");SC(e.model,t,o.event.item),xr(t,sr()),e.onItemExecute(t,n,o.event.item,Zd.getValue(t)),Dd.close(n),CC(t)}))].concat(e.dismissOnBlur?[Br(Ys(),(e=>{const t=mw.getCoupled(e,"sandbox");xl(t.element).isNone()&&Dd.close(t)}))]:[]))];return{uid:e.uid,dom:Gx(sn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...tu(e.typeaheadBehaviours,i)},eventOrder:e.eventOrder}}}),BC=e=>({...e,toCached:()=>BC(e.toCached()),bindFuture:t=>BC(e.bind((e=>e.fold((e=>Zx(qo.error(e))),(e=>t(e)))))),bindResult:t=>BC(e.map((e=>e.bind(t)))),mapResult:t=>BC(e.map((e=>e.map(t)))),mapError:t=>BC(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>BC(Jx((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(qo.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),MC=e=>BC(Jx(e)),AC=e=>({isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t),setActive:t=>{const o=e.element;t?(Ta(o,"tox-tbtn--enabled"),ht(o,"aria-pressed",!0)):(Ea(o,"tox-tbtn--enabled"),xt(o,"aria-pressed"))},isActive:()=>Ba(e.element,"tox-tbtn--enabled")}),DC=(e,t,o,n)=>Zk({text:e.text,icon:e.icon,tooltip:e.tooltip,role:n,fetch:(t,n)=>{e.fetch((e=>{n(oC(e,Uf.CLOSE_ON_EXECUTE,o,!1))}))},onSetup:e.onSetup,getApi:AC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Tx.config({})]},t,o.shared),FC=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{bl(t.element),wr(t,Vx,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(P(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},IC=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:cl([Rv((()=>!e.enabled||r.isDisabled())),Iv(),Tx.config({}),Fp("button press",[Er("click"),Er("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=sn(i,{dom:n});return sn(l,{components:s})},RC=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>qk(e,o.icons))),a=Uv([r]);return IC(e,t,n,s,a,o)},VC=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>qk(e,o.icons))),i=[a.getOrThunk((()=>La(r)))],l=[...(e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}})(e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary")),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return IC(e,t,n,{tag:"button",classes:l,attributes:{title:r}},i,o)},zC=(e,t,o,n=[],s=[])=>{const r=VC(e,B.some(t),o,n,s);return Sh.sketch(r)},HC=(e,t)=>o=>{"custom"===t?wr(o,Vx,{name:e,value:{}}):"submit"===t?xr(o,zx):"cancel"===t?xr(o,Rx):console.error("Unknown button type: ",t)},PC=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,onSetup:t=>(t.setEnabled(e.enabled),b),fetch:FC(n.items,t,o)},r=kh(DC(s,"tox-tbtn",o,B.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=HC(e.name,t),s={...e,borderless:!1};return zC(s,n,o.shared.providers,[])}console.error("Unknown footer button type: ",t)},NC={type:"separator"},LC=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),WC=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),UC=(e,t)=>(e=>P(e,LC))(((e,t)=>W(t,(t=>t.type===e)))(e,t)),jC=e=>UC("header",e.targets),GC=e=>UC("anchor",e.targets),$C=e=>B.from(e.anchorTop).map((e=>WC("<top>",e))).toArray(),qC=e=>B.from(e.anchorBottom).map((e=>WC("<bottom>",e))).toArray(),XC=(e,t)=>{const o=e.toLowerCase();return W(t,(e=>{const t=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text;return Oe(t.toLowerCase(),o)||Oe(e.value.toLowerCase(),o)}))},KC=Kr("aria-invalid"),YC=(e,t)=>{e.dom.checked=t},JC=e=>e.dom.checked,ZC=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,B.none())),(r=>t.field(r,e(o,s,be(n,r))))),QC={bar:ZC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:P(e.items,t.interpreter)}))(e,t.shared))),collection:ZC(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>Dx(e,t))),s=e=>(t,o)=>{ti(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,bt(n,"data-collection-item-value"))}))},r=s(((o,n,s,r)=>{n.stop(),t.isDisabled()||wr(o,Vx,{name:e.name,value:r})})),a=[Br(Rs(),s(((e,t,o)=>{bl(o)}))),Br(Ws(),r),Br(tr(),r),Br(Vs(),s(((e,t,o)=>{ei(e.element,"."+Jf).each((e=>{Ea(e,Jf)})),Ta(o,Jf)}))),Br(zs(),s((e=>{ei(e.element,"."+Jf).each((e=>{Ea(e,Jf)}))}))),Nr(s(((t,o,n,s)=>{wr(t,Vx,{name:e.name,value:s})})))],i=(e,t)=>P(Dc(e.element,".tox-collection__item"),t),l=Cx.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:x},behaviours:cl([ym.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{Ta(e,"tox-collection__item--state-disabled"),ht(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{Ea(e,"tox-collection__item--state-disabled"),xt(e,"aria-disabled")}))}}),Iv(),Dp.config({}),Zd.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const s=P(n,(o=>{const n=Ch.translate(o.text),s=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",r=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${Ex.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${r}${s}</div>`})),r="auto"!==e.columns&&e.columns>1?H(s,e.columns):[s],a=P(r,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));Wr(o.element,a.join(""))})(o,n),"auto"===e.columns&&xv(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{kp.setGridSize(o,e,t)})),xr(o,Lx)}}),Tx.config({}),kp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${Gf}`}})),Fp("collection-events",a)]),eventOrder:{[Qs()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return Bx(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:ZC(((e,t)=>((e,t)=>xx.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Sh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Mh(e.icon,t.icons),attributes:{title:t.translate(e.iconTooltip)}},action:t=>{wr(t,Vx,{name:"alert-banner",value:e.url})},buttonBehaviours:cl([Ah()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]}))(e,t.shared.providers))),input:ZC(((e,t,o)=>((e,t,o)=>hC({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:B.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:ZC(((e,t,o)=>((e,t,o)=>hC({name:e.name,multiline:!0,label:e.label,inputMode:B.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:B.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:ZC(((e,t)=>((e,t)=>{return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:["tox-label"]},components:[La(t.providers.translate(e.label))]},...P(e.items,t.interpreter)],behaviours:cl([Ok(),Dp.config({}),(o=B.none(),Bk(o,Lr,Wr)),kp.config({mode:"acyclic"})])};var o})(e,t.shared))),iframe:(C_=(e,t,o)=>((e,t,o)=>{const n=e.sandboxed,s=e.transparent,r="tox-dialog__iframe",a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...n?{sandbox:"allow-scripts allow-same-origin"}:{}},i=(e=>{const t=fs(e.getOr(""));return{getValue:e=>t.get(),setValue:(e,o)=>{t.get()!==o&&ht(e.element,"srcdoc",o),t.set(o)}}})(o),l=e.label.map((e=>Dx(e,t))),c=Cx.parts.field({factory:{sketch:e=>Nk({uid:e.uid,dom:{tag:"iframe",attributes:a,classes:s?[r]:[r,`${r}--opaque`]},behaviours:cl([Tx.config({}),Hp.config({}),Dk(o,i.getValue,i.setValue)])})}});return Bx(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=sn(t,{source:"dynamic"});return ZC(C_)(e,s,o,n)}),button:ZC(((e,t)=>((e,t)=>{const o=HC(e.name,"custom");return n=B.none(),s=Cx.parts.field({factory:Sh,...VC(e,B.some(o),t,[Fk(""),Ok()])}),Bx(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:ZC(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),B.some(!0)),s=Cx.parts.field({factory:{sketch:x},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:cl([Ok(),ym.config({disabled:()=>!e.enabled||t.isDisabled()}),Tx.config({}),Hp.config({}),Ak(o,JC,YC),kp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Fp("checkbox-events",[Br(Ls(),((t,o)=>{wr(t,Fx,{name:e.name})}))])])}),r=Cx.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[La(t.translate(e.label))],behaviours:cl([Ew.config({})])}),a=e=>Fh("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=kh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return Cx.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:cl([ym.config({disabled:()=>!e.enabled||t.isDisabled(),disableClass:"tox-checkbox--disabled",onDisabled:e=>{Cx.getField(e).each(ym.disable)},onEnabled:e=>{Cx.getField(e).each(ym.enable)}}),Iv()])})})(e,t.shared.providers,o))),colorinput:ZC(((e,t,o)=>((e,t,o,n)=>{const s=Cx.parts.field({factory:$x,inputClasses:["tox-textfield"],data:n,onSetValue:e=>lw.run(e).get(b),inputBehaviours:cl([ym.config({disabled:t.providers.isDisabled}),Iv(),Tx.config({}),lw.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>Ze(e.element),notify:{onValid:e=>{const t=Zd.getValue(e);wr(e,Bw,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=Zd.getValue(e);if(0===t.length)return Zx(qo.value(!0));{const e=De("span");kt(e,"background-color",t);const o=Et(e,"background-color").fold((()=>qo.error("blah")),(e=>qo.value(t)));return Zx(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>Dx(e,t.providers))),a=(e,t)=>{wr(e,Mw,{value:t})},i=kh(((e,t)=>Tw.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:cl([Rv(t.providers.isDisabled),Iv(),Ew.config({}),Tx.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>Jx((t=>e.fetch(t))).map((n=>B.from(lx(sn(Xy(Kr("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,Uf.CLOSE_ON_EXECUTE,_,t.providers),{movement:Yy(e.columns,e.presets)}))))),parts:{menu:ab(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Gi,ji,Ki],onLtr:()=>[ji,Gi,Ki]},components:[],fetch:Uy(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:(e,t)=>{i.getOpt(e).each((e=>{"custom"===t?o.colorPicker((t=>{t.fold((()=>xr(e,Aw)),(t=>{a(e,t),Hy(t)}))}),"#ffffff"):a(e,"remove"===t?"":t)}))}},t));return Cx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:cl([Fp("form-field-events",[Br(Bw,((t,o)=>{i.getOpt(t).each((e=>{kt(e.element,"background-color",o.event.color)})),wr(t,Fx,{name:e.name})})),Br(Mw,((e,t)=>{Cx.getField(e).each((o=>{Zd.setValue(o,t.event.value),rm.getCurrent(e).each(Hp.focus)}))})),Br(Aw,((e,t)=>{Cx.getField(e).each((t=>{rm.getCurrent(e).each(Hp.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:ZC(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=Ck((e=>t=>e.translate(Ik[t]))(t),n),r=kh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{wr(e,Vx,{name:"hex-valid",value:!0})},onInvalidHex:e=>{wr(e,Vx,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:cl([Dk(o,(e=>{const t=r.get(e);return rm.getCurrent(t).bind((e=>Zd.getValue(e).hex)).map((e=>"#"+e)).getOr("")}),((e,t)=>{const o=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),n=r.get(e);rm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{Zd.setValue(e,{hex:B.from(o[1]).getOr("")}),vk.getField(e,"hex").each((e=>{xr(e,Ns())}))}))})),Ok()])}})(0,t.shared.providers,o))),dropzone:ZC(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{N(e,(e=>{e(t,o)}))},r=(e,t)=>{if(!ym.isDisabled(e)){const o=t.event.raw;i(e,o.dataTransfer.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{Zd.setValue(o,((e,t)=>{const o=Vk.explode(t.getOption("images_file_types"));return W(se(e),(e=>V(o,(t=>_e(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),wr(o,Fx,{name:e.name})},l=kh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:cl([Fp("input-file-events",[Ir(Ws()),Ir(tr())])])}),c=e.label.map((e=>Dx(e,t))),d=Cx.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:cl([Fk(o.getOr([])),Ok(),ym.config({}),$p.config({toggleClass:"dragenter",toggleOnExecute:!1}),Fp("dropzone-events",[Br("dragenter",s([n,$p.toggle])),Br("dragleave",s([n,$p.toggle])),Br("dragover",n),Br("drop",s([n,r])),Br(Ls(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[La(t.translate("Drop an image here"))]},Sh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[La(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:cl([Tx.config({}),Rv(t.isDisabled),Iv()])})]}]})}});return Bx(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:ZC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:P(e.items,t.interpreter)}))(e,t.shared))),listbox:ZC(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>aC(e.items,t))).orThunk((()=>oe(e.items).filter(nC))),r=e.label.map((e=>Dx(e,n))),a=Cx.parts.field({dom:{},factory:{sketch:o=>Zk({uid:o.uid,text:s.map((e=>e.text)),icon:B.none(),tooltip:e.label,role:B.none(),fetch:(o,n)=>{const s=rC(o,e.name,e.items,Zd.getValue(o));n(oC(s,Uf.CLOSE_ON_EXECUTE,t,!1))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Tx.config({}),Dk(s.map((e=>e.value)),(e=>bt(e.element,sC)),((t,o)=>{aC(e.items,o).each((e=>{ht(t.element,sC,e.value),wr(t,Yk,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return Cx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:cl([ym.config({disabled:y(!e.enabled),onDisabled:e=>{Cx.getField(e).each(ym.disable)},onEnabled:e=>{Cx.getField(e).each(ym.enable)}})])})})(e,t,o))),selectbox:ZC(((e,t,o)=>((e,t,o)=>{const n=P(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>Dx(e,t))),r=Cx.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:iC,selectBehaviours:cl([ym.config({disabled:()=>!e.enabled||t.isDisabled()}),Tx.config({}),Fp("selectbox-change",[Br(Ls(),((t,o)=>{wr(t,Fx,{name:e.name})}))])])}),a=e.size>1?B.none():B.some(Fh("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return Cx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:cl([ym.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{Cx.getField(e).each(ym.disable)},onEnabled:e=>{Cx.getField(e).each(ym.enable)}}),Iv()])})})(e,t.shared.providers,o))),sizeinput:ZC(((e,t)=>((e,t)=>{let o=pC;const n=Kr("ratio-event"),s=e=>Fh(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=uC.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:cl([ym.config({disabled:()=>!e.enabled||t.isDisabled()}),Iv(),Tx.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>Cx.parts.field({factory:$x,inputClasses:["tox-textfield"],inputBehaviours:cl([ym.config({disabled:()=>!e.enabled||t.isDisabled()}),Iv(),Tx.config({}),Fp("size-input-events",[Br(Vs(),((e,t)=>{wr(e,n,{isField1:o})})),Br(Ls(),((t,o)=>{wr(t,Fx,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[La(t.translate(e))]}),c=uC.parts.field1(a([Cx.parts.label(l("Width")),i(!0)])),d=uC.parts.field2(a([Cx.parts.label(l("Height")),i(!1)]));return uC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{mC(Zd.getValue(e)).each((e=>{o(e).each((e=>{Zd.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:cl([ym.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{uC.getField1(e).bind(Cx.getField).each(ym.disable),uC.getField2(e).bind(Cx.getField).each(ym.disable),uC.getLock(e).each(ym.disable)},onEnabled:e=>{uC.getField1(e).bind(Cx.getField).each(ym.enable),uC.getField2(e).bind(Cx.getField).each(ym.enable),uC.getLock(e).each(ym.enable)}}),Iv(),Fp("size-input-events2",[Br(n,((e,t)=>{const n=t.event.isField1,s=n?uC.getField1(e):uC.getField2(e),r=n?uC.getField2(e):uC.getField1(e),a=s.map(Zd.getValue).getOr(""),i=r.map(Zd.getValue).getOr("");o=((e,t)=>{const o=mC(e).toOptional(),n=mC(t).toOptional();return Se(o,n,((e,t)=>gC(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>gC(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(pC))).getOr(pC)})(a,i)}))])])})})(e,t.shared.providers))),slider:ZC(((e,t,o)=>((e,t,o)=>{const n=dk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[La(t.translate(e.label))]}),s=dk.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=dk.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return dk.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:cl([Ok(),Hp.config({})]),onChoose:(t,o,n)=>{wr(t,Fx,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:ZC(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=Zd.getValue(t);o.addToHistory(n.value,e.filetype)},a=Cx.parts.field({factory:EC,...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":KC,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{const n=Zd.getValue(t),s=void 0!==n.meta.text?n.meta.text:n.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=XC(s,(e=>P(e,(e=>WC(e,e))))(o.getHistory(e)));return"file"===e?(r=[n,XC(s,jC(t)),XC(s,q([$C(t),GC(t),qC(t)]))],j(r,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(NC,t)),[])):n;var r}))})(e.filetype,n,o),r=oC(s,Uf.BUBBLE_TO_SANDBOX,t,!1);return Zx(r)},getHotspot:e=>m.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(lw)&&lw.run(e).get(b)},typeaheadBehaviours:cl(q([o.getValidationHandler().map((t=>lw.config({getRoot:e=>Ze(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{l.getOpt(e).each((e=>{ht(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=Zd.getValue(o);return MC((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=qo.error(e.message);o(t)}else{const t=qo.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),[ym.config({disabled:()=>!e.enabled||s.isDisabled()}),Tx.config({}),Fp("urlinput-events",q(["file"===e.filetype?[Br(Ns(),(t=>{wr(t,Fx,{name:e.name})}))]:[],[Br(Ls(),(t=>{wr(t,Fx,{name:e.name}),r(t)})),Br(Js(),(t=>{wr(t,Fx,{name:e.name}),r(t)}))]]))]])),eventOrder:{[Ns()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:ab(0,0,"normal")},onExecute:(e,t,o)=>{wr(t,zx,{})},onItemExecute:(t,o,n,s)=>{r(t),wr(t,Fx,{name:e.name})}}),i=e.label.map((e=>Dx(e,s))),l=kh(((e,t,o=e,n=e)=>Fh(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",B.some(KC),"warning")),c=kh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[l.asSpec()]}),d=o.getUrlPicker(e.filetype),u=Kr("browser.url.event"),m=kh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[a,c.asSpec()],behaviours:cl([ym.config({disabled:()=>!e.enabled||s.isDisabled()})])}),g=kh(zC({name:e.name,icon:B.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:B.none(),borderless:!0},(e=>xr(e,u)),s,[],["tox-browse-url"]));return Cx.sketch({dom:Ax([]),components:i.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[m.asSpec()],d.map((()=>g.asSpec())).toArray()])}]),fieldBehaviours:cl([ym.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{Cx.getField(e).each(ym.disable),g.getOpt(e).each(ym.disable)},onEnabled:e=>{Cx.getField(e).each(ym.enable),g.getOpt(e).each(ym.enable)}}),Iv(),Fp("url-input-events",[Br(u,(t=>{rm.getCurrent(t).each((o=>{const n=Zd.getValue(o),s={fieldname:e.name,...n};d.each((n=>{n(s).get((n=>{Zd.setValue(o,n),wr(t,Fx,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:ZC((e=>{const t=Hl(),o=kh({dom:{tag:e.tag}}),n=Hl();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:cl([Fp("custom-editor-events",[zr((s=>{o.getOpt(s).each((o=>{((e=>ve(e,"init"))(e)?e.init(o.element.dom):Rk.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),Dk(B.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),Ok()]),components:[o.asSpec()]}})),htmlpanel:ZC((e=>"presentation"===e.presets?xx.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):xx.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:cl([Tx.config({}),Hp.config({})])}))),imagepreview:ZC(((e,t,o)=>((e,t)=>{const o=fs(t.getOr({url:""})),n=kh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=kh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:B.from(e.zoom),cachedWidth:B.from(e.cachedWidth),cachedHeight:B.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:cl([Ok(),Dk(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const t=r.cachedWidth,o=r.cachedHeight;if(u(r.zoom)){const n=((e,t,o)=>{const n=Ut(e),s=Rt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const n=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Ut(e.element),Rt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Ct(e.element,n)}))};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==bt(n,"src")&&(ht(n,"src",t.url),Ea(e.element,"tox-imagepreview__loaded")),u(r.cachedWidth)||u(r.cachedHeight)||a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[Nl(s,"load",o),Nl(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>N(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(Ta(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:ZC(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:P(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:P(n,(e=>({dom:{tag:"tr"},components:P(e,o)})))})],behaviours:cl([Tx.config({}),Hp.config({})])};var n,s})(e,t.shared.providers))),panel:ZC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:P(e.items,t.shared.interpreter)}))(e,t)))},eO={field:(e,t)=>t,record:y([])},tO=(e,t,o,n)=>{const s=sn(n,{shared:{interpreter:t=>oO(e,t,o,s)}});return oO(e,t,o,s)},oO=(e,t,o,n)=>be(QC,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),nO="layout-inset",sO=e=>e.x,rO=(e,t)=>e.x+e.width/2-t.width/2,aO=(e,t)=>e.x+e.width-t.width,iO=e=>e.y,lO=(e,t)=>e.y+e.height-t.height,cO=(e,t)=>e.y+e.height/2-t.height/2,dO=(e,t,o)=>Ci(aO(e,t),lO(e,t),o.insetSouthwest(),Bi(),"southwest",Vi(e,{right:0,bottom:3}),nO),uO=(e,t,o)=>Ci(sO(e),lO(e,t),o.insetSoutheast(),Ei(),"southeast",Vi(e,{left:1,bottom:3}),nO),mO=(e,t,o)=>Ci(aO(e,t),iO(e),o.insetNorthwest(),Ti(),"northwest",Vi(e,{right:0,top:2}),nO),gO=(e,t,o)=>Ci(sO(e),iO(e),o.insetNortheast(),_i(),"northeast",Vi(e,{left:1,top:2}),nO),pO=(e,t,o)=>Ci(rO(e,t),iO(e),o.insetNorth(),Mi(),"north",Vi(e,{top:2}),nO),hO=(e,t,o)=>Ci(rO(e,t),lO(e,t),o.insetSouth(),Ai(),"south",Vi(e,{bottom:3}),nO),fO=(e,t,o)=>Ci(aO(e,t),cO(e,t),o.insetEast(),Fi(),"east",Vi(e,{right:0}),nO),bO=(e,t,o)=>Ci(sO(e),cO(e,t),o.insetWest(),Di(),"west",Vi(e,{left:1}),nO),vO=e=>{switch(e){case"north":return pO;case"northeast":return gO;case"northwest":return mO;case"south":return hO;case"southeast":return uO;case"southwest":return dO;case"east":return fO;case"west":return bO}},yO=(e,t,o,n,s)=>Fl(n).map(vO).getOr(pO)(e,t,o,n,s),xO=e=>{switch(e){case"north":return hO;case"northeast":return uO;case"northwest":return dO;case"south":return pO;case"southeast":return gO;case"southwest":return mO;case"east":return bO;case"west":return fO}},wO=(e,t,o,n,s)=>Fl(n).map(xO).getOr(pO)(e,t,o,n,s),SO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},kO=(e,t,o)=>{const n={maxHeightFunction:Xl()};return()=>o()?{type:"node",root:lt(e()),node:B.from(e()),bubble:Zl(12,12,SO),layouts:{onRtl:()=>[gO],onLtr:()=>[mO]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:Zl(-12,12,SO),layouts:{onRtl:()=>[ji],onLtr:()=>[Gi]},overrides:n}},CO=(e,t,o)=>()=>o()?{type:"node",root:lt(e()),node:B.from(e()),layouts:{onRtl:()=>[pO],onLtr:()=>[pO]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[Ki],onLtr:()=>[Ki]}},OO=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng();return B.some(Oc.range(Ie(t.startContainer),t.startOffset,Ie(t.endContainer),t.endOffset))}}),_O=e=>t=>({type:"node",root:e(),node:t}),TO=(e,t,o)=>{const n=If(e),s=()=>Ie(e.getBody()),r=()=>Ie(e.getContentAreaContainer()),a=()=>n||!o();return{inlineDialog:kO(r,t,a),banner:CO(r,t,a),cursor:OO(e,s),node:_O(s)}},EO=e=>(t,o)=>{qy(e)(t,o)},BO=e=>()=>Vy(e),MO=e=>()=>zy(e),AO=e=>()=>Ry(e),DO=e=>({colorPicker:EO(e),hasCustomColors:BO(e),getColors:MO(e),getColorCols:AO(e)}),FO=e=>()=>vf(e),IO=e=>({isDraggableModal:FO(e)}),RO=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],VO=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=VO(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),zO=e=>Jh(e).map((t=>{const o=((e,t)=>{const o=VO(t),n=t=>{N(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return Zh(e)?RO.concat(o):o})).getOr(RO),HO=(e,t,o)=>{const n={type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)};return sn(e,n)},PO=(e,t,o,n)=>{const s=t=>P(t,(t=>{const a=ae(t);if(ye(t,"items")){const e=s(t.items);return sn((e=>sn(e,{type:"submenu"}))(t),{getStyleItems:y(e)})}return ye(t,"format")?(e=>HO(e,o,n))(t):1===a.length&&R(a,"title")?sn(t,{type:"separator"}):(t=>{const s=r(t.name)?t.name:Kr(t.title),a=`custom-${s}`,i={type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)},l=sn(t,i);return e.formatter.register(s,l),l})(t)}));return s(t)},NO=e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):B.none()},n=e=>{const t=e.items;return void 0!==t&&t.length>0?X(t,n):[e.format]},s=fs([]),r=fs([]),a=fs([]),i=fs([]),l=fs(!1);return e.on("PreInit",(a=>{const i=zO(e),l=PO(e,i,t,o);s.set(l),r.set(X(l,n))})),e.on("addStyleModifications",(s=>{const r=PO(e,s.items,t,o);a.set(r),l.set(s.replace),i.set(X(r,n))})),{getData:()=>{const e=l.get()?[]:s.get(),t=a.get();return e.concat(t)},getFlattenedKeys:()=>{const e=l.get()?[]:r.get(),t=i.get();return e.concat(t)}}},LO=Vk.trim,WO=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},UO=WO("true"),jO=WO("false"),GO=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),$O=e=>e.innerText||e.textContent,qO=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&KO(e),XO=e=>e&&/^(H[1-6])$/.test(e.nodeName),KO=e=>(e=>{for(;e=e.parentNode;){const t=e.contentEditable;if(t&&"inherit"!==t)return UO(e)}return!1})(e)&&!jO(e),YO=e=>XO(e)&&KO(e),JO=e=>{const t=(e=>e.id?e.id:Kr("h"))(e);return GO("header",$O(e),"#"+t,(e=>XO(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=t}))},ZO=e=>{const t=e.id||e.name,o=$O(e);return GO("anchor",o||"#"+t,"#"+t,0,b)},QO=e=>LO(e.title).length>0,e_=e=>{const t=(e=>{const t=P(Dc(Ie(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return W((e=>P(W(e,YO),JO))(t).concat((e=>P(W(e,qO),ZO))(t)),QO)},t_="tinymce-url-history",o_=e=>r(e)&&/^https?/.test(e),n_=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&K(t,o_));var t})).isNone(),s_=()=>{const e=My.getItem(t_);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+t_+" was not valid JSON",e),{};throw e}return n_(t)?t:(console.log("Local storage "+t_+" was not valid format",t),{})},r_=e=>{const t=s_();return be(t,e).getOr([])},a_=(e,t)=>{if(!o_(e))return;const o=s_(),n=be(o,t).getOr([]),s=W(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!n_(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));My.setItem(t_,JSON.stringify(e))})(o)},i_=e=>!!e,l_=e=>ce(Vk.makeMap(e,/[, ]/),i_),c_=e=>B.from(mf(e)),d_=e=>B.from(e).filter(r).getOrUndefined(),u_=e=>({getHistory:r_,addToHistory:a_,getLinkInformation:()=>(e=>hf(e)?B.some({targets:e_(e.getBody()),anchorTop:d_(ff(e)),anchorBottom:d_(bf(e))}):B.none())(e),getValidationHandler:()=>(e=>B.from(gf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=B.from(pf(e)).filter(i_).map(l_);return c_(e).fold(_,(e=>t.fold(T,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?c_(e):B.none():o[t]?c_(e):B.none()})(e,t).map((o=>n=>Jx((s=>{const i={filetype:t,fieldname:n.fieldname,...B.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),m_=Xu,g_=Au,p_=y([is("shell",!1),jn("makeItem"),is("setupItem",b),ou("listBehaviours",[Dp])]),h_=Eu({name:"items",overrides:()=>({behaviours:cl([Dp.config({})])})}),f_=y([h_]),b_=tm({name:y("CustomList")(),configFields:p_(),partFields:f_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Dp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:tu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?B.some(n):Nu(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Dp.contents(n),r=o.length,a=r-s.length,i=a>0?z(a,(()=>e.makeItem())):[],l=s.slice(r);N(l,(e=>Dp.remove(n,e))),N(i,(e=>Dp.append(n,e)));const c=Dp.contents(n);N(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),v_=y([jn("dom"),is("shell",!0),Qd("toolbarBehaviours",[Dp])]),y_=y([Eu({name:"groups",overrides:()=>({behaviours:cl([Dp.config({})])})})]),x_=tm({name:"Toolbar",configFields:v_(),partFields:y_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Dp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:tu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?B.some(n):Nu(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Dp.set(e,o)}))}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),w_=b,S_=_,k_=y([]);var C_,O_=Object.freeze({__proto__:null,setup:w_,isDocked:S_,getBehaviours:k_});const __=e=>(xe(Et(e,"position"),"fixed")?B.none():Qe(e)).orThunk((()=>{const t=De("span");return Je(e).bind((e=>{Bo(e,t);const o=Qe(t);return Do(t),o}))})),T_=e=>__(e).map(Nt).getOrThunk((()=>Ht(0,0))),E_=bs([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),B_=(e,t)=>{const o=e.element;Ta(o,t.transitionClass),Ea(o,t.fadeOutClass),Ta(o,t.fadeInClass),t.onShow(e)},M_=(e,t)=>{const o=e.element;Ta(o,t.transitionClass),Ea(o,t.fadeInClass),Ta(o,t.fadeOutClass),t.onHide(e)},A_=(e,t,o)=>K(e,(e=>{switch(e){case"bottom":return((e,t)=>e.bottom<=t.bottom)(t,o);case"top":return((e,t)=>e.y>=t.y)(t,o)}})),D_=(e,t)=>t.getInitialPos().map((t=>Lo(t.bounds.x,t.bounds.y,Ut(e),Rt(e)))),F_=(e,t,o)=>o.getInitialPos().bind((n=>{switch(o.clearInitialPos(),n.position){case"static":return B.some(E_.static());case"absolute":const o=__(e).map(Wo).getOrThunk((()=>Wo(mt())));return B.some(E_.absolute(Sl("absolute",be(n.style,"left").map((e=>t.x-o.x)),be(n.style,"top").map((e=>t.y-o.y)),be(n.style,"right").map((e=>o.right-t.right)),be(n.style,"bottom").map((e=>o.bottom-t.bottom)))));default:return B.none()}})),I_=(e,t,o)=>{const n=e.element;return xe(Et(n,"position"),"fixed")?((e,t,o)=>D_(e,o).filter((e=>A_(o.getModes(),e,t))).bind((t=>F_(e,t,o))))(n,t,o):((e,t,o)=>{const n=Wo(e);if(A_(o.getModes(),n,t))return B.none();{((e,t,o)=>{o.setInitialPos({style:Bt(e),position:_t(e,"position")||"static",bounds:t})})(e,n,o);const s=jo(),r=n.x-s.x,a=t.y-s.y,i=s.bottom-t.bottom,l=n.y<=t.y;return B.some(E_.fixed(Sl("fixed",B.some(r),l?B.some(a):B.none(),B.none(),l?B.none():B.some(i))))}})(n,t,o)},R_=(e,t,o)=>{o.setDocked(!1),N(["left","right","top","bottom","position"],(t=>At(e.element,t))),t.onUndocked(e)},V_=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),kl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},z_=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Ma(e.element,[t.fadeOutClass]),t.onHide(e)):(a?B_:M_)(e,t))}))}))},H_=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);o.isDocked()&&z_(e,t,o,n),I_(e,n,o).each((s=>{s.fold((()=>R_(e,t,o)),(n=>V_(e,t,o,n)),(s=>{z_(e,t,o,n,!0),V_(e,t,o,s)}))}))})(e,t,o)},P_=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1),((e,t)=>{const o=e.element;return D_(o,t).bind((e=>F_(o,e,t)))})(e,o).each((n=>{n.fold((()=>R_(e,t,o)),(n=>V_(e,t,o,n)),b)})),o.setVisible(!0),t.contextual.each((t=>{Aa(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),H_(e,t,o)})(e,t,o)};var N_=Object.freeze({__proto__:null,refresh:H_,reset:P_,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n)}),L_=Object.freeze({__proto__:null,events:(e,t)=>_r([Vr(js(),((o,n)=>{e.contextual.each((e=>{Ba(o.element,e.transitionClass)&&(Aa(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Br(lr(),((o,n)=>{H_(o,e,t)})),Br(cr(),((o,n)=>{P_(o,e,t)}))])}),W_=[as("contextual",[qn("fadeInClass"),qn("fadeOutClass"),qn("transitionClass"),Kn("lazyContext"),bi("onShow"),bi("onShown"),bi("onHide"),bi("onHidden")]),gs("lazyViewport",jo),ps("modes",["top","bottom"],En),bi("onDocked"),bi("onUndocked")];const U_=ul({fields:W_,name:"docking",active:L_,apis:N_,state:Object.freeze({__proto__:null,init:e=>{const t=fs(!1),o=fs(!0),n=Hl(),s=fs(e.modes);return ga({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),j_=y(Kr("toolbar-height-change")),G_={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},$_="tox-tinymce--toolbar-sticky-on",q_="tox-tinymce--toolbar-sticky-off",X_=(e,t)=>R(U_.getModes(e),t),K_=e=>{const t=e.element;Je(t).each((o=>{const n="padding-"+U_.getModes(e)[0];if(U_.isDocked(e)){const e=Ut(o);kt(t,"width",e+"px"),kt(o,n,(e=>Vt(e)+(parseInt(_t(e,"margin-top"),10)||0)+(parseInt(_t(e,"margin-bottom"),10)||0))(t)+"px")}else At(t,"width"),At(o,n)}))},Y_=(e,t)=>{t?(Ea(e,G_.fadeOutClass),Ma(e,[G_.transitionClass,G_.fadeInClass])):(Ea(e,G_.fadeInClass),Ma(e,[G_.fadeOutClass,G_.transitionClass]))},J_=(e,t)=>{const o=Ie(e.getContainer());t?(Ta(o,$_),Ea(o,q_)):(Ta(o,q_),Ea(o,$_))},Z_=(e,t)=>{const o=Hl(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||K_(t),J_(e,U_.isDocked(t)),t.getSystem().broadcastOn([Id()],{}),n().each((e=>e.getSystem().broadcastOn([Id()],{})))},a=e.inline?[]:[hl.config({channels:{[j_()]:{onReceive:K_}}})];return[Hp.config({}),U_.config({contextual:{lazyContext:t=>{const o=Vt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer(),s=Wo(Ie(n)),r=s.height-o,a=s.y+(X_(t,"top")?0:o);return B.some(Lo(s.x,a,s.width,r))},onShow:()=>{s((e=>Y_(e,!0)))},onShown:e=>{s((e=>Aa(e,[G_.transitionClass,G_.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=qe(t);yl(o).filter((e=>!Ge(t,e))).filter((t=>Ge(t,Ie(o.dom.body))||$e(e,t))).each((()=>bl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>xl(e).orThunk((()=>t().toOptional().bind((e=>xl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>Y_(e,!1)))},onHidden:()=>{s((e=>Aa(e,[G_.transitionClass])))},...G_},lazyViewport:t=>{const o=jo(),n=cf(e),s=o.y+(X_(t,"top")?n:0),r=o.height-(X_(t,"bottom")?n:0);return Lo(o.x,s,o.width,r)},modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var Q_=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(U_.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(K_)})),e.on("SkinLoaded",(()=>{o().each((e=>{U_.isDocked(e)?U_.reset(e):U_.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(U_.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{U_.refresh(t);const o=t.element;vg(o)&&((e,t)=>{const o=qe(t),n=o.dom.defaultView.innerHeight,s=Fo(o),r=Ie(e.elm),a=Uo(r),i=Rt(r),l=a.y,c=l+i,d=Nt(t),u=Rt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Io(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Io(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{J_(e,!1)}))},isDocked:e=>e().map(U_.isDocked).getOr(!1),getBehaviours:Z_});const eT=xn([pb,Gn("items",Sn([Cn([hb,Zn("items",En)]),En]))].concat(jb)),tT=[os("text"),os("tooltip"),os("icon"),Kn("fetch"),gs("onSetup",(()=>b))],oT=xn([pb,...tT]),nT=e=>Vn("menubutton",oT,e),sT=xn([pb,Bb,Eb,Tb,Db,wb,Ob,us("presets","normal",["normal","color","listpreview"]),zb(1),kb,Cb]);var rT=em({factory:(e,t)=>{const o={focus:kp.focusIn,setMenus:(e,o)=>{const n=P(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=nT(o).mapError((e=>Pn(e))).getOrDie();return DC(n,"tox-mbtn",t.backstage,B.some("menuitem"))}));Dp.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:cl([Dp.config({}),Fp("menubar-events",[zr((t=>{e.onSetup(t)})),Br(Rs(),((e,t)=>{ei(e.element,".tox-mbtn--active").each((o=>{ti(t.event.target,".tox-mbtn").each((t=>{Ge(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{Tw.expand(e),Tw.close(o),Hp.focus(e)}))}))}))}))})),Br(pr(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{Tw.isOpen(o)&&(Tw.expand(e),Tw.close(o))}))}))}))]),kp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),B.some(!0))}),Tx.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[jn("dom"),jn("uid"),jn("onEscape"),jn("backstage"),is("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const aT=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),iT=e=>e.dimension.property,lT=(e,t)=>e.dimension.getDimension(t),cT=(e,t)=>{const o=aT(e,t);Aa(o,[t.shrinkingClass,t.growingClass])},dT=(e,t)=>{Ea(e.element,t.openClass),Ta(e.element,t.closedClass),kt(e.element,iT(t),"0px"),Dt(e.element)},uT=(e,t)=>{Ea(e.element,t.closedClass),Ta(e.element,t.openClass),At(e.element,iT(t))},mT=(e,t,o,n)=>{o.setCollapsed(),kt(e.element,iT(t),lT(t,e.element)),cT(e,t),dT(e,t),t.onStartShrink(e),t.onShrunk(e)},gT=(e,t,o,n)=>{const s=n.getOrThunk((()=>lT(t,e.element)));o.setCollapsed(),kt(e.element,iT(t),s),Dt(e.element);const r=aT(e,t);Ea(r,t.growingClass),Ta(r,t.shrinkingClass),dT(e,t),t.onStartShrink(e)},pT=(e,t,o)=>{const n=lT(t,e.element);("0px"===n?mT:gT)(e,t,o,B.some(n))},hT=(e,t,o)=>{const n=aT(e,t),s=Ba(n,t.shrinkingClass),r=lT(t,e.element);uT(e,t);const a=lT(t,e.element);(s?()=>{kt(e.element,iT(t),r),Dt(e.element)}:()=>{dT(e,t)})(),Ea(n,t.shrinkingClass),Ta(n,t.growingClass),uT(e,t),kt(e.element,iT(t),a),o.setExpanded(),t.onStartGrow(e)},fT=(e,t,o)=>{const n=aT(e,t);return!0===Ba(n,t.growingClass)},bT=(e,t,o)=>{const n=aT(e,t);return!0===Ba(n,t.shrinkingClass)};var vT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){At(e.element,iT(t));const o=lT(t,e.element);kt(e.element,iT(t),o)}},grow:(e,t,o)=>{o.isExpanded()||hT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&pT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&mT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:fT,isShrinking:bT,isTransitioning:(e,t,o)=>fT(e,t)||bT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?pT:hT)(e,t,o)},disableTransitions:cT,immediateGrow:(e,t,o)=>{o.isExpanded()||(uT(e,t),kt(e.element,iT(t),lT(t,e.element)),cT(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),yT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return ha(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:vs(t.dimension.property,"0px")})},events:(e,t)=>_r([Vr(js(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(cT(o,e),t.isExpanded()&&At(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),xT=[jn("closedClass"),jn("openClass"),jn("shrinkingClass"),jn("growingClass"),Qn("getAnimationRoot"),bi("onShrunk"),bi("onStartShrink"),bi("onGrown"),bi("onStartGrow"),is("expanded",!1),Gn("dimension",Nn("property",{width:[wi("property","width"),wi("getDimension",(e=>Ut(e)+"px"))],height:[wi("property","height"),wi("getDimension",(e=>Rt(e)+"px"))]}))];const wT=ul({fields:xT,name:"sliding",active:yT,apis:vT,state:Object.freeze({__proto__:null,init:e=>{const t=fs(e.expanded);return ga({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:S(t.set,!1),setExpanded:S(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),ST="container",kT=[Qd("slotBehaviours",[])],CT=e=>"<alloy.field."+e+">",OT=(e,t)=>{const o=t=>ju(e),n=(t,o)=>(n,s)=>Nu(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==bt(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;kt(o,"display","none"),ht(o,"aria-hidden","true"),wr(e,hr(),{name:t,visible:!1})}})),i=(l=a,(e,t)=>{N(t,(t=>l(e,t)))});var l;const c=n(((e,t)=>{if(!s(e)){const o=e.element;At(o,"display"),xt(o,"aria-hidden"),wr(e,hr(),{name:t,visible:!0})}})),d={getSlotNames:o,getSlot:(t,o)=>Nu(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:c};return{uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.slotBehaviours),apis:d}},_T=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>ua(e))),TT={..._T,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Ru(ST,CT(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>_u({name:e,pname:CT(e)})));return Yu(ST,kT,s,OT,o)}},ET=xn([Eb,Bb,gs("onShow",b),gs("onHide",b),Ob]),BT=e=>({element:()=>e.element.dom}),MT=(e,t)=>{const o=P(ae(t),(e=>{const o=t[e],n=zn((e=>Vn("sidebar",ET,e))(o));return{name:e,getApi:BT,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return P(o,(t=>{const n=fs(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:wv([Pv(t,n),Nv(t,n),Br(hr(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},AT=e=>TT.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:MT(t,e),slotBehaviours:wv([zr((e=>TT.hideAllSlots(e)))])}))),DT=e=>rm.getCurrent(e).bind((e=>wT.isGrowing(e)||wT.hasGrown(e)?rm.getCurrent(e).bind((e=>G(TT.getSlotNames(e),(t=>TT.isShowing(e,t))))):B.none())),FT=Kr("FixSizeEvent"),IT=Kr("AutoSizeEvent");var RT=Object.freeze({__proto__:null,block:(e,t,o,n)=>{ht(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=cl([kp.config({mode:"special",onTab:()=>B.some(!0),onShiftTab:()=>B.some(!0)}),Hp.config({})]),a=n(s,r),i=s.getSystem().build(a);Dp.append(s,$a(i)),i.hasConfigured(kp)&&t.focus&&kp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Dp.remove(s,i)))},unblock:(e,t,o)=>{xt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()}}),VT=[gs("getRoot",B.none),ms("focus",!0),bi("onBlock"),bi("onUnblock")];const zT=ul({fields:VT,name:"blocking",apis:RT,state:Object.freeze({__proto__:null,init:()=>{const e=Vl((e=>e.destroy()));return ga({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),HT=e=>{const t=Ae(e),o=et(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:Lr(t)};return{tag:Ve(t),classes:s,attributes:n,...r}},PT=e=>rm.getCurrent(e).each((e=>bl(e.element))),NT=(e,t,o)=>{const n=fs(!1),s=Hl(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):B.from(s.target)).map(Ie).filter(He).exists((e=>Ba(e,"mce-pastebin"))))&&(o.preventDefault(),PT(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n="data-mce-tabindex";B.from(e.iframeElement).map(Ie).each((e=>{t?(vt(e,o).each((t=>ht(e,n,t))),ht(e,o,-1)):(xt(e,o),vt(e,n).each((t=>{ht(e,o,t),xt(e,n)})))}))})(e,o),o)zT.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:HT('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),At(s,"display"),xt(s,"aria-hidden"),e.hasFocus()&&PT(t);else{const o=rm.getCurrent(t).exists((e=>vl(e.element)));zT.unblock(t),kt(s,"display","none"),ht(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),e.dispatch("AfterProgressState",{state:s}))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=wh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},LT=(e,t,o)=>({within:e,extra:t,withinWidth:o}),WT=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return B.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=W(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},UT=e=>P(e,(e=>e.element)),jT=(e,t)=>{const o=P(t,(e=>$a(e)));x_.setGroups(e,o)},GT=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=Lu(e,t,"primary"),r=mw.getCoupled(e,"overflowGroup");kt(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>xl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),jT(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=WT(t,e,o);return 0===n.extra.length?B.some(n):B.none()})(e,t,o).getOrThunk((()=>WT(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=UT(e.concat(t));return LT(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=UT(e).concat([o]);return LT(s,UT(t),n)})(r,a,n,i):((e,t,o)=>LT(UT(e),[],o))(r,0,i)})(Ut(s.element),t.builtGroups.get(),(e=>Ut(e.element)),r);0===l.extra.length?(Dp.remove(s,r),o([])):(jT(s,l.within),o(l.extra)),At(s.element,"visibility"),Dt(s.element),i.each(Hp.focus)},$T=y([Qd("splitToolbarBehaviours",[mw]),Wn("builtGroups",(()=>fs([])))]),qT=y([hi(["overflowToggledClass"]),ss("getOverflowBounds"),jn("lazySink"),Wn("overflowGroups",(()=>fs([])))].concat($T())),XT=y([_u({factory:x_,schema:v_(),name:"primary"}),Tu({schema:v_(),name:"overflow"}),Tu({name:"overflow-button"}),Tu({name:"overflow-group"})]),KT=y(((e,t)=>{((e,t)=>{const o=Wt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);kt(e,"max-width",o+"px")})(e,Math.floor(t))})),YT=y([hi(["toggledClass"]),jn("lazySink"),Kn("fetch"),ss("getBounds"),as("fireDismissalEventInstead",[is("event",mr())]),ac()]),JT=y([Tu({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:cl([$p.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),Tu({factory:x_,schema:v_(),name:"toolbar",overrides:e=>({toolbarBehaviours:cl([kp.config({mode:"cyclic",onEscape:t=>(Nu(t,e,"button").each(Hp.focus),B.none())})])})})]),ZT=(e,t)=>{const o=mw.getCoupled(e,"toolbarSandbox");Dd.isOpen(o)?Dd.close(o):Dd.open(o,t.toolbar())},QT=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();id.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:KT()}}},s)},eE=(e,t,o,n,s)=>{x_.setGroups(t,s),QT(e,t,o,n),$p.on(e)},tE=tm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Sh.sketch({...n.button(),action:e=>{ZT(e,n)},buttonBehaviours:nu({dump:n.button().buttonBehaviours},[mw.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=ni();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:cl([kp.config({mode:"special",onEscape:e=>(Dd.close(e),B.some(!0))}),Dd.config({onOpen:(s,r)=>{o.fetch().get((s=>{eE(e,r,o,t.layouts,s),n.link(e.element),kp.focusIn(r)}))},onClose:()=>{$p.off(e),Hp.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>si(o,n)||si(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),hl.config({channels:{...zd({isExtraPart:_,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Pd({doReposition:()=>{Dd.getState(mw.getCoupled(e,"toolbarSandbox")).each((n=>{QT(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Dd.getState(mw.getCoupled(t,"toolbarSandbox")).each((s=>{eE(t,s,e,o.layouts,n)}))},reposition:t=>{Dd.getState(mw.getCoupled(t,"toolbarSandbox")).each((n=>{QT(t,n,e,o.layouts)}))},toggle:e=>{ZT(e,n)},getToolbar:e=>Dd.getState(mw.getCoupled(e,"toolbarSandbox")),isOpen:e=>Dd.isOpen(mw.getCoupled(e,"toolbarSandbox"))}}),configFields:YT(),partFields:JT(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),oE=y([jn("items"),hi(["itemSelector"]),Qd("tgroupBehaviours",[kp])]),nE=y([Bu({name:"items",unit:"item"})]),sE=tm({name:"ToolbarGroup",configFields:oE(),partFields:nE(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:tu(e.tgroupBehaviours,[kp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),rE=e=>P(e,(e=>$a(e))),aE=(e,t,o)=>{GT(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{tE.setGroups(e,rE(n))}))}))},iE=tm({name:"SplitFloatingToolbar",configFields:qT(),partFields:XT(),factory:(e,t,o,n)=>{const s=kh(tE.sketch({fetch:()=>Jx((t=>{t(rE(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Gi,ji],onRtl:()=>[ji,Gi],onBottomLtr:()=>[qi,$i],onBottomRtl:()=>[$i,qi]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:tu(e.splitToolbarBehaviours,[mw.config({others:{overflowGroup:()=>sE.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(P(o,t.getSystem().build)),aE(t,s,e)},refresh:t=>aE(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{tE.toggle(e)}))},isOpen:e=>s.getOpt(e).map(tE.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{tE.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(tE.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),lE=y([hi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),bi("onOpened"),bi("onClosed")].concat($T())),cE=y([_u({factory:x_,schema:v_(),name:"primary"}),_u({factory:x_,schema:v_(),name:"overflow",overrides:e=>({toolbarBehaviours:cl([wT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{Nu(t,e,"overflow-button").each((e=>{$p.off(e),Hp.focus(e)})),e.onClosed(t)},onGrown:t=>{kp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{Nu(t,e,"overflow-button").each($p.on)}}),kp.config({mode:"acyclic",onEscape:t=>(Nu(t,e,"overflow-button").each(Hp.focus),B.some(!0))})])})}),Tu({name:"overflow-button",overrides:e=>({buttonBehaviours:cl([$p.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Tu({name:"overflow-group"})]),dE=(e,t)=>{Nu(e,t,"overflow-button").bind((()=>Nu(e,t,"overflow"))).each((o=>{uE(e,t),wT.toggleGrow(o)}))},uE=(e,t)=>{Nu(e,t,"overflow").each((o=>{GT(e,t,(e=>{const t=P(e,(e=>$a(e)));x_.setGroups(o,t)})),Nu(e,t,"overflow-button").each((e=>{wT.hasGrown(o)&&$p.on(e)})),wT.refresh(o)}))},mE=tm({name:"SplitSlidingToolbar",configFields:lE(),partFields:cE(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:tu(e.splitToolbarBehaviours,[mw.config({others:{overflowGroup:e=>sE.sketch({...n["overflow-group"](),items:[Sh.sketch({...n["overflow-button"](),action:t=>{xr(e,s)}})]})}}),Fp("toolbar-toggle-events",[Br(s,(t=>{dE(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=P(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),uE(t,e)},refresh:t=>uE(t,e),toggle:t=>dE(t,e),isOpen:t=>((e,t)=>Nu(e,t,"overflow").map(wT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),gE=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[sE.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:cl([Tx.config({}),Hp.config({})])}},pE=e=>sE.sketch(gE(e)),hE=(e,t)=>{const o=zr((t=>{const o=P(e.initGroups,pE);x_.setGroups(t,o)}));return cl([zv(e.providers.isDisabled),Iv(),kp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Fp("toolbar-events",[o])])},fE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":gE({title:B.none(),items:[]}),"overflow-button":RC({name:"more",icon:B.some("more-drawer"),enabled:!0,tooltip:B.some("More..."),primary:!1,buttonType:B.none(),borderless:!1},B.none(),e.providers)},splitToolbarBehaviours:hE(e,t)}},bE=e=>{const t=fE(e),o=iE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return iE.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Uo(t),n=Ke(t),s=Uo(n),r=Math.max(n.dom.scrollHeight,s.height);return Lo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"}})},vE=e=>{const t=mE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=mE.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=fE(e);return mE.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:e=>{e.getSystem().broadcastOn([j_()],{type:"opened"})},onClosed:e=>{e.getSystem().broadcastOn([j_()],{type:"closed"})}})},yE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return x_.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===Vh.scrolling?["tox-toolbar--scrolling"]:[])},components:[x_.parts.groups({})],toolbarBehaviours:hE(e,t)})},xE=g_.optional({factory:rT,name:"menubar",schema:[jn("backstage")]}),wE=g_.optional({factory:{sketch:e=>b_.sketch({uid:e.uid,dom:e.dom,listBehaviours:cl([kp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>yE({type:e.type,uid:Kr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),B.some(!0))}),setupItem:(e,t,o,n)=>{x_.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[jn("dom"),jn("onEscape")]}),SE=g_.optional({factory:{sketch:e=>{const t=(e=>e.type===Vh.sliding?vE:e.type===Vh.floating?bE:yE)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),B.some(!0)),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[jn("dom"),jn("onEscape"),jn("getSink")]}),kE=g_.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?Z_:k_;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:cl(o(t,e.sharedBackstage))}}},name:"header",schema:[jn("dom")]}),CE=g_.optional({name:"socket",schema:[jn("dom")]}),OE=g_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:cl([Tx.config({}),Hp.config({}),wT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{rm.getCurrent(e).each(TT.hideAllSlots),xr(e,IT)},onGrown:e=>{xr(e,IT)},onStartGrow:e=>{wr(e,FT,{width:Et(e.element,"width").getOr("")})},onStartShrink:e=>{wr(e,FT,{width:Ut(e.element)+"px"})}}),Dp.config({}),rm.config({find:e=>{const t=Dp.contents(e);return oe(t)}})])}],behaviours:cl([_k(0),Fp("sidebar-sliding-events",[Br(FT,((e,t)=>{kt(e.element,"width",t.event.width)})),Br(IT,((e,t)=>{At(e.element,"width")}))])])})},name:"sidebar",schema:[jn("dom")]}),_E=g_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:cl([Dp.config({}),zT.config({focus:!1}),rm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[jn("dom")]});var TE=tm({name:"OuterContainer",factory:(e,t,o)=>{const n={getSocket:t=>m_.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{m_.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{rm.getCurrent(e).each((e=>{Dp.set(e,[AT(t)]);const n=null==o?void 0:o.toLowerCase();r(o)&&ve(t,n)&&rm.getCurrent(e).each((t=>{TT.showSlot(t,n),wT.immediateGrow(e),At(e.element,"width")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{m_.getPart(t,e,"sidebar").each((e=>((e,t)=>{rm.getCurrent(e).each((e=>{rm.getCurrent(e).each((o=>{wT.hasGrown(e)?TT.isShowing(o,t)?wT.shrink(e):(TT.hideAllSlots(o),TT.showSlot(o,t)):(TT.hideAllSlots(o),TT.showSlot(o,t),wT.grow(e))}))}))})(e,o)))},whichSidebar:t=>m_.getPart(t,e,"sidebar").bind(DT).getOrNull(),getHeader:t=>m_.getPart(t,e,"header"),getToolbar:t=>m_.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{m_.getPart(t,e,"toolbar").each((e=>{e.getApis().setGroups(e,o)}))},setToolbars:(t,o)=>{m_.getPart(t,e,"multiple-toolbar").each((e=>{b_.setItems(e,o)}))},refreshToolbar:t=>{m_.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{m_.getPart(t,e,"toolbar").each((e=>{var t,o;o=t=>t(e),null!=(t=e.getApis().toggle)?B.some(o(t)):B.none()}))},isToolbarDrawerToggled:t=>m_.getPart(t,e,"toolbar").bind((e=>B.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>m_.getPart(t,e,"throbber"),focusToolbar:t=>{m_.getPart(t,e,"toolbar").orThunk((()=>m_.getPart(t,e,"multiple-toolbar"))).each((e=>{kp.focusIn(e)}))},setMenubar:(t,o)=>{m_.getPart(t,e,"menubar").each((e=>{rT.setMenus(e,o)}))},focusMenubar:t=>{m_.getPart(t,e,"menubar").each((e=>{rT.focus(e)}))}};return{uid:e.uid,dom:e.dom,components:t,apis:n,behaviours:e.behaviours}},configFields:[jn("dom"),jn("behaviours")],partFields:[kE,xE,SE,wE,CE,OE,_E],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{const n=P(o,(e=>pE(e)));e.setToolbar(t,n)},setToolbars:(e,t,o)=>{const n=P(o,(e=>P(e,pE)));e.setToolbars(t,n)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)}}});const EE={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},BE=e=>"string"==typeof e?e.split(" "):e,ME=(e,t)=>{const o={...EE,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?BE("file edit view insert format tools table help"):BE(!1===t.menubar?"":t.menubar),r=W(s,(e=>{const o=ve(EE,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),a=P(r,(n=>{const s=o[n];return((e,t,o)=>{const n=tf(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||V(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:BE(s.items)},t,e)}));return W(a,(e=>e.getItems().length>0&&V(e.getItems(),(e=>"separator"!==e.type))))},AE=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},DE=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),FE=(e,t)=>DE(e,t+"/skin.min.css",e.ui.styleSheetLoader),IE=(e,t)=>{var o;return o=Ie(e.getElement()),ct(o).isSome()?DE(e,t+"/skin.shadowdom.min.css",Hh.DOM.styleSheetLoader):Promise.resolve()},RE=(e,t)=>{const o=Tf(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Of(t)&&r(o)?Promise.all([FE(t,o),IE(t,o)]).then(AE(t),((e,t)=>()=>((e,t)=>e.dispatch("SkinLoadError",t))(e,{message:"Skin could not be loaded"}))(t)):AE(t)()},VE=S(RE,!1),zE=S(RE,!0),HE=(e,t)=>o=>{const n=zl(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}},PE=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},NE=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},LE=(e,t)=>()=>e.execCommand(t),WE=(e,t,o)=>{const n=(e,n,r,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return B.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>s(e,n,a)));return 0===n&&t.length<=0?B.none():B.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>s(e,n,a)))})}return B.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!r,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},s=(e,t,s)=>{const r="formatter"===e.type&&o.isInvalid(e);return 0===t?r?[]:n(e,t,!1,s).toArray():n(e,t,r,s).toArray()},r=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>s(e,n,t)))};return{validateItems:r,getFetch:(e,t)=>(o,n)=>{const s=t(),a=r(s);n(oC(a,Uf.CLOSE_ON_EXECUTE,e,!1))}}},UE=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>P(n.data,(e=>HO(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:WE(0,t,o),getStyleItems:s}},jE=(e,t,o)=>{const{items:n,getStyleItems:s}=UE(0,t,o),r=PE(e,"NodeChange",(e=>{const t=e.getComponent();o.updateText(t)}));return Zk({text:o.icon.isSome()?B.none():o.text,icon:o.icon,tooltip:B.from(o.tooltip),role:B.none(),fetch:n.getFetch(t,s),onSetup:r,getApi:e=>({getComponent:y(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var GE;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(GE||(GE={}));const $E=(e,t,o)=>{const n=(s=((e,t)=>t===GE.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),P(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},qE=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],XE=e=>{const t={type:"basic",data:qE};return{tooltip:"Align",text:B.none(),icon:B.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:e=>B.none,onAction:t=>()=>G(qE,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(qE,(t=>e.formatter.match(t.format))).fold(y("left"),(e=>e.title.toLowerCase()));wr(t,Jk,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},KE=(e,t)=>{const o=t(),n=P(o,(e=>e.format));return B.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>ke(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},YE=e=>{const t="Paragraph",o=$E(e,"block_formats",GE.SemiColon);return{tooltip:"Blocks",text:B.some(t),icon:B.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))})},onAction:NE(e),updateText:n=>{const s=KE(e,(()=>o.data)).fold(y(t),(e=>e.title));wr(n,Yk,{text:s})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},JE=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],ZE=e=>{const t=e.split(/\s*,\s*/);return P(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},QE=e=>{const t="System Font",o=()=>{const o=e=>e?ZE(e)[0]:"",s=e.queryCommandValue("FontName"),r=n.data,a=s?s.toLowerCase():"",i=G(r,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>ke((e=>0===e.indexOf("-apple-system")&&(()=>{const t=ZE(e.toLowerCase());return K(JE,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:s}},n=$E(e,"font_family_formats",GE.SemiColon);return{tooltip:"Fonts",text:B.some(t),icon:B.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>B.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),s=t.fold(y(n),(e=>e.title));wr(e,Yk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},eB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},tB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},oB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(tB,e).getOr(e),nB=e=>be(eB,e).getOr(""),sB=e=>{const t=()=>{let t=B.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=oB(s,e),r=nB(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=y(B.none),n=$E(e,"font_size_formats",GE.Space);return{tooltip:"Font sizes",text:B.some("12pt"),icon:B.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),s=o.fold(y(n),(e=>e.title));wr(e,Yk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},rB=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:B.some(o),icon:B.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):B.none()},onAction:NE(e),updateText:t=>{const n=e=>{const t=e.items;return void 0!==t&&t.length>0?X(t,n):[{title:e.title,format:e.format}]},s=X(zO(e),n),r=KE(e,y(s)).fold(y(o),(e=>e.title));wr(t,Yk,{text:r})},shouldHide:Qh(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}};var aB=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Op:Cp)(o,r)}))};return _r([Br(Zs(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;R(s.channels,n)&&o(t,s.data)}})),zr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),iB=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),lB=[jn("channel"),Qn("renderComponents"),Qn("updateState"),Qn("initialData"),ms("reuseDom",!0)];const cB=ul({fields:lB,name:"reflecting",active:aB,apis:iB,state:Object.freeze({__proto__:null,init:()=>{const e=fs(B.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(B.none())}}})}),dB=y([jn("toggleClass"),jn("fetch"),yi("onExecute"),is("getHotspot",B.some),is("getAnchorOverrides",y({})),ac(),yi("onItemExecute"),Qn("lazySink"),jn("dom"),bi("onOpen"),Qd("splitDropdownBehaviours",[mw,kp,Hp]),is("matchWidth",!1),is("useMinWidth",!1),is("eventOrder",{}),Qn("role")].concat(Cw())),uB=_u({factory:Sh,schema:[jn("dom")],name:"arrow",defaults:()=>({buttonBehaviours:cl([Hp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Sr)},buttonBehaviours:cl([$p.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),mB=_u({factory:Sh,schema:[jn("dom")],name:"button",defaults:()=>({buttonBehaviours:cl([Hp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),gB=y([uB,mB,Eu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[jn("text")],name:"aria-descriptor"}),Tu({schema:[pi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),pw()]),pB=tm({name:"SplitDropdown",configFields:dB(),partFields:gB(),factory:(e,t,o,n)=>{const s=e=>{rm.getCurrent(e).each((e=>{Bm.highlightFirst(e),kp.focusIn(e)}))},r=t=>{yw(e,x,t,n,s,hw.HighlightFirst).get(b)},a=t=>{const o=Lu(t,e,"button");return Sr(o),B.some(!0)},i={..._r([zr(((t,o)=>{Nu(t,e,"aria-descriptor").each((e=>{const o=Kr("aria");ht(e.element,"id",o),ht(t.element,"aria-describedby",o)}))}))]),...Xp(B.some(r))},l={repositionMenus:e=>{$p.isOn(e)&&kw(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[Qs()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:tu(e.splitDropdownBehaviours,[mw.config({others:{sandbox:t=>{const o=Lu(t,e,"arrow");return Sw(e,t,{onOpen:()=>{$p.on(o),$p.on(t)},onClose:()=>{$p.off(o),$p.off(t)}})}}}),kp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),B.some(!0))}),Hp.config({}),$p.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),hB=e=>({isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t)}),fB=e=>({setActive:t=>{$p.set(e,t)},isActive:()=>$p.isOn(e),isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t)}),bB=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),vB=Kr("focus-button"),yB=(e,t,o,n,s,r)=>({dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:bB(o,r)},components:Uv([e.map((e=>qk(e,r.icons))),t.map((e=>Kk(e,"tox-tbtn",r)))]),eventOrder:{[As()]:["focusing","alloy.base.behaviour","common-button-display-events"]},buttonBehaviours:cl([zv(r.isDisabled),Iv(),Fp("common-button-display-events",[Br(As(),((e,t)=>{t.event.prevent(),xr(e,vB)}))])].concat(n.map((o=>cB.config({channel:o,initialData:{icon:e,text:t},renderComponents:(e,t)=>Uv([e.icon.map((e=>qk(e,r.icons))),e.text.map((e=>Kk(e,"tox-tbtn",r)))])}))).toArray()).concat(s.getOr([])))}),xB=(e,t,o)=>{const n=fs(b),s=yB(e.icon,e.text,e.tooltip,B.none(),B.none(),o);return Sh.sketch({dom:s.dom,components:s.components,eventOrder:Gk,buttonBehaviours:cl([Fp("toolbar-button-events",[(r={onAction:e.onAction,getApi:t.getApi},Nr(((e,t)=>{Hv(r,e)((t=>{wr(e,jk,{buttonApi:t}),r.onAction(t)}))}))),Pv(t,n),Nv(t,n)]),zv((()=>!e.enabled||o.isDisabled())),Iv()].concat(t.toolbarButtonBehaviours))});var r},wB=(e,t,o)=>xB(e,{toolbarButtonBehaviours:[].concat(o.length>0?[Fp("toolbarButtonWith",o)]:[]),getApi:hB,onSetup:e.onSetup},t),SB=(e,t,o)=>sn(xB(e,{toolbarButtonBehaviours:[Dp.config({}),$p.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Fp("toolbarToggleButtonWith",o)]:[]),getApi:fB,onSetup:e.onSetup},t)),kB=(e,t,o)=>n=>Jx((e=>t.fetch(e))).map((s=>B.from(lx(sn(Xy(Kr("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,Uf.CLOSE_ON_EXECUTE,t.select.getOr(_),o),{movement:Yy(t.columns,t.presets),menuBehaviours:wv("auto"!==t.columns?[]:[zr(((e,o)=>{xv(e,4,eb(t.presets)).each((({numRows:t,numColumns:o})=>{kp.setGridSize(e,t,o)}))}))])}))))),CB=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],OB=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Pn(e))).getOrDie();return t(r,n,s)},_B={button:OB($b,((e,t)=>{return o=e,n=t.backstage.shared.providers,wB(o,n,[]);var o,n})),togglebutton:OB(Kb,((e,t)=>{return o=e,n=t.backstage.shared.providers,SB(o,n,[]);var o,n})),menubutton:OB(nT,((e,t)=>DC(e,"tox-tbtn",t.backstage,B.none()))),splitbutton:OB((e=>Vn("SplitButton",sT,e)),((e,t)=>((e,t)=>{const o=Kr("channel-update-split-dropdown-display"),n=e=>({isEnabled:()=>!ym.isDisabled(e),setEnabled:t=>ym.set(e,!t),setIconFill:(t,o)=>{ei(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each((e=>{ht(e,"fill",o)}))},setActive:t=>{ht(e.element,"aria-pressed",t),ei(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>$p.set(e,t)))}))},isActive:()=>ei(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists($p.isOn)))}),s=fs(b),r={getApi:n,onSetup:e.onSetup};return pB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...bB(e.tooltip,t.providers)}},onExecute:t=>{e.onAction(n(t))},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:cl([Vv(t.providers.isDisabled),Iv(),Fp("split-dropdown-events",[Br(vB,Hp.focus),Pv(r,s),Nv(r,s)]),Ew.config({})]),eventOrder:{[dr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:kB(n,e,t.providers),parts:{menu:ab(0,e.columns,e.presets)},components:[pB.parts.button(yB(e.icon,e.text,B.none(),B.some(o),B.some([$p.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),pB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Mh("chevron-down",t.providers.icons)},buttonBehaviours:cl([Vv(t.providers.isDisabled),Iv(),Ah()])}),pB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.backstage.shared))),grouptoolbarbutton:OB((e=>Vn("GroupToolbarButton",eT,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[sc]:t.backstage.shared.header.isPositionedAtTop()?nc.TopToBottom:nc.BottomToTop};if(of(o)===Vh.floating)return((e,t,o,n)=>{const s=t.shared;return tE.sketch({lazySink:s.getSink,fetch:()=>Jx((t=>{t(P(o(e.items),pE))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:yB(e.icon,e.text,e.tooltip,B.none(),B.none(),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t.backstage,(e=>EB(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,B.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")})),styleSelectButton:(e,t)=>((e,t)=>{const o={type:"advanced",...t.styles};return jE(e,t,rB(e,o))})(e,t.backstage),fontsizeSelectButton:(e,t)=>((e,t)=>jE(e,t,sB(e)))(e,t.backstage),fontSelectButton:(e,t)=>((e,t)=>jE(e,t,QE(e)))(e,t.backstage),formatButton:(e,t)=>((e,t)=>jE(e,t,YE(e)))(e,t.backstage),alignMenuButton:(e,t)=>((e,t)=>jE(e,t,XE(e)))(e,t.backstage)},TB={styles:_B.styleSelectButton,fontsize:_B.fontsizeSelectButton,fontfamily:_B.fontSelectButton,blocks:_B.formatButton,align:_B.alignMenuButton},EB=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=P(CB,(t=>{const o=W(t.items,(t=>ve(e,t)||ve(TB,t)));return{name:t.name,items:o}}));return W(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return P(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=P(s,(s=>{const r=X(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(TB,o.toLowerCase()).map((t=>t(e,s))).orThunk((()=>B.none()))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>be(_B,e.type).fold((()=>(console.error("skipping button defined by",e),B.none())),(n=>B.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),B.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:B.from(e.translate(s.name)),items:r}}));return W(a,(e=>e.items.length>0))},BB=(e,t,o,n)=>{const s=t.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return EB(e,s,{backstage:n},B.none())}));TE.setToolbars(s,t)}else TE.setToolbar(s,EB(e,o,{backstage:n},B.none()))},MB=So(),AB=MB.os.isiOS()&&MB.os.version.major<=12;var DB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const r=fs(0),a=t.outerContainer;VE(e);const i=Ie(s.targetNode),l=lt(it(i));((e,t)=>{xd(e,t,To)})(i,t.mothership),yd(l,t.uiMothership),e.on("PostRender",(()=>{BB(e,t,o,n),r.set(e.getWin().innerWidth),TE.setMenubar(a,ME(e,o)),TE.setSidebar(a,o.sidebar,Cf(e)),((e,t)=>{const o=e.dom;let n=e.getWin();const s=e.getDoc().documentElement,r=fs(Ht(n.innerWidth,n.innerHeight)),a=fs(Ht(s.offsetWidth,s.offsetHeight)),i=()=>{const t=r.get();t.left===n.innerWidth&&t.top===n.innerHeight||(r.set(Ht(n.innerWidth,n.innerHeight)),Oy(e))},l=()=>{const t=e.getDoc().documentElement,o=a.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(a.set(Ht(t.offsetWidth,t.offsetHeight)),Oy(e))},c=t=>((e,t)=>e.dispatch("ScrollContent",t))(e,t);o.bind(n,"resize",i),o.bind(n,"scroll",c);const d=Ll(Ie(e.getBody()),"load",l),u=t.uiMothership.element;e.on("hide",(()=>{kt(u,"display","none")})),e.on("show",(()=>{At(u,"display")})),e.on("NodeChange",l),e.on("remove",(()=>{d.unbind(),o.unbind(n,"resize",i),o.unbind(n,"scroll",c),n=null}))})(e,t)}));const d=TE.getSocket(a).getOrDie("Could not find expected socket element");if(AB){Ct(d.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Nl(d.element,"scroll",t.throttle);e.on("remove",o.unbind)}Fv(e,t),e.addCommand("ToggleSidebar",((t,o)=>{TE.toggleSidebar(a,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>TE.whichSidebar(a)));const u=of(e);u!==Vh.sliding&&u!==Vh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==r.get()&&(TE.refreshToolbar(t.outerContainer),r.set(o))}));const m={setEnabled:e=>{Dv(t,!e)},isEnabled:()=>!ym.isDisabled(a)};return{iframeContainer:d.element.dom,editorContainer:a.element.dom,api:m}}});const FB=e=>/^[0-9\.]+(|px)$/i.test(""+e)?B.some(parseInt(""+e,10)):B.none(),IB=e=>h(e)?e+"px":e,RB=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},VB=e=>{const t=$h(e),o=qh(e),n=Kh(e);return FB(t).map((e=>RB(e,o,n)))},{ToolbarLocation:zB,ToolbarMode:HB}=Pf,PB=(e,t)=>{const o=Wo(e);return{pos:t?o.y:o.bottom,bounds:o}};var NB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mothership:r,uiMothership:a,outerContainer:i}=t,l=fs(null),c=Ie(s.targetNode),d=((e,t,o,n,s)=>{const{uiMothership:r,outerContainer:a}=o,i=Hh.DOM,l=If(e),c=zf(e),d=Kh(e).or(VB(e)),u=n.shared.header,m=u.isPositionedAtTop,g=of(e),p=g===HB.sliding||g===HB.floating,h=fs(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?Rt(e.components()[1].element):0)):0,v=()=>{r.broadcastOn([Id()],{})},x=(e=!1)=>{if(f()){if(l||(()=>{const e=d.getOrThunk((()=>{const e=FB(_t(mt(),"margin-left")).getOr(0);return Ut(mt())-Nt(t).left+e}));kt(s.get().element,"max-width",e+"px")})(),p&&TE.refreshToolbar(a),l||(()=>{const e=TE.getToolbar(a),o=b(e),n=Wo(t),r=m()?Math.max(n.y-Rt(s.get().element)+o,0):n.bottom;Ct(a.element,{position:"absolute",top:Math.round(r)+"px",left:Math.round(n.x)+"px"})})(),c){const t=s.get();e?U_.reset(t):U_.refresh(t)}v()}},w=(o=!0)=>{if(l||!c||!f())return;const n=u.getDockingMode(),r=(o=>{switch(sf(e)){case zB.auto:const e=TE.getToolbar(a),n=b(e),s=Rt(o.element)-n,r=Wo(t);if(r.y>s)return"top";{const e=Ke(t),o=Math.max(e.dom.scrollHeight,Rt(e));return r.bottom<o-s||jo().bottom<r.bottom-s?"bottom":"top"}case zB.bottom:return"bottom";case zB.top:default:return"top"}})(s.get());r!==n&&((e=>{const t=s.get();U_.setModes(t,[e]),u.setDockingMode(e);const o=m()?nc.TopToBottom:nc.BottomToTop;ht(t.element,sc,o)})(r),o&&x(!0))};return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),kt(a.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),At(r.element,"display"),w(!1),x()},hide:()=>{h.set(!1),o.outerContainer&&(kt(a.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus")),kt(r.element,"display","none")},update:x,updateMode:w,repositionPopups:v}})(e,c,t,n,l),u=lf(e);zE(e);const m=()=>{if(l.get())return void d.show();l.set(TE.getHeader(i).getOrDie());const s=Rf(e);yd(s,r),yd(s,a),BB(e,t,o,n),TE.setMenubar(i,ME(e,o)),d.show(),((e,t,o,n)=>{const s=fs(PB(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=PB(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&Oy(e,n),o.isVisible()&&(i!==r?o.update(!0):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(!0))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))})),e.on("ScrollWindow",(()=>o.updateMode()));const a=zl();a.set(Ll(Ie(e.getBody()),"load",r)),e.on("remove",(()=>{a.clear()}))})(e,c,d,u),e.nodeChanged()};e.on("show",m),e.on("hide",d.hide),u||(e.on("focus",m),e.on("blur",d.hide)),e.on("init",(()=>{(e.hasFocus()||u)&&m()})),Fv(e,t);const g={show:()=>{m()},hide:()=>{d.hide()},setEnabled:e=>{Dv(t,!e)},isEnabled:()=>!ym.isDisabled(i)};return{editorContainer:i.element.dom,api:g}}});const LB="contexttoolbar-hide",WB=(e,t)=>Br(jk,((o,n)=>{const s=(e=>({hide:()=>xr(e,nr()),getValue:()=>Zd.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),UB=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=kh($x.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:cl([kp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Sr(e),!0))),onLeft:(e,t)=>(t.cut(),B.none()),onRight:(e,t)=>(t.cut(),B.none())})])})),s=((e,t,o)=>{const n=P(t,(t=>kh(((e,t,o)=>{const n={backstage:{shared:{providers:o}}};return"contextformtogglebutton"===t.type?((e,t,o)=>{const{primary:n,...s}=t.original,r=zn(Kb({...s,type:"togglebutton",onAction:b}));return SB(r,o.backstage.shared.providers,[WB(e,t)])})(e,t,n):((e,t,o)=>{const{primary:n,...s}=t.original,r=zn($b({...s,type:"button",onAction:b}));return wB(r,o.backstage.shared.providers,[WB(e,t)])})(e,t,n)})(e,t,o))));return{asSpecs:()=>P(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?B.from(n[o]).bind((t=>t.getOpt(e))).filter(k(ym.isDisabled)):B.none()))}})(n,e.commands,t);return[{title:B.none(),items:[n.asSpec()]},{title:B.none(),items:s.asSpecs()}]},jB=(e,t,o=.01)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,GB=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=nt(Ie(e.startContainer),e.startOffset).element;return(Pe(o)?Je(o):B.some(o)).filter(He).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Fo();return Lo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Uo(Ie(e.getBody()));return Lo(o.x+t.left,o.y+t.top,t.width,t.height)}},$B=(e,t,o,n=0)=>{const s=Vo(window),r=Wo(Ie(e.getContentAreaContainer())),a=_f(e)||Bf(e)||Af(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Lo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=Ie(e.getContainer()),i=ei(a,".tox-editor-header").getOr(a),l=Wo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Wo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Lo(i,c,l,d-c)}},qB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},XB={maxHeightFunction:Xl(),maxWidthFunction:KT()},KB=e=>"node"===e,YB=(e,t,o,n,s)=>{const r=GB(e),a=n.lastElement().exists((e=>Ge(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=nt(Ie(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Ge(n.element,t)})(e,o)?a?yO:pO:a?((e,o,s)=>{const a=Et(e,"position");kt(e,"position",o);const i=jB(r,Wo(t))&&!n.isReposition()?wO:yO;return a.each((t=>kt(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Fo().top:s.y)+(Rt(t)+12)<=r.y?pO:hO},JB=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...YB(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>KB(n)?[s(e)]:[];return t?{onLtr:e=>[Ki,ji,Gi,$i,qi,Xi].concat(r(e)),onRtl:e=>[Ki,Gi,ji,qi,$i,Xi].concat(r(e))}:{onLtr:e=>[Xi,Ki,$i,ji,qi,Gi].concat(r(e)),onRtl:e=>[Xi,Ki,qi,Gi,$i,ji].concat(r(e))}},ZB=(e,t)=>{const o=W(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=L(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},QB=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return N(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=zn(Vn("ContextForm",ov,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Vn("ContextToolbar",nv,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},eM=Kr("forward-slide"),tM=Kr("backward-slide"),oM=Kr("change-slide-event"),nM="tox-pop--resizing",sM="tox-pop--transition",rM=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=So().deviceType.isTouch,i=Hl(),l=Hl(),c=Hl(),d=Ga((e=>{const t=fs([]);return xh.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),xh.getContent(e).each((e=>{At(e.element,"visibility")})),Ea(e.element,nM),At(e.element,"width")},inlineBehaviours:cl([Fp("context-toolbar-events",[Vr(js(),((e,t)=>{"width"===t.event.raw.propertyName&&(Ea(e.element,nM),At(e.element,"width"))})),Br(oM,((e,t)=>{const o=e.element;At(o,"width");const n=Ut(o);xh.setContent(e,t.event.contents),Ta(o,nM);const s=Ut(o);kt(o,"width",n+"px"),xh.getContent(e).each((e=>{t.event.focus.bind((e=>(bl(e),xl(o)))).orThunk((()=>(kp.focusIn(e),yl(it(o)))))})),setTimeout((()=>{kt(e.element,"width",s+"px")}),0)})),Br(eM,((e,o)=>{xh.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:yl(it(e.element))}]))})),wr(e,oM,{contents:o.event.forwardContents,focus:B.none()})})),Br(tM,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),wr(e,oM,{contents:$a(o.bar),focus:o.focus})}))}))]),kp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(xr(o,tM),B.some(!0))))})]),lazySink:()=>qo.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),B.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=KB(t)?1:0;return $B(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter(ut).map(Uo).getOrThunk((()=>GB(e))))(e,i.get()):GB(e);return t.height<=0||!jB(o,t)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),xh.hide(d)},h=()=>{if(xh.isOpen(d)){const e=d.element;At(e,"display"),g()?kt(e,"display","none"):(l.set(0),xh.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:cl([kp.config({mode:"acyclic"}),Fp("pop-dialog-wrap-events",[zr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>kp.focusIn(t)))})),Hr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=Gt((()=>QB(t,(e=>{const t=y([e]);wr(d,eM,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=of(e)===Vh.scrolling?Vh.scrolling:Vh.default,i=q(P(t,(t=>"contexttoolbar"===t.type?((t,o)=>EB(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n,B.some(["form:"])))(s,t):((e,t)=>UB(e,t))(t,r.providers))));return yE({type:a,uid:Kr("context-toolbar"),initGroups:i,onEscape:B.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(w.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:Zl(12,0,qB),layouts:{onLtr:()=>[Yi],onRtl:()=>[Ji]},overrides:XB}:{bubble:Zl(0,12,qB,1/12),layouts:JB(e,o,n,t),overrides:XB})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>id.getMode(o)});return sn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;At(b,"display"),(e=>xe(Se(e,i.get(),Ge),!0))(n)||(Ea(b,sM),id.reset(o,d)),xh.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[sM],mode:"placement"}},(()=>B.some(u()))),n.fold(i.clear,i.set),g()&&kt(b,"display","none")},w=bC((()=>{e.hasFocus()&&!e.removed&&(Ba(d.element,sM)?w.throttle():((e,t)=>{const o=Ie(t.getBody()),n=e=>Ge(e,o),s=Ie(t.selection.getNode());return(e=>!n(e)&&!$e(o,e))(s)?B.none():((e,t,o)=>{const n=ZB(e,t);if(n.contextForms.length>0)return B.some({elem:e,toolbars:[n.contextForms[0]]});{const t=ZB(e,o);if(t.contextForms.length>0)return B.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>V(e,(e=>e.position===t)),o=t=>W(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=P(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return B.some({elem:e,toolbars:o})}return B.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?B.none():ws(t,(e=>{if(He(e)){const{contextToolbars:t,contextForms:n}=ZB(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>W(e,(e=>e.position===t))))}})(t);return s.length>0?B.some({elem:e,toolbars:s}):B.none()}return B.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,B.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",w.throttle),e.on(LB,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],ke(t.target!==e,t.target)),xh.getContent(d).each(kp.focusIn)}))})),e.on("focusout",(t=>{wh.setEditorTimeout(e,(()=>{xl(o.element).isNone()&&xl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&w.throttle()})),e.on("NodeChange",(e=>{xl(d.element).fold(w.throttle,b)}))}))},aM={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},iM=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),lM=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Hl();return P(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},cM={name:"lineheight",text:"Line height",icon:"line-height",getOptions:Ef,hash:e=>((e,t)=>((e,t)=>B.from(iM.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>V(t,(t=>V(aM[t],(t=>e===t)))))(n,t)?B.some({value:o,unit:n}):B.none()})))(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:x,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>B.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t)},dM=e=>PE(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent"))})),uM=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),()=>e.off("PastePlainTextToggle",n)},mM=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},gM=e=>{(e=>{(e=>{Vk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:HE(e,t.name),onAction:mM(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:HE(e,o),onAction:mM(e,o)})}})(e),(e=>{Vk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"print",text:"Print",action:"mcePrint",icon:"print"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:LE(e,t.action)})}))})(e),(e=>{Vk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:LE(e,t.action),onSetup:HE(e,t.name)})}))})(e)})(e),(e=>{Vk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:LE(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:mM(e,"code")})})(e)},pM=(e,t)=>PE(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),hM=e=>PE(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),fM=(e,t)=>{(e=>{N([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:LE(e,t.cmd),onSetup:HE(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:LE(e,"JustifyNone")})})(e),gM(e),((e,t)=>{((e,t)=>{const o=UE(0,t,XE(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=UE(0,t,QE(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=UE(0,t,rB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=UE(0,t,YE(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=UE(0,t,sB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:pM(e,"hasUndo"),onAction:LE(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:pM(e,"hasRedo"),onAction:LE(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:pM(e,"hasUndo"),onAction:LE(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:pM(e,"hasRedo"),onAction:LE(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},null,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=fs(Py),o=fs(Py);Gy(e,"forecolor","forecolor","Text color",t),Gy(e,"backcolor","hilitecolor","Background color",o),$y(e,"forecolor","forecolor","Text color"),$y(e,"backcolor","hilitecolor","Background color")})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:LE(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:hM(e),onAction:LE(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:dM(e),onAction:LE(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:LE(e,"indent")})})(e)})(e),(e=>{lM(e,cM),(e=>B.from(ef(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:t.customCode}).unbind,getCurrent:e=>{const t=Ie(e.selection.getNode());return Ss(t,(e=>B.some(e).filter(He).bind((e=>vt(e,"lang").map((t=>({code:t,customCode:vt(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=zl();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),o.clear}}))))(e).each((t=>lM(e,t)))})(e),(e=>{const t=fs(kf(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:uM(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:uM(e,t)})})(e)},bM=e=>r(e)?e.split(/[ ,]/):e,vM=e=>t=>t.options.get(e),yM=vM("contextmenu_never_use_native"),xM=vM("contextmenu_avoid_overlap"),wM=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:W(o,(e=>ve(t,e)))},SM=(e,t)=>({type:"makeshift",x:e,y:t}),kM=e=>"longpress"===e.type||0===e.type.indexOf("touch"),CM=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(kM(e)){const t=e.touches[0];return SM(t.pageX,t.pageY)}return SM(e.pageX,e.pageY)})(t):((e,t)=>{const o=Hh.DOM.getPos(e);return((e,t,o)=>SM(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(kM(e)){const t=e.touches[0];return SM(t.clientX,t.clientY)}return SM(e.clientX,e.clientY)})(t)):OM(e),OM=e=>({type:"selection",root:Ie(e.selection.getNode())}),_M=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:B.some(Ie(e.selection.getNode())),root:Ie(e.getBody())}))(e);case"point":return CM(e,t);case"selection":return OM(e)}},TM=(e,t,o,n,s,r)=>{const a=o(),i=_M(e,t,r);oC(a,Uf.CLOSE_ON_EXECUTE,n,!1).map((e=>{t.preventDefault(),xh.showMenuAt(s,{anchor:i},{menu:{markers:nb("normal")},data:e})}))},EM={onLtr:()=>[Ki,ji,Gi,$i,qi,Xi,pO,hO,gO,uO,mO,dO],onRtl:()=>[Ki,Gi,ji,qi,$i,Xi,pO,hO,mO,dO,gO,uO]},BM={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},MM=(e,t,o,n,s,r)=>{const a=So(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=_M(e,t,o);return{bubble:Zl(0,"point"===o?12:0,BM),layouts:EM,overrides:{maxWidthFunction:KT(),maxHeightFunction:Xl()},...n}})(e,t,r);oC(o,Uf.CLOSE_ON_EXECUTE,n,!0).map((o=>{t.preventDefault(),xh.showMenuWithinBounds(s,{anchor:i},{menu:{markers:nb("normal"),highlightImmediately:a},data:o,type:"horizontal"},(()=>B.some($B(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(LB)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{wh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Rc(e.getWin(),Oc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},AM=e=>r(e)?"|"===e:"separator"===e.type,DM={type:"separator"},FM=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return DM;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:P(t,FM)}};default:return{type:"menuitem",...t(e),onAction:(o=e.onAction,()=>o())}}var o},IM=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!AM(e))).fold((()=>[]),(e=>[DM]));return e.concat(o).concat(t).concat([DM])},RM=(e,t)=>"longpress"!==t.type&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),VM=(e,t)=>RM(e,t)?e.selection.getStart(!0):t.target,zM=(e,t,o)=>{const n=So().deviceType.isTouch,s=Ga(xh.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:cl([Fp("dismissContextMenu",[Br(mr(),((t,o)=>{Dd.close(t),e.focus()}))])])})),a=e=>xh.hide(s),i=t=>{if(yM(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!yM(e))(e,t)||(e=>0===wM(e).length)(e))return;const a=((e,t)=>{const o=xM(e),n=RM(e,t)?"selection":"point";if(Ee(o)){const s=VM(e,t);return dx(Ie(s),o)?"node":n}return n})(e,t);(n()?MM:TM)(e,t,(()=>{const o=VM(e,t),n=e.ui.registry.getAll(),s=wM(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n))return IM(t,n.split(" "));if(n.length>0){const e=P(n,FM);return IM(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&AM(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},HM=bs([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),PM=e=>t=>t.translate(-e.left,-e.top),NM=e=>t=>t.translate(e.left,e.top),LM=e=>(t,o)=>j(e,((e,t)=>t(e)),Ht(t,o)),WM=(e,t,o)=>e.fold(LM([NM(o),PM(t)]),LM([PM(t)]),LM([])),UM=(e,t,o)=>e.fold(LM([NM(o)]),LM([]),LM([NM(t)])),jM=(e,t,o)=>e.fold(LM([]),LM([PM(o)]),LM([NM(t),PM(o)])),GM=(e,t,o)=>{const n=e.fold(((e,t)=>({position:B.some("absolute"),left:B.some(e+"px"),top:B.some(t+"px")})),((e,t)=>({position:B.some("absolute"),left:B.some(e-o.left+"px"),top:B.some(t-o.top+"px")})),((e,t)=>({position:B.some("fixed"),left:B.some(e+"px"),top:B.some(t+"px")})));return{right:B.none(),bottom:B.none(),...n}},$M=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(jM,qM),s(UM,XM),s(WM,KM))},qM=HM.offset,XM=HM.absolute,KM=HM.fixed,YM=(e,t)=>{const o=bt(e,t);return u(o)?NaN:parseInt(o,10)},JM=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=YM(o,t.leftAttr),s=YM(o,t.topAttr);return isNaN(n)||isNaN(s)?B.none():B.some(Ht(n,s))})(e,t).fold((()=>o),(e=>KM(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?QM(e,t,a,s,r):eA(e,t,a,s,r),l=WM(a,s,r);return((e,t,o)=>{const n=e.element;ht(n,t.leftAttr,o.left+"px"),ht(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:KM(l.left,l.top),extra:B.none()})),(e=>({coord:e.output,extra:e.extra})))},ZM=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=UM(e,s,r),i=UM(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?B.some({output:$M(e.output,t,o,n),extra:e.extra}):B.none()})),QM=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return ZM(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=UM(e,s,r),i=UM(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Ht(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:B.some(a),snap:B.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:B.some(a),snap:B.some(t)}:e))}),{deltas:B.none(),snap:B.none()});return e.snap.map((e=>({output:$M(e.output,o,n,s),extra:e.extra})))}))},eA=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return ZM(r,o,n,s)};var tA=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=qe(e.element),o=Fo(t),r=T_(s),a=((e,t,o)=>({coord:$M(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=GM(a.coord,0,r);Ot(s,i)}}});const oA="data-initial-z-index",nA=(e,t)=>{e.getSystem().addToGui(t),(e=>{Je(e.element).filter(He).each((t=>{Et(t,"z-index").each((e=>{ht(t,oA,e)})),kt(t,"z-index",_t(e.element,"z-index"))}))})(t)},sA=e=>{(e=>{Je(e.element).filter(He).each((e=>{vt(e,oA).fold((()=>At(e,"z-index")),(t=>kt(e,"z-index",t))),xt(e,oA)}))})(e),e.getSystem().removeFromGui(e)},rA=(e,t,o)=>e.getSystem().build(xx.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var aA=as("snaps",[jn("getSnapPoints"),bi("onSensor"),jn("leftAttr"),jn("topAttr"),is("lazyViewport",jo),is("mustSnap",!1)]);const iA=[is("useFixed",_),jn("blockerClass"),is("getTarget",x),is("onDrag",b),is("repositionTarget",!0),is("onDrop",b),gs("getBounds",jo),aA],lA=(e,t)=>({bounds:e.getBounds(),height:Vt(t.element),width:jt(t.element)}),cA=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>lA(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=qe(e.element),a=Fo(r),i=T_(s),l=(e=>{return(t=Et(e,"left"),o=Et(e,"top"),n=Et(e,"position"),s=(e,t,o)=>("fixed"===o?KM:qM)(parseInt(e,10),parseInt(t,10)),t.isSome()&&o.isSome()&&n.isSome()?B.some(s(t.getOrDie(),o.getOrDie(),n.getOrDie())):B.none()).getOrThunk((()=>{const t=Nt(e);return XM(t.left,t.top)}));var t,o,n,s})(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=UM(t,o,n),i=Ri(a.left,r.x,r.x+r.width-s.width),l=Ri(a.top,r.y,r.y+r.height-s.height),c=XM(i,l);return t.fold((()=>{const e=jM(c,o,n);return qM(e.left,e.top)}),y(c),(()=>{const e=WM(c,o,n);return KM(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>qM(e+a,t+i)),((e,t)=>XM(e+a,t+i)),((e,t)=>KM(e+a,t+i))));var t,a,i;const l=WM(e,n,s);return KM(l.left,l.top)}),(t=>{const a=JM(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=GM(c,0,i);Ot(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},dA=(e,t,o,n)=>{t.each(sA),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;xt(o,t.leftAttr),xt(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},uA=e=>(t,o)=>{const n=e=>{o.setStartData(lA(t,e))};return _r([Br(lr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var mA=Object.freeze({__proto__:null,getData:e=>B.from(Ht(e.x,e.y)),getDelta:(e,t)=>Ht(t.left-e.left,t.top-e.top)});const gA=(e,t,o)=>[Br(As(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>dA(n,B.some(l),e,t),a=ux(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),cA(n,e,t,mA,o)}},l=rA(n,e.blockerClass,(e=>_r([Br(As(),e.forceDrop),Br(Is(),e.drop),Br(Ds(),((t,o)=>{e.move(o.event)})),Br(Fs(),e.delayDrop)]))(i));o(n),nA(n,l)}))],pA=[...iA,wi("dragger",{handlers:uA(gA)})];var hA=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return B.some(Ht(t.clientX,t.clientY))})(t):B.none()},getDelta:(e,t)=>Ht(t.left-e.left,t.top-e.top)});const fA=(e,t,o)=>{const n=Hl(),s=o=>{dA(o,n.get(),e,t),n.clear()};return[Br(Ts(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{cA(r,e,t,hA,o)}},c=rA(r,e.blockerClass,(e=>_r([Br(Ts(),e.forceDrop),Br(Bs(),e.drop),Br(Ms(),e.drop),Br(Es(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),nA(r,c)})),Br(Es(),((o,n)=>{n.stop(),cA(o,e,t,hA,n.event)})),Br(Bs(),((e,t)=>{t.stop(),s(e)})),Br(Ms(),s)]},bA=pA,vA=[...iA,wi("dragger",{handlers:uA(fA)})],yA=[...iA,wi("dragger",{handlers:uA(((e,t,o)=>[...gA(e,t,o),...fA(e,t,o)]))})];var xA=Object.freeze({__proto__:null,mouse:bA,touch:vA,mouseOrTouch:yA}),wA=Object.freeze({__proto__:null,init:()=>{let e=B.none(),t=B.none();const o=y({});return ga({readState:o,reset:()=>{e=B.none(),t=B.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=B.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=B.some(e)}})}});const SA=gl({branchKey:"mode",branches:xA,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:B.from(e.extra)})},state:wA,apis:tA}),kA=(e,t,o,n,s,r)=>e.fold((()=>SA.snap({sensor:XM(o-20,n-20),range:Ht(s,r),output:XM(B.some(o),B.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return SA.snap({sensor:XM(s,r),range:Ht(40,40),output:XM(B.some(o-a.width/2),B.some(n-a.height/2)),extra:{td:t}})})),CA=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Ge(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),OA=e=>kh(Sh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:cl([SA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Ew.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),_A=(e,t)=>{const o=fs([]),n=fs([]),s=fs(!1),r=Hl(),a=Hl(),i=e=>{const o=Uo(e);return kA(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Uo(e);return kA(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=CA((()=>P(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=CA((()=>P(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=OA(c),m=OA(d),g=Ga(u.asSpec()),p=Ga(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);SA.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();At(t.element,"display");const i=Ye(Ie(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&kt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");So().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(pd(t,g),pd(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(bd(g),bd(p),s.set(!1)),r.clear(),a.clear()})))},TA=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:cl([kp.config({mode:"flow",selector:"div[role=button]"}),ym.config({disabled:o.isDisabled}),Iv(),Tx.config({}),Dp.config({}),Fp("elementPathEvents",[zr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>kp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=e.dispatch("ResolveName",{name:r.nodeName.toLowerCase(),target:r});if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Sh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[La(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:cl([Rv(o.isDisabled),Iv()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[La(` ${s} `)]},a])}),[]):[];Dp.set(t,a)}))}))])]),components:[]}};var EA;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(EA||(EA={}));const BA=(e,t,o)=>{const n=Ie(e.getContainer()),s=((e,t,o,n,s)=>{const r={};return r.height=RB(n+t.top,Xh(e),Yh(e)),o===EA.Both&&(r.width=RB(s+t.left,qh(e),Kh(e))),r})(e,t,o,Rt(n),Ut(n));le(s,((e,t)=>kt(n,t,IB(e)))),(e=>{e.dispatch("ResizeEditor")})(e)},MA=(e,t,o,n)=>{const s=Ht(20*o,20*n);return BA(e,s,t),B.some(!0)},AA=(e,t)=>({dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const o=(()=>{const o=[];return xf(e)&&o.push(TA(e,{},t)),e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Dp.set(e,[La(t.translate(["{0} "+n,o[n]]))]);return Sh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:cl([Rv(t.isDisabled),Iv(),Tx.config({}),Dp.config({}),Zd.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Fp("wordcount-events",[Nr((e=>{const t=Zd.getValue(e),n="words"===t.mode?"characters":"words";Zd.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),zr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=Zd.getValue(t);Zd.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[Qs()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),wf(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":Ch.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:cl([Hp.config({})])}]}),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:o}]:[]})(),n=((e,t)=>{const o=(e=>{const t=Sf(e);return!1===t?EA.None:"both"===t?EA.Both:EA.Vertical})(e);return o===EA.None?B.none():B.some(Fh("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},behaviours:[SA.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>BA(e,s,o),blockerClass:"tox-blocker"}),kp.config({mode:"special",onLeft:()=>MA(e,o,-1,0),onRight:()=>MA(e,o,1,0),onUp:()=>MA(e,o,0,-1),onDown:()=>MA(e,o,0,1)}),Tx.config({}),Hp.config({})]},t.icons))})(e,t);return o.concat(n.toArray())})()}),DA=e=>e.get().getOrDie("UI has not been rendered"),FA=e=>{const t=e.inline,o=t?NB:DB,n=zf(e)?Q_:O_,s=Hl(),r=Hl(),a=Hl(),i=Hl(),l=So().deviceType.isTouch()?["tox-platform-touch"]:[],c=Df(e),d=of(e),u=kh({dom:{tag:"div",classes:["tox-anchorbar"]}}),m=()=>r.get().bind(TE.getHeader),g=()=>qo.fromOption(s.get(),"UI has not been rendered"),p=()=>r.get().bind((e=>TE.getToolbar(e))).getOrDie("Could not find more toolbar element"),h=()=>r.get().bind((e=>TE.getThrobber(e))).getOrDie("Could not find throbber element"),f=((e,t,o)=>{const n=fs(!1),s=(e=>{const t=fs(Df(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),r={shared:{providers:{icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Ch.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},interpreter:e=>((e,t,o)=>oO(eO,e,{},o))(e,0,r),anchors:TO(t,o,s.isPositionedAtTop),header:s,getSink:e},urlinput:u_(t),styles:NO(t),colorinput:DO(t),dialog:IO(t),isContextMenuOpen:()=>n.get(),setContextMenuState:e=>n.set(e)};return r})(g,e,(()=>r.get().bind((e=>u.getOpt(e))).getOrDie("Could not find a anchor bar element"))),b=t=>{const o=IB((e=>{const t=(e=>{const t=Gh(e),o=Xh(e),n=Yh(e);return FB(t).map((e=>RB(e,o,n)))})(e);return t.getOr(Gh(e))})(e)),n=IB((e=>VB(e).getOr($h(e)))(e));return e.inline||(Mt("div","width",n)&&kt(t.element,"width",n),Mt("div","height",o)?kt(t.element,"height",o):kt(t.element,"height","400px")),o};return{getMothership:()=>DA(a),getUiMothership:()=>DA(i),backstage:f,renderUI:()=>{const{mothership:v,outerContainer:y}=(()=>{const o=(()=>{const t={attributes:{[sc]:c?nc.BottomToTop:nc.TopToBottom}},o=TE.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:f,onEscape:()=>{e.focus()}}),n=TE.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:g,providers:f.shared.providers,onEscape:()=>{e.focus()},type:d,lazyToolbar:p,lazyHeader:()=>m().getOrDie("Could not find header element"),...t}),s=TE.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:f.shared.providers,onEscape:()=>{e.focus()},type:d}),r=Af(e),a=Bf(e),i=_f(e);return TE.parts.header({dom:{tag:"div",classes:["tox-editor-header"],...t},components:q([i?[o]:[],r?[s]:a?[n]:[],If(e)?[]:[u.asSpec()]]),sticky:zf(e),editor:e,sharedBackstage:f.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[TE.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),TE.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=TE.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:f}),i=yf(e)&&!t?B.some(AA(e,f.shared.providers)):B.none(),h=q([c?[]:[o],t?[]:[n],c?[o]:[]]),b=q([[{dom:{tag:"div",classes:["tox-editor-container"]},components:h}],t?[]:i.toArray(),[s]]),v=Vf(e),y={role:"application",...Ch.isRtl()?{dir:"rtl"}:{},...v?{"aria-hidden":"true"}:{}},x=Ga(TE.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(c?["tox-tinymce--toolbar-bottom"]:[]).concat(l),styles:{visibility:"hidden",...v?{opacity:"0",border:"0"}:{}},attributes:y},components:b,behaviours:cl([Iv(),ym.config({disableClass:"tox-tinymce--disabled"}),kp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=wx(x);return r.set(x),a.set(w),{mothership:w,outerContainer:x}})(),{uiMothership:x,sink:w}=(()=>{const t=Rf(e),o=Ge(mt(),t)&&"grid"===_t(t,"display"),r={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(l),attributes:{...Ch.isRtl()?{dir:"rtl"}:{}}},behaviours:cl([id.config({useFixed:()=>n.isDocked(m)})])},a={dom:{styles:{width:document.body.clientWidth+"px"}},events:_r([Br(cr(),(e=>{kt(e.element,"width",document.body.clientWidth+"px")}))])},c=Ga(sn(r,o?a:{})),d=wx(c);return s.set(c),i.set(d),{sink:c,uiMothership:d}})();ce(nf(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:S,menuItems:k,contextToolbars:C,sidebars:O}=e.ui.registry.getAll(),_=Mf(e),T={menuItems:k,menus:Hf(e),menubar:df(e),toolbar:_.getOrThunk((()=>uf(e))),allowToolbarGroups:d===Vh.floating,buttons:S,sidebar:O};(t=>{e.addShortcut("alt+F9","focus menubar",(()=>{TE.focusMenubar(t)})),e.addShortcut("alt+F10","focus toolbar",(()=>{TE.focusToolbar(t)})),e.addCommand("ToggleToolbarDrawer",(()=>{TE.toggleToolbarDrawer(t)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>TE.isToolbarDrawerToggled(t)))})(y),((e,t,o)=>{const n=(e,n)=>{N([t,o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{N([t,o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Fd(),{target:e.target}),a=zo(),i=Nl(a,"touchstart",r),l=Nl(a,"touchmove",(e=>n(ar(),e))),c=Nl(a,"touchend",(e=>n(ir(),e))),d=Nl(a,"mousedown",r),u=Nl(a,"mouseup",(e=>{0===e.raw.button&&s(Rd(),{target:e.target})})),m=e=>s(Fd(),{target:Ie(e.target)}),g=e=>{0===e.button&&s(Rd(),{target:Ie(e.target)})},p=()=>{N(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(lr(),Wl(e)),f=e=>{s(Id(),{}),n(cr(),Wl(e))},b=()=>s(Id(),{}),v=t=>{t.state&&s(Fd(),{target:Ie(e.getContainer())})},y=e=>{s(Fd(),{target:Ie(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",b),e.on("AfterProgressState",v),e.on("DismissPopups",y)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",b),e.off("AfterProgressState",v),e.off("DismissPopups",y),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind()})),e.on("detach",(()=>{wd(t),wd(o),t.destroy(),o.destroy()}))})(e,v,x),n.setup(e,f.shared,m),fM(e,f),zM(e,g,f),(e=>{const{sidebars:t}=e.ui.registry.getAll();N(ae(t),(o=>{const n=t[o],s=()=>xe(B.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),NT(e,h,f.shared),rM(e,C,w,{backstage:f}),_A(e,w);const E={mothership:v,uiMothership:x,outerContainer:y,sink:w},M={targetNode:e.getElement(),height:b(y)};return o.render(e,E,T,f,M)}}},IA=y([jn("lazySink"),Qn("dragBlockClass"),gs("getBounds",jo),is("useTabstopAt",T),is("eventOrder",{}),Qd("modalBehaviours",[kp]),vi("onExecute"),xi("onEscape")]),RA={sketch:x},VA=y([Eu({name:"draghandle",overrides:(e,t)=>({behaviours:cl([SA.config({mode:"mouse",getTarget:e=>Za(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),_u({schema:[jn("dom")],name:"title"}),_u({factory:RA,schema:[jn("dom")],name:"close"}),_u({factory:RA,schema:[jn("dom")],name:"body"}),Eu({factory:RA,schema:[jn("dom")],name:"footer"}),Tu({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[is("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),is("components",[])],name:"blocker"})]),zA=tm({name:"ModalDialog",configFields:IA(),partFields:VA(),factory:(e,t,o,n)=>{const s=Hl(),r=Kr("modal-events"),a={...e.eventOrder,[dr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([$a(t)]),behaviours:cl([Hp.config({}),Fp("dialog-blocker-events",[Vr(Vs(),(()=>{kp.focusIn(t)}))])])});pd(o,a),kp.focusIn(t)},hide:e=>{s.clear(),Je(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{bd(e)}))}))},getBody:t=>Lu(t,e,"body"),getFooter:t=>Lu(t,e,"footer"),setIdle:e=>{zT.unblock(e)},setBusy:(e,t)=>{zT.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:tu(e.modalBehaviours,[Dp.config({}),kp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt}),zT.config({getRoot:s.get}),Fp(r,[zr((t=>{((e,t)=>{const o=vt(e,"id").fold((()=>{const e=Kr("dialog-label");return ht(t,"id",e),e}),x);ht(e,"aria-labelledby",o)})(t.element,Lu(t,e,"title").element),((e,t)=>{const o=B.from(bt(e,"id")).fold((()=>{const e=Kr("dialog-describe");return ht(t,"id",e),e}),x);ht(e,"aria-describedby",o)})(t.element,Lu(t,e,"body").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),HA=xn([pb,hb].concat(dv)),PA=Bn,NA=[Lb("button"),Eb,us("align","end",["start","end"]),Vb,Rb,ns("buttonType",["primary","secondary"])],LA=[...NA,bb],WA=[Xn("type",["submit","cancel","custom"]),...LA],UA=[Xn("type",["menu"]),Tb,Bb,Eb,Zn("items",HA),...NA],jA=Nn("type",{submit:WA,cancel:WA,custom:WA,menu:UA}),GA=[pb,bb,Xn("level",["info","warn","error","success"]),yb,is("url","")],$A=xn(GA),qA=[pb,bb,Rb,Lb("button"),Eb,Ib,ns("buttonType",["primary","secondary","toolbar"]),Vb],XA=xn(qA),KA=[pb,hb],YA=KA.concat([Mb]),JA=KA.concat([fb,Rb]),ZA=xn(JA),QA=Bn,eD=YA.concat([zb("auto")]),tD=xn(eD),oD=Cn([xb,bb,yb]),nD=xn(YA),sD=En,rD=xn(YA),aD=En,iD=KA.concat([ds("tag","textarea"),qn("scriptId"),qn("scriptUrl"),ls("settings",void 0,Dn)]),lD=KA.concat([ds("tag","textarea"),Kn("init")]),cD=In((e=>Vn("customeditor.old",yn(lD),e).orThunk((()=>Vn("customeditor.new",yn(iD),e))))),dD=En,uD=xn(YA),mD=wn(pn),gD=e=>[pb,$n("columns"),e],pD=[pb,qn("html"),us("presets","presentation",["presentation","document"])],hD=xn(pD),fD=YA.concat([ms("sandboxed",!0),ms("transparent",!0)]),bD=xn(fD),vD=En,yD=xn(KA.concat([os("height")])),xD=xn([qn("url"),ts("zoom"),ts("cachedWidth"),ts("cachedHeight")]),wD=YA.concat([os("inputMode"),os("placeholder"),ms("maximized",!1),Rb]),SD=xn(wD),kD=En,CD=e=>[pb,fb,e],OD=[bb,xb],_D=[bb,Zn("items",((e,t)=>{const o=Gt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,(()=>TD)))],TD=Sn([xn(OD),xn(_D)]),ED=YA.concat([Zn("items",TD),Rb]),BD=xn(ED),MD=En,AD=YA.concat([Jn("items",[bb,xb]),cs("size",1),Rb]),DD=xn(AD),FD=En,ID=YA.concat([ms("constrain",!0),Rb]),RD=xn(ID),VD=xn([qn("width"),qn("height")]),zD=KA.concat([fb,cs("min",0),cs("max",0)]),HD=xn(zD),PD=Tn,ND=[pb,Zn("header",En),Zn("cells",wn(En))],LD=xn(ND),WD=YA.concat([os("placeholder"),ms("maximized",!1),Rb]),UD=xn(WD),jD=En,GD=YA.concat([us("filetype","file",["image","media","file"]),Rb]),$D=xn(GD),qD=xn([xb,Hb]),XD=e=>Ln("items","items",{tag:"required",process:{}},wn(In((t=>Vn(`Checking item of ${e}`,KD,t).fold((e=>qo.error(Pn(e))),(e=>qo.value(e))))))),KD=bn((()=>{return Fn("type",{alertbanner:$A,bar:xn((e=XD("bar"),[pb,e])),button:XA,checkbox:ZA,colorinput:nD,colorpicker:rD,dropzone:uD,grid:xn(gD(XD("grid"))),iframe:bD,input:SD,listbox:BD,selectbox:DD,sizeinput:RD,slider:HD,textarea:UD,urlinput:$D,customeditor:cD,htmlpanel:hD,imagepreview:yD,collection:tD,label:xn(CD(XD("label"))),table:LD,panel:JD});var e})),YD=[pb,is("classes",[]),Zn("items",KD)],JD=xn(YD),ZD=[Lb("tab"),vb,Zn("items",KD)],QD=[pb,Jn("tabs",ZD)],eF=xn(QD),tF=LA,oF=jA,nF=xn([qn("title"),Gn("body",Fn("type",{panel:JD,tabpanel:eF})),ds("size","normal"),Zn("buttons",oF),is("initialData",{}),gs("onAction",b),gs("onChange",b),gs("onSubmit",b),gs("onClose",b),gs("onCancel",b),gs("onTabChange",b)]),sF=xn([Xn("type",["cancel","custom"]),...tF]),rF=xn([qn("title"),qn("url"),ts("height"),ts("width"),rs("buttons",sF),gs("onAction",b),gs("onCancel",b),gs("onClose",b),gs("onMessage",b)]),aF=e=>a(e)?[e].concat(X(fe(e),aF)):l(e)?X(e,aF):[],iF=e=>r(e.type)&&r(e.name),lF={checkbox:QA,colorinput:sD,colorpicker:aD,dropzone:mD,input:kD,iframe:vD,imagepreview:xD,selectbox:FD,sizeinput:VD,slider:PD,listbox:MD,size:VD,textarea:jD,urlinput:qD,customeditor:dD,collection:oD,togglemenuitem:PA},cF=e=>{const t=(e=>W(aF(e),iF))(e),o=X(t,(e=>(e=>B.from(lF[e.type]))(e).fold((()=>[]),(t=>[Gn(e.name,t)]))));return xn(o)},dF=e=>({internalDialog:zn(Vn("dialog",nF,e)),dataValidator:cF(e),initialData:e.initialData}),uF={open:(e,t)=>{const o=dF(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(zn(Vn("dialog",rF,t))),redial:e=>dF(e)},mF=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?qo.error(t):qo.value(o)},gF=(e,t,o)=>{const n=kh(vk.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:P(e.items,(e=>tO(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:cl([kp.config({mode:"acyclic",useTabstopAt:k(Uk)}),(s=n,rm.config({find:s.getOpt})),Mk(n,{postprocess:e=>mF(e).fold((e=>(console.error(e),{})),x)})])};var s},pF=em({name:"TabButton",configFields:[is("uid",void 0),jn("value"),Ln("dom","dom",cn((()=>({attributes:{role:"tab",id:Kr("aria"),"aria-selected":"false"}}))),On()),Qn("action"),is("domModification",{}),Qd("tabButtonBehaviours",[Hp,kp,Zd]),jn("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:Xp(e.action),behaviours:tu(e.tabButtonBehaviours,[Hp.config({}),kp.config({mode:"execution",useSpace:!0,useEnter:!0}),Zd.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),hF=y([jn("tabs"),jn("dom"),is("clickToDismiss",!1),Qd("tabbarBehaviours",[Bm,kp]),hi(["tabClass","selectedClass"])]),fF=Bu({factory:pF,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Bm.dehighlight(e,t),wr(e,br(),{tabbar:e,button:t})},o=(e,t)=>{Bm.highlight(e,t),wr(e,fr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Bm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),bF=y([fF]),vF=tm({name:"Tabbar",configFields:hF(),partFields:bF(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:tu(e.tabbarBehaviours,[Bm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{ht(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{ht(t.element,"aria-selected","false")}}),kp.config({mode:"flow",getInitial:e=>Bm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),yF=em({name:"Tabview",configFields:[Qd("tabviewBehaviours",[Dp])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:tu(e.tabviewBehaviours,[Dp.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),xF=y([is("selectFirst",!0),bi("onChangeTab"),bi("onDismissTab"),is("tabs",[]),Qd("tabSectionBehaviours",[])]),wF=_u({factory:vF,schema:[jn("dom"),Yn("markers",[jn("tabClass"),jn("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),SF=_u({factory:yF,name:"tabview"}),kF=y([wF,SF]),CF=tm({name:"TabSection",configFields:xF(),partFields:kF(),factory:(e,t,o,n)=>{const s=(t,o)=>{Nu(t,e,"tabbar").each((e=>{o(e).each(Sr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.tabSectionBehaviours),events:_r(q([e.selectFirst?[zr(((e,t)=>{s(e,Bm.getFirst)}))]:[],[Br(fr(),((t,o)=>{(t=>{const o=Zd.getValue(t);Nu(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();vt(t.element,"id").each((e=>{ht(n.element,"aria-labelledby",e)})),Dp.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Br(br(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>Nu(t,e,"tabview").map((e=>Dp.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Bm.getCandidates(e);return G(o,(e=>Zd.getValue(e)===t)).filter((t=>!Bm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),OF=(e,t)=>{kt(e,"height",t+"px"),kt(e,"flex-basis",t+"px")},_F=(e,t,o)=>{Za(e,'[role="dialog"]').each((e=>{ei(e,'[role="tablist"]').each((n=>{o.get().map((o=>(kt(t,"height","0"),kt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=Ke(e).dom,s=Za(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===_t(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Rt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Ut(o)?Math.max(Rt(o),a):a,l=parseInt(_t(e,"margin-top"),10)||0,c=parseInt(_t(e,"margin-bottom"),10)||0;return r-(Rt(e)+l+c-i)})(e,t,n))))).each((e=>{OF(t,e)}))}))}))},TF=e=>ei(e,'[role="tabpanel"]'),EF="send-data-to-section",BF="send-data-to-view",MF=(e,t,o)=>{const n=fs({}),s=e=>{const t=Zd.getValue(e),o=mF(t).getOr({}),s=n.get(),r=sn(s,o);n.set(r)},r=e=>{const t=n.get();Zd.setValue(e,t)},a=fs(null),i=P(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[La(o.shared.providers.translate(e.title))],view:()=>[vk.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:P(e.items,(e=>tO(n,e,t,o))),formBehaviours:cl([kp.config({mode:"acyclic",useTabstopAt:k(Uk)}),Fp("TabView.form.events",[zr(r),Hr(s)]),hl.config({channels:ys([{key:EF,value:{onReceive:s}},{key:BF,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=(()=>{const t=Hl(),o=[zr((o=>{const n=o.element;TF(n).each((s=>{kt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>P(e,((n,s)=>{Dp.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Dp.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),_F(n,s,t),At(s,"visibility"),((e,t)=>{oe(e).each((e=>CF.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{_F(n,s,t)}))}))})),Br(cr(),(e=>{const o=e.element;TF(o).each((e=>{_F(o,e,t)}))})),Br(Lx,((e,o)=>{const n=e.element;TF(n).each((e=>{const o=yl(it(e));kt(e,"visibility","hidden");const s=Et(e,"height").map((e=>parseInt(e,10)));At(e,"height"),At(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),_F(n,e,t)):s.each((t=>{OF(e,t)})),At(e,"visibility"),o.each(bl)}))}))];return{extraEvents:o,selectFirst:!1}})();return{smartTabHeight:t,naiveTabHeight:{extraEvents:[],selectFirst:!0}}})(i).smartTabHeight;return CF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=Zd.getValue(t);wr(e,Nx,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[CF.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[vF.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:cl([Tx.config({})])}),CF.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:cl([Fp("tabpanel",l.extraEvents),kp.config({mode:"acyclic"}),rm.config({find:e=>oe(CF.getViewItems(e))}),Dk(B.none(),(e=>(e.getSystem().broadcastOn([EF],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([BF],{})}))])})},AF=Kr("update-dialog"),DF=Kr("update-title"),FF=Kr("update-body"),IF=Kr("update-footer"),RF=Kr("body-send-message"),VF=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:cl([_k(0),cB.config({channel:`${FF}-${t}`,updateState:(e,t)=>B.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[MF(t,e.initialData,n)]:[gF(t,e.initialData,n)]},initialData:e})])}),zF=Nh.deviceType.isTouch(),HF=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),PF=(e,t)=>zA.parts.close(Sh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:cl([Tx.config({})])})),NF=()=>zA.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),LF=(e,t)=>zA.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:HT(`<p>${t.translate(e)}</p>`)}]}]}),WF=e=>zA.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),UF=(e,t)=>[xx.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),xx.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],jF=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return zA.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),B.some(!0)),useTabstopAt:e=>!Uk(e),dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:HT(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:zF?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:cl([Hp.config({}),Fp("dialog-events",e.dialogEvents.concat([Vr(Vs(),((e,t)=>{kp.focusIn(e)}))])),Fp("scroll-lock",[zr((()=>{Ta(mt(),s)})),Hr((()=>{Ea(mt(),s)}))]),...e.extraBehaviours]),eventOrder:{[Qs()]:["dialog-events"],[dr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[ur()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},GF=e=>Sh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},components:[Fh("close",{tag:"div",classes:["tox-icon"]},e.icons)],action:e=>{xr(e,Rx)}}),$F=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:cl([cB.config({channel:`${DF}-${t}`,initialData:e,renderComponents:e=>[La(n.translate(e.title))]})])}),qF=()=>({dom:HT('<div class="tox-dialog__draghandle"></div>')}),XF=(e,t,o)=>((e,t,o)=>{const n=zA.parts.title($F(e,t,B.none(),o)),s=zA.parts.draghandle(qF()),r=zA.parts.close(GF(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return xx.sketch({dom:HT('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),KF=(e,t,o)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:HT('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),YF=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{zA.setBusy(e(),((e,n)=>KF(o.message,n,t)))},onUnblock:()=>{zA.setIdle(e())}}),JF=(e,t,o,n)=>Ga(jF({...e,lazySink:n.shared.getSink,extraBehaviours:[cB.config({channel:`${AF}-${e.id}`,updateState:(e,t)=>B.some(t),initialData:t}),Fk({}),...e.extraBehaviours],onEscape:e=>{xr(e,Rx)},dialogEvents:o,eventOrder:{[Zs()]:[cB.name(),hl.name()],[dr()]:["scroll-lock",cB.name(),"messages","dialog-events","alloy.base.behaviour"],[ur()]:["alloy.base.behaviour","dialog-events","messages",cB.name(),"scroll-lock"]}})),ZF=e=>P(e,(e=>"menu"===e.type?(e=>{const t=P(e.items,(e=>({...e,storage:fs(!1)})));return{...e,items:t}})(e):e)),QF=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),eI=(e,t)=>[Fr(Vs(),Wk),e(Ix,((e,o)=>{t.onClose(),o.onClose()})),e(Rx,((e,t,o,n)=>{t.onCancel(e),xr(n,Ix)})),Br(Px,((e,o)=>t.onUnblock())),Br(Hx,((e,o)=>t.onBlock(o.event)))],tI=(e,t,o)=>{const n=(t,o)=>Br(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{cB.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...eI(n,t),n(zx,((e,t)=>t.onSubmit(e))),n(Fx,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Vx,((e,t,n,s)=>{const r=()=>kp.focusIn(s),a=e=>yt(e,"disabled")||vt(e,"aria-disabled").exists((e=>"true"===e)),i=it(s.element),l=yl(i);t.onAction(e,{name:n.name,value:n.value}),yl(i).fold(r,(e=>{a(e)||l.exists((t=>$e(e,t)&&a(t)))?r():o().toOptional().filter((t=>!$e(t.element,e))).each(r)}))})),n(Nx,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Hr((t=>{const o=e();Zd.setValue(t,o.getData())}))]},oI=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=L(o,(e=>"start"===e.align)),s=(e,t)=>xx.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:P(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},nI=(e,t,o)=>({dom:HT('<div class="tox-dialog__footer"></div>'),components:[],behaviours:cl([cB.config({channel:`${IF}-${t}`,initialData:e,updateState:(e,t)=>{const n=P(t.buttons,(e=>{const t=kh(((e,t)=>PC(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return B.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:oI})])}),sI=(e,t,o)=>zA.parts.footer(nI(e,t,o)),rI=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=rm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return vk.getField(o,t).orThunk((()=>{const o=e.getFooter();return cB.getState(o).get().bind((e=>e.lookupByName(t)))}))}return B.none()},aI=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...Zd.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=sn(r,t),i=((e,t)=>{const o=e.getRoot();return cB.getState(o).get().map((e=>zn(Vn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();Zd.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{rI(e,t).each(o?ym.enable:ym.disable)},focus:t=>{rI(e,t).each(Hp.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{wr(t,Hx,{message:e})}))},unblock:()=>{n((e=>{xr(e,Px)}))},showTab:t=>{n((o=>{const n=e.getBody();cB.getState(n).get().exists((e=>e.isTabPanel()))&&rm.getCurrent(n).each((e=>{CF.showTab(e,t)}))}))},redial:o=>{n((n=>{const r=e.getId(),a=t(o);n.getSystem().broadcastOn([`${AF}-${r}`],a),n.getSystem().broadcastOn([`${DF}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${FF}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${IF}-${r}`],a.internalDialog),s.setData(a.initialData)}))},close:()=>{n((e=>{xr(e,Ix)}))}};return s};var iI=tinymce.util.Tools.resolve("tinymce.util.URI");const lI=["insertContent","setContent","execCommand","close","block","unblock"],cI=e=>a(e)&&-1!==lI.indexOf(e.mceAction),dI=(e,t,o,n)=>{const s=Kr("dialog"),i=XF(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[Nk({dom:{tag:"iframe",attributes:{src:e.url}},behaviours:cl([Tx.config({}),Hp.config({})])})]}],behaviours:cl([kp.config({mode:"acyclic",useTabstopAt:k(Uk)})])};return zA.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?B.none():B.some(sI({buttons:e},s,n)))),u=((e,t)=>{const o=(t,o)=>Br(t,((t,s)=>{n(t,((n,r)=>{o(e(),n,s.event,t)}))})),n=(e,t)=>{cB.getState(e).get().each((o=>{t(o,e)}))};return[...eI(o,t),o(Vx,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})((()=>y),YF((()=>v),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},g=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],p=new iI(e.url,{base_uri:new iI(window.location.href)}),h=`${p.protocol}://${p.host}${p.port?":"+p.port:""}`,f=zl(),b=[Fp("messages",[zr((()=>{const t=Nl(Ie(window),"message",(t=>{if(p.isSameOrigin(new iI(t.raw.origin))){const n=t.raw.data;cI(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,y,n):(e=>!cI(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(y,n)}}));f.set(t)})),Hr(f.clear)]),hl.config({channels:{[RF]:{onReceive:(e,t)=>{ei(e.element,"iframe").each((e=>{e.dom.contentWindow.postMessage(t,h)}))}}}})],v=JF({id:s,header:i,body:l,footer:c,extraClasses:g,extraBehaviours:b,extraStyles:m},e,u,n),y=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{wr(t,Hx,{message:e})}))},unblock:()=>{t((e=>{xr(e,Px)}))},close:()=>{t((e=>{xr(e,Ix)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([RF],e)}))}}})(v);return{dialog:v,instanceApi:y}},uI=(e,t,o)=>t&&o?[]:[U_.config({contextual:{lazyContext:()=>B.some(Wo(Ie(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})],mI=e=>{const t=e.backstage,o=e.editor,n=zf(o),s=(e=>{const t=e.backstage.shared;return{open:(o,n)=>{const s=()=>{zA.hide(l),n()},r=kh(PC({name:"close-alert",text:"OK",primary:!0,buttonType:B.some("primary"),align:"end",enabled:!0,icon:B.none()},"cancel",e.backstage)),a=NF(),i=PF(s,t.providers),l=Ga(jF({lazySink:()=>t.getSink(),header:HF(a,i),body:LF(o,t.providers),footer:B.some(WF(UF([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Br(Rx,s)],eventOrder:{}}));zA.show(l);const c=r.get(l);Hp.focus(c)}}})(e),r=(e=>{const t=e.backstage.shared;return{open:(o,n)=>{const s=e=>{zA.hide(c),n(e)},r=kh(PC({name:"yes",text:"Yes",primary:!0,buttonType:B.some("primary"),align:"end",enabled:!0,icon:B.none()},"submit",e.backstage)),a=PC({name:"no",text:"No",primary:!1,buttonType:B.some("secondary"),align:"end",enabled:!0,icon:B.none()},"cancel",e.backstage),i=NF(),l=PF((()=>s(!1)),t.providers),c=Ga(jF({lazySink:()=>t.getSink(),header:HF(i,l),body:LF(o,t.providers),footer:B.some(WF(UF([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Br(Rx,(()=>s(!1))),Br(zx,(()=>s(!0)))],eventOrder:{}}));zA.show(c);const d=r.get(c);Hp.focus(d)}}})(e),a=(e,o)=>uF.open(((e,n,s)=>{const r=n,a=((e,t,o)=>{const n=Kr("dialog"),s=e.internalDialog,r=XF(s.title,n,o),a=((e,t,o)=>{const n=VF(e,t,B.none(),o,!1);return zA.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),i=ZF(s.buttons),l=QF(i),c=sI({buttons:i},n,o),d=tI((()=>h),YF((()=>g),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(s.size),m={id:n,header:r,body:a,footer:B.some(c),extraClasses:u,extraBehaviours:[],extraStyles:{}},g=JF(m,e,d,o),p={getId:y(n),getRoot:y(g),getBody:()=>zA.getBody(g),getFooter:()=>zA.getFooter(g),getFormWrapper:()=>{const e=zA.getBody(g);return rm.getCurrent(e).getOr(e)}},h=aI(p,t.redial,l);return{dialog:g,instanceApi:h}})({dataValidator:s,initialData:r,internalDialog:e},{redial:uF.redial,closeWindow:()=>{zA.hide(a.dialog),o(a.instanceApi)}},t);return zA.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),e),i=(e,s,r,a)=>uF.open(((e,i,l)=>{const c=zn(Vn("data",l,i)),d=Hl(),u=t.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{xh.reposition(e),U_.refresh(e)})),g=((e,t,o,n)=>{const s=Kr("dialog"),r=Kr("dialog-label"),a=Kr("dialog-content"),i=e.internalDialog,l=kh(((e,t,o,n)=>xx.sketch({dom:HT('<div class="tox-dialog__header"></div>'),components:[$F(e,t,B.some(o),n),qF(),GF(n)],containerBehaviours:cl([SA.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>ti(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},s,r,o.shared.providers)),c=kh(((e,t,o,n,s)=>VF(e,t,B.some(o),n,s))({body:i.body,initialData:i.initialData},s,a,o,n)),d=ZF(i.buttons),u=QF(d),m=kh(((e,t,o)=>nI(e,t,o))({buttons:d},s,o)),g=tI((()=>h),{onBlock:e=>{zT.block(p,((t,n)=>KF(e.message,n,o.shared.providers)))},onUnblock:()=>{zT.unblock(p)},onClose:()=>t.closeWindow()},o.shared.getSink),p=Ga({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:{role:"dialog","aria-labelledby":r,"aria-describedby":a}},eventOrder:{[Zs()]:[cB.name(),hl.name()],[Qs()]:["execute-on-form"],[dr()]:["reflecting","execute-on-form"]},behaviours:cl([kp.config({mode:"cyclic",onEscape:e=>(xr(e,Ix),B.some(!0)),useTabstopAt:e=>!Uk(e)&&("button"!==Ve(e)||"disabled"!==bt(e,"disabled"))}),cB.config({channel:`${AF}-${s}`,updateState:(e,t)=>B.some(t),initialData:e}),Hp.config({}),Fp("execute-on-form",g.concat([Vr(Vs(),((e,t)=>{kp.focusIn(e)}))])),zT.config({getRoot:()=>B.some(p)}),Dp.config({}),Fk({})]),components:[l.asSpec(),c.asSpec(),m.asSpec()]}),h=aI({getId:y(s),getRoot:y(p),getFooter:()=>m.get(p),getBody:()=>c.get(p),getFormWrapper:()=>{const e=c.get(p);return rm.getCurrent(e).getOr(e)}},t.redial,u);return{dialog:p,instanceApi:h}})({dataValidator:l,initialData:c,internalDialog:e},{redial:uF.redial,closeWindow:()=>{d.on(xh.hide),o.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},t,a),p=Ga(xh.sketch({lazySink:t.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:cl([Fp("window-manager-inline-events",[Br(mr(),((e,t)=>{xr(g.dialog,Rx)}))]),...uI(o,n,u)]),isExtraPart:(e,t)=>(e=>dx(e,".tox-alert-dialog")||dx(e,".tox-confirm-dialog"))(t)}));return d.set(p),xh.showWithin(p,$a(g.dialog),{anchor:s},B.some(mt())),n&&u||(U_.refresh(p),o.on("ResizeEditor",m)),g.instanceApi.setData(c),kp.focusIn(g.dialog),g.instanceApi}),e);return{open:(e,o,n)=>void 0!==o&&"toolbar"===o.inline?i(e,t.shared.anchors.inlineDialog(),n,o.ariaAttrs):void 0!==o&&"cursor"===o.inline?i(e,t.shared.anchors.cursor(),n,o.ariaAttrs):a(e,n),openUrl:(e,n)=>((e,n)=>uF.openUrl((e=>{const s=dI(e,{closeWindow:()=>{zA.hide(s.dialog),n(s.instanceApi)}},o,t);return zA.show(s.dialog),s.instanceApi}),e))(e,n),alert:(e,t)=>{s.open(e,(()=>{t()}))},close:e=>{e.close()},confirm:(e,t)=>{r.open(e,(e=>{t(e)}))}}};E.add("silver",(e=>{(e=>{Uh(e),(e=>{const t=e.options.register;var o;t("color_map",{processor:e=>f(e,r)?{value:Fy(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_cols",{processor:"number",default:(o=zy(e).length,Math.max(5,Math.ceil(Math.sqrt(o))))}),t("custom_colors",{processor:"boolean",default:!0})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:bM(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);const{getUiMothership:t,backstage:o,renderUI:n}=FA(e);cx(e,o.shared);const s=mI({editor:e,backstage:o});return{renderUI:n,getWindowManagerImpl:y(s),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Wo(Ie(e.getContentAreaContainer())),o=jo(),n=Ri(o.x,t.x,t.right),s=Ri(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return B.some(Lo(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),xh.hide(l)},i=Ga(Rh.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:B.from(t.icon),closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=Ga(xh.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),t.timeout>0&&wh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=$a(i),o={maxHeightFunction:Xl()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};xh.showWithinBounds(l,t,{anchor:e},s)}else I(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:mt(),node:B.some(Ie(n)),overrides:o,layouts:{onRtl:()=>[Ki],onLtr:()=>[Ki]}};xh.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{Rh.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{Rh.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:o},t())}}))}();