const VirtualItem={
    name: 'VirtualItem',
    props: {
        curId: {
            type: String,
            default: ""
        },
        source: {
            type: Object,
            default () {
                return {}
            }
        },
    },
    data(){
        return {
            parent:null,
            timer:null,
            hoverKey:null
        }
    },
    mounted(){
        this.parent=this.getParent()
    },
    methods: {
        getParent(){
            let parent = this.$parent || this.$root;
            let name = parent.$options.name;
            while (parent && (!name || name !== 'App')) {
                parent = parent.$parent;
                if (parent) {
                    name = parent.$options.name;
                }
            }
            return parent
        },
        dispatch(componentName, eventName, ...rest) {
            let parent = this.$parent || this.$root;
            let name = parent.$options.name;
            while (parent && (!name || name !== componentName)) {
                parent = parent.$parent;
                if (parent) {
                    name = parent.$options.name;
                }
            }
            if (parent) {
                parent.$emit.apply(parent, [eventName].concat(rest));
            }
        },
        handleClick() {
            // 通知 SelectVirtualList 组件，点击了项目
            this.dispatch('SelectVirtualList', 'click-virtual-item', this.source);
        },
        handleHover(e){
            e.stopPropagation()
            clearTimeout(this.timer)
            this.hoverKey=e.key
            this.timer= setTimeout(()=>{
                if (this.hoverKey===e.key){
                    this.dispatch('App','visible-popover', {state:true,source:this.source,ref:this.$refs?.tempRef})
                    clearTimeout(this.timer)
                }
            },500)
        },
        handleHoverLeave(e){
            e.stopPropagation()
            clearTimeout(this.timer)
            this.hoverKey=null
            this.dispatch('App','visible-popover', {state:false,source:this.source,ref:this.$refs?.tempRef})
        }
    },
    components:{
      'use-popover':usePopover
    },
    template:`
     <div :class="['virtual-item', {'is-selected': curId === source.key}]" @click="handleClick">
            <use-popover :title="source.label" :show-text="source.label" :width="250"
             size="small" :data-list="source.info"  placement="left"></use-popover>
<!--            <span ref="tempRef" style="display: inline-block" @mouseenter.stop="handleHover" @mouseleave.stop="handleHoverLeave">{{source.label}}</span>-->
<!--                <span>{{source.label}}</span>-->
     </div>
`
}
