<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <script src="../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../static/vue/vue.min.js"></script>
    <!-- rem布局 -->
    <script src="../static/js/page/lib-flexible.js"></script>
    <link rel="stylesheet" href="../static/css/page/payFailurePage.css" />
    <!-- I18n国际化 -->
    <script src="../static/I18n/initI18n.js"></script>
  </head>

  <body>
    <div id="app">
      <!-- <button @click="test">关闭</button>
      测试 -->
      <!-- 隐私条款 -->
    </div>

    <script>
      //dom加载完成后自定义title
      window.addEventListener("DOMContentLoaded", () => {
        $.getJSON("../static/utils/config.json", function (data) {
          document.title = data.BYOD.DocumentTitle || ""
        })
      })
      new Vue({
        el: "#app",
        data: {
          systemLanguage: {},
          openTable: {}
        },
        created() {
          this.initializeThe()
          this.fixLan()
        },
        mounted() {
          $("html").css({ "--styleColor": this.openTable.color })
        },
        methods: {
          initializeThe() {
            this.openTable = JSON.parse(sessionStorage.getItem("openTable"))
          },

          fixLan() {
            // let language = sessionStorage.getItem(language) || 'zh';
            let { language } = this.openTable
            this.systemLanguage = window.i18n[language]
          },
          onBlack() {
            window.location.href = "../order/menuPage.html"
          },
          test() {
            //当你在iframe页面关闭自身时
            var index = parent.layer.getFrameIndex(window.name) //先得到当前iframe层的索引
            parent.layer.close(index) //再执行关闭
          }
        },
        computed: {}
      })
    </script>
  </body>
</html>
