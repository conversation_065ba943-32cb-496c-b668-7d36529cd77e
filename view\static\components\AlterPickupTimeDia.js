const AlterPickupTimeDia = {
  props: ["showPickupDia"],
  methods: {
    updateDialog() {
      this.$parent.openTable.storeData = { ...this.$parent.backupStoreData }
      this.$emit("update:showPickupDia", false)
    },
    confirm() {
      let fCodeList = this.$parent.invalidShopCartList.map(item => item.fCode)
      this.$parent.shopCartList = this.$parent.shopCartList.filter(
        item => !fCodeList.includes(item.fCode)
      )
      sessionStorage.setItem("shopCartList", JSON.stringify(this.$parent.shopCartList))
      this.$emit("update:showPickupDia", false)
      window.location.reload()
    },
    getFoodTitle() {
      return this.$parent.invalidShopCartList.reduce((acc, cur) => {
        acc += this.$parent.inListTitle(cur) + " "
        return acc
      }, "")
    }
  },
  template: `
     <v-dialog
      v-model="showPickupDia"
      persistent
    >
      <v-card>
        <v-card-title/>
        <v-card-text style="font-size: 0.4rem">
          <p> {{$parent.systemLanguage.changePickupTimeWarnDialogTitle}}</p>
          <strong> {{getFoodTitle()}}</strong>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="error darken-1"
            text
            @click="updateDialog"
          >
            {{$parent.systemLanguage.cancelBtn}}
          </v-btn>

          <v-btn
            color="primary darken-1"
            text
            @click="confirm"
          >
            {{$parent.systemLanguage.confirmBtn}}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>`
}
