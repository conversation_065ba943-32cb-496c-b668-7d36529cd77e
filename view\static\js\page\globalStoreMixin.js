const globalStoreMixin = {
  data() {
    return {
      payTypeObj: [
        {
          value: "payAtCashier",
          imgSrc: `${API_PATH}static/img/payImage/payAtCashierIcon.jpg`,
          class: "payAtCashierIcon",
          belong: "pos",
          id: 1
        },
        {
          value: "wallet",
          imgSrc: `${API_PATH}static/img/payImage/wallet.jpg`,
          class: "payAtCashierIcon",
          belong: "pos",
          id: 2
        },
        {
          value: "VM",
          imgSrc: `${API_PATH}static/img/payImage/vmPay.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 3
        },
        {
          value: "UNIONPAY",
          imgSrc: `${API_PATH}static/img/payImage/unionPay.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 4
        },
        {
          value: "FPS",
          imgSrc: `${API_PATH}static/img/payImage/fpsPay.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 5
        },
        {
          value: "ALIPAYHK",
          imgSrc: `${API_PATH}static/img/payImage/alipayHK.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 6
        },
        {
          value: "ALIPAYCN",
          imgSrc: `${API_PATH}static/img/payImage/alipayCN.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 7
        },
        {
          value: "WECHAT",
          imgSrc: `${API_PATH}static/img/payImage/wechatPay.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 8
        },
        {
          value: "OCTOPUS",
          imgSrc: `${API_PATH}static/img/payImage/octopusPay.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 9
        },
        {
          value: "PAYME",
          imgSrc: `${API_PATH}static/img/payImage/payme.jpg`,
          class: "onlinePaymentIcon",
          belong: "eft",
          id: 10
        },
        {
          value: "iPay88",
          imgSrc: `${API_PATH}static/img/payImage/iPay88.jpg`,
          class: "onlinePaymentIcon",
          belong: "solely",
          id: 11
        },
        {
          value: "razer",
          imgSrc: `${API_PATH}static/img/payImage/fiuu.jpg`,
          class: "onlinePaymentIcon",
          belong: "solely",
          id: 12
        },
        {
          value: "boc",
          imgSrc: `${API_PATH}static/img/payImage/boc.jpg`,
          class: "onlinePaymentIcon",
          belong: "boc",
          id: 13
        },
        {
          value: "card",
          imgSrc: `${API_PATH}static/img/payImage/creditcard.svg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 14
        },
        {
          value: "account2account",
          imgSrc: `${API_PATH}static/img/payImage/account2account.png`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 15
        },
        {
          value: "alipay",
          imgSrc: `${API_PATH}static/img/payImage/alipayCN.jpg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 16
        },
        {
          value: "applepay",
          imgSrc: `${API_PATH}static/img/payImage/applepay.svg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 17
        },
        {
          value: "googlepay",
          imgSrc: `${API_PATH}static/img/payImage/googlepay.svg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 18
        },
        {
          value: "paypal",
          imgSrc: `${API_PATH}static/img/payImage/paypal.svg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 19
        },
        {
          value: "interac",
          imgSrc: `${API_PATH}static/img/payImage/InteracPay.png`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 20
        },
        {
          value: "unionpay",
          imgSrc: `${API_PATH}static/img/payImage/unionPay.jpg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 21
        },
        {
          value: "oxipay",
          imgSrc: `${API_PATH}static/img/payImage/oxipay.jpg`,
          // imgSrc: "https://cdn.shophumm.com/humm/uploads/sites/3/humm-logo-1.png",
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 22
        },
        {
          value: "visacheckout",
          imgSrc: `${API_PATH}static/img/payImage/visapay.svg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 23
        },
        {
          value: "wechat",
          imgSrc: `${API_PATH}static/img/payImage/wechatPay.jpg`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 24
        },
        {
          value: "Host Page",
          imgSrc: `${API_PATH}static/img/payImage/HostPage.png`,
          class: "onlinePaymentIcon",
          belong: "windcave",
          id: 25
        }
      ]
    }
  },
  methods: {
    setCookie(serialNumber) {
      // cookie只能存储4kb 数量限制,每个域下最多不能超过50个cookie 存储数据类型限制,cookie只能存储字符串
      let oldMerchantRefArray = Cookies.get("merchantRefArray")
      let nowTime = moment().valueOf()
      let setMerchantRefArray
      if (oldMerchantRefArray) {
        setMerchantRefArray = JSON.parse(oldMerchantRefArray)
        let merchantRefObj = {
          merchantRef: serialNumber,
          time: nowTime
        }
        if (setMerchantRefArray.length >= 20) setMerchantRefArray.shift() //储存数量最大20条订单
        setMerchantRefArray.push(merchantRefObj)
      } else {
        console.log("无订单号")
        setMerchantRefArray = [
          {
            merchantRef: serialNumber,
            time: nowTime
          }
        ]
      }
      console.log(JSON.parse(JSON.stringify(setMerchantRefArray)), "setMerchantRefArray")

      Cookies.set("merchantRefArray", JSON.stringify(setMerchantRefArray), {
        expires: 3 // cookie默认3天过期
      })
    },
    // ipay支付
    iPay88Online(data) {
      layer.load(2)
      let { urlPrefix } = mark === "pc_index" ? app.openTableData : this.openTable
      let { clientID, currency, goodsName, refNo, signature, totalPrice } = data
      let requestData = {
        MerchantCode: clientID,
        PaymentId: "",
        RefNo: refNo,
        Amount: totalPrice,
        Currency: currency,
        ProdDesc: goodsName,
        UserName: "",
        UserEmail: "",
        UserContact: "",
        Remark: "",
        Lang: "",
        SignatureType: "SHA256",
        Signature: signature,
        ResponseURL: `${urlPrefix}iPay88/responseUrl`,
        BackendURL: `${urlPrefix}iPay88/backendUrl`
      }
      var turnForm = document.createElement("form")
      document.body.appendChild(turnForm)
      turnForm.method = "post"
      turnForm.action = "https://www.mobile88.com/epayment/entry.asp"
      // turnForm.target = 'mainFrame';

      for (var x in requestData) {
        var newElement = document.createElement("input")
        newElement.setAttribute("type", "hidden")
        newElement.name = x
        newElement.value = requestData[x]
        //alert(newElement.name);
        turnForm.appendChild(newElement)
      }
      console.log(turnForm, "turnForm")
      turnForm.submit()
    },
    // EFT跳转支付
    onEFTPay() {
      let isPayOrderPage = window.location.pathname.includes("payOrderPage")
      try {
        SpiralPG.pay()
        const weChatDom = document.querySelector("#spiralpg-lightbox")
        if (weChatDom) {
          isPayOrderPage ? this.closePayLoading() : layer.closeAll("loading")
        }
      } catch (err) {
        setTimeout(() => {
          this.onEFTPay()
        }, 200)
      }
    },
    // init支付初始方法
    initEFTPay(sessionId) {
      //历史订单页调用不同UI方法
      let isPayOrderPage = window.location.pathname.includes("payOrderPage")
      isPayOrderPage ? this.openPayLoading() : layer.load(2)
      let { language } = mark === "pc_index" ? app.openTableData : this.openTable
      let localeLan = {
        en: "en_US", // 英文
        zh: "zh_HK", // 繁体
        thirdLan: "en_US" // 第三语言
      }
      const errorCall = () => {
        let { EFTPayInitError } = mark === "pc_index" ? app.arrLang : this.systemLanguage
        if (isPayOrderPage) {
          this.showPayErrorTip(EFTPayInitError)
          this.closePayLoading()
        } else {
          layer.msg(EFTPayInitError)
          layer.closeAll("loading")
        }
      }
      try {
        SpiralPG.init(sessionId, {
          locale: localeLan[language]
        })

        SpiralPG.errorCallback = errorCall.bind(this)
      } catch (err) {
        errorCall()
        return
      }
      this.onEFTPay()
    },
    /**
     * @description: 跳转支付成功页面
     * @param {Object} result 支付成功返回的数据
     * @param {Boolean} needSetMerchantRef 订单号是否需要设置cookie
     */
    //跳转到支付成功页面
    redirectPaySuccessPage(result, needSetMerchantRef = false) {
      if (needSetMerchantRef) {
        this.setCookie(result.merchantRef) // 加入缓存
      }
      window.location.href = result.url
    },
    //支付返回状态码提示询问是否要确定下单(重复食品下单/存在未处理订单)
    orderAgainConfirm(content, repeatParams, confirmType = "order") {
      let { confirmTitle, cancelBtn, confirmOrderAgain } = this.systemLanguage
      layer.confirm(
        content,
        {
          title: confirmTitle,
          btn: [cancelBtn, confirmOrderAgain],
          skin: "verify-repeated-layer layui-custom-style"
        },
        (index, layero) => {
          layer.close(index)
        },
        () => {
          switch (confirmType) {
            case "recharge":
              this.requestRecharge(repeatParams)
              break
            default:
              this.sendOrder(repeatParams)
              break
          }
        }
      )
    }
  }
}
