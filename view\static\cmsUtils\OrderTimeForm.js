Vue.component("order-timer-form", {
  props: ["list"],
  data() {
    return {
      ruleForms: [],
      rules: {
        period: []
      }
    }
  },

  created() {
    let defaultForm = [
      {
        period: "",
        longestOrderTime: 0,
        countdown: 0,
        fCode: ""
      }
    ]
    let list = []
    if (typeof this.list === "function") {
      list = Object.values(this.list.call(this.$root))
    }
    if (Array.isArray(list) && list.length) {
      this.ruleForms = list
    } else {
      this.ruleForms = defaultForm
    }
  },
  methods: {
    onFCodeChange(index) {
      this.ruleForms[index].period = ""
    },
    onPeriodChange(index) {
      this.ruleForms[index].fCode = ""
    },
    onLongestOrderTimeChange(ev, index) {
      if (ev < this.ruleForms[index].countdown) {
        this.ruleForms[index].countdown = ev
      }
    },
    addItem() {
      this.ruleForms.push({
        period: "",
        longestOrderTime: 0,
        countdown: 0,
        fCode: ""
      })
    },
    // 删除使用时间
    del(index) {
      this.ruleForms.splice(index, 1)
    }
  },
  template: `
       <template>
        <div class="container">
         <el-row gutter="20">
            <el-col :span="6">
              <el-form-item label="Period" ></el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="FCode" ></el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="Order Time" ></el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="Countdown"></el-form-item>
            </el-col>
         </el-row>
          <el-row gutter="20" v-for="(item, index) in ruleForms">
            <el-form
              :inline="true"
              :model="ruleForms[index]"
              ref="order-time-rule"
              class="demo-form-inline"
              size="small"
              label-position="top"
              :rules="rules"
            >
             <el-col :span="7">
                <el-form-item style="width:100%" prop="period" :rules="[
                { required: false,
                  pattern: /^((?:[01]\\d|2[0-3]):([0-5]\\d)(-)(?:[01]\\d|2[0-3]):([0-5]\\d)(;)?)*$/,
                  message: 'Format : hh:mm-hh:mm  (Use ; to separate multiple times)'
                }
                ]">
                    <el-input
                      placeholder="format:HH:mm-HH:mm,use ; to separate multiple"
                      v-model="ruleForms[index].period"
                      style="width:100%"
                      :disabled="!!ruleForms[index].fCode"
                      @change="onPeriodChange(index)"
                      >
                    </el-input>
                </el-form-item>
            </el-col>
              <el-col :span="5">
               <el-form-item style="width:100%">
                <el-input placeholder="use ; to separate multiple" :disabled="!!ruleForms[index].period"  @change="onFCodeChange(index)"  v-model="ruleForms[index].fCode" :min="0"  style="width:100%" ></el-input>
              </el-form-item>
            </el-col>
             <el-col :span="5">
               <el-form-item style="width:100%">
                <el-input-number @change="onLongestOrderTimeChange($event,index)" :max="86400" v-model="ruleForms[index].longestOrderTime" :min="0"  style="width:100%" ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  style="width:100%" prop="countdown">
               <el-input-number v-model="ruleForms[index].countdown" :min="0" :max="ruleForms[index].longestOrderTime" style="width:100%" ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-form-item>
                 <el-button
                 v-if="index != 0"
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  @click="del(index)"
                ></el-button>
                
                <el-button v-if="index == 0" type="success" @click="addItem" size="small">
                  <i class="el-icon-plus"></i>
                </el-button>
               
              </el-form-item>
            </el-col>
            </el-form>
          </el-row>
        </div>
      </template>
  `
})
