const imitateInput = {
  props: {
    keywords: { type: Array }, //接收的插入字符串
    editValue: { type: String }, //接收到源数据
  },
  data() {
    return {
      editHtml: '', //input内容(包括dom)
      _editValue: null, //input内容(不包括dom)
      cursorCoordinates: null, //光标坐标位置
    }
  },
  template: `<div id="imitateInput" v-show='keywords.length'>
      <el-button size="small" v-for='item in keywords'  :key='item'
      style="margin: 3px 5px"
      type="primary" @click="addDynamicName(item)">
        {{item}}
      </el-button>
      <div class="el-input el-input--suffix input-wrapper">
        <div
          contenteditable="plaintext-only"
          ref="inputRef"
          class="imitate-input el-input__inner"
          placerholder="Please enter content"
          @input="inputChange"
          @blur="handleBlur"
          @click='handleClickInput'
          @paste="checkPastedMsgTitle"
          ></div>
      </div>
    </div>`,
  computed: {},
  mounted() {
    //初始化显示数据
    this.echoData(this.editValue, 0)
  },
  methods: {
    //处理点击input框逻辑
    handleClickInput() {
      //点击时获取当前文字坐标,可用于添加tag标签位置
      this.cursorCoordinates = this.optimizedOffsetCoordinates()
    },
    //优化偏移光标坐标位置偏移:移除点击到tag标签的光标坐标
    optimizedOffsetCoordinates() {
      //若点击的光标坐标在tag范围内,则根据中位数距离判断是否向前取值或向后取值
      const tag = this.tagRange() //拿到的范围
      // console.log(tag, 'tag范围')
      let cursor = this.getCursortPosition(this.$refs.inputRef) //拿到的坐标
      // console.log(cursor, '点击input的坐标')
      for (const key in tag) {
        if (Object.hasOwnProperty.call(tag, key)) {
          for (let i = 0; i < tag[key].length; i++) {
            const el = tag[key][i]
            //若大于el[0],并且小于el[1],代表处于某个tag范围内,需要改变
            if (cursor >= el[0] && cursor <= el[1]) {
              // console.log(tag[key][i])
              //判断应该向前取值\向后取值
              let newCursor =
                  el[1] - cursor >= cursor - el[0] ? el[0] - 1 : el[1] + 1
              // console.log(newCursor, '修正后的坐标')
              return newCursor
            }
          }
        }
      }
      return cursor
    },
    /**
     * 若editValue存在值,则回显至输入框中
     * @param val :用于刷新input框显示数据
     * @param len :新增tag的长度,若为0则标识初始化执行的此函数,反之为改变input后刷新页面执行的
     */
    echoData(val, len) {
      if (val) {
        let echoStr = val
        //拿到所有的标签的位置索引
        this.keywords.forEach(v => {
          echoStr = echoStr.replaceAll(
              v,
              `<span class="el-tag el-tag--danger el-tag--plain tagg" contenteditable="false">${v}</span>&nbsp;`
          )
        })
        // console.log(echoStr, 'echoStr')
        this.editHtml = this.$refs.inputRef.innerHTML = echoStr
      } else {
        this.$refs.inputRef.innerText = ''
      }
      //若len为true则标识
      //为false:初始化执行函数
      if (len) {
        this.cursorCoordinates = this.cursorCoordinates + len
        // console.log(this.cursorCoordinates, 'echoData重置后光标位置')
      } else {
        this.cursorCoordinates = this.editValue.length
        this._editValue = this.editValue
        // console.log(this.cursorCoordinates, 'echoData初始化后光标位置')
      }
    },
    //失焦事件
    handleBlur() {
      // console.log('触发失去焦点')
      let text = this.$refs.inputRef.innerText
      this.removeTagAfterEmpty(text)
      this.$emit('input-blur', text)
    },
    //移除tag后面的一位空格
      //val : innerText ,
    removeTagAfterEmpty(val){
      if (val){
      let text=val
      // console.log(text,text.length,'处理之前')
          text = text.replace(/#billNumber\s/g, '#billNumber')
          text = text.replace(/#amt\s/g, '#amt')
          text=text.replace(/#paymentMethod\s/g,'#paymentMethod')
          text=text.replace(/#address\s/g,'#address')
      // console.log(text,text.length ,'处理完后')
      return text
      }
      return  ''
    },
    // 点击添加按钮时候，加入tag,改变html
    addDynamicName(val) {
      //拿到innerText,在光标对应位置插入keywords,然后在回显初始化
      let input = this.$refs.inputRef
      this._editValue = this.insertStr(
          this.removeTagAfterEmpty(this._editValue),
          this.cursorCoordinates,
          val
      ) //插入值
      // console.log(this._editValue,'点击按钮加入后的值')
      this.echoData(this._editValue, val.length) //初始化
      this.$refs.inputRef.innerHTML = this.editHtml
      this.handleBlur()
      // input.focus()
      //聚焦,移动光标位置
      // this.setCaretPosition(input, this.cursorCoordinates)
      input.blur()
      //光标设置至最后
      // this.locateToLastIndex(this.$refs.inputRef)
      // console.log(this.cursorCoordinates, '点击插入后的光标位置')
    },
    //input输入时
    inputChange() {
      this.$nextTick(() => {
        this.editHtml = this.$refs.inputRef.innerHTML
        this._editValue = this.$refs.inputRef.innerText
      })
      // console.log('触发input输入事件')
      this.cursorCoordinates = this.getCursortPosition(this.$refs.inputRef)
      // this.cursorCoordinates = this.optimizedOffsetCoordinates()
      //   console.log(this.cursorCoordinates, '修改input时的坐标')
    },
    //移动光标至最后
    locateToLastIndex(obj) {
      if (window.getSelection) {
        obj.focus() //聚焦
        let range = window.getSelection() // 创建range
        range.selectAllChildren(obj) // range 选择obj下所有子内容
        range.collapseToEnd() // 光标移至最后
      } else if (document.selection) {
        let range = document.selection.createRange() // 创建选择对象
        range.moveToElementText(obj) // range定位到obj
        range.collapse(false) // 光标移至最后
        range.select()
      }
    },
    //粘贴时触发事件
    checkPastedMsgTitle(e) {
      // 阻止末默认事件,手动获取剪切板的内容，同步到editString上
      e.preventDefault()
      let pasteData = (e.clipboardData || window.clipboardData).getData('text')
      const selection = window.getSelection()
      if (!selection.rangeCount) {
        return false
      }
      //删除dom中选中的文本
      selection.deleteFromDocument()
      //重新创建显示文字
      selection.getRangeAt(0).insertNode(document.createTextNode(pasteData))
      //聚焦在最后面
      selection.collapseToEnd()
      //   selection.removeAllRanges()
      this.$nextTick(() => {
        this.editHtml = this.$refs.inputRef.innerHTML
      })
    },
    // 获取当前光标位置
    getCursortPosition(element) {
      let caretOffset = 0
      let doc = element.ownerDocument || element.document
      let win = doc.defaultView || doc.parentWindow
      let sel = null
      // 谷歌、火狐
      if (typeof win.getSelection != 'undefined') {
        sel = win.getSelection()
        // 选中的区域
        if (sel.rangeCount > 0) {
          var range = win.getSelection().getRangeAt(0)
          // 克隆一个选中区域
          var preCaretRange = range.cloneRange()
          // 设置选中区域的节点内容为当前节点
          preCaretRange.selectNodeContents(element)
          // 重置选中区域的结束位置
          preCaretRange.setEnd(range.endContainer, range.endOffset)
          caretOffset = preCaretRange.toString().length
        }
        // IE浏览器
      } else if ((sel = doc.selection) && sel.type != 'Control') {
        let textRange = sel.createRange()
        let preCaretTextRange = doc.body.createTextRange()
        preCaretTextRange.moveToElementText(element)
        preCaretTextRange.setEndPoint('EndToEnd', textRange)
        caretOffset = preCaretTextRange.text.length
      }
      // console.log(caretOffset, '光标坐标')
      return caretOffset
    },
    //在指定位置插入字符串
    insertStr(soure, start, newStr) {
      // console.log(soure,start,newStr)
      return soure.slice(0, start) + newStr + soure.slice(start)
    },
    //设置光标位置
    setCaretPosition(dom, pos) {
      if (dom.setSelectionRange) {
        // IE Support
        dom.focus()
        dom.setSelectionRange(pos, pos)
      } else if (dom.createTextRange) {
        // Firefox support
        var range = dom.createTextRange()
        range.collapse(true)
        range.moveEnd('character', pos)
        range.moveStart('character', pos)
        range.select()
      }
    },
    //设置光标位置
    setFocusFun(ele, pos) {
      // console.log(ele, pos)
      let sel = window.getSelection()
      let rang = document.createRange() //创建一个rang对象
      let content = ele.firstChild
      let innerLen = ele.innerText.length
      rang.setStart(content, pos)
      rang.setEnd(content, innerLen)
      rang.collapse(false) //起始位置和终止位置是否相同的布尔值
      sel.removeAllRanges() //移除选中区域的range对象
      sel.addRange(rang) //给选中区域添加range对象
    },
    //返回字符串所有存在的坐标范围
    searchSubStr(str, subStr) {
      let arr = []
      let pos = str.indexOf(subStr)
      while (pos > -1) {
        arr.push([pos + 1, pos + subStr.length - 1])
        pos = str.indexOf(subStr, pos + 1)
      }
      return arr
    },
    //将存在的tag标签的索引范围保存起来
    tagRange() {
      let tagRange = {}
      // console.log(this._editValue, 'value')
      this.keywords.forEach(i => {
        tagRange[i] = this.searchSubStr(this._editValue, i)
      })
      return tagRange
    },
  },
}
