<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <script src="../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <!-- I18n国际化 -->
    <script src="../static/I18n/initI18n.js"></script>
    <!--     vue -->
    <script src="../static/vue/vue.min.js"></script>
    <!-- rem布局 -->
    <!--  <script src="../static/js/page/lib-flexible.js"></script>-->
    <link rel="stylesheet" href="../static/css/page/payFailurePage.css" />
    <!-- 引入 layui.css/layui.js -->
    <link rel="stylesheet" href="../static/layui/css/layui.css" />
    <script src="../static/layui/layui.js"></script>
  </head>

  <body>
    <div id="app" v-cloak>
      <div class="container">
        <div class="header">
          <img src="../static/img/payImage/errPay.jpg" alt="errPay" class="pay-fail-logo" />
          <p class="pay-fail-title">{{systemLanguage.failurePayTip}}</p>
          <div class="pay-fail-sub-box" v-if="loaded">
            <span class="pay-fail-sub-title">{{ systemLanguage.payFailSubtitle}}</span>
            <span class="pay-fail-sub-value">{{ payFailReason}}</span>
          </div>
        </div>
        <div class="main">
          <div class="pay-fail-tips" v-html="failPrompt"></div>
        </div>
        <div class="footer">
          <button class="back-btn" @click="onBlack">{{systemLanguage.failurePayBlackBtn}}</button>
        </div>
      </div>
    </div>
    <script src="../static/js/index/utils.js"></script>
    <script src="../static/js/index/device.js"></script>

    <script>
      //dom加载完成后自定义title
      window.addEventListener("DOMContentLoaded", () => {
        $.getJSON("../static/utils/config.json", function (data) {
          document.title = data.BYOD.DocumentTitle || ""
        })
      })
      new Vue({
        el: "#app",
        data: {
          systemLanguage: {},
          openTable: {},
          failPrompt: "",
          failStatus: "",
          loaded: false,
          loading: false
        },
        created() {
          this.initializeThe()
          this.fixLan()
          this.getParmObj()
          $.getScript("../static/js/page/lib-flexible.js")
        },
        mounted() {
          $("html").css({ "--styleColor": this.openTable.color })
        },
        methods: {
          initializeThe() {
            this.openTable = JSON.parse(sessionStorage.getItem("openTable"))
          },

          fixLan() {
            let { language } = this.openTable
            this.systemLanguage = window.i18n[language]
          },
          onBlack() {
            window.location.href = "../order/menuPage"
          },
          // 解析url参数
          getQueryVariable(list = []) {
            let query = window.location.search.substring(1)
            if (!query) return {}
            try {
              query = window.atob(query)
            } catch (error) {
              query = window.location.search
            }
            const searchParams = new URLSearchParams(query)

            return list.reduce((acc, cur) => {
              acc[cur] = searchParams.get(cur)
              return acc
            }, {})
          },
          getParmObj() {
            let index = layer.load(2, { shade: 0 })
            let { sessionId, message } = this.getQueryVariable(["sessionId", "message"])
            let { errorGetParmObjTxt } = this.systemLanguage
            let { language } = this.openTable
            $.post({
              url: "../pay/getPayFailureReason",
              dataType: "json",
              data: { sessionId, message },
              success: res => {
                this.loaded = true
                this.failPrompt = res[language]
                this.failStatus = res.state
              },
              error: () => {
                layer.msg(errorGetParmObjTxt)
              },
              complete: () => {
                layer.close(index) //關閉加載層
                this.loading = false
              }
            })
          }
        },
        computed: {
          payFailReason() {
            let { payTimeout, payCancellation, payOtherExceptions, securityVerificationFailed } =
              this.systemLanguage
            let map = {
              timeout: payTimeout,
              cancel: payCancellation,
              securifyFailure: securityVerificationFailed,
              other: payOtherExceptions
            }
            return map[this.failStatus] || ""
          }
        }
      })
    </script>
  </body>
</html>
