.searchListContent .modal-body {
  max-height: none;
}

.searchListWarp input.form-control:focus {
  outline: 0;
  color: #212529;
  box-shadow: none;
  border-color: #ced4da;
}

.listSearch-Help {
  font-size: 0.9em;
  margin: 0 auto;
  padding: 36px 0;
  text-align: center;
  width: 80%;
}

.searchListContent .modal-footer {
  box-shadow: 0 -1px 0 0 #e0e3e8, 0 -3px 6px 0 rgba(69, 98, 155, 0.12);
  justify-content: left;
}

.searchList-Commands {
  color: rgba(33, 37, 41, 0.75);
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.searchListWarp svg {
  height: 24px;
  width: 24px;
}

.searchList-LoadingIndicator {
  display: none;
}

.searchList-Commands li {
  align-items: center;
  display: flex;
  margin-right: 0.8em;
}

.searchList-Commands-Key {
  margin-right: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  background-image: none;
  box-shadow: none;
  color: rgba(33, 37, 41, 0.75);
}

.searchList-Label {
  font-size: 0.65rem;
}

.searchList-Commands {
  display: flex;
}

.searchList-Data {
  text-align: left;
  margin: 0;
  padding: 0;
  /* margin-bottom: 10px; */
  max-height: 60vh;
  overflow-y: scroll;
}

.searchList-Data-li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 10px;
  list-style: none;
  /* 鼠标手势 */
  cursor: pointer;
}

.searchList-Data-li:hover {
  background-color: #087990;
  color: #fff;
  border-radius: 2px;
  /* width: 100vw; */
}

.searchList-Data-li-left {
  display: flex;
  align-items: center;
  /* flex-wrap: wrap; */
}

.searchList-Data-li-left p {
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.searchList-Data-selectSvg {
  display: none;
}

.searchList-Data-li:hover .searchList-Data-selectSvg {
  display: block;
}

.searchList-Data-name {
  /* display: flex;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
}

.DocSearch-Loading-Icon {
  display: none;
}
.matchContent {
  /* 高亮黄色颜色 */
  color: #ffc107;
}
