/* Time Simulation Component Styles */

/* Mask Layer */
.test-time-simulation-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 10;
  transition: opacity 0.3s ease;
}

/* Unified container for trigger and panel */
.test-time-simulation-container {
  position: fixed;
  top: 180px;
  right: 0;
  z-index: 12;
  display: flex;
  height: 40px;
  min-width: 200px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 6px 0 0 6px;

  /* * [ 核心代码 ] 就是这一行！
   * 它告诉浏览器不仅要为 transform (位置) 添加动画，
   * 也要为 width (宽度) 添加一个同样时长、同样效果的平滑过渡动画。
   */
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), width 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  transform: translateX(calc(100% - 40px));
}

/* Expanded state: slide the container into view */
.test-time-simulation-container--expanded {
  transform: translateX(0);
}

/* Animating state: provide feedback and prevent clicks */
.test-time-simulation-container--animating {
  cursor: not-allowed;
  opacity: 0.8;
}

/* Trigger area within the container */
.test-time-simulation-trigger {
  width: 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  border-radius: 6px 0 0 6px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.test-time-simulation-trigger-icon {
  font-size: 18px;
  color: white;
  line-height: 1;
}

/* Panel content area within the container */
.test-time-simulation-panel-content {
  flex-grow: 1;
  width: auto;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px 8px 8px;
  gap: 8px;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-right: none;
  font-family: -apple-system, BlinkMacSystemFont, Arial, sans-serif;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  overflow: hidden;
}

/* Simulating state styles */
.test-time-simulation-container--simulating .test-time-simulation-trigger {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  box-shadow: 0 4px 16px rgba(72, 187, 120, 0.3);
}

.test-time-simulation-container--simulating .test-time-simulation-panel-content {
  background: #f0fff4;
  border-color: #9ae6b4;
}

/* Time display text */
.test-time-simulation-time-display {
  flex-grow: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

.test-time-simulation-time-text {
  font-size: 13px;
  font-weight: 500;
  color: #718096;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.test-time-simulation-time-text--simulating {
  color: #2d3748;
}

/* Edit button */
.test-time-simulation-edit-btn {
  flex-shrink: 0;
  background: #4299e1;
  color: white;
  padding: 4px;
  border-radius: 4px;
  font-size: 11px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(66, 153, 225, 0.3);
  transition: all 0.2s ease;
}

.test-time-simulation-edit-icon {
  line-height: 1;
}
