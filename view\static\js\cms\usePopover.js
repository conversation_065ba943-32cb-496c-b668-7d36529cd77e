const usePopover = {
    props: {
        title: {//弹窗title
            type: String,
            default: 'More Info'
        },
        dataList: {//传入数据
            type: Object,
            default: {},
        },
        showText:{ //显示按钮的文字
            type:String,
            default:'Hover'
        },
        trigger: {//激活方式
            type: String,
            default: "hover"
        },
        width:{//width
            type:Number,
            default:400
        },
        size:{//传入的class ,, ,没啥用  还是要在外部定义class 样式
            type:String,
            default:'medium'
        },
        placement:{
            type:String,
            default:"top-start"
        }
    },
    data() {
        return {
            list: {},
            isShow: false
        }
    },
    created() {
        this.filterData()
    },
    methods: {
        filterData() {
            for (const key in this.dataList) {
                let t = typeof this.dataList[key]
                 if (t === 'object') {
                    let obj = this.dataList[key]
                    Object.keys(obj).forEach(el => this.list[key.concat('-', el)] = obj[el])
                }else{
                    this.list[key] = this.dataList[key]
                }
            }
            this.isShow = Object.values(this.list).some(el => el)
        }
    },
    template: `
             <el-popover
                   :placement="placement"
                   :width="width"
                   :title="title"
                   v-if="isShow"
                   :open-delay="500"
                   :trigger="trigger">
                     <template #default >
                        <el-scrollbar style="height: 250px;max-height: 400px">
                           <div  :class="size" v-for="key in Object.keys(list)" :key="key"> 
                             <span>
                                <el-tag>{{key}}</el-tag>
                                {{list[key]}}
                             </span>
                                <el-divider v-if="key!==Object.keys(list)[Object.keys(list).length-1]"></el-divider>
                           </div>
                           <slot name="switch"></slot>
                        </el-scrollbar>
                     </template>
                     
                     <el-link slot="reference">{{showText}}</el-link>
             </el-popover>
             <el-tooltip v-else class="item" effect="dark" content="No Data" placement="top-start">
                      <el-link>{{showText}}</el-link>
             </el-tooltip>

    `,
}
