<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      // 解决用户激活问题的返回键拦截方案
      ;(function () {
        let userActivated = false
        let historyLength = 0

        // 建立初始历史记录栈
        function buildHistoryStack() {
          // 建立足够深的历史记录栈，即使没有用户激活也能工作一段时间
          for (let i = 0; i < 100; i++) {
            try {
              history.pushState({ index: i }, null, document.URL)
              historyLength++
            } catch (e) {
              break
            }
          }
        }

        // 核心拦截函数
        function blockBack(e) {
          if (userActivated) {
            // 用户已激活，可以安全使用 pushState
            history.pushState({ index: ++historyLength }, null, document.URL)
          } else {
            // 用户未激活，使用替代方案
            // 尝试阻止事件传播
            if (e) {
              e.preventDefault()
              e.stopPropagation()
              e.stopImmediatePropagation()
            }

            // 尝试 pushState（可能会失败，但不影响）
            try {
              history.pushState({ index: ++historyLength }, null, document.URL)
            } catch (err) {
              // 静默失败
            }

            // 使用 setTimeout 延迟执行，有时能绕过限制
            setTimeout(() => {
              try {
                history.pushState({ index: ++historyLength }, null, document.URL)
              } catch (err) {
                // 静默失败
              }
            }, 0)
          }
        }

        // 监听用户交互事件来标记用户激活状态
        function markUserActivated() {
          if (!userActivated) {
            userActivated = true
            // 用户激活后立即建立更多历史记录
            for (let i = 0; i < 50; i++) {
              history.pushState({ index: ++historyLength }, null, document.URL)
            }
          }
        }

        // 绑定用户交互事件
        const userEvents = ["click", "touchstart", "touchend", "mousedown", "keydown"]
        userEvents.forEach(eventType => {
          document.addEventListener(eventType, markUserActivated, {
            once: false,
            passive: true,
            capture: true
          })
        })

        // 绑定 popstate 事件
        window.addEventListener("popstate", blockBack, { capture: true })

        // 页面加载时立即建立历史栈
        buildHistoryStack()

        // 页面获得焦点时重新建立保护
        window.addEventListener("focus", function () {
          if (userActivated) {
            for (let i = 0; i < 20; i++) {
              history.pushState({ index: ++historyLength }, null, document.URL)
            }
          }
        })

        // 页面可见性变化时的处理
        document.addEventListener("visibilitychange", function () {
          if (!document.hidden && userActivated) {
            for (let i = 0; i < 20; i++) {
              history.pushState({ index: ++historyLength }, null, document.URL)
            }
          }
        })

        // 定时器维护历史栈（仅在用户激活后）
        setInterval(function () {
          if (userActivated) {
            try {
              history.pushState({ index: ++historyLength }, null, document.URL)
            } catch (e) {
              // 静默失败
            }
          }
        }, 500)

        // 监听 Android 返回键（如果在 WebView 中）
        document.addEventListener(
          "backbutton",
          function (e) {
            e.preventDefault()
            markUserActivated()
            return false
          },
          false
        )
      })()

      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
