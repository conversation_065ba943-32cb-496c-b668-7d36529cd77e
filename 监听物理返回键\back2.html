<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      // 禁止浏览器后退
      history.pushState(null, null, document.URL)
      window.addEventListener("popstate", function (e) {
        history.pushState(null, null, document.URL)
      })
      // 彻底阻止浏览器物理返回键 - 解决焦点问题的增强版本
      // ;(function () {
      //   // 立即建立历史记录堆栈
      //   history.pushState(null, null, location.href)

      //   // 核心拦截函数 - 每次都无条件推送新状态
      //   function blockBack() {
      //     history.pushState(null, null, location.href)
      //   }

      //   // 确保页面获得焦点的函数
      //   function ensureFocus() {
      //     // 尝试让页面获得焦点
      //     if (document.body) {
      //       document.body.focus()
      //       console.log("点击页面")
      //     }
      //     window.focus()

      //     // 模拟用户交互来激活事件监听器
      //     const event = new Event("click", { bubbles: true })
      //     document.dispatchEvent(event)
      //   }

      //   // 绑定popstate事件 - 移除所有条件判断
      //   window.addEventListener("popstate", blockBack)

      //   // 页面可见性变化时重新建立保护
      //   document.addEventListener("visibilitychange", function () {
      //     if (!document.hidden) {
      //       // 页面重新可见时立即建立保护
      //       history.pushState(null, null, location.href)
      //       ensureFocus()
      //     }
      //   })

      //   // 页面获得焦点时重新建立保护
      //   window.addEventListener("focus", function () {
      //     history.pushState(null, null, location.href)
      //   })

      //   // 页面失去焦点时也要保护
      //   window.addEventListener("blur", function () {
      //     history.pushState(null, null, location.href)
      //   })

      //   // 额外保护：定时器持续维护历史栈
      //   setInterval(function () {
      //     history.pushState(null, null, location.href)
      //   }, 100)

      //   // 监听hashchange事件（如果有hash变化）
      //   window.addEventListener("hashchange", blockBack)

      //   // 监听beforeunload事件
      //   window.addEventListener("beforeunload", blockBack)

      //   // 监听unload事件
      //   window.addEventListener("unload", blockBack)

      //   // 监听页面隐藏事件
      //   window.addEventListener("pagehide", blockBack)

      //   // 监听页面显示事件
      //   window.addEventListener("pageshow", function () {
      //     history.pushState(null, null, location.href)
      //     ensureFocus()
      //   })

      //   // 增强的页面加载完成处理
      //   function setupProtection() {
      //     // 建立多层历史记录保护
      //     for (let i = 0; i < 50; i++) {
      //       history.pushState(null, null, location.href)
      //     }

      //     // 确保页面获得焦点
      //     setTimeout(ensureFocus, 100)
      //     setTimeout(ensureFocus, 500)
      //     setTimeout(ensureFocus, 1000)

      //     // 添加键盘事件监听器作为额外保护
      //     document.addEventListener("keydown", function (e) {
      //       // 如果是返回键相关的按键，立即建立保护
      //       if (e.key === "Backspace" || e.keyCode === 8) {
      //         e.preventDefault()
      //         history.pushState(null, null, location.href)
      //         return false
      //       }
      //     })

      //     // 添加触摸事件监听器（移动端）
      //     document.addEventListener(
      //       "touchstart",
      //       function () {
      //         history.pushState(null, null, location.href)
      //       },
      //       { passive: true }
      //     )

      //     // 添加鼠标事件监听器
      //     document.addEventListener("mousedown", function () {
      //       history.pushState(null, null, location.href)
      //     })
      //   }

      //   // 页面加载完成后额外建立保护层
      //   if (document.readyState === "loading") {
      //     document.addEventListener("DOMContentLoaded", setupProtection)
      //   } else {
      //     setupProtection()
      //   }

      //   // 窗口加载完成后再次确保保护
      //   window.addEventListener("load", function () {
      //     setTimeout(setupProtection, 100)
      //   })

      //   // 立即尝试获得焦点
      //   setTimeout(ensureFocus, 0)
      // })()

      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
