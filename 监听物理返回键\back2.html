<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      // 彻底阻止浏览器物理返回键 - 使用最强力的方法
      ;(function () {
        // 立即建立历史记录堆栈
        history.pushState(null, null, location.href)

        // 核心拦截函数 - 每次都无条件推送新状态
        function blockBack() {
          history.pushState(null, null, location.href)
        }

        // 绑定popstate事件 - 移除所有条件判断
        window.addEventListener("popstate", blockBack)

        // 页面可见性变化时重新建立保护
        document.addEventListener("visibilitychange", function () {
          if (!document.hidden) {
            // 页面重新可见时立即建立保护
            history.pushState(null, null, location.href)
          }
        })

        // 页面获得焦点时重新建立保护
        window.addEventListener("focus", function () {
          history.pushState(null, null, location.href)
        })

        // 额外保护：定时器持续维护历史栈
        setInterval(function () {
          history.pushState(null, null, location.href)
        }, 100)

        // 监听hashchange事件（如果有hash变化）
        window.addEventListener("hashchange", blockBack)

        // 监听beforeunload事件
        window.addEventListener("beforeunload", blockBack)

        // 监听unload事件
        window.addEventListener("unload", blockBack)

        // 页面加载完成后额外建立保护层
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", function () {
            for (let i = 0; i < 50; i++) {
              history.pushState(null, null, location.href)
            }
          })
        } else {
          for (let i = 0; i < 50; i++) {
            history.pushState(null, null, location.href)
          }
        }
      })()

      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
