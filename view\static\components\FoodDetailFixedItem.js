Vue.component("food-detail-fixed-item", {
  props: ["systemLanguage", "foodInfoItem", "cellClass", "level"],
  computed: {
    calcFixedList() {
      const { foodList = [], mListList = [] } = this.foodInfoItem
      const [fRequired, fOptional] = this.groupByHasRequiredItem(foodList)
      const [mRequired, mOptional] = this.groupByHasRequiredItem(mListList)
      return {
        foodList: fOptional,
        mListList: mOptional,
        foodRequiredList: fRequired,
        mRequiredList: mRequired
      }
    },
    keyMap() {
      const keys = [
        {
          nameType: "foodList",
          clickType: "foodList"
        },
        {
          nameType: "mList",
          clickType: "mListList"
        }
      ]
      return Object.values(this.calcFixedList).map((it, idx) => {
        const keyIdx = idx % 2
        return {
          list: it,
          key: idx,
          ...keys[keyIdx]
        }
      })
    }
  },
  methods: {
    // 将固定细项分组,根据有无必选细项以及type
    groupByHasRequiredItem(list = []) {
      return list.reduce(
        (pre, cur) => {
          if (cur.hasRequiredItem) {
            pre[0].push(cur)
          } else pre[1].push(cur)
          return pre
        },
        [[], []]
      )
    },
    openEdit(...args) {
      switch (this.level) {
        case 1: {
          return app.fixXiShowDia(...args)
        }
        case 2: {
          return app.secOnPicker(app.clickXiItem, ...args)
        }
        default:
          return null
      }
    },
    openAddDia(item, type) {
      // 若已经选中则不能通过点击name 打开弹窗,仅铅笔icon点击打开
      if (item.selected) return false
      this.openEdit(item, type)
    },
    showTimeoutStyle(...args) {
      return app.showTimeoutStyle(...args)
    },
    inListTitle(...args) {
      return app.inListTitle(...args)
    },
    showXiPrice(...args) {
      return app.showXiPrice(...args)
    },
    priceName(...args) {
      return app.priceName(...args)
    }
  },
  template: `
    <div
      class="food_info_warp_content_infoPoints"
    >
      <div class="infoPoints_lable">{{ systemLanguage.singleItemTaste }}</div>
      <div class="infoPoints_content">
      <template v-for="type in keyMap" :key="type.key">
        <div
          v-for="item in type.list"
          :key="item.fCode"
          class="infoPoints_content_cell"
          :class="cellClass"
        >
          <div
            :class="[ {infoPoints_select:item.selected}, 'default_infoPoints', {hideBack:item.isExpired||item.itemCtrl}]"
            @click.stop="openAddDia(item,type.clickType)"
          >
            <div
              class="food_disable_box"
              v-if="showTimeoutStyle(item, foodInfoItem)"
            ></div>
            <img
              src="../static/img/newImage/custom.jpg"
              alt=""
              class="infoPoints_addicon"
              @click.stop="openEdit(item,type.clickType)"
              v-if="item.listSelect&&!item.itemCtrl&&item.selected"
            />
            <!-- 售罄itemCtrl -->
            <img
              v-if="item.itemCtrl"
              src="../static/img/newImage/soldOutList.jpg"
              alt=""
              class="soldoutList"
            />
            <div class="xi_Title">
              {{inListTitle(item)}}
              <!-- 细项价钱 -->
              <template v-if="item[priceName(type.nameType)]">
                {{showXiPrice(item[priceName(type.nameType)])}}
              </template>
            </div>
          </div>
        </div>
      
      </template>
      </div>
    </div>
  
    
  `
})
