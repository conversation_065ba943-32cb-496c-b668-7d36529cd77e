* {
  margin: 0;
  padding: 0;
  /* -webkit-overflow-scrolling: touch; */
  font-family: Arial, Helvetica, sans-serif;
}
:root {
  --styleColor: #ed6211;
}
#app {
  position: absolute;
  height: 100%;
  width: 100%;
}
[v-cloak] {
  display: none !important;
}
.container {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  height: 100%;
}

.header,
.main,
.footer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.pay-fail-logo {
  width: 5rem;
  align-self: center;
}
.pay-fail-title {
  padding: 0.4rem 0;
  font-size: 0.7rem;
}
.pay-fail-sub-box {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  width: 90%;
  height: 1.3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0.1rem 0;
  border-radius: 0.3rem;
}
.pay-fail-sub-title {
  font-size: 0.38rem;
  color: #777777;
  margin-right: 0.5rem;
}
.pay-fail-sub-value {
  min-width: 2rem;
  min-height: 0.8rem;
  display: flex;
  align-items: center;
  font-size: 0.5rem;
  color: var(--styleColor);
  position: relative;
}

.pay-fail-tips {
  width: 85%;
}
.back-btn {
  width: 80%;
  padding: 0.25rem 0;
  border: 1px solid #f15352;
  /* color: var(--styleColor); */
  color: #fff;
  font-size: 0.39rem;
  outline: none !important;
  font-weight: 500;
  border-radius: 0.15rem;
  background-color: #f15352;
  /* position: relative; */
}
