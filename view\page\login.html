<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title></title>
    <script src="../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../static/js/getParameter.js"></script>

    <link rel="stylesheet" href="../static/css/manager_style.css" />
    <!--    <script src="../static/js/form.js"></script>-->

    <!-- 弹窗组件 -->
    <link rel="stylesheet" href="../static/plugins/toastr/toastr.css" />
    <script src="../static/plugins/toastr/toastr.min.js"></script>
  </head>

  <body>
    <div class="contentBox">
      <!-- <div class="left_content">
  <div class="loginImg"></div>
</div> -->
      <header>
        <div class="leftTitle">
          <section>
            <span class="oneLine"></span>
            <span class="twoLine"></span>
          </section>
        </div>
        <div class="centerTitle">
          <section>
            <span></span>
          </section>
        </div>
      </header>
      <div class="right_content">
        <div class="loginContent">
          <form
            class="needs-validation"
            novalidate=""
            action="../user/login"
            method="post"
            onSubmit="beforeSubmit(this);"
          >
            <h3>Login</h3>
            <!-- <p>Please enter your username and password to continue</p> -->
            <div class="mb-3">
              <!-- <label for="validationCustom08">UserName</label> -->
              <div class="input-group">
                <!-- <input type="text" class="noneInput">
          <input type="password" class="noneInput"> -->
                <i class="logoBox">
                  <img src="../static/img/logo/username.jpg" style="width: 1.3rem" />
                </i>
                <input type="text" name="username" id="username" placeholder="Username" required />
              </div>
            </div>
            <div class="mb-2">
              <!-- <label for="validationCustom09">Password</label> -->
              <div class="input-group">
                <i class="logoBox">
                  <img src="../static/img/logo/password.jpg" style="width: 1.3rem" />
                </i>
                <input
                  type="password"
                  name="password"
                  id="password"
                  placeholder="Password"
                  autocomplete="new-password"
                  required
                />
              </div>
            </div>
            <button class="submitBtn" type="submit">Sign In</button>

            <!-- <div id="errorStr" style="text-align: center; color: red;"></div> -->
          </form>
        </div>
      </div>
    </div>
  </body>
  <script type="text/javascript">
    let centerTitle = "Dohtonbori"
    $(function () {
      $.getJSON("../static/utils/config.json", function (data) {
        document.title = data.CMS.DocumentTitle || ""
        centerTitle = data.CMS.LoginHeadline
        $(".centerTitle section span").text(centerTitle)
      })
      //自定义参数
      toastr.options = {
        closeButton: false, //是否显示关闭按钮（提示框右上角关闭按钮）。
        debug: false, //是否为调试。
        progressBar: true, //是否显示进度条（设置关闭的超时时间进度条）
        positionClass: "toast-top-center", //消息框在页面显示的位置
        onclick: null, //点击消息框自定义事件
        showDuration: "300", //显示动作时间
        hideDuration: "1000", //隐藏动作时间
        timeOut: "2000", //自动关闭超时时间
        extendedTimeOut: "1000",
        showEasing: "swing",
        hideEasing: "linear",
        showMethod: "fadeIn", //显示的方式，和jquery相同
        hideMethod: "fadeOut" //隐藏的方式，和jquery相同
        //等其他参数
      }
    })

    function beforeSubmit(form) {
      if (form.username.value == "") {
        toastr.error("Please provide a username!")
        form.username.focus()
        return false
      }
      if (form.password.value == "") {
        toastr.error("Please provide a password!")
        form.password.focus()
        return false
      }
    }
    var failed = getParameter("failed")
    if (failed == "true") {
      console.log("555")
      setTimeout(function () {
        toastr.error("Username or Password invalid!")
      }, 100)
      // document.getElementById('errorStr').innerText =
      //     'Username or Password invalid';
    }
  </script>
</html>
