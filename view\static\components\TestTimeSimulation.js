Vue.component("test-time-simulation", {
  props: {
    systemLanguage: {
      type: Object,
      default: () => ({})
    },
    storeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isSimulating: false, // 是否正在进行时间模拟
      isExpanded: false, // 控制组件的展开/折叠状态
      isAnimating: false // 控制动画状态以防止快速点击
    }
  },
  created() {},

  computed: {
    displayText() {
      let { testNumMockSetTxt, testNumMockTime } = this.systemLanguage
      let { pickupTime } = this.storeData
      if (pickupTime) {
        return `${testNumMockTime} ${pickupTime}`
      }
      return testNumMockSetTxt
    },
    containerClasses() {
      return [
        "test-time-simulation-container",
        {
          "test-time-simulation-container--expanded": this.isExpanded,
          "test-time-simulation-container--simulating": this.storeData.pickupTime,
          "test-time-simulation-container--animating": this.isAnimating
        }
      ]
    }
  },
  mounted() {},

  beforeDestroy() {},
  watch: {},
  methods: {
    /**
     * @param {number} days - 需要生成的天数
     * @param {number} interval - 每个时间段的分钟间隔
     * @param {string} startTime - 每天的开始时间，格式为 "HH:mm"
     * @param {string} endTime - 每天的结束时间，格式为 "HH:mm"
     * @returns {Object} - 返回一个以 'MM/DD' 为键，时间段数组为值的对象
     */
    generateDateTimeMap(days = 5, interval = 5, startTime = "00:00", endTime = "23:59") {
      const dateTimeMap = {}
      const today = moment()
      for (let i = 0; i < days; i++) {
        const currentDay = today.clone().add(i, "days")
        const key = currentDay.format("MM/DD")
        dateTimeMap[key] = []
        const timeIterator = currentDay
          .clone()
          .startOf("day")
          .set({
            hour: moment(startTime, "HH:mm").hour(),
            minute: moment(startTime, "HH:mm").minute()
          })
        const endDateTime = currentDay
          .clone()
          .startOf("day")
          .set({
            hour: moment(endTime, "HH:mm").hour(),
            minute: moment(endTime, "HH:mm").minute()
          })
        while (timeIterator.isBefore(endDateTime)) {
          const slotStart = timeIterator.clone()
          timeIterator.add(interval, "minutes")
          const slotEnd = timeIterator.isAfter(endDateTime)
            ? endDateTime.clone()
            : timeIterator.clone()
          const startTimeString = slotStart.format("HH:mm")
          const endTimeString = slotEnd.format("HH:mm")
          dateTimeMap[key].push(`${startTimeString}-${endTimeString}`)
        }
      }
      return dateTimeMap
    },

    showTestTimePicker() {
      let { pickupTime: { pickupDayRange, pickupTimeInterval } = {} } = app.openTable
      let myDateTimeMap = this.generateDateTimeMap(pickupDayRange, pickupTimeInterval)
      let mockFormatTimeZone = moment().format("YYYY-MM-DD") + " 00:00:00"
      DeliveryTimePicker.option.checkTimeObj = {
        date: app.handleOpenTableLangEchoByStore("checkDate"),
        hourStrAndMinute: app.handleOpenTableLangEchoByStore("hourStrAndMinute")
      }
      DeliveryTimePicker.show(myDateTimeMap, undefined, mockFormatTimeZone)
    },
    // 切换组件展开/收起状态（带动画同步控制）
    toggleExpanded() {
      if (this.isAnimating) return
      this.isAnimating = true
      this.isExpanded = !this.isExpanded
      setTimeout(() => {
        this.isAnimating = false
      }, 450)
    },

    // 收起组件（带动画同步控制）
    collapsePanel() {
      if (this.isAnimating || !this.isExpanded) return
      return new Promise((resolve, reject) => {
        this.isAnimating = true
        this.isExpanded = false
        // 动画完成后重置动画状态（400ms 动画时长 + 50ms 缓冲）
        setTimeout(() => {
          this.isAnimating = false
          resolve()
        }, 450)
      })
    },
    // 点击遮罩层收起组件
    handleMaskClick() {
      this.collapsePanel()
    }
  },

  template: `
 <div>
    <div
      v-if="isExpanded"
      @click="handleMaskClick"
      class="test-time-simulation-mask"
    ></div>

    <div :class="containerClasses">
      <div @click="toggleExpanded" class="test-time-simulation-trigger">
        <span class="test-time-simulation-trigger-icon">🕐</span>
      </div>

      <div
        @click.stop="showTestTimePicker"
        class="test-time-simulation-panel-content"
      >
        <div class="test-time-simulation-time-display">
          <span
            :class="[
              'test-time-simulation-time-text',
              {
                'test-time-simulation-time-text--simulating': storeData.pickupTime
              }
            ]"
          >{{ displayText }}</span>
        </div>

        <div class="test-time-simulation-edit-btn">
          <span class="test-time-simulation-edit-icon">✏️</span>
        </div>
      </div>
    </div>
  </div>
  `
})
