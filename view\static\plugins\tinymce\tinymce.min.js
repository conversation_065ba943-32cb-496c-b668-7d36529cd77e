/**
 * TinyMCE version 6.1.0 (2022-06-29)
 */
!function(){"use strict";var e=function(e){if(null===e)return"null";if(void 0===e)return"undefined";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t},t=function(e){return{eq:e}},n=t((function(e,t){return e===t})),o=function(e){return t((function(t,n){if(t.length!==n.length)return!1;for(var o=t.length,r=0;r<o;r++)if(!e.eq(t[r],n[r]))return!1;return!0}))},r=function(e){return t((function(r,s){var a=Object.keys(r),i=Object.keys(s);if(!function(e,n){return function(e,n){return t((function(t,o){return e.eq(n(t),n(o))}))}(o(e),(function(e){return function(e,t){return Array.prototype.slice.call(e).sort(t)}(e,n)}))}(n).eq(a,i))return!1;for(var l=a.length,d=0;d<l;d++){var c=a[d];if(!e.eq(r[c],s[c]))return!1}return!0}))},s=t((function(t,n){if(t===n)return!0;var a=e(t);return a===e(n)&&(function(e){return-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(e)}(a)?t===n:"array"===a?o(s).eq(t,n):"object"===a&&r(s).eq(t,n))}));const a=Object.getPrototypeOf,i=(e,t,n)=>{var o;return!!n(e,t.prototype)||(null===(o=e.constructor)||void 0===o?void 0:o.name)===t.name},l=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&i(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":t})(t)===e,d=e=>t=>typeof t===e,c=e=>t=>e===t,u=(e,t)=>f(e)&&i(e,t,((e,t)=>a(e)===t)),m=l("string"),f=l("object"),g=e=>u(e,Object),p=l("array"),h=c(null),b=d("boolean"),v=c(void 0),y=e=>null==e,C=e=>!y(e),w=d("function"),x=d("number"),k=(e,t)=>{if(p(e)){for(let n=0,o=e.length;n<o;++n)if(!t(e[n]))return!1;return!0}return!1},S=()=>{},_=(e,t)=>(...n)=>e(t.apply(null,n)),E=(e,t)=>n=>e(t(n)),N=e=>()=>e,R=e=>e,A=(e,t)=>e===t;function O(e,...t){return(...n)=>{const o=t.concat(n);return e.apply(null,o)}}const T=e=>t=>!e(t),B=e=>e(),D=e=>{e()},P=N(!1),L=N(!0);class M{constructor(e,t){this.tag=e,this.value=t}static some(e){return new M(!0,e)}static none(){return M.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?M.some(e(this.value)):M.none()}bind(e){return this.tag?e(this.value):M.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:M.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return C(e)?M.some(e):M.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}M.singletonNone=new M(!1);const I=Array.prototype.slice,F=Array.prototype.indexOf,U=Array.prototype.push,z=(e,t)=>F.call(e,t),j=(e,t)=>z(e,t)>-1,V=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return!0;return!1},H=(e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o},$=(e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)},q=(e,t)=>{for(let n=e.length-1;n>=0;n--)t(e[n],n)},W=(e,t)=>{const n=[],o=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?n:o).push(s)}return{pass:n,fail:o}},K=(e,t)=>{const n=[];for(let o=0,r=e.length;o<r;o++){const r=e[o];t(r,o)&&n.push(r)}return n},G=(e,t,n)=>(q(e,((e,o)=>{n=t(n,e,o)})),n),Y=(e,t,n)=>($(e,((e,o)=>{n=t(n,e,o)})),n),X=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++){const r=e[o];if(t(r,o))return M.some(r);if(n(r,o))break}return M.none()},Q=(e,t)=>X(e,t,P),J=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return M.some(n);return M.none()},Z=e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!p(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);U.apply(t,e[n])}return t},ee=(e,t)=>Z(H(e,t)),te=(e,t)=>{for(let n=0,o=e.length;n<o;++n)if(!0!==t(e[n],n))return!1;return!0},ne=e=>{const t=I.call(e,0);return t.reverse(),t},oe=(e,t)=>K(e,(e=>!j(t,e))),re=(e,t)=>{const n={};for(let o=0,r=e.length;o<r;o++){const r=e[o];n[String(r)]=t(r,o)}return n},se=(e,t)=>{const n=I.call(e,0);return n.sort(t),n},ae=(e,t)=>t>=0&&t<e.length?M.some(e[t]):M.none(),ie=e=>ae(e,0),le=e=>ae(e,e.length-1),de=w(Array.from)?Array.from:e=>I.call(e),ce=(e,t)=>{for(let n=0;n<e.length;n++){const o=t(e[n],n);if(o.isSome())return o}return M.none()},ue=Object.keys,me=Object.hasOwnProperty,fe=(e,t)=>{const n=ue(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}},ge=(e,t)=>pe(e,((e,n)=>({k:n,v:t(e,n)}))),pe=(e,t)=>{const n={};return fe(e,((e,o)=>{const r=t(e,o);n[r.k]=r.v})),n},he=e=>(t,n)=>{e[n]=t},be=(e,t,n,o)=>(fe(e,((e,r)=>{(t(e,r)?n:o)(e,r)})),{}),ve=(e,t)=>{const n={};return be(e,t,he(n),S),n},ye=(e,t)=>{const n=[];return fe(e,((e,o)=>{n.push(t(e,o))})),n},Ce=e=>ye(e,R),we=(e,t)=>xe(e,t)?M.from(e[t]):M.none(),xe=(e,t)=>me.call(e,t),ke=(e,t)=>xe(e,t)&&void 0!==e[t]&&null!==e[t],Se=e=>{const t={};return $(e,(e=>{t[e]={}})),ue(t)},_e=Array.isArray,Ee=(e,t,n)=>{let o,r;if(!e)return!1;if(n=n||e,void 0!==e.length){for(o=0,r=e.length;o<r;o++)if(!1===t.call(n,e[o],o,e))return!1}else for(o in e)if(xe(e,o)&&!1===t.call(n,e[o],o,e))return!1;return!0},Ne=(e,t)=>{const n=[];return Ee(e,((o,r)=>{n.push(t(o,r,e))})),n},Re=(e,t)=>{const n=[];return Ee(e,((o,r)=>{t&&!t(o,r,e)||n.push(o)})),n},Ae=(e,t)=>{if(e)for(let n=0,o=e.length;n<o;n++)if(e[n]===t)return n;return-1},Oe=(e,t,n,o)=>{let r=v(n)?e[0]:n;for(let n=0;n<e.length;n++)r=t.call(o,r,e[n],n);return r},Te=(e,t,n)=>{let o,r;for(o=0,r=e.length;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1},Be=e=>e[e.length-1],De=e=>{let t,n=!1;return(...o)=>(n||(n=!0,t=e.apply(null,o)),t)},Pe=()=>Le(0,0),Le=(e,t)=>({major:e,minor:t}),Me={nu:Le,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?Pe():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const o=e[n];if(o.test(t))return o}})(e,t);if(!n)return{major:0,minor:0};const o=e=>Number(t.replace(n,"$"+e));return Le(o(1),o(2))})(e,n)},unknown:Pe},Ie=(e,t)=>{const n=String(t).toLowerCase();return Q(e,(e=>e.search(n)))},Fe=(e,t,n)=>""===t||e.length>=t.length&&e.substr(n,n+t.length)===t,Ue=(e,t)=>-1!==e.indexOf(t),ze=(e,t)=>Fe(e,t,0),je=(e,t)=>Fe(e,t,e.length-t.length),Ve=e=>t=>t.replace(e,""),He=Ve(/^\s+|\s+$/g),$e=Ve(/^\s+/g),qe=Ve(/\s+$/g),We=e=>e.length>0,Ke=e=>!We(e),Ge=(e,t=10)=>{const n=parseInt(e,t);return isNaN(n)?M.none():M.some(n)},Ye=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Xe=e=>t=>Ue(t,e),Qe=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Ue(e,"edge/")&&Ue(e,"chrome")&&Ue(e,"safari")&&Ue(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Ye],search:e=>Ue(e,"chrome")&&!Ue(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Ue(e,"msie")||Ue(e,"trident")},{name:"Opera",versionRegexes:[Ye,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Xe("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Xe("firefox")},{name:"Safari",versionRegexes:[Ye,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Ue(e,"safari")||Ue(e,"mobile/"))&&Ue(e,"applewebkit")}],Je=[{name:"Windows",search:Xe("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Ue(e,"iphone")||Ue(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Xe("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Xe("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Xe("linux"),versionRegexes:[]},{name:"Solaris",search:Xe("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Xe("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Xe("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Ze={browsers:N(Qe),oses:N(Je)},et="Edge",tt="Chromium",nt="Opera",ot="Firefox",rt="Safari",st=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isEdge:o(et),isChromium:o(tt),isIE:o("IE"),isOpera:o(nt),isFirefox:o(ot),isSafari:o(rt)}},at=()=>st({current:void 0,version:Me.unknown()}),it=st,lt=(N(et),N(tt),N("IE"),N(nt),N(ot),N(rt),"Windows"),dt="Android",ct="Linux",ut="macOS",mt="Solaris",ft="FreeBSD",gt="ChromeOS",pt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isWindows:o(lt),isiOS:o("iOS"),isAndroid:o(dt),isMacOS:o(ut),isLinux:o(ct),isSolaris:o(mt),isFreeBSD:o(ft),isChromeOS:o(gt)}},ht=()=>pt({current:void 0,version:Me.unknown()}),bt=pt,vt=(N(lt),N("iOS"),N(dt),N(ct),N(ut),N(mt),N(ft),N(gt),e=>window.matchMedia(e).matches);let yt=De((()=>((e,t,n)=>{const o=Ze.browsers(),r=Ze.oses(),s=t.bind((e=>((e,t)=>ce(t.brands,(t=>{const n=t.brand.toLowerCase();return Q(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Me.nu(parseInt(t.version,10),0)})))})))(o,e))).orThunk((()=>((e,t)=>Ie(e,t).map((e=>{const n=Me.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e))).fold(at,it),a=((e,t)=>Ie(e,t).map((e=>{const n=Me.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e).fold(ht,bt),i=((e,t,n,o)=>{const r=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!r,a=e.isiOS()||e.isAndroid(),i=a||o("(pointer:coarse)"),l=r||!s&&a&&o("(min-device-width:768px)"),d=s||a&&!l,c=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),u=!d&&!l&&!c;return{isiPad:N(r),isiPhone:N(s),isTablet:N(l),isPhone:N(d),isTouch:N(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:N(c),isDesktop:N(u)}})(a,s,e,n);return{browser:s,os:a,deviceType:i}})(navigator.userAgent,M.from(navigator.userAgentData),vt)));const Ct=()=>yt(),wt=navigator.userAgent,xt=Ct(),kt=xt.browser,St=xt.os,_t=xt.deviceType,Et=-1!==wt.indexOf("Windows Phone"),Nt={transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",documentMode:kt.isIE()?document.documentMode||7:10,cacheSuffix:null,container:null,canHaveCSP:!kt.isIE(),windowsPhone:Et,browser:{current:kt.current,version:kt.version,isChromium:kt.isChromium,isEdge:kt.isEdge,isFirefox:kt.isFirefox,isIE:kt.isIE,isOpera:kt.isOpera,isSafari:kt.isSafari},os:{current:St.current,version:St.version,isAndroid:St.isAndroid,isChromeOS:St.isChromeOS,isFreeBSD:St.isFreeBSD,isiOS:St.isiOS,isLinux:St.isLinux,isMacOS:St.isMacOS,isSolaris:St.isSolaris,isWindows:St.isWindows},deviceType:{isDesktop:_t.isDesktop,isiPad:_t.isiPad,isiPhone:_t.isiPhone,isPhone:_t.isPhone,isTablet:_t.isTablet,isTouch:_t.isTouch,isWebView:_t.isWebView}},Rt=/^\s*|\s*$/g,At=e=>null==e?"":(""+e).replace(Rt,""),Ot=(e,t)=>t?!("array"!==t||!_e(e))||typeof e===t:void 0!==e,Tt=function(e,t,n,o){o=o||this,e&&(n&&(e=e[n]),Ee(e,((e,r)=>{if(!1===t.call(o,e,r,n))return!1;Tt(e,t,n,o)})))},Bt={trim:At,isArray:_e,is:Ot,toArray:e=>{if(_e(e))return e;{const t=[];for(let n=0,o=e.length;n<o;n++)t[n]=e[n];return t}},makeMap:(e,t,n)=>{let o;for(t=t||",","string"==typeof(e=e||[])&&(e=e.split(t)),n=n||{},o=e.length;o--;)n[e[o]]={};return n},each:Ee,map:Ne,grep:Re,inArray:Ae,hasOwn:xe,extend:(e,...t)=>{for(let n=0;n<t.length;n++){const o=t[n];for(const t in o)if(xe(o,t)){const n=o[t];void 0!==n&&(e[t]=n)}}return e},walk:Tt,resolve:(e,t)=>{let n,o;for(t=t||window,n=0,o=(e=e.split(".")).length;n<o&&(t=t[e[n]]);n++);return t},explode:(e,t)=>!e||Ot(e,"array")?e:Ne(e.split(t||","),At),_addCacheSuffix:e=>{const t=Nt.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Dt=(e,t,n=A)=>e.exists((e=>n(e,t))),Pt=(e,t,n)=>e.isSome()&&t.isSome()?M.some(n(e.getOrDie(),t.getOrDie())):M.none(),Lt=(e,t)=>e?M.some(t):M.none();"undefined"!=typeof window?window:Function("return this;")();const Mt=e=>e.dom.nodeName.toLowerCase(),It=e=>e.dom.nodeType,Ft=e=>t=>It(t)===e,Ut=Ft(1),zt=Ft(3),jt=Ft(9),Vt=Ft(11),Ht=e=>t=>Ut(t)&&Mt(t)===e,$t=(e,t,n)=>{if(!(m(n)||b(n)||x(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},qt=(e,t,n)=>{$t(e.dom,t,n)},Wt=(e,t)=>{const n=e.dom;fe(t,((e,t)=>{$t(n,t,e)}))},Kt=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},Gt=(e,t)=>M.from(Kt(e,t)),Yt=(e,t)=>{const n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},Xt=(e,t)=>{e.dom.removeAttribute(t)},Qt=e=>Y(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),Jt=(e,t)=>{const n=Kt(e,t);return void 0===n||""===n?[]:n.split(" ")},Zt=e=>void 0!==e.dom.classList,en=e=>Jt(e,"class"),tn=(e,t)=>((e,t,n)=>{const o=Jt(e,t).concat([n]);return qt(e,t,o.join(" ")),!0})(e,"class",t),nn=(e,t)=>((e,t,n)=>{const o=K(Jt(e,t),(e=>e!==n));return o.length>0?qt(e,t,o.join(" ")):Xt(e,t),!1})(e,"class",t),on=(e,t)=>{Zt(e)?e.dom.classList.add(t):tn(e,t)},rn=e=>{0===(Zt(e)?e.dom.classList:en(e)).length&&Xt(e,"class")},sn=(e,t)=>{Zt(e)?e.dom.classList.remove(t):nn(e,t),rn(e)},an=(e,t)=>Zt(e)&&e.dom.classList.contains(t),ln=e=>void 0!==e.style&&w(e.style.getPropertyValue),dn=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},cn=(e,t)=>{const n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return dn(n.childNodes[0])},un=(e,t)=>{const n=(t||document).createElement(e);return dn(n)},mn=(e,t)=>{const n=(t||document).createTextNode(e);return dn(n)},fn=dn,gn=(e,t,n)=>M.from(e.dom.elementFromPoint(t,n)).map(dn),pn=(e,t)=>{const n=[],o=e=>(n.push(e),t(e));let r=t(e);do{r=r.bind(o)}while(r.isSome());return n},hn=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},bn=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,vn=(e,t)=>e.dom===t.dom,yn=(e,t)=>{const n=e.dom,o=t.dom;return n!==o&&n.contains(o)},Cn=e=>fn(e.dom.ownerDocument),wn=e=>jt(e)?e:Cn(e),xn=e=>fn(wn(e).dom.defaultView),kn=e=>M.from(e.dom.parentNode).map(fn),Sn=e=>M.from(e.dom.previousSibling).map(fn),_n=e=>M.from(e.dom.nextSibling).map(fn),En=e=>ne(pn(e,Sn)),Nn=e=>pn(e,_n),Rn=e=>H(e.dom.childNodes,fn),An=(e,t)=>{const n=e.dom.childNodes;return M.from(n[t]).map(fn)},On=e=>An(e,0),Tn=e=>An(e,e.dom.childNodes.length-1),Bn=e=>e.dom.childNodes.length,Dn=e=>Vt(e)&&C(e.dom.host),Pn=w(Element.prototype.attachShadow)&&w(Node.prototype.getRootNode),Ln=N(Pn),Mn=Pn?e=>fn(e.dom.getRootNode()):wn,In=e=>Dn(e)?e:(e=>{const t=e.dom.head;if(null==t)throw new Error("Head is not available yet");return fn(t)})(wn(e)),Fn=e=>fn(e.dom.host),Un=e=>{if(Ln()&&C(e.target)){const t=fn(e.target);if(Ut(t)&&zn(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return ie(t)}}return M.from(e.target)},zn=e=>C(e.dom.shadowRoot),jn=e=>{const t=zt(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return(e=>{const t=Mn(e);return Dn(t)?M.some(t):M.none()})(fn(t)).fold((()=>n.body.contains(t)),E(jn,Fn))},Vn=(e,t,n)=>{if(!m(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);ln(e)&&e.style.setProperty(t,n)},Hn=(e,t,n)=>{const o=e.dom;Vn(o,t,n)},$n=(e,t)=>{const n=e.dom;fe(t,((e,t)=>{Vn(n,t,e)}))},qn=(e,t)=>{const n=e.dom,o=window.getComputedStyle(n).getPropertyValue(t);return""!==o||jn(e)?o:Wn(n,t)},Wn=(e,t)=>ln(e)?e.style.getPropertyValue(t):"",Kn=(e,t)=>{const n=e.dom,o=Wn(n,t);return M.from(o).filter((e=>e.length>0))},Gn=e=>{const t={},n=e.dom;if(ln(n))for(let e=0;e<n.style.length;e++){const o=n.style.item(e);t[o]=n.style[o]}return t},Yn=(e,t)=>{((e,t)=>{ln(e)&&e.style.removeProperty(t)})(e.dom,t),Dt(Gt(e,"style").map(He),"")&&Xt(e,"style")},Xn=(e,t)=>{kn(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},Qn=(e,t)=>{_n(e).fold((()=>{kn(e).each((e=>{Zn(e,t)}))}),(e=>{Xn(e,t)}))},Jn=(e,t)=>{On(e).fold((()=>{Zn(e,t)}),(n=>{e.dom.insertBefore(t.dom,n.dom)}))},Zn=(e,t)=>{e.dom.appendChild(t.dom)},eo=(e,t)=>{$(t,(t=>{Zn(e,t)}))},to=e=>{e.dom.textContent="",$(Rn(e),(e=>{no(e)}))},no=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},oo=e=>{const t=Rn(e);var n,o;t.length>0&&(n=e,$(o=t,((e,t)=>{const r=0===t?n:o[t-1];Qn(r,e)}))),no(e)},ro=e=>e.dom.innerHTML,so=(e,t)=>{const n=Cn(e).dom,o=fn(n.createDocumentFragment()),r=((e,t)=>{const n=(t||document).createElement("div");return n.innerHTML=e,Rn(fn(n))})(t,n);eo(o,r),to(e),Zn(e,o)},ao=(e,t,n,o)=>((e,t,n,o,r)=>{const s=((e,t)=>n=>{e(n)&&t((e=>{const t=fn(Un(e).getOr(e.target)),n=()=>e.stopPropagation(),o=()=>e.preventDefault(),r=_(o,n);return((e,t,n,o,r,s,a)=>({target:e,x:t,y:n,stop:o,prevent:r,kill:s,raw:a}))(t,e.clientX,e.clientY,n,o,r,e)})(n))})(n,o);return e.dom.addEventListener(t,s,false),{unbind:O(io,e,t,s,false)}})(e,t,n,o),io=(e,t,n,o)=>{e.dom.removeEventListener(t,n,o)},lo=(e,t)=>({left:e,top:t,translate:(n,o)=>lo(e+n,t+o)}),co=lo,uo=(e,t)=>void 0!==e?e:void 0!==t?t:0,mo=e=>{const t=e.dom,n=t.ownerDocument.body;return n===t?co(n.offsetLeft,n.offsetTop):jn(e)?(e=>{const t=e.getBoundingClientRect();return co(t.left,t.top)})(t):co(0,0)},fo=e=>{const t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return co(n,o)},go=(e,t,n)=>{const o=(void 0!==n?n.dom:document).defaultView;o&&o.scrollTo(e,t)},po=(e,t)=>{Ct().browser.isSafari()&&w(e.dom.scrollIntoViewIfNeeded)?e.dom.scrollIntoViewIfNeeded(!1):e.dom.scrollIntoView(t)},ho=(e,t,n,o)=>({x:e,y:t,width:n,height:o,right:e+n,bottom:t+o}),bo=e=>{const t=void 0===e?window:e,n=t.document,o=fo(fn(n));return(e=>{const t=void 0===e?window:e;return Ct().browser.isFirefox()?M.none():M.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,n=e.clientWidth,r=e.clientHeight;return ho(o.left,o.top,n,r)}),(e=>ho(Math.max(e.pageLeft,o.left),Math.max(e.pageTop,o.top),e.width,e.height)))},vo=e=>t=>!!t&&t.nodeType===e,yo=e=>!!e&&!Object.getPrototypeOf(e),Co=vo(1),wo=e=>{const t=e.map((e=>e.toLowerCase()));return e=>{if(e&&e.nodeName){const n=e.nodeName.toLowerCase();return j(t,n)}return!1}},xo=(e,t)=>{const n=t.toLowerCase().split(" ");return t=>{if(Co(t))for(let o=0;o<n.length;o++){const r=t.ownerDocument.defaultView.getComputedStyle(t,null);if((r?r.getPropertyValue(e):null)===n[o])return!0}return!1}},ko=e=>t=>Co(t)&&t.hasAttribute(e),So=e=>Co(e)&&e.hasAttribute("data-mce-bogus"),_o=e=>Co(e)&&"TABLE"===e.tagName,Eo=e=>t=>{if(Co(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},No=wo(["textarea","input"]),Ro=vo(3),Ao=vo(4),Oo=vo(7),To=vo(8),Bo=vo(9),Do=vo(11),Po=wo(["br"]),Lo=wo(["img"]),Mo=Eo("true"),Io=Eo("false"),Fo=wo(["td","th"]),Uo=wo(["video","audio","object","embed"]),zo=Ct().browser,jo=e=>Q(e,Ut),Vo=(e,t)=>e.children&&j(e.children,t);var Ho=(e,t,n,o,r)=>e(n,o)?M.some(n):w(r)&&r(n)?M.none():t(n,o,r);const $o=(e,t,n)=>{let o=e.dom;const r=w(n)?n:P;for(;o.parentNode;){o=o.parentNode;const e=fn(o);if(t(e))return M.some(e);if(r(e))break}return M.none()},qo=(e,t,n)=>Ho(((e,t)=>t(e)),$o,e,t,n),Wo=(e,t,n)=>$o(e,(e=>hn(e,t)),n),Ko=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return bn(n)?M.none():M.from(n.querySelector(e)).map(fn)})(t,e),Go=(e,t,n)=>Ho(((e,t)=>hn(e,t)),Wo,e,t,n),Yo=(e,t={})=>{let n=0;const o={},r=fn(e),s=wn(r),a=t.maxLoadTime||5e3,i=i=>new Promise(((l,d)=>{let c;const u=Bt._addCacheSuffix(i),m=(e=>we(o,e).getOrThunk((()=>({id:"mce-u"+n++,passed:[],failed:[],count:0}))))(u);o[u]=m,m.count++;const f=(e,t)=>{$(e,D),m.status=t,m.passed=[],m.failed=[],c&&(c.onload=null,c.onerror=null,c=null)},g=()=>f(m.passed,2),p=()=>f(m.failed,3),h=()=>{var t;t=h,(()=>{const t=e.styleSheets;let n=t.length;for(;n--;){const e=t[n].ownerNode;if(e&&e.id===c.id)return g(),!0}return!1})()||(Date.now()-v<a?setTimeout(t):p())};if(l&&m.passed.push(l),d&&m.failed.push(d),1===m.status)return;if(2===m.status)return void g();if(3===m.status)return void p();m.status=1;const b=un("link",s.dom);Wt(b,{rel:"stylesheet",type:"text/css",id:m.id});const v=Date.now();var y;t.contentCssCors&&qt(b,"crossOrigin","anonymous"),t.referrerPolicy&&qt(b,"referrerpolicy",t.referrerPolicy),c=b.dom,c.onload=h,c.onerror=p,y=b,Zn(In(r),y),qt(b,"href",u)})),l=e=>{const t=Bt._addCacheSuffix(e);we(o,t).each((e=>{0==--e.count&&(delete o[t],(e=>{const t=In(r);Ko(t,"#"+e).each(no)})(e.id))}))};return{load:i,loadAll:e=>Promise.allSettled(H(e,(e=>i(e).then(N(e))))).then((e=>{const t=W(e,(e=>"fulfilled"===e.status));return t.fail.length>0?Promise.reject(H(t.fail,(e=>e.reason))):H(t.pass,(e=>e.value))})),unload:l,unloadAll:e=>{$(e,(e=>{l(e)}))},_setReferrerPolicy:e=>{t.referrerPolicy=e}}},Xo=(()=>{const e=new WeakMap;return{forElement:(t,n)=>{const o=Mn(t).dom;return M.from(e.get(o)).getOrThunk((()=>{const t=Yo(o,n);return e.set(o,t),t}))}}})();class Qo{constructor(e,t){this.node=e,this.rootNode=t,this.current=this.current.bind(this),this.next=this.next.bind(this),this.prev=this.prev.bind(this),this.prev2=this.prev2.bind(this)}current(){return this.node}next(e){return this.node=this.findSibling(this.node,"firstChild","nextSibling",e),this.node}prev(e){return this.node=this.findSibling(this.node,"lastChild","previousSibling",e),this.node}prev2(e){return this.node=this.findPreviousNode(this.node,e),this.node}findSibling(e,t,n,o){let r,s;if(e){if(!o&&e[t])return e[t];if(e!==this.rootNode){if(r=e[n],r)return r;for(s=e.parentNode;s&&s!==this.rootNode;s=s.parentNode)if(r=s[n],r)return r}}}findPreviousNode(e,t){let n,o,r;if(e){if(n=e.previousSibling,this.rootNode&&n===this.rootNode)return;if(n){if(!t)for(r=n.lastChild;r;r=r.lastChild)if(!r.lastChild)return r;return n}if(o=e.parentNode,o&&o!==this.rootNode)return o}}}const Jo=["pre"].concat(["h1","h2","h3","h4","h5","h6"]),Zo=e=>{let t;return n=>(t=t||re(e,L),xe(t,Mt(n)))},er=Zo(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),tr=e=>Ut(e)&&!er(e),nr=e=>Ut(e)&&"br"===Mt(e),or=Zo(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),rr=Zo(["ul","ol","dl"]),sr=Zo(["li","dd","dt"]),ar=Zo(["thead","tbody","tfoot"]),ir=Zo(["td","th"]),lr=Zo(["pre","script","textarea","style"]),dr=Zo(Jo),cr=e=>dr(e)||tr(e),ur=(e,t,n)=>Wo(e,t,n).isSome(),mr="\ufeff",fr="\xa0",gr=e=>e===mr,pr=mr,hr=gr,br=e=>e.replace(/\uFEFF/g,""),vr=Co,yr=Ro,Cr=e=>(yr(e)&&(e=e.parentNode),vr(e)&&e.hasAttribute("data-mce-caret")),wr=e=>yr(e)&&hr(e.data),xr=e=>Cr(e)||wr(e),kr=e=>e.firstChild!==e.lastChild||!Po(e.firstChild),Sr=e=>{const t=e.container();return!!Ro(t)&&(t.data.charAt(e.offset())===pr||e.isAtStart()&&wr(t.previousSibling))},_r=e=>{const t=e.container();return!!Ro(t)&&(t.data.charAt(e.offset()-1)===pr||e.isAtEnd()&&wr(t.nextSibling))},Er=e=>yr(e)&&e.data[0]===pr,Nr=e=>yr(e)&&e.data[e.data.length-1]===pr,Rr=e=>e&&e.hasAttribute("data-mce-caret")?((e=>{const t=e.getElementsByTagName("br"),n=t[t.length-1];So(n)&&n.parentNode.removeChild(n)})(e),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("data-mce-style"),e.removeAttribute("_moz_abspos"),e):null,Ar=e=>Cr(e.startContainer),Or=Mo,Tr=Io,Br=Po,Dr=Ro,Pr=wo(["script","style","textarea"]),Lr=wo(["img","input","textarea","hr","iframe","video","audio","object","embed"]),Mr=wo(["table"]),Ir=xr,Fr=e=>!Ir(e)&&(Dr(e)?!Pr(e.parentNode):Lr(e)||Br(e)||Mr(e)||Ur(e)),Ur=e=>!1===(e=>Co(e)&&"true"===e.getAttribute("unselectable"))(e)&&Tr(e),zr=(e,t)=>Fr(e)&&((e,t)=>{for(e=e.parentNode;e&&e!==t;e=e.parentNode){if(Ur(e))return!1;if(Or(e))return!0}return!0})(e,t),jr=/^[ \t\r\n]*$/,Vr=e=>jr.test(e),Hr=e=>"\n"===e||"\r"===e,$r=(e,t=4,n=!0,o=!0)=>{const r=((e,t)=>t<=0?"":new Array(t+1).join(" "))(0,t),s=e.replace(/\t/g,r),a=Y(s,((e,t)=>(e=>-1!==" \f\t\v".indexOf(e))(t)||t===fr?e.pcIsSpace||""===e.str&&n||e.str.length===s.length-1&&o||((e,t)=>t<e.length&&t>=0&&Hr(e[t]))(s,e.str.length+1)?{pcIsSpace:!1,str:e.str+fr}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:Hr(t),str:e.str+t}),{pcIsSpace:!1,str:""});return a.str},qr=(e,t)=>Fr(e)&&!1===((e,t)=>Ro(e)&&Vr(e.data)&&!1===((e,t)=>{const n=fn(t),o=fn(e);return ur(o,"pre,code",O(vn,n))})(e,t))(e,t)||(e=>Co(e)&&"A"===e.nodeName&&!e.hasAttribute("href")&&(e.hasAttribute("name")||e.hasAttribute("id")))(e)||Wr(e),Wr=ko("data-mce-bookmark"),Kr=ko("data-mce-bogus"),Gr=("data-mce-bogus","all",e=>Co(e)&&"all"===e.getAttribute("data-mce-bogus"));const Yr=(e,t=!0)=>((e,t)=>{let n=0;if(qr(e,e))return!1;{let o=e.firstChild;if(!o)return!0;const r=new Qo(o,e);do{if(t){if(Gr(o)){o=r.next(!0);continue}if(Kr(o)){o=r.next();continue}}if(Po(o))n++,o=r.next();else{if(qr(o,e))return!1;o=r.next()}}while(o);return n<=1}})(e.dom,t),Xr=(e,t)=>C(e)&&(qr(e,t)||tr(fn(e))),Qr=e=>(e=>"span"===e.nodeName.toLowerCase())(e)&&"bookmark"===e.getAttribute("data-mce-type"),Jr=(e,t,n)=>{const o=n||t;if(Co(t)&&Qr(t))return t;const r=t.childNodes;for(let t=r.length-1;t>=0;t--)Jr(e,r[t],o);if(Co(t)){const e=t.childNodes;1===e.length&&Qr(e[0])&&t.parentNode.insertBefore(e[0],t)}return(e=>Do(e)||Bo(e))(t)||qr(t,o)||(e=>!!Co(e)&&e.childNodes.length>0)(t)||((e,t)=>Ro(e)&&e.data.length>0&&((e,t)=>{const n=new Qo(e,t).prev(!1),o=new Qo(e,t).next(!1),r=v(n)||Xr(n,t),s=v(o)||Xr(o,t);return r&&s})(e,t))(t,o)||e.remove(t),t},Zr=Bt.makeMap,es=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,ts=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,ns=/[<>&\"\']/g,os=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,rs={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"},ss={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},as={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"},is=(e,t)=>{let n,o,r;const s={};if(e){for(e=e.split(","),t=t||10,n=0;n<e.length;n+=2)o=String.fromCharCode(parseInt(e[n],t)),ss[o]||(r="&"+e[n+1]+";",s[o]=r,s[r]=o);return s}},ls=is("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32),ds=(e,t)=>e.replace(t?es:ts,(e=>ss[e]||e)),cs=(e,t)=>e.replace(t?es:ts,(e=>e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":ss[e]||"&#"+e.charCodeAt(0)+";")),us=(e,t,n)=>(n=n||ls,e.replace(t?es:ts,(e=>ss[e]||n[e]||e))),ms={encodeRaw:ds,encodeAllRaw:e=>(""+e).replace(ns,(e=>ss[e]||e)),encodeNumeric:cs,encodeNamed:us,getEncodeFunc:(e,t)=>{const n=is(t)||ls,o=Zr(e.replace(/\+/g,","));return o.named&&o.numeric?(e,t)=>e.replace(t?es:ts,(e=>void 0!==ss[e]?ss[e]:void 0!==n[e]?n[e]:e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";")):o.named?t?(e,t)=>us(e,t,n):us:o.numeric?cs:ds},decode:e=>e.replace(os,((e,t)=>t?(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))>65535?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):rs[t]||String.fromCharCode(t):as[e]||ls[e]||(e=>{const t=un("div").dom;return t.innerHTML=e,t.textContent||t.innerText||e})(e)))},fs={},gs={},ps=Bt.makeMap,hs=Bt.each,bs=Bt.extend,vs=Bt.explode,ys=Bt.inArray,Cs=(e,t)=>(e=Bt.trim(e))?e.split(t||" "):[],ws=(e,t)=>{const n=ps(e," ",ps(e.toUpperCase()," "));return bs(n,t)},xs=e=>ws("td th li dt dd figcaption caption details summary",e.getTextBlockElements()),ks=(e,t)=>{let n;return e&&(n={},"string"==typeof e&&(e={"*":e}),hs(e,((e,o)=>{n[o]=n[o.toUpperCase()]="map"===t?ps(e,/[, ]/):vs(e,/[, ]/)}))),n},Ss=e=>{var t;const n={},o={};let r=[];const s={},a={},i=(t,n,o)=>{let r=e[t];return r?r=ps(r,/[, ]/,ps(r.toUpperCase(),/[, ]/)):(r=fs[t],r||(r=ws(n,o),fs[t]=r)),r},l=null!==(t=(e=e||{}).schema)&&void 0!==t?t:"html5",d=(e=>{const t={};let n,o,r,s,a,i;const l=(e,o="",r="")=>{const s=Cs(r),a=Cs(e);let i=a.length;for(;i--;){const e=Cs([n,o].join(" "));t[a[i]]={attributes:re(e,(()=>({}))),attributesOrder:e,children:re(s,N(gs))}}},d=(e,n)=>{const o=Cs(e),r=Cs(n);let s=o.length;for(;s--;){const e=t[o[s]];for(let t=0,n=r.length;t<n;t++)e.attributes[r[t]]={},e.attributesOrder.push(r[t])}};return fs[e]?fs[e]:(n="id accesskey class dir lang style tabindex title role",o="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",r="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(n+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",o+=" article aside details dialog figure main header footer hgroup section nav",r+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e&&(n+=" xml:lang",i="acronym applet basefont big font strike tt",r=[r,i].join(" "),hs(Cs(i),(e=>{l(e,"",r)})),a="center dir isindex noframes",o=[o,a].join(" "),s=[o,r].join(" "),hs(Cs(a),(e=>{l(e,"",s)}))),s=s||[o,r].join(" "),l("html","manifest","head body"),l("head","","base command link meta noscript script style title"),l("title hr noscript br"),l("base","href target"),l("link","href rel media hreflang type sizes hreflang"),l("meta","name http-equiv content charset"),l("style","media type scoped"),l("script","src async defer type charset"),l("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",s),l("address dt dd div caption","",s),l("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",r),l("blockquote","cite",s),l("ol","reversed start type","li"),l("ul","","li"),l("li","value",s),l("dl","","dt dd"),l("a","href target rel media hreflang type",r),l("q","cite",r),l("ins del","cite datetime",s),l("img","src sizes srcset alt usemap ismap width height"),l("iframe","src name width height",s),l("embed","src type width height"),l("object","data type typemustmatch name usemap form width height",[s,"param"].join(" ")),l("param","name value"),l("map","name",[s,"area"].join(" ")),l("area","alt coords shape href target rel media hreflang type"),l("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),l("colgroup","span","col"),l("col","span"),l("tbody thead tfoot","","tr"),l("tr","","td th"),l("td","colspan rowspan headers",s),l("th","colspan rowspan headers scope abbr",s),l("form","accept-charset action autocomplete enctype method name novalidate target",s),l("fieldset","disabled form name",[s,"legend"].join(" ")),l("label","form for",r),l("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),l("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?s:r),l("select","disabled form multiple name required size","option optgroup"),l("optgroup","disabled label","option"),l("option","disabled label selected value"),l("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),l("menu","type label",[s,"li"].join(" ")),l("noscript","",s),"html4"!==e&&(l("wbr"),l("ruby","",[r,"rt rp"].join(" ")),l("figcaption","",s),l("mark rt rp summary bdi","",r),l("canvas","width height",s),l("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[s,"track source"].join(" ")),l("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[s,"track source"].join(" ")),l("picture","","img source"),l("source","src srcset type media sizes"),l("track","kind src srclang label default"),l("datalist","",[r,"option"].join(" ")),l("article section nav aside main header footer","",s),l("hgroup","","h1 h2 h3 h4 h5 h6"),l("figure","",[s,"figcaption"].join(" ")),l("time","datetime",r),l("dialog","open",s),l("command","type label icon disabled checked radiogroup command"),l("output","for form name",r),l("progress","value max",r),l("meter","value min max low high optimum",r),l("details","open",[s,"summary"].join(" ")),l("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(d("script","language xml:space"),d("style","xml:space"),d("object","declare classid code codebase codetype archive standby align border hspace vspace"),d("embed","align name hspace vspace"),d("param","valuetype type"),d("a","charset name rev shape coords"),d("br","clear"),d("applet","codebase archive code object alt name width height align hspace vspace"),d("img","name longdesc align border hspace vspace"),d("iframe","longdesc frameborder marginwidth marginheight scrolling align"),d("font basefont","size color face"),d("input","usemap align"),d("select"),d("textarea"),d("h1 h2 h3 h4 h5 h6 div p legend caption","align"),d("ul","type compact"),d("li","type"),d("ol dl menu dir","compact"),d("pre","width xml:space"),d("hr","align noshade size width"),d("isindex","prompt"),d("table","summary width frame rules cellspacing cellpadding align bgcolor"),d("col","width align char charoff valign"),d("colgroup","width align char charoff valign"),d("thead","align char charoff valign"),d("tr","align char charoff valign bgcolor"),d("th","axis align char charoff valign nowrap bgcolor width height"),d("form","accept"),d("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),d("tfoot","align char charoff valign"),d("tbody","align char charoff valign"),d("area","nohref"),d("body","background bgcolor text link vlink alink")),"html4"!==e&&(d("input button select textarea","autofocus"),d("input textarea","placeholder"),d("a","download"),d("link script img","crossorigin"),d("img","loading"),d("iframe","sandbox seamless allowfullscreen loading")),"html4"!==e&&$([t.video,t.audio],(e=>{delete e.children.audio,delete e.children.video})),hs(Cs("a form meter progress dfn"),(e=>{t[e]&&delete t[e].children[e]})),delete t.caption.children.table,delete t.script,fs[e]=t,t)})(l);!1===e.verify_html&&(e.valid_elements="*[*]");const c=ks(e.valid_styles),u=ks(e.invalid_styles,"map"),m=ks(e.valid_classes,"map"),f=i("whitespace_elements","pre script noscript style textarea video audio iframe object code"),g=i("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),p=i("void_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),h=i("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls allowfullscreen"),b="td th iframe video audio object script code",v=i("non_empty_elements",b+" pre",p),y=i("move_caret_before_on_enter_elements",b+" table",p),C=i("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),w=i("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary",C),x=i("text_inline_elements","span strong b em i font s strike u var cite dfn code mark q sup sub samp");hs("script noscript iframe noframes noembed title style textarea xmp plaintext".split(" "),(e=>{a[e]=new RegExp("</"+e+"[^>]*>","gi")}));const k=e=>new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$"),S=e=>{let t,o,s,a,i,l,d,c,u,m,f,g,p,h,b,v,y,C;const w=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)])?$/,x=/^([!\-])?(\w+[\\:]:\w+|[^=~<]+)?(?:([=~<])(.*))?$/,S=/[*?+]/;if(e){const _=Cs(e,",");for(n["@"]&&(v=n["@"].attributes,y=n["@"].attributesOrder),t=0,o=_.length;t<o;t++)if(i=w.exec(_[t]),i){if(h=i[1],u=i[2],b=i[3],c=i[5],g={},p=[],l={attributes:g,attributesOrder:p},"#"===h&&(l.paddEmpty=!0),"-"===h&&(l.removeEmpty=!0),"!"===i[4]&&(l.removeEmptyAttrs=!0),v&&(fe(v,((e,t)=>{g[t]=e})),p.push.apply(p,y)),c)for(c=Cs(c,"|"),s=0,a=c.length;s<a;s++)if(i=x.exec(c[s]),i){if(d={},f=i[1],m=i[2].replace(/[\\:]:/g,":"),h=i[3],C=i[4],"!"===f&&(l.attributesRequired=l.attributesRequired||[],l.attributesRequired.push(m),d.required=!0),"-"===f){delete g[m],p.splice(ys(p,m),1);continue}h&&("="===h&&(l.attributesDefault=l.attributesDefault||[],l.attributesDefault.push({name:m,value:C}),d.defaultValue=C),"~"===h&&(l.attributesForced=l.attributesForced||[],l.attributesForced.push({name:m,value:C}),d.forcedValue=C),"<"===h&&(d.validValues=ps(C,"?"))),S.test(m)?(l.attributePatterns=l.attributePatterns||[],d.pattern=k(m),l.attributePatterns.push(d)):(g[m]||p.push(m),g[m]=d)}v||"@"!==u||(v=g,y=p),b&&(l.outputName=u,n[b]=l),S.test(u)?(l.pattern=k(u),r.push(l)):n[u]=l}}},_=e=>{r=[],$(ue(n),(e=>{delete n[e]})),S(e),hs(d,((e,t)=>{o[t]=e.children}))},E=e=>{const t=/^(~)?(.+)$/;e&&(fs.text_block_elements=fs.block_elements=null,hs(Cs(e,","),(e=>{const r=t.exec(e),a="~"===r[1],i=a?"span":"div",l=r[2];if(o[l]=o[i],s[l]=i,v[l.toUpperCase()]={},v[l]={},a||(w[l.toUpperCase()]={},w[l]={}),!n[l]){let e=n[i];e=bs({},e),delete e.removeEmptyAttrs,delete e.removeEmpty,n[l]=e}hs(o,((e,t)=>{e[i]&&(o[t]=e=bs({},o[t]),e[l]=e[i])}))})))},R=e=>{const t=/^([+\-]?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)\[([^\]]+)]$/;fs[l]=null,e&&hs(Cs(e,","),(e=>{const n=t.exec(e);let r,s;n&&(s=n[1],r=s?o[n[2]]:o[n[2]]={"#comment":{}},r=o[n[2]],hs(Cs(n[3],"|"),(e=>{"-"===s?delete r[e]:r[e]={}})))}))},A=e=>{let t,o=n[e];if(o)return o;for(t=r.length;t--;)if(o=r[t],o.pattern.test(e))return o};e.valid_elements?_(e.valid_elements):(hs(d,((e,t)=>{n[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},o[t]=e.children})),hs(Cs("strong/b em/i"),(e=>{const t=Cs(e,"/");n[t[1]].outputName=t[0]})),hs(x,((t,o)=>{n[o]&&(e.padd_empty_block_inline_children&&(n[o].paddInEmptyBlock=!0),n[o].removeEmpty=!0)})),hs(Cs("ol ul blockquote a table tbody"),(e=>{n[e]&&(n[e].removeEmpty=!0)})),hs(Cs("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),(e=>{n[e].paddEmpty=!0})),hs(Cs("span"),(e=>{n[e].removeEmptyAttrs=!0}))),E(e.custom_elements),R(e.valid_children),S(e.extended_valid_elements),R("+ol[ul|ol],+ul[ul|ol]"),hs({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},((e,t)=>{n[t]&&(n[t].parentsRequired=Cs(e))})),e.invalid_elements&&hs(vs(e.invalid_elements),(e=>{n[e]&&delete n[e]})),A("span")||S("span[!data-mce-type|*]");const O=N(c),T=N(u),B=N(m),D=N(h),P=N(w),L=N(C),M=N(x),I=N(Object.seal(p)),F=N(g),U=N(v),z=N(y),j=N(f),V=N(Object.seal(a)),H=N(s);return{type:l,children:o,elements:n,getValidStyles:O,getValidClasses:B,getBlockElements:P,getInvalidStyles:T,getVoidElements:I,getTextBlockElements:L,getTextInlineElements:M,getBoolAttrs:D,getElementRule:A,getSelfClosingElements:F,getNonEmptyElements:U,getMoveCaretBeforeOnEnterElements:z,getWhitespaceElements:j,getSpecialElements:V,isValidChild:(e,t)=>{const n=o[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:(e,t)=>{let n,o;const r=A(e);if(r){if(!t)return!0;if(r.attributes[t])return!0;if(n=r.attributePatterns,n)for(o=n.length;o--;)if(n[o].pattern.test(t))return!0}return!1},getCustomElements:H,addValidElements:S,setValidElements:_,addCustomElements:E,addValidChildren:R}},_s=(e,t)=>{const n=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,o=/\s*([^:]+):\s*([^;]+);?/g,r=/\s+$/;let s;const a={};let i,l;e=e||{},t&&(i=t.getValidStyles(),l=t.getInvalidStyles());const d="\\\" \\' \\; \\: ; : \ufeff".split(" ");for(s=0;s<d.length;s++)a[d[s]]="\ufeff"+s,a["\ufeff"+s]=d[s];const c={parse:t=>{const i={};let l,d,u,m;const f=e.url_converter,g=e.url_converter_scope||c,p=(e,t,n)=>{const o=i[e+"-top"+t];if(!o)return;const r=i[e+"-right"+t];if(!r)return;const a=i[e+"-bottom"+t];if(!a)return;const l=i[e+"-left"+t];if(!l)return;const d=[o,r,a,l];for(s=d.length-1;s--&&d[s]===d[s+1];);s>-1&&n||(i[e+t]=-1===s?d[0]:d.join(" "),delete i[e+"-top"+t],delete i[e+"-right"+t],delete i[e+"-bottom"+t],delete i[e+"-left"+t])},h=e=>{let t,n=i[e];if(n){for(n=n.split(" "),t=n.length;t--;)if(n[t]!==n[0])return!1;return i[e]=n[0],!0}},b=e=>(m=!0,a[e]),v=(e,t)=>(m&&(e=e.replace(/\uFEFF[0-9]/g,(e=>a[e]))),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e),y=e=>String.fromCharCode(parseInt(e.slice(1),16)),C=e=>e.replace(/\\[0-9a-f]+/gi,y),w=(t,n,o,r,s,a)=>{if(s=s||a)return"'"+(s=v(s)).replace(/\'/g,"\\'")+"'";if(n=v(n||o||r),!e.allow_script_urls){const t=n.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(t))return"";if(!e.allow_svg_data_urls&&/^data:image\/svg/i.test(t))return""}return f&&(n=f.call(g,n,"style")),"url('"+n.replace(/\'/g,"\\'")+"')"};if(t){for(t=(t=t.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,b).replace(/\"[^\"]+\"|\'[^\']+\'/g,(e=>e.replace(/[;:]/g,b)));l=o.exec(t);)if(o.lastIndex=l.index+l[0].length,d=l[1].replace(r,"").toLowerCase(),u=l[2].replace(r,""),d&&u){if(d=C(d),u=C(u),-1!==d.indexOf("\ufeff")||-1!==d.indexOf('"'))continue;if(!e.allow_script_urls&&("behavior"===d||/expression\s*\(|\/\*|\*\//.test(u)))continue;"font-weight"===d&&"700"===u?u="bold":"color"!==d&&"background-color"!==d||(u=u.toLowerCase()),u=u.replace(n,w),i[d]=m?v(u,!0):u}p("border","",!0),p("border","-width"),p("border","-color"),p("border","-style"),p("padding",""),p("margin",""),"border",k="border-style",S="border-color",h(x="border-width")&&h(k)&&h(S)&&(i.border=i[x]+" "+i[k]+" "+i[S],delete i[x],delete i[k],delete i[S]),"medium none"===i.border&&delete i.border,"none"===i["border-image"]&&delete i["border-image"]}var x,k,S;return i},serialize:(e,t)=>{let n="";const o=t=>{let o;const r=i[t];if(r)for(let s=0,a=r.length;s<a;s++)t=r[s],o=e[t],o&&(n+=(n.length>0?" ":"")+t+": "+o+";")};return t&&i?(o("*"),o(t)):fe(e,((e,o)=>{!e||l&&!((e,t)=>{let n=l["*"];return!(n&&n[e]||(n=l[t],n&&n[e]))})(o,t)||(n+=(n.length>0?" ":"")+o+": "+e+";")})),n}};return c},Es={keyLocation:!0,layerX:!0,layerY:!0,returnValue:!0,webkitMovementX:!0,webkitMovementY:!0,keyIdentifier:!0,mozPressure:!0},Ns=(e,t)=>{const n=null!=t?t:{};for(const t in e)xe(Es,t)||(n[t]=e[t]);return C(n.composedPath)&&(n.composedPath=()=>e.composedPath()),n},Rs=(e,t,n,o)=>{var r;const s=Ns(t,o);return s.type=e,y(s.target)&&(s.target=null!==(r=s.srcElement)&&void 0!==r?r:n),(e=>y(e.preventDefault)||(e=>e instanceof Event||w(e.initEvent))(e))(t)&&(s.preventDefault=()=>{s.defaultPrevented=!0,s.isDefaultPrevented=L,w(t.preventDefault)&&t.preventDefault()},s.stopPropagation=()=>{s.cancelBubble=!0,s.isPropagationStopped=L,w(t.stopPropagation)&&t.stopPropagation()},s.stopImmediatePropagation=()=>{s.isImmediatePropagationStopped=L,s.stopPropagation()},(e=>e.isDefaultPrevented===L||e.isDefaultPrevented===P)(s)||(s.isDefaultPrevented=!0===s.defaultPrevented?L:P,s.isPropagationStopped=!0===s.cancelBubble?L:P,s.isImmediatePropagationStopped=P)),s},As=/^(?:mouse|contextmenu)|click/,Os=(e,t,n,o)=>{e.addEventListener?e.addEventListener(t,n,o||!1):e.attachEvent&&e.attachEvent("on"+t,n)},Ts=(e,t,n,o)=>{e.removeEventListener?e.removeEventListener(t,n,o||!1):e.detachEvent&&e.detachEvent("on"+t,n)},Bs=(e,t)=>{const n=Rs(e.type,e,document,t);if((e=>C(e)&&As.test(e.type))(e)&&v(e.pageX)&&!v(e.clientX)){const t=n.target.ownerDocument||document,o=t.documentElement,r=t.body,s=n;s.pageX=e.clientX+(o&&o.scrollLeft||r&&r.scrollLeft||0)-(o&&o.clientLeft||r&&r.clientLeft||0),s.pageY=e.clientY+(o&&o.scrollTop||r&&r.scrollTop||0)-(o&&o.clientTop||r&&r.clientTop||0)}return n},Ds=(e,t,n)=>{const o=e.document,r={type:"ready"};if(n.domLoaded)return void t(r);const s=()=>{Ts(e,"DOMContentLoaded",s),Ts(e,"load",s),n.domLoaded||(n.domLoaded=!0,t(r)),e=null};"complete"===o.readyState||"interactive"===o.readyState&&o.body?s():Os(e,"DOMContentLoaded",s),n.domLoaded||Os(e,"load",s)};class Ps{constructor(){this.domLoaded=!1,this.events={},this.count=1,this.expando="mce-data-"+(+new Date).toString(32),this.hasMouseEnterLeave="onmouseenter"in document.documentElement,this.hasFocusIn="onfocusin"in document.documentElement,this.count=1}bind(e,t,n,o){const r=this;let s,a,i,l,d,c,u;const m=window,f=e=>{r.executeHandlers(Bs(e||m.event),s)};if(!e||3===e.nodeType||8===e.nodeType)return;e[r.expando]?s=e[r.expando]:(s=r.count++,e[r.expando]=s,r.events[s]={}),o=o||e;const g=t.split(" ");for(i=g.length;i--;)l=g[i],c=f,d=u=!1,"DOMContentLoaded"===l&&(l="ready"),r.domLoaded&&"ready"===l&&"complete"===e.readyState?n.call(o,Bs({type:l})):(r.hasMouseEnterLeave||(d=r.mouseEnterLeave[l],d&&(c=e=>{const t=e.currentTarget;let n=e.relatedTarget;if(n&&t.contains)n=t.contains(n);else for(;n&&n!==t;)n=n.parentNode;n||((e=Bs(e||m.event)).type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,r.executeHandlers(e,s))})),r.hasFocusIn||"focusin"!==l&&"focusout"!==l||(u=!0,d="focusin"===l?"focus":"blur",c=e=>{(e=Bs(e||m.event)).type="focus"===e.type?"focusin":"focusout",r.executeHandlers(e,s)}),a=r.events[s][l],a?"ready"===l&&r.domLoaded?n(Bs({type:l})):a.push({func:n,scope:o}):(r.events[s][l]=a=[{func:n,scope:o}],a.fakeName=d,a.capture=u,a.nativeHandler=c,"ready"===l?Ds(e,c,r):Os(e,d||l,c,u)));return e=a=null,n}unbind(e,t,n){let o,r,s,a,i;if(!e||3===e.nodeType||8===e.nodeType)return this;const l=e[this.expando];if(l){if(i=this.events[l],t){const l=t.split(" ");for(r=l.length;r--;)if(a=l[r],o=i[a],o){if(n)for(s=o.length;s--;)if(o[s].func===n){const e=o.nativeHandler,t=o.fakeName,n=o.capture;o=o.slice(0,s).concat(o.slice(s+1)),o.nativeHandler=e,o.fakeName=t,o.capture=n,i[a]=o}n&&0!==o.length||(delete i[a],Ts(e,o.fakeName||a,o.nativeHandler,o.capture))}}else fe(i,((t,n)=>{Ts(e,t.fakeName||n,t.nativeHandler,t.capture)})),i={};for(a in i)if(xe(i,a))return this;delete this.events[l];try{delete e[this.expando]}catch(t){e[this.expando]=null}}return this}fire(e,t,n){return this.dispatch(e,t,n)}dispatch(e,t,n){let o;if(!e||3===e.nodeType||8===e.nodeType)return this;const r=Bs({type:t,target:e},n);do{o=e[this.expando],o&&this.executeHandlers(r,o),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow}while(e&&!r.isPropagationStopped());return this}clean(e){let t,n;if(!e||3===e.nodeType||8===e.nodeType)return this;if(e[this.expando]&&this.unbind(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName)for(this.unbind(e),n=e.getElementsByTagName("*"),t=n.length;t--;)(e=n[t])[this.expando]&&this.unbind(e);return this}destroy(){this.events={}}cancel(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}executeHandlers(e,t){const n=this.events[t],o=n&&n[e.type];if(o)for(let t=0,n=o.length;t<n;t++){const n=o[t];if(n&&!1===n.func.call(n.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return}}}Ps.Event=new Ps;const Ls=Bt.each,Ms=Bt.grep,Is="data-mce-style",Fs=(e,t,n)=>{y(n)||""===n?Xt(e,t):qt(e,t,n)},Us=(e,t)=>{const n=Kt(t,"style"),o=e.serialize(e.parse(n),Mt(t));Fs(t,Is,o)},zs=(e,t)=>{let n,o,r=0;if(e)for(n=e.nodeType,e=e.previousSibling;e;e=e.previousSibling)o=e.nodeType,(!t||3!==o||o!==n&&e.nodeValue.length)&&(r++,n=o);return r},js=Bt.makeMap("fill-opacity font-weight line-height opacity orphans widows z-index zoom"," "),Vs=e=>e.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase())),Hs=(e,t={})=>{const n={},o=window,r={};let s=0;const a=Xo.forElement(fn(e),{contentCssCors:t.contentCssCors,referrerPolicy:t.referrerPolicy}),i=[],l=t.schema?t.schema:Ss({}),d=_s({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope},t.schema),c=t.ownEvents?new Ps:Ps.Event,u=l.getBlockElements(),g=t=>t&&e&&m(t)?e.getElementById(t):t,h=e=>{const t=g(e);return C(t)?fn(t):null},b=(e,t,n)=>{let o;const r=h(e);if(C(r)&&Ut(r)){const e=J[t];o=e&&e.get?e.get(r.dom,t):Kt(r,t)}return C(o)?o:null!=n?n:""},v=e=>{const t=g(e);return y(t)?[]:t.attributes},k=(e,n,o)=>{P(e,(e=>{if(Co(e)){const r=fn(e);""===o&&(o=null);const s=Kt(r,n),a=J[n];a&&a.set?a.set(r.dom,o,n):Fs(r,n,o),s!==o&&t.onSetAttrib&&t.onSetAttrib({attrElm:r,attrName:n,attrValue:o})}}))},_=()=>t.root_element||e.body,E=(t,n)=>((e,t,n)=>{let o=0,r=0;const s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===qn(fn(e),"position")){const n=t.getBoundingClientRect();return o=n.left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,r=n.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop,{x:o,y:r}}let a=t;for(;a&&a!==n&&a.nodeType&&!Vo(a,n);){const e=a;o+=e.offsetLeft||0,r+=e.offsetTop||0,a=e.offsetParent}for(a=t.parentNode;a&&a!==n&&a.nodeType&&!Vo(a,n);)o-=a.scrollLeft||0,r-=a.scrollTop||0,a=a.parentNode;r+=(e=>zo.isFirefox()&&"table"===Mt(e)?jo(Rn(e)).filter((e=>"caption"===Mt(e))).bind((e=>jo(Nn(e)).map((t=>{const n=t.dom.offsetTop,o=e.dom.offsetTop,r=e.dom.offsetHeight;return n<=o?-r:0})))).getOr(0):0)(fn(t))}return{x:o,y:r}})(e.body,g(t),n),R=(e,n,o)=>{const r=(e,t)=>m(e)?e:x(e)?xe(js,t)?e+"":e+"px":ge(e,r),s=(e,t,n)=>{const o=Vs(t);y(n)||""===n?Yn(e,o):Hn(e,o,r(n,o))};P(e,(e=>{const r=fn(e);m(n)?s(r,n,o):fe(n,((e,t)=>{s(r,t,e)})),t.update_styles&&Us(d,r)}))},A=(e,t,n)=>{const o=g(e);if(!y(o)&&Co(o))return n?qn(fn(o),Vs(t)):("float"===(t=t.replace(/-(\D)/g,((e,t)=>t.toUpperCase())))&&(t="cssFloat"),o.style?o.style[t]:void 0)},O=e=>{let t,n;const o=g(e);return t=A(o,"width"),n=A(o,"height"),-1===t.indexOf("px")&&(t=0),-1===n.indexOf("px")&&(n=0),{w:parseInt(t,10)||o.offsetWidth||o.clientWidth,h:parseInt(n,10)||o.offsetHeight||o.clientHeight}},T=(e,t)=>{if(!e)return!1;const n=p(e)?e:[e];return V(n,(e=>hn(fn(e),t)))},B=(e,t,n,o)=>{const r=[];let s,a=g(e);for(o=void 0===o,n=n||("BODY"!==_().nodeName?_().parentNode:null),m(t)&&(s=t,t="*"===t?Co:e=>T(e,s));a&&!(a===n||y(a.nodeType)||Bo(a)||Do(a));){if(!t||t(a)){if(!o)return[a];r.push(a)}a=a.parentNode}return o?r:null},D=(e,t,n)=>{let o=t;if(e)for(m(t)&&(o=e=>T(e,t)),e=e[n];e;e=e[n])if(w(o)&&o(e))return e;return null},P=function(e,t,n){const o=null!=n?n:this,r=m(e)?g(e):e;if(!r)return!1;if(p(r)&&(r.length||0===r.length)){const e=[];return Ls(r,((n,r)=>{n&&e.push(t.call(o,m(n)?g(n):n,r))})),e}return t.call(o,r)},L=(e,t)=>{P(e,(e=>{fe(t,((t,n)=>{k(e,n,t)}))}))},M=(e,t)=>{P(e,(e=>{const n=fn(e);so(n,t)}))},I=(t,n,o,r,s)=>P(t,(t=>{const a=m(n)?e.createElement(n):n;return C(o)&&L(a,o),r&&(!m(r)&&r.nodeType?a.appendChild(r):m(r)&&M(a,r)),s?a:t.appendChild(a)})),F=(t,n,o)=>I(e.createElement(t),t,n,o,!0),U=ms.encodeAllRaw,z=(e,t)=>P(e,(e=>{const n=fn(e);return t&&$(Rn(n),(e=>{zt(e)&&0===e.dom.length?no(e):Xn(n,e)})),no(n),n.dom})),H=(e,t,n)=>{P(e,(e=>{if(Co(e)){const o=fn(e),r=t.split(" ");$(r,(e=>{C(n)?(n?on:sn)(o,e):((e,t)=>{const n=Zt(e)?e.dom.classList.toggle(t):((e,t)=>j(en(e),t)?nn(e,t):tn(e,t))(e,t);rn(e)})(o,e)}))}}))},q=(e,t,n)=>P(t,(t=>(p(t)&&(e=e.cloneNode(!0)),n&&Ls(Ms(t.childNodes),(t=>{e.appendChild(t)})),t.parentNode.replaceChild(e,t)))),W=e=>{if(Co(e)){const t="a"===e.nodeName.toLowerCase()&&!b(e,"href")&&b(e,"id");if(b(e,"name")||b(e,"data-mce-bookmark")||t)return!0}return!1},K=()=>e.createRange(),G=(n,r,s,a)=>{if(p(n)){let e=n.length;const t=[];for(;e--;)t[e]=G(n[e],r,s,a);return t}return!t.collect||n!==e&&n!==o||i.push([n,r,s,a]),c.bind(n,r,s,a||Q)},Y=(t,n,r)=>{if(p(t)){let e=t.length;const o=[];for(;e--;)o[e]=Y(t[e],n,r);return o}if(i.length>0&&(t===e||t===o)){let e=i.length;for(;e--;){const o=i[e];t!==o[0]||n&&n!==o[1]||r&&r!==o[2]||c.unbind(o[0],o[1],o[2])}}return c.unbind(t,n,r)},X=e=>{if(e&&Co(e)){const t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},Q={doc:e,settings:t,win:o,files:r,stdMode:!0,boxModel:!0,styleSheetLoader:a,boundEvents:i,styles:d,schema:l,events:c,isBlock:e=>m(e)?xe(u,e):Co(e)&&xe(u,e.nodeName),root:null,clone:(e,t)=>e.cloneNode(t),getRoot:_,getViewPort:e=>{const t=bo(e);return{x:t.x,y:t.y,w:t.width,h:t.height}},getRect:e=>{const t=g(e),n=E(t),o=O(t);return{x:n.x,y:n.y,w:o.w,h:o.h}},getSize:O,getParent:(e,t,n)=>{const o=B(e,t,n,!1);return o&&o.length>0?o[0]:null},getParents:B,get:g,getNext:(e,t)=>D(e,t,"nextSibling"),getPrev:(e,t)=>D(e,t,"previousSibling"),select:(n,o)=>{var r,s;const a=null!==(s=null!==(r=g(o))&&void 0!==r?r:t.root_element)&&void 0!==s?s:e;return de(a.querySelectorAll(n))},is:T,add:I,create:F,createHTML:(e,t,n="")=>{let o,r="";for(o in r+="<"+e,t)ke(t,o)&&(r+=" "+o+'="'+U(t[o])+'"');return Ke(n)&&xe(l.getVoidElements(),e)?r+" />":r+">"+n+"</"+e+">"},createFragment:t=>{let n;const o=e.createElement("div"),r=e.createDocumentFragment();for(r.appendChild(o),t&&(o.innerHTML=t);n=o.firstChild;)r.appendChild(n);return r.removeChild(o),r},remove:z,setStyle:R,getStyle:A,setStyles:(e,t)=>{R(e,t)},removeAllAttribs:e=>P(e,(e=>{const t=e.attributes;for(let n=t.length-1;n>=0;n--)e.removeAttributeNode(t.item(n))})),setAttrib:k,setAttribs:L,getAttrib:b,getPos:E,parseStyle:e=>d.parse(e),serializeStyle:(e,t)=>d.serialize(e,t),addStyle:t=>{let o,r;if(Q!==Hs.DOM&&e===document){if(n[t])return;n[t]=!0}r=e.getElementById("mceDefaultStyles"),r||(r=e.createElement("style"),r.id="mceDefaultStyles",r.type="text/css",o=e.getElementsByTagName("head")[0],o.firstChild?o.insertBefore(r,o.firstChild):o.appendChild(r)),r.styleSheet?r.styleSheet.cssText+=t:r.appendChild(e.createTextNode(t))},loadCSS:e=>{e||(e=""),$(e.split(","),(e=>{r[e]=!0,a.load(e).catch(S)}))},addClass:(e,t)=>{H(e,t,!0)},removeClass:(e,t)=>{H(e,t,!1)},hasClass:(e,t)=>{const n=h(e),o=t.split(" ");return te(o,(e=>an(n,e)))},toggleClass:H,show:e=>{P(e,(e=>Yn(fn(e),"display")))},hide:e=>{P(e,(e=>Hn(fn(e),"display","none")))},isHidden:e=>{const t=h(e);return Dt(Kn(t,"display"),"none")},uniqueId:e=>(e||"mce_")+s++,setHTML:M,getOuterHTML:e=>{const t=h(e);return Co(t.dom)?t.dom.outerHTML:(e=>{const t=un("div"),n=fn(e.dom.cloneNode(!0));return Zn(t,n),ro(t)})(t)},setOuterHTML:(e,t)=>{P(e,(e=>{Co(e)&&(e.outerHTML=t)}))},decode:ms.decode,encode:U,insertAfter:(e,t)=>{const n=g(t);return P(e,(e=>{const t=n.parentNode,o=n.nextSibling;return o?t.insertBefore(e,o):t.appendChild(e),e}))},replace:q,rename:(e,t)=>{let n;return e.nodeName!==t.toUpperCase()&&(n=F(t),Ls(v(e),(t=>{k(n,t.nodeName,b(e,t.nodeName))})),q(n,e,!0)),n||e},findCommonAncestor:(e,t)=>{let n,o=e;for(;o;){for(n=t;n&&o!==n;)n=n.parentNode;if(o===n)break;o=o.parentNode}return!o&&e.ownerDocument?e.ownerDocument.documentElement:o},run:P,getAttribs:v,isEmpty:(e,t)=>{let n,o,r=0;if(W(e))return!1;if(e=e.firstChild){const s=new Qo(e,e.parentNode),a=l?l.getWhitespaceElements():{};t=t||(l?l.getNonEmptyElements():null);do{if(n=e.nodeType,Co(e)){const n=e.getAttribute("data-mce-bogus");if(n){e=s.next("all"===n);continue}if(o=e.nodeName.toLowerCase(),t&&t[o]){if("br"===o){r++,e=s.next();continue}return!1}if(W(e))return!1}if(8===n)return!1;if(3===n&&!Vr(e.nodeValue))return!1;if(3===n&&e.parentNode&&a[e.parentNode.nodeName]&&Vr(e.nodeValue))return!1;e=s.next()}while(e)}return r<=1},createRng:K,nodeIndex:zs,split:(e,t,n)=>{let o,r,s,a=K();if(e&&t)return a.setStart(e.parentNode,zs(e)),a.setEnd(t.parentNode,zs(t)),o=a.extractContents(),a=K(),a.setStart(t.parentNode,zs(t)+1),a.setEnd(e.parentNode,zs(e)+1),r=a.extractContents(),s=e.parentNode,s.insertBefore(Jr(Q,o),e),n?s.insertBefore(n,e):s.insertBefore(t,e),s.insertBefore(Jr(Q,r),e),z(e),n||t},bind:G,unbind:Y,fire:(e,t,n)=>c.dispatch(e,t,n),dispatch:(e,t,n)=>c.dispatch(e,t,n),getContentEditable:X,getContentEditableParent:e=>{const t=_();let n=null;for(;e&&e!==t&&(n=X(e),null===n);e=e.parentNode);return n},destroy:()=>{if(i.length>0){let e=i.length;for(;e--;){const t=i[e];c.unbind(t[0],t[1],t[2])}}fe(r,((e,t)=>{a.unload(t),delete r[t]}))},isChildOf:(e,t)=>e===t||t.contains(e),dumpRng:e=>"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset},J=((e,t,n)=>{const o=t.keep_values,r={set:(e,o,r)=>{const s=fn(e);w(t.url_converter)&&C(o)&&(o=t.url_converter.call(t.url_converter_scope||n(),o,r,e[0])),Fs(s,"data-mce-"+r,o),Fs(s,r,o)},get:(e,t)=>{const n=fn(e);return Kt(n,"data-mce-"+t)||Kt(n,t)}},s={style:{set:(t,n)=>{const r=fn(t);f(n)?$n(r,n):(o&&Fs(r,Is,n),Xt(r,"style"),m(n)&&$n(r,e.parse(n)))},get:t=>{const n=fn(t),o=Kt(n,Is)||Kt(n,"style");return e.serialize(e.parse(o),Mt(n))}}};return o&&(s.href=s.src=r),s})(d,t,N(Q));return Q};Hs.DOM=Hs(document),Hs.nodeIndex=zs;const $s=Hs.DOM;class qs{constructor(e={}){this.states={},this.queue=[],this.scriptLoadedCallbacks={},this.queueLoadedCallbacks=[],this.loading=!1,this.settings=e}_setReferrerPolicy(e){this.settings.referrerPolicy=e}loadScript(e){return new Promise(((t,n)=>{const o=$s;let r;const s=()=>{o.remove(a),r&&(r.onerror=r.onload=r=null)},a=o.uniqueId();r=document.createElement("script"),r.id=a,r.type="text/javascript",r.src=Bt._addCacheSuffix(e),this.settings.referrerPolicy&&o.setAttrib(r,"referrerpolicy",this.settings.referrerPolicy),r.onload=()=>{s(),t()},r.onerror=()=>{s(),n("Failed to load script: "+e)},(document.getElementsByTagName("head")[0]||document.body).appendChild(r)}))}isDone(e){return 2===this.states[e]}markDone(e){this.states[e]=2}add(e){const t=this;return t.queue.push(e),void 0===t.states[e]&&(t.states[e]=0),new Promise(((n,o)=>{t.scriptLoadedCallbacks[e]||(t.scriptLoadedCallbacks[e]=[]),t.scriptLoadedCallbacks[e].push({resolve:n,reject:o})}))}load(e){return this.add(e)}remove(e){delete this.states[e],delete this.scriptLoadedCallbacks[e]}loadQueue(){const e=this.queue;return this.queue=[],this.loadScripts(e)}loadScripts(e){const t=this,n=(e,n)=>{we(t.scriptLoadedCallbacks,n).each((t=>{$(t,(t=>t[e](n)))})),delete t.scriptLoadedCallbacks[n]},o=e=>{const t=K(e,(e=>"rejected"===e.status));return t.length>0?Promise.reject(ee(t,(({reason:e})=>p(e)?e:[e]))):Promise.resolve()},r=e=>Promise.allSettled(H(e,(e=>2===t.states[e]?(n("resolve",e),Promise.resolve()):3===t.states[e]?(n("reject",e),Promise.reject(e)):(t.states[e]=1,t.loadScript(e).then((()=>{t.states[e]=2,n("resolve",e);const s=t.queue;if(s.length>0)return t.queue=[],r(s).then(o)}),(()=>(t.states[e]=3,n("reject",e),Promise.reject(e)))))))),s=e=>(t.loading=!0,r(e).then((e=>{t.loading=!1;const n=t.queueLoadedCallbacks.shift();return M.from(n).each(D),o(e)}))),a=Se(e);return t.loading?new Promise(((e,n)=>{t.queueLoadedCallbacks.push((()=>s(a).then(e,n)))})):s(a)}}qs.ScriptLoader=new qs;const Ws=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Ks={},Gs=Ws("en"),Ys=()=>we(Ks,Gs.get()),Xs={getData:()=>ge(Ks,(e=>({...e}))),setCode:e=>{e&&Gs.set(e)},getCode:()=>Gs.get(),add:(e,t)=>{let n=Ks[e];n||(Ks[e]=n={}),fe(t,((e,t)=>{n[t.toLowerCase()]=e}))},translate:e=>{const t=Ys().getOr({}),n=e=>w(e)?Object.prototype.toString.call(e):o(e)?"":""+e,o=e=>""===e||null==e,r=e=>{const o=n(e);return we(t,o.toLowerCase()).map(n).getOr(o)},s=e=>e.replace(/{context:\w+}$/,"");if(o(e))return"";if(f(a=e)&&xe(a,"raw"))return n(e.raw);var a;if((e=>p(e)&&e.length>1)(e)){const t=e.slice(1);return s(r(e[0]).replace(/\{([0-9]+)\}/g,((e,o)=>xe(t,o)?n(t[o]):e)))}return s(r(e))},isRtl:()=>Ys().bind((e=>we(e,"_dir"))).exists((e=>"rtl"===e)),hasCode:e=>xe(Ks,e)},Qs=()=>{const e=[],t={},n={},o=[],r=(e,t)=>{const n=K(o,(n=>n.name===e&&n.state===t));$(n,(e=>e.resolve()))},s=e=>xe(t,e),a=(e,n)=>{const o=Xs.getCode();!o||n&&-1===(","+(n||"")+",").indexOf(","+o+",")||qs.ScriptLoader.add(t[e]+"/langs/"+o+".js")},i=(e,t="added")=>"added"===t&&(e=>xe(n,e))(e)||"loaded"===t&&s(e)?Promise.resolve():new Promise((n=>{o.push({name:e,state:t,resolve:n})}));return{items:e,urls:t,lookup:n,get:e=>{if(n[e])return n[e].instance},requireLangPack:(e,t)=>{!1!==Qs.languageLoad&&(s(e)?a(e,t):i(e,"loaded").then((()=>a(e,t))))},add:(t,o)=>(e.push(o),n[t]={instance:o},r(t,"added"),o),remove:e=>{delete t[e],delete n[e]},createUrl:(e,t)=>m(t)?m(e)?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}:t,load:(e,o)=>{if(t[e])return Promise.resolve();let s=m(o)?o:o.prefix+o.resource+o.suffix;0!==s.indexOf("/")&&-1===s.indexOf("://")&&(s=Qs.baseURL+"/"+s),t[e]=s.substring(0,s.lastIndexOf("/"));const a=()=>(r(e,"loaded"),Promise.resolve());return n[e]?a():qs.ScriptLoader.add(s).then(a)},waitFor:i}};Qs.languageLoad=!0,Qs.baseURL="",Qs.PluginManager=Qs(),Qs.ThemeManager=Qs(),Qs.ModelManager=Qs();const Js=()=>{const e=(e=>{const t=Ws(M.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(M.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(M.some(e))}}})(S);return{...e,on:t=>e.get().each(t)}},Zs=(e,t)=>{let n=null;return{cancel:()=>{h(n)||(clearTimeout(n),n=null)},throttle:(...o)=>{h(n)&&(n=setTimeout((()=>{n=null,e.apply(null,o)}),t))}}},ea=(e,t)=>{let n=null;const o=()=>{h(n)||(clearTimeout(n),n=null)};return{cancel:o,throttle:(...r)=>{o(),n=setTimeout((()=>{n=null,e.apply(null,r)}),t)}}},ta=(e,t)=>{let n=[];return $(Rn(e),(e=>{t(e)&&(n=n.concat([e])),n=n.concat(ta(e,t))})),n},na=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return bn(n)?[]:H(n.querySelectorAll(e),fn)})(t,e),oa=N("mce-annotation"),ra=N("data-mce-annotation"),sa=N("data-mce-annotation-uid"),aa=N("data-mce-annotation-active"),ia=N("data-mce-annotation-classes"),la=N("data-mce-annotation-attrs"),da=e=>t=>vn(t,e),ca=(e,t)=>{const n=e.selection.getRng(),o=fn(n.startContainer),r=fn(e.getBody()),s=t.fold((()=>"."+oa()),(e=>`[${ra()}="${e}"]`)),a=An(o,n.startOffset).getOr(o),i=Go(a,s,da(r)),l=(e,t)=>Yt(e,t)?M.some(Kt(e,t)):M.none();return i.bind((t=>l(t,`${sa()}`).bind((n=>l(t,`${ra()}`).map((t=>{const o=ma(e,n);return{uid:n,name:t,elements:o}}))))))},ua=(e,t)=>Yt(e,"data-mce-bogus")||ur(e,'[data-mce-bogus="all"]',da(t)),ma=(e,t)=>{const n=fn(e.getBody()),o=na(n,`[${sa()}="${t}"]`);return K(o,(e=>!ua(e,n)))},fa=(e,t)=>{const n=fn(e.getBody()),o=na(n,`[${ra()}="${t}"]`),r={};return $(o,(e=>{if(!ua(e,n)){const t=Kt(e,sa()),n=we(r,t).getOr([]);r[t]=n.concat([e])}})),r};let ga=0;const pa=e=>{const t=(new Date).getTime(),n=Math.floor(1e9*Math.random());return ga++,e+"_"+n+ga+String(t)},ha=(e,t)=>fn(e.dom.cloneNode(t)),ba=e=>ha(e,!1),va=e=>ha(e,!0),ya=(e,t,n=P)=>{const o=new Qo(e,t),r=e=>{let t;do{t=o[e]()}while(t&&!Ro(t)&&!n(t));return M.from(t).filter(Ro)};return{current:()=>M.from(o.current()).filter(Ro),next:()=>r("next"),prev:()=>r("prev"),prev2:()=>r("prev2")}},Ca=(e,t)=>{const n=t||(t=>e.isBlock(t)||Po(t)||Io(t)),o=(e,t,n,r)=>{if(Ro(e)){const n=r(e,t,e.data);if(-1!==n)return M.some({container:e,offset:n})}return n().bind((e=>o(e.container,e.offset,n,r)))};return{backwards:(e,t,r,s)=>{const a=ya(e,s,n);return o(e,t,(()=>a.prev().map((e=>({container:e,offset:e.length})))),r).getOrNull()},forwards:(e,t,r,s)=>{const a=ya(e,s,n);return o(e,t,(()=>a.next().map((e=>({container:e,offset:0})))),r).getOrNull()}}},wa=Math.round,xa=e=>e?{left:wa(e.left),top:wa(e.top),bottom:wa(e.bottom),right:wa(e.right),width:wa(e.width),height:wa(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0},ka=(e,t)=>(e=xa(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e),Sa=(e,t,n)=>e>=0&&e<=Math.min(t.height,n.height)/2,_a=(e,t)=>{const n=Math.min(t.height/2,e.height/2);return e.bottom-n<t.top||!(e.top>t.bottom)&&Sa(t.top-e.bottom,e,t)},Ea=(e,t)=>e.top>t.bottom||!(e.bottom<t.top)&&Sa(t.bottom-e.top,e,t),Na=(e,t,n)=>{const o=Math.max(Math.min(t,e.left+e.width),e.left),r=Math.max(Math.min(n,e.top+e.height),e.top);return Math.sqrt((t-o)*(t-o)+(n-r)*(n-r))},Ra=e=>{const t=e.startContainer,n=e.startOffset;return t===e.endContainer&&t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},Aa=(e,t)=>{if(Co(e)&&e.hasChildNodes()){const n=e.childNodes,o=((e,t,n)=>Math.min(Math.max(e,0),n))(t,0,n.length-1);return n[o]}return e},Oa=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),Ta=e=>"string"==typeof e&&e.charCodeAt(0)>=768&&Oa.test(e),Ba=Co,Da=Fr,Pa=xo("display","block table"),La=xo("float","left right"),Ma=((...e)=>t=>{for(let n=0;n<e.length;n++)if(!e[n](t))return!1;return!0})(Ba,Da,T(La)),Ia=T(xo("white-space","pre pre-line pre-wrap")),Fa=Ro,Ua=Po,za=Hs.nodeIndex,ja=(e,t)=>t<0&&Co(e)&&e.hasChildNodes()?void 0:Aa(e,t),Va=e=>"createRange"in e?e.createRange():Hs.DOM.createRng(),Ha=e=>e&&/[\r\n\t ]/.test(e),$a=e=>!!e.setStart&&!!e.setEnd,qa=e=>{const t=e.startContainer,n=e.startOffset;if(Ha(e.toString())&&Ia(t.parentNode)&&Ro(t)){const e=t.data;if(Ha(e[n-1])||Ha(e[n+1]))return!0}return!1},Wa=e=>0===e.left&&0===e.right&&0===e.top&&0===e.bottom,Ka=e=>{let t;const n=e.getClientRects();return t=n.length>0?xa(n[0]):xa(e.getBoundingClientRect()),!$a(e)&&Ua(e)&&Wa(t)?(e=>{const t=e.ownerDocument,n=Va(t),o=t.createTextNode(fr),r=e.parentNode;r.insertBefore(o,e),n.setStart(o,0),n.setEnd(o,1);const s=xa(n.getBoundingClientRect());return r.removeChild(o),s})(e):Wa(t)&&$a(e)?(e=>{const t=e.startContainer,n=e.endContainer,o=e.startOffset,r=e.endOffset;if(t===n&&Ro(n)&&0===o&&1===r){const t=e.cloneRange();return t.setEndAfter(n),Ka(t)}return null})(e):t},Ga=(e,t)=>{const n=ka(e,t);return n.width=1,n.right=n.left+1,n},Ya=(e,t,n)=>{const o=()=>(n||(n=(e=>{const t=[],n=e=>{var n,o;0!==e.height&&(t.length>0&&(n=e,o=t[t.length-1],n.left===o.left&&n.top===o.top&&n.bottom===o.bottom&&n.right===o.right)||t.push(e))},o=(e,o)=>{const r=Va(e.ownerDocument);if(o<e.data.length){if(Ta(e.data[o]))return t;if(Ta(e.data[o-1])&&(r.setStart(e,o),r.setEnd(e,o+1),!qa(r)))return n(Ga(Ka(r),!1)),t}o>0&&(r.setStart(e,o-1),r.setEnd(e,o),qa(r)||n(Ga(Ka(r),!1))),o<e.data.length&&(r.setStart(e,o),r.setEnd(e,o+1),qa(r)||n(Ga(Ka(r),!0)))},r=e.container(),s=e.offset();if(Fa(r))return o(r,s),t;if(Ba(r))if(e.isAtEnd()){const e=ja(r,s);Fa(e)&&o(e,e.data.length),Ma(e)&&!Ua(e)&&n(Ga(Ka(e),!1))}else{const a=ja(r,s);if(Fa(a)&&o(a,0),Ma(a)&&e.isAtEnd())return n(Ga(Ka(a),!1)),t;const i=ja(e.container(),e.offset()-1);Ma(i)&&!Ua(i)&&(Pa(i)||Pa(a)||!Ma(a))&&n(Ga(Ka(i),!1)),Ma(a)&&n(Ga(Ka(a),!0))}return t})(Ya(e,t))),n);return{container:N(e),offset:N(t),toRange:()=>{const n=Va(e.ownerDocument);return n.setStart(e,t),n.setEnd(e,t),n},getClientRects:o,isVisible:()=>o().length>0,isAtStart:()=>(Fa(e),0===t),isAtEnd:()=>Fa(e)?t>=e.data.length:t>=e.childNodes.length,isEqual:n=>n&&e===n.container()&&t===n.offset(),getNode:n=>ja(e,n?t-1:t)}};Ya.fromRangeStart=e=>Ya(e.startContainer,e.startOffset),Ya.fromRangeEnd=e=>Ya(e.endContainer,e.endOffset),Ya.after=e=>Ya(e.parentNode,za(e)+1),Ya.before=e=>Ya(e.parentNode,za(e)),Ya.isAbove=(e,t)=>Pt(ie(t.getClientRects()),le(e.getClientRects()),_a).getOr(!1),Ya.isBelow=(e,t)=>Pt(le(t.getClientRects()),ie(e.getClientRects()),Ea).getOr(!1),Ya.isAtStart=e=>!!e&&e.isAtStart(),Ya.isAtEnd=e=>!!e&&e.isAtEnd(),Ya.isTextPosition=e=>!!e&&Ro(e.container()),Ya.isElementPosition=e=>!1===Ya.isTextPosition(e);const Xa=(e,t)=>{Ro(t)&&0===t.data.length&&e.remove(t)},Qa=(e,t,n)=>{Do(n)?((e,t,n)=>{const o=M.from(n.firstChild),r=M.from(n.lastChild);t.insertNode(n),o.each((t=>Xa(e,t.previousSibling))),r.each((t=>Xa(e,t.nextSibling)))})(e,t,n):((e,t,n)=>{t.insertNode(n),Xa(e,n.previousSibling),Xa(e,n.nextSibling)})(e,t,n)},Ja=Ro,Za=So,ei=Hs.nodeIndex,ti=e=>{const t=e.parentNode;return Za(t)?ti(t):t},ni=e=>e?Oe(e.childNodes,((e,t)=>(Za(t)&&"BR"!==t.nodeName?e=e.concat(ni(t)):e.push(t),e)),[]):[],oi=e=>t=>e===t,ri=e=>{let t;return t=Ja(e)?"text()":e.nodeName.toLowerCase(),t+"["+(e=>{let t,n;t=ni(ti(e)),n=Te(t,oi(e),e),t=t.slice(0,n+1);const o=Oe(t,((e,n,o)=>(Ja(n)&&Ja(t[o-1])&&e++,e)),0);return t=Re(t,wo([e.nodeName])),n=Te(t,oi(e),e),n-o})(e)+"]"},si=(e,t)=>{let n,o,r,s,a,i=[];return n=t.container(),o=t.offset(),Ja(n)?r=((e,t)=>{for(;(e=e.previousSibling)&&Ja(e);)t+=e.data.length;return t})(n,o):(s=n.childNodes,o>=s.length?(r="after",o=s.length-1):r="before",n=s[o]),i.push(ri(n)),a=((e,t,n)=>{const o=[];for(t=t.parentNode;t!==e;t=t.parentNode)o.push(t);return o})(e,n),a=Re(a,T(So)),i=i.concat(Ne(a,(e=>ri(e)))),i.reverse().join("/")+","+r},ai=(e,t)=>{let n;if(!t)return null;const o=t.split(","),r=o[0].split("/");n=o.length>1?o[1]:"before";const s=Oe(r,((e,t)=>{const n=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t);return n?("text()"===n[1]&&(n[1]="#text"),((e,t,n)=>{let o=ni(e);return o=Re(o,((e,t)=>!Ja(e)||!Ja(o[t-1]))),o=Re(o,wo([t])),o[n]})(e,n[1],parseInt(n[2],10))):null}),e);return s?Ja(s)?((e,t)=>{let n,o=e,r=0;for(;Ja(o);){if(n=o.data.length,t>=r&&t<=r+n){e=o,t-=r;break}if(!Ja(o.nextSibling)){e=o,t=n;break}r+=n,o=o.nextSibling}return Ja(e)&&t>e.data.length&&(t=e.data.length),Ya(e,t)})(s,parseInt(n,10)):(n="after"===n?ei(s)+1:ei(s),Ya(s.parentNode,n)):null},ii=Io,li=(e,t,n,o,r)=>{let s=o[r?"startContainer":"endContainer"],a=o[r?"startOffset":"endOffset"];const i=[];let l,d=0;const c=e.getRoot();for(Ro(s)?i.push(n?((e,t,n)=>{let o,r;for(r=e(t.data.slice(0,n)).length,o=t.previousSibling;o&&Ro(o);o=o.previousSibling)r+=e(o.data).length;return r})(t,s,a):a):(l=s.childNodes,a>=l.length&&l.length&&(d=1,a=Math.max(0,l.length-1)),i.push(e.nodeIndex(l[a],n)+d));s&&s!==c;s=s.parentNode)i.push(e.nodeIndex(s,n));return i},di=(e,t,n)=>{let o=0;return Bt.each(e.select(t),(e=>{if("all"!==e.getAttribute("data-mce-bogus"))return e!==n&&void o++})),o},ci=(e,t)=>{let n,o;const r=t?"start":"end";if(n=e[r+"Container"],o=e[r+"Offset"],Co(n)&&"TR"===n.nodeName){const r=n.childNodes;n=r[Math.min(t?o:o-1,r.length-1)],n&&(o=t?0:n.childNodes.length,e["set"+(t?"Start":"End")](n,o))}},ui=e=>(ci(e,!0),ci(e,!1),e),mi=(e,t)=>{let n;if(Co(e)&&(e=Aa(e,t),ii(e)))return e;if(xr(e)){if(Ro(e)&&Cr(e)&&(e=e.parentNode),n=e.previousSibling,ii(n))return n;if(n=e.nextSibling,ii(n))return n}},fi=(e,t,n)=>{const o=n.getNode();let r=o?o.nodeName:null;const s=n.getRng();if(ii(o)||"IMG"===r)return{name:r,index:di(n.dom,r,o)};const a=(e=>mi(e.startContainer,e.startOffset)||mi(e.endContainer,e.endOffset))(s);return a?(r=a.tagName,{name:r,index:di(n.dom,r,a)}):((e,t,n,o)=>{const r=t.dom,s=li(r,e,n,o,!0),a=t.isForward(),i=Ar(o)?{isFakeCaret:!0}:{};return t.isCollapsed()?{start:s,forward:a,...i}:{start:s,end:li(r,e,n,o,!1),forward:a,...i}})(e,n,t,s)},gi=(e,t,n)=>{const o={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",o,"&#xFEFF;"):e.create("span",o)},pi=(e,t)=>{const n=e.dom;let o=e.getRng();const r=n.uniqueId(),s=e.isCollapsed(),a=e.getNode(),i=a.nodeName,l=e.isForward();if("IMG"===i)return{name:i,index:di(n,i,a)};const d=ui(o.cloneRange());if(!s){d.collapse(!1);const e=gi(n,r+"_end",t);Qa(n,d,e)}o=ui(o),o.collapse(!0);const c=gi(n,r+"_start",t);return Qa(n,o,c),e.moveToBookmark({id:r,keep:!0,forward:l}),{id:r,forward:l}},hi=O(fi,R,!0),bi=e=>{const t=t=>t(e),n=N(e),o=()=>r,r={tag:!0,inner:e,fold:(t,n)=>n(e),isValue:L,isError:P,map:t=>yi.value(t(e)),mapError:o,bind:t,exists:t,forall:t,getOr:n,or:o,getOrThunk:n,orThunk:o,getOrDie:n,each:t=>{t(e)},toOptional:()=>M.some(e)};return r},vi=e=>{const t=()=>n,n={tag:!1,inner:e,fold:(t,n)=>t(e),isValue:P,isError:L,map:t,mapError:t=>yi.error(t(e)),bind:t,exists:P,forall:L,getOr:R,or:R,getOrThunk:B,orThunk:B,getOrDie:(o=String(e),()=>{throw new Error(o)}),each:S,toOptional:M.none};var o;return n},yi={value:bi,error:vi,fromOption:(e,t)=>e.fold((()=>vi(t)),bi)},Ci=e=>{if(!p(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],n={};return $(e,((o,r)=>{const s=ue(o);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],i=o[a];if(void 0!==n[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!p(i))throw new Error("case arguments must be an array");t.push(a),n[a]=(...n)=>{const o=n.length;if(o!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+o);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,n)},match:e=>{const o=ue(e);if(t.length!==o.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+o.join(","));if(!te(t,(e=>j(o,e))))throw new Error("Not all branches were specified when using match. Specified: "+o.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,n)},log:e=>{console.log(e,{constructors:t,constructor:a,params:n})}}}})),n};Ci([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const wi=e=>"inline-command"===e.type||"inline-format"===e.type,xi=e=>"block-command"===e.type||"block-format"===e.type,ki=e=>{const t=t=>yi.error({message:t,pattern:e}),n=(n,o,r)=>{if(void 0!==e.format){let r;if(p(e.format)){if(!te(e.format,m))return t(n+" pattern has non-string items in the `format` array");r=e.format}else{if(!m(e.format))return t(n+" pattern has non-string `format` parameter");r=[e.format]}return yi.value(o(r))}return void 0!==e.cmd?m(e.cmd)?yi.value(r(e.cmd,e.value)):t(n+" pattern has non-string `cmd` parameter"):t(n+" pattern is missing both `format` and `cmd` parameters")};if(!f(e))return t("Raw pattern is not an object");if(!m(e.start))return t("Raw pattern is missing `start` parameter");if(void 0!==e.end){if(!m(e.end))return t("Inline pattern has non-string `end` parameter");if(0===e.start.length&&0===e.end.length)return t("Inline pattern has empty `start` and `end` parameters");let o=e.start,r=e.end;return 0===r.length&&(r=o,o=""),n("Inline",(e=>({type:"inline-format",start:o,end:r,format:e})),((e,t)=>({type:"inline-command",start:o,end:r,cmd:e,value:t})))}return void 0!==e.replacement?m(e.replacement)?0===e.start.length?t("Replacement pattern has empty `start` parameter"):yi.value({type:"inline-command",start:"",end:e.start,cmd:"mceInsertContent",value:e.replacement}):t("Replacement pattern has non-string `replacement` parameter"):0===e.start.length?t("Block pattern has empty `start` parameter"):n("Block",(t=>({type:"block-format",start:e.start,format:t[0]})),((t,n)=>({type:"block-command",start:e.start,cmd:t,value:n})))},Si=e=>(e=>se(e,((e,t)=>e.start.length===t.start.length?0:e.start.length>t.start.length?-1:1)))(K(e,xi)),_i=e=>K(e,wi),Ei=e=>{const t=(e=>{const t=[],n=[];return $(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{n.push(e)}))})),{errors:t,values:n}})(H(e,ki));return $(t.errors,(e=>console.error(e.message,e.pattern))),t.values},Ni=Ct().deviceType,Ri=Ni.isTouch(),Ai=Hs.DOM,Oi=e=>u(e,RegExp),Ti=e=>t=>t.options.get(e),Bi=e=>m(e)||f(e),Di=(e,t="")=>n=>{const o=m(n);if(o){if(-1!==n.indexOf("=")){const r=(e=>{const t=e.indexOf("=")>0?e.split(/[;,](?![^=;,]*(?:[;,]|$))/):e.split(",");return Y(t,((e,t)=>{const n=t.split("="),o=n[0],r=n.length>1?n[1]:o;return e[He(o)]=He(r),e}),{})})(n);return{value:we(r,e.id).getOr(t),valid:o}}return{value:n,valid:o}}return{valid:!1,message:"Must be a string."}},Pi=Ti("iframe_attrs"),Li=Ti("doctype"),Mi=Ti("document_base_url"),Ii=Ti("body_id"),Fi=Ti("body_class"),Ui=Ti("content_security_policy"),zi=Ti("br_in_pre"),ji=Ti("forced_root_block"),Vi=Ti("forced_root_block_attrs"),Hi=Ti("newline_behavior"),$i=Ti("br_newline_selector"),qi=Ti("no_newline_selector"),Wi=Ti("keep_styles"),Ki=Ti("end_container_on_empty_block"),Gi=Ti("automatic_uploads"),Yi=Ti("images_reuse_filename"),Xi=Ti("images_replace_blob_uris"),Qi=Ti("icons"),Ji=Ti("icons_url"),Zi=Ti("images_upload_url"),el=Ti("images_upload_base_path"),tl=Ti("images_upload_credentials"),nl=Ti("images_upload_handler"),ol=Ti("content_css_cors"),rl=Ti("referrer_policy"),sl=Ti("language"),al=Ti("language_url"),il=Ti("indent_use_margin"),ll=Ti("indentation"),dl=Ti("content_css"),cl=Ti("content_style"),ul=Ti("font_css"),ml=Ti("directionality"),fl=Ti("inline_boundaries_selector"),gl=Ti("object_resizing"),pl=Ti("resize_img_proportional"),hl=Ti("placeholder"),bl=Ti("event_root"),vl=Ti("service_message"),yl=Ti("theme"),Cl=Ti("theme_url"),wl=Ti("model"),xl=Ti("model_url"),kl=Ti("inline_boundaries"),Sl=Ti("formats"),_l=Ti("preview_styles"),El=Ti("format_empty_lines"),Nl=Ti("custom_ui_selector"),Rl=Ti("inline"),Al=Ti("hidden_input"),Ol=Ti("submit_patch"),Tl=Ti("add_form_submit_trigger"),Bl=Ti("add_unload_trigger"),Dl=Ti("custom_undo_redo_levels"),Pl=Ti("disable_nodechange"),Ll=Ti("readonly"),Ml=Ti("content_css_cors"),Il=Ti("plugins"),Fl=Ti("external_plugins"),Ul=Ti("block_unsupported_drop"),zl=Ti("visual"),jl=Ti("visual_table_class"),Vl=Ti("visual_anchor_class"),Hl=Ti("iframe_aria_text"),$l=Ti("setup"),ql=Ti("init_instance_callback"),Wl=Ti("urlconverter_callback"),Kl=Ti("auto_focus"),Gl=Ti("browser_spellcheck"),Yl=Ti("protect"),Xl=Ti("paste_block_drop"),Ql=Ti("paste_data_images"),Jl=Ti("paste_preprocess"),Zl=Ti("paste_postprocess"),ed=Ti("paste_webkit_styles"),td=Ti("paste_remove_styles_if_webkit"),nd=Ti("paste_merge_formats"),od=Ti("smart_paste"),rd=Ti("paste_as_text"),sd=Ti("paste_tab_spaces"),ad=Ti("allow_html_data_urls"),id=Ti("text_patterns"),ld=Ti("noneditable_class"),dd=Ti("editable_class"),cd=Ti("noneditable_regexp"),ud=e=>Bt.explode(e.options.get("images_file_types")),md=Ti("table_tab_navigation"),fd=Co,gd=Ro,pd=e=>{const t=e.parentNode;t&&t.removeChild(e)},hd=e=>{const t=br(e);return{count:e.length-t.length,text:t}},bd=e=>{let t;for(;-1!==(t=e.data.lastIndexOf(pr));)e.deleteData(t,1)},vd=(e,t)=>(Cd(e),t),yd=(e,t)=>Ya.isTextPosition(t)?((e,t)=>gd(e)&&t.container()===e?((e,t)=>{const n=hd(e.data.substr(0,t.offset())),o=hd(e.data.substr(t.offset()));return(n.text+o.text).length>0?(bd(e),Ya(e,t.offset()-n.count)):t})(e,t):vd(e,t))(e,t):((e,t)=>t.container()===e.parentNode?((e,t)=>{const n=t.container(),o=((e,t)=>{const n=z(e,t);return-1===n?M.none():M.some(n)})(de(n.childNodes),e).map((e=>e<t.offset()?Ya(n,t.offset()-1):t)).getOr(t);return Cd(e),o})(e,t):vd(e,t))(e,t),Cd=e=>{fd(e)&&xr(e)&&(kr(e)?e.removeAttribute("data-mce-caret"):pd(e)),gd(e)&&(bd(e),0===e.data.length&&pd(e))},wd=Io,xd=Uo,kd=Fo,Sd=(e,t,n)=>{const o=ka(t.getBoundingClientRect(),n);let r,s;if("BODY"===e.tagName){const t=e.ownerDocument.documentElement;r=e.scrollLeft||t.scrollLeft,s=e.scrollTop||t.scrollTop}else{const t=e.getBoundingClientRect();r=e.scrollLeft-t.left,s=e.scrollTop-t.top}o.left+=r,o.right+=r,o.top+=s,o.bottom+=s,o.width=1;let a=t.offsetWidth-t.clientWidth;return a>0&&(n&&(a*=-1),o.left+=a,o.right+=a),o},_d=(e,t,n,o)=>{const r=Js();let s,a;const i=ji(e),l=e.dom,d=()=>{(e=>{const t=na(fn(e),"*[contentEditable=false],video,audio,embed,object");for(let e=0;e<t.length;e++){const n=t[e].dom;let o=n.previousSibling;if(Nr(o)){const e=o.data;1===e.length?o.parentNode.removeChild(o):o.deleteData(e.length-1,1)}o=n.nextSibling,Er(o)&&(1===o.data.length?o.parentNode.removeChild(o):o.deleteData(0,1))}})(t),a&&(Cd(a),a=null),r.on((e=>{l.remove(e.caret),r.clear()})),s&&(clearInterval(s),s=void 0)};return{show:(e,c)=>{let u;if(d(),kd(c))return null;if(!n(c))return a=((e,t)=>{const n=e.ownerDocument.createTextNode(pr),o=e.parentNode;if(t){const t=e.previousSibling;if(yr(t)){if(xr(t))return t;if(Nr(t))return t.splitText(t.data.length-1)}o.insertBefore(n,e)}else{const t=e.nextSibling;if(yr(t)){if(xr(t))return t;if(Er(t))return t.splitText(1),t}e.nextSibling?o.insertBefore(n,e.nextSibling):o.appendChild(n)}return n})(c,e),u=c.ownerDocument.createRange(),Nd(a.nextSibling)?(u.setStart(a,0),u.setEnd(a,0)):(u.setStart(a,1),u.setEnd(a,1)),u;{a=((e,t,n)=>{const o=t.ownerDocument.createElement(e);o.setAttribute("data-mce-caret",n?"before":"after"),o.setAttribute("data-mce-bogus","all"),o.appendChild((()=>{const e=document.createElement("br");return e.setAttribute("data-mce-bogus","1"),e})());const r=t.parentNode;return n?r.insertBefore(o,t):t.nextSibling?r.insertBefore(o,t.nextSibling):r.appendChild(o),o})(i,c,e);const n=Sd(t,c,e);l.setStyle(a,"top",n.top);const d=l.create("div",{class:"mce-visual-caret","data-mce-bogus":"all"});l.setStyles(d,{...n}),l.add(t,d),r.set({caret:d,element:c,before:e}),e&&l.addClass(d,"mce-visual-caret-before"),s=setInterval((()=>{r.on((e=>{o()?l.toggleClass(e.caret,"mce-visual-caret-hidden"):l.addClass(e.caret,"mce-visual-caret-hidden")}))}),500),u=c.ownerDocument.createRange(),u.setStart(a,0),u.setEnd(a,0)}return u},hide:d,getCss:()=>".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}",reposition:()=>{r.on((e=>{const n=Sd(t,e.element,e.before);l.setStyles(e.caret,{...n})}))},destroy:()=>clearInterval(s)}},Ed=()=>Nt.browser.isFirefox(),Nd=e=>wd(e)||xd(e),Rd=e=>Nd(e)||_o(e)&&Ed(),Ad=Mo,Od=Io,Td=Uo,Bd=xo("display","block table table-cell table-caption list-item"),Dd=xr,Pd=Cr,Ld=Co,Md=Fr,Id=e=>e>0,Fd=e=>e<0,Ud=(e,t)=>{let n;for(;n=e(t);)if(!Pd(n))return n;return null},zd=(e,t,n,o,r)=>{const s=new Qo(e,o),a=Od(e)||Pd(e);if(Fd(t)){if(a&&n(e=Ud(s.prev.bind(s),!0)))return e;for(;e=Ud(s.prev.bind(s),r);)if(n(e))return e}if(Id(t)){if(a&&n(e=Ud(s.next.bind(s),!0)))return e;for(;e=Ud(s.next.bind(s),r);)if(n(e))return e}return null},jd=(e,t)=>{for(;e&&e!==t;){if(Bd(e))return e;e=e.parentNode}return null},Vd=(e,t,n)=>jd(e.container(),n)===jd(t.container(),n),Hd=(e,t)=>{if(!t)return null;const n=t.container(),o=t.offset();return Ld(n)?n.childNodes[o+e]:null},$d=(e,t)=>{const n=t.ownerDocument.createRange();return e?(n.setStartBefore(t),n.setEndBefore(t)):(n.setStartAfter(t),n.setEndAfter(t)),n},qd=(e,t,n)=>jd(t,e)===jd(n,e),Wd=(e,t,n)=>{const o=e?"previousSibling":"nextSibling";for(;n&&n!==t;){let e=n[o];if(Dd(e)&&(e=e[o]),Od(e)||Td(e)){if(qd(t,e,n))return e;break}if(Md(e))break;n=n.parentNode}return null},Kd=O($d,!0),Gd=O($d,!1),Yd=(e,t,n)=>{let o;const r=O(Wd,!0,t),s=O(Wd,!1,t);let a=n.startContainer;const i=n.startOffset;if(Cr(a)){Ld(a)||(a=a.parentNode);const e=a.getAttribute("data-mce-caret");if("before"===e&&(o=a.nextSibling,Rd(o)))return Kd(o);if("after"===e&&(o=a.previousSibling,Rd(o)))return Gd(o)}if(!n.collapsed)return n;if(Ro(a)){if(Dd(a)){if(1===e){if(o=s(a),o)return Kd(o);if(o=r(a),o)return Gd(o)}if(-1===e){if(o=r(a),o)return Gd(o);if(o=s(a),o)return Kd(o)}return n}if(Nr(a)&&i>=a.data.length-1)return 1===e&&(o=s(a),o)?Kd(o):n;if(Er(a)&&i<=1)return-1===e&&(o=r(a),o)?Gd(o):n;if(i===a.data.length)return o=s(a),o?Kd(o):n;if(0===i)return o=r(a),o?Gd(o):n}return n},Xd=(e,t)=>M.from(Hd(e?0:-1,t)).filter(Od),Qd=(e,t,n)=>{const o=Yd(e,t,n);return-1===e?Ya.fromRangeStart(o):Ya.fromRangeEnd(o)},Jd=e=>M.from(e.getNode()).map(fn),Zd=(e,t)=>{for(;t=e(t);)if(t.isVisible())return t;return t},ec=(e,t)=>{const n=Vd(e,t);return!(n||!Po(e.getNode()))||n};var tc;!function(e){e[e.Backwards=-1]="Backwards",e[e.Forwards=1]="Forwards"}(tc||(tc={}));const nc=Io,oc=Ro,rc=Co,sc=Po,ac=Fr,ic=e=>Lr(e)||(e=>!!Ur(e)&&!0!==Y(de(e.getElementsByTagName("*")),((e,t)=>e||Or(t)),!1))(e),lc=zr,dc=(e,t)=>e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null,cc=(e,t)=>{if(Id(e)){if(ac(t.previousSibling)&&!oc(t.previousSibling))return Ya.before(t);if(oc(t))return Ya(t,0)}if(Fd(e)){if(ac(t.nextSibling)&&!oc(t.nextSibling))return Ya.after(t);if(oc(t))return Ya(t,t.data.length)}return Fd(e)?sc(t)?Ya.before(t):Ya.after(t):Ya.before(t)},uc=(e,t,n)=>{let o,r,s,a;if(!rc(n)||!t)return null;if(t.isEqual(Ya.after(n))&&n.lastChild){if(a=Ya.after(n.lastChild),Fd(e)&&ac(n.lastChild)&&rc(n.lastChild))return sc(n.lastChild)?Ya.before(n.lastChild):a}else a=t;const i=a.container();let l=a.offset();if(oc(i)){if(Fd(e)&&l>0)return Ya(i,--l);if(Id(e)&&l<i.length)return Ya(i,++l);o=i}else{if(Fd(e)&&l>0&&(r=dc(i,l-1),ac(r)))return!ic(r)&&(s=zd(r,e,lc,r),s)?oc(s)?Ya(s,s.data.length):Ya.after(s):oc(r)?Ya(r,r.data.length):Ya.before(r);if(Id(e)&&l<i.childNodes.length&&(r=dc(i,l),ac(r)))return sc(r)?((e,t)=>{const n=t.nextSibling;return n&&ac(n)?oc(n)?Ya(n,0):Ya.before(n):uc(tc.Forwards,Ya.after(t),e)})(n,r):!ic(r)&&(s=zd(r,e,lc,r),s)?oc(s)?Ya(s,0):Ya.before(s):oc(r)?Ya(r,0):Ya.after(r);o=r||a.getNode()}if((Id(e)&&a.isAtEnd()||Fd(e)&&a.isAtStart())&&(o=zd(o,e,L,n,!0),lc(o,n)))return cc(e,o);r=zd(o,e,lc,n);const d=Be(K(((e,t)=>{const n=[];for(;e&&e!==t;)n.push(e),e=e.parentNode;return n})(i,n),nc));return!d||r&&d.contains(r)?r?cc(e,r):null:(a=Id(e)?Ya.after(d):Ya.before(d),a)},mc=e=>({next:t=>uc(tc.Forwards,t,e),prev:t=>uc(tc.Backwards,t,e)}),fc=e=>Ya.isTextPosition(e)?0===e.offset():Fr(e.getNode()),gc=e=>{if(Ya.isTextPosition(e)){const t=e.container();return e.offset()===t.data.length}return Fr(e.getNode(!0))},pc=(e,t)=>!Ya.isTextPosition(e)&&!Ya.isTextPosition(t)&&e.getNode()===t.getNode(!0),hc=(e,t,n)=>{const o=mc(t);return M.from(e?o.next(n):o.prev(n))},bc=(e,t,n)=>hc(e,t,n).bind((o=>Vd(n,o,t)&&((e,t,n)=>{return e?!pc(t,n)&&(o=t,!(!Ya.isTextPosition(o)&&Po(o.getNode())))&&gc(t)&&fc(n):!pc(n,t)&&fc(t)&&gc(n);var o})(e,n,o)?hc(e,t,o):M.some(o))),vc=(e,t,n,o)=>bc(e,t,n).bind((n=>o(n)?vc(e,t,n,o):M.some(n))),yc=(e,t)=>{const n=e?t.firstChild:t.lastChild;return Ro(n)?M.some(Ya(n,e?0:n.data.length)):n?Fr(n)?M.some(e?Ya.before(n):Po(o=n)?Ya.before(o):Ya.after(o)):((e,t,n)=>{const o=e?Ya.before(n):Ya.after(n);return hc(e,t,o)})(e,t,n):M.none();var o},Cc=O(hc,!0),wc=O(hc,!1),xc=O(yc,!0),kc=O(yc,!1),Sc="_mce_caret",_c=e=>Co(e)&&e.id===Sc,Ec=(e,t)=>{for(;t&&t!==e;){if(t.id===Sc)return t;t=t.parentNode}return null},Nc=e=>xe(e,"name"),Rc=e=>Bt.isArray(e.start),Ac=e=>!(!Nc(e)&&b(e.forward))||e.forward,Oc=(e,t)=>(Co(t)&&e.isBlock(t)&&!t.innerHTML&&(t.innerHTML='<br data-mce-bogus="1" />'),t),Tc=(e,t)=>kc(e).fold(P,(e=>(t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0))),Bc=(e,t,n)=>!(!(e=>!1===e.hasChildNodes())(t)||!Ec(e,t)||(((e,t)=>{const n=e.ownerDocument.createTextNode(pr);e.appendChild(n),t.setStart(n,0),t.setEnd(n,0)})(t,n),0)),Dc=(e,t,n,o)=>{const r=n[t?"start":"end"];let s,a,i,l;const d=e.getRoot();if(r){for(i=r[0],a=d,s=r.length-1;s>=1;s--){if(l=a.childNodes,Bc(d,a,o))return!0;if(r[s]>l.length-1)return!!Bc(d,a,o)||Tc(a,o);a=l[r[s]]}3===a.nodeType&&(i=Math.min(r[0],a.nodeValue.length)),1===a.nodeType&&(i=Math.min(r[0],a.childNodes.length)),t?o.setStart(a,i):o.setEnd(a,i)}return!0},Pc=e=>Ro(e)&&e.data.length>0,Lc=(e,t,n)=>{let o,r,s,a,i=e.get(n.id+"_"+t);const l=n.keep;let d,c;if(i){if(o=i.parentNode,"start"===t?(l?i.hasChildNodes()?(o=i.firstChild,r=1):Pc(i.nextSibling)?(o=i.nextSibling,r=0):Pc(i.previousSibling)?(o=i.previousSibling,r=i.previousSibling.data.length):(o=i.parentNode,r=e.nodeIndex(i)+1):r=e.nodeIndex(i),d=o,c=r):(l?i.hasChildNodes()?(o=i.firstChild,r=1):Pc(i.previousSibling)?(o=i.previousSibling,r=i.previousSibling.data.length):(o=i.parentNode,r=e.nodeIndex(i)):r=e.nodeIndex(i),d=o,c=r),!l){for(a=i.previousSibling,s=i.nextSibling,Bt.each(Bt.grep(i.childNodes),(e=>{Ro(e)&&(e.nodeValue=e.nodeValue.replace(/\uFEFF/g,""))}));i=e.get(n.id+"_"+t);)e.remove(i,!0);a&&s&&a.nodeType===s.nodeType&&Ro(a)&&!Nt.browser.isOpera()&&(r=a.nodeValue.length,a.appendData(s.nodeValue),e.remove(s),d=a,c=r)}return M.some(Ya(d,c))}return M.none()},Mc=(e,t,n)=>((e,t,n)=>2===t?fi(br,n,e):3===t?(e=>{const t=e.getRng();return{start:si(e.dom.getRoot(),Ya.fromRangeStart(t)),end:si(e.dom.getRoot(),Ya.fromRangeEnd(t)),forward:e.isForward()}})(e):t?(e=>({rng:e.getRng(),forward:e.isForward()}))(e):pi(e,!1))(e,t,n),Ic=(e,t)=>{((e,t)=>{const n=e.dom;if(t){if(Rc(t))return((e,t)=>{const n=e.createRng();return Dc(e,!0,t,n)&&Dc(e,!1,t,n)?M.some({range:n,forward:Ac(t)}):M.none()})(n,t);if((e=>m(e.start))(t))return M.some(((e,t)=>{const n=e.createRng(),o=ai(e.getRoot(),t.start);n.setStart(o.container(),o.offset());const r=ai(e.getRoot(),t.end);return n.setEnd(r.container(),r.offset()),{range:n,forward:Ac(t)}})(n,t));if((e=>xe(e,"id"))(t))return((e,t)=>{const n=Lc(e,"start",t),o=Lc(e,"end",t);return Pt(n,o.or(n),((n,o)=>{const r=e.createRng();return r.setStart(Oc(e,n.container()),n.offset()),r.setEnd(Oc(e,o.container()),o.offset()),{range:r,forward:Ac(t)}}))})(n,t);if(Nc(t))return((e,t)=>M.from(e.select(t.name)[t.index]).map((t=>{const n=e.createRng();return n.selectNode(t),{range:n,forward:!0}})))(n,t);if((e=>xe(e,"rng"))(t))return M.some({range:t.rng,forward:Ac(t)})}return M.none()})(e,t).each((({range:t,forward:n})=>{e.setRng(t,n)}))},Fc=e=>Co(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type"),Uc=(fr,e=>"\xa0"===e);const zc=e=>""!==e&&-1!==" \f\n\r\t\v".indexOf(e),jc=e=>!zc(e)&&!Uc(e)&&!gr(e),Vc=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Hc=e=>(e=>({value:e}))(Vc(e.red)+Vc(e.green)+Vc(e.blue)),$c=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,qc=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,Wc=(e,t,n,o)=>({red:e,green:t,blue:n,alpha:o}),Kc=(e,t,n,o)=>{const r=parseInt(e,10),s=parseInt(t,10),a=parseInt(n,10),i=parseFloat(o);return Wc(r,s,a,i)},Gc=e=>(e=>{if("transparent"===e)return M.some(Wc(0,0,0,0));const t=$c.exec(e);if(null!==t)return M.some(Kc(t[1],t[2],t[3],"1"));const n=qc.exec(e);return null!==n?M.some(Kc(n[1],n[2],n[3],n[4])):M.none()})(e).map(Hc).map((e=>"#"+e.value)).getOr(e),Yc=e=>!!e.nodeType,Xc=(e,t,n)=>{const o=n.startOffset;let r=n.startContainer;var s;if((r!==n.endContainer||!(s=r.childNodes[o])||!/^(IMG)$/.test(s.nodeName))&&Co(r)){const s=r.childNodes;let a;o<s.length?(r=s[o],a=new Qo(r,e.getParent(r,e.isBlock))):(r=s[s.length-1],a=new Qo(r,e.getParent(r,e.isBlock)),a.next(!0));for(let e=a.current();e;e=a.next())if(Ro(e)&&!eu(e))return n.setStart(e,0),void t.setRng(n)}},Qc=(e,t,n)=>{if(e){const o=t?"nextSibling":"previousSibling";for(e=n?e:e[o];e;e=e[o])if(Co(e)||!eu(e))return e}},Jc=(e,t)=>(Yc(t)&&(t=t.nodeName),!!e.schema.getTextBlockElements()[t.toLowerCase()]),Zc=(e,t,n)=>e.schema.isValidChild(t,n),eu=(e,t=!1)=>{if(C(e)&&Ro(e)){const n=t?e.data.replace(/ /g,"\xa0"):e.data;return Vr(n)}return!1},tu=(e,t)=>(w(e)?e=e(t):C(t)&&(e=e.replace(/%(\w+)/g,((e,n)=>t[n]||e))),e),nu=(e,t)=>(t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()),ou=(e,t)=>("color"!==t&&"backgroundColor"!==t||(e=Gc(e)),"fontWeight"===t&&700===e&&(e="bold"),"fontFamily"===t&&(e=e.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),""+e),ru=(e,t,n)=>ou(e.getStyle(t,n),n),su=(e,t)=>{let n;return e.getParent(t,(t=>(n=e.getStyle(t,"text-decoration"),n&&"none"!==n))),n},au=(e,t,n)=>e.getParents(t,n,e.getRoot()),iu=e=>ke(e,"block"),lu=e=>ke(e,"selector"),du=e=>ke(e,"inline"),cu=e=>lu(e)&&!1!==e.expand&&!du(e),uu=Fc,mu=au,fu=eu,gu=Jc,pu=(e,t)=>{let n=t;for(;n;){if(Co(n)&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},hu=(e,t,n,o)=>{const r=t.data;for(let t=n;e?t>=0:t<r.length;e?t--:t++)if(o(r.charAt(t)))return e?t+1:t;return-1},bu=(e,t,n)=>hu(e,t,n,(e=>Uc(e)||zc(e))),vu=(e,t,n)=>hu(e,t,n,jc),yu=(e,t,n,o,r,s)=>{let a;const i=e.getParent(n,e.isBlock)||t,l=(t,n,o)=>{const s=Ca(e),l=r?s.backwards:s.forwards;return M.from(l(t,n,((e,t)=>uu(e.parentNode)?-1:(a=e,o(r,e,t))),i))};return l(n,o,bu).bind((e=>s?l(e.container,e.offset+(r?-1:0),vu):M.some(e))).orThunk((()=>a?M.some({container:a,offset:r?0:a.length}):M.none()))},Cu=(e,t,n,o,r)=>{Ro(o)&&Ke(o.data)&&o[r]&&(o=o[r]);const s=mu(e,o);for(let o=0;o<s.length;o++)for(let r=0;r<t.length;r++){const a=t[r];if((!C(a.collapsed)||a.collapsed===n.collapsed)&&lu(a)&&e.is(s[o],a.selector))return s[o]}return o},wu=(e,t,n,o)=>{let r=n;const s=e.dom,a=s.getRoot(),i=t[0];if(iu(i)&&(r=i.wrapper?null:s.getParent(n,i.block,a)),!r){const t=s.getParent(n,"LI,TD,TH");r=s.getParent(Ro(n)?n.parentNode:n,(t=>t!==a&&gu(e,t)),t)}if(r&&iu(i)&&i.wrapper&&(r=mu(s,r,"ul,ol").reverse()[0]||r),!r)for(r=n;r[o]&&!s.isBlock(r[o])&&(r=r[o],!nu(r,"br")););return r||n},xu=(e,t,n,o)=>{const r=n.parentNode;return!C(n[o])&&(!(r!==t&&!y(r)&&!e.isBlock(r))||xu(e,t,r,o))},ku=(e,t,n,o,r)=>{let s=n;const a=r?"previousSibling":"nextSibling",i=e.getRoot();if(Ro(n)&&!fu(n)&&(r?o>0:o<n.data.length))return n;for(;;){if(!t[0].block_expand&&e.isBlock(s))return s;for(let t=s[a];t;t=t[a]){const n=Ro(t)&&!xu(e,i,t,a);if(!uu(t)&&(!Po(l=t)||!l.getAttribute("data-mce-bogus")||l.nextSibling)&&!fu(t,n))return s}if(s===i||s.parentNode===i){n=s;break}s=s.parentNode}var l;return n},Su=e=>uu(e.parentNode)||uu(e),_u=(e,t,n,o=!1)=>{let{startContainer:r,startOffset:s,endContainer:a,endOffset:i}=t;const l=e.dom,d=n[0];return Co(r)&&r.hasChildNodes()&&(r=Aa(r,s),Ro(r)&&(s=0)),Co(a)&&a.hasChildNodes()&&(a=Aa(a,t.collapsed?i:i-1),Ro(a)&&(i=a.nodeValue.length)),r=pu(l,r),a=pu(l,a),Su(r)&&(r=uu(r)?r:r.parentNode,r=t.collapsed?r.previousSibling||r:r.nextSibling||r,Ro(r)&&(s=t.collapsed?r.length:0)),Su(a)&&(a=uu(a)?a:a.parentNode,a=t.collapsed?a.nextSibling||a:a.previousSibling||a,Ro(a)&&(i=t.collapsed?0:a.length)),t.collapsed&&(yu(l,e.getBody(),r,s,!0,o).each((({container:e,offset:t})=>{r=e,s=t})),yu(l,e.getBody(),a,i,!1,o).each((({container:e,offset:t})=>{a=e,i=t}))),(du(d)||d.block_expand)&&(du(d)&&Ro(r)&&0!==s||(r=ku(l,n,r,s,!0)),du(d)&&Ro(a)&&i!==a.nodeValue.length||(a=ku(l,n,a,i,!1))),cu(d)&&(r=Cu(l,n,t,r,"previousSibling"),a=Cu(l,n,t,a,"nextSibling")),(iu(d)||lu(d))&&(r=wu(e,n,r,"previousSibling"),a=wu(e,n,a,"nextSibling"),iu(d)&&(l.isBlock(r)||(r=ku(l,n,r,s,!0)),l.isBlock(a)||(a=ku(l,n,a,i,!1)))),Co(r)&&(s=l.nodeIndex(r),r=r.parentNode),Co(a)&&(i=l.nodeIndex(a)+1,a=a.parentNode),{startContainer:r,startOffset:s,endContainer:a,endOffset:i}},Eu=(e,t,n)=>{const o=t.startOffset,r=Aa(t.startContainer,o),s=t.endOffset,a=Aa(t.endContainer,s-1),i=e=>{const t=e[0];Ro(t)&&t===r&&o>=t.data.length&&e.splice(0,1);const n=e[e.length-1];return 0===s&&e.length>0&&n===a&&Ro(n)&&e.splice(e.length-1,1),e},l=(e,t,n)=>{const o=[];for(;e&&e!==n;e=e[t])o.push(e);return o},d=(t,n)=>e.getParent(t,(e=>e.parentNode===n),n),c=(e,t,o)=>{const r=o?"nextSibling":"previousSibling";for(let s=e,a=s.parentNode;s&&s!==t;s=a){a=s.parentNode;const t=l(s===e?s:s[r],r);t.length&&(o||t.reverse(),n(i(t)))}};if(r===a)return n(i([r]));const u=e.findCommonAncestor(r,a);if(e.isChildOf(r,a))return c(r,u,!0);if(e.isChildOf(a,r))return c(a,u);const m=d(r,u)||r,f=d(a,u)||a;c(r,m,!0);const g=l(m===r?m:m.nextSibling,"nextSibling",f===a?f.nextSibling:f);g.length&&n(i(g)),c(a,f)},Nu=e=>{const t=[];if(e)for(let n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},Ru=(e,t)=>{const n=na(t,"td[data-mce-selected],th[data-mce-selected]");return n.length>0?n:(e=>K((e=>ee(e,(e=>{const t=Ra(e);return t?[fn(t)]:[]})))(e),ir))(e)},Au=e=>Ru(Nu(e.selection.getSel()),fn(e.getBody())),Ou=(e,t)=>Wo(e,"table",t),Tu=e=>On(e).fold(N([e]),(t=>[e].concat(Tu(t)))),Bu=e=>Tn(e).fold(N([e]),(t=>"br"===Mt(t)?Sn(t).map((t=>[e].concat(Bu(t)))).getOr([]):[e].concat(Bu(t)))),Du=(e,t)=>Pt((e=>{const t=e.startContainer,n=e.startOffset;return Ro(t)?0===n?M.some(fn(t)):M.none():M.from(t.childNodes[n]).map(fn)})(t),(e=>{const t=e.endContainer,n=e.endOffset;return Ro(t)?n===t.data.length?M.some(fn(t)):M.none():M.from(t.childNodes[n-1]).map(fn)})(t),((t,n)=>{const o=Q(Tu(e),O(vn,t)),r=Q(Bu(e),O(vn,n));return o.isSome()&&r.isSome()})).getOr(!1),Pu=(e,t,n,o)=>{const r=n,s=new Qo(n,r),a=ve(e.schema.getMoveCaretBeforeOnEnterElements(),((e,t)=>!j(["td","th","table"],t.toLowerCase())));do{if(Ro(n)&&0!==Bt.trim(n.nodeValue).length)return void(o?t.setStart(n,0):t.setEnd(n,n.nodeValue.length));if(a[n.nodeName])return void(o?t.setStartBefore(n):"BR"===n.nodeName?t.setEndBefore(n):t.setEndAfter(n))}while(n=o?s.next():s.prev());"BODY"===r.nodeName&&(o?t.setStart(r,0):t.setEnd(r,r.childNodes.length))},Lu=e=>{const t=e.selection.getSel();return t&&t.rangeCount>0},Mu=(e,t)=>{const n=Au(e);n.length>0?$(n,(n=>{const o=n.dom,r=e.dom.createRng();r.setStartBefore(o),r.setEndAfter(o),t(r,!0)})):t(e.selection.getRng(),!1)},Iu=(e,t,n)=>{const o=pi(e,t);n(o),e.moveToBookmark(o)},Fu=((e,t)=>{const n=t=>e(t)?M.from(t.dom.nodeValue):M.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return n(t).getOr("")},getOption:n,set:(t,n)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}})(zt),Uu=e=>Fu.get(e),zu=e=>Fu.getOption(e),ju=['pre[class*=language-][contenteditable="false"]',"figure.image","div[data-ephox-embed-iri]","div.tiny-pageembed","div.mce-toc","div[data-mce-toc]"],Vu=(e,t,n,o,r,s)=>{const{uid:a=t,...i}=n;on(e,oa()),qt(e,`${sa()}`,a),qt(e,`${ra()}`,o);const{attributes:l={},classes:d=[]}=r(a,i);if(Wt(e,l),((e,t)=>{$(t,(t=>{on(e,t)}))})(e,d),s){d.length>0&&qt(e,`${ia()}`,d.join(","));const t=ue(l);t.length>0&&qt(e,`${la()}`,t.join(","))}},Hu=(e,t,n,o,r)=>{const s=un("span",e);return Vu(s,t,n,o,r,!1),s},$u=(e,t,n,o,r,s)=>{const a=[],i=Hu(e.getDoc(),n,s,o,r),l=Js(),d=()=>{l.clear()},c=e=>{$(e,u)},u=t=>{switch(((e,t,n,o)=>kn(t).fold((()=>"skipping"),(r=>"br"===o||(e=>zt(e)&&Uu(e)===pr)(t)?"valid":(e=>Ut(e)&&an(e,oa()))(t)?"existing":_c(t.dom)?"caret":V(ju,(e=>hn(t,e)))?"valid-block":Zc(e,n,o)&&Zc(e,Mt(r),n)?"valid":"invalid-child")))(e,t,"span",Mt(t))){case"invalid-child":{d();const e=Rn(t);c(e),d();break}case"valid-block":d(),Vu(t,n,s,o,r,!0);break;case"valid":{const e=l.get().getOrThunk((()=>{const e=ba(i);return a.push(e),l.set(e),e}));((e,t)=>{Xn(e,t),Zn(t,e)})(t,e);break}}};return Eu(e.dom,t,(e=>{d(),(e=>{const t=H(e,fn);c(t)})(e)})),a},qu=e=>{const t=(()=>{const e={};return{register:(t,n)=>{e[t]={name:t,settings:n}},lookup:t=>we(e,t).map((e=>e.settings)),getNames:()=>ue(e)}})();((e,t)=>{const n=ra(),o=e=>M.from(e.attr(n)).bind(t.lookup),r=e=>{var t,n;e.attr(sa(),null),e.attr(ra(),null),e.attr(aa(),null);const o=M.from(e.attr(la())).map((e=>e.split(","))).getOr([]),r=M.from(e.attr(ia())).map((e=>e.split(","))).getOr([]);$(o,(t=>e.attr(t,null)));const s=null!==(n=null===(t=e.attr("class"))||void 0===t?void 0:t.split(" "))&&void 0!==n?n:[],a=oe(s,[oa()].concat(r));e.attr("class",a.length>0?a.join(" "):null),e.attr(ia(),null),e.attr(la(),null)};e.serializer.addTempAttr(aa()),e.serializer.addAttributeFilter(n,(e=>{for(const t of e)o(t).each((e=>{!1===e.persistent&&("span"===t.name?t.unwrap():r(t))}))}))})(e,t);const n=((e,t)=>{const n=Ws({}),o=()=>({listeners:[],previous:Js()}),r=(e,t)=>{s(e,(e=>(t(e),e)))},s=(e,t)=>{const r=n.get(),s=t(we(r,e).getOrThunk(o));r[e]=s,n.set(r)},a=(t,n)=>{$(ma(e,t),(e=>{n?qt(e,aa(),"true"):Xt(e,aa())}))},i=ea((()=>{const n=se(t.getNames());$(n,(t=>{s(t,(n=>{const o=n.previous.get();return ca(e,M.some(t)).fold((()=>{o.each((e=>{(e=>{r(e,(t=>{$(t.listeners,(t=>t(!1,e)))}))})(t),n.previous.clear(),a(e,!1)}))}),(({uid:e,name:t,elements:s})=>{Dt(o,e)||(o.each((e=>a(e,!1))),((e,t,n)=>{r(e,(o=>{$(o.listeners,(o=>o(!0,e,{uid:t,nodes:H(n,(e=>e.dom))})))}))})(t,e,s),n.previous.set(e),a(e,!0))})),{previous:n.previous,listeners:n.listeners}}))}))}),30);return e.on("remove",(()=>{i.cancel()})),e.on("NodeChange",(()=>{i.throttle()})),{addListener:(e,t)=>{s(e,(e=>({previous:e.previous,listeners:e.listeners.concat([t])})))}}})(e,t),o=Ht("span"),r=e=>{$(e,(e=>{o(e)?oo(e):(e=>{sn(e,oa()),Xt(e,`${sa()}`),Xt(e,`${ra()}`),Xt(e,`${aa()}`);const t=Gt(e,`${la()}`).map((e=>e.split(","))).getOr([]),n=Gt(e,`${ia()}`).map((e=>e.split(","))).getOr([]);var o;$(t,(t=>Xt(e,t))),o=e,$(n,(e=>{sn(o,e)})),Xt(e,`${ia()}`),Xt(e,`${la()}`)})(e)}))};return{register:(e,n)=>{t.register(e,n)},annotate:(n,o)=>{t.lookup(n).each((t=>{((e,t,n,o)=>{e.undoManager.transact((()=>{const r=e.selection,s=r.getRng(),a=Au(e).length>0,i=pa("mce-annotation");if(s.collapsed&&!a&&((e,t)=>{const n=_u(e,t,[{inline:"span"}]);t.setStart(n.startContainer,n.startOffset),t.setEnd(n.endContainer,n.endOffset),e.selection.setRng(t)})(e,s),r.getRng().collapsed&&!a){const s=Hu(e.getDoc(),i,o,t,n.decorate);so(s,fr),r.getRng().insertNode(s.dom),r.select(s.dom)}else Iu(r,!1,(()=>{Mu(e,(r=>{$u(e,r,i,t,n.decorate,o)}))}))}))})(e,n,t,o)}))},annotationChanged:(e,t)=>{n.addListener(e,t)},remove:t=>{const n=e.selection.getBookmark();ca(e,M.some(t)).each((({elements:e})=>{r(e)})),e.selection.moveToBookmark(n)},removeAll:t=>{const n=e.selection.getBookmark();fe(fa(e,t),((e,t)=>{r(e)})),e.selection.moveToBookmark(n)},getAll:t=>{const n=fa(e,t);return ge(n,(e=>H(e,(e=>e.dom))))}}},Wu=e=>({getBookmark:O(Mc,e),moveToBookmark:O(Ic,e)});Wu.isBookmarkNode=Fc;const Ku=(e,t,n)=>!n.collapsed&&V(n.getClientRects(),(n=>((e,t,n)=>t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom)(n,e,t))),Gu=(e,t,n)=>e.dispatch(t,n),Yu=(e,t,n,o)=>e.dispatch("FormatApply",{format:t,node:n,vars:o}),Xu=(e,t,n,o)=>e.dispatch("FormatRemove",{format:t,node:n,vars:o}),Qu=(e,t)=>e.dispatch("SetContent",t),Ju=(e,t)=>e.dispatch("GetContent",t),Zu=(e,t)=>e.dispatch("PastePlainTextToggle",{state:t}),em={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,ESC:27,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,modifierPressed:e=>e.shiftKey||e.ctrlKey||e.altKey||em.metaKeyPressed(e),metaKeyPressed:e=>Nt.os.isMacOS()||Nt.os.isiOS()?e.metaKey:e.ctrlKey&&!e.altKey},tm="data-mce-selected",nm=Math.abs,om=Math.round,rm={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]},sm=(e,t)=>{const n=t.dom,o=t.getDoc(),r=document,s=t.getBody();let a,i,l,d,c,u,m,f,g,p,h,b,v,y,w;const x=e=>C(e)&&(Lo(e)||n.is(e,"figure.image")),k=e=>Uo(e)||n.hasClass(e,"mce-preview-object"),S=e=>{const n=e.target;((e,t)=>{if((e=>"longpress"===e.type||0===e.type.indexOf("touch"))(e)){const n=e.touches[0];return x(e.target)&&!Ku(n.clientX,n.clientY,t)}return x(e.target)&&!Ku(e.clientX,e.clientY,t)})(e,t.selection.getRng())&&!e.isDefaultPrevented()&&t.selection.select(n)},_=e=>n.is(e,"figure.image")?[e.querySelector("img")]:n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?[e,e.firstElementChild]:[e],E=e=>{const o=gl(t);return!!o&&"false"!==e.getAttribute("data-mce-resize")&&e!==t.getBody()&&(n.hasClass(e,"mce-preview-object")?hn(fn(e.firstElementChild),o):hn(fn(e),o))},N=(e,o,r)=>{if(C(r)){const s=_(e);$(s,(e=>{e.style[o]||!t.schema.isValid(e.nodeName.toLowerCase(),o)?n.setStyle(e,o,r):n.setAttrib(e,o,""+r)}))}},R=(e,t,n)=>{N(e,"width",t),N(e,"height",n)},A=e=>{let o,r,c,C,S;o=e.screenX-u,r=e.screenY-m,b=o*d[2]+f,v=r*d[3]+g,b=b<5?5:b,v=v<5?5:v,c=(x(a)||k(a))&&!1!==pl(t)?!em.modifierPressed(e):em.modifierPressed(e),c&&(nm(o)>nm(r)?(v=om(b*p),b=om(v/p)):(b=om(v/p),v=om(b*p))),R(i,b,v),C=d.startPos.x+o,S=d.startPos.y+r,C=C>0?C:0,S=S>0?S:0,n.setStyles(l,{left:C,top:S,display:"block"}),l.innerHTML=b+" &times; "+v,d[2]<0&&i.clientWidth<=b&&n.setStyle(i,"left",void 0+(f-b)),d[3]<0&&i.clientHeight<=v&&n.setStyle(i,"top",void 0+(g-v)),o=s.scrollWidth-y,r=s.scrollHeight-w,o+r!==0&&n.setStyles(l,{left:C-o,top:S-r}),h||(((e,t,n,o,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:n,height:o,origin:r})})(t,a,f,g,"corner-"+d.name),h=!0)},O=()=>{const e=h;h=!1,e&&(N(a,"width",b),N(a,"height",v)),n.unbind(o,"mousemove",A),n.unbind(o,"mouseup",O),r!==o&&(n.unbind(r,"mousemove",A),n.unbind(r,"mouseup",O)),n.remove(i),n.remove(l),n.remove(c),T(a),e&&(((e,t,n,o,r)=>{e.dispatch("ObjectResized",{target:t,width:n,height:o,origin:r})})(t,a,b,v,"corner-"+d.name),n.setAttrib(a,"style",n.getAttrib(a,"style"))),t.nodeChanged()},T=e=>{M();const h=n.getPos(e,s),C=h.x,x=h.y,S=e.getBoundingClientRect(),N=S.width||S.right-S.left,T=S.height||S.bottom-S.top;a!==e&&(D(),a=e,b=v=0);const B=t.dispatch("ObjectSelected",{target:e});E(e)&&!B.isDefaultPrevented()?fe(rm,((e,t)=>{let h;h=n.get("mceResizeHandle"+t),h&&n.remove(h),h=n.add(s,"div",{id:"mceResizeHandle"+t,"data-mce-bogus":"all",class:"mce-resizehandle",unselectable:!0,style:"cursor:"+t+"-resize; margin:0; padding:0"}),n.bind(h,"mousedown",(h=>{h.stopImmediatePropagation(),h.preventDefault(),(h=>{const b=_(a)[0];var v;u=h.screenX,m=h.screenY,f=b.clientWidth,g=b.clientHeight,p=g/f,d=e,d.name=t,d.startPos={x:N*e[0]+C,y:T*e[1]+x},y=s.scrollWidth,w=s.scrollHeight,c=n.add(s,"div",{class:"mce-resize-backdrop","data-mce-bogus":"all"}),n.setStyles(c,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}),i=k(v=a)?n.create("img",{src:Nt.transparentSrc}):v.cloneNode(!0),n.addClass(i,"mce-clonedresizable"),n.setAttrib(i,"data-mce-bogus","all"),i.contentEditable="false",n.setStyles(i,{left:C,top:x,margin:0}),R(i,N,T),i.removeAttribute(tm),s.appendChild(i),n.bind(o,"mousemove",A),n.bind(o,"mouseup",O),r!==o&&(n.bind(r,"mousemove",A),n.bind(r,"mouseup",O)),l=n.add(s,"div",{class:"mce-resize-helper","data-mce-bogus":"all"},f+" &times; "+g)})(h)})),e.elm=h,n.setStyles(h,{left:N*e[0]+C-h.offsetWidth/2,top:T*e[1]+x-h.offsetHeight/2})})):D(!1)},B=Zs(T,0),D=(e=!0)=>{B.cancel(),M(),a&&e&&a.removeAttribute(tm),fe(rm,((e,t)=>{const o=n.get("mceResizeHandle"+t);o&&(n.unbind(o),n.remove(o))}))},P=(e,t)=>C(e)&&n.isChildOf(e,t),L=o=>{if(h||t.removed||t.composing)return;const r="mousedown"===o.type?o.target:e.getNode(),a=Go(fn(r),"table,img,figure.image,hr,video,span.mce-preview-object").map((e=>e.dom)).getOrUndefined(),i=C(a)?n.getAttrib(a,tm,"1"):"1";if($(n.select("img[data-mce-selected],hr[data-mce-selected]"),(e=>{e.removeAttribute(tm)})),P(a,s)){I();const t=e.getStart(!0);if(P(t,a)&&P(e.getEnd(!0),a))return n.setAttrib(a,tm,i),void B.throttle(a)}D()},M=()=>{fe(rm,(e=>{e.elm&&(n.unbind(e.elm),delete e.elm)}))},I=()=>{try{t.getDoc().execCommand("enableObjectResizing",!1,"false")}catch(e){}};return t.on("init",(()=>{I(),t.on("NodeChange ResizeEditor ResizeWindow ResizeContent drop",L),t.on("keyup compositionend",(e=>{a&&"TABLE"===a.nodeName&&L(e)})),t.on("hide blur",D),t.on("contextmenu longpress",S,!0)})),t.on("remove",M),{isResizable:E,showResizeRect:T,hideResizeRect:D,updateResizeRect:L,destroy:()=>{B.cancel(),a=i=c=null}}},am=(e,t,n)=>{const o=e.document.createRange();var r;return r=o,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,n)=>{e.setEnd(t.dom,n)}),(t=>{e.setEndAfter(t.dom)}))})(o,n),o},im=(e,t,n,o,r)=>{const s=e.document.createRange();return s.setStart(t.dom,n),s.setEnd(o.dom,r),s},lm=Ci([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),dm=(e,t,n)=>t(fn(n.startContainer),n.startOffset,fn(n.endContainer),n.endOffset);lm.ltr,lm.rtl;const cm=(e,t,n,o)=>({start:e,soffset:t,finish:n,foffset:o}),um=document.caretPositionFromPoint?(e,t,n)=>{var o,r;return M.from(null===(r=(o=e.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(o,t,n)).bind((t=>{if(null===t.offsetNode)return M.none();const n=e.dom.createRange();return n.setStart(t.offsetNode,t.offset),n.collapse(),M.some(n)}))}:document.caretRangeFromPoint?(e,t,n)=>{var o,r;return M.from(null===(r=(o=e.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(o,t,n))}:M.none,mm=Ci([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),fm={before:mm.before,on:mm.on,after:mm.after,cata:(e,t,n,o)=>e.fold(t,n,o),getStart:e=>e.fold(R,R,R)},gm=Ci([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),pm={domRange:gm.domRange,relative:gm.relative,exact:gm.exact,exactFromRange:e=>gm.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>fn(e.startContainer),relative:(e,t)=>fm.getStart(e),exact:(e,t,n,o)=>e}))(e);return xn(t)},range:cm},hm=(e,t)=>{const n=Mt(e);return"input"===n?fm.after(e):j(["br","img"],n)?0===t?fm.before(e):fm.after(e):fm.on(e,t)},bm=(e,t)=>{const n=e.fold(fm.before,hm,fm.after),o=t.fold(fm.before,hm,fm.after);return pm.relative(n,o)},vm=(e,t,n,o)=>{const r=hm(e,t),s=hm(n,o);return pm.relative(r,s)},ym=(e,t)=>{const n=(t||document).createDocumentFragment();return $(e,(e=>{n.appendChild(e.dom)})),fn(n)},Cm=e=>{const t=pm.getWin(e).dom,n=(e,n,o,r)=>im(t,e,n,o,r),o=(e=>e.match({domRange:e=>{const t=fn(e.startContainer),n=fn(e.endContainer);return vm(t,e.startOffset,n,e.endOffset)},relative:bm,exact:vm}))(e);return((e,t)=>{const n=((e,t)=>t.match({domRange:e=>({ltr:N(e),rtl:M.none}),relative:(t,n)=>({ltr:De((()=>am(e,t,n))),rtl:De((()=>M.some(am(e,n,t))))}),exact:(t,n,o,r)=>({ltr:De((()=>im(e,t,n,o,r))),rtl:De((()=>M.some(im(e,o,r,t,n))))})}))(e,t);return((e,t)=>{const n=t.ltr();return n.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>lm.rtl(fn(e.endContainer),e.endOffset,fn(e.startContainer),e.startOffset))).getOrThunk((()=>dm(0,lm.ltr,n))):dm(0,lm.ltr,n)})(0,n)})(t,o).match({ltr:n,rtl:n})},wm=(e,t,n)=>{return(o=n.defaultView,r=e,s=t,((e,t,n)=>{const o=fn(e.document);return um(o,t,n).map((e=>cm(fn(e.startContainer),e.startOffset,fn(e.endContainer),e.endOffset)))})(o,r,s)).map((e=>{const t=n.createRange();return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),t})).getOrUndefined();var o,r,s},xm=(e,t)=>e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset,km=(e,t,n)=>null!==((e,t,n)=>{for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null})(e,t,n),Sm=(e,t,n)=>km(e,t,(e=>e.nodeName===n)),_m=e=>e&&"TABLE"===e.nodeName,Em=e=>e&&/^(TD|TH|CAPTION)$/.test(e.nodeName),Nm=(e,t)=>xr(e)&&!1===km(e,t,_c),Rm=(e,t,n)=>{const o=new Qo(t,e.getParent(t.parentNode,e.isBlock)||e.getRoot());for(;t=o[n?"prev":"next"]();)if(Po(t))return!0},Am=(e,t,n,o,r)=>{let s;const a=e.getRoot();let i;const l=e.schema.getNonEmptyElements(),d=e.getParent(r.parentNode,e.isBlock)||a;if(o&&Po(r)&&t&&e.isEmpty(d))return M.some(Ya(r.parentNode,e.nodeIndex(r)));const c=new Qo(r,d);for(;i=c[o?"prev":"next"]();){if("false"===e.getContentEditableParent(i)||Nm(i,a))return M.none();if(Ro(i)&&i.nodeValue.length>0)return!1===Sm(i,a,"A")?M.some(Ya(i,o?i.nodeValue.length:0)):M.none();if(e.isBlock(i)||l[i.nodeName.toLowerCase()])return M.none();s=i}return To(s)?M.none():n&&s?M.some(Ya(s,0)):M.none()},Om=(e,t,n,o)=>{let r,s;const a=e.getRoot();let i,l,d=!1;r=o[(n?"start":"end")+"Container"],s=o[(n?"start":"end")+"Offset"];const c=Co(r)&&s===r.childNodes.length,u=e.schema.getNonEmptyElements();if(l=n,xr(r))return M.none();if(Co(r)&&s>r.childNodes.length-1&&(l=!1),Bo(r)&&(r=a,s=0),r===a){if(l&&(i=r.childNodes[s>0?s-1:0],i)){if(xr(i))return M.none();if(u[i.nodeName]||_m(i))return M.none()}if(r.hasChildNodes()){if(s=Math.min(!l&&s>0?s-1:s,r.childNodes.length-1),r=r.childNodes[s],s=Ro(r)&&c?r.data.length:0,!t&&r===a.lastChild&&_m(r))return M.none();if(((e,t)=>{for(;t&&t!==e;){if(Io(t))return!0;t=t.parentNode}return!1})(a,r)||xr(r))return M.none();if(r.hasChildNodes()&&!1===_m(r)){i=r;const t=new Qo(r,a);do{if(Io(i)||xr(i)){d=!1;break}if(Ro(i)&&i.nodeValue.length>0){s=l?0:i.nodeValue.length,r=i,d=!0;break}if(u[i.nodeName.toLowerCase()]&&!Em(i)){s=e.nodeIndex(i),r=i.parentNode,l||s++,d=!0;break}}while(i=l?t.next():t.prev())}}}return t&&(Ro(r)&&0===s&&Am(e,c,t,!0,r).each((e=>{r=e.container(),s=e.offset(),d=!0})),Co(r)&&(i=r.childNodes[s],i||(i=r.childNodes[s-1]),!i||!Po(i)||((e,t)=>e.previousSibling&&"A"===e.previousSibling.nodeName)(i)||Rm(e,i,!1)||Rm(e,i,!0)||Am(e,c,t,!0,i).each((e=>{r=e.container(),s=e.offset(),d=!0})))),l&&!t&&Ro(r)&&s===r.nodeValue.length&&Am(e,c,t,!1,r).each((e=>{r=e.container(),s=e.offset(),d=!0})),d?M.some(Ya(r,s)):M.none()},Tm=(e,t)=>{const n=t.collapsed,o=t.cloneRange(),r=Ya.fromRangeStart(t);return Om(e,n,!0,o).each((e=>{n&&Ya.isAbove(r,e)||o.setStart(e.container(),e.offset())})),n||Om(e,n,!1,o).each((e=>{o.setEnd(e.container(),e.offset())})),n&&o.collapse(!0),xm(t,o)?M.none():M.some(o)},Bm=(e,t)=>e.splitText(t),Dm=e=>{let t=e.startContainer,n=e.startOffset,o=e.endContainer,r=e.endOffset;return t===o&&Ro(t)?n>0&&n<t.nodeValue.length&&(o=Bm(t,n),t=o.previousSibling,r>n?(r-=n,t=o=Bm(o,r).previousSibling,r=o.nodeValue.length,n=0):r=0):(Ro(t)&&n>0&&n<t.nodeValue.length&&(t=Bm(t,n),n=0),Ro(o)&&r>0&&r<o.nodeValue.length&&(o=Bm(o,r).previousSibling,r=o.nodeValue.length)),{startContainer:t,startOffset:n,endContainer:o,endOffset:r}},Pm=e=>({walk:(t,n)=>Eu(e,t,n),split:Dm,normalize:t=>Tm(e,t).fold(P,(e=>(t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0)))});Pm.compareRanges=xm,Pm.getCaretRangeFromPoint=wm,Pm.getSelectedNode=Ra,Pm.getNode=Aa;const Lm=((e,t)=>{const n=t=>{const n=(e=>{const t=e.dom;return jn(e)?t.getBoundingClientRect().height:t.offsetHeight})(t);if(n<=0||null===n){const n=qn(t,e);return parseFloat(n)||0}return n},o=(e,t)=>Y(t,((t,n)=>{const o=qn(e,n),r=void 0===o?0:parseInt(o,10);return isNaN(r)?t:t+r}),0);return{set:(t,n)=>{if(!x(n)&&!n.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+n);const o=t.dom;ln(o)&&(o.style[e]=n+"px")},get:n,getOuter:n,aggregate:o,max:(e,t,n)=>{const r=o(e,n);return t>r?t-r:0}}})("height"),Mm=()=>fn(document),Im=(e,t)=>e.view(t).fold(N([]),(t=>{const n=e.owner(t),o=Im(e,n);return[t].concat(o)}));var Fm=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?M.none():M.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(fn)},owner:e=>wn(e)});const Um=e=>"textarea"===Mt(e),zm=(e,t)=>{const n=(e=>{const t=e.dom.ownerDocument,n=t.body,o=t.defaultView,r=t.documentElement;if(n===e.dom)return co(n.offsetLeft,n.offsetTop);const s=uo(null==o?void 0:o.pageYOffset,r.scrollTop),a=uo(null==o?void 0:o.pageXOffset,r.scrollLeft),i=uo(r.clientTop,n.clientTop),l=uo(r.clientLeft,n.clientLeft);return mo(e).translate(a-l,s-i)})(e),o=(e=>Lm.get(e))(e);return{element:e,bottom:n.top+o,height:o,pos:n,cleanup:t}},jm=(e,t,n,o)=>{qm(e,((r,s)=>Hm(e,t,n,o)),n)},Vm=(e,t,n,o,r)=>{const s={elm:o.element.dom,alignToTop:r};((e,t)=>e.dispatch("ScrollIntoView",t).isDefaultPrevented())(e,s)||(n(t,fo(t).top,o,r),((e,t)=>{e.dispatch("AfterScrollIntoView",t)})(e,s))},Hm=(e,t,n,o)=>{const r=fn(e.getBody()),s=fn(e.getDoc());r.dom.offsetWidth;const a=((e,t)=>{const n=((e,t)=>{const n=Rn(e);if(0===n.length||Um(e))return{element:e,offset:t};if(t<n.length&&!Um(n[t]))return{element:n[t],offset:0};{const o=n[n.length-1];return Um(o)?{element:e,offset:t}:"img"===Mt(o)?{element:o,offset:1}:zt(o)?{element:o,offset:Uu(o).length}:{element:o,offset:Rn(o).length}}})(e,t),o=cn('<span data-mce-bogus="all" style="display: inline-block;">\ufeff</span>');return Xn(n.element,o),zm(o,(()=>no(o)))})(fn(n.startContainer),n.startOffset);Vm(e,s,t,a,o),a.cleanup()},$m=(e,t,n,o)=>{const r=fn(e.getDoc());Vm(e,r,n,(e=>zm(fn(e),S))(t),o)},qm=(e,t,n)=>{const o=n.startContainer,r=n.startOffset,s=n.endContainer,a=n.endOffset;t(fn(o),fn(s));const i=e.dom.createRng();i.setStart(o,r),i.setEnd(s,a),e.selection.setRng(n)},Wm=(e,t,n,o)=>{const r=e.pos;if(n)go(r.left,r.top,o);else{const n=r.top-t+e.height;go(r.left,n,o)}},Km=(e,t,n,o,r)=>{const s=n+t,a=o.pos.top,i=o.bottom,l=i-a>=n;a<t?Wm(o,n,!1!==r,e):a>s?Wm(o,n,l?!1!==r:!0===r,e):i>s&&!l&&Wm(o,n,!0===r,e)},Gm=(e,t,n,o)=>{const r=e.dom.defaultView.innerHeight;Km(e,t,r,n,o)},Ym=(e,t,n,o)=>{const r=e.dom.defaultView.innerHeight;Km(e,t,r,n,o);const s=(e=>{const t=Mm(),n=fo(t),o=((e,t)=>{const n=t.owner(e);return Im(t,n)})(e,Fm),r=mo(e),s=G(o,((e,t)=>{const n=mo(t);return{left:e.left+n.left,top:e.top+n.top}}),{left:0,top:0});return co(s.left+r.left+n.left,s.top+r.top+n.top)})(n.element),a=bo(window);s.top<a.y?po(n.element,!1!==o):s.top>a.bottom&&po(n.element,!0===o)},Xm=(e,t,n)=>jm(e,Gm,t,n),Qm=(e,t,n)=>$m(e,t,Gm,n),Jm=(e,t,n)=>jm(e,Ym,t,n),Zm=(e,t,n)=>$m(e,t,Ym,n),ef=(e,t,n)=>{(e.inline?Xm:Jm)(e,t,n)},tf=e=>e.dom.focus(),nf=e=>{const t=Mn(e).dom;return e.dom===t.activeElement},of=(e=Mm())=>M.from(e.dom.activeElement).map(fn),rf=(e,t)=>{const n=zt(t)?Uu(t).length:Rn(t).length+1;return e>n?n:e<0?0:e},sf=e=>pm.range(e.start,rf(e.soffset,e.start),e.finish,rf(e.foffset,e.finish)),af=(e,t)=>!yo(t.dom)&&(yn(e,t)||vn(e,t)),lf=e=>t=>af(e,t.start)&&af(e,t.finish),df=e=>pm.range(fn(e.startContainer),e.startOffset,fn(e.endContainer),e.endOffset),cf=e=>{const t=document.createRange();try{return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),M.some(t)}catch(e){return M.none()}},uf=e=>{const t=(e=>e.inline)(e)?(n=fn(e.getBody()),(e=>{const t=e.getSelection();return(t&&0!==t.rangeCount?M.from(t.getRangeAt(0)):M.none()).map(df)})(xn(n).dom).filter(lf(n))):M.none();var n;e.bookmark=t.isSome()?t:e.bookmark},mf=e=>(e.bookmark?e.bookmark:M.none()).bind((t=>{return n=fn(e.getBody()),o=t,M.from(o).filter(lf(n)).map(sf);var n,o})).bind(cf),ff={isEditorUIElement:e=>{const t=e.className.toString();return-1!==t.indexOf("tox-")||-1!==t.indexOf("mce-")}},gf={setEditorTimeout:(e,t,n)=>((e,t)=>(x(t)||(t=0),setTimeout(e,t)))((()=>{e.removed||t()}),n),setEditorInterval:(e,t,n)=>{const o=((e,t)=>(x(t)||(t=0),setInterval(e,t)))((()=>{e.removed?clearInterval(o):t()}),n);return o}};let pf;const hf=Hs.DOM,bf=(e,t)=>{const n=Nl(e),o=hf.getParent(t,(t=>(e=>ff.isEditorUIElement(e))(t)||!!n&&e.dom.is(t,n)));return null!==o},vf=(e,t)=>{const n=t.editor;(e=>{const t=Zs((()=>{uf(e)}),0);e.on("init",(()=>{e.inline&&((e,t)=>{const n=()=>{t.throttle()};Hs.DOM.bind(document,"mouseup",n),e.on("remove",(()=>{Hs.DOM.unbind(document,"mouseup",n)}))})(e,t),((e,t)=>{((e,t)=>{e.on("mouseup touchend",(e=>{t.throttle()}))})(e,t),e.on("keyup NodeChange AfterSetSelectionRange",(t=>{(e=>"nodechange"===e.type&&e.selectionChange)(t)||uf(e)}))})(e,t)})),e.on("remove",(()=>{t.cancel()}))})(n),n.on("focusin",(()=>{const t=e.focusedEditor;t!==n&&(t&&t.dispatch("blur",{focusedEditor:n}),e.setActive(n),e.focusedEditor=n,n.dispatch("focus",{blurredEditor:t}),n.focus(!0))})),n.on("focusout",(()=>{gf.setEditorTimeout(n,(()=>{const t=e.focusedEditor;bf(n,(e=>{try{const t=Mn(fn(e.getElement()));return of(t).fold((()=>document.body),(e=>e.dom))}catch(e){return document.body}})(n))||t!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null)}))})),pf||(pf=t=>{const n=e.activeEditor;n&&Un(t).each((t=>{t.ownerDocument===document&&(t===document.body||bf(n,t)||e.focusedEditor!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null))}))},hf.bind(document,"focusin",pf))},yf=(e,t)=>{e.focusedEditor===t.editor&&(e.focusedEditor=null),e.activeEditor||(hf.unbind(document,"focusin",pf),pf=null)},Cf=(e,t)=>{((e,t)=>(e=>e.collapsed?M.from(Aa(e.startContainer,e.startOffset)).map(fn):M.none())(t).bind((t=>ar(t)?M.some(t):!1===yn(e,t)?M.some(e):M.none())))(fn(e.getBody()),t).bind((e=>xc(e.dom))).fold((()=>{e.selection.normalize()}),(t=>e.selection.setRng(t.toRange())))},wf=e=>{if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},xf=e=>e.inline?(e=>{const t=e.getBody();return t&&(n=fn(t),nf(n)||(o=n,of(Mn(o)).filter((e=>o.dom.contains(e.dom)))).isSome());var n,o})(e):(e=>e.iframeElement&&nf(fn(e.iframeElement)))(e),kf=e=>e.editorManager.setActive(e),Sf=(e,t,n,o,r)=>{const s=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return M.from(s).map(fn).map((e=>o&&t.collapsed?e:An(e,r(e,a)).getOr(e))).bind((e=>Ut(e)?M.some(e):kn(e).filter(Ut))).map((e=>e.dom)).getOr(e)},_f=(e,t,n)=>Sf(e,t,!0,n,((e,t)=>Math.min(Bn(e),t))),Ef=(e,t,n)=>Sf(e,t,!1,n,((e,t)=>t>0?t-1:t)),Nf=(e,t)=>{const n=e;for(;e&&Ro(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},Rf=(e,t)=>H(t,(t=>{const n=e.dispatch("GetSelectionRange",{range:t});return n.range!==t?n.range:t})),Af=["img","br"],Of=e=>{const t=zu(e).filter((e=>0!==e.trim().length||e.indexOf(fr)>-1)).isSome();return t||j(Af,Mt(e))},Tf="[data-mce-autocompleter]",Bf=(e,t)=>{if(Df(fn(e.getBody())).isNone()){const o=cn('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());Zn(o,fn(t.extractContents())),t.insertNode(o.dom),kn(o).each((e=>e.dom.normalize())),(n=o,((e,t)=>{const n=e=>{const o=Rn(e);for(let e=o.length-1;e>=0;e--){const r=o[e];if(t(r))return M.some(r);const s=n(r);if(s.isSome())return s}return M.none()};return n(e)})(n,Of)).map((t=>{e.selection.setCursorLocation(t.dom,(e=>"img"===Mt(e)?1:zu(e).fold((()=>Rn(e).length),(e=>e.length)))(t))}))}var n},Df=e=>Ko(e,Tf),Pf={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Lf=(e,t,n)=>{const o=n?"lastChild":"firstChild",r=n?"prev":"next";if(e[o])return e[o];if(e!==t){let n=e[r];if(n)return n;for(let o=e.parent;o&&o!==t;o=o.parent)if(n=o[r],n)return n}},Mf=e=>{if(!Vr(e.value))return!1;const t=e.parent;return!t||"span"===t.name&&!t.attr("style")||!/^[ ]+$/.test(e.value)},If=e=>{const t="a"===e.name&&!e.attr("href")&&e.attr("id");return e.attr("name")||e.attr("id")&&!e.firstChild||e.attr("data-mce-bookmark")||t};class Ff{constructor(e,t){this.name=e,this.type=t,1===t&&(this.attributes=[],this.attributes.map={})}static create(e,t){const n=new Ff(e,Pf[e]||1);return t&&fe(t,((e,t)=>{n.attr(t,e)})),n}replace(e){const t=this;return e.parent&&e.remove(),t.insert(e,t),t.remove(),t}attr(e,t){const n=this;let o;if("string"!=typeof e)return null!=e&&fe(e,((e,t)=>{n.attr(t,e)})),n;if(o=n.attributes){if(void 0!==t){if(null===t){if(e in o.map){delete o.map[e];let t=o.length;for(;t--;)if(o[t].name===e)return o.splice(t,1),n}return n}if(e in o.map){let n=o.length;for(;n--;)if(o[n].name===e){o[n].value=t;break}}else o.push({name:e,value:t});return o.map[e]=t,n}return o.map[e]}}clone(){const e=this,t=new Ff(e.name,e.type);let n;if(n=e.attributes){const e=[];e.map={};for(let t=0,o=n.length;t<o;t++){const o=n[t];"id"!==o.name&&(e[e.length]={name:o.name,value:o.value},e.map[o.name]=o.value)}t.attributes=e}return t.value=e.value,t}wrap(e){const t=this;return t.parent.insert(e,t),e.append(t),t}unwrap(){const e=this;for(let t=e.firstChild;t;){const n=t.next;e.insert(t,e,!0),t=n}e.remove()}remove(){const e=this,t=e.parent,n=e.next,o=e.prev;return t&&(t.firstChild===e?(t.firstChild=n,n&&(n.prev=null)):o.next=n,t.lastChild===e?(t.lastChild=o,o&&(o.next=null)):n.prev=o,e.parent=e.next=e.prev=null),e}append(e){const t=this;e.parent&&e.remove();const n=t.lastChild;return n?(n.next=e,e.prev=n,t.lastChild=e):t.lastChild=t.firstChild=e,e.parent=t,e}insert(e,t,n){e.parent&&e.remove();const o=t.parent||this;return n?(t===o.firstChild?o.firstChild=e:t.prev.next=e,e.prev=t.prev,e.next=t,t.prev=e):(t===o.lastChild?o.lastChild=e:t.next.prev=e,e.next=t.next,e.prev=t,t.next=e),e.parent=o,e}getAll(e){const t=this,n=[];for(let o=t.firstChild;o;o=Lf(o,t))o.name===e&&n.push(o);return n}children(){const e=[];for(let t=this.firstChild;t;t=t.next)e.push(t);return e}empty(){const e=this;if(e.firstChild){const t=[];for(let n=e.firstChild;n;n=Lf(n,e))t.push(n);let n=t.length;for(;n--;){const e=t[n];e.parent=e.firstChild=e.lastChild=e.next=e.prev=null}}return e.firstChild=e.lastChild=null,e}isEmpty(e,t={},n){const o=this;let r=o.firstChild;if(If(o))return!1;if(r)do{if(1===r.type){if(r.attr("data-mce-bogus"))continue;if(e[r.name])return!1;if(If(r))return!1}if(8===r.type)return!1;if(3===r.type&&!Mf(r))return!1;if(3===r.type&&r.parent&&t[r.parent.name]&&Vr(r.value))return!1;if(n&&n(r))return!1}while(r=Lf(r,o));return!0}walk(e){return Lf(this,null,e)}}const Uf=(e,t,n=0)=>{const o=e.toLowerCase();if(-1!==o.indexOf("[if ",n)&&((e,t)=>/^\s*\[if [\w\W]+\]>.*<!\[endif\](--!?)?>/.test(e.substr(t)))(o,n)){const e=o.indexOf("[endif]",n);return o.indexOf(">",e)}if(t){const e=o.indexOf(">",n);return-1!==e?e:o.length}{const t=/--!?>/g;t.lastIndex=n;const r=t.exec(e);return r?r.index+r[0].length:o.length}},zf=(e,t,n)=>{const o=/<([!?\/])?([A-Za-z0-9\-_:.]+)/g,r=/(?:\s(?:[^'">]+(?:"[^"]*"|'[^']*'))*[^"'>]*(?:"[^">]*|'[^'>]*)?|\s*|\/)>/g,s=e.getVoidElements();let a=1,i=n;for(;0!==a;)for(o.lastIndex=i;;){const e=o.exec(t);if(null===e)return i;if("!"===e[1]){i=ze(e[2],"--")?Uf(t,!1,e.index+"!--".length):Uf(t,!0,e.index+1);break}{r.lastIndex=o.lastIndex;const n=r.exec(t);if(h(n)||n.index!==o.lastIndex)continue;"/"===e[1]?a-=1:xe(s,e[2])||(a+=1),i=o.lastIndex+n[0].length;break}}return i},jf=(e,t)=>{const n=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,o=e.schema;let r=((e,t)=>{const n=new RegExp(["\\s?("+e.join("|")+')="[^"]+"'].join("|"),"gi");return t.replace(n,"")})(e.getTempAttrs(),t);const s=o.getVoidElements();let a;for(;a=n.exec(r);){const e=n.lastIndex,t=a[0].length;let i;i=s[a[1]]?e:zf(o,r,e),r=r.substring(0,e-t)+r.substring(i),n.lastIndex=e-t}return br(r)},Vf=jf,Hf=Bt.each,$f=e=>({compare:(t,n)=>{if(t.nodeName!==n.nodeName)return!1;const o=t=>{const n={};return Hf(e.getAttribs(t),(o=>{const r=o.nodeName.toLowerCase();0!==r.indexOf("_")&&"style"!==r&&0!==r.indexOf("data-")&&(n[r]=e.getAttrib(t,r))})),n},r=(e,t)=>{let n,o;for(o in e)if(xe(e,o)){if(n=t[o],void 0===n)return!1;if(e[o]!==n)return!1;delete t[o]}for(o in t)if(xe(t,o))return!1;return!0};return!(!r(o(t),o(n))||!r(e.parseStyle(e.getAttrib(t,"style")),e.parseStyle(e.getAttrib(n,"style")))||Fc(t)||Fc(n))}}),qf=Bt.makeMap,Wf=e=>{const t=[],n=(e=e||{}).indent,o=qf(e.indent_before||""),r=qf(e.indent_after||""),s=ms.getEncodeFunc(e.entity_encoding||"raw",e.entities),a="xhtml"!==e.element_format;return{start:(e,i,l)=>{let d,c,u,m;if(n&&o[e]&&t.length>0&&(m=t[t.length-1],m.length>0&&"\n"!==m&&t.push("\n")),t.push("<",e),i)for(d=0,c=i.length;d<c;d++)u=i[d],t.push(" ",u.name,'="',s(u.value,!0),'"');t[t.length]=!l||a?">":" />",l&&n&&r[e]&&t.length>0&&(m=t[t.length-1],m.length>0&&"\n"!==m&&t.push("\n"))},end:e=>{let o;t.push("</",e,">"),n&&r[e]&&t.length>0&&(o=t[t.length-1],o.length>0&&"\n"!==o&&t.push("\n"))},text:(e,n)=>{e.length>0&&(t[t.length]=n?e:s(e))},cdata:e=>{t.push("<![CDATA[",e,"]]>")},comment:e=>{t.push("\x3c!--",e,"--\x3e")},pi:(e,o)=>{o?t.push("<?",e," ",s(o),"?>"):t.push("<?",e,"?>"),n&&t.push("\n")},doctype:e=>{t.push("<!DOCTYPE",e,">",n?"\n":"")},reset:()=>{t.length=0},getContent:()=>t.join("").replace(/\n$/,"")}},Kf=(e,t=Ss())=>{const n=Wf(e);return(e=e||{}).validate=!("validate"in e)||e.validate,{serialize:o=>{const r=e.validate,s={3:e=>{n.text(e.value,e.raw)},8:e=>{n.comment(e.value)},7:e=>{n.pi(e.name,e.value)},10:e=>{n.doctype(e.value)},4:e=>{n.cdata(e.value)},11:e=>{if(e=e.firstChild)do{a(e)}while(e=e.next)}};n.reset();const a=e=>{const o=s[e.type];if(o)o(e);else{const o=e.name,s=o in t.getVoidElements();let i=e.attributes;if(r&&i&&i.length>1){const n=[];n.map={};const o=t.getElementRule(e.name);if(o){for(let e=0,t=o.attributesOrder.length;e<t;e++){const t=o.attributesOrder[e];if(t in i.map){const e=i.map[t];n.map[t]=e,n.push({name:t,value:e})}}for(let e=0,t=i.length;e<t;e++){const t=i[e].name;if(!(t in n.map)){const e=i.map[t];n.map[t]=e,n.push({name:t,value:e})}}i=n}}if(n.start(o,i,s),!s){let t=e.firstChild;if(t){"pre"!==o&&"textarea"!==o||3!==t.type||"\n"!==t.value[0]||n.text("\n",!0);do{a(t)}while(t=t.next)}n.end(o)}}};return 1!==o.type||e.inner?3===o.type?s[3](o):s[11](o):a(o),n.getContent()}}},Gf=new Set;$(["margin","margin-left","margin-right","margin-top","margin-bottom","padding","padding-left","padding-right","padding-top","padding-bottom","border","border-width","border-style","border-color","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","float","position","left","right","top","bottom","z-index","display","transform","width","max-width","min-width","height","max-height","min-height","overflow","overflow-x","overflow-y","text-overflow","vertical-align","transition","transition-delay","transition-duration","transition-property","transition-timing-function"],(e=>{Gf.add(e)}));const Yf=["font","text-decoration","text-emphasis"],Xf=(e,t)=>ue(e.parseStyle(e.getAttrib(t,"style"))),Qf=(e,t,n)=>{const o=Xf(e,t),r=Xf(e,n),s=o=>{var r,s;const a=null!==(r=e.getStyle(t,o))&&void 0!==r?r:"",i=null!==(s=e.getStyle(n,o))&&void 0!==s?s:"";return We(a)&&We(i)&&a!==i};return V(o,(e=>{const t=t=>V(t,(t=>t===e));if(!t(r)&&t(Yf)){const e=K(r,(e=>V(Yf,(t=>ze(e,t)))));return V(e,s)}return s(e)}))},Jf=(e,t,n)=>M.from(n.container()).filter(Ro).exists((o=>{const r=e?0:-1;return t(o.data.charAt(n.offset()+r))})),Zf=O(Jf,!0,zc),eg=O(Jf,!1,zc),tg=e=>{const t=e.container();return Ro(t)&&(0===t.data.length||hr(t.data)&&Wu.isBookmarkNode(t.parentNode))},ng=(e,t)=>n=>M.from(Hd(e?0:-1,n)).filter(t).isSome(),og=e=>Lo(e)&&"block"===qn(fn(e),"display"),rg=e=>Io(e)&&!(e=>Co(e)&&"all"===e.getAttribute("data-mce-bogus"))(e),sg=ng(!0,og),ag=ng(!1,og),ig=ng(!0,Uo),lg=ng(!1,Uo),dg=ng(!0,_o),cg=ng(!1,_o),ug=ng(!0,rg),mg=ng(!1,rg),fg=e=>{to(e),Zn(e,cn('<br data-mce-bogus="1">'))},gg=e=>{Tn(e).each((t=>{Sn(t).each((n=>{er(e)&&nr(t)&&er(n)&&no(t)}))}))},pg=(e,t)=>((e,t,n)=>{return yn(t,e)?(o=((e,t)=>{const n=w(t)?t:P;let o=e.dom;const r=[];for(;null!==o.parentNode&&void 0!==o.parentNode;){const e=o.parentNode,t=fn(e);if(r.push(t),!0===n(t))break;o=e}return r})(e,(e=>n(e)||vn(e,t))),o.slice(0,-1)):[];var o})(e,t,P),hg=(e,t)=>[e].concat(pg(e,t)),bg=(e,t,n)=>vc(e,t,n,tg),vg=(e,t)=>Q(hg(fn(t.container()),e),er),yg=(e,t,n)=>bg(e,t.dom,n).forall((e=>vg(t,n).fold((()=>!1===Vd(e,n,t.dom)),(o=>!1===Vd(e,n,t.dom)&&yn(o,fn(e.container())))))),Cg=(e,t,n)=>vg(t,n).fold((()=>bg(e,t.dom,n).forall((e=>!1===Vd(e,n,t.dom)))),(t=>bg(e,t.dom,n).isNone())),wg=O(Cg,!1),xg=O(Cg,!0),kg=O(yg,!1),Sg=O(yg,!0),_g=e=>Jd(e).exists(nr),Eg=(e,t,n)=>{const o=K(hg(fn(n.container()),t),er),r=ie(o).getOr(t);return hc(e,r.dom,n).filter(_g)},Ng=(e,t)=>Jd(t).exists(nr)||Eg(!0,e,t).isSome(),Rg=(e,t)=>(e=>M.from(e.getNode(!0)).map(fn))(t).exists(nr)||Eg(!1,e,t).isSome(),Ag=O(Eg,!1),Og=O(Eg,!0),Tg=e=>Ya.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd(),Bg=(e,t)=>{const n=K(hg(fn(t.container()),e),er);return ie(n).getOr(e)},Dg=(e,t)=>Tg(t)?eg(t):eg(t)||wc(Bg(e,t).dom,t).exists(eg),Pg=(e,t)=>Tg(t)?Zf(t):Zf(t)||Cc(Bg(e,t).dom,t).exists(Zf),Lg=e=>Jd(e).bind((e=>qo(e,Ut))).exists((e=>(e=>j(["pre","pre-wrap"],e))(qn(e,"white-space")))),Mg=(e,t)=>!Lg(t)&&(wg(e,t)||kg(e,t)||Rg(e,t)||Dg(e,t)),Ig=(e,t)=>!Lg(t)&&(xg(e,t)||Sg(e,t)||Ng(e,t)||Pg(e,t)||((e,t)=>{const n=Cc(e.dom,t).getOr(t),o=(e=>t=>{return n=new Qo(t,e).next(),C(n)&&Io(n)&&Bd(n);var n})(e.dom);return t.isAtEnd()&&(o(t.container())||o(n.container()))})(e,t)),Fg=(e,t)=>Mg(e,t)||Ig(e,(e=>{const t=e.container(),n=e.offset();return Ro(t)&&n<t.data.length?Ya(t,n+1):e})(t)),Ug=(e,t)=>Uc(e.charAt(t)),zg=e=>{const t=e.container();return Ro(t)&&Ue(t.data,fr)},jg=(e,t)=>M.some(t).filter(zg).bind((t=>{const n=t.container(),o=((e,t)=>{const n=t.data,o=Ya(t,0);return!(!Ug(n,0)||Fg(e,o)||(t.data=" "+n.slice(1),0))})(e,n)||(e=>{const t=e.data,n=(e=>{const t=e.split("");return H(t,((e,n)=>Uc(e)&&n>0&&n<t.length-1&&jc(t[n-1])&&jc(t[n+1])?" ":e)).join("")})(t);return n!==t&&(e.data=n,!0)})(n)||((e,t)=>{const n=t.data,o=Ya(t,n.length-1);return!(!Ug(n,n.length-1)||Fg(e,o)||(t.data=n.slice(0,-1)+" ",0))})(e,n);return o?M.some(t):M.none()})),Vg=(e,t,n)=>{if(0===n)return;const o=fn(e),r=$o(o,er).getOr(o),s=e.data.slice(t,t+n),a=t+n>=e.data.length&&Ig(r,Ya(e,e.data.length)),i=0===t&&Mg(r,Ya(e,0));e.replaceData(t,n,$r(s,4,i,a))},Hg=(e,t)=>{const n=e.data.slice(t),o=n.length-$e(n).length;Vg(e,t,o)},$g=(e,t)=>{const n=e.data.slice(0,t),o=n.length-qe(n).length;Vg(e,t-o,o)},qg=(e,t,n,o=!0)=>{const r=qe(e.data).length,s=o?e:t,a=o?t:e;return o?s.appendData(a.data):s.insertData(0,a.data),no(fn(a)),n&&Hg(s,r),s},Wg=(e,t)=>((e,t)=>{const n=e.container(),o=e.offset();return!1===Ya.isTextPosition(e)&&n===t.parentNode&&o>Ya.before(t).offset()})(t,e)?Ya(t.container(),t.offset()-1):t,Kg=e=>{return Fr(e.previousSibling)?M.some((t=e.previousSibling,Ro(t)?Ya(t,t.data.length):Ya.after(t))):e.previousSibling?kc(e.previousSibling):M.none();var t},Gg=e=>{return Fr(e.nextSibling)?M.some((t=e.nextSibling,Ro(t)?Ya(t,0):Ya.before(t))):e.nextSibling?xc(e.nextSibling):M.none();var t},Yg=(e,t,n)=>((e,t,n)=>e?((e,t)=>Gg(t).orThunk((()=>Kg(t))).orThunk((()=>((e,t)=>Cc(e,Ya.after(t)).fold((()=>wc(e,Ya.before(t))),M.some))(e,t))))(t,n):((e,t)=>Kg(t).orThunk((()=>Gg(t))).orThunk((()=>((e,t)=>{const n=Ya.before(t.previousSibling?t.previousSibling:t.parentNode);return wc(e,n).fold((()=>Cc(e,Ya.after(t))),M.some)})(e,t))))(t,n))(e,t,n).map(O(Wg,n)),Xg=(e,t,n)=>{n.fold((()=>{e.focus()}),(n=>{e.selection.setRng(n.toRange(),t)}))},Qg=(e,t)=>t&&xe(e.schema.getBlockElements(),Mt(t)),Jg=e=>{if(Yr(e)){const t=cn('<br data-mce-bogus="1">');return to(e),Zn(e,t),M.some(Ya.before(t.dom))}return M.none()},Zg=(e,t,n,o=!0)=>{const r=Yg(t,e.getBody(),n.dom),s=$o(n,O(Qg,e),(a=e.getBody(),e=>e.dom===a));var a;const i=((e,t,n)=>{const o=Sn(e).filter(zt),r=_n(e).filter(zt);return no(e),(s=o,a=r,i=t,l=(e,t,o)=>{const r=e.dom,s=t.dom,a=r.data.length;return qg(r,s,n),o.container()===s?Ya(r,a):o},s.isSome()&&a.isSome()&&i.isSome()?M.some(l(s.getOrDie(),a.getOrDie(),i.getOrDie())):M.none()).orThunk((()=>(n&&(o.each((e=>$g(e.dom,e.dom.length))),r.each((e=>Hg(e.dom,0)))),t)));var s,a,i,l})(n,r,((e,t)=>xe(e.schema.getTextInlineElements(),Mt(t)))(e,n));e.dom.isEmpty(e.getBody())?(e.setContent(""),e.selection.setCursorLocation()):s.bind(Jg).fold((()=>{o&&Xg(e,t,i)}),(n=>{o&&Xg(e,t,M.some(n))}))},ep=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,tp=(e,t)=>hn(fn(t),fl(e)),np=(e,t,n)=>{const o=((e,t,n)=>K(Hs.DOM.getParents(n.container(),"*",t),e))(e,t,n);return M.from(o[o.length-1])},op=(e,t)=>{if(!t)return t;const n=t.container(),o=t.offset();return e?wr(n)?Ro(n.nextSibling)?Ya(n.nextSibling,0):Ya.after(n):Sr(t)?Ya(n,o+1):t:wr(n)?Ro(n.previousSibling)?Ya(n.previousSibling,n.previousSibling.data.length):Ya.before(n):_r(t)?Ya(n,o-1):t},rp=O(op,!0),sp=O(op,!1),ap=(e,t)=>{const n=e=>e.stopImmediatePropagation();e.on("beforeinput input",n,!0),e.getDoc().execCommand(t),e.off("beforeinput input",n)},ip=e=>ap(e,"Delete"),lp=e=>or(e)||sr(e),dp=(e,t)=>yn(e,t)?qo(t,lp,(e=>t=>vn(e,fn(t.dom.parentNode)))(e)):M.none(),cp=e=>{e.dom.isEmpty(e.getBody())&&(e.setContent(""),(e=>{const t=e.getBody(),n=t.firstChild&&e.dom.isBlock(t.firstChild)?t.firstChild:t;e.selection.setCursorLocation(n,0)})(e))},up=e=>{var t;return(8===It(t=e)||"#comment"===Mt(t)?Sn(e):Tn(e)).bind(up).orThunk((()=>M.some(e)))},mp=(e,t,n,o=!0)=>{t.deleteContents();const r=up(n).getOr(n),s=fn(e.dom.getParent(r.dom,e.dom.isBlock));if(Yr(s)&&(fg(s),o&&e.selection.setCursorLocation(s.dom,0)),!vn(n,s)){const e=Dt(kn(s),n)?[]:kn(a=s).map(Rn).map((e=>K(e,(e=>!vn(a,e))))).getOr([]);$(e.concat(Rn(n)),(e=>{vn(e,s)||yn(e,s)||!Yr(e)||no(e)}))}var a},fp=e=>na(e,"td,th"),gp=(e,t)=>({start:e,end:t}),pp=Ci([{singleCellTable:["rng","cell"]},{fullTable:["table"]},{partialTable:["cells","outsideDetails"]},{multiTable:["startTableCells","endTableCells","betweenRng"]}]),hp=(e,t)=>Go(fn(e),"td,th",t),bp=e=>!vn(e.start,e.end),vp=(e,t)=>Ou(e.start,t).bind((n=>Ou(e.end,t).bind((e=>Lt(vn(n,e),n))))),yp=e=>t=>vp(t,e).map((e=>((e,t,n)=>({rng:e,table:t,cells:n}))(t,e,fp(e)))),Cp=(e,t,n,o)=>{if(n.collapsed||!e.forall(bp))return M.none();if(t.isSameTable){const t=e.bind(yp(o));return M.some({start:t,end:t})}{const e=hp(n.startContainer,o),t=hp(n.endContainer,o),r=e.bind((e=>t=>Ou(t,e).bind((e=>le(fp(e)).map((e=>gp(t,e))))))(o)).bind(yp(o)),s=t.bind((e=>t=>Ou(t,e).bind((e=>ie(fp(e)).map((e=>gp(e,t))))))(o)).bind(yp(o));return M.some({start:r,end:s})}},wp=(e,t)=>J(e,(e=>vn(e,t))),xp=e=>Pt(wp(e.cells,e.rng.start),wp(e.cells,e.rng.end),((t,n)=>e.cells.slice(t,n+1))),kp=(e,t)=>{const{startTable:n,endTable:o}=t,r=e.cloneRange();return n.each((e=>r.setStartAfter(e.dom))),o.each((e=>r.setEndBefore(e.dom))),r},Sp=(e,t)=>{const n=(e=>t=>vn(e,t))(e),o=((e,t)=>{const n=hp(e.startContainer,t),o=hp(e.endContainer,t);return Pt(n,o,gp)})(t,n),r=((e,t)=>{const n=e=>Ou(fn(e),t),o=n(e.startContainer),r=n(e.endContainer),s=o.isSome(),a=r.isSome(),i=Pt(o,r,vn).getOr(!1);return{startTable:o,endTable:r,isStartInTable:s,isEndInTable:a,isSameTable:i,isMultiTable:!i&&s&&a}})(t,n);return((e,t,n)=>e.exists((e=>((e,t)=>!bp(e)&&vp(e,t).exists((e=>{const t=e.dom.rows;return 1===t.length&&1===t[0].cells.length})))(e,n)&&Du(e.start,t))))(o,t,n)?o.map((e=>pp.singleCellTable(t,e.start))):r.isMultiTable?((e,t,n,o)=>Cp(e,t,n,o).bind((({start:e,end:o})=>{const r=e.bind(xp).getOr([]),s=o.bind(xp).getOr([]);if(r.length>0&&s.length>0){const e=kp(n,t);return M.some(pp.multiTable(r,s,e))}return M.none()})))(o,r,t,n):((e,t,n,o)=>Cp(e,t,n,o).bind((({start:e,end:t})=>e.or(t))).bind((e=>{const{isSameTable:o}=t,r=xp(e).getOr([]);if(o&&e.cells.length===r.length)return M.some(pp.fullTable(e.table));if(r.length>0){if(o)return M.some(pp.partialTable(r,M.none()));{const e=kp(n,t);return M.some(pp.partialTable(r,M.some({...t,rng:e})))}}return M.none()})))(o,r,t,n)},_p=e=>$(e,(e=>{Xt(e,"contenteditable"),fg(e)})),Ep=(e,t,n,o)=>{const r=n.cloneRange();o?(r.setStart(n.startContainer,n.startOffset),r.setEndAfter(t.dom.lastChild)):(r.setStartBefore(t.dom.firstChild),r.setEnd(n.endContainer,n.endOffset)),Op(e,r,t,!1).each((e=>e()))},Np=e=>{const t=Au(e),n=fn(e.selection.getNode());Fo(n.dom)&&Yr(n)?e.selection.setCursorLocation(n.dom,0):e.selection.collapse(!0),t.length>1&&V(t,(e=>vn(e,n)))&&qt(n,"data-mce-selected","1")},Rp=(e,t,n)=>M.some((()=>{const o=e.selection.getRng(),r=n.bind((({rng:n,isStartInTable:r})=>{const s=((e,t)=>M.from(e.dom.getParent(t,e.dom.isBlock)).map(fn))(e,r?n.endContainer:n.startContainer);n.deleteContents(),((e,t,n)=>{n.each((n=>{t?no(n):(fg(n),e.selection.setCursorLocation(n.dom,0))}))})(e,r,s.filter(Yr));const a=r?t[0]:t[t.length-1];return Ep(e,a,o,r),Yr(a)?M.none():M.some(r?t.slice(1):t.slice(0,-1))})).getOr(t);_p(r),Np(e)})),Ap=(e,t,n,o)=>M.some((()=>{const r=e.selection.getRng(),s=t[0],a=n[n.length-1];Ep(e,s,r,!0),Ep(e,a,r,!1);const i=Yr(s)?t:t.slice(1),l=Yr(a)?n:n.slice(0,-1);_p(i.concat(l)),o.deleteContents(),Np(e)})),Op=(e,t,n,o=!0)=>M.some((()=>{mp(e,t,n,o)})),Tp=(e,t)=>M.some((()=>Zg(e,!1,t))),Bp=(e,t)=>Q(hg(t,e),ir),Dp=(e,t)=>Q(hg(t,e),Ht("caption")),Pp=(e,t)=>M.some((()=>{fg(t),e.selection.setCursorLocation(t.dom,0)})),Lp=(e,t)=>e?dg(t):cg(t),Mp=(e,t,n)=>{const o=fn(e.getBody());return Dp(o,n).fold((()=>((e,t,n,o)=>{const r=Ya.fromRangeStart(e.selection.getRng());return Bp(n,o).bind((o=>Yr(o)?Pp(e,o):((e,t,n,o,r)=>bc(n,e.getBody(),r).bind((e=>Bp(t,fn(e.getNode())).bind((e=>vn(e,o)?M.none():M.some(S))))))(e,n,t,o,r)))})(e,t,o,n).orThunk((()=>Lt(((e,t)=>{const n=Ya.fromRangeStart(e.selection.getRng());return Lp(t,n)||hc(t,e.getBody(),n).exists((e=>Lp(t,e)))})(e,t),S)))),(n=>((e,t,n,o)=>{const r=Ya.fromRangeStart(e.selection.getRng());return Yr(o)?Pp(e,o):((e,t,n,o,r)=>bc(n,e.getBody(),r).fold((()=>M.some(S)),(s=>((e,t,n,o)=>xc(e.dom).bind((r=>kc(e.dom).map((e=>t?n.isEqual(r)&&o.isEqual(e):n.isEqual(e)&&o.isEqual(r))))).getOr(!0))(o,n,r,s)?((e,t)=>Pp(e,t))(e,o):((e,t,n)=>Dp(e,fn(n.getNode())).fold((()=>M.some(S)),(e=>Lt(!vn(e,t),S))))(t,o,s))))(e,n,t,o,r)})(e,t,o,n)))},Ip=(e,t)=>{const n=fn(e.selection.getStart(!0)),o=Au(e);return e.selection.isCollapsed()&&0===o.length?Mp(e,t,n):((e,t,n)=>{const o=fn(e.getBody()),r=e.selection.getRng();return 0!==n.length?Rp(e,n,M.none()):((e,t,n,o)=>Dp(t,o).fold((()=>((e,t,n)=>Sp(t,n).bind((t=>t.fold(O(Op,e),O(Tp,e),O(Rp,e),O(Ap,e)))))(e,t,n)),(t=>((e,t)=>Pp(e,t))(e,t))))(e,o,r,t)})(e,n,o)},Fp=(e,t)=>{for(;t&&t!==e;){if(Mo(t)||Io(t))return t;t=t.parentNode}return null},Up=(e,t)=>{t(e),e.firstChild&&Up(e.firstChild,t),e.next&&Up(e.next,t)},zp=(e,t,n,o)=>{const r=n.name;for(let t=0,s=e.length;t<s;t++){const s=e[t];if(s.name===r){const e=o.nodes[r];e?e.nodes.push(n):o.nodes[r]={filter:s,nodes:[n]}}}if(n.attributes)for(let e=0,r=t.length;e<r;e++){const r=t[e],s=r.name;if(s in n.attributes.map){const e=o.attributes[s];e?e.nodes.push(n):o.attributes[s]={filter:r,nodes:[n]}}}},jp=(e,t)=>{const n=e=>{fe(e,(e=>{const n=K(e.nodes,(e=>C(e.parent)));$(e.filter.callbacks,(o=>{o(n,e.filter.name,t)}))}))};n(e.nodes),n(e.attributes)},Vp=(e,t,n,o={})=>{const r=((e,t,n)=>{const o={nodes:{},attributes:{}};return n.firstChild&&Up(n.firstChild,(n=>{zp(e,t,n,o)})),o})(e,t,n);jp(r,o)},Hp=(e,t,n,o)=>{t.insert&&n[o.name]?o.empty().append(new Ff("br",1)):o.empty().append(new Ff("#text",3)).value=fr},$p=(e,t)=>e&&e.firstChild&&e.firstChild===e.lastChild&&e.firstChild.name===t,qp=(e,t,n,o)=>o.isEmpty(t,n,(t=>((e,t)=>{const n=e.getElementRule(t.name);return n&&n.paddEmpty})(e,t))),Wp=(e,t,n=e.parent)=>{if(t.getSpecialElements()[e.name])e.empty().remove();else{const o=e.children();for(const e of o)t.isValidChild(n.name,e.name)||Wp(e,t,n);e.unwrap()}},Kp=(e,t,n=S)=>{const o=t.getTextBlockElements(),r=t.getNonEmptyElements(),s=t.getWhitespaceElements(),a=Bt.makeMap("tr,td,th,tbody,thead,tfoot,table"),i=new Set;for(let l=0;l<e.length;l++){const d=e[l];let c,u,m;if(!d.parent||i.has(d))continue;if(o[d.name]&&"li"===d.parent.name){let e=d.next;for(;e&&o[e.name];)e.name="li",i.add(e),d.parent.insert(e,d.parent),e=e.next;d.unwrap();continue}const f=[d];for(c=d.parent;c&&!t.isValidChild(c.name,d.name)&&!a[c.name];c=c.parent)f.push(c);if(c&&f.length>1)if(t.isValidChild(c.name,d.name)){f.reverse(),u=f[0].clone(),n(u);let e=u;for(let o=0;o<f.length-1;o++){t.isValidChild(e.name,f[o].name)?(m=f[o].clone(),n(m),e.append(m)):m=e;for(let e=f[o].firstChild;e&&e!==f[o+1];){const t=e.next;m.append(e),e=t}e=m}qp(t,r,s,u)?c.insert(d,f[0],!0):(c.insert(u,f[0],!0),c.insert(d,u)),c=f[0],(qp(t,r,s,c)||$p(c,"br"))&&c.empty().remove()}else Wp(d,t);else if(d.parent){if("li"===d.name){let e=d.prev;if(e&&("ul"===e.name||"ol"===e.name)){e.append(d);continue}if(e=d.next,e&&("ul"===e.name||"ol"===e.name)){e.insert(d,e.firstChild,!0);continue}const t=new Ff("ul",1);n(t),d.wrap(t);continue}if(t.isValidChild(d.parent.name,"div")&&t.isValidChild("div",d.name)){const e=new Ff("div",1);n(e),d.wrap(e)}else Wp(d,t)}}},Gp=e=>e.collapsed?e:(e=>{const t=Ya.fromRangeStart(e),n=Ya.fromRangeEnd(e),o=e.commonAncestorContainer;return hc(!1,o,n).map((r=>!Vd(t,n,o)&&Vd(t,r,o)?((e,t,n,o)=>{const r=document.createRange();return r.setStart(e,t),r.setEnd(n,o),r})(t.container(),t.offset(),r.container(),r.offset()):e)).getOr(e)})(e),Yp=(e,t)=>{let n=t.firstChild,o=t.lastChild;return n&&"meta"===n.name&&(n=n.next),o&&"mce_marker"===o.attr("id")&&(o=o.prev),((e,t)=>{const n=e.getNonEmptyElements();return t&&(t.isEmpty(n)||((e,t)=>e.getBlockElements()[t.name]&&(e=>e.firstChild&&e.firstChild===e.lastChild)(t)&&(e=>"br"===e.name||e.value===fr)(t.firstChild))(e,t))})(e,o)&&(o=o.prev),!(!n||n!==o||"ul"!==n.name&&"ol"!==n.name)},Xp=e=>{return e.length>0&&(!(n=e[e.length-1]).firstChild||(t=n)&&t.firstChild&&t.firstChild===t.lastChild&&(e=>e.data===fr||Po(e))(t.firstChild))?e.slice(0,-1):e;var t,n},Qp=(e,t)=>{const n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Jp=(e,t)=>{const n=Ya.after(e),o=mc(t).prev(n);return o?o.toRange():null},Zp=(e,t,n,o)=>{const r=((e,t,n)=>{const o=t.serialize(n);return(e=>{const t=e.firstChild,n=e.lastChild;return t&&"META"===t.nodeName&&t.parentNode.removeChild(t),n&&"mce_marker"===n.id&&n.parentNode.removeChild(n),e})(e.createFragment(o))})(t,e,o),s=Qp(t,n.startContainer),a=Xp((i=r.firstChild,K(i.childNodes,(e=>"LI"===e.nodeName))));var i;const l=t.getRoot(),d=e=>{const o=Ya.fromRangeStart(n),r=mc(t.getRoot()),a=1===e?r.prev(o):r.next(o);return!a||Qp(t,a.getNode())!==s};return d(1)?((e,t,n)=>{const o=e.parentNode;return Bt.each(t,(t=>{o.insertBefore(t,e)})),((e,t)=>{const n=Ya.before(e),o=mc(t).next(n);return o?o.toRange():null})(e,n)})(s,a,l):d(2)?((e,t,n,o)=>(o.insertAfter(t.reverse(),e),Jp(t[0],n)))(s,a,l,t):((e,t,n,o)=>{const r=((e,t)=>{const n=t.cloneRange(),o=t.cloneRange();return n.setStartBefore(e),o.setEndAfter(e),[n.cloneContents(),o.cloneContents()]})(e,o),s=e.parentNode;return s.insertBefore(r[0],e),Bt.each(t,(t=>{s.insertBefore(t,e)})),s.insertBefore(r[1],e),s.removeChild(e),Jp(t[t.length-1],n)})(s,a,l,n)},eh=["pre"],th=Fo,nh=(e,t,n)=>{let o,r,s;const a=e.selection,i=e.dom,l=e.parser,d=n.merge,c=Kf({validate:!0},e.schema),u='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;</span>';-1===t.indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,u),r=a.getRng();const m=r.startContainer||(r.parentElement?r.parentElement():null),f=e.getBody();m===f&&a.isCollapsed()&&i.isBlock(f.firstChild)&&((e,t)=>t&&!e.schema.getVoidElements()[t.nodeName])(e,f.firstChild)&&i.isEmpty(f.firstChild)&&(r=i.createRng(),r.setStart(f.firstChild,0),r.setEnd(f.firstChild,0),a.setRng(r)),a.isCollapsed()||(e=>{const t=e.dom,n=Gp(e.selection.getRng());e.selection.setRng(n);const o=t.getParent(n.startContainer,th);((e,t,n)=>null!==n&&n===e.getParent(t.endContainer,th)&&Du(fn(n),t))(t,n,o)?Op(e,n,fn(o)):e.getDoc().execCommand("Delete",!1,null)})(e),o=a.getNode();const g={context:o.nodeName.toLowerCase(),data:n.data,insert:!0},p=l.parse(t,g);if(!0===n.paste&&Yp(e.schema,p)&&((e,t)=>!!Qp(e,t))(i,o))return r=Zp(c,i,a.getRng(),p),a.setRng(r),t;if(!0===n.paste&&((e,t)=>{const n=e.firstChild,o=j(eh,n.name),r=n.name===t.tagName.toLowerCase();return n===("bookmark"===e.lastChild.attr("data-mce-type")?e.lastChild.prev:e.lastChild)&&o&&r})(p,o)&&p.firstChild.unwrap(),(e=>{let t=e;for(;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")})(p),s=p.lastChild,"mce_marker"===s.attr("id")){const t=s;for(s=s.prev;s;s=s.walk(!0))if(3===s.type||!i.isBlock(s.name)){e.schema.isValidChild(s.parent.name,"span")&&s.parent.insert(t,s,"br"===s.name);break}}if(e._selectionOverrides.showBlockCaretContainer(o),g.invalid){e.selection.setContent(u),o=a.getNode();const n=e.getBody();for(9===o.nodeType?o=s=n:s=o;s!==n;)o=s,s=s.parentNode;t=o===n?n.innerHTML:i.getOuterHTML(o);const r=l.parse(t);for(let e=r;e;e=e.walk())if("mce_marker"===e.attr("id")){e.replace(p);break}const d=p.children(),m=p.parent.name;p.unwrap();const f=K(d,(t=>!e.schema.isValidChild(m,t.name)));Kp(f,e.schema),Vp(l.getNodeFilters(),l.getAttributeFilters(),r),t=c.serialize(r),o===n?i.setHTML(n,t):i.setOuterHTML(o,t)}else t=c.serialize(p),((e,t,n)=>{if("all"===n.getAttribute("data-mce-bogus"))n.parentNode.insertBefore(e.dom.createFragment(t),n);else{const o=n.firstChild,r=n.lastChild;!o||o===r&&"BR"===o.nodeName?e.dom.setHTML(n,t):e.selection.setContent(t,{no_events:!0})}})(e,t,o);var h;return((e,t)=>{const n=e.schema.getTextInlineElements(),o=e.dom;if(t){const t=e.getBody(),r=$f(o);Bt.each(o.select("*[data-mce-fragment]"),(e=>{if(C(n[e.nodeName.toLowerCase()])&&((e,t)=>te(Xf(e,t),(e=>!(e=>Gf.has(e))(e))))(o,e))for(let n=e.parentNode;C(n)&&n!==t&&!Qf(o,e,n);n=n.parentNode)if(r.compare(n,e)){o.remove(e,!0);break}}))}})(e,d),((e,t)=>{let n;const o=e.dom,r=e.selection;if(!t)return;r.scrollIntoView(t);const s=Fp(e.getBody(),t);if("false"===o.getContentEditable(s))return o.remove(t),void r.select(s);let a=o.createRng();const i=t.previousSibling;if(Ro(i)){a.setStart(i,i.nodeValue.length);const e=t.nextSibling;Ro(e)&&(i.appendData(e.data),e.parentNode.removeChild(e))}else a.setStartBefore(t),a.setEndBefore(t);const l=o.getParent(t,o.isBlock);o.remove(t),l&&o.isEmpty(l)&&(to(fn(l)),a.setStart(l,0),a.setEnd(l,0),th(l)||(e=>!!e.getAttribute("data-mce-fragment"))(l)||!(n=(t=>{let n=Ya.fromRangeStart(t);if(n=mc(e.getBody()).next(n),n)return n.toRange()})(a))?o.add(l,o.create("br",{"data-mce-bogus":"1"})):(a=n,o.remove(l))),r.setRng(a)})(e,i.get("mce_marker")),h=e.getBody(),Bt.each(h.getElementsByTagName("*"),(e=>{e.removeAttribute("data-mce-fragment")})),((e,t)=>{M.from(e.getParent(t,"td,th")).map(fn).each(gg)})(i,a.getStart()),t},oh=e=>e instanceof Ff,rh=(e,t,n)=>{e.dom.setHTML(e.getBody(),t),!0!==n&&(e=>{xf(e)&&xc(e.getBody()).each((t=>{const n=t.getNode(),o=_o(n)?xc(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e)},sh=(e,t)=>((e,t)=>{const n=e.dom;return n.parentNode?((e,t)=>Q(e.dom.childNodes,(e=>t(fn(e)))).map(fn))(fn(n.parentNode),(n=>!vn(e,n)&&t(n))):M.none()})(e,t).isSome(),ah=e=>w(e)?e:P,ih=(e,t,n)=>{const o=t(e),r=ah(n);return o.orThunk((()=>r(e)?M.none():((e,t,n)=>{let o=e.dom;const r=ah(n);for(;o.parentNode;){o=o.parentNode;const e=fn(o),n=t(e);if(n.isSome())return n;if(r(e))break}return M.none()})(e,t,r)))},lh=nu,dh=(e,t,n)=>{const o=e.formatter.get(n);if(o)for(let n=0;n<o.length;n++){const r=o[n];if(lu(r)&&!1===r.inherit&&e.dom.is(t,r.selector))return!0}return!1},ch=(e,t,n,o,r)=>{const s=e.dom.getRoot();return t!==s&&(t=e.dom.getParent(t,(t=>!!dh(e,t,n)||t.parentNode===s||!!fh(e,t,n,o,!0))),!!fh(e,t,n,o,r))},uh=(e,t,n)=>!(!du(n)||!lh(t,n.inline))||!(!iu(n)||!lh(t,n.block))||!!lu(n)&&Co(t)&&e.is(t,n.selector),mh=(e,t,n,o,r,s)=>{const a=n[o];if(w(n.onmatch))return n.onmatch(t,n,o);if(a)if(v(a.length)){for(const i in a)if(xe(a,i)){const l="attributes"===o?e.getAttrib(t,i):ru(e,t,i),d=tu(a[i],s),c=y(l)||Ke(l);if(c&&y(d))continue;if(r&&c&&!n.exact)return!1;if((!r||n.exact)&&!lh(l,ou(d,i)))return!1}}else for(let n=0;n<a.length;n++)if("attributes"===o?e.getAttrib(t,a[n]):ru(e,t,a[n]))return!0;return!0},fh=(e,t,n,o,r)=>{const s=e.formatter.get(n),a=e.dom;if(s&&t)for(let n=0;n<s.length;n++){const i=s[n];if(uh(e.dom,t,i)&&mh(a,t,i,"attributes",r,o)&&mh(a,t,i,"styles",r,o)){const n=i.classes;if(n)for(let r=0;r<n.length;r++)if(!e.dom.hasClass(t,tu(n[r],o)))return;return i}}},gh=(e,t,n,o,r)=>{if(o)return ch(e,o,t,n,r);if(o=e.selection.getNode(),ch(e,o,t,n,r))return!0;const s=e.selection.getStart();return!(s===o||!ch(e,s,t,n,r))},ph=pr,hh="_mce_caret",bh=e=>(e=>{const t=[];for(;e;){if(3===e.nodeType&&e.nodeValue!==ph||e.childNodes.length>1)return[];1===e.nodeType&&t.push(e),e=e.firstChild}return t})(e).length>0,vh=e=>{if(e){const t=new Qo(e,e);for(e=t.current();e;e=t.next())if(Ro(e))return e}return null},yh=e=>{const t=un("span");return Wt(t,{id:hh,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&Zn(t,mn(ph)),t},Ch=(e,t,n=!0)=>{const o=e.dom,r=e.selection;if(bh(t))Zg(e,!1,fn(t),n);else{const e=r.getRng(),n=o.getParent(t,o.isBlock),s=e.startContainer,a=e.startOffset,i=e.endContainer,l=e.endOffset,d=(e=>{const t=vh(e);return t&&t.nodeValue.charAt(0)===ph&&t.deleteData(0,1),t})(t);o.remove(t,!0),s===d&&a>0&&e.setStart(d,a-1),i===d&&l>0&&e.setEnd(d,l-1),n&&o.isEmpty(n)&&fg(fn(n)),r.setRng(e)}},wh=(e,t,n=!0)=>{const o=e.dom,r=e.selection;if(t)Ch(e,t,n);else if(!(t=Ec(e.getBody(),r.getStart())))for(;t=o.get(hh);)Ch(e,t,!1)},xh=(e,t)=>(e.appendChild(t),t),kh=(e,t)=>{const n=G(e,((e,t)=>xh(e,t.cloneNode(!1))),t);return xh(n,n.ownerDocument.createTextNode(ph))},Sh=(e,t,n,o)=>{const a=e.dom,i=e.selection;let l,d,c;const u=[],m=i.getRng(),f=m.startContainer,g=m.startOffset;for(d=f,3===f.nodeType&&(g!==f.nodeValue.length&&(l=!0),d=d.parentNode);d;){if(fh(e,d,t,n,o)){c=d;break}d.nextSibling&&(l=!0),u.push(d),d=d.parentNode}if(c)if(l){const r=i.getBookmark();m.collapse(!0);let s=_u(e,m,e.formatter.get(t),!0);s=Dm(s),e.formatter.remove(t,n,s,o),i.moveToBookmark(r)}else{const l=Ec(e.getBody(),c),d=yh(!1).dom;((e,t,n)=>{const o=e.dom,r=o.getParent(n,O(Jc,e));r&&o.isEmpty(r)?n.parentNode.replaceChild(t,n):((e=>{const t=na(e,"br"),n=K((e=>{const t=[];let n=e.dom;for(;n;)t.push(fn(n)),n=n.lastChild;return t})(e).slice(-1),nr);t.length===n.length&&$(n,no)})(fn(n)),o.isEmpty(n)?n.parentNode.replaceChild(t,n):o.insertAfter(t,n))})(e,d,null!==l?l:c);const m=((e,t,n,o,a,i)=>{const l=e.formatter,d=e.dom,c=K(ue(l.get()),(e=>e!==o&&!Ue(e,"removeformat"))),u=((e,t,n)=>Y(n,((n,o)=>{const r=((e,t)=>V(e.formatter.get(t),(e=>{const t=e=>e.length>1&&"%"===e.charAt(0);return V(["styles","attributes"],(n=>we(e,n).exists((e=>{const n=p(e)?e:Ce(e);return V(n,t)}))))})))(e,o);return e.formatter.matchNode(t,o,{},r)?n.concat([o]):n}),[]))(e,n,c);if(K(u,(t=>!((e,t,n)=>{const o=["inline","block","selector","attributes","styles","classes"],a=e=>ve(e,((e,t)=>V(o,(e=>e===t))));return V(e.formatter.get(t),(t=>{const o=a(t);return V(e.formatter.get(n),(e=>{const t=a(e);return((e,t,n=s)=>r(n).eq(e,t))(o,t)}))}))})(e,t,o))).length>0){const e=n.cloneNode(!1);return d.add(t,e),l.remove(o,a,e,i),d.remove(e),M.some(e)}return M.none()})(e,d,c,t,n,o),f=kh(u.concat(m.toArray()),d);Ch(e,l,!1),i.setCursorLocation(f,1),a.isEmpty(c)&&a.remove(c)}},_h=(e,t)=>{const n=e.schema.getTextInlineElements();return xe(n,Mt(t))&&!_c(t.dom)&&!So(t.dom)},Eh={},Nh=Re,Rh=Ee;Eh.pre||(Eh.pre=[]),Eh.pre.push((e=>{const t=e.selection.getRng();let n;const o=e=>r(e.previousSibling)&&-1!==Ae(n,e.previousSibling),r=wo(["pre"]);t.collapsed||(n=e.selection.getSelectedBlocks(),Rh(Nh(Nh(n,r),o),(e=>{((e,t)=>{const n=fn(t),o=wn(n).dom;no(n),eo(fn(e),[un("br",o),un("br",o),...Rn(n)])})(e.previousSibling,e)})))}));const Ah=Bt.each,Oh=e=>Co(e)&&!Fc(e)&&!_c(e)&&!So(e),Th=(e,t)=>{for(let n=e;n;n=n[t]){if(Ro(n)&&We(n.data))return e;if(Co(n)&&!Fc(n))return n}return e},Bh=(e,t,n)=>{const o=$f(e);if(t&&n&&(t=Th(t,"previousSibling"),n=Th(n,"nextSibling"),o.compare(t,n))){for(let e=t.nextSibling;e&&e!==n;){const n=e;e=e.nextSibling,t.appendChild(n)}return e.remove(n),Bt.each(Bt.grep(n.childNodes),(e=>{t.appendChild(e)})),t}return n},Dh=(e,t,n,o)=>{if(o&&!1!==t.merge_siblings){const t=Bh(e,Qc(o),o);Bh(e,t,Qc(t,!0))}},Ph=(e,t,n)=>{Ah(e.childNodes,(e=>{Oh(e)&&(t(e)&&n(e),e.hasChildNodes()&&Ph(e,t,n))}))},Lh=(e,t)=>n=>!(!n||!ru(e,n,t)),Mh=(e,t,n)=>o=>{e.setStyle(o,t,n),""===o.getAttribute("style")&&o.removeAttribute("style"),((e,t)=>{"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)})(e,o)},Ih=Ci([{keep:[]},{rename:["name"]},{removed:[]}]),Fh=/^(src|href|style)$/,Uh=Bt.each,zh=nu,jh=(e,t,n)=>e.isChildOf(t,n)&&t!==n&&!e.isBlock(n),Vh=(e,t,n)=>{let o=t[n?"startContainer":"endContainer"],r=t[n?"startOffset":"endOffset"];if(Co(o)){const e=o.childNodes.length-1;!n&&r&&r--,o=o.childNodes[r>e?e:r]}return Ro(o)&&n&&r>=o.nodeValue.length&&(o=new Qo(o,e.getBody()).next()||o),Ro(o)&&!n&&0===r&&(o=new Qo(o,e.getBody()).prev()||o),o},Hh=(e,t)=>{const n=t?"firstChild":"lastChild";if((e=>/^(TR|TH|TD)$/.test(e.nodeName))(e)&&e[n]){const t=e[n];return"TR"===e.nodeName&&t[n]||t}return e},$h=(e,t,n,o)=>{const r=e.create(n,o);return t.parentNode.insertBefore(r,t),r.appendChild(t),r},qh=(e,t,n,o,r)=>{const s=fn(t),a=fn(e.create(o,r)),i=n?Nn(s):En(s);return eo(a,i),n?(Xn(s,a),Jn(a,s)):(Qn(s,a),Zn(a,s)),a.dom},Wh=(e,t,n,o,r)=>{let s;const a=e.dom;if(!uh(a,o,t)&&!((e,t)=>t.links&&"A"===e.nodeName)(o,t))return Ih.keep();const i=o;if(du(t)&&"all"===t.remove&&p(t.preserve_attributes)){const e=K(a.getAttribs(i),(e=>j(t.preserve_attributes,e.name.toLowerCase())));if(a.removeAllAttribs(i),$(e,(e=>a.setAttrib(i,e.name,e.value))),e.length>0)return Ih.rename("span")}if("all"!==t.remove){Uh(t.styles,((e,o)=>{e=ou(tu(e,n),o+""),x(o)&&(o=e,r=null),(t.remove_similar||!r||zh(ru(a,r,o),e))&&a.setStyle(i,o,""),s=!0})),s&&""===a.getAttrib(i,"style")&&(i.removeAttribute("style"),i.removeAttribute("data-mce-style")),Uh(t.attributes,((e,o)=>{let s;if(e=tu(e,n),x(o)&&(o=e,r=null),t.remove_similar||!r||zh(a.getAttrib(r,o),e)){if("class"===o&&(e=a.getAttrib(i,o))&&(s="",$(e.split(/\s+/),(e=>{/mce\-\w+/.test(e)&&(s+=(s?" ":"")+e)})),s))return void a.setAttrib(i,o,s);if(Fh.test(o)&&i.removeAttribute("data-mce-"+o),"style"===o&&wo(["li"])(i)&&"none"===a.getStyle(i,"list-style-type"))return i.removeAttribute(o),void a.setStyle(i,"list-style-type","none");"class"===o&&i.removeAttribute("className"),i.removeAttribute(o)}})),Uh(t.classes,(e=>{e=tu(e,n),r&&!a.hasClass(r,e)||a.removeClass(i,e)}));const e=a.getAttribs(i);for(let t=0;t<e.length;t++){const n=e[t].nodeName;if(0!==n.indexOf("_")&&0!==n.indexOf("data-"))return Ih.keep()}}return"none"!==t.remove?(((e,t,n)=>{const o=t.parentNode;let r;const s=e.dom,a=ji(e);iu(n)&&o===s.getRoot()&&(n.list_block&&zh(t,n.list_block)||$(de(t.childNodes),(t=>{Zc(e,a,t.nodeName.toLowerCase())?r?r.appendChild(t):(r=$h(s,t,a),s.setAttribs(r,Vi(e))):r=null}))),(e=>lu(e)&&du(e)&&Dt(we(e,"mixed"),!0))(n)&&!zh(n.inline,t)||s.remove(t,!0)})(e,i,t),Ih.removed()):Ih.keep()},Kh=(e,t,n,o,r)=>Wh(e,t,n,o,r).fold(P,(t=>(e.dom.rename(o,t),!0)),L),Gh=(e,t,n,o)=>Wh(e,t,n,o,o).fold(N(o),(t=>(e.dom.createFragment().appendChild(o),e.dom.rename(o,t))),N(null)),Yh=(e,t,n,o,r)=>{const s=e.formatter.get(t),a=s[0];let i=!0;const l=e.dom,d=e.selection,c=o=>{const i=((e,t,n,o,r)=>{let s;return $(au(e.dom,t.parentNode).reverse(),(t=>{if(!s&&"_start"!==t.id&&"_end"!==t.id){const a=fh(e,t,n,o,r);a&&!1!==a.split&&(s=t)}})),s})(e,o,t,n,r);return((e,t,n,o,r,s,a,i)=>{let l,d,c;const u=e.dom;if(n){const s=n.parentNode;for(let n=o.parentNode;n&&n!==s;n=n.parentNode){l=u.clone(n,!1);for(let n=0;n<t.length&&(l=Gh(e,t[n],i,l),null!==l);n++);l&&(d&&l.appendChild(d),c||(c=l),d=l)}a.mixed&&u.isBlock(n)||(o=u.split(n,o)),d&&(r.parentNode.insertBefore(d,r),c.appendChild(r),du(a)&&Dh(u,a,0,d))}return o})(e,s,i,o,o,0,a,n)},u=t=>V(s,(o=>Kh(e,o,n,t,t))),m=t=>{let n=!0,o=!1;Co(t)&&l.getContentEditable(t)&&(n=i,i="true"===l.getContentEditable(t),o=!0);const r=de(t.childNodes);if(i&&!o){const e=u(t)||V(s,(e=>uh(l,t,e))),n=t.parentNode;!e&&C(n)&&cu(a)&&u(n)}if(a.deep&&r.length){for(let e=0;e<r.length;e++)m(r[e]);o&&(i=n)}$(["underline","line-through","overline"],(n=>{Co(t)&&e.dom.getStyle(t,"text-decoration")===n&&t.parentNode&&su(l,t.parentNode)===n&&Kh(e,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:n}},null,t)}))},f=e=>{const t=l.get(e?"_start":"_end");let n=t[e?"firstChild":"lastChild"];return(e=>Fc(e)&&Co(e)&&("_start"===e.id||"_end"===e.id))(n)&&(n=n[e?"firstChild":"lastChild"]),Ro(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),l.remove(t,!0),n},g=t=>{let n,o,r=_u(e,t,s,t.collapsed);if(a.split){if(r=Dm(r),n=Vh(e,r,!0),o=Vh(e,r),n!==o){if(n=Hh(n,!0),o=Hh(o,!1),jh(l,n,o)){const e=M.from(n.firstChild).getOr(n);return c(qh(l,e,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void f(!0)}if(jh(l,o,n)){const e=M.from(o.lastChild).getOr(o);return c(qh(l,e,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void f(!1)}n=$h(l,n,"span",{id:"_start","data-mce-type":"bookmark"}),o=$h(l,o,"span",{id:"_end","data-mce-type":"bookmark"});const e=l.createRng();e.setStartAfter(n),e.setEndBefore(o),Eu(l,e,(e=>{$(e,(e=>{Fc(e)||Fc(e.parentNode)||c(e)}))})),c(n),c(o),n=f(!0),o=f()}else n=o=c(n);r.startContainer=n.parentNode?n.parentNode:n,r.startOffset=l.nodeIndex(n),r.endContainer=o.parentNode?o.parentNode:o,r.endOffset=l.nodeIndex(o)+1}Eu(l,r,(e=>{$(e,m)}))};if(o){if(Yc(o)){const e=l.createRng();e.setStartBefore(o),e.setEndAfter(o),g(e)}else g(o);Xu(e,t,o,n)}else if("false"!==l.getContentEditable(d.getNode()))d.isCollapsed()&&du(a)&&!Au(e).length?Sh(e,t,n,r):(Iu(d,!0,(()=>{Mu(e,g)})),du(a)&&gh(e,t,n,d.getStart())&&Xc(l,d,d.getRng()),e.nodeChanged()),Xu(e,t,o,n);else{o=d.getNode();for(let t=0;t<s.length&&(!s[t].ceFalseOverride||!Kh(e,s[t],n,o,o));t++);Xu(e,t,o,n)}},Xh=Bt.each,Qh=Bt.each,Jh=e=>Co(e)&&!Fc(e)&&!_c(e)&&!So(e),Zh=(e,t,n,o)=>{const r=e.formatter.get(t),s=r[0],a=!o&&e.selection.isCollapsed(),i=e.dom,l=e.selection,d=(e,t=s)=>{if(w(t.onformat)&&t.onformat(e,t,n,o),Qh(t.styles,((t,o)=>{i.setStyle(e,o,tu(t,n))})),t.styles){const t=i.getAttrib(e,"style");t&&i.setAttrib(e,"data-mce-style",t)}Qh(t.attributes,((t,o)=>{i.setAttrib(e,o,tu(t,n))})),Qh(t.classes,(t=>{t=tu(t,n),i.hasClass(e,t)||i.addClass(e,t)}))},c=(e,t)=>{let n=!1;return Qh(e,(e=>!!lu(e)&&(C(e.collapsed)&&e.collapsed!==a?void 0:i.is(t,e.selector)&&!_c(t)?(d(t,e),n=!0,!1):void 0))),n},u=e=>{if(m(e)){const t=i.create(e);return d(t),t}return null},f=(o,a,i)=>{const l=[];let m=!0;const f=s.inline||s.block,g=u(f);Eu(o,a,(a=>{let u;const p=a=>{let h=!1,b=m;const v=a.nodeName.toLowerCase(),y=a.parentNode,w=y.nodeName.toLowerCase();if(Co(a)&&o.getContentEditable(a)&&(b=m,m="true"===o.getContentEditable(a),h=!0),Po(a)&&!((e,t,n,o)=>{if(El(e)&&du(t)){const t=xs(e.schema),r=sh(fn(n),(e=>_c(e.dom)));return ke(t,o)&&Yr(fn(n.parentNode),!1)&&!r}return!1})(e,s,a,w))return u=null,void(iu(s)&&o.remove(a));if(iu(s)&&s.wrapper&&fh(e,a,t,n))u=null;else{if(m&&!h&&iu(s)&&!s.wrapper&&Jc(e,v)&&Zc(e,w,f)){const e=o.rename(a,f);return d(e),l.push(e),void(u=null)}if(lu(s)){let e=c(r,a);if(!e&&C(y)&&cu(s)&&(e=c(r,y)),!du(s)||e)return void(u=null)}!m||h||!Zc(e,f,v)||!Zc(e,w,f)||!i&&Ro(a)&&hr(a.data)||_c(a)||du(s)&&o.isBlock(a)?(u=null,$(de(a.childNodes),p),h&&(m=b),u=null):(u||(u=o.clone(g,!1),a.parentNode.insertBefore(u,a),l.push(u)),u.appendChild(a))}};$(a,p)})),!0===s.links&&$(l,(e=>{const t=e=>{"A"===e.nodeName&&d(e,s),$(de(e.childNodes),t)};t(e)})),$(l,(a=>{const i=(e=>{let t=0;return $(e.childNodes,(e=>{(e=>C(e)&&Ro(e)&&0===e.length)(e)||Fc(e)||t++})),t})(a);!(l.length>1)&&o.isBlock(a)||0!==i?(du(s)||iu(s)&&s.wrapper)&&(s.exact||1!==i||(a=(e=>{const t=Q(e.childNodes,Jh).filter((e=>uh(o,e,s)));return t.map((t=>{const n=o.clone(t,!1);return d(n),o.replace(n,e,!0),o.remove(t,!0),n})).getOr(e)})(a)),((e,t,n,o)=>{Xh(t,(t=>{du(t)&&Xh(e.dom.select(t.inline,o),(o=>{Oh(o)&&Kh(e,t,n,o,t.exact?o:null)})),((e,t,n)=>{if(t.clear_child_styles){const o=t.links?"*:not(a)":"*";Ah(e.select(o,n),(n=>{Oh(n)&&Ah(t.styles,((t,o)=>{e.setStyle(n,o,"")}))}))}})(e.dom,t,o)}))})(e,r,n,a),((e,t,n,o,r)=>{fh(e,r.parentNode,n,o)&&Kh(e,t,o,r)||t.merge_with_parents&&e.dom.getParent(r.parentNode,(s=>{if(fh(e,s,n,o))return Kh(e,t,o,r),!0}))})(e,s,t,n,a),((e,t,n,o)=>{t.styles&&t.styles.backgroundColor&&Ph(o,Lh(e,"fontSize"),Mh(e,"backgroundColor",tu(t.styles.backgroundColor,n)))})(o,s,n,a),((e,t,n,o)=>{const r=t=>{if(1===t.nodeType&&t.parentNode&&1===t.parentNode.nodeType){const n=su(e,t.parentNode);e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null)}};t.styles&&(t.styles.color||t.styles.textDecoration)&&(Bt.walk(o,r,"childNodes"),r(o))})(o,s,0,a),((e,t,n,o)=>{!du(t)||"sub"!==t.inline&&"sup"!==t.inline||(Ph(o,Lh(e,"fontSize"),Mh(e,"fontSize","")),e.remove(e.select("sup"===t.inline?"sub":"sup",o),!0))})(o,s,0,a),Dh(o,s,0,a)):o.remove(a,!0)}))};if("false"!==i.getContentEditable(l.getNode())){if(s){if(o)if(Yc(o)){if(!c(r,o)){const t=i.createRng();t.setStartBefore(o),t.setEndAfter(o),f(i,_u(e,t,r),!0)}}else f(i,o,!0);else a&&du(s)&&!Au(e).length?((e,t,n)=>{let o,r;const s=e.selection,a=s.getRng();let i=a.startOffset;const l=a.startContainer.nodeValue;o=Ec(e.getBody(),s.getStart()),o&&(r=vh(o));const d=/[^\s\u00a0\u00ad\u200b\ufeff]/;if(l&&i>0&&i<l.length&&d.test(l.charAt(i))&&d.test(l.charAt(i-1))){const o=s.getBookmark();a.collapse(!0);let r=_u(e,a,e.formatter.get(t));r=Dm(r),e.formatter.apply(t,n,r),s.moveToBookmark(o)}else o&&r.nodeValue===ph||(c=e.getDoc(),u=yh(!0).dom,o=c.importNode(u,!0),r=o.firstChild,a.insertNode(o),i=1),e.formatter.apply(t,n,o),s.setCursorLocation(r,i);var c,u})(e,t,n):(l.setRng(Gp(l.getRng())),Iu(l,!0,(()=>{Mu(e,((t,n)=>{const o=n?t:_u(e,t,r);f(i,o,!1)}))})),Xc(i,l,l.getRng()),e.nodeChanged());((e,t)=>{Rh(Eh[e],(e=>{e(t)}))})(t,e)}Yu(e,t,o,n)}else{o=l.getNode();for(let e=0,t=r.length;e<t;e++){const t=r[e];if(t.ceFalseOverride&&lu(t)&&i.is(o,t.selector)){d(o,t);break}}Yu(e,t,o,n)}},eb=e=>xe(e,"vars"),tb=e=>e.selection.getStart(),nb=(e,t,n,o,r)=>X(t,(t=>{const s=e.formatter.matchNode(t,n,null!=r?r:{},o);return!v(s)}),(t=>!!dh(e,t,n)||!o&&C(e.formatter.matchNode(t,n,r,!0)))),ob=(e,t)=>{const n=null!=t?t:tb(e);return K(au(e.dom,n),(e=>Co(e)&&!So(e)))},rb=(e,t,n)=>{const o=ob(e,t);fe(n,((n,r)=>{const s=n=>{const s=nb(e,o,r,n.similar,eb(n)?n.vars:void 0),a=s.isSome();if(n.state.get()!==a){n.state.set(a);const e=s.getOr(t);eb(n)?n.callback(a,{node:e,format:r,parents:o}):$(n.callbacks,(t=>t(a,{node:e,format:r,parents:o})))}};$([n.withSimilar,n.withoutSimilar],s),$(n.withVars,s)}))};function sb(e){return sb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sb(e)}function ab(e,t){return ab=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},ab(e,t)}function ib(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function lb(e,t,n){return lb=ib()?Reflect.construct:function(e,t,n){var o=[null];o.push.apply(o,t);var r=new(Function.bind.apply(e,o));return n&&ab(r,n.prototype),r},lb.apply(null,arguments)}function db(e){return function(e){if(Array.isArray(e))return cb(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return cb(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?cb(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cb(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var ub=Object.hasOwnProperty,mb=Object.setPrototypeOf,fb=Object.isFrozen,gb=Object.getPrototypeOf,pb=Object.getOwnPropertyDescriptor,hb=Object.freeze,bb=Object.seal,vb=Object.create,yb="undefined"!=typeof Reflect&&Reflect,Cb=yb.apply,wb=yb.construct;Cb||(Cb=function(e,t,n){return e.apply(t,n)}),hb||(hb=function(e){return e}),bb||(bb=function(e){return e}),wb||(wb=function(e,t){return lb(e,db(t))});var xb,kb=Db(Array.prototype.forEach),Sb=Db(Array.prototype.pop),_b=Db(Array.prototype.push),Eb=Db(String.prototype.toLowerCase),Nb=Db(String.prototype.match),Rb=Db(String.prototype.replace),Ab=Db(String.prototype.indexOf),Ob=Db(String.prototype.trim),Tb=Db(RegExp.prototype.test),Bb=(xb=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return wb(xb,t)});function Db(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return Cb(e,t,o)}}function Pb(e,t){mb&&mb(e,null);for(var n=t.length;n--;){var o=t[n];if("string"==typeof o){var r=Eb(o);r!==o&&(fb(t)||(t[n]=r),o=r)}e[o]=!0}return e}function Lb(e){var t,n=vb(null);for(t in e)Cb(ub,e,[t])&&(n[t]=e[t]);return n}function Mb(e,t){for(;null!==e;){var n=pb(e,t);if(n){if(n.get)return Db(n.get);if("function"==typeof n.value)return Db(n.value)}e=gb(e)}return function(e){return console.warn("fallback value for",e),null}}var Ib=hb(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Fb=hb(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ub=hb(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),zb=hb(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),jb=hb(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),Vb=hb(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Hb=hb(["#text"]),$b=hb(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),qb=hb(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Wb=hb(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Kb=hb(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Gb=bb(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Yb=bb(/<%[\w\W]*|[\w\W]*%>/gm),Xb=bb(/^data-[\-\w.\u00B7-\uFFFF]/),Qb=bb(/^aria-[\-\w]+$/),Jb=bb(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Zb=bb(/^(?:\w+script|data):/i),ev=bb(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),tv=bb(/^html$/i),nv=function(){return"undefined"==typeof window?null:window},ov=function(e,t){if("object"!==sb(e)||"function"!=typeof e.createPolicy)return null;var n=null,o="data-tt-policy-suffix";t.currentScript&&t.currentScript.hasAttribute(o)&&(n=t.currentScript.getAttribute(o));var r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}},rv=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:nv(),n=function(t){return e(t)};if(n.version="2.3.8",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;var o=t.document,r=t.document,s=t.DocumentFragment,a=t.HTMLTemplateElement,i=t.Node,l=t.Element,d=t.NodeFilter,c=t.NamedNodeMap,u=void 0===c?t.NamedNodeMap||t.MozNamedAttrMap:c,m=t.HTMLFormElement,f=t.DOMParser,g=t.trustedTypes,p=l.prototype,h=Mb(p,"cloneNode"),b=Mb(p,"nextSibling"),v=Mb(p,"childNodes"),y=Mb(p,"parentNode");if("function"==typeof a){var C=r.createElement("template");C.content&&C.content.ownerDocument&&(r=C.content.ownerDocument)}var w=ov(g,o),x=w?w.createHTML(""):"",k=r,S=k.implementation,_=k.createNodeIterator,E=k.createDocumentFragment,N=k.getElementsByTagName,R=o.importNode,A={};try{A=Lb(r).documentMode?r.documentMode:{}}catch(e){}var O={};n.isSupported="function"==typeof y&&S&&void 0!==S.createHTMLDocument&&9!==A;var T,B,D=Gb,P=Yb,L=Xb,M=Qb,I=Zb,F=ev,U=Jb,z=null,j=Pb({},[].concat(db(Ib),db(Fb),db(Ub),db(jb),db(Hb))),V=null,H=Pb({},[].concat(db($b),db(qb),db(Wb),db(Kb))),$=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),q=null,W=null,K=!0,G=!0,Y=!1,X=!1,Q=!1,J=!1,Z=!1,ee=!1,te=!1,ne=!1,oe=!0,re=!0,se=!1,ae={},ie=null,le=Pb({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),de=null,ce=Pb({},["audio","video","img","source","image","track"]),ue=null,me=Pb({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),fe="http://www.w3.org/1998/Math/MathML",ge="http://www.w3.org/2000/svg",pe="http://www.w3.org/1999/xhtml",he=pe,be=!1,ve=["application/xhtml+xml","text/html"],ye="text/html",Ce=null,we=r.createElement("form"),xe=function(e){return e instanceof RegExp||e instanceof Function},ke=function(e){Ce&&Ce===e||(e&&"object"===sb(e)||(e={}),e=Lb(e),z="ALLOWED_TAGS"in e?Pb({},e.ALLOWED_TAGS):j,V="ALLOWED_ATTR"in e?Pb({},e.ALLOWED_ATTR):H,ue="ADD_URI_SAFE_ATTR"in e?Pb(Lb(me),e.ADD_URI_SAFE_ATTR):me,de="ADD_DATA_URI_TAGS"in e?Pb(Lb(ce),e.ADD_DATA_URI_TAGS):ce,ie="FORBID_CONTENTS"in e?Pb({},e.FORBID_CONTENTS):le,q="FORBID_TAGS"in e?Pb({},e.FORBID_TAGS):{},W="FORBID_ATTR"in e?Pb({},e.FORBID_ATTR):{},ae="USE_PROFILES"in e&&e.USE_PROFILES,K=!1!==e.ALLOW_ARIA_ATTR,G=!1!==e.ALLOW_DATA_ATTR,Y=e.ALLOW_UNKNOWN_PROTOCOLS||!1,X=e.SAFE_FOR_TEMPLATES||!1,Q=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,ne=e.RETURN_TRUSTED_TYPE||!1,Z=e.FORCE_BODY||!1,oe=!1!==e.SANITIZE_DOM,re=!1!==e.KEEP_CONTENT,se=e.IN_PLACE||!1,U=e.ALLOWED_URI_REGEXP||U,he=e.NAMESPACE||pe,e.CUSTOM_ELEMENT_HANDLING&&xe(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&($.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&xe(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&($.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&($.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),T=T=-1===ve.indexOf(e.PARSER_MEDIA_TYPE)?ye:e.PARSER_MEDIA_TYPE,B="application/xhtml+xml"===T?function(e){return e}:Eb,X&&(G=!1),te&&(ee=!0),ae&&(z=Pb({},db(Hb)),V=[],!0===ae.html&&(Pb(z,Ib),Pb(V,$b)),!0===ae.svg&&(Pb(z,Fb),Pb(V,qb),Pb(V,Kb)),!0===ae.svgFilters&&(Pb(z,Ub),Pb(V,qb),Pb(V,Kb)),!0===ae.mathMl&&(Pb(z,jb),Pb(V,Wb),Pb(V,Kb))),e.ADD_TAGS&&(z===j&&(z=Lb(z)),Pb(z,e.ADD_TAGS)),e.ADD_ATTR&&(V===H&&(V=Lb(V)),Pb(V,e.ADD_ATTR)),e.ADD_URI_SAFE_ATTR&&Pb(ue,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&(ie===le&&(ie=Lb(ie)),Pb(ie,e.FORBID_CONTENTS)),re&&(z["#text"]=!0),Q&&Pb(z,["html","head","body"]),z.table&&(Pb(z,["tbody"]),delete q.tbody),hb&&hb(e),Ce=e)},Se=Pb({},["mi","mo","mn","ms","mtext"]),_e=Pb({},["foreignobject","desc","title","annotation-xml"]),Ee=Pb({},["title","style","font","a","script"]),Ne=Pb({},Fb);Pb(Ne,Ub),Pb(Ne,zb);var Re=Pb({},jb);Pb(Re,Vb);var Ae=function(e){var t=y(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});var n=Eb(e.tagName),o=Eb(t.tagName);return e.namespaceURI===ge?t.namespaceURI===pe?"svg"===n:t.namespaceURI===fe?"svg"===n&&("annotation-xml"===o||Se[o]):Boolean(Ne[n]):e.namespaceURI===fe?t.namespaceURI===pe?"math"===n:t.namespaceURI===ge?"math"===n&&_e[o]:Boolean(Re[n]):e.namespaceURI===pe&&!(t.namespaceURI===ge&&!_e[o])&&!(t.namespaceURI===fe&&!Se[o])&&!Re[n]&&(Ee[n]||!Ne[n])},Oe=function(e){_b(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=x}catch(t){e.remove()}}},Te=function(e,t){try{_b(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){_b(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!V[e])if(ee||te)try{Oe(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){var t,n;if(Z)e="<remove></remove>"+e;else{var o=Nb(e,/^[\r\n\t ]+/);n=o&&o[0]}"application/xhtml+xml"===T&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var s=w?w.createHTML(e):e;if(he===pe)try{t=(new f).parseFromString(s,T)}catch(e){}if(!t||!t.documentElement){t=S.createDocument(he,"template",null);try{t.documentElement.innerHTML=be?"":s}catch(e){}}var a=t.body||t.documentElement;return e&&n&&a.insertBefore(r.createTextNode(n),a.childNodes[0]||null),he===pe?N.call(t,Q?"html":"body")[0]:Q?t.documentElement:a},De=function(e){return _.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},Pe=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore)},Le=function(e){return"object"===sb(i)?e instanceof i:e&&"object"===sb(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Me=function(e,t,o){O[e]&&kb(O[e],(function(e){e.call(n,t,o,Ce)}))},Ie=function(e){var t;if(Me("beforeSanitizeElements",e,null),Pe(e))return Oe(e),!0;if(Tb(/[\u0080-\uFFFF]/,e.nodeName))return Oe(e),!0;var o=B(e.nodeName);if(Me("uponSanitizeElement",e,{tagName:o,allowedTags:z}),e.hasChildNodes()&&!Le(e.firstElementChild)&&(!Le(e.content)||!Le(e.content.firstElementChild))&&Tb(/<[/\w]/g,e.innerHTML)&&Tb(/<[/\w]/g,e.textContent))return Oe(e),!0;if("select"===o&&Tb(/<template/i,e.innerHTML))return Oe(e),!0;if(!z[o]||q[o]){if(!q[o]&&Ue(o)){if($.tagNameCheck instanceof RegExp&&Tb($.tagNameCheck,o))return!1;if($.tagNameCheck instanceof Function&&$.tagNameCheck(o))return!1}if(re&&!ie[o]){var r=y(e)||e.parentNode,s=v(e)||e.childNodes;if(s&&r)for(var a=s.length-1;a>=0;--a)r.insertBefore(h(s[a],!0),b(e))}return Oe(e),!0}return e instanceof l&&!Ae(e)?(Oe(e),!0):"noscript"!==o&&"noembed"!==o||!Tb(/<\/no(script|embed)/i,e.innerHTML)?(X&&3===e.nodeType&&(t=e.textContent,t=Rb(t,D," "),t=Rb(t,P," "),e.textContent!==t&&(_b(n.removed,{element:e.cloneNode()}),e.textContent=t)),Me("afterSanitizeElements",e,null),!1):(Oe(e),!0)},Fe=function(e,t,n){if(oe&&("id"===t||"name"===t)&&(n in r||n in we))return!1;if(G&&!W[t]&&Tb(L,t));else if(K&&Tb(M,t));else if(!V[t]||W[t]){if(!(Ue(e)&&($.tagNameCheck instanceof RegExp&&Tb($.tagNameCheck,e)||$.tagNameCheck instanceof Function&&$.tagNameCheck(e))&&($.attributeNameCheck instanceof RegExp&&Tb($.attributeNameCheck,t)||$.attributeNameCheck instanceof Function&&$.attributeNameCheck(t))||"is"===t&&$.allowCustomizedBuiltInElements&&($.tagNameCheck instanceof RegExp&&Tb($.tagNameCheck,n)||$.tagNameCheck instanceof Function&&$.tagNameCheck(n))))return!1}else if(ue[t]);else if(Tb(U,Rb(n,F,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Ab(n,"data:")||!de[e])if(Y&&!Tb(I,Rb(n,F,"")));else if(n)return!1;return!0},Ue=function(e){return e.indexOf("-")>0},ze=function(e){var t,n,o,r;Me("beforeSanitizeAttributes",e,null);var s=e.attributes;if(s){var a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:V};for(r=s.length;r--;){var i=t=s[r],l=i.name,d=i.namespaceURI;n="value"===l?t.value:Ob(t.value),o=B(l);var c=n;if(a.attrName=o,a.attrValue=n,a.keepAttr=!0,a.forceKeepAttr=void 0,Me("uponSanitizeAttribute",e,a),n=a.attrValue,!a.forceKeepAttr)if(a.keepAttr)if(Tb(/\/>/i,n))Te(l,e);else{X&&(n=Rb(n,D," "),n=Rb(n,P," "));var u=B(e.nodeName);if(Fe(u,o,n)){if(n!==c)try{d?e.setAttributeNS(d,l,n):e.setAttribute(l,n)}catch(t){Te(l,e)}}else Te(l,e)}else Te(l,e)}Me("afterSanitizeAttributes",e,null)}},je=function e(t){var n,o=De(t);for(Me("beforeSanitizeShadowDOM",t,null);n=o.nextNode();)Me("uponSanitizeShadowNode",n,null),Ie(n)||(n.content instanceof s&&e(n.content),ze(n));Me("afterSanitizeShadowDOM",t,null)};return n.sanitize=function(e,r){var a,l,d,c,u;if((be=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Le(e)){if("function"!=typeof e.toString)throw Bb("toString is not a function");if("string"!=typeof(e=e.toString()))throw Bb("dirty is not a string, aborting")}if(!n.isSupported){if("object"===sb(t.toStaticHTML)||"function"==typeof t.toStaticHTML){if("string"==typeof e)return t.toStaticHTML(e);if(Le(e))return t.toStaticHTML(e.outerHTML)}return e}if(J||ke(r),n.removed=[],"string"==typeof e&&(se=!1),se){if(e.nodeName){var m=B(e.nodeName);if(!z[m]||q[m])throw Bb("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof i)1===(l=(a=Be("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===l.nodeName||"HTML"===l.nodeName?a=l:a.appendChild(l);else{if(!ee&&!X&&!Q&&-1===e.indexOf("<"))return w&&ne?w.createHTML(e):e;if(!(a=Be(e)))return ee?null:ne?x:""}a&&Z&&Oe(a.firstChild);for(var f=De(se?e:a);d=f.nextNode();)3===d.nodeType&&d===c||Ie(d)||(d.content instanceof s&&je(d.content),ze(d),c=d);if(c=null,se)return e;if(ee){if(te)for(u=E.call(a.ownerDocument);a.firstChild;)u.appendChild(a.firstChild);else u=a;return V.shadowroot&&(u=R.call(o,u,!0)),u}var g=Q?a.outerHTML:a.innerHTML;return Q&&z["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&Tb(tv,a.ownerDocument.doctype.name)&&(g="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+g),X&&(g=Rb(g,D," "),g=Rb(g,P," ")),w&&ne?w.createHTML(g):g},n.setConfig=function(e){ke(e),J=!0},n.clearConfig=function(){Ce=null,J=!1},n.isValidAttribute=function(e,t,n){Ce||ke({});var o=B(e),r=B(t);return Fe(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&(O[e]=O[e]||[],_b(O[e],t))},n.removeHook=function(e){if(O[e])return Sb(O[e])},n.removeHooks=function(e){O[e]&&(O[e]=[])},n.removeAllHooks=function(){O={}},n}();const sv=Bt.explode,av=()=>{const e={};return{addFilter:(t,n)=>{$(sv(t),(t=>{xe(e,t)||(e[t]={name:t,callbacks:[]}),e[t].callbacks.push(n)}))},getFilters:()=>Ce(e),removeFilter:(t,n)=>{$(sv(t),(t=>{if(xe(e,t))if(C(n)){const o=e[t],r=K(o.callbacks,(e=>e!==n));r.length>0?o.callbacks=r:delete e[t]}else delete e[t]}))}}},iv=(e,t,n)=>{const o=_s();t.convert_fonts_to_spans&&((e,t,n)=>{e.addNodeFilter("font",(e=>{$(e,(e=>{const o=t.parse(e.attr("style")),r=e.attr("color"),s=e.attr("face"),a=e.attr("size");r&&(o.color=r),s&&(o["font-family"]=s),a&&(o["font-size"]=n[parseInt(e.attr("size"),10)-1]),e.name="span",e.attr("style",t.serialize(o)),((e,t)=>{$(["color","face","size"],(t=>{e.attr(t,null)}))})(e)}))}))})(e,o,Bt.explode(t.font_size_legacy_values)),((e,t,n)=>{e.addNodeFilter("strike",(e=>{const o="html4"!==t.type;$(e,(e=>{if(o)e.name="s";else{const t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))}}))}))})(e,n,o)},lv=e=>{const[t,...n]=e.split(","),o=n.join(","),r=/data:([^/]+\/[^;]+)(;.+)?/.exec(t);if(r){const e=";base64"===r[2],t=e?(e=>{const t=/([a-z0-9+\/=\s]+)/i.exec(e);return t?t[1]:""})(o):decodeURIComponent(o);return M.some({type:r[1],data:t,base64Encoded:e})}return M.none()},dv=(e,t,n=!0)=>{let o=t;if(n)try{o=atob(t)}catch(e){return M.none()}const r=new Uint8Array(o.length);for(let e=0;e<r.length;e++)r[e]=o.charCodeAt(e);return M.some(new Blob([r],{type:e}))},cv=e=>new Promise(((t,n)=>{const o=new FileReader;o.onloadend=()=>{t(o.result)},o.onerror=()=>{n(o.error.message)},o.readAsDataURL(e)}));let uv=0;const mv=(e,t,n)=>lv(e).bind((({data:e,type:o,base64Encoded:r})=>{if(t&&!r)return M.none();{const t=r?e:btoa(e);return n(t,o)}})),fv=(e,t,n)=>{const o=e.create("blobid"+uv++,t,n);return e.add(o),o},gv=(e,t,n=!1)=>mv(t,n,((t,n)=>M.from(e.getByData(t,n)).orThunk((()=>dv(n,t).map((n=>fv(e,n,t))))))),pv=Bt.each,hv=Bt.trim,bv="source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),vv={ftp:21,http:80,https:443,mailto:25},yv=["img","video"],Cv=(e,t,n)=>{const o=(e=>{try{return decodeURIComponent(e)}catch(t){return unescape(e)}})(t);return!e.allow_script_urls&&(!!/((java|vb)script|mhtml):/i.test(o)||!e.allow_html_data_urls&&(/^data:image\//i.test(o)?((e,t)=>C(e)?!e:!C(t)||!j(yv,t))(e.allow_svg_data_urls,n)&&/^data:image\/svg\+xml/i.test(o):/^data:/i.test(o)))};class wv{constructor(e,t){e=hv(e),this.settings=t||{};const n=this.settings.base_uri,o=this;if(/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))return void(o.source=e);const r=0===e.indexOf("//");if(0!==e.indexOf("/")||r||(e=(n&&n.protocol||"http")+"://mce_host"+e),!/^[\w\-]*:?\/\//.test(e)){const t=this.settings.base_uri?this.settings.base_uri.path:new wv(document.location.href).directory;if(this.settings.base_uri&&""==this.settings.base_uri.protocol)e="//mce_host"+o.toAbsPath(t,e);else{const r=/([^#?]*)([#?]?.*)/.exec(e);e=(n&&n.protocol||"http")+"://mce_host"+o.toAbsPath(t,r[1])+r[2]}}e=e.replace(/@@/g,"(mce_at)");const s=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?(\[[a-zA-Z0-9:.%]+\]|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e);pv(bv,((e,t)=>{let n=s[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n})),n&&(o.protocol||(o.protocol=n.protocol),o.userInfo||(o.userInfo=n.userInfo),o.port||"mce_host"!==o.host||(o.port=n.port),o.host&&"mce_host"!==o.host||(o.host=n.host),o.source=""),r&&(o.protocol="")}static parseDataUri(e){let t;const n=decodeURIComponent(e).split(","),o=/data:([^;]+)/.exec(n[0]);return o&&(t=o[1]),{type:t,data:n[1]}}static isDomSafe(e,t,n={}){if(n.allow_script_urls)return!0;{const o=ms.decode(e).replace(/[\s\u0000-\u001F]+/g,"");return!Cv(n,o,t)}}static getDocumentBaseUrl(e){let t;return t=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?e.href:e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),t}setPath(e){const t=/^(.*?)\/?(\w+)?$/.exec(e);this.path=t[0],this.directory=t[1],this.file=t[2],this.source="",this.getURI()}toRelative(e){let t;if("./"===e)return e;const n=new wv(e,{base_uri:this});if("mce_host"!==n.host&&this.host!==n.host&&n.host||this.port!==n.port||this.protocol!==n.protocol&&""!==n.protocol)return n.getURI();const o=this.getURI(),r=n.getURI();return o===r||"/"===o.charAt(o.length-1)&&o.substr(0,o.length-1)===r?o:(t=this.toRelPath(this.path,n.path),n.query&&(t+="?"+n.query),n.anchor&&(t+="#"+n.anchor),t)}toAbsolute(e,t){const n=new wv(e,{base_uri:this});return n.getURI(t&&this.isSameOrigin(n))}isSameOrigin(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;const t=vv[this.protocol];if(t&&(this.port||t)==(e.port||t))return!0}return!1}toRelPath(e,t){let n,o,r=0,s="";const a=e.substring(0,e.lastIndexOf("/")).split("/"),i=t.split("/");if(a.length>=i.length)for(n=0,o=a.length;n<o;n++)if(n>=i.length||a[n]!==i[n]){r=n+1;break}if(a.length<i.length)for(n=0,o=i.length;n<o;n++)if(n>=a.length||a[n]!==i[n]){r=n+1;break}if(1===r)return t;for(n=0,o=a.length-(r-1);n<o;n++)s+="../";for(n=r-1,o=i.length;n<o;n++)s+=n!==r-1?"/"+i[n]:i[n];return s}toAbsPath(e,t){let n,o,r=0,s=[];const a=/\/$/.test(t)?"/":"";let i=e.split("/");const l=t.split("/");for(pv(i,(e=>{e&&s.push(e)})),i=s,n=l.length-1,s=[];n>=0;n--)0!==l[n].length&&"."!==l[n]&&(".."!==l[n]?r>0?r--:s.push(l[n]):r++);return n=i.length-r,o=n<=0?ne(s).join("/"):i.slice(0,n).join("/")+"/"+ne(s).join("/"),0!==o.indexOf("/")&&(o="/"+o),a&&o.lastIndexOf("/")!==o.length-1&&(o+=a),o}getURI(e=!1){let t;return this.source&&!e||(t="",e||(this.protocol?t+=this.protocol+"://":t+="//",this.userInfo&&(t+=this.userInfo+"@"),this.host&&(t+=this.host),this.port&&(t+=":"+this.port)),this.path&&(t+=this.path),this.query&&(t+="?"+this.query),this.anchor&&(t+="#"+this.anchor),this.source=t),this.source}}const xv=Bt.makeMap,kv=Bt.extend,Sv={IN_PLACE:!0,ALLOW_UNKNOWN_PROTOCOLS:!0,ALLOWED_TAGS:["#comment","#cdata-section","body"],ALLOWED_ATTR:[]},_v=Bt.makeMap("src,href,data,background,action,formaction,poster,xlink:href"),Ev="data-mce-type",Nv=(e,t)=>{const n=rv(),o=e.validate;let r=0;return n.addHook("uponSanitizeElement",((n,s)=>{var a,i;8===n.nodeType&&!e.allow_conditional_comments&&/^\[if/i.test(n.nodeValue)&&(n.nodeValue=" "+n.nodeValue);const l=s.tagName;if(1!==n.nodeType||"body"===l)return;const d=fn(n),c=Yt(d,Ev),u=Kt(d,"data-mce-bogus");if(!c&&m(u))return void("all"===u?no(d):oo(d));const f=t.getElementRule(l.toLowerCase());if(!o||f){if(s.allowedTags[l]=!0,o&&!c){if($(null!==(a=f.attributesForced)&&void 0!==a?a:[],(e=>{qt(d,e.name,"{$uid}"===e.value?"mce_"+r++:e.value)})),$(null!==(i=f.attributesDefault)&&void 0!==i?i:[],(e=>{Yt(d,e.name)||qt(d,e.name,"{$uid}"===e.value?"mce_"+r++:e.value)})),f.attributesRequired&&!V(f.attributesRequired,(e=>Yt(d,e))))return void oo(d);if(f.removeEmptyAttrs&&(e=>{const t=e.dom.attributes;return null==t||0===t.length})(d))return void oo(d);f.outputName&&f.outputName!==l.toLowerCase()&&((e,t)=>{const n=((e,t)=>{const n=un(t),o=Qt(e);return Wt(n,o),n})(e,t);Qn(e,n);const o=Rn(e);eo(n,o),no(e)})(d,f.outputName)}}else oo(d)})),n.addHook("uponSanitizeAttribute",((n,r)=>{const s=n.tagName.toLowerCase(),{attrName:a,attrValue:i}=r;r.keepAttr=!o||t.isValid(s,a)||ze(a,"data-")||ze(a,"aria-"),a in _v&&Cv(e,i,s)&&(r.keepAttr=!1),r.keepAttr?(r.allowedAttributes[a]=!0,a in t.getBoolAttrs()&&(r.attrValue=a),e.allow_svg_data_urls&&ze(i,"data:image/svg+xml")&&(r.forceKeepAttr=!0)):!n.hasAttribute(Ev)||"id"!==a&&"class"!==a&&"style"!==a||(r.forceKeepAttr=!0)})),n},Rv=(e,t,n)=>{const o=e.name,r=o in n&&"title"!==o&&"textarea"!==o,s=t.childNodes;for(let t=0,o=s.length;t<o;t++){const o=s[t],a=new Ff(o.nodeName.toLowerCase(),o.nodeType);if(Co(o)){const e=o.attributes;for(let t=0,n=e.length;t<n;t++){const n=e[t];a.attr(n.name,n.value)}}else Ro(o)?(a.value=o.data,r&&(a.raw=!0)):(To(o)||Ao(o)||Oo(o))&&(a.value=o.data);Rv(a,o,n),e.append(a)}},Av=(e={},t=Ss())=>{const n=av(),o=av(),r={validate:!0,root_name:"body",...e},s=new DOMParser,a=Nv(r,t),i=n.addFilter,l=n.getFilters,d=n.removeFilter,c=o.addFilter,u=o.getFilters,m=o.removeFilter,f={schema:t,addAttributeFilter:c,getAttributeFilters:u,removeAttributeFilter:m,addNodeFilter:i,getNodeFilters:l,removeNodeFilter:d,parse:(e,n={})=>{var o;const i=r.validate,d=null!==(o=n.context)&&void 0!==o?o:r.root_name,c=((e,n,o="html")=>{const i="xhtml"===o?"application/xhtml+xml":"text/html",l=xe(t.getSpecialElements(),n.toLowerCase()),d=l?`<${n}>${e}</${n}>`:e,c="xhtml"===o?`<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>${d}</body></html>`:`<body>${d}</body>`,u=s.parseFromString(c,i).body;return a.sanitize(u,((e,t)=>{const n={...Sv};return n.PARSER_MEDIA_TYPE=t,e.allow_script_urls?n.ALLOWED_URI_REGEXP=/.*/:e.allow_html_data_urls&&(n.ALLOWED_URI_REGEXP=/^(?!(\w+script|mhtml):)/i),n})(r,i)),a.removed=[],l?u.firstChild:u})(e,d,n.format),m=new Ff(d,11);Rv(m,c,t.getSpecialElements());const[f,g]=((e,t,n,o)=>{const r=n.validate,s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=kv(xv("script,style,head,html,body,title,meta,param"),t.getBlockElements()),l=xs(t),d=/[ \t\r\n]+/g,c=/^[ \t\r\n]+/,u=/[ \t\r\n]+$/,m=e=>{for(e=e.parent;C(e);){if(e.name in a)return!0;e=e.parent}return!1},f=(t,n)=>{const r=n?t.prev:t.next;return!C(r)&&t.parent.name in i&&(t.parent!==e||o.isRootContent)};return[e=>{if(3===e.type&&!m(e)){let t=e.value;t=t.replace(d," "),(((e,t)=>e&&(e.name in t||"br"===e.name))(e.prev,i)||f(e,!0))&&(t=t.replace(c,"")),0===t.length?e.remove():e.value=t}},e=>{var n;if(1===e.type){const n=t.getElementRule(e.name);if(r&&n){const r=qp(t,s,a,e);n.paddInEmptyBlock&&r&&(e=>{let n=e;for(;C(n);){if(n.name in l)return qp(t,s,a,n);n=n.parent}return!1})(e)?Hp(0,o,i,e):n.removeEmpty&&r?i[e.name]?e.remove():e.unwrap():n.paddEmpty&&(r||(e=>$p(e,"#text")&&e.firstChild.value===fr)(e))&&Hp(0,o,i,e)}}else if(3===e.type&&!m(e)){let t=e.value;(i[null===(n=e.next)||void 0===n?void 0:n.name]||f(e,!1))&&(t=t.replace(u,"")),0===t.length?e.remove():e.value=t}}]})(m,t,r,n),p=[],h=i?e=>((e,n)=>{const o=e.parent;o&&t.children[e.name]&&!t.isValidChild(o.name,e.name)&&n.push(e)})(e,p):S,b={nodes:{},attributes:{}},v=e=>zp(l(),u(),e,b);if(((e,t,n)=>{const o=[];for(let n=e,r=n;C(n);r=n,n=n.walk())$(t,(e=>e(n))),y(n.parent)&&n!==e?n=r:o.push(n);for(let e=o.length-1;e>=0;e--){const t=o[e];$(n,(e=>e(t)))}})(m,[f,v],[g,h]),p.reverse(),i&&p.length>0)if(n.context){const{pass:e,fail:o}=W(p,(e=>e.parent===m));Kp(o,t,v),n.invalid=e.length>0}else Kp(p,t,v);const w=((e,t)=>{var n;const o=null!==(n=t.forced_root_block)&&void 0!==n?n:e.forced_root_block;return!1===o?"":!0===o?"p":o})(r,n);return w&&("body"===m.name||n.isRootContent)&&((e,n)=>{const o=kv(xv("script,style,head,html,body,title,meta,param"),t.getBlockElements()),s=/^[ \t\r\n]+/,a=/[ \t\r\n]+$/;let i=e.firstChild,l=null;const d=e=>{e&&(i=e.firstChild,i&&3===i.type&&(i.value=i.value.replace(s,"")),i=e.lastChild,i&&3===i.type&&(i.value=i.value.replace(a,"")))};if(t.isValidChild(e.name,n.toLowerCase())){for(;i;){const t=i.next;3===i.type||1===i.type&&"p"!==i.name&&!o[i.name]&&!i.attr(Ev)?(l||(l=new Ff(n,1),l.attr(r.forced_root_block_attrs),e.insert(l,i)),l.append(i)):(d(l),l=null),i=t}d(l)}})(m,w),n.invalid||jp(b,n),m}};return((e,t)=>{const n=e.schema;t.remove_trailing_brs&&e.addNodeFilter("br",((e,t,o)=>{const r=Bt.extend({},n.getBlockElements()),s=n.getNonEmptyElements(),a=n.getWhitespaceElements();r.body=1;for(let t=0,i=e.length;t<i;t++){let i=e[t],l=i.parent;if(r[i.parent.name]&&i===l.lastChild){let e=i.prev;for(;e;){const t=e.name;if("span"!==t||"bookmark"!==e.attr("data-mce-type")){"br"===t&&(i=null);break}e=e.prev}if(i&&(i.remove(),qp(n,s,a,l))){const e=n.getElementRule(l.name);e&&(e.removeEmpty?l.remove():e.paddEmpty&&Hp(0,o,r,l))}}else{let e=i;for(;l&&l.firstChild===e&&l.lastChild===e&&(e=l,!r[l.name]);)l=l.parent;if(e===l){const e=new Ff("#text",3);e.value=fr,i.replace(e)}}}})),e.addAttributeFilter("href",(e=>{let n=e.length;const o=e=>{const t=e?Bt.trim(e):"";return/\b(noopener)\b/g.test(t)?t:(e=>e.split(" ").filter((e=>e.length>0)).concat(["noopener"]).sort().join(" "))(t)};if(!t.allow_unsafe_link_target)for(;n--;){const t=e[n];"a"===t.name&&"_blank"===t.attr("target")&&t.attr("rel",o(t.attr("rel")))}})),t.allow_html_in_named_anchor||e.addAttributeFilter("id,name",(e=>{let t,n,o,r,s=e.length;for(;s--;)if(r=e[s],"a"===r.name&&r.firstChild&&!r.attr("href")){o=r.parent,t=r.lastChild;do{n=t.prev,o.insert(t,r),t=n}while(t)}})),t.fix_list_elements&&e.addNodeFilter("ul,ol",(e=>{let t,n,o=e.length;for(;o--;)if(t=e[o],n=t.parent,"ul"===n.name||"ol"===n.name)if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{const e=new Ff("li",1);e.attr("style","list-style-type: none"),t.wrap(e)}})),t.validate&&n.getValidClasses()&&e.addAttributeFilter("class",(e=>{const t=n.getValidClasses();let o=e.length;for(;o--;){const n=e[o],r=n.attr("class").split(" ");let s="";for(let e=0;e<r.length;e++){const o=r[e];let a=!1,i=t["*"];i&&i[o]&&(a=!0),i=t[n.name],!a&&i&&i[o]&&(a=!0),a&&(s&&(s+=" "),s+=o)}s.length||(s=null),n.attr("class",s)}})),((e,t)=>{const{blob_cache:n}=t,o=e=>{const t=e.attr("src");(e=>e.attr("src")===Nt.transparentSrc||C(e.attr("data-mce-placeholder")))(e)||(e=>C(e.attr("data-mce-bogus")))(e)||gv(n,t,!0).each((t=>{e.attr("src",t.blobUri())}))};n&&e.addAttributeFilter("src",(e=>$(e,o)))})(e,t)})(f,r),((e,t,n)=>{t.inline_styles&&iv(e,t,n)})(f,r,t),f},Ov=(e,t)=>{const n=(e=>oh(e)?Kf({validate:!1}).serialize(e):e)(e),o=t(n);if(o.isDefaultPrevented())return o;if(oh(e)){if(o.content!==n){const t=Av({validate:!1,forced_root_block:!1}).parse(o.content,{context:e.name});return{...o,content:t}}return{...o,content:e}}return o},Tv=(e,t)=>{if(t.no_events)return yi.value(t);{const n=((e,t)=>e.dispatch("BeforeGetContent",t))(e,t);return n.isDefaultPrevented()?yi.error(Ju(e,{content:"",...n}).content):yi.value(n)}},Bv=(e,t,n)=>n.no_events?t:Ov(t,(t=>Ju(e,{...n,content:t}))).content,Dv=(e,t)=>{if(t.no_events)return yi.value(t);{const n=Ov(t.content,(n=>((e,t)=>e.dispatch("BeforeSetContent",t))(e,{...t,content:n})));return n.isDefaultPrevented()?(Qu(e,n),yi.error(void 0)):yi.value(n)}},Pv=(e,t,n)=>{n.no_events||Qu(e,{...n,content:t})},Lv=(e,t,n)=>({element:e,width:t,rows:n}),Mv=(e,t)=>({element:e,cells:t}),Iv=(e,t)=>({x:e,y:t}),Fv=(e,t)=>{const n=parseInt(Kt(e,t),10);return isNaN(n)?1:n},Uv=(e,t,n)=>{const o=e.rows;return!!(o[n]?o[n].cells:[])[t]},zv=e=>Y(e,((e,t)=>t.cells.length>e?t.cells.length:e),0),jv=(e,t)=>{const n=e.rows;for(let e=0;e<n.length;e++){const o=n[e].cells;for(let n=0;n<o.length;n++)if(vn(o[n],t))return M.some(Iv(n,e))}return M.none()},Vv=(e,t,n,o,r)=>{const s=[],a=e.rows;for(let e=n;e<=r;e++){const n=a[e].cells,r=t<o?n.slice(t,o+1):n.slice(o,t+1);s.push(Mv(a[e].element,r))}return s},Hv=e=>((e,t)=>{const n=ba(e.element),o=un("tbody");return eo(o,t),Zn(n,o),n})(e,(e=>H(e.rows,(e=>{const t=H(e.cells,(e=>{const t=va(e);return Xt(t,"colspan"),Xt(t,"rowspan"),t})),n=ba(e.element);return eo(n,t),n})))(e)),$v=(e,t)=>{const n=fn(t.commonAncestorContainer),o=hg(n,e),r=K(o,cr),s=((e,t)=>Q(e,(e=>"li"===Mt(e)&&Du(e,t))).fold(N([]),(t=>(e=>Q(e,(e=>"ul"===Mt(e)||"ol"===Mt(e))))(e).map((e=>{const t=un(Mt(e)),n=ve(Gn(e),((e,t)=>ze(t,"list-style")));return $n(t,n),[un("li"),t]})).getOr([]))))(o,t),a=r.concat(s.length?s:(e=>sr(e)?kn(e).filter(rr).fold(N([]),(t=>[e,t])):rr(e)?[e]:[])(n));return H(a,ba)},qv=()=>ym([]),Wv=(e,t)=>((e,t)=>Wo(t,"table",O(vn,e)))(e,t[0]).bind((e=>{const n=t[0],o=t[t.length-1],r=(e=>{const t=Lv(ba(e),0,[]);return $(na(e,"tr"),((e,n)=>{$(na(e,"td,th"),((o,r)=>{((e,t,n,o,r)=>{const s=Fv(r,"rowspan"),a=Fv(r,"colspan"),i=e.rows;for(let e=n;e<n+s;e++){i[e]||(i[e]=Mv(va(o),[]));for(let o=t;o<t+a;o++)i[e].cells[o]=e===n&&o===t?r:ba(r)}})(t,((e,t,n)=>{for(;Uv(e,t,n);)t++;return t})(t,r,n),n,e,o)}))})),Lv(t.element,zv(t.rows),t.rows)})(e);return((e,t,n)=>jv(e,t).bind((t=>jv(e,n).map((n=>((e,t,n)=>{const o=t.x,r=t.y,s=n.x,a=n.y,i=r<a?Vv(e,o,r,s,a):Vv(e,o,a,s,r);return Lv(e.element,zv(i),i)})(e,t,n))))))(r,n,o).map((e=>ym([Hv(e)])))})).getOrThunk(qv),Kv=(e,t)=>{const n=Ru(t,e);return n.length>0?Wv(e,n):((e,t)=>t.length>0&&t[0].collapsed?qv():((e,t)=>((e,t)=>{const n=Y(t,((e,t)=>(Zn(t,e),t)),e);return t.length>0?ym([n]):n})(fn(t.cloneContents()),$v(e,t)))(e,t[0]))(e,t)},Gv=(e,t)=>t>=0&&t<e.length&&zc(e.charAt(t)),Yv=e=>br(e.innerText),Xv=e=>Co(e)?e.outerHTML:Ro(e)?ms.encodeRaw(e.data,!1):To(e)?"\x3c!--"+e.data+"--\x3e":"",Qv=(e,t)=>(((e,t)=>{let n=0;$(e,(e=>{0===e[0]?n++:1===e[0]?(((e,t,n)=>{const o=(e=>{let t;const n=document.createElement("div"),o=document.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)o.appendChild(t);return o})(t);if(e.hasChildNodes()&&n<e.childNodes.length){const t=e.childNodes[n];t.parentNode.insertBefore(o,t)}else e.appendChild(o)})(t,e[1],n),n++):2===e[0]&&((e,t)=>{if(e.hasChildNodes()&&t<e.childNodes.length){const n=e.childNodes[t];n.parentNode.removeChild(n)}})(t,n)}))})(((e,t)=>{const n=e.length+t.length+2,o=new Array(n),r=new Array(n),s=(n,o,r,a,l)=>{const d=i(n,o,r,a);if(null===d||d.start===o&&d.diag===o-a||d.end===n&&d.diag===n-r){let s=n,i=r;for(;s<o||i<a;)s<o&&i<a&&e[s]===t[i]?(l.push([0,e[s]]),++s,++i):o-n>a-r?(l.push([2,e[s]]),++s):(l.push([1,t[i]]),++i)}else{s(n,d.start,r,d.start-d.diag,l);for(let t=d.start;t<d.end;++t)l.push([0,e[t]]);s(d.end,o,d.end-d.diag,a,l)}},a=(n,o,r,s)=>{let a=n;for(;a-o<s&&a<r&&e[a]===t[a-o];)++a;return((e,t,n)=>({start:e,end:t,diag:n}))(n,a,o)},i=(n,s,i,l)=>{const d=s-n,c=l-i;if(0===d||0===c)return null;const u=d-c,m=c+d,f=(m%2==0?m:m+1)/2;let g,p,h,b,v;for(o[1+f]=n,r[1+f]=s+1,g=0;g<=f;++g){for(p=-g;p<=g;p+=2){for(h=p+f,p===-g||p!==g&&o[h-1]<o[h+1]?o[h]=o[h+1]:o[h]=o[h-1]+1,b=o[h],v=b-n+i-p;b<s&&v<l&&e[b]===t[v];)o[h]=++b,++v;if(u%2!=0&&u-g<=p&&p<=u+g&&r[h-u]<=o[h])return a(r[h-u],p+n-i,s,l)}for(p=u-g;p<=u+g;p+=2){for(h=p+f-u,p===u-g||p!==u+g&&r[h+1]<=r[h-1]?r[h]=r[h+1]-1:r[h]=r[h-1],b=r[h]-1,v=b-n+i-p;b>=n&&v>=i&&e[b]===t[v];)r[h]=b--,v--;if(u%2==0&&-g<=p&&p<=g&&r[h]<=o[h+u])return a(r[h],p+n-i,s,l)}}},l=[];return s(0,e.length,0,t.length,l),l})(H(de(t.childNodes),Xv),e),t),t),Jv=De((()=>document.implementation.createHTMLDocument("undo"))),Zv=e=>{const t=(n=e.getBody(),K(H(de(n.childNodes),Xv),(e=>e.length>0)));var n;const o=ee(t,(t=>{const n=jf(e.serializer,t);return n.length>0?[n]:[]})),r=o.join("");return(e=>-1!==e.indexOf("</iframe>"))(r)?(e=>({type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}))(o):(e=>({type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}))(r)},ey=(e,t,n)=>{const o=n?t.beforeBookmark:t.bookmark;"fragmented"===t.type?Qv(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw",no_selection:!C(o)||!Rc(o)||!o.isFakeCaret}),e.selection.moveToBookmark(o)},ty=e=>"fragmented"===e.type?e.fragments.join(""):e.content,ny=e=>{const t=un("body",Jv());return so(t,ty(e)),$(na(t,"*[data-mce-bogus]"),oo),ro(t)},oy=(e,t)=>!(!e||!t)&&(!!((e,t)=>ty(e)===ty(t))(e,t)||((e,t)=>ny(e)===ny(t))(e,t)),ry=e=>0===e.get(),sy=(e,t,n)=>{ry(n)&&(e.typing=t)},ay=(e,t)=>{e.typing&&(sy(e,!1,t),e.add())},iy=e=>({init:{bindEvents:S},undoManager:{beforeChange:(t,n)=>((e,t,n)=>{ry(t)&&n.set(hi(e.selection))})(e,t,n),add:(t,n,o,r,s,a)=>((e,t,n,o,r,s,a)=>{const i=Zv(e);if(s=s||{},s=Bt.extend(s,i),!1===ry(o)||e.removed)return null;const l=t.data[n.get()];if(e.dispatch("BeforeAddUndo",{level:s,lastLevel:l,originalEvent:a}).isDefaultPrevented())return null;if(l&&oy(l,s))return null;t.data[n.get()]&&r.get().each((e=>{t.data[n.get()].beforeBookmark=e}));const d=Dl(e);if(d&&t.data.length>d){for(let e=0;e<t.data.length-1;e++)t.data[e]=t.data[e+1];t.data.length--,n.set(t.data.length)}s.bookmark=hi(e.selection),n.get()<t.data.length-1&&(t.data.length=n.get()+1),t.data.push(s),n.set(t.data.length-1);const c={level:s,lastLevel:l,originalEvent:a};return n.get()>0?(e.setDirty(!0),e.dispatch("AddUndo",c),e.dispatch("change",c)):e.dispatch("AddUndo",c),s})(e,t,n,o,r,s,a),undo:(t,n,o)=>((e,t,n,o)=>{let r;return t.typing&&(t.add(),t.typing=!1,sy(t,!1,n)),o.get()>0&&(o.set(o.get()-1),r=t.data[o.get()],ey(e,r,!0),e.setDirty(!0),e.dispatch("Undo",{level:r})),r})(e,t,n,o),redo:(t,n)=>((e,t,n)=>{let o;return t.get()<n.length-1&&(t.set(t.get()+1),o=n[t.get()],ey(e,o,!1),e.setDirty(!0),e.dispatch("Redo",{level:o})),o})(e,t,n),clear:(t,n)=>((e,t,n)=>{t.data=[],n.set(0),t.typing=!1,e.dispatch("ClearUndos")})(e,t,n),reset:e=>(e=>{e.clear(),e.add()})(e),hasUndo:(t,n)=>((e,t,n)=>n.get()>0||t.typing&&t.data[0]&&!oy(Zv(e),t.data[0]))(e,t,n),hasRedo:(e,t)=>((e,t)=>t.get()<e.data.length-1&&!e.typing)(e,t),transact:(e,t,n)=>((e,t,n)=>(ay(e,t),e.beforeChange(),e.ignore(n),e.add()))(e,t,n),ignore:(e,t)=>((e,t)=>{try{e.set(e.get()+1),t()}finally{e.set(e.get()-1)}})(e,t),extra:(t,n,o,r)=>((e,t,n,o,r)=>{if(t.transact(o)){const o=t.data[n.get()].bookmark,s=t.data[n.get()-1];ey(e,s,!0),t.transact(r)&&(t.data[n.get()-1].beforeBookmark=o)}})(e,t,n,o,r)},formatter:{match:(t,n,o,r)=>gh(e,t,n,o,r),matchAll:(t,n)=>((e,t,n)=>{const o=[],r={},s=e.selection.getStart();return e.dom.getParent(s,(s=>{for(let a=0;a<t.length;a++){const i=t[a];!r[i]&&fh(e,s,i,n)&&(r[i]=!0,o.push(i))}}),e.dom.getRoot()),o})(e,t,n),matchNode:(t,n,o,r)=>fh(e,t,n,o,r),canApply:t=>((e,t)=>{const n=e.formatter.get(t),o=e.dom;if(n){const t=e.selection.getStart(),r=au(o,t);for(let e=n.length-1;e>=0;e--){const t=n[e];if(!lu(t))return!0;for(let e=r.length-1;e>=0;e--)if(o.is(r[e],t.selector))return!0}}return!1})(e,t),closest:t=>((e,t)=>{const n=t=>vn(t,fn(e.getBody()));return M.from(e.selection.getStart(!0)).bind((o=>ih(fn(o),(n=>ce(t,(t=>((t,n)=>fh(e,t.dom,n)?M.some(n):M.none())(n,t)))),n))).getOrNull()})(e,t),apply:(t,n,o)=>Zh(e,t,n,o),remove:(t,n,o,r)=>Yh(e,t,n,o,r),toggle:(t,n,o)=>((e,t,n,o)=>{const r=e.formatter.get(t);!gh(e,t,n,o)||"toggle"in r[0]&&!r[0].toggle?Zh(e,t,n,o):Yh(e,t,n,o)})(e,t,n,o),formatChanged:(t,n,o,r,s)=>((e,t,n,o,r,s)=>(null===t.get()&&((e,t)=>{e.set({}),t.on("NodeChange",(n=>{rb(t,n.element,e.get())})),t.on("FormatApply FormatRemove",(n=>{const o=M.from(n.node).map((e=>Yc(e)?e:e.startContainer)).bind((e=>Co(e)?M.some(e):M.from(e.parentElement))).getOrThunk((()=>tb(t)));rb(t,o,e.get())}))})(t,e),((e,t,n,o,r,s)=>{const a=t.get();$(n.split(","),(t=>{const n=we(a,t).getOrThunk((()=>{const e={withSimilar:{state:Ws(!1),similar:!0,callbacks:[]},withoutSimilar:{state:Ws(!1),similar:!1,callbacks:[]},withVars:[]};return a[t]=e,e})),i=()=>{const n=ob(e);return nb(e,n,t,r,s).isSome()};if(v(s)){const e=r?n.withSimilar:n.withoutSimilar;e.callbacks.push(o),1===e.callbacks.length&&e.state.set(i())}else n.withVars.push({state:Ws(i()),similar:r,vars:s,callback:o})})),t.set(a)})(e,t,n,o,r,s),{unbind:()=>((e,t,n)=>{const o=e.get();$(t.split(","),(e=>we(o,e).each((t=>{o[e]={withSimilar:{...t.withSimilar,callbacks:K(t.withSimilar.callbacks,(e=>e!==n))},withoutSimilar:{...t.withoutSimilar,callbacks:K(t.withoutSimilar.callbacks,(e=>e!==n))},withVars:K(t.withVars,(e=>e.callback!==n))}})))),e.set(o)})(t,n,o)}))(e,t,n,o,r,s)},editor:{getContent:t=>((e,t)=>M.from(e.getBody()).fold(N("tree"===t.format?new Ff("body",11):""),(n=>((e,t,n)=>{let o;return"raw"===t.format?o=Bt.trim(Vf(e.serializer,n.innerHTML)):"text"===t.format?(o=br(n.innerText),o="\n"===o?"":o):o="tree"===t.format?e.serializer.serialize(n,t):((e,t)=>{const n=ji(e),o=new RegExp(`^(<${n}[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/${n}>[\r\n]*|<br \\/>[\r\n]*)$`);return t.replace(o,"")})(e,e.serializer.serialize(n,t)),"text"!==t.format&&!lr(fn(n))&&m(o)?Bt.trim(o):o})(e,t,n))))(e,t),setContent:(t,n)=>((e,t,n)=>M.from(e.getBody()).map((o=>oh(t)?((e,t,n,o)=>{Vp(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);const r=Kf({validate:!1},e.schema).serialize(n),s=lr(fn(t))?r:Bt.trim(r);return rh(e,s,o.no_selection),{content:n,html:s}})(e,o,t,n):((e,t,n,o)=>{if(0===n.length||/^\s+$/.test(n)){const r='<br data-mce-bogus="1">';"TABLE"===t.nodeName?n="<tr><td>"+r+"</td></tr>":/^(UL|OL)$/.test(t.nodeName)&&(n="<li>"+r+"</li>");const s=ji(e);return e.schema.isValidChild(t.nodeName.toLowerCase(),s.toLowerCase())?(n=r,n=e.dom.createHTML(s,Vi(e),n)):n||(n=r),rh(e,n,o.no_selection),{content:n,html:n}}{"raw"!==o.format&&(n=Kf({validate:!1},e.schema).serialize(e.parser.parse(n,{isRootContent:!0,insert:!0})));const r=lr(fn(t))?n:Bt.trim(n);return rh(e,r,o.no_selection),{content:r,html:r}}})(e,o,t,n))).getOr({content:t,html:oh(n.content)?"":n.content}))(e,t,n),insertContent:(t,n)=>nh(e,t,n),addVisual:t=>((e,t)=>{const n=e.dom,o=C(t)?t:e.getBody();v(e.hasVisual)&&(e.hasVisual=zl(e)),$(n.select("table,a",o),(t=>{switch(t.nodeName){case"TABLE":const o=jl(e),r=n.getAttrib(t,"border");r&&"0"!==r||!e.hasVisual?n.removeClass(t,o):n.addClass(t,o);break;case"A":if(!n.getAttrib(t,"href")){const o=n.getAttrib(t,"name")||t.id,r=Vl(e);o&&e.hasVisual?n.addClass(t,r):n.removeClass(t,r)}}})),e.dispatch("VisualAid",{element:t,hasVisual:e.hasVisual})})(e,t)},selection:{getContent:(t,n)=>((e,t,n={})=>{const o=((e,t)=>({...e,format:t,get:!0,selection:!0,getInner:!0}))(n,t);return Tv(e,o).fold(R,(t=>{const n=((e,t)=>{if("text"===t.format)return(e=>M.from(e.selection.getRng()).map((t=>{const n=M.from(e.dom.getParent(t.commonAncestorContainer,e.dom.isBlock)),o=e.getBody(),r=(e=>e.map((e=>e.nodeName)).getOr("div").toLowerCase())(n),s=e.dom.add(o,r,{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},t.cloneContents()),a=Yv(s),i=br(s.textContent);if(e.dom.remove(s),Gv(i,0)||Gv(i,i.length-1)){const e=n.getOr(o),t=Yv(e),r=t.indexOf(a);return-1===r?a:(Gv(t,r-1)?" ":"")+a+(Gv(t,r+a.length)?" ":"")}return a})).getOr(""))(e);{const n=((e,t)=>{const n=e.selection.getRng(),o=e.dom.create("body"),r=e.selection.getSel(),s=Rf(e,Nu(r)),a=t.contextual?Kv(fn(e.getBody()),s).dom:n.cloneContents();return a&&o.appendChild(a),e.selection.serializer.serialize(o,t)})(e,t);return"tree"===t.format?n:e.selection.isCollapsed()?"":n}})(e,t);return Bv(e,n,t)}))})(e,t,n)},autocompleter:{addDecoration:t=>Bf(e,t),removeDecoration:()=>((e,t)=>Df(t).each((t=>{const n=e.selection.getBookmark();oo(t),e.selection.moveToBookmark(n)})))(e,fn(e.getBody()))},raw:{getModel:()=>M.none()}}),ly=e=>xe(e.plugins,"rtc"),dy=e=>e.rtcInstance?e.rtcInstance:iy(e),cy=e=>{const t=e.rtcInstance;if(t)return t;throw new Error("Failed to get RTC instance not yet initialized.")},uy=e=>cy(e).init.bindEvents(),my=e=>0===e.dom.length?(no(e),M.none()):M.some(e),fy=(e,t,n,o)=>{e.bind((e=>((o?$g:Hg)(e.dom,o?e.dom.length:0),t.filter(zt).map((t=>((e,t,n,o)=>{const r=e.dom,s=t.dom,a=o?r.length:s.length;o?(qg(r,s,!1,!o),n.setStart(s,a)):(qg(s,r,!1,!o),n.setEnd(s,a))})(e,t,n,o)))))).orThunk((()=>{const e=((e,t)=>e.filter((e=>Wu.isBookmarkNode(e.dom))).bind(t?_n:Sn))(t,o).or(t).filter(zt);return e.map((e=>((e,t)=>{kn(e).each((n=>{const o=e.dom;t&&Mg(n,Ya(o,0))?Hg(o,0):!t&&Ig(n,Ya(o,o.length))&&$g(o,o.length)}))})(e,o)))}))},gy=(e,t,n)=>{if(e&&xe(e,t)){const o=K(e[t],(e=>e!==n));0===o.length?delete e[t]:e[t]=o}},py=e=>!(!e||!e.ownerDocument)&&yn(fn(e.ownerDocument),fn(e)),hy=(e,t,n,o)=>{let r,s;const{selectorChangedWithUnbind:a}=((e,t)=>{let n,o;const r=(t,n)=>Q(n,(n=>e.is(n,t))),s=t=>e.getParents(t,null,e.getRoot());return{selectorChangedWithUnbind:(e,a)=>(n||(n={},o={},t.on("NodeChange",(e=>{const t=e.element,a=s(t),i={};Bt.each(n,((e,t)=>{r(t,a).each((n=>{o[t]||($(e,(e=>{e(!0,{node:n,selector:t,parents:a})})),o[t]=e),i[t]=e}))})),Bt.each(o,((e,n)=>{i[n]||(delete o[n],Bt.each(e,(e=>{e(!1,{node:t,selector:n,parents:a})})))}))}))),n[e]||(n[e]=[]),n[e].push(a),r(e,s(t.selection.getStart())).each((()=>{o[e]=n[e]})),{unbind:()=>{gy(n,e,a),gy(o,e,a)}})}})(e,o),i=(e,t)=>((e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,selection:!0,content:t}))(n,t);Dv(e,o).each((t=>{const n=((e,t)=>{if("raw"!==t.format){const n=e.selection.getRng(),o=e.dom.getParent(n.commonAncestorContainer,e.dom.isBlock),r=o?{context:o.nodeName.toLowerCase()}:{},s=e.parser.parse(t.content,{forced_root_block:!1,...r,...t});return Kf({validate:!1},e.schema).serialize(s)}return t.content})(e,t),o=e.selection.getRng();((e,t)=>{const n=M.from(t.firstChild).map(fn),o=M.from(t.lastChild).map(fn);e.deleteContents(),e.insertNode(t);const r=n.bind(Sn).filter(zt).bind(my),s=o.bind(_n).filter(zt).bind(my);fy(r,n,e,!0),fy(s,o,e,!1),e.collapse(!1)})(o,o.createContextualFragment(n)),e.selection.setRng(o),ef(e,o),Pv(e,n,t)}))})(o,e,t),l=e=>{const t=c();t.collapse(!!e),u(t)},d=()=>t.getSelection?t.getSelection():t.document.selection,c=()=>{let n,a,i;const l=(e,t,n)=>{try{return t.compareBoundaryPoints(e,n)}catch(e){return-1}},c=t.document;if(void 0!==o.bookmark&&!1===xf(o)){const e=mf(o);if(e.isSome())return e.map((e=>Rf(o,[e])[0])).getOr(c.createRange())}try{(n=d())&&!yo(n.anchorNode)&&(a=n.rangeCount>0?n.getRangeAt(0):n.createRange?n.createRange():c.createRange(),a=Rf(o,[a])[0])}catch(e){}return a||(a=c.createRange()),a.setStart&&9===a.startContainer.nodeType&&a.collapsed&&(i=e.getRoot(),a.setStart(i,0),a.setEnd(i,0)),r&&s&&(0===l(a.START_TO_START,a,r)&&0===l(a.END_TO_END,a,r)?a=s:(r=null,s=null)),a},u=(e,t)=>{let n;if(!(e=>!!e&&py(e.startContainer)&&py(e.endContainer))(e))return;const a=d();if(e=o.dispatch("SetSelectionRange",{range:e,forward:t}).range,a){s=e;try{a.removeAllRanges(),a.addRange(e)}catch(e){}!1===t&&a.extend&&(a.collapse(e.endContainer,e.endOffset),a.extend(e.startContainer,e.startOffset)),r=a.rangeCount>0?a.getRangeAt(0):null}!e.collapsed&&e.startContainer===e.endContainer&&a.setBaseAndExtent&&e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()&&(n=e.startContainer.childNodes[e.startOffset],n&&"IMG"===n.tagName&&(a.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),a.anchorNode===e.startContainer&&a.focusNode===e.endContainer||a.setBaseAndExtent(n,0,n,1))),o.dispatch("AfterSetSelectionRange",{range:e,forward:t})},m=()=>{const t=d(),n=null==t?void 0:t.anchorNode,o=null==t?void 0:t.focusNode;if(!t||!n||!o||yo(n)||yo(o))return!0;const r=e.createRng(),s=e.createRng();try{r.setStart(n,t.anchorOffset),r.collapse(!0),s.setStart(o,t.focusOffset),s.collapse(!0)}catch(e){return!0}return r.compareBoundaryPoints(r.START_TO_START,s)<=0},f={bookmarkManager:null,controlSelection:null,dom:e,win:t,serializer:n,editor:o,collapse:l,setCursorLocation:(t,n)=>{const r=e.createRng();C(t)&&C(n)?(r.setStart(t,n),r.setEnd(t,n),u(r),l(!1)):(Pu(e,r,o.getBody(),!0),u(r))},getContent:e=>((e,t={})=>((e,t,n)=>cy(e).selection.getContent(t,n))(e,t.format?t.format:"html",t))(o,e),setContent:i,getBookmark:(e,t)=>g.getBookmark(e,t),moveToBookmark:e=>g.moveToBookmark(e),select:(t,n)=>(((e,t,n)=>M.from(t).map((t=>{const o=e.nodeIndex(t),r=e.createRng();return r.setStart(t.parentNode,o),r.setEnd(t.parentNode,o+1),n&&(Pu(e,r,t,!0),Pu(e,r,t,!1)),r})))(e,t,n).each(u),t),isCollapsed:()=>{const e=c(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isForward:m,setNode:t=>(i(e.getOuterHTML(t)),t),getNode:()=>((e,t)=>{let n,o,r;if(!t)return e;o=t.startContainer,r=t.endContainer;const s=t.startOffset,a=t.endOffset;return n=t.commonAncestorContainer,!t.collapsed&&(o===r&&a-s<2&&o.hasChildNodes()&&(n=o.childNodes[s]),3===o.nodeType&&3===r.nodeType&&(o=o.length===s?Nf(o.nextSibling,!0):o.parentNode,r=0===a?Nf(r.previousSibling,!1):r.parentNode,o&&o===r))?o:n&&3===n.nodeType?n.parentNode:n})(o.getBody(),c()),getSel:d,setRng:u,getRng:c,getStart:e=>_f(o.getBody(),c(),e),getEnd:e=>Ef(o.getBody(),c(),e),getSelectedBlocks:(t,n)=>((e,t,n,o)=>{let r;const s=[],a=e.getRoot();if(n=e.getParent(n||_f(a,t,t.collapsed),e.isBlock),o=e.getParent(o||Ef(a,t,t.collapsed),e.isBlock),n&&n!==a&&s.push(n),n&&o&&n!==o){r=n;const t=new Qo(n,a);for(;(r=t.next())&&r!==o;)e.isBlock(r)&&s.push(r)}return o&&n!==o&&o!==a&&s.push(o),s})(e,c(),t,n),normalize:()=>{const t=c(),n=d();if(!(Nu(n).length>1)&&Lu(o)){const n=Tm(e,t);return n.each((e=>{u(e,m())})),n.getOr(t)}return t},selectorChanged:(e,t)=>(a(e,t),f),selectorChangedWithUnbind:a,getScrollContainer:()=>{let t,n=e.getRoot();for(;n&&"BODY"!==n.nodeName;){if(n.scrollHeight>n.clientHeight){t=n;break}n=n.parentNode}return t},scrollIntoView:(e,t)=>{C(e)?((e,t,n)=>{(e.inline?Qm:Zm)(e,t,n)})(o,e,t):ef(o,c(),t)},placeCaretAt:(e,t)=>u(wm(e,t,o.getDoc())),getBoundingClientRect:()=>{const e=c();return e.collapsed?Ya.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:()=>{t=r=s=null,p.destroy()}},g=Wu(f),p=sm(f,o);return f.bookmarkManager=g,f.controlSelection=p,f},by=(e,t,n)=>{-1===Bt.inArray(t,n)&&(e.addAttributeFilter(n,((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.push(n))},vy=(e,t)=>{const n=["data-mce-selected"],o=t&&t.dom?t.dom:Hs.DOM,r=t&&t.schema?t.schema:Ss(e);e.entity_encoding=e.entity_encoding||"named",e.remove_trailing_brs=!("remove_trailing_brs"in e)||e.remove_trailing_brs;const s=Av(e,r);return((e,t,n)=>{e.addAttributeFilter("data-mce-tabindex",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];o.attr("tabindex",o.attr("data-mce-tabindex")),o.attr(t,null)}})),e.addAttributeFilter("src,href,style",((e,o)=>{const r="data-mce-"+o,s=t.url_converter,a=t.url_converter_scope;let i=e.length;for(;i--;){const t=e[i];let l=t.attr(r);void 0!==l?(t.attr(o,l.length>0?l:null),t.attr(r,null)):(l=t.attr(o),"style"===o?l=n.serializeStyle(n.parseStyle(l),t.name):s&&(l=s.call(a,l,o,t.name)),t.attr(o,l.length>0?l:null))}})),e.addAttributeFilter("class",(e=>{let t=e.length;for(;t--;){const n=e[t];let o=n.attr("class");o&&(o=n.attr("class").replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),n.attr("class",o.length>0?o:null))}})),e.addAttributeFilter("data-mce-type",((e,t,n)=>{let o=e.length;for(;o--;){const t=e[o];if("bookmark"===t.attr("data-mce-type")&&!n.cleanup){const e=M.from(t.firstChild).exists((e=>!hr(e.value)));e?t.unwrap():t.remove()}}})),e.addNodeFilter("noscript",(e=>{let t=e.length;for(;t--;){const n=e[t].firstChild;n&&(n.value=ms.decode(n.value))}})),e.addNodeFilter("script,style",((e,n)=>{const o=e=>e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"");let r=e.length;for(;r--;){const s=e[r],a=s.firstChild?s.firstChild.value:"";if("script"===n){const e=s.attr("type");e&&s.attr("type","mce-no/type"===e?null:e.replace(/^mce\-/,"")),"xhtml"===t.element_format&&a.length>0&&(s.firstChild.value="// <![CDATA[\n"+o(a)+"\n// ]]>")}else"xhtml"===t.element_format&&a.length>0&&(s.firstChild.value="\x3c!--\n"+o(a)+"\n--\x3e")}})),e.addNodeFilter("#comment",(e=>{let o=e.length;for(;o--;){const r=e[o];t.preserve_cdata&&0===r.value.indexOf("[CDATA[")?(r.name="#cdata",r.type=4,r.value=n.decode(r.value.replace(/^\[CDATA\[|\]\]$/g,""))):0===r.value.indexOf("mce:protected ")&&(r.name="#text",r.type=3,r.raw=!0,r.value=unescape(r.value).substr(14))}})),e.addNodeFilter("xml:namespace,input",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];7===o.type?o.remove():1===o.type&&("input"!==t||o.attr("type")||o.attr("type","text"))}})),e.addAttributeFilter("data-mce-type",(t=>{$(t,(t=>{"format-caret"===t.attr("data-mce-type")&&(t.isEmpty(e.schema.getNonEmptyElements())?t.remove():t.unwrap())}))})),e.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-type,data-mce-resize,data-mce-placeholder",((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)}))})(s,e,o),{schema:r,addNodeFilter:s.addNodeFilter,addAttributeFilter:s.addAttributeFilter,serialize:(n,a={})=>{const i={format:"html",...a},l=((e,t,n)=>((e,t)=>e&&e.hasEventListeners("PreProcess")&&!t.no_events)(e,n)?((e,t,n)=>{let o;const r=e.dom;let s=t.cloneNode(!0);const a=document.implementation;if(a.createHTMLDocument){const e=a.createHTMLDocument("");Bt.each("BODY"===s.nodeName?s.childNodes:[s],(t=>{e.body.appendChild(e.importNode(t,!0))})),s="BODY"!==s.nodeName?e.body.firstChild:e.body,o=r.doc,r.doc=e}return((e,t)=>{e.dispatch("PreProcess",t)})(e,{...n,node:s}),o&&(r.doc=o),s})(e,t,n):t)(t,n,i),d=((e,t,n)=>{const o=br(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||lr(fn(t))?o:Bt.trim(o)})(o,l,i),c=((e,t,n)=>{const o=n.selection?{forced_root_block:!1,...n}:n,r=e.parse(t,o);return(e=>{const t=e=>e&&"br"===e.name,n=e.lastChild;if(t(n)){const e=n.prev;t(e)&&(n.remove(),e.remove())}})(r),r})(s,d,i);return"tree"===i.format?c:((e,t,n,o,r)=>{const s=((e,t,n)=>Kf(e,t).serialize(n))(t,n,o);return((e,t,n)=>{if(!t.no_events&&e){const o=((e,t)=>e.dispatch("PostProcess",t))(e,{...t,content:n});return o.content}return n})(e,r,s)})(t,e,r,c,i)},addRules:r.addValidElements,setRules:r.setValidElements,addTempAttr:O(by,s,n),getTempAttrs:N(n),getNodeFilters:s.getNodeFilters,getAttributeFilters:s.getAttributeFilters,removeNodeFilter:s.removeNodeFilter,removeAttributeFilter:s.removeAttributeFilter}},yy=(e,t)=>{const n=vy(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs,getNodeFilters:n.getNodeFilters,getAttributeFilters:n.getAttributeFilters,removeNodeFilter:n.removeNodeFilter,removeAttributeFilter:n.removeAttributeFilter}},Cy=(e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,content:t}))(n,t);return Dv(e,o).map((t=>{const n=((e,t,n)=>dy(e).editor.setContent(t,n))(e,t.content,t);return Pv(e,n.html,t),n.content})).getOr(t)},wy="autoresize_on_init,content_editable_state,padd_empty_with_br,block_elements,boolean_attributes,editor_deselector,editor_selector,elements,file_browser_callback_types,filepicker_validator_handler,force_hex_style_colors,force_p_newlines,gecko_spellcheck,images_dataimg_filter,media_scripts,mode,move_caret_before_on_enter_elements,non_empty_elements,self_closing_elements,short_ended_elements,special,spellchecker_select_languages,spellchecker_whitelist,tab_focus,tabfocus_elements,table_responsive_width,text_block_elements,text_inline_elements,toolbar_drawer,types,validate,whitespace_elements,paste_enable_default_filters,paste_filter_drop,paste_word_valid_elements,paste_retain_style_properties,paste_convert_word_fake_lists".split(","),xy="bbcode,colorpicker,contextmenu,fullpage,legacyoutput,spellchecker,textcolor".split(","),ky=e=>{const t=K(wy,(t=>xe(e,t))),n=e.forced_root_block;return!1!==n&&""!==n||t.push("forced_root_block (false only)"),se(t)},Sy=e=>{const t=Bt.makeMap(e.plugins," "),n=K(xy,(e=>xe(t,e)));return se(n)},_y=Hs.DOM,Ey=e=>M.from(e).each((e=>e.destroy())),Ny=(()=>{const e={};return{add:(t,n)=>{e[t]=n},get:t=>e[t]?e[t]:{icons:{}},has:t=>xe(e,t)}})(),Ry=Qs.ModelManager,Ay=(e,t)=>t.dom[e],Oy=(e,t)=>parseInt(qn(t,e),10),Ty=O(Ay,"clientWidth"),By=O(Ay,"clientHeight"),Dy=O(Oy,"margin-top"),Py=O(Oy,"margin-left"),Ly=e=>{const t=[],n=()=>{const t=e.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,getArgs:e}})()},o=()=>M.from(t[0]),r=()=>{$(t,(e=>{e.reposition()}))},s=e=>{J(t,(t=>t===e)).each((e=>{t.splice(e,1)}))},a=(a,i=!0)=>{if(!e.removed&&(e=>{return(t=e.inline?e.getBody():e.getContentAreaContainer(),M.from(t).map(fn)).map(jn).getOr(!1);var t})(e))return i&&e.dispatch("BeforeOpenNotification",{notification:a}),Q(t,(e=>{return t=n().getArgs(e),o=a,!(t.type!==o.type||t.text!==o.text||t.progressBar||t.timeout||o.progressBar||o.timeout);var t,o})).getOrThunk((()=>{e.editorManager.setActive(e);const i=n().open(a,(()=>{s(i),r(),o().fold((()=>e.focus()),(e=>tf(fn(e.getEl()))))}));return(e=>{t.push(e)})(i),r(),e.dispatch("OpenNotification",{notification:{...i}}),i}))},i=N(t);return(e=>{e.on("SkinLoaded",(()=>{const t=vl(e);t&&a({text:t,type:"warning",timeout:0},!1),r()})),e.on("show ResizeEditor ResizeWindow NodeChange",(()=>{requestAnimationFrame(r)})),e.on("remove",(()=>{$(t.slice(),(e=>{n().close(e)}))}))})(e),{open:a,close:()=>{o().each((e=>{n().close(e),s(e),r()}))},getNotifications:i}},My=Qs.PluginManager,Iy=Qs.ThemeManager,Fy=e=>{let t=[];const n=()=>{const t=e.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,openUrl:e,alert:e,confirm:e,close:e,getParams:e,setParams:e}})()},o=(e,t)=>(...n)=>t?t.apply(e,n):void 0,r=n=>{(t=>{e.dispatch("CloseWindow",{dialog:t})})(n),t=K(t,(e=>e!==n)),0===t.length&&e.focus()},s=n=>{e.editorManager.setActive(e),uf(e),e.ui.show();const o=n();return(n=>{t.push(n),(t=>{e.dispatch("OpenWindow",{dialog:t})})(n)})(o),o};return e.on("remove",(()=>{$(t,(e=>{n().close(e)}))})),{open:(e,t)=>s((()=>n().open(e,t,r))),openUrl:e=>s((()=>n().openUrl(e,r))),alert:(e,t,r)=>{const s=n();s.alert(e,o(r||s,t))},confirm:(e,t,r)=>{const s=n();s.confirm(e,o(r||s,t))},close:()=>{M.from(t[t.length-1]).each((e=>{n().close(e),r(e)}))}}},Uy=(e,t)=>{e.notificationManager.open({type:"error",text:t})},zy=(e,t)=>{e._skinLoaded?Uy(e,t):e.on("SkinLoaded",(()=>{Uy(e,t)}))},jy=(e,t,n)=>{Gu(e,t,{message:n}),console.error(n)},Vy=(e,t,n)=>n?`Failed to load ${e}: ${n} from url ${t}`:`Failed to load ${e} url: ${t}`,Hy=(e,...t)=>{const n=window.console;n&&(n.error?n.error(e,...t):n.log(e,...t))},$y=(e,t)=>{const n=e.editorManager.baseURL+"/skins/content",o=`content${e.editorManager.suffix}.css`,r=!0===e.inline;return H(t,(t=>(e=>/^[a-z0-9\-]+$/i.test(e))(t)&&!r?`${n}/${t}/${o}`:e.documentBaseURI.toAbsolute(t)))},qy=L,Wy=(e,t)=>{const n={};return{findAll:(o,r=L)=>{const s=K((e=>e?de(e.getElementsByTagName("img")):[])(o),(t=>{const n=t.src;return!t.hasAttribute("data-mce-bogus")&&!t.hasAttribute("data-mce-placeholder")&&!(!n||n===Nt.transparentSrc)&&(ze(n,"blob:")?!e.isUploaded(n)&&r(t):!!ze(n,"data:")&&r(t))})),a=H(s,(e=>{const o=e.src;if(xe(n,o))return n[o].then((t=>m(t)?t:{image:e,blobInfo:t.blobInfo}));{const r=((e,t)=>{const n=()=>Promise.reject("Invalid data URI");if(ze(t,"blob:")){const s=e.getByUri(t);return C(s)?Promise.resolve(s):(o=t,ze(o,"blob:")?(e=>fetch(e).then((e=>e.ok?e.blob():Promise.reject())).catch((()=>Promise.reject(`Cannot convert ${e} to Blob. Resource might not exist or is inaccessible.`))))(o):ze(o,"data:")?(r=o,new Promise(((e,t)=>{lv(r).bind((({type:e,data:t,base64Encoded:n})=>dv(e,t,n))).fold((()=>t("Invalid data URI")),e)}))):Promise.reject("Unknown URI format")).then((t=>cv(t).then((o=>mv(o,!1,(n=>M.some(fv(e,t,n)))).getOrThunk(n)))))}var o,r;return ze(t,"data:")?gv(e,t).fold(n,(e=>Promise.resolve(e))):Promise.reject("Unknown image data format")})(t,o).then((t=>(delete n[o],{image:e,blobInfo:t}))).catch((e=>(delete n[o],e)));return n[o]=r,r}}));return Promise.all(a)}}},Ky=()=>{let e={};const t=(e,t)=>({status:e,resultUri:t}),n=t=>t in e;return{hasBlobUri:n,getResultUri:t=>{const n=e[t];return n?n.resultUri:null},isPending:t=>!!n(t)&&1===e[t].status,isUploaded:t=>!!n(t)&&2===e[t].status,markPending:n=>{e[n]=t(1,null)},markUploaded:(n,o)=>{e[n]=t(2,o)},removeFailed:t=>{delete e[t]},destroy:()=>{e={}}}};let Gy=0;const Yy=(e,t)=>{const n={},o=(e,n)=>new Promise(((o,r)=>{const s=new XMLHttpRequest;s.open("POST",t.url),s.withCredentials=t.credentials,s.upload.onprogress=e=>{n(e.loaded/e.total*100)},s.onerror=()=>{r("Image upload failed due to a XHR Transport error. Code: "+s.status)},s.onload=()=>{if(s.status<200||s.status>=300)return void r("HTTP Error: "+s.status);const e=JSON.parse(s.responseText);var n,a;e&&m(e.location)?o((n=t.basePath,a=e.location,n?n.replace(/\/$/,"")+"/"+a.replace(/^\//,""):a)):r("Invalid JSON: "+s.responseText)};const a=new FormData;a.append("file",e.blob(),e.filename()),s.send(a)})),r=(e,t)=>({url:t,blobInfo:e,status:!0}),s=(e,t)=>({url:"",blobInfo:e,status:!1,error:t}),a=(e,t)=>{Bt.each(n[e],(e=>{e(t)})),delete n[e]};return!1===w(t.handler)&&(t.handler=o),{upload:(i,l)=>t.url||t.handler!==o?((o,i)=>(o=Bt.grep(o,(t=>!e.isUploaded(t.blobUri()))),Promise.all(Bt.map(o,(o=>e.isPending(o.blobUri())?(e=>{const t=e.blobUri();return new Promise((e=>{n[t]=n[t]||[],n[t].push(e)}))})(o):((t,n,o)=>(e.markPending(t.blobUri()),new Promise((i=>{let l,d;try{const c=()=>{l&&(l.close(),d=S)},u=n=>{c(),e.markUploaded(t.blobUri(),n),a(t.blobUri(),r(t,n)),i(r(t,n))},f=n=>{c(),e.removeFailed(t.blobUri()),a(t.blobUri(),s(t,n)),i(s(t,n))};d=e=>{e<0||e>100||M.from(l).orThunk((()=>M.from(o).map(B))).each((t=>{l=t,t.progressBar.value(e)}))},n(t,d).then(u,(e=>{f(m(e)?{message:e}:e)}))}catch(e){i(s(t,e))}}))))(o,t.handler,i))))))(i,l):new Promise((e=>{e([])}))}},Xy=e=>()=>e.notificationManager.open({text:e.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0}),Qy=(e,t)=>Yy(t,{url:Zi(e),basePath:el(e),credentials:tl(e),handler:nl(e)}),Jy=e=>{const t=(()=>{let e=[];const t=e=>{if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");const t=e.id||"blobid"+Gy+++(()=>{const e=()=>Math.round(4294967295*Math.random()).toString(36);return"s"+(new Date).getTime().toString(36)+e()+e()+e()})(),n=e.name||t,o=e.blob;var r;return{id:N(t),name:N(n),filename:N(e.filename||n+"."+(r=o.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"}[r.toLowerCase()]||"dat")),blob:N(o),base64:N(e.base64),blobUri:N(e.blobUri||URL.createObjectURL(o)),uri:N(e.uri)}},n=t=>Q(e,t).getOrUndefined(),o=e=>n((t=>t.id()===e));return{create:(e,n,o,r,s)=>{if(m(e))return t({id:e,name:r,filename:s,blob:n,base64:o});if(f(e))return t(e);throw new Error("Unknown input type")},add:t=>{o(t.id())||e.push(t)},get:o,getByUri:e=>n((t=>t.blobUri()===e)),getByData:(e,t)=>n((n=>n.base64()===e&&n.blob().type===t)),findFirst:n,removeByUri:t=>{e=K(e,(e=>e.blobUri()!==t||(URL.revokeObjectURL(e.blobUri()),!1)))},destroy:()=>{$(e,(e=>{URL.revokeObjectURL(e.blobUri())})),e=[]}}})();let n,o;const r=Ky(),s=[],a=t=>n=>e.selection?t(n):[],i=(e,t,n)=>{let o=0;do{o=e.indexOf(t,o),-1!==o&&(e=e.substring(0,o)+n+e.substr(o+t.length),o+=n.length-t.length+1)}while(-1!==o);return e},l=(e,t,n)=>{const o=`src="${n}"${n===Nt.transparentSrc?' data-mce-placeholder="1"':""}`;return e=i(e,`src="${t}"`,o),i(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},d=(t,n)=>{$(e.undoManager.data,(e=>{"fragmented"===e.type?e.fragments=H(e.fragments,(e=>l(e,t,n))):e.content=l(e.content,t,n)}))},c=()=>(n||(n=Qy(e,r)),p().then(a((o=>{const r=H(o,(e=>e.blobInfo));return n.upload(r,Xy(e)).then(a((n=>{const r=[];let s=!1;const a=H(n,((n,a)=>{const i=o[a].blobInfo,l=o[a].image;let c=!1;return n.status&&Xi(e)?(n.url&&!Ue(l.src,n.url)&&(s=!0),t.removeByUri(l.src),ly(e)||((t,n)=>{const o=e.convertURL(n,"src");var r;d(t.src,n),Wt(fn(t),{src:Yi(e)?(r=n,r+(-1===r.indexOf("?")?"?":"&")+(new Date).getTime()):n,"data-mce-src":o})})(l,n.url)):n.error&&(n.error.remove&&(d(l.getAttribute("src"),Nt.transparentSrc),r.push(l),c=!0),((e,t)=>{zy(e,Xs.translate(["Failed to upload image: {0}",t]))})(e,n.error.message)),{element:l,status:n.status,uploadUri:n.url,blobInfo:i,removed:c}}));return r.length>0&&!ly(e)?e.undoManager.transact((()=>{$(r,(n=>{e.dom.remove(n),t.removeByUri(n.src)}))})):s&&e.undoManager.dispatchChange(),a})))})))),u=()=>Gi(e)?c():Promise.resolve([]),g=e=>te(s,(t=>t(e))),p=()=>(o||(o=Wy(r,t)),o.findAll(e.getBody(),g).then(a((t=>{const n=K(t,(t=>!m(t)||(zy(e,t),!1)));return ly(e)||$(n,(e=>{d(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")})),n})))),h=n=>n.replace(/src="(blob:[^"]+)"/g,((n,o)=>{const s=r.getResultUri(o);if(s)return'src="'+s+'"';let a=t.getByUri(o);return a||(a=Y(e.editorManager.get(),((e,t)=>e||t.editorUpload&&t.editorUpload.blobCache.getByUri(o)),null)),a?'src="data:'+a.blob().type+";base64,"+a.base64()+'"':n}));return e.on("SetContent",(()=>{Gi(e)?u():p()})),e.on("RawSaveContent",(e=>{e.content=h(e.content)})),e.on("GetContent",(e=>{e.source_view||"raw"===e.format||"tree"===e.format||(e.content=h(e.content))})),e.on("PostRender",(()=>{e.parser.addNodeFilter("img",(e=>{$(e,(e=>{const n=e.attr("src");if(t.getByUri(n))return;const o=r.getResultUri(n);o&&e.attr("src",o)}))}))})),{blobCache:t,addFilter:e=>{s.push(e)},uploadImages:c,uploadImagesAuto:u,scanForImages:p,destroy:()=>{t.destroy(),r.destroy(),o=n=null}}},Zy={remove_similar:!0,inherit:!1},eC={selector:"td,th",...Zy},tC={tablecellbackgroundcolor:{styles:{backgroundColor:"%value"},...eC},tablecellverticalalign:{styles:{"vertical-align":"%value"},...eC},tablecellbordercolor:{styles:{borderColor:"%value"},...eC},tablecellclass:{classes:["%value"],...eC},tableclass:{selector:"table",classes:["%value"],...Zy},tablecellborderstyle:{styles:{borderStyle:"%value"},...eC},tablecellborderwidth:{styles:{borderWidth:"%value"},...eC}},nC=N(tC),oC=Bt.each,rC=Hs.DOM,sC=(e,t)=>{let n,o,r;const s=t&&t.schema||Ss({}),a=e=>{o="string"==typeof e?{name:e,classes:[],attrs:{}}:e;const t=rC.create(o.name);return((e,t)=>{t.classes.length&&rC.addClass(e,t.classes.join(" ")),rC.setAttribs(e,t.attrs)})(t,o),t},i=(e,t,n)=>{let o,r;const l=t.length>0&&t[0],d=l&&l.name,c=((e,t)=>{const n="string"!=typeof e?e.nodeName.toLowerCase():e,o=s.getElementRule(n),r=o&&o.parentsRequired;return!(!r||!r.length)&&(t&&-1!==Bt.inArray(r,t)?t:r[0])})(e,d);if(c)d===c?(r=t[0],t=t.slice(1)):r=c;else if(l)r=t[0],t=t.slice(1);else if(!n)return e;return r&&(o=a(r),o.appendChild(e)),n&&(o||(o=rC.create("div"),o.appendChild(e)),Bt.each(n,(t=>{const n=a(t);o.insertBefore(n,e)}))),i(o,t,r&&r.siblings)};return e&&e.length?(o=e[0],n=a(o),r=rC.create("div"),r.appendChild(i(n,e.slice(1),o.siblings)),r):""},aC=e=>{let t;const n={classes:[],attrs:{}};return"*"!==(e=n.selector=Bt.trim(e))&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,((e,t,o,r,s)=>{switch(t){case"#":n.attrs.id=o;break;case".":n.classes.push(o);break;case":":-1!==Bt.inArray("checked disabled enabled read-only required".split(" "),o)&&(n.attrs[o]=o)}if("["===r){const e=s.match(/([\w\-]+)(?:\=\"([^\"]+))?/);e&&(n.attrs[e[1]]=e[2])}return""}))),n.name=t||"div",n},iC=(e,t)=>{let n,o,r,s="",a=_l(e);if(""===a)return"";const i=e=>e.replace(/%(\w+)/g,"");if("string"==typeof t){if(!(t=e.formatter.get(t)))return;t=t[0]}if("preview"in t){const e=we(t,"preview");if(Dt(e,!1))return"";a=e.getOr(a)}n=t.block||t.inline||"span";const l=(d=t.selector)&&"string"==typeof d?(d=(d=d.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Bt.map(d.split(/(?:>|\s+(?![^\[\]]+\]))/),(e=>{const t=Bt.map(e.split(/(?:~\+|~|\+)/),aC),n=t.pop();return t.length&&(n.siblings=t),n})).reverse()):[];var d;l.length?(l[0].name||(l[0].name=n),n=t.selector,o=sC(l,e)):o=sC([n],e);const c=rC.select(n,o)[0]||o.firstChild;return oC(t.styles,((e,t)=>{const n=i(e);n&&rC.setStyle(c,t,n)})),oC(t.attributes,((e,t)=>{const n=i(e);n&&rC.setAttrib(c,t,n)})),oC(t.classes,(e=>{const t=i(e);rC.hasClass(c,t)||rC.addClass(c,t)})),e.dispatch("PreviewFormats"),rC.setStyles(o,{position:"absolute",left:-65535}),e.getBody().appendChild(o),r=rC.getStyle(e.getBody(),"fontSize",!0),r=/px$/.test(r)?parseInt(r,10):0,oC(a.split(" "),(t=>{let n=rC.getStyle(c,t,!0);if(!("background-color"===t&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(n)&&(n=rC.getStyle(e.getBody(),t,!0),"#ffffff"===Gc(n).toLowerCase())||"color"===t&&"#000000"===Gc(n).toLowerCase())){if("font-size"===t&&/em|%$/.test(n)){if(0===r)return;n=parseFloat(n)/(/%$/.test(n)?100:1)*r+"px"}"border"===t&&n&&(s+="padding:0 2px;"),s+=t+":"+n+";"}})),e.dispatch("AfterPreviewFormats"),rC.remove(o),s},lC=e=>{const t=(e=>{const t={},n=(e,o)=>{e&&(m(e)?(p(o)||(o=[o]),$(o,(e=>{v(e.deep)&&(e.deep=!lu(e)),v(e.split)&&(e.split=!lu(e)||du(e)),v(e.remove)&&lu(e)&&!du(e)&&(e.remove="none"),lu(e)&&du(e)&&(e.mixed=!0,e.block_expand=!0),m(e.classes)&&(e.classes=e.classes.split(/\s+/))})),t[e]=o):fe(e,((e,t)=>{n(t,e)})))};return n((e=>{const t=e.dom,n=e.schema.type,o={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"left"},inherit:!1,preview:!1},{selector:"img,audio,video",collapsed:!1,styles:{float:"left"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginLeft:"0px",marginRight:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{float:"right"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginRight:"0px",marginLeft:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"justify"},inherit:!1,preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all",preserve_attributes:["class","style"]}],italic:[{inline:"em",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all",preserve_attributes:["class","style"]}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all",preserve_attributes:["class","style"]}],strikethrough:(()=>{const e={inline:"span",styles:{textDecoration:"line-through"},exact:!0},t={inline:"strike",remove:"all",preserve_attributes:["class","style"]},o={inline:"s",remove:"all",preserve_attributes:["class","style"]};return"html4"!==n?[o,e,t]:[e,o,t]})(),forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},lineheight:{selector:"h1,h2,h3,h4,h5,h6,p,li,td,th,div",styles:{lineHeight:"%value"}},fontsize_class:{inline:"span",attributes:{class:"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:(e,t,n)=>Co(e)&&e.hasAttribute("href"),onformat:(e,n,o)=>{Bt.each(o,((n,o)=>{t.setAttrib(e,o,n)}))}},lang:{inline:"span",clear_child_styles:!0,remove_similar:!0,attributes:{lang:"%value","data-mce-lang":e=>{var t;return null!==(t=null==e?void 0:e.customValue)&&void 0!==t?t:null}}},removeformat:[{selector:"b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Bt.each("p h1 h2 h3 h4 h5 h6 div address pre dt dd samp".split(/\s/),(e=>{o[e]={block:e,remove:"all"}})),o})(e)),n(nC()),n(Sl(e)),{get:e=>C(e)?t[e]:t,has:e=>xe(t,e),register:n,unregister:e=>(e&&t[e]&&delete t[e],t)}})(e),n=Ws(null);return(e=>{e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(let t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])})(e),(e=>{e.on("mouseup keydown",(t=>{((e,t)=>{const n=e.selection,o=e.getBody();wh(e,null,!1),8!==t&&46!==t||!n.isCollapsed()||n.getStart().innerHTML!==ph||wh(e,Ec(o,n.getStart())),37!==t&&39!==t||wh(e,Ec(o,n.getStart()))})(e,t.keyCode)}))})(e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:(t,n,o)=>{((e,t,n,o)=>{cy(e).formatter.apply(t,n,o)})(e,t,n,o)},remove:(t,n,o,r)=>{((e,t,n,o,r)=>{cy(e).formatter.remove(t,n,o,r)})(e,t,n,o,r)},toggle:(t,n,o)=>{((e,t,n,o)=>{cy(e).formatter.toggle(t,n,o)})(e,t,n,o)},match:(t,n,o,r)=>((e,t,n,o,r)=>cy(e).formatter.match(t,n,o,r))(e,t,n,o,r),closest:t=>((e,t)=>cy(e).formatter.closest(t))(e,t),matchAll:(t,n)=>((e,t,n)=>cy(e).formatter.matchAll(t,n))(e,t,n),matchNode:(t,n,o,r)=>((e,t,n,o,r)=>cy(e).formatter.matchNode(t,n,o,r))(e,t,n,o,r),canApply:t=>((e,t)=>cy(e).formatter.canApply(t))(e,t),formatChanged:(t,o,r,s)=>((e,t,n,o,r,s)=>cy(e).formatter.formatChanged(t,n,o,r,s))(e,n,t,o,r,s),getCssText:O(iC,e)}},dC=e=>{switch(e.toLowerCase()){case"undo":case"redo":case"mcefocus":return!0;default:return!1}},cC=e=>{const t=Js(),n=Ws(0),o=Ws(0),r={data:[],typing:!1,beforeChange:()=>{((e,t,n)=>{cy(e).undoManager.beforeChange(t,n)})(e,n,t)},add:(s,a)=>((e,t,n,o,r,s,a)=>cy(e).undoManager.add(t,n,o,r,s,a))(e,r,o,n,t,s,a),dispatchChange:()=>{e.setDirty(!0),e.dispatch("change",{level:Zv(e),lastLevel:ae(r.data,o.get()).getOrUndefined()})},undo:()=>((e,t,n,o)=>cy(e).undoManager.undo(t,n,o))(e,r,n,o),redo:()=>((e,t,n)=>cy(e).undoManager.redo(t,n))(e,o,r.data),clear:()=>{((e,t,n)=>{cy(e).undoManager.clear(t,n)})(e,r,o)},reset:()=>{((e,t)=>{cy(e).undoManager.reset(t)})(e,r)},hasUndo:()=>((e,t,n)=>cy(e).undoManager.hasUndo(t,n))(e,r,o),hasRedo:()=>((e,t,n)=>cy(e).undoManager.hasRedo(t,n))(e,r,o),transact:t=>((e,t,n,o)=>cy(e).undoManager.transact(t,n,o))(e,r,n,t),ignore:t=>{((e,t,n)=>{cy(e).undoManager.ignore(t,n)})(e,n,t)},extra:(t,n)=>{((e,t,n,o,r)=>{cy(e).undoManager.extra(t,n,o,r)})(e,r,o,t,n)}};return ly(e)||((e,t,n)=>{const o=Ws(!1),r=e=>{sy(t,!1,n),t.add({},e)};e.on("init",(()=>{t.add()})),e.on("BeforeExecCommand",(e=>{const o=e.command;dC(o)||(ay(t,n),t.beforeChange())})),e.on("ExecCommand",(e=>{const t=e.command;dC(t)||r(e)})),e.on("ObjectResizeStart cut",(()=>{t.beforeChange()})),e.on("SaveContent ObjectResized blur",r),e.on("dragend",r),e.on("keyup",(n=>{const s=n.keyCode;n.isDefaultPrevented()||((s>=33&&s<=36||s>=37&&s<=40||45===s||n.ctrlKey)&&(r(),e.nodeChanged()),46!==s&&8!==s||e.nodeChanged(),o.get()&&t.typing&&!1===oy(Zv(e),t.data[0])&&(!1===e.isDirty()&&e.setDirty(!0),e.dispatch("TypingUndo"),o.set(!1),e.nodeChanged()))})),e.on("keydown",(e=>{const s=e.keyCode;if(e.isDefaultPrevented())return;if(s>=33&&s<=36||s>=37&&s<=40||45===s)return void(t.typing&&r(e));const a=e.ctrlKey&&!e.altKey||e.metaKey;!(s<16||s>20)||224===s||91===s||t.typing||a||(t.beforeChange(),sy(t,!0,n),t.add({},e),o.set(!0))})),e.on("mousedown",(e=>{t.typing&&r(e)})),e.on("input",(e=>{var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data||(e=>"insertFromPaste"===e.inputType||"insertFromDrop"===e.inputType)(e))&&r(e)})),e.on("AddUndo Undo Redo ClearUndos",(t=>{t.isDefaultPrevented()||e.nodeChanged()}))})(e,r,n),(e=>{e.addShortcut("meta+z","","Undo"),e.addShortcut("meta+y,meta+shift+z","","Redo")})(e),r},uC=[9,27,em.HOME,em.END,19,20,44,144,145,33,34,45,16,17,18,91,92,93,em.DOWN,em.UP,em.LEFT,em.RIGHT].concat(Nt.browser.isFirefox()?[224]:[]),mC="data-mce-placeholder",fC=e=>"keydown"===e.type||"keyup"===e.type,gC=e=>{const t=e.keyCode;return t===em.BACKSPACE||t===em.DELETE},pC=(e,t)=>({from:e,to:t}),hC=(e,t)=>{const n=fn(e),o=fn(t.container());return dp(n,o).map((e=>((e,t)=>({block:e,position:t}))(e,t)))},bC=e=>{const t=Rn(e);return J(t,er).fold(N(t),(e=>t.slice(0,e)))},vC=e=>{const t=bC(e);return $(t,no),t},yC=(e,t)=>{const n=hg(t,e);return Q(n.reverse(),(e=>Yr(e))).each(no)},CC=(e,t,n,o)=>{if(Yr(n))return fg(n),xc(n.dom);0===K(En(o),(e=>!Yr(e))).length&&Yr(t)&&Xn(o,un("br"));const r=wc(n.dom,Ya.before(o.dom));return $(vC(t),(e=>{Xn(o,e)})),yC(e,t),r},wC=(e,t,n)=>{if(Yr(n))return no(n),Yr(t)&&fg(t),xc(t.dom);const o=kc(n.dom);return $(vC(t),(e=>{Zn(n,e)})),yC(e,t),o},xC=(e,t)=>{yc(e,t.dom).map((e=>e.getNode())).map(fn).filter(nr).each(no)},kC=(e,t,n)=>(xC(!0,t),xC(!1,n),((e,t)=>yn(t,e)?((e,t)=>{const n=hg(t,e);return M.from(n[n.length-1])})(t,e):M.none())(t,n).fold(O(wC,e,t,n),O(CC,e,t,n))),SC=(e,t,n,o)=>t?kC(e,o,n):kC(e,n,o),_C=(e,t)=>{const n=fn(e.getBody()),o=((e,t,n)=>n.collapsed?((e,t,n)=>{const o=hC(e,Ya.fromRangeStart(n)),r=o.bind((n=>hc(t,e,n.position).bind((n=>hC(e,n).map((n=>((e,t,n)=>Po(n.position.getNode())&&!1===Yr(n.block)?yc(!1,n.block.dom).bind((o=>o.isEqual(n.position)?hc(t,e,o).bind((t=>hC(e,t))):M.some(n))).getOr(n):n)(e,t,n)))))));return Pt(o,r,pC).filter((e=>(e=>!1===vn(e.from.block,e.to.block))(e)&&(e=>kn(e.from.block).bind((t=>kn(e.to.block).filter((e=>vn(t,e))))).isSome())(e)&&(e=>!1===Io(e.from.block.dom)&&!1===Io(e.to.block.dom))(e)))})(e,t,n):M.none())(n.dom,t,e.selection.getRng()).map((o=>()=>{SC(n,t,o.from.block,o.to.block).each((t=>{e.selection.setRng(t.toRange())}))}));return o},EC=(e,t)=>{const n=fn(t),o=O(vn,e);return $o(n,ir,o).isSome()},NC=e=>{const t=fn(e.getBody());return((e,t)=>{const n=wc(e.dom,Ya.fromRangeStart(t)).isNone(),o=Cc(e.dom,Ya.fromRangeEnd(t)).isNone();return!((e,t)=>EC(e,t.startContainer)||EC(e,t.endContainer))(e,t)&&n&&o})(t,e.selection.getRng())?(e=>M.some((()=>{e.setContent(""),e.selection.setCursorLocation()})))(e):((e,t)=>{const n=t.getRng();return Pt(dp(e,fn(n.startContainer)),dp(e,fn(n.endContainer)),((o,r)=>!1===vn(o,r)?M.some((()=>{n.deleteContents(),SC(e,!0,o,r).each((e=>{t.setRng(e.toRange())}))})):M.none())).getOr(M.none())})(t,e.selection)},RC=(e,t)=>e.selection.isCollapsed()?M.none():NC(e),AC=Mo,OC=Io,TC=(e,t,n,o,r)=>M.from(t._selectionOverrides.showCaret(e,n,o,r)),BC=(e,t)=>e.dispatch("BeforeObjectSelected",{target:t}).isDefaultPrevented()?M.none():M.some((e=>{const t=e.ownerDocument.createRange();return t.selectNode(e),t})(t)),DC=(e,t,n)=>t.collapsed?((e,t,n)=>{const o=Yd(1,e.getBody(),t),r=Ya.fromRangeStart(o),s=r.getNode();if(Nd(s))return TC(1,e,s,!r.isAtEnd(),!1);const a=r.getNode(!0);if(Nd(a))return TC(1,e,a,!1,!1);const i=e.dom.getParent(r.getNode(),(e=>OC(e)||AC(e)));return Nd(i)?TC(1,e,i,!1,n):M.none()})(e,t,n).getOr(t):t,PC=e=>ug(e)||ig(e),LC=e=>mg(e)||lg(e),MC=(e,t,n,o,r,s)=>{TC(o,e,s.getNode(!r),r,!0).each((n=>{if(t.collapsed){const e=t.cloneRange();r?e.setEnd(n.startContainer,n.startOffset):e.setStart(n.endContainer,n.endOffset),e.deleteContents()}else t.deleteContents();e.selection.setRng(n)})),((e,t)=>{Ro(t)&&0===t.data.length&&e.remove(t)})(e.dom,n)},IC=(e,t)=>((e,t)=>{const n=e.selection.getRng();if(!Ro(n.commonAncestorContainer))return M.none();const o=t?tc.Forwards:tc.Backwards,r=mc(e.getBody()),s=O(Zd,t?r.next:r.prev),a=t?PC:LC,i=Qd(o,e.getBody(),n),l=op(t,s(i));if(!l||!ec(i,l))return M.none();if(a(l))return M.some((()=>MC(e,n,i.getNode(),o,t,l)));const d=s(l);return d&&a(d)&&ec(l,d)?M.some((()=>MC(e,n,i.getNode(),o,t,d))):M.none()})(e,t),FC=(e,t)=>{const n=e.getBody();return t?xc(n).filter(ug):kc(n).filter(mg)},UC=e=>{const t=e.selection.getRng();return!t.collapsed&&(FC(e,!0).exists((e=>e.isEqual(Ya.fromRangeStart(t))))||FC(e,!1).exists((e=>e.isEqual(Ya.fromRangeEnd(t)))))},zC=Ci([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),jC=(e,t,n)=>hc(t,e,n).bind((o=>{return r=o.getNode(),ir(fn(r))||sr(fn(r))||((e,t,n,o)=>{const r=t=>tr(fn(t))&&!Vd(n,o,e);return Xd(!t,n).fold((()=>Xd(t,o).fold(P,r)),r)})(e,t,n,o)?M.none():t&&Io(o.getNode())||!1===t&&Io(o.getNode(!0))?((e,t,n,o)=>{const r=o.getNode(!1===t);return dp(fn(e),fn(n.getNode())).map((e=>Yr(e)?zC.remove(e.dom):zC.moveToElement(r))).orThunk((()=>M.some(zC.moveToElement(r))))})(e,t,n,o):t&&mg(n)||!1===t&&ug(n)?M.some(zC.moveToPosition(o)):M.none();var r})),VC=(e,t)=>M.from(Fp(e.getBody(),t)),HC=(e,t)=>{const n=e.selection.getNode();return VC(e,n).filter(Io).fold((()=>((e,t,n)=>{const o=Yd(t?1:-1,e,n),r=Ya.fromRangeStart(o),s=fn(e);return!1===t&&mg(r)?M.some(zC.remove(r.getNode(!0))):t&&ug(r)?M.some(zC.remove(r.getNode())):!1===t&&ug(r)&&Rg(s,r)?Ag(s,r).map((e=>zC.remove(e.getNode()))):t&&mg(r)&&Ng(s,r)?Og(s,r).map((e=>zC.remove(e.getNode()))):((e,t,n)=>((e,t)=>{const n=t.getNode(!1===e),o=e?"after":"before";return Co(n)&&n.getAttribute("data-mce-caret")===o})(t,n)?((e,t)=>e&&Io(t.nextSibling)?M.some(zC.moveToElement(t.nextSibling)):!1===e&&Io(t.previousSibling)?M.some(zC.moveToElement(t.previousSibling)):M.none())(t,n.getNode(!1===t)).fold((()=>jC(e,t,n)),M.some):jC(e,t,n).bind((t=>((e,t,n)=>n.fold((e=>M.some(zC.remove(e))),(e=>M.some(zC.moveToElement(e))),(n=>Vd(t,n,e)?M.none():M.some(zC.moveToPosition(n)))))(e,n,t))))(e,t,r)})(e.getBody(),t,e.selection.getRng()).map((n=>()=>n.fold(((e,t)=>n=>(e._selectionOverrides.hideFakeCaret(),Zg(e,t,fn(n)),!0))(e,t),((e,t)=>n=>{const o=t?Ya.before(n):Ya.after(n);return e.selection.setRng(o.toRange()),!0})(e,t),(e=>t=>(e.selection.setRng(t.toRange()),!0))(e))))),(()=>M.some(S)))},$C=e=>{const t=e.dom,n=e.selection,o=Fp(e.getBody(),n.getNode());if(Mo(o)&&t.isBlock(o)&&t.isEmpty(o)){const e=t.create("br",{"data-mce-bogus":"1"});t.setHTML(o,""),o.appendChild(e),n.setRng(Ya.before(e).toRange())}return!0},qC=(e,t)=>e.selection.isCollapsed()?HC(e,t):((e,t)=>{const n=e.selection.getNode();return Io(n)&&!Fo(n)?VC(e,n.parentNode).filter(Io).fold((()=>M.some((()=>{var n;n=fn(e.getBody()),$(na(n,".mce-offscreen-selection"),no),Zg(e,t,fn(e.selection.getNode())),cp(e)}))),(()=>M.some(S))):UC(e)?M.some((()=>{mp(e,e.selection.getRng(),fn(e.getBody()))})):M.none()})(e,t),WC=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=Ya.fromRangeStart(e.selection.getRng());return hc(t,e.getBody(),n).filter((e=>t?sg(e):ag(e))).bind((e=>M.from(Hd(t?0:-1,e)))).map((t=>()=>e.selection.select(t)))})(e,t):M.none(),KC=Ro,GC=e=>KC(e)&&e.data[0]===pr,YC=e=>KC(e)&&e.data[e.data.length-1]===pr,XC=e=>e.ownerDocument.createTextNode(pr),QC=(e,t)=>e?(e=>{if(KC(e.previousSibling))return YC(e.previousSibling)||e.previousSibling.appendData(pr),e.previousSibling;if(KC(e))return GC(e)||e.insertData(0,pr),e;{const t=XC(e);return e.parentNode.insertBefore(t,e),t}})(t):(e=>{if(KC(e.nextSibling))return GC(e.nextSibling)||e.nextSibling.insertData(0,pr),e.nextSibling;if(KC(e))return YC(e)||e.appendData(pr),e;{const t=XC(e);return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t),t}})(t),JC=O(QC,!0),ZC=O(QC,!1),ew=(e,t)=>Ro(e.container())?QC(t,e.container()):QC(t,e.getNode()),tw=(e,t)=>{const n=t.get();return n&&e.container()===n&&wr(n)},nw=(e,t)=>t.fold((t=>{Cd(e.get());const n=JC(t);return e.set(n),M.some(Ya(n,n.length-1))}),(t=>xc(t).map((t=>{if(tw(t,e))return Ya(e.get(),1);{Cd(e.get());const n=ew(t,!0);return e.set(n),Ya(n,1)}}))),(t=>kc(t).map((t=>{if(tw(t,e))return Ya(e.get(),e.get().length-1);{Cd(e.get());const n=ew(t,!1);return e.set(n),Ya(n,n.length-1)}}))),(t=>{Cd(e.get());const n=ZC(t);return e.set(n),M.some(Ya(n,1))})),ow=(e,t)=>{for(let n=0;n<e.length;n++){const o=e[n].apply(null,t);if(o.isSome())return o}return M.none()},rw=Ci([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),sw=(e,t)=>jd(t,e)||e,aw=(e,t,n)=>{const o=rp(n),r=sw(t,o.container());return np(e,r,o).fold((()=>Cc(r,o).bind(O(np,e,r)).map((e=>rw.before(e)))),M.none)},iw=(e,t)=>null===Ec(e,t),lw=(e,t,n)=>np(e,t,n).filter(O(iw,t)),dw=(e,t,n)=>{const o=sp(n);return lw(e,t,o).bind((e=>wc(e,o).isNone()?M.some(rw.start(e)):M.none()))},cw=(e,t,n)=>{const o=rp(n);return lw(e,t,o).bind((e=>Cc(e,o).isNone()?M.some(rw.end(e)):M.none()))},uw=(e,t,n)=>{const o=sp(n),r=sw(t,o.container());return np(e,r,o).fold((()=>wc(r,o).bind(O(np,e,r)).map((e=>rw.after(e)))),M.none)},mw=e=>{return!1===(t=gw(e),"rtl"===Hs.DOM.getStyle(t,"direction",!0)||(e=>ep.test(e))(t.textContent));var t},fw=(e,t,n)=>ow([aw,dw,cw,uw],[e,t,n]).filter(mw),gw=e=>e.fold(R,R,R,R),pw=e=>e.fold(N("before"),N("start"),N("end"),N("after")),hw=e=>e.fold(rw.before,rw.before,rw.after,rw.after),bw=e=>e.fold(rw.start,rw.start,rw.end,rw.end),vw=(e,t,n,o,r,s)=>Pt(np(t,n,o),np(t,n,r),((t,o)=>t!==o&&((e,t,n)=>{const o=jd(t,e),r=jd(n,e);return o&&o===r})(n,t,o)?rw.after(e?t:o):s)).getOr(s),yw=(e,t)=>e.fold(L,(e=>{return o=t,!(pw(n=e)===pw(o)&&gw(n)===gw(o));var n,o})),Cw=(e,t)=>e?t.fold(_(M.some,rw.start),M.none,_(M.some,rw.after),M.none):t.fold(M.none,_(M.some,rw.before),M.none,_(M.some,rw.end)),ww=(e,t,n)=>{const o=e?1:-1;return t.setRng(Ya(n.container(),n.offset()+o).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0};var xw;!function(e){e[e.Br=0]="Br",e[e.Block=1]="Block",e[e.Wrap=2]="Wrap",e[e.Eol=3]="Eol"}(xw||(xw={}));const kw=(e,t)=>e===tc.Backwards?ne(t):t,Sw=(e,t,n)=>e===tc.Forwards?t.next(n):t.prev(n),_w=(e,t,n,o)=>Po(o.getNode(t===tc.Forwards))?xw.Br:!1===Vd(n,o)?xw.Block:xw.Wrap,Ew=(e,t,n,o)=>{const r=mc(n);let s=o;const a=[];for(;s;){const n=Sw(t,r,s);if(!n)break;if(Po(n.getNode(!1)))return t===tc.Forwards?{positions:kw(t,a).concat([n]),breakType:xw.Br,breakAt:M.some(n)}:{positions:kw(t,a),breakType:xw.Br,breakAt:M.some(n)};if(n.isVisible()){if(e(s,n)){const e=_w(0,t,s,n);return{positions:kw(t,a),breakType:e,breakAt:M.some(n)}}a.push(n),s=n}else s=n}return{positions:kw(t,a),breakType:xw.Eol,breakAt:M.none()}},Nw=(e,t,n,o)=>t(n,o).breakAt.map((o=>{const r=t(n,o).positions;return e===tc.Backwards?r.concat(o):[o].concat(r)})).getOr([]),Rw=(e,t)=>Y(e,((e,n)=>e.fold((()=>M.some(n)),(o=>Pt(ie(o.getClientRects()),ie(n.getClientRects()),((e,r)=>{const s=Math.abs(t-e.left);return Math.abs(t-r.left)<=s?n:o})).or(e)))),M.none()),Aw=(e,t)=>ie(t.getClientRects()).bind((t=>Rw(e,t.left))),Ow=O(Ew,Ya.isAbove,-1),Tw=O(Ew,Ya.isBelow,1),Bw=O(Nw,-1,Ow),Dw=O(Nw,1,Tw),Pw=(e,t)=>Aw(Bw(e,t),t),Lw=(e,t)=>Aw(Dw(e,t),t),Mw=Io,Iw=(e,t)=>Math.abs(e.left-t),Fw=(e,t)=>Math.abs(e.right-t),Uw=(e,t)=>Oe(e,((e,n)=>{const o=Math.min(Iw(e,t),Fw(e,t)),r=Math.min(Iw(n,t),Fw(n,t));return r===o&&ke(n,"node")&&Mw(n.node)||r<o?n:e})),zw=e=>{const t=t=>H(t,(t=>{const n=xa(t);return n.node=e,n}));if(Co(e))return t(e.getClientRects());if(Ro(e)){const n=e.ownerDocument.createRange();return n.setStart(e,0),n.setEnd(e,e.data.length),t(n.getClientRects())}return[]},jw=e=>ee(e,zw);var Vw;!function(e){e[e.Up=-1]="Up",e[e.Down=1]="Down"}(Vw||(Vw={}));const Hw=(e,t,n,o,r,s)=>{let a=0;const i=[],l=o=>{let s=jw([o]);-1===e&&(s=s.reverse());for(let e=0;e<s.length;e++){const o=s[e];if(!n(o,d)){if(i.length>0&&t(o,Be(i))&&a++,o.line=a,r(o))return!0;i.push(o)}}},d=Be(s.getClientRects());if(!d)return i;const c=s.getNode();return l(c),((e,t,n,o)=>{for(;o=zd(o,e,zr,t);)if(n(o))return})(e,o,l,c),i},$w=O(Hw,Vw.Up,_a,Ea),qw=O(Hw,Vw.Down,Ea,_a),Ww=e=>t=>((e,t)=>t.line>e)(e,t),Kw=e=>t=>((e,t)=>t.line===e)(e,t),Gw=(e,t)=>{e.selection.setRng(t),ef(e,e.selection.getRng())},Yw=(e,t,n)=>M.some(DC(e,t,n)),Xw=(e,t,n,o,r,s)=>{const a=t===tc.Forwards,i=mc(e.getBody()),l=O(Zd,a?i.next:i.prev),d=a?o:r;if(!n.collapsed){const o=Ra(n);if(s(o))return TC(t,e,o,t===tc.Backwards,!1);if(UC(e)){const e=n.cloneRange();return e.collapse(t===tc.Backwards),M.from(e)}}const c=Qd(t,e.getBody(),n);if(d(c))return BC(e,c.getNode(!a));const u=op(a,l(c)),m=Ar(n);if(!u)return m?M.some(n):M.none();if(d(u))return TC(t,e,u.getNode(!a),a,!1);const f=l(u);return f&&d(f)&&ec(u,f)?TC(t,e,f.getNode(!a),a,!1):m?Yw(e,u.toRange(),!1):M.none()},Qw=(e,t,n,o,r,s)=>{const a=Qd(t,e.getBody(),n),i=Be(a.getClientRects()),l=t===Vw.Down,d=e.getBody();if(!i)return M.none();if(UC(e)){const e=l?Ya.fromRangeEnd(n):Ya.fromRangeStart(n);return(l?Lw:Pw)(d,e).orThunk((()=>M.from(e))).map((e=>e.toRange()))}const c=(l?qw:$w)(d,Ww(1),a),u=K(c,Kw(1)),m=i.left,f=Uw(u,m);if(f&&s(f.node)){const n=Math.abs(m-f.left),o=Math.abs(m-f.right);return TC(t,e,f.node,n<o,!1)}let g;if(g=o(a)?a.getNode():r(a)?a.getNode(!0):Ra(n),g){const n=((e,t,n,o)=>{const r=mc(t);let s,a,i,l;const d=[];let c=0;const u=e=>Be(e.getClientRects());1===e?(s=r.next,a=Ea,i=_a,l=Ya.after(o)):(s=r.prev,a=_a,i=Ea,l=Ya.before(o));const m=u(l);do{if(!l.isVisible())continue;const e=u(l);if(i(e,m))continue;d.length>0&&a(e,Be(d))&&c++;const t=xa(e);if(t.position=l,t.line=c,n(t))return d;d.push(t)}while(l=s(l));return d})(t,d,Ww(1),g);let o=Uw(K(n,Kw(1)),m);if(o)return Yw(e,o.position.toRange(),!1);if(o=Be(K(n,Kw(0))),o)return Yw(e,o.position.toRange(),!1)}return 0===u.length?Jw(e,l).filter(l?r:o).map((t=>DC(e,t.toRange(),!1))):M.none()},Jw=(e,t)=>{const n=e.selection.getRng(),o=t?Ya.fromRangeEnd(n):Ya.fromRangeStart(n),r=(s=o.container(),a=e.getBody(),$o(fn(s),(e=>Ad(e.dom)),(e=>e.dom===a)).map((e=>e.dom)).getOr(a));var s,a;if(t){const e=Tw(r,o);return le(e.positions)}{const e=Ow(r,o);return ie(e.positions)}},Zw=(e,t,n)=>Jw(e,t).filter(n).exists((t=>(e.selection.setRng(t.toRange()),!0))),ex=(e,t)=>{const n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},tx=(e,t)=>{e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},nx=(e,t,n)=>nw(t,n).map((t=>(ex(e,t),n))),ox=(e,t,n)=>{const o=e.getBody(),r=((e,t,n)=>{const o=Ya.fromRangeStart(e);if(e.collapsed)return o;{const r=Ya.fromRangeEnd(e);return n?wc(t,r).getOr(r):Cc(t,o).getOr(o)}})(e.selection.getRng(),o,n);return((e,t,n,o)=>{const r=op(e,o),s=fw(t,n,r);return fw(t,n,r).bind(O(Cw,e)).orThunk((()=>((e,t,n,o,r)=>{const s=op(e,r);return hc(e,n,s).map(O(op,e)).fold((()=>o.map(hw)),(r=>fw(t,n,r).map(O(vw,e,t,n,s,r)).filter(O(yw,o)))).filter(mw)})(e,t,n,s,o)))})(n,O(tp,e),o,r).bind((n=>nx(e,t,n)))},rx=(e,t,n)=>!!kl(e)&&ox(e,t,n).isSome(),sx=(e,t,n)=>!!kl(t)&&((e,t)=>{const n=t.selection.getRng(),o=e?Ya.fromRangeEnd(n):Ya.fromRangeStart(n);return!!(e=>w(e.selection.getSel().modify))(t)&&(e&&Sr(o)?ww(!0,t.selection,o):!(e||!_r(o))&&ww(!1,t.selection,o))})(e,t),ax=e=>{const t=Ws(null),n=O(tp,e);return e.on("NodeChange",(o=>{kl(e)&&(((e,t,n)=>{const o=H(na(fn(t.getRoot()),'*[data-mce-selected="inline-boundary"]'),(e=>e.dom)),r=K(o,e),s=K(n,e);$(oe(r,s),O(tx,!1)),$(oe(s,r),O(tx,!0))})(n,e.dom,o.parents),((e,t)=>{if(e.selection.isCollapsed()&&!0!==e.composing&&t.get()){const n=Ya.fromRangeStart(e.selection.getRng());Ya.isTextPosition(n)&&!1===(e=>Sr(e)||_r(e))(n)&&(ex(e,yd(t.get(),n)),t.set(null))}})(e,t),((e,t,n,o)=>{if(t.selection.isCollapsed()){const r=K(o,e);$(r,(o=>{const r=Ya.fromRangeStart(t.selection.getRng());fw(e,t.getBody(),r).bind((e=>nx(t,n,e)))}))}})(n,e,t,o.parents))})),t},ix=O(sx,!0),lx=O(sx,!1),dx=(e,t,n)=>{if(kl(e)){const o=Jw(e,t).getOrThunk((()=>{const n=e.selection.getRng();return t?Ya.fromRangeEnd(n):Ya.fromRangeStart(n)}));return fw(O(tp,e),e.getBody(),o).exists((t=>{const o=hw(t);return nw(n,o).exists((t=>(ex(e,t),!0)))}))}return!1},cx=(e,t)=>n=>nw(t,n).map((t=>()=>ex(e,t))),ux=(e,t,n,o)=>{const r=e.getBody(),s=O(tp,e);e.undoManager.ignore((()=>{e.selection.setRng(((e,t)=>{const n=document.createRange();return n.setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n})(n,o)),ip(e),fw(s,r,Ya.fromRangeStart(e.selection.getRng())).map(bw).bind(cx(e,t)).each(D)})),e.nodeChanged()},mx=(e,t,n)=>{if(e.selection.isCollapsed()&&kl(e)){const o=Ya.fromRangeStart(e.selection.getRng());return((e,t,n,o)=>{const r=((e,t)=>jd(t,e)||e)(e.getBody(),o.container()),s=O(tp,e),a=fw(s,r,o);return a.bind((e=>n?e.fold(N(M.some(bw(e))),M.none,N(M.some(hw(e))),M.none):e.fold(M.none,N(M.some(hw(e))),M.none,N(M.some(bw(e)))))).map(cx(e,t)).getOrThunk((()=>{const i=bc(n,r,o),l=i.bind((e=>fw(s,r,e)));return Pt(a,l,(()=>np(s,r,o).bind((t=>(e=>Pt(xc(e),kc(e),((t,n)=>{const o=op(!0,t),r=op(!1,n);return Cc(e,o).forall((e=>e.isEqual(r)))})).getOr(!0))(t)?M.some((()=>{Zg(e,n,fn(t))})):M.none())))).getOrThunk((()=>l.bind((()=>i.map((r=>()=>{n?ux(e,t,o,r):ux(e,t,r,o)}))))))}))})(e,t,n,o)}return M.none()},fx=e=>1===Bn(e),gx=(e,t)=>{const n=fn(e.getBody()),o=fn(e.selection.getStart()),r=K(((e,t)=>{const n=hg(t,e);return J(n,er).fold(N(n),(e=>n.slice(0,e)))})(n,o),fx);return le(r).bind((n=>{const o=Ya.fromRangeStart(e.selection.getRng());return!((e,t,n)=>Pt(xc(n),kc(n),((o,r)=>{const s=op(!0,o),a=op(!1,r),i=op(!1,t);return e?Cc(n,i).exists((e=>e.isEqual(a)&&t.isEqual(s))):wc(n,i).exists((e=>e.isEqual(s)&&t.isEqual(a)))})).getOr(!0))(t,o,n.dom)||_c((s=n).dom)&&bh(s.dom)?M.none():M.some((()=>((e,t,n,o)=>{const r=O(_h,t),s=H(K(o,r),(e=>e.dom));if(0===s.length)Zg(t,e,n);else{const e=((e,t)=>{const n=yh(!1),o=kh(t,n.dom);return Xn(fn(e),n),no(fn(e)),Ya(o,0)})(n.dom,s);t.selection.setRng(e.toRange())}})(t,e,n,r)));var s}))},px=(e,t)=>e.selection.isCollapsed()?gx(e,t):M.none(),hx=(e,t,n)=>M.some((()=>{e._selectionOverrides.hideFakeCaret(),Zg(e,t,fn(n))})),bx=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=t?ig:lg,o=t?tc.Forwards:tc.Backwards,r=Qd(o,e.getBody(),e.selection.getRng());return n(r)?hx(e,t,r.getNode(!t)):M.from(op(t,r)).filter((e=>n(e)&&ec(r,e))).map((n=>()=>hx(e,t,n.getNode(!t))))})(e,t):((e,t)=>{const n=e.selection.getNode();return Uo(n)?hx(e,t,n):M.none()})(e,t),vx=e=>{const t=parseInt(e,10);return isNaN(t)?0:t},yx=(e,t)=>(e||"table"===Mt(t)?"margin":"padding")+("rtl"===qn(t,"direction")?"-right":"-left"),Cx=e=>{const t=xx(e);return!e.mode.isReadOnly()&&(t.length>1||((e,t)=>te(t,(t=>{const n=yx(il(e),t),o=Kn(t,n).map(vx).getOr(0);return"false"!==e.dom.getContentEditable(t.dom)&&o>0})))(e,t))},wx=e=>rr(e)||sr(e),xx=e=>{return K((t=e.selection.getSelectedBlocks(),H(t,fn)),(e=>!wx(e)&&!(e=>kn(e).exists(wx))(e)&&qo(e,(e=>Mo(e.dom)||Io(e.dom))).exists((e=>Mo(e.dom)))));var t},kx=(e,t)=>{const{dom:n}=e,o=ll(e),r=/[a-z%]+$/i.exec(o)[0],s=parseInt(o,10),a=il(e);$(xx(e),(e=>{((e,t,n,o,r,s)=>{const a=yx(n,fn(s));if("outdent"===t){const t=Math.max(0,vx(s.style[a])-o);e.setStyle(s,a,t?t+r:"")}else{const t=vx(s.style[a])+o+r;e.setStyle(s,a,t)}})(n,t,a,s,r,e.dom)}))},Sx=e=>kx(e,"outdent"),_x=e=>{if(e.selection.isCollapsed()&&Cx(e)){const t=e.dom,n=e.selection.getRng(),o=Ya.fromRangeStart(n),r=t.getParent(n.startContainer,t.isBlock);if(null!==r&&wg(fn(r),o))return M.some((()=>Sx(e)))}return M.none()},Ex=(e,t,n)=>ce([_x,qC,IC,(e,n)=>mx(e,t,n),_C,Ip,WC,bx,RC,px],(t=>t(e,n))),Nx=(e,t)=>{e.addCommand("delete",(()=>{((e,t)=>{Ex(e,t,!1).fold((()=>{ip(e),cp(e)}),D)})(e,t)})),e.addCommand("forwardDelete",(()=>{((e,t)=>{Ex(e,t,!0).fold((()=>(e=>ap(e,"ForwardDelete"))(e)),D)})(e,t)}))},Rx=e=>void 0===e.touches||1!==e.touches.length?M.none():M.some(e.touches[0]),Ax=(e,t)=>xe(e,t.nodeName),Ox=(e,t)=>!!Ro(t)||!!Co(t)&&!Ax(e,t)&&!Fc(t),Tx=(e,t)=>{if(Ro(t)){if(0===t.nodeValue.length)return!0;if(/^\s+$/.test(t.nodeValue)&&(!t.nextSibling||Ax(e,t.nextSibling)))return!0}return!1},Bx=e=>{const t=e.dom,n=e.selection,o=e.schema,r=o.getBlockElements();let s=n.getStart();const a=e.getBody();let i,l,d;const c=ji(e);if(!s||!Co(s))return;const u=a.nodeName.toLowerCase();if(!o.isValidChild(u,c.toLowerCase())||((e,t,n)=>V(pg(fn(n),fn(t)),(t=>Ax(e,t.dom))))(r,a,s))return;const m=n.getRng(),f=m.startContainer,g=m.startOffset,p=m.endContainer,h=m.endOffset,b=xf(e);for(s=a.firstChild;s;)if(Ox(r,s)){if(Tx(r,s)){l=s,s=s.nextSibling,t.remove(l);continue}i||(i=t.create(c,Vi(e)),s.parentNode.insertBefore(i,s),d=!0),l=s,s=s.nextSibling,i.appendChild(l)}else i=null,s=s.nextSibling;d&&b&&(m.setStart(f,g),m.setEnd(p,h),n.setRng(m),e.nodeChanged())},Dx=e=>t=>-1!==(" "+t.attr("class")+" ").indexOf(e),Px=(e,t,n)=>function(o){const r=arguments,s=r[r.length-2],a=s>0?t.charAt(s-1):"";if('"'===a)return o;if(">"===a){const e=t.lastIndexOf("<",s);if(-1!==e&&-1!==t.substring(e,s).indexOf('contenteditable="false"'))return o}return'<span class="'+n+'" data-mce-content="'+e.dom.encode(r[0])+'">'+e.dom.encode("string"==typeof r[1]?r[1]:r[0])+"</span>"},Lx=(e,t)=>{t.hasAttribute("data-mce-caret")&&(Rr(t),e.selection.setRng(e.selection.getRng()),e.selection.scrollIntoView(t))},Mx=(e,t)=>{const n=(e=>Ko(fn(e.getBody()),"*[data-mce-caret]").map((e=>e.dom)).getOrNull())(e);if(n)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void Lx(e,n)):void(kr(n)&&(Lx(e,n),e.undoManager.add()))},Ix=Io,Fx=(e,t,n)=>{const o=mc(e.getBody()),r=O(Zd,1===t?o.next:o.prev);if(n.collapsed){const o=e.dom.getParent(n.startContainer,"PRE");if(!o)return;if(!r(Ya.fromRangeStart(n))){const n=fn((e=>{const t=e.dom.create(ji(e));return t.innerHTML='<br data-mce-bogus="1">',t})(e));1===t?Qn(fn(o),n):Xn(fn(o),n),e.selection.select(n.dom,!0),e.selection.collapse()}}},Ux=(e,t)=>((e,t)=>{const n=t?tc.Forwards:tc.Backwards,o=e.selection.getRng();return((e,t,n)=>Xw(t,e,n,ug,mg,Ix))(n,e,o).orThunk((()=>(Fx(e,n,o),M.none())))})(e,t).exists((t=>(Gw(e,t),!0))),zx=(e,t)=>((e,t)=>{const n=t?1:-1,o=e.selection.getRng();return((e,t,n)=>Qw(t,e,n,(e=>ug(e)||dg(e)),(e=>mg(e)||cg(e)),Ix))(n,e,o).orThunk((()=>(Fx(e,n,o),M.none())))})(e,t).exists((t=>(Gw(e,t),!0))),jx=(e,t)=>Zw(e,t,t?mg:ug),Vx=(e,t)=>FC(e,!t).map((n=>{const o=n.toRange(),r=e.selection.getRng();return t?o.setStart(r.startContainer,r.startOffset):o.setEnd(r.endContainer,r.endOffset),o})).exists((t=>(Gw(e,t),!0))),Hx=e=>j(["figcaption"],Mt(e)),$x=(e,t)=>{const n=fn(e.getBody()),o=Ya.fromRangeStart(e.selection.getRng()),r=ji(e),s=Vi(e);return((e,t)=>{const n=O(vn,t);return qo(fn(e.container()),er,n).filter(Hx)})(o,n).exists((()=>{if(((e,t,n)=>t?((e,t)=>Tw(e,t).breakAt.isNone())(e.dom,n):((e,t)=>Ow(e,t).breakAt.isNone())(e.dom,n))(n,t,o)){const o=((e,t,n,o)=>{const r=un(n),s=un("br");return Wt(r,o),Zn(r,s),((e,t,n)=>{n?Zn(e,t):Jn(e,t)})(e,r,t),(e=>{const t=document.createRange();return t.setStartBefore(e.dom),t.setEndBefore(e.dom),t})(s)})(n,t,r,s);return e.selection.setRng(o),!0}return!1}))},qx=(e,t)=>!!e.selection.isCollapsed()&&$x(e,t),Wx={shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0},Kx=(e,t)=>t.keyCode===e.keyCode&&t.shiftKey===e.shiftKey&&t.altKey===e.altKey&&t.ctrlKey===e.ctrlKey&&t.metaKey===e.metaKey,Gx=(e,...t)=>()=>e.apply(null,t),Yx=(e,t)=>Q(((e,t)=>ee((e=>H(e,(e=>({...Wx,action:S,...e}))))(e),(e=>Kx(e,t)?[e]:[])))(e,t),(e=>e.action())),Xx=(e,t)=>ce(((e,t)=>ee((e=>H(e,(e=>({...Wx,action:()=>M.none(),...e}))))(e),(e=>Kx(e,t)?[e]:[])))(e,t),(e=>e.action())),Qx=(e,t)=>{const n=t?tc.Forwards:tc.Backwards,o=e.selection.getRng();return Xw(e,n,o,ig,lg,Uo).exists((t=>(Gw(e,t),!0)))},Jx=(e,t)=>{const n=t?1:-1,o=e.selection.getRng();return Qw(e,n,o,ig,lg,Uo).exists((t=>(Gw(e,t),!0)))},Zx=(e,t)=>Zw(e,t,t?lg:ig),ek=Ci([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),tk={...ek,none:e=>ek.none(e)},nk=(e,t,n)=>ee(Rn(e),(e=>hn(e,t)?n(e)?[e]:[]:nk(e,t,n))),ok=(e,t)=>Go(e,"table",t),rk=(e,t,n,o,r=L)=>{const s=1===o;if(!s&&n<=0)return tk.first(e[0]);if(s&&n>=e.length-1)return tk.last(e[e.length-1]);{const s=n+o,a=e[s];return r(a)?tk.middle(t,a):rk(e,t,s,o,r)}},sk=(e,t)=>ok(e,t).bind((t=>{const n=nk(t,"th,td",L);return J(n,(t=>vn(e,t))).map((e=>({index:e,all:n})))})),ak=(e,t=!1)=>{return jn(e)?e.dom.isContentEditable:(n=e,Go(n,"[contenteditable]")).fold(N(t),(e=>"true"===ik(e)));var n},ik=e=>e.dom.contentEditable,lk=(e,t,n,o,r)=>{const s=na(fn(n),"td,th,caption").map((e=>e.dom)),a=K(((e,t)=>ee(t,(t=>{const n=((e,t)=>({left:e.left-t,top:e.top-t,right:e.right+-2,bottom:e.bottom+-2,width:e.width+t,height:e.height+t}))(xa(t.getBoundingClientRect()),-1);return[{x:n.left,y:e(n),cell:t},{x:n.right,y:e(n),cell:t}]})))(e,s),(e=>t(e,r)));return((e,t,n)=>Y(e,((e,o)=>e.fold((()=>M.some(o)),(e=>{const r=Math.sqrt(Math.abs(e.x-t)+Math.abs(e.y-n)),s=Math.sqrt(Math.abs(o.x-t)+Math.abs(o.y-n));return M.some(s<r?o:e)}))),M.none()))(a,o,r).map((e=>e.cell))},dk=O(lk,(e=>e.bottom),((e,t)=>e.y<t)),ck=O(lk,(e=>e.top),((e,t)=>e.y>t)),uk=(e,t,n)=>{const o=e(t,n);return(e=>e.breakType===xw.Wrap&&0===e.positions.length)(o)||!Po(n.getNode())&&(e=>e.breakType===xw.Br&&1===e.positions.length)(o)?!((e,t,n)=>n.breakAt.exists((n=>e(t,n).breakAt.isSome())))(e,t,o):o.breakAt.isNone()},mk=O(uk,Ow),fk=O(uk,Tw),gk=(e,t,n,o)=>{const r=e.selection.getRng(),s=t?1:-1;return!(!Ed()||!((e,t,n)=>{const o=Ya.fromRangeStart(t);return yc(!e,n).exists((e=>e.isEqual(o)))})(t,r,n)||(TC(s,e,n,!t,!1).each((t=>{Gw(e,t)})),0))},pk=(e,t,n)=>{const o=((e,t)=>{const n=t.getNode(e);return Co(n)&&"TABLE"===n.nodeName?M.some(n):M.none()})(!!t,n),r=!1===t;o.fold((()=>Gw(e,n.toRange())),(o=>yc(r,e.getBody()).filter((e=>e.isEqual(n))).fold((()=>Gw(e,n.toRange())),(n=>((e,t,n)=>{const o=ji(t);t.undoManager.transact((()=>{const r=un(o);Wt(r,Vi(t)),Zn(r,un("br")),e?Qn(fn(n),r):Xn(fn(n),r);const s=t.dom.createRng();s.setStart(r.dom,0),s.setEnd(r.dom,0),Gw(t,s)}))})(t,e,o)))))},hk=(e,t,n,o)=>{const r=e.selection.getRng(),s=Ya.fromRangeStart(r),a=e.getBody();if(!t&&mk(o,s)){const o=((e,t,n)=>((e,t)=>ie(t.getClientRects()).bind((t=>dk(e,t.left,t.top))).bind((e=>{return Aw(kc(n=e).map((e=>Ow(n,e).positions.concat(e))).getOr([]),t);var n})))(t,n).orThunk((()=>ie(n.getClientRects()).bind((n=>Rw(Bw(e,Ya.before(t)),n.left))))).getOr(Ya.before(t)))(a,n,s);return pk(e,t,o),!0}if(t&&fk(o,s)){const o=((e,t,n)=>((e,t)=>le(t.getClientRects()).bind((t=>ck(e,t.left,t.top))).bind((e=>{return Aw(xc(n=e).map((e=>[e].concat(Tw(n,e).positions))).getOr([]),t);var n})))(t,n).orThunk((()=>ie(n.getClientRects()).bind((n=>Rw(Dw(e,Ya.after(t)),n.left))))).getOr(Ya.after(t)))(a,n,s);return pk(e,t,o),!0}return!1},bk=(e,t,n)=>M.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind((o=>M.from(e.dom.getParent(o,"table")).map((r=>n(e,t,r,o))))).getOr(!1),vk=(e,t)=>bk(e,t,gk),yk=(e,t)=>bk(e,t,hk),Ck=(e,t,n)=>n.fold(M.none,M.none,((e,t)=>{return(n=t,((e,t)=>{const n=e=>{for(let o=0;o<e.childNodes.length;o++){const r=fn(e.childNodes[o]);if(t(r))return M.some(r);const s=n(e.childNodes[o]);if(s.isSome())return s}return M.none()};return n(e.dom)})(n,Of)).map((e=>(e=>{const t=pm.exact(e,0,e,0);return Cm(t)})(e)));var n}),(n=>(e.execCommand("mceTableInsertRowAfter"),wk(e,t,n)))),wk=(e,t,n)=>{return Ck(e,t,(r=ak,sk(o=n,void 0).fold((()=>tk.none(o)),(e=>rk(e.all,o,e.index,1,r)))));var o,r},xk=(e,t,n)=>{return Ck(e,t,(r=ak,sk(o=n,void 0).fold((()=>tk.none()),(e=>rk(e.all,o,e.index,-1,r)))));var o,r},kk=(e,t)=>{const n=["table","li","dl"],o=fn(e.getBody()),r=e=>{const t=Mt(e);return vn(e,o)||j(n,t)},s=e.selection.getRng();return((e,t)=>((e,t,n=P)=>n(t)?M.none():j(e,Mt(t))?M.some(t):Wo(t,e.join(","),(e=>hn(e,"table")||n(e))))(["td","th"],e,t))(fn(t?s.endContainer:s.startContainer),r).map((n=>(ok(n,r).each((t=>{e.model.table.clearSelectedCells(t.dom)})),e.selection.collapse(!t),(t?wk:xk)(e,r,n).each((t=>{e.selection.setRng(t)})),!0))).getOr(!1)},Sk=(e,t)=>({container:e,offset:t}),_k=Hs.DOM,Ek=e=>t=>e===t?-1:0,Nk=(e,t,n)=>{if(Ro(e)&&t>=0)return M.some(Sk(e,t));{const o=Ca(_k);return M.from(o.backwards(e,t,Ek(e),n)).map((e=>Sk(e.container,e.container.data.length)))}},Rk=(e,t,n)=>{if(!Ro(e))return M.none();const o=e.textContent;if(t>=0&&t<=o.length)return M.some(Sk(e,t));{const o=Ca(_k);return M.from(o.backwards(e,t,Ek(e),n)).bind((e=>{const o=e.container.data;return Rk(e.container,t+o.length,n)}))}},Ak=(e,t,n)=>{if(!Ro(e))return M.none();const o=e.textContent;if(t<=o.length)return M.some(Sk(e,t));{const r=Ca(_k);return M.from(r.forwards(e,t,Ek(e),n)).bind((e=>Ak(e.container,t-o.length,n)))}},Ok=(e,t,n,o,r)=>{const s=Ca(e,(e=>t=>e.isBlock(t)||j(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===e.getContentEditable(t))(e));return M.from(s.backwards(t,n,o,r))},Tk=e=>e.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,""),Bk=e=>""!==e&&-1!==" \xa0\f\n\r\t\v".indexOf(e),Dk=(e,t)=>e.substring(t.length),Pk=(e,t,n,o=0)=>{return(r=fn(t.startContainer),Go(r,Tf)).fold((()=>((e,t,n,o=0)=>{if(!(r=t).collapsed||3!==r.startContainer.nodeType)return M.none();var r;const s=e.getParent(t.startContainer,e.isBlock)||e.getRoot();return Ok(e,t.startContainer,t.startOffset,((e,t,o)=>((e,t,n)=>{let o;for(o=t-1;o>=0;o--){const t=e.charAt(o);if(Bk(t))return M.none();if(t===n)break}return M.some(o)})(o,t,n).getOr(t)),s).bind((e=>{const r=t.cloneRange();if(r.setStart(e.container,e.offset),r.setEnd(t.endContainer,t.endOffset),r.collapsed)return M.none();const s=Tk(r);return 0!==s.lastIndexOf(n)||Dk(s,n).length<o?M.none():M.some({text:Dk(s,n),range:r,triggerChar:n})}))})(e,t,n,o)),(t=>{const o=e.createRng();o.selectNode(t.dom);const r=Tk(o);return M.some({range:o,text:Dk(r,n),triggerChar:n})}));var r},Lk=e=>{if((e=>3===e.nodeType)(e))return Sk(e,e.data.length);{const t=e.childNodes;return t.length>0?Lk(t[t.length-1]):Sk(e,t.length)}},Mk=(e,t)=>{const n=e.childNodes;return n.length>0&&t<n.length?Mk(n[t],0):n.length>0&&(e=>1===e.nodeType)(e)&&n.length===t?Lk(n[n.length-1]):Sk(e,t)},Ik=(e,t,n,o={})=>{const r=t(),s=e.selection.getRng().startContainer.nodeValue,a=K(r.lookupByChar(n.triggerChar),(t=>n.text.length>=t.minChars&&t.matches.getOrThunk((()=>(e=>t=>{const n=Mk(t.startContainer,t.startOffset);return!((e,t)=>{const n=e.getParent(t.container,e.isBlock);return Ok(e,t.container,t.offset,((e,t)=>0===t?-1:t),n).filter((e=>{const t=e.container.data.charAt(e.offset-1);return!Bk(t)})).isSome()})(e,n)})(e.dom)))(n.range,s,n.text)));if(0===a.length)return M.none();const i=Promise.all(H(a,(e=>e.fetch(n.text,e.maxResults,o).then((t=>({matchText:n.text,items:t,columns:e.columns,onAction:e.onAction,highlightOn:e.highlightOn}))))));return M.some({lookupData:i,context:n})};var Fk;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(Fk||(Fk={}));const Uk=(e,t,n)=>e.stype===Fk.Error?t(e.serror):n(e.svalue),zk=e=>({stype:Fk.Value,svalue:e}),jk=e=>({stype:Fk.Error,serror:e}),Vk=Uk,Hk=e=>f(e)&&ue(e).length>100?" removed due to size":JSON.stringify(e,null,2),$k=(e,t)=>jk([{path:e,getErrorInfo:t}]),qk=(e,t)=>({extract:(n,o)=>we(o,e).fold((()=>((e,t)=>$k(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(n,e)),(e=>((e,t,n,o)=>we(n,o).fold((()=>((e,t,n)=>$k(e,(()=>'The chosen schema: "'+n+'" did not exist in branches: '+Hk(t))))(e,n,o)),(n=>n.extract(e.concat(["branch: "+o]),t))))(n,o,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ue(t)}),Wk=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const n={};for(let o=0;o<t.length;o++){const r=t[o];for(const t in r)xe(r,t)&&(n[t]=e(n[t],r[t]))}return n},Kk=Wk(((e,t)=>g(e)&&g(t)?Kk(e,t):t)),Gk=(Wk(((e,t)=>t)),e=>({tag:"defaultedThunk",process:N(e)})),Yk=e=>{const t=(e=>{const t=[],n=[];return $(e,(e=>{Uk(e,(e=>n.push(e)),(e=>t.push(e)))})),{values:t,errors:n}})(e);return t.errors.length>0?(n=t.errors,_(jk,Z)(n)):zk(t.values);var n},Xk=(e,t,n)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return n(e.newKey,e.instantiator)}},Qk=e=>({extract:(t,n)=>{return o=e(n),r=e=>((e,t)=>$k(e,N(t)))(t,e),o.stype===Fk.Error?r(o.serror):o;var o,r},toString:N("val")}),Jk=Qk(zk),Zk=(e,t,n,o)=>o(we(e,t).getOrThunk((()=>n(e)))),eS=(e,t,n,o,r)=>{const s=e=>r.extract(t.concat([o]),e),a=e=>e.fold((()=>zk(M.none())),(e=>{const n=r.extract(t.concat([o]),e);return s=n,a=M.some,s.stype===Fk.Value?{stype:Fk.Value,svalue:a(s.svalue)}:s;var s,a}));switch(e.tag){case"required":return((e,t,n,o)=>we(t,n).fold((()=>((e,t,n)=>$k(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Hk(n))))(e,n,t)),o))(t,n,o,s);case"defaultedThunk":return Zk(n,o,e.process,s);case"option":return((e,t,n)=>n(we(e,t)))(n,o,a);case"defaultedOptionThunk":return((e,t,n,o)=>o(we(e,t).map((t=>!0===t?n(e):t))))(n,o,e.process,a);case"mergeWithThunk":return Zk(n,o,N({}),(t=>{const o=Kk(e.process(n),t);return s(o)}))}},tS=e=>({extract:(t,n)=>((e,t,n)=>{const o={},r=[];for(const s of n)Xk(s,((n,s,a,i)=>{const l=eS(a,e,t,n,i);Vk(l,(e=>{r.push(...e)}),(e=>{o[s]=e}))}),((e,n)=>{o[e]=n(t)}));return r.length>0?jk(r):zk(o)})(t,n,e),toString:()=>{const t=H(e,(e=>Xk(e,((e,t,n,o)=>e+" -> "+o.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),nS=e=>({extract:(t,n)=>{const o=H(n,((n,o)=>e.extract(t.concat(["["+o+"]"]),n)));return Yk(o)},toString:()=>"array("+e.toString()+")"}),oS=(e,t,n)=>{return o=((e,t,n)=>((e,t)=>e.stype===Fk.Error?{stype:Fk.Error,serror:t(e.serror)}:e)(t.extract([e],n),(e=>({input:n,errors:e}))))(e,t,n),Uk(o,yi.error,yi.value);var o},rS=(e,t)=>qk(e,ge(t,tS)),sS=N(Jk),aS=(e,t)=>Qk((n=>{const o=typeof n;return e(n)?zk(n):jk(`Expected type: ${t} but got: ${o}`)})),iS=aS(x,"number"),lS=aS(m,"string"),dS=aS(b,"boolean"),cS=aS(w,"function"),uS=(e,t,n,o)=>({tag:"field",key:e,newKey:t,presence:n,prop:o}),mS=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),fS=(e,t)=>uS(e,e,{tag:"required",process:{}},t),gS=e=>fS(e,lS),pS=e=>fS(e,cS),hS=(e,t)=>uS(e,e,{tag:"option",process:{}},t),bS=e=>hS(e,lS),vS=(e,t,n)=>uS(e,e,Gk(t),n),yS=(e,t)=>vS(e,t,iS),CS=(e,t,n)=>vS(e,t,(e=>{return t=t=>j(e,t)?yi.value(t):yi.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`),Qk((e=>t(e).fold(jk,zk)));var t})(n)),wS=(e,t)=>vS(e,t,dS),xS=(e,t)=>vS(e,t,cS),kS=gS("type"),SS=pS("fetch"),_S=pS("onAction"),ES=xS("onSetup",(()=>S)),NS=bS("text"),RS=bS("icon"),AS=bS("tooltip"),OS=bS("label"),TS=wS("active",!1),BS=wS("enabled",!0),DS=wS("primary",!1),PS=e=>((e,t)=>vS("type",t,lS))(0,e),LS=tS([kS,gS("ch"),yS("minChars",1),(1,((e,t)=>uS(e,e,Gk(1),sS()))("columns")),yS("maxResults",10),("matches",hS("matches",cS)),SS,_S,(MS=lS,vS("highlightOn",[],nS(MS)))]);var MS;const IS=[BS,AS,RS,NS,ES],FS=[TS].concat(IS),US=[xS("predicate",P),CS("scope","node",["node","editor"]),CS("position","selection",["node","selection","line"])],zS=IS.concat([PS("contextformbutton"),DS,_S,mS("original",R)]),jS=FS.concat([PS("contextformbutton"),DS,_S,mS("original",R)]),VS=IS.concat([PS("contextformbutton")]),HS=FS.concat([PS("contextformtogglebutton")]),$S=rS("type",{contextformbutton:zS,contextformtogglebutton:jS});tS([PS("contextform"),xS("initValue",N("")),OS,((e,t)=>uS(e,e,{tag:"required",process:{}},nS(t)))("commands",$S),hS("launch",rS("type",{contextformbutton:VS,contextformtogglebutton:HS}))].concat(US));const qS=e=>{const t=e.ui.registry.getAll().popups,n=ge(t,(e=>{return(t=e,oS("Autocompleter",LS,t)).fold((e=>{throw new Error("Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:N("... (only showing first ten failures)")}]):e;return H(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})((t=e).errors).join("\n")+"\n\nInput object: "+Hk(t.input));var t}),R);var t})),o=Se(ye(n,(e=>e.ch))),r=Ce(n);return{dataset:n,triggerChars:o,lookupByChar:e=>K(r,(t=>t.ch===e))}},WS=e=>{const t=Js(),n=Ws(!1),o=t.isSet,r=()=>{o()&&((e=>{cy(e).autocompleter.removeDecoration()})(e),(e=>{e.dispatch("AutocompleterEnd")})(e),n.set(!1),t.clear())},s=De((()=>qS(e))),a=a=>{(n=>t.get().map((t=>Pk(e.dom,e.selection.getRng(),t.triggerChar).bind((t=>Ik(e,s,t,n))))).getOrThunk((()=>((e,t)=>{const n=t(),o=e.selection.getRng();return((e,t,n)=>ce(n.triggerChars,(n=>Pk(e,t,n))))(e.dom,o,n).bind((n=>Ik(e,t,n)))})(e,s))))(a).fold(r,(s=>{(n=>{o()||(((e,t)=>{cy(e).autocompleter.addDecoration(t)})(e,n.range),t.set({triggerChar:n.triggerChar,matchLength:n.text.length}))})(s.context),s.lookupData.then((o=>{t.get().map((a=>{const i=s.context;a.triggerChar===i.triggerChar&&(i.text.length-a.matchLength>=10?r():(t.set({...a,matchLength:i.text.length}),n.get()?((e,t)=>{e.dispatch("AutocompleterUpdate",t)})(e,{lookupData:o}):(n.set(!0),((e,t)=>{e.dispatch("AutocompleterStart",t)})(e,{lookupData:o}))))}))}))}))};e.addCommand("mceAutocompleterReload",((e,t)=>{const n=f(t)?t.fetchOptions:{};a(n)})),e.addCommand("mceAutocompleterClose",r),((e,t)=>{const n=ea(t.load,50);e.on("keypress compositionend",(e=>{27!==e.which&&n.throttle()})),e.on("keydown",(e=>{const o=e.which;8===o?n.throttle():27===o&&t.cancelIfNecessary()})),e.on("remove",n.cancel)})(e,{cancelIfNecessary:r,load:a})},KS=e=>(t,n,o={})=>{const r=t.getBody(),s={bubbles:!0,composed:!0,data:null,isComposing:!1,detail:0,view:null,target:r,currentTarget:r,eventPhase:Event.AT_TARGET,originalTarget:r,explicitOriginalTarget:r,isTrusted:!1,srcElement:r,cancelable:!1,preventDefault:S,inputType:n},a=Ns(new InputEvent(e));return t.dispatch(e,{...a,...s,...o})},GS=KS("input"),YS=KS("beforeinput"),XS=(e,t)=>{let n,o=t;const r=e.dom,s=e.schema.getMoveCaretBeforeOnEnterElements();if(!t)return;if(/^(LI|DT|DD)$/.test(t.nodeName)){const e=(e=>{for(;e;){if(1===e.nodeType||3===e.nodeType&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}})(t.firstChild);e&&/^(UL|OL|DL)$/.test(e.nodeName)&&t.insertBefore(r.doc.createTextNode(fr),t.firstChild)}const a=r.createRng();if(t.normalize(),t.hasChildNodes()){const e=new Qo(t,t);for(;n=e.current();){if(Ro(n)){a.setStart(n,0),a.setEnd(n,0);break}if(s[n.nodeName.toLowerCase()]){a.setStartBefore(n),a.setEndBefore(n);break}o=n,n=e.next()}n||(a.setStart(o,0),a.setEnd(o,0))}else Po(t)?t.nextSibling&&r.isBlock(t.nextSibling)?(a.setStartBefore(t),a.setEndBefore(t)):(a.setStartAfter(t),a.setEndAfter(t)):(a.setStart(t,0),a.setEnd(t,0));e.selection.setRng(a),ef(e,a)},QS=e=>M.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock)),JS=(e,t)=>e&&e.parentNode&&e.parentNode.nodeName===t,ZS=e=>e&&/^(OL|UL|LI)$/.test(e.nodeName),e_=e=>{const t=e.parentNode;return/^(LI|DT|DD)$/.test(t.nodeName)?t:e},t_=(e,t,n)=>{let o=e[n?"firstChild":"lastChild"];for(;o&&!Co(o);)o=o[n?"nextSibling":"previousSibling"];return o===t},n_=(e,t)=>t&&"A"===t.nodeName&&e.isEmpty(t),o_=e=>{e.innerHTML='<br data-mce-bogus="1">'},r_=(e,t)=>e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t,s_=(e,t)=>t&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&"true"!==e.getContentEditable(t),a_=(e,t,n)=>!1===Ro(t)?n:e?1===n&&t.data.charAt(n-1)===pr?0:n:n===t.data.length-1&&t.data.charAt(n)===pr?t.data.length:n,i_=(e,t)=>{const n=e.getRoot();let o,r;for(o=t;o!==n&&"false"!==e.getContentEditable(o);)"true"===e.getContentEditable(o)&&(r=o),o=o.parentNode;return o!==n?r:n},l_=(e,t)=>{ji(e).toLowerCase()===t.tagName.toLowerCase()&&((e,t,n)=>{const o=e.dom;M.from(n.style).map(o.parseStyle).each((e=>{const n={...Gn(fn(t)),...e};o.setStyles(t,n)}));const r=M.from(n.class).map((e=>e.split(/\s+/))),s=M.from(t.className).map((e=>K(e.split(/\s+/),(e=>""!==e))));Pt(r,s,((e,n)=>{const r=K(n,(t=>!j(e,t))),s=[...e,...r];o.setAttrib(t,"class",s.join(" "))}));const a=["style","class"],i=ve(n,((e,t)=>!j(a,t)));o.setAttribs(t,i)})(e,t,Vi(e))},d_={insert:(e,t)=>{let n,o,r,s,a,i,l,d,c;const u=e.dom,f=e.schema,g=f.getNonEmptyElements(),p=e.selection.getRng(),h=ji(e),b=t=>{let n,r,a,i=o;const l=f.getTextInlineElements();if(n=t||"TABLE"===d||"HR"===d?u.create(t||h):s.cloneNode(!1),a=n,!1===Wi(e))u.setAttrib(n,"style",null),u.setAttrib(n,"class",null);else do{if(l[i.nodeName]){if(_c(i)||Fc(i))continue;r=i.cloneNode(!1),u.setAttrib(r,"id",""),n.hasChildNodes()?(r.appendChild(n.firstChild),n.appendChild(r)):(a=r,n.appendChild(r))}}while((i=i.parentNode)&&i!==k);return l_(e,n),o_(a),n},v=e=>{let t,n;const a=a_(e,o,r);if(Ro(o)&&(e?a>0:a<o.nodeValue.length))return!1;if(o.parentNode===s&&c&&!e)return!0;if(e&&Co(o)&&o===s.firstChild)return!0;if(r_(o,"TABLE")||r_(o,"HR"))return c&&!e||!c&&e;const i=new Qo(o,s);for(Ro(o)&&(e&&0===a?i.prev():e||a!==o.nodeValue.length||i.next());t=i.current();){if(Co(t)){if(!t.getAttribute("data-mce-bogus")&&(n=t.nodeName.toLowerCase(),g[n]&&"br"!==n))return!1}else if(Ro(t)&&!Vr(t.nodeValue))return!1;e?i.prev():i.next()}return!0},C=()=>{a=/^(H[1-6]|PRE|FIGURE)$/.test(d)&&"HGROUP"!==S?b(h):b(),((e,t)=>{const n=Ki(e);return!y(t)&&(m(n)?j(Bt.explode(n),t.nodeName.toLowerCase()):n)})(e,l)&&s_(u,l)&&u.isEmpty(s)?a=u.split(l,s):u.insertAfter(a,s),XS(e,a)};Tm(u,p).each((e=>{p.setStart(e.startContainer,e.startOffset),p.setEnd(e.endContainer,e.endOffset)})),o=p.startContainer,r=p.startOffset;const w=!(!t||!t.shiftKey),x=!(!t||!t.ctrlKey);Co(o)&&o.hasChildNodes()&&(c=r>o.childNodes.length-1,o=o.childNodes[Math.min(r,o.childNodes.length-1)]||o,r=c&&Ro(o)?o.nodeValue.length:0);const k=i_(u,o);if(!k)return;w||(o=((e,t,n,o,r)=>{let s,a,i,l,d,c;const u=e.dom,m=i_(u,o);if(a=u.getParent(o,u.isBlock),!a||!s_(u,a)){if(a=a||m,c=a===e.getBody()||(e=>e&&/^(TD|TH|CAPTION)$/.test(e.nodeName))(a)?a.nodeName.toLowerCase():a.parentNode.nodeName.toLowerCase(),!a.hasChildNodes())return s=u.create(t),l_(e,s),a.appendChild(s),n.setStart(s,0),n.setEnd(s,0),s;for(l=o;l.parentNode!==a;)l=l.parentNode;for(;l&&!u.isBlock(l);)i=l,l=l.previousSibling;if(i&&e.schema.isValidChild(c,t.toLowerCase())){for(s=u.create(t),l_(e,s),i.parentNode.insertBefore(s,i),l=i;l&&!u.isBlock(l);)d=l.nextSibling,s.appendChild(l),l=d;n.setStart(o,r),n.setEnd(o,r)}}return o})(e,h,p,o,r)),s=u.getParent(o,u.isBlock),l=s?u.getParent(s.parentNode,u.isBlock):null,d=s?s.nodeName.toUpperCase():"";const S=l?l.nodeName.toUpperCase():"";"LI"!==S||x||(s=l,l=l.parentNode,d=S),/^(LI|DT|DD)$/.test(d)&&u.isEmpty(s)?((e,t,n,o,r)=>{const s=e.dom,a=e.selection.getRng();if(n===e.getBody())return;var i;ZS(i=n)&&ZS(i.parentNode)&&(r="LI");let l=t(r);if(t_(n,o,!0)&&t_(n,o,!1))if(JS(n,"LI")){const e=e_(n);s.insertAfter(l,e),(e=>{var t;return(null===(t=e.parentNode)||void 0===t?void 0:t.firstChild)===e})(n)?s.remove(e):s.remove(n)}else s.replace(l,n);else if(t_(n,o,!0))JS(n,"LI")?(s.insertAfter(l,e_(n)),l.appendChild(s.doc.createTextNode(" ")),l.appendChild(n)):n.parentNode.insertBefore(l,n),s.remove(o);else if(t_(n,o,!1))s.insertAfter(l,e_(n)),s.remove(o);else{n=e_(n);const e=a.cloneRange();e.setStartAfter(o),e.setEndAfter(n);const t=e.extractContents();"LI"===r&&((e,t)=>e.firstChild&&"LI"===e.firstChild.nodeName)(t)?(l=t.firstChild,s.insertAfter(t,n)):(s.insertAfter(t,n),s.insertAfter(l,n)),s.remove(o)}XS(e,l)})(e,b,l,s,h):s!==e.getBody()&&(Cr(s)?(a=Rr(s),u.isEmpty(s)&&o_(s),l_(e,a),XS(e,a)):v()?C():v(!0)?(a=s.parentNode.insertBefore(b(),s),XS(e,r_(s,"HR")?a:s)):(n=(e=>{const t=e.cloneRange();return t.setStart(e.startContainer,a_(!0,e.startContainer,e.startOffset)),t.setEnd(e.endContainer,a_(!1,e.endContainer,e.endOffset)),t})(p).cloneRange(),n.setEndAfter(s),i=n.extractContents(),(e=>{$(ta(fn(e),zt),(e=>{const t=e.dom;t.nodeValue=br(t.nodeValue)}))})(i),(e=>{do{Ro(e)&&(e.nodeValue=e.nodeValue.replace(/^[\r\n]+/,"")),e=e.firstChild}while(e)})(i),a=i.firstChild,u.insertAfter(i,s),((e,t,n)=>{let o=n;const r=[];let s;if(o){for(;o=o.firstChild;){if(e.isBlock(o))return;Co(o)&&!t[o.nodeName.toLowerCase()]&&r.push(o)}for(s=r.length;s--;)o=r[s],(!o.hasChildNodes()||o.firstChild===o.lastChild&&""===o.firstChild.nodeValue||n_(e,o))&&e.remove(o)}})(u,g,a),((e,t)=>{t.normalize();const n=t.lastChild;n&&!/^(left|right)$/gi.test(e.getStyle(n,"float",!0))||e.add(t,"br")})(u,s),u.isEmpty(s)&&o_(s),a.normalize(),u.isEmpty(a)?(u.remove(a),C()):(l_(e,a),XS(e,a))),u.setAttrib(a,"id",""),e.dispatch("NewBlock",{newBlock:a}))},fakeEventName:"insertParagraph"},c_=(e,t,n)=>{const o=e.dom.createRng();n?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),e.selection.setRng(o),ef(e,o)},u_=(e,t)=>{const n=un("br");Xn(fn(t),n),e.undoManager.add()},m_=(e,t)=>{f_(e.getBody(),t)||Qn(fn(t),un("br"));const n=un("br");Qn(fn(t),n),c_(e,n.dom,!1),e.undoManager.add()},f_=(e,t)=>{return n=Ya.after(t),!!Po(n.getNode())||Cc(e,Ya.after(t)).map((e=>Po(e.getNode()))).getOr(!1);var n},g_=e=>e&&"A"===e.nodeName&&"href"in e,p_=e=>e.fold(P,g_,g_,P),h_=(e,t)=>{t.fold(S,O(u_,e),O(m_,e),S)},b_={insert:(e,t)=>{const n=(e=>{const t=O(tp,e),n=Ya.fromRangeStart(e.selection.getRng());return fw(t,e.getBody(),n).filter(p_)})(e);n.isSome()?n.each(O(h_,e)):((e,t)=>{const n=e.selection,o=e.dom,r=n.getRng();let s,a;Tm(o,r).each((e=>{r.setStart(e.startContainer,e.startOffset),r.setEnd(e.endContainer,e.endOffset)}));let i=r.startOffset,l=r.startContainer;if(1===l.nodeType&&l.hasChildNodes()){const e=i>l.childNodes.length-1;l=l.childNodes[Math.min(i,l.childNodes.length-1)]||l,i=e&&3===l.nodeType?l.nodeValue.length:0}let d=o.getParent(l,o.isBlock);const c=d?o.getParent(d.parentNode,o.isBlock):null,u=c?c.nodeName.toUpperCase():"",m=!(!t||!t.ctrlKey);"LI"!==u||m||(d=c),l&&3===l.nodeType&&i>=l.nodeValue.length&&(((e,t,n)=>{const o=new Qo(t,n);let r;const s=e.getNonEmptyElements();for(;r=o.next();)if(s[r.nodeName.toLowerCase()]||r.length>0)return!0})(e.schema,l,d)||(s=o.create("br"),r.insertNode(s),r.setStartAfter(s),r.setEndAfter(s),a=!0)),s=o.create("br"),Qa(o,r,s),c_(e,s,a),e.undoManager.add()})(e,t)},fakeEventName:"insertLineBreak"},v_=(e,t)=>QS(e).filter((e=>t.length>0&&hn(fn(e),t))).isSome(),y_=Ci([{br:[]},{block:[]},{none:[]}]),C_=(e,t)=>(e=>v_(e,qi(e)))(e),w_=e=>(t,n)=>(e=>QS(e).filter((e=>sr(fn(e)))).isSome())(t)===e,x_=(e,t)=>(n,o)=>{const r=(e=>QS(e).fold(N(""),(e=>e.nodeName.toUpperCase())))(n)===e.toUpperCase();return r===t},k_=e=>x_("pre",e),S_=e=>(t,n)=>zi(t)===e,__=(e,t)=>(e=>v_(e,$i(e)))(e),E_=(e,t)=>t,N_=e=>{const t=ji(e),n=((e,t)=>{const n=e.getRoot();let o,r;for(o=t;o!==n&&"false"!==e.getContentEditable(o);)"true"===e.getContentEditable(o)&&(r=o),o=o.parentNode;return o!==n?r:n})(e.dom,e.selection.getStart());return n&&e.schema.isValidChild(n.nodeName,t)},R_=(e,t)=>(n,o)=>Y(e,((e,t)=>e&&t(n,o)),!0)?M.some(t):M.none(),A_=(e,t,n)=>{t.selection.isCollapsed()||ip(t),C(n)&&YS(t,e.fakeEventName).isDefaultPrevented()||(e.insert(t,n),C(n)&&GS(t,e.fakeEventName))},O_=(e,t)=>{const n=()=>A_(b_,e,t),o=()=>A_(d_,e,t),r=((e,t)=>ow([R_([C_],y_.none()),R_([x_("summary",!0)],y_.br()),R_([k_(!0),S_(!1),E_],y_.br()),R_([k_(!0),S_(!1)],y_.block()),R_([k_(!0),S_(!0),E_],y_.block()),R_([k_(!0),S_(!0)],y_.br()),R_([w_(!0),E_],y_.br()),R_([w_(!0)],y_.block()),R_([__],y_.br()),R_([E_],y_.br()),R_([N_],y_.block())],[e,!(!t||!t.shiftKey)]).getOr(y_.none()))(e,t);switch(Hi(e)){case"linebreak":r.fold(n,n,S);break;case"block":r.fold(o,o,S);break;case"invert":r.fold(o,n,S);break;default:r.fold(n,o,S)}},T_=Ct(),B_=e=>e.stopImmediatePropagation(),D_=e=>e.keyCode===em.PAGE_UP||e.keyCode===em.PAGE_DOWN,P_=(e,t,n)=>{n&&!e.get()?t.on("NodeChange",B_,!0):!n&&e.get()&&t.off("NodeChange",B_),e.set(n)},L_=(e,t)=>{const n=t.container(),o=t.offset();return Ro(n)?(n.insertData(o,e),M.some(Ya(n,o+e.length))):Jd(t).map((n=>{const o=mn(e);return t.isAtEnd()?Qn(n,o):Xn(n,o),Ya(o.dom,e.length)}))},M_=O(L_,fr),I_=O(L_," "),F_=(e,t)=>n=>((e,t)=>!Lg(t)&&(((e,t)=>((e,t)=>wc(e.dom,t).isNone())(e,t)||((e,t)=>Cc(e.dom,t).isNone())(e,t)||wg(e,t)||xg(e,t)||Rg(e,t)||Ng(e,t))(e,t)||Dg(e,t)||Pg(e,t)))(e,n)?M_(t):I_(t),U_=e=>{const t=Ya.fromRangeStart(e.selection.getRng()),n=fn(e.getBody());if(e.selection.isCollapsed()){const o=O(tp,e),r=Ya.fromRangeStart(e.selection.getRng());return fw(o,e.getBody(),r).bind((e=>t=>t.fold((t=>wc(e.dom,Ya.before(t))),(e=>xc(e)),(e=>kc(e)),(t=>Cc(e.dom,Ya.after(t)))))(n)).map((o=>()=>F_(n,t)(o).each((e=>t=>(e.selection.setRng(t.toRange()),e.nodeChanged(),!0))(e))))}return M.none()},z_=e=>md(e)?[{keyCode:em.TAB,action:Gx(kk,e,!0)},{keyCode:em.TAB,shiftKey:!0,action:Gx(kk,e,!1)}]:[],j_=e=>{if(e.addShortcut("Meta+P","","mcePrint"),WS(e),ly(e))return Ws(null);{const t=ax(e);return(e=>{e.on("keyup compositionstart",O(Mx,e))})(e),((e,t)=>{e.on("keydown",(n=>{!1===n.isDefaultPrevented()&&((e,t,n)=>{const o=Nt.os.isMacOS()||Nt.os.isiOS();Yx([{keyCode:em.RIGHT,action:Gx(Ux,e,!0)},{keyCode:em.LEFT,action:Gx(Ux,e,!1)},{keyCode:em.UP,action:Gx(zx,e,!1)},{keyCode:em.DOWN,action:Gx(zx,e,!0)},...o?[{keyCode:em.UP,action:Gx(Vx,e,!1),metaKey:!0,shiftKey:!0},{keyCode:em.DOWN,action:Gx(Vx,e,!0),metaKey:!0,shiftKey:!0}]:[],{keyCode:em.RIGHT,action:Gx(vk,e,!0)},{keyCode:em.LEFT,action:Gx(vk,e,!1)},{keyCode:em.UP,action:Gx(yk,e,!1)},{keyCode:em.DOWN,action:Gx(yk,e,!0)},{keyCode:em.RIGHT,action:Gx(Qx,e,!0)},{keyCode:em.LEFT,action:Gx(Qx,e,!1)},{keyCode:em.UP,action:Gx(Jx,e,!1)},{keyCode:em.DOWN,action:Gx(Jx,e,!0)},{keyCode:em.RIGHT,action:Gx(rx,e,t,!0)},{keyCode:em.LEFT,action:Gx(rx,e,t,!1)},{keyCode:em.RIGHT,ctrlKey:!o,altKey:o,action:Gx(ix,e,t)},{keyCode:em.LEFT,ctrlKey:!o,altKey:o,action:Gx(lx,e,t)},{keyCode:em.UP,action:Gx(qx,e,!1)},{keyCode:em.DOWN,action:Gx(qx,e,!0)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{e.on("keydown",(n=>{!1===n.isDefaultPrevented()&&((e,t,n)=>{const o=n.keyCode===em.BACKSPACE?"deleteContentBackward":"deleteContentForward";Xx([{keyCode:em.BACKSPACE,action:Gx(_x,e)},{keyCode:em.BACKSPACE,action:Gx(qC,e,!1)},{keyCode:em.DELETE,action:Gx(qC,e,!0)},{keyCode:em.BACKSPACE,action:Gx(IC,e,!1)},{keyCode:em.DELETE,action:Gx(IC,e,!0)},{keyCode:em.BACKSPACE,action:Gx(mx,e,t,!1)},{keyCode:em.DELETE,action:Gx(mx,e,t,!0)},{keyCode:em.BACKSPACE,action:Gx(Ip,e,!1)},{keyCode:em.DELETE,action:Gx(Ip,e,!0)},{keyCode:em.BACKSPACE,action:Gx(WC,e,!1)},{keyCode:em.DELETE,action:Gx(WC,e,!0)},{keyCode:em.BACKSPACE,action:Gx(bx,e,!1)},{keyCode:em.DELETE,action:Gx(bx,e,!0)},{keyCode:em.BACKSPACE,action:Gx(RC,e,!1)},{keyCode:em.DELETE,action:Gx(RC,e,!0)},{keyCode:em.BACKSPACE,action:Gx(_C,e,!1)},{keyCode:em.DELETE,action:Gx(_C,e,!0)},{keyCode:em.BACKSPACE,action:Gx(px,e,!1)},{keyCode:em.DELETE,action:Gx(px,e,!0)}],n).each((t=>{n.preventDefault(),YS(e,o).isDefaultPrevented()||(t(),GS(e,o))}))})(e,t,n)})),e.on("keyup",(t=>{!1===t.isDefaultPrevented()&&((e,t)=>{Yx([{keyCode:em.BACKSPACE,action:Gx($C,e)},{keyCode:em.DELETE,action:Gx($C,e)}],t)})(e,t)}))})(e,t),(e=>{e.on("keydown",(t=>{t.keyCode===em.ENTER&&((e,t)=>{var n;t.isDefaultPrevented()||(t.preventDefault(),(n=e.undoManager).typing&&(n.typing=!1,n.add()),e.undoManager.transact((()=>{O_(e,t)})))})(e,t)}))})(e),(e=>{e.on("keydown",(t=>{!1===t.isDefaultPrevented()&&((e,t)=>{Xx([{keyCode:em.SPACEBAR,action:Gx(U_,e)}],t).each((n=>{t.preventDefault(),YS(e,"insertText",{data:" "}).isDefaultPrevented()||(n(),GS(e,"insertText",{data:" "}))}))})(e,t)}))})(e),(e=>{e.on("input",(t=>{!1===t.isComposing&&(e=>{const t=fn(e.getBody());e.selection.isCollapsed()&&jg(t,Ya.fromRangeStart(e.selection.getRng())).each((t=>{e.selection.setRng(t.toRange())}))})(e)}))})(e),(e=>{e.on("keydown",(t=>{!1===t.isDefaultPrevented()&&((e,t)=>{Yx([...z_(e)],t).each((e=>{t.preventDefault()}))})(e,t)}))})(e),((e,t)=>{e.on("keydown",(n=>{!1===n.isDefaultPrevented()&&((e,t,n)=>{const o=Nt.os.isMacOS()||Nt.os.isiOS();Yx([{keyCode:em.END,action:Gx(jx,e,!0)},{keyCode:em.HOME,action:Gx(jx,e,!1)},...o?[]:[{keyCode:em.HOME,action:Gx(Vx,e,!1),ctrlKey:!0,shiftKey:!0},{keyCode:em.END,action:Gx(Vx,e,!0),ctrlKey:!0,shiftKey:!0}],{keyCode:em.END,action:Gx(Zx,e,!0)},{keyCode:em.HOME,action:Gx(Zx,e,!1)},{keyCode:em.END,action:Gx(dx,e,!0,t)},{keyCode:em.HOME,action:Gx(dx,e,!1,t)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{if(T_.os.isMacOS())return;const n=Ws(!1);e.on("keydown",(t=>{D_(t)&&P_(n,e,!0)})),e.on("keyup",(o=>{!1===o.isDefaultPrevented()&&((e,t,n)=>{Yx([{keyCode:em.PAGE_UP,action:Gx(dx,e,!1,t)},{keyCode:em.PAGE_DOWN,action:Gx(dx,e,!0,t)}],n)})(e,t,o),D_(o)&&n.get()&&(P_(n,e,!1),e.nodeChanged())}))})(e,t),t}};class V_{constructor(e){let t;this.lastPath=[],this.editor=e;const n=this;"onselectionchange"in e.getDoc()||e.on("NodeChange click mouseup keyup focus",(n=>{const o=e.selection.getRng(),r={startContainer:o.startContainer,startOffset:o.startOffset,endContainer:o.endContainer,endOffset:o.endOffset};"nodechange"!==n.type&&xm(r,t)||e.dispatch("SelectionChange"),t=r})),e.on("contextmenu",(()=>{e.dispatch("SelectionChange")})),e.on("SelectionChange",(()=>{const t=e.selection.getStart(!0);t&&Lu(e)&&!n.isSameElementPath(t)&&e.dom.isChildOf(t,e.getBody())&&e.nodeChanged({selectionChange:!0})})),e.on("mouseup",(t=>{!t.isDefaultPrevented()&&Lu(e)&&("IMG"===e.selection.getNode().nodeName?gf.setEditorTimeout(e,(()=>{e.nodeChanged()})):e.nodeChanged())}))}nodeChanged(e){const t=this.editor.selection;let n,o,r;this.editor.initialized&&t&&!Pl(this.editor)&&!this.editor.mode.isReadOnly()&&(r=this.editor.getBody(),n=t.getStart(!0)||r,n.ownerDocument===this.editor.getDoc()&&this.editor.dom.isChildOf(n,r)||(n=r),o=[],this.editor.dom.getParent(n,(e=>{if(e===r)return!0;o.push(e)})),(e=e||{}).element=n,e.parents=o,this.editor.dispatch("NodeChange",e))}isSameElementPath(e){let t;const n=this.editor,o=ne(n.dom.getParents(e,L,n.getBody()));if(o.length===this.lastPath.length){for(t=o.length;t>=0&&o[t]===this.lastPath[t];t--);if(-1===t)return this.lastPath=o,!0}return this.lastPath=o,!1}}const H_=N("x-tinymce/html"),$_="\x3c!-- x-tinymce/html --\x3e",q_=e=>$_+e,W_=e=>-1!==e.indexOf($_),K_="%MCEPASTEBIN%",G_=e=>e.dom.get("mcepastebin"),Y_=e=>e&&"mcepastebin"===e.id,X_=e=>e===K_,Q_=(e,t)=>(Bt.each(t,(t=>{e=u(t,RegExp)?e.replace(t,""):e.replace(t[0],t[1])})),e),J_=e=>Q_(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,(e,t,n)=>t||n?fr:" "],/<br class="Apple-interchange-newline">/g,/<br>$/i]),Z_=(e,t)=>({content:e,cancelled:t}),eE=(e,t)=>(e.insertContent(t,{merge:nd(e),paste:!0}),!0),tE=e=>/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(e),nE=(e,t,n)=>!(e.selection.isCollapsed()||!tE(t))&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.execCommand("mceInsertLink",!1,t)})),!0))(e,t,n),oE=(e,t,n)=>!!((e,t)=>tE(t)&&V(ud(e),(e=>je(t.toLowerCase(),`.${e.toLowerCase()}`))))(e,t)&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.insertContent('<img src="'+t+'">')})),!0))(e,t,n),rE=(e=>{let t=0;return()=>"mceclip"+t++})(),sE=(e,t,n,o)=>{const r=((e,t,n)=>((e,t,n)=>{const o=((e,t,n)=>e.dispatch("PastePreProcess",{content:t,internal:n}))(e,t,n),r=((e,t)=>{const n=Av({},e.schema);n.addNodeFilter("meta",(e=>{Bt.each(e,(e=>{e.remove()}))}));const o=n.parse(t,{forced_root_block:!1,isRootContent:!0});return Kf({validate:!0},e.schema).serialize(o)})(e,o.content);return e.hasEventListeners("PastePostProcess")&&!o.isDefaultPrevented()?((e,t,n)=>{const o=e.dom.create("div",{style:"display:none"},t),r=((e,t,n)=>e.dispatch("PastePostProcess",{node:t,internal:n}))(e,o,n);return Z_(r.node.innerHTML,r.isDefaultPrevented())})(e,r,n):Z_(r,o.isDefaultPrevented())})(e,t,n))(e,t,n);!1===r.cancelled&&((e,t,n)=>{n||!od(e)?eE(e,t):((e,t)=>{Bt.each([nE,oE,eE],(n=>!0!==n(e,t,eE)))})(e,t)})(e,r.content,o)},aE=(e,t,n)=>{const o=n||W_(t);sE(e,(e=>e.replace($_,""))(t),o,!1)},iE=(e,t)=>{const n=e.dom.encode(t).replace(/\r\n/g,"\n"),o=((e,t,n)=>{const o=e.split(/\n\n/),r=((e,t)=>{let n="<"+e;const o=ye(t,((e,t)=>t+'="'+ms.encodeAllRaw(e)+'"'));return o.length&&(n+=" "+o.join(" ")),n+">"})(t,n),s="</"+t+">",a=H(o,(e=>e.split(/\n/).join("<br />")));return 1===a.length?a[0]:H(a,(e=>r+e+s)).join("")})($r(n,sd(e)),ji(e),Vi(e));sE(e,o,!1,!0)},lE=e=>{const t={};if(e&&e.types)for(let n=0;n<e.types.length;n++){const o=e.types[n];try{t[o]=e.getData(o)}catch(e){t[o]=""}}return t},dE=(e,t)=>t in e&&e[t].length>0,cE=e=>dE(e,"text/html")||dE(e,"text/plain"),uE=(e,t,n)=>{const o="paste"===t.type?t.clipboardData:t.dataTransfer;var r;if(Ql(e)&&o){const s=((e,t)=>{const n=t.items?ee(de(t.items),(e=>"file"===e.kind?[e.getAsFile()]:[])):[],o=t.files?de(t.files):[];return K(n.length>0?n:o,(e=>{const t=ud(e);return e=>ze(e.type,"image/")&&V(t,(t=>(e=>{const t=e.toLowerCase(),n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"};return Bt.hasOwn(n,t)?"image/"+n[t]:"image/"+t})(t)===e.type))})(e))})(e,o);if(s.length>0)return t.preventDefault(),(r=s,Promise.all(H(r,(e=>cv(e).then((t=>({file:e,uri:t}))))))).then((t=>{n&&e.selection.setRng(n),$(t,(t=>{((e,t)=>{lv(t.uri).each((({data:n,type:o,base64Encoded:r})=>{const s=r?n:btoa(n),a=t.file,i=e.editorUpload.blobCache,l=i.getByData(s,o),d=null!=l?l:((e,t,n,o)=>{const r=rE(),s=Yi(e)&&C(n.name),a=s?((e,t)=>{const n=t.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i);return C(n)?e.dom.encode(n[1]):null})(e,n.name):r,i=s?n.name:void 0,l=t.create(r,n,o,a,i);return t.add(l),l})(e,i,a,s);aE(e,`<img src="${d.blobUri()}">`,!1)}))})(e,t)}))})),!0}return!1},mE=(e,t,n,o)=>{let r=J_(n);const s=dE(t,H_())||W_(n),a=!s&&(e=>!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(e))(r),i=tE(r);(X_(r)||!r.length||a&&!i)&&(o=!0),(o||i)&&(r=dE(t,"text/plain")&&a?t["text/plain"]:(e=>{const t=Ss(),n=Av({},t);let o="";const r=t.getVoidElements(),s=Bt.makeMap("script noscript style textarea video audio iframe object"," "),a=t.getBlockElements(),i=e=>{const n=e.name,l=e;if("br"!==n){if("wbr"!==n)if(r[n]&&(o+=" "),s[n])o+=" ";else{if(3===e.type&&(o+=e.value),!(e.name in t.getVoidElements())&&(e=e.firstChild))do{i(e)}while(e=e.next);a[n]&&l.next&&(o+="\n","p"===n&&(o+="\n"))}}else o+="\n"};return e=Q_(e,[/<!\[[^\]]+\]>/g]),i(n.parse(e)),o})(r)),X_(r)||(o?iE(e,r):aE(e,r,s))},fE=(e,t,n)=>{((e,t,n)=>{let o;e.on("keydown",(e=>{(e=>em.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode)(e)&&!e.isDefaultPrevented()&&(o=e.shiftKey&&86===e.keyCode)})),e.on("paste",(r=>{if(r.isDefaultPrevented()||(e=>{var t,n;return Nt.os.isAndroid()&&0===(null===(n=null===(t=e.clipboardData)||void 0===t?void 0:t.items)||void 0===n?void 0:n.length)})(r))return;const s="text"===n.get()||o;o=!1;const a=lE(r.clipboardData);!cE(a)&&uE(e,r,t.getLastRng()||e.selection.getRng())||(dE(a,"text/html")?(r.preventDefault(),mE(e,a,a["text/html"],s)):(t.create(),gf.setEditorTimeout(e,(()=>{const n=t.getHtml();t.remove(),mE(e,a,n,s)}),0)))}))})(e,t,n),(e=>{const t=e=>ze(e,"webkit-fake-url"),n=e=>ze(e,"data:");e.parser.addNodeFilter("img",((o,r,s)=>{if(!Ql(e)&&(e=>{var t;return!0===(null===(t=e.data)||void 0===t?void 0:t.paste)})(s))for(const r of o){const o=r.attr("src");m(o)&&!r.attr("data-mce-object")&&o!==Nt.transparentSrc&&(t(o)||!ad(e)&&n(o))&&r.remove()}}))})(e)},gE=(e,t,n,o)=>{((e,t,n)=>{try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(H_(),t),!0}catch(e){return!1}})(e.clipboardData,t.html,t.text)?(e.preventDefault(),o()):n(t.html,o)},pE=e=>(t,n)=>{const{dom:o,selection:r}=e,s=o.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=o.create("div",{contenteditable:"true"},t);o.setStyles(s,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),s.appendChild(a),o.add(e.getBody(),s);const i=r.getRng();a.focus();const l=o.createRng();l.selectNodeContents(a),r.setRng(l),gf.setEditorTimeout(e,(()=>{r.setRng(i),o.remove(s),n()}),0)},hE=e=>({html:q_(e.selection.getContent({contextual:!0})),text:e.selection.getContent({format:"text"})}),bE=e=>!e.selection.isCollapsed()||(e=>!!e.dom.getParent(e.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",e.getBody()))(e),vE=(e,t)=>{var n,o;return Pm.getCaretRangeFromPoint(null!==(n=t.clientX)&&void 0!==n?n:0,null!==(o=t.clientY)&&void 0!==o?o:0,e.getDoc())},yE=(e,t)=>{e.focus(),t&&e.selection.setRng(t)},CE=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,wE=e=>Bt.trim(e).replace(CE,Gc).toLowerCase(),xE=(e,t,n)=>{const o=ed(e);if(n||"all"===o||!td(e))return t;const r=o?o.split(/[, ]/):[];if(r&&"none"!==o){const n=e.dom,o=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,((e,t,s,a)=>{const i=n.parseStyle(n.decode(s)),l={};for(let e=0;e<r.length;e++){const t=i[r[e]];let s=t,a=n.getStyle(o,r[e],!0);/color/.test(r[e])&&(s=wE(s),a=wE(a)),a!==s&&(l[r[e]]=t)}const d=n.serializeStyle(l,"span");return d?t+' style="'+d+'"'+a:t+a}))}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,((e,t,n,o)=>t+' style="'+n+'"'+o)),t},kE=e=>{const t=Ws(!1),n=Ws(rd(e)?"text":"html"),o=(e=>{const t=Ws(null);return{create:()=>((e,t)=>{const{dom:n,selection:o}=e,r=e.getBody();t.set(o.getRng());const s=n.add(e.getBody(),"div",{id:"mcepastebin",class:"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},K_);Nt.browser.isFirefox()&&n.setStyle(s,"left","rtl"===n.getStyle(r,"direction",!0)?65535:-65535),n.bind(s,"beforedeactivate focusin focusout",(e=>{e.stopPropagation()})),s.focus(),o.select(s,!0)})(e,t),remove:()=>((e,t)=>{const n=e.dom;if(G_(e)){let o;const r=t.get();for(;o=G_(e);)n.remove(o),n.unbind(o);r&&e.selection.setRng(r)}t.set(null)})(e,t),getEl:()=>G_(e),getHtml:()=>(e=>{const t=e.dom,n=(e,n)=>{e.appendChild(n),t.remove(n,!0)},[o,...r]=K(e.getBody().childNodes,Y_);$(r,(e=>{n(o,e)}));const s=t.select("div[id=mcepastebin]",o);for(let e=s.length-1;e>=0;e--){const r=t.create("div");o.insertBefore(r,s[e]),n(r,s[e])}return o?o.innerHTML:""})(e),getLastRng:t.get}})(e);(e=>{(Nt.browser.isChromium()||Nt.browser.isSafari())&&((e,t)=>{e.on("PastePreProcess",(n=>{n.content=t(e,n.content,n.internal)}))})(e,xE)})(e),((e,t)=>{e.addCommand("mceTogglePlainTextPaste",(()=>{((e,t)=>{"text"===t.get()?(t.set("html"),Zu(e,!1)):(t.set("text"),Zu(e,!0)),e.focus()})(e,t)})),e.addCommand("mceInsertClipboardContent",((t,n)=>{n.html&&aE(e,n.html,n.internal),n.text&&iE(e,n.text)}))})(e,n),(e=>{const t=t=>n=>{t(e,n)},n=Jl(e);w(n)&&e.on("PastePreProcess",t(n));const o=Zl(e);w(o)&&e.on("PastePostProcess",t(o))})(e),e.on("PreInit",(()=>{(e=>{e.on("cut",(e=>t=>{!t.isDefaultPrevented()&&bE(e)&&gE(t,hE(e),pE(e),(()=>{if(Nt.browser.isChromium()||Nt.browser.isFirefox()){const t=e.selection.getRng();gf.setEditorTimeout(e,(()=>{e.selection.setRng(t),e.execCommand("Delete")}),0)}else e.execCommand("Delete")}))})(e)),e.on("copy",(e=>t=>{!t.isDefaultPrevented()&&bE(e)&&gE(t,hE(e),pE(e),S)})(e))})(e),((e,t)=>{Xl(e)&&e.on("dragend dragover draggesture dragdrop drop drag",(e=>{e.preventDefault(),e.stopPropagation()})),Ql(e)||e.on("drop",(e=>{const t=e.dataTransfer;t&&(e=>V(e.files,(e=>/^image\//.test(e.type))))(t)&&e.preventDefault()})),e.on("drop",(n=>{if(n.isDefaultPrevented()||t.get())return;const o=vE(e,n);if(y(o))return;const r=lE(n.dataTransfer),s=dE(r,H_());if((!cE(r)||(e=>{const t=e["text/plain"];return!!t&&0===t.indexOf("file://")})(r))&&uE(e,n,o))return;const a=r[H_()],i=a||r["text/html"]||r["text/plain"];i&&(n.preventDefault(),gf.setEditorTimeout(e,(()=>{e.undoManager.transact((()=>{a&&e.execCommand("Delete"),yE(e,o);const t=J_(i);r["text/html"]?aE(e,t,s):iE(e,t)}))})))})),e.on("dragstart",(e=>{t.set(!0)})),e.on("dragover dragend",(n=>{Ql(e)&&!1===t.get()&&(n.preventDefault(),yE(e,vE(e,n))),"dragend"===n.type&&t.set(!1)}))})(e,t),fE(e,o,n)}))},SE=Po,_E=Ro,EE=e=>Io(e.dom),NE=e=>t=>vn(fn(e),t),RE=(e,t)=>qo(fn(e),EE,NE(t)),AE=(e,t,n)=>{const o=new Qo(e,t),r=n?o.next.bind(o):o.prev.bind(o);let s=e;for(let t=n?e:r();t&&!SE(t);t=r())Fr(t)&&(s=t);return s},OE=e=>{const t=((e,t)=>{const n=Ya.fromRangeStart(e).getNode(),o=((e,t)=>qo(fn(e),(e=>(e=>Mo(e.dom))(e)||er(e)),NE(t)).getOr(fn(t)).dom)(n,t),r=AE(n,o,!1),s=AE(n,o,!0),a=document.createRange();return RE(r,o).fold((()=>{_E(r)?a.setStart(r,0):a.setStartBefore(r)}),(e=>a.setStartBefore(e.dom))),RE(s,o).fold((()=>{_E(s)?a.setEnd(s,s.data.length):a.setEndAfter(s)}),(e=>a.setEndAfter(e.dom))),a})(e.selection.getRng(),e.getBody());e.selection.setRng(Gp(t))};var TE;!function(e){e.Before="before",e.After="after"}(TE||(TE={}));const BE=(e,t)=>Math.abs(e.left-t),DE=(e,t)=>Math.abs(e.right-t),PE=(e,t)=>(e=>Y(e,((e,t)=>e.fold((()=>M.some(t)),(e=>{const n=Math.min(t.left,e.left),o=Math.min(t.top,e.top),r=Math.max(t.right,e.right),s=Math.max(t.bottom,e.bottom);return M.some({top:o,right:r,bottom:s,left:n,width:r-n,height:s-o})}))),M.none()))(K(e,(e=>{return(n=t)>=(o=e).top&&n<=o.bottom;var n,o}))).fold((()=>[[],e]),(t=>{const{pass:n,fail:o}=W(e,(e=>((e,t)=>{const n=((e,t)=>Math.max(0,Math.min(e.bottom,t.bottom)-Math.max(e.top,t.top)))(e,t)/Math.min(e.height,t.height);return((e,t)=>e.top<t.bottom&&e.bottom>t.top)(e,t)&&n>.5})(e,t)));return[n,o]})),LE=(e,t,n)=>t>e.left&&t<e.right?0:Math.min(Math.abs(e.left-t),Math.abs(e.right-t)),ME=(e,t,n)=>{const o=e=>Fr(e.node)?M.some(e):Co(e.node)?ME(de(e.node.childNodes),t,n):M.none(),r=(e,r)=>{const s=se(e,((e,o)=>r(e,t,n)-r(o,t,n)));return((e,r)=>{if(e.length>=2){const s=o(e[0]).getOr(e[0]),a=o(e[1]).getOr(e[1]);if(Math.abs(r(s,t,n)-r(a,t,n))<2){if(Ro(s.node))return M.some(s);if(Ro(a.node))return M.some(a)}}return M.none()})(s,r).orThunk((()=>ce(s,o)))},[s,a]=PE(jw(e),n),{pass:i,fail:l}=W(a,(e=>e.top<n));return r(s,LE).orThunk((()=>r(l,Na))).orThunk((()=>r(i,Na)))},IE=(e,t,n)=>((e,t,n)=>{const o=fn(e),r=wn(o),s=gn(r,t,n).filter((e=>yn(o,e))).getOr(o);return((e,t,n,o)=>{const r=(t,s)=>s.fold((()=>ME(de(t.dom.childNodes),n,o)),(e=>{const r=K(de(t.dom.childNodes),(t=>t!==e.dom));return ME(r,n,o)})).orThunk((()=>{var n;return(vn(t,e)?M.none():(n=t,M.from(n.dom.parentElement).map(fn))).bind((e=>r(e,M.some(t))))}));return r(t,M.none())})(o,s,t,n)})(e,t,n).filter((e=>Rd(e.node))).map((e=>((e,t)=>({node:e.node,position:BE(e,t)<DE(e,t)?TE.Before:TE.After}))(e,t))),FE=e=>{const t=e.getBoundingClientRect(),n=e.ownerDocument,o=n.documentElement,r=n.defaultView;return{top:t.top+r.pageYOffset-o.clientTop,left:t.left+r.pageXOffset-o.clientLeft}},UE=Io,zE=Mo,jE=(e,t,n,o)=>{const r=e.dom,s=t.cloneNode(!0);r.setStyles(s,{width:n,height:o}),r.setAttrib(s,"data-mce-selected",null);const a=r.create("div",{class:"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return r.setStyles(a,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:o}),r.setStyles(s,{margin:0,boxSizing:"border-box"}),a.appendChild(s),a},VE=e=>{e&&e.parentNode&&e.parentNode.removeChild(e)},HE=e=>{e.on((e=>{VE(e.ghost)})),e.clear()},$E=e=>{const t=Js(),n=Hs.DOM,o=document,r=((e,t)=>n=>{if((e=>0===e.button)(n)){const s=Q(t.dom.getParents(n.target),((...e)=>t=>{for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1})(UE,zE)).getOr(null);if(o=t.getBody(),UE(r=s)&&r!==o){const o=t.dom.getPos(s),r=t.getBody(),a=t.getDoc().documentElement;e.set({element:s,dragging:!1,screenX:n.screenX,screenY:n.screenY,maxX:(t.inline?r.scrollWidth:a.offsetWidth)-2,maxY:(t.inline?r.scrollHeight:a.offsetHeight)-2,relX:n.pageX-o.x,relY:n.pageY-o.y,width:s.offsetWidth,height:s.offsetHeight,ghost:jE(t,s,s.offsetWidth,s.offsetHeight)})}}var o,r})(t,e),s=((e,t)=>{const n=Zs(((e,n)=>{t._selectionOverrides.hideFakeCaret(),t.selection.placeCaretAt(e,n)}),0);return t.on("remove",n.cancel),o=>e.on((e=>{const r=Math.max(Math.abs(o.screenX-e.screenX),Math.abs(o.screenY-e.screenY));if(!e.dragging&&r>10){if(t.dispatch("dragstart",{target:e.element}).isDefaultPrevented())return;e.dragging=!0,t.focus()}if(e.dragging){const r=((e,t)=>({pageX:t.pageX-e.relX,pageY:t.pageY+5}))(e,((e,t)=>{return n=(e=>e.inline?FE(e.getBody()):{left:0,top:0})(e),o=(e=>{const t=e.getBody();return e.inline?{left:t.scrollLeft,top:t.scrollTop}:{left:0,top:0}})(e),r=((e,t)=>{if(t.target.ownerDocument!==e.getDoc()){const n=FE(e.getContentAreaContainer()),o=(e=>{const t=e.getBody(),n=e.getDoc().documentElement,o={left:t.scrollLeft,top:t.scrollTop},r={left:t.scrollLeft||n.scrollLeft,top:t.scrollTop||n.scrollTop};return e.inline?o:r})(e);return{left:t.pageX-n.left+o.left,top:t.pageY-n.top+o.top}}return{left:t.pageX,top:t.pageY}})(e,t),{pageX:r.left-n.left+o.left,pageY:r.top-n.top+o.top};var n,o,r})(t,o));s=e.ghost,a=t.getBody(),s.parentNode!==a&&a.appendChild(s),((e,t,n,o,r,s)=>{let a=0,i=0;e.style.left=t.pageX+"px",e.style.top=t.pageY+"px",t.pageX+n>r&&(a=t.pageX+n-r),t.pageY+o>s&&(i=t.pageY+o-s),e.style.width=n-a+"px",e.style.height=o-i+"px"})(e.ghost,r,e.width,e.height,e.maxX,e.maxY),n.throttle(o.clientX,o.clientY)}var s,a}))})(t,e),a=((e,t)=>n=>{e.on((e=>{if(e.dragging){if(((e,t,n)=>t!==n&&!e.dom.isChildOf(t,n)&&!UE(t))(t,(e=>{const t=e.getSel().getRangeAt(0).startContainer;return 3===t.nodeType?t.parentNode:t})(t.selection),e.element)){const o=(e=>{const t=e.cloneNode(!0);return t.removeAttribute("data-mce-selected"),t})(e.element);t.dispatch("drop",{clientX:n.clientX,clientY:n.clientY}).isDefaultPrevented()||t.undoManager.transact((()=>{VE(e.element),t.insertContent(t.dom.getOuterHTML(o)),t._selectionOverrides.hideFakeCaret()}))}t.dispatch("dragend")}})),HE(e)})(t,e),i=((e,t)=>()=>{e.on((e=>{e.dragging&&t.dispatch("dragend")})),HE(e)})(t,e);e.on("mousedown",r),e.on("mousemove",s),e.on("mouseup",a),n.bind(o,"mousemove",s),n.bind(o,"mouseup",i),e.on("remove",(()=>{n.unbind(o,"mousemove",s),n.unbind(o,"mouseup",i)})),e.on("keydown",(e=>{e.keyCode===em.ESC&&i()}))},qE=Io,WE=(e,t)=>Fp(e.getBody(),t),KE=e=>{const t=e.selection,n=e.dom,o=n.isBlock,r=e.getBody(),s=_d(e,r,o,(()=>xf(e))),a="sel-"+n.uniqueId(),i="data-mce-selected";let l;const d=e=>e!==r&&(qE(e)||Uo(e))&&n.isChildOf(e,r),c=(n,o,r,a=!0)=>e.dispatch("ShowCaret",{target:o,direction:n,before:r}).isDefaultPrevented()?null:(a&&t.scrollIntoView(o,-1===n),s.show(r,o)),u=e=>xr(e)||Er(e)||Nr(e),m=e=>u(e.startContainer)||u(e.endContainer),f=t=>{const o=e.schema.getVoidElements(),r=n.createRng(),s=t.startContainer,a=t.startOffset,i=t.endContainer,l=t.endOffset;return xe(o,s.nodeName.toLowerCase())?0===a?r.setStartBefore(s):r.setStartAfter(s):r.setStart(s,a),xe(o,i.nodeName.toLowerCase())?0===l?r.setEndBefore(i):r.setEndAfter(i):r.setEnd(i,l),r},g=(o,s)=>{if(!o)return null;if(o.collapsed){if(!m(o)){const e=s?1:-1,t=Qd(e,r,o),n=t.getNode(!s);if(Rd(n))return c(e,n,!!s&&!t.isAtEnd(),!1);const a=t.getNode(s);if(Rd(a))return c(e,a,!s&&!t.isAtEnd(),!1)}return null}let u=o.startContainer,f=o.startOffset;const g=o.endOffset;if(3===u.nodeType&&0===f&&qE(u.parentNode)&&(u=u.parentNode,f=n.nodeIndex(u),u=u.parentNode),1!==u.nodeType)return null;if(g===f+1&&u===o.endContainer){const o=u.childNodes[f];if(d(o))return(o=>{const r=o.cloneNode(!0),s=e.dispatch("ObjectSelected",{target:o,targetClone:r});if(s.isDefaultPrevented())return null;const d=((o,r)=>{const s=fn(e.getBody()),i=e.getDoc(),l=Ko(s,"#"+a).getOrThunk((()=>{const e=cn('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>',i);return qt(e,"id",a),Zn(s,e),e})),d=n.createRng();to(l),eo(l,[mn(fr,i),fn(r),mn(fr,i)]),d.setStart(l.dom.firstChild,1),d.setEnd(l.dom.lastChild,0),$n(l,{top:n.getPos(o,e.getBody()).y+"px"}),tf(l);const c=t.getSel();return c.removeAllRanges(),c.addRange(d),d})(o,s.targetClone),c=fn(o);return $(na(fn(e.getBody()),"*[data-mce-selected]"),(e=>{vn(c,e)||Xt(e,i)})),n.getAttrib(o,i)||o.setAttribute(i,"1"),l=o,h(),d})(o)}return null},p=()=>{l&&l.removeAttribute(i),Ko(fn(e.getBody()),"#"+a).each(no),l=null},h=()=>{s.hide()};return ly(e)||(e.on("click",(t=>{const n=WE(e,t.target);n&&qE(n)&&(t.preventDefault(),e.focus())})),e.on("blur NewBlock",p),e.on("ResizeWindow FullscreenStateChanged",s.reposition),e.on("tap",(t=>{const n=t.target,o=WE(e,n);qE(o)?(t.preventDefault(),BC(e,o).each(g)):d(n)&&BC(e,n).each(g)}),!0),e.on("mousedown",(o=>{const s=o.target;if(s!==r&&"HTML"!==s.nodeName&&!n.isChildOf(s,r))return;if(!1===((e,t,n)=>{const o=fn(e.getBody()),r=e.inline?o:fn(wn(o).dom.documentElement),s=((e,t,n,o)=>{const r=(e=>e.dom.getBoundingClientRect())(t);return{x:n-(e?r.left+t.dom.clientLeft+Py(t):0),y:o-(e?r.top+t.dom.clientTop+Dy(t):0)}})(e.inline,r,t,n);return((e,t,n)=>{const o=Ty(e),r=By(e);return t>=0&&n>=0&&t<=o&&n<=r})(r,s.x,s.y)})(e,o.clientX,o.clientY))return;p(),h();const a=WE(e,s);qE(a)?(o.preventDefault(),BC(e,a).each(g)):IE(r,o.clientX,o.clientY).each((n=>{var r;o.preventDefault(),(r=c(1,n.node,n.position===TE.Before,!1))&&t.setRng(r),Co(a)?a.focus():e.getBody().focus()}))})),e.on("keypress",(e=>{em.modifierPressed(e)||qE(t.getNode())&&e.preventDefault()})),e.on("GetSelectionRange",(e=>{let t=e.range;if(l){if(!l.parentNode)return void(l=null);t=t.cloneRange(),t.selectNode(l),e.range=t}})),e.on("SetSelectionRange",(e=>{e.range=f(e.range);const t=g(e.range,e.forward);t&&(e.range=t)})),e.on("AfterSetSelectionRange",(e=>{const t=e.range,o=t.startContainer.parentNode;var r;m(t)||"mcepastebin"===o.id||h(),r=o,n.hasClass(r,"mce-offscreen-selection")||p()})),(e=>{$E(e),Ul(e)&&(e=>{const t=t=>{if(!t.isDefaultPrevented()){const n=t.dataTransfer;n&&(j(n.types,"Files")||n.files.length>0)&&(t.preventDefault(),"drop"===t.type&&zy(e,"Dropped file type is not supported"))}},n=n=>{bf(e,n.target)&&t(n)},o=()=>{const o=Hs.DOM,r=e.dom,s=document,a=e.inline?e.getBody():e.getDoc(),i=["drop","dragover"];$(i,(e=>{o.bind(s,e,n),r.bind(a,e,t)})),e.on("remove",(()=>{$(i,(e=>{o.unbind(s,e,n),r.unbind(a,e,t)}))}))};e.on("init",(()=>{gf.setEditorTimeout(e,o,0)}))})(e)})(e),(e=>{const t=Zs((()=>{if(!e.removed&&e.getBody().contains(document.activeElement)){const t=e.selection.getRng();if(t.collapsed){const n=DC(e,t,!1);e.selection.setRng(n)}}}),0);e.on("focus",(()=>{t.throttle()})),e.on("blur",(()=>{t.cancel()}))})(e),(e=>{e.on("init",(()=>{e.on("focusin",(t=>{const n=t.target;if(Uo(n)){const t=Fp(e.getBody(),n),o=Io(t)?t:n;e.selection.getNode()!==o&&BC(e,o).each((t=>e.selection.setRng(t)))}}))}))})(e)),{showCaret:c,showBlockCaretContainer:e=>{e.hasAttribute("data-mce-caret")&&(Rr(e),t.scrollIntoView(e))},hideFakeCaret:h,destroy:()=>{s.destroy(),l=null}}},GE=(e,t,n)=>{if(Ro(t)&&(n<0||n>t.data.length))return[];const o=[n];let r=t;for(;r!==e&&r.parentNode;){const e=r.parentNode;for(let t=0;t<e.childNodes.length;t++)if(e.childNodes[t]===r){o.push(t);break}r=e}return r===e?o.reverse():[]},YE=(e,t,n,o,r)=>({start:GE(e,t,n),end:GE(e,o,r)}),XE=(e,t)=>{const n=t.slice(),o=n.pop();return Y(n,((e,t)=>e.bind((e=>M.from(e.childNodes[t])))),M.some(e)).bind((e=>Ro(e)&&(o<0||o>e.data.length)?M.none():M.some({node:e,offset:o})))},QE=(e,t)=>XE(e,t.start).bind((({node:n,offset:o})=>XE(e,t.end).map((({node:e,offset:t})=>{const r=document.createRange();return r.setStart(n,o),r.setEnd(e,t),r})))),JE=(e,t,n)=>{if(t&&e.isEmpty(t)&&!n(t)){const o=t.parentNode;e.remove(t),JE(e,o,n)}},ZE=(e,t,n,o=!0)=>{const r=t.startContainer.parentNode,s=t.endContainer.parentNode;t.deleteContents(),o&&!n(t.startContainer)&&(Ro(t.startContainer)&&0===t.startContainer.data.length&&e.remove(t.startContainer),Ro(t.endContainer)&&0===t.endContainer.data.length&&e.remove(t.endContainer),JE(e,r,n),r!==s&&JE(e,s,n))},eN=(e,t)=>M.from(e.dom.getParent(t.startContainer,e.dom.isBlock)),tN=(e,t,n)=>{((e,t,n)=>{if(Ro(e)&&0>=e.length)return M.some(Sk(e,0));{const t=Ca(_k);return M.from(t.forwards(e,0,Ek(e),n)).map((e=>Sk(e.container,0)))}})(t,0,t).each((o=>{const r=o.container;Ak(r,n.start.length,t).each((n=>{const o=e.createRng();o.setStart(r,0),o.setEnd(n.container,n.offset),ZE(e,o,(e=>e===t))}))}))},nN=(e,t)=>e.create("span",{"data-mce-type":"bookmark",id:t}),oN=(e,t)=>{const n=e.createRng();return n.setStartAfter(t.start),n.setEndBefore(t.end),n},rN=(e,t,n)=>{const o=QE(e.getRoot(),n).getOrDie("Unable to resolve path range"),r=o.startContainer,s=o.endContainer,a=0===o.endOffset?s:s.splitText(o.endOffset),i=0===o.startOffset?r:r.splitText(o.startOffset);return{prefix:t,end:a.parentNode.insertBefore(nN(e,t+"-end"),a),start:i.parentNode.insertBefore(nN(e,t+"-start"),i)}},sN=(e,t,n)=>{JE(e,e.get(t.prefix+"-end"),n),JE(e,e.get(t.prefix+"-start"),n)},aN=e=>0===e.start.length,iN=(e,t,n,o)=>{const r=t.start;var s;return Ok(e,o.container,o.offset,(s=r,(e,t)=>{const n=e.data.substring(0,t),o=n.lastIndexOf(s.charAt(s.length-1)),r=n.lastIndexOf(s);return-1!==r?r+s.length:-1!==o?o+1:-1}),n).bind((o=>{if(o.offset>=r.length){const t=e.createRng();return t.setStart(o.container,o.offset-r.length),t.setEnd(o.container,o.offset),M.some(t)}{const s=o.offset-r.length;return Rk(o.container,s,n).map((t=>{const n=e.createRng();return n.setStart(t.container,t.offset),n.setEnd(o.container,o.offset),n})).filter((e=>e.toString()===r)).orThunk((()=>iN(e,t,n,Sk(o.container,0))))}}))},lN=(e,t,n)=>{const o=e.dom,r=o.getRoot(),s=n.pattern,a=n.position.container,i=n.position.offset;return Rk(a,i-n.pattern.end.length,t).bind((l=>{const d=YE(r,l.container,l.offset,a,i);if(aN(s))return M.some({matches:[{pattern:s,startRng:d,endRng:d}],position:l});{const a=dN(e,n.remainingPatterns,l.container,l.offset,t),i=a.getOr({matches:[],position:l}),c=i.position,u=((e,t,n,o,r,s=!1)=>{if(0===t.start.length&&!s){const t=e.createRng();return t.setStart(n,o),t.setEnd(n,o),M.some(t)}return Nk(n,o,r).bind((n=>iN(e,t,r,n).bind((e=>{if(s){if(e.endContainer===n.container&&e.endOffset===n.offset)return M.none();if(0===n.offset&&e.endContainer.textContent.length===e.endOffset)return M.none()}return M.some(e)}))))})(o,s,c.container,c.offset,t,a.isNone());return u.map((e=>{const t=((e,t)=>YE(e,t.startContainer,t.startOffset,t.endContainer,t.endOffset))(r,e);return{matches:i.matches.concat([{pattern:s,startRng:t,endRng:d}]),position:Sk(e.startContainer,e.startOffset)}}))}}))},dN=(e,t,n,o,r)=>{const s=e.dom;return Nk(n,o,s.getRoot()).bind((a=>{const i=s.createRng();i.setStart(r,0),i.setEnd(n,o);const l=i.toString();for(let n=0;n<t.length;n++){const o=t[n];if(!je(l,o.end))continue;const s=t.slice();s.splice(n,1);const i=lN(e,r,{pattern:o,remainingPatterns:s,position:a});if(i.isSome())return i}return M.none()}))},cN=(e,t,n)=>{e.selection.setRng(n),"inline-format"===t.type?$(t.format,(t=>{e.formatter.apply(t)})):e.execCommand(t.cmd,!1,t.value)},uN=(e,t,n)=>{const o=e.selection.getRng();return!1===o.collapsed?[]:eN(e,o).bind((r=>{const s=Math.max(0,o.startOffset-(n?1:0));return dN(e,t,o.startContainer,s,r)})).fold((()=>[]),(e=>e.matches))},mN=(e,t)=>{if(0===t.length)return;const n=e.dom,o=e.selection.getBookmark(),r=((e,t)=>{const n=pa("mce_textpattern"),o=G(t,((t,o)=>{const r=rN(e,n+`_end${t.length}`,o.endRng);return t.concat([{...o,endMarker:r}])}),[]);return G(o,((t,r)=>{const s=o.length-t.length-1,a=aN(r.pattern)?r.endMarker:rN(e,n+`_start${s}`,r.startRng);return t.concat([{...r,startMarker:a}])}),[])})(n,t);$(r,(t=>{const o=n.getParent(t.startMarker.start,n.isBlock),r=e=>e===o;aN(t.pattern)?((e,t,n,o)=>{const r=oN(e.dom,n);ZE(e.dom,r,o),cN(e,t,r)})(e,t.pattern,t.endMarker,r):((e,t,n,o,r)=>{const s=e.dom,a=oN(s,o),i=oN(s,n);ZE(s,i,r),ZE(s,a,r);const l={prefix:n.prefix,start:n.end,end:o.start},d=oN(s,l);cN(e,t,d)})(e,t.pattern,t.startMarker,t.endMarker,r),sN(n,t.endMarker,r),sN(n,t.startMarker,r)})),e.selection.moveToBookmark(o)},fN=(e,t)=>{if(!e.selection.isCollapsed()||!(e=>e.inlinePatterns.length>0||e.blockPatterns.length>0)(t))return!1;const n=uN(e,t.inlinePatterns,!1),o=((e,t)=>{const n=e.dom,o=e.selection.getRng();return eN(e,o).filter((t=>{const o=ji(e),r=n.is(t,o);return null!==t&&r})).bind((e=>{const o=e.textContent,r=((e,t)=>{const n=t.replace(fr," ");return Q(e,(e=>0===t.indexOf(e.start)||0===n.indexOf(e.start)))})(t,o);return r.map((t=>Bt.trim(o).length===t.start.length?[]:[{pattern:t,range:YE(n.getRoot(),e,0,e,0)}]))})).getOr([])})(e,t.blockPatterns);return(o.length>0||n.length>0)&&(e.undoManager.add(),e.undoManager.extra((()=>{e.execCommand("mceInsertNewLine")}),(()=>{e.insertContent(mr),mN(e,n),((e,t)=>{if(0===t.length)return;const n=e.selection.getBookmark();$(t,(t=>((e,t)=>{const n=e.dom,o=t.pattern,r=QE(n.getRoot(),t.range).getOrDie("Unable to resolve path range");return eN(e,r).each((t=>{"block-format"===o.type?((e,t)=>{const n=t.get(e);return p(n)&&ie(n).exists((e=>xe(e,"block")))})(o.format,e.formatter)&&e.undoManager.transact((()=>{tN(e.dom,t,o),e.formatter.apply(o.format)})):"block-command"===o.type&&e.undoManager.transact((()=>{tN(e.dom,t,o),e.execCommand(o.cmd,!1,o.value)}))})),!0})(e,t))),e.selection.moveToBookmark(n)})(e,o);const t=e.selection.getRng(),r=Nk(t.startContainer,t.startOffset,e.dom.getRoot());e.execCommand("mceInsertNewLine"),r.each((t=>{const n=t.container;n.data.charAt(t.offset-1)===mr&&(n.deleteData(t.offset-1,1),JE(e.dom,n.parentNode,(t=>t===e.dom.getRoot())))}))})),!0)},gN=(e,t)=>{if(t.length>0){const n=uN(e,t,!0);n.length>0&&e.undoManager.transact((()=>{mN(e,n)}))}},pN=(e,t,n)=>{for(let o=0;o<e.length;o++)if(n(e[o],t))return!0;return!1},hN=e=>{const t=[",",".",";",":","!","?"],n=[32],o=()=>_i(id(e));e.on("keydown",(t=>{var n;13!==t.keyCode||em.modifierPressed(t)||fN(e,(n=id(e),{inlinePatterns:_i(n),blockPatterns:Si(n)}))&&t.preventDefault()}),!0),e.on("keyup",(t=>{pN(n,t,((e,t)=>e===t.keyCode&&!1===em.modifierPressed(t)))&&gN(e,o())})),e.on("keypress",(n=>{pN(t,n,((e,t)=>e.charCodeAt(0)===t.charCode))&&gf.setEditorTimeout(e,(()=>{gN(e,o())}))}))},bN=e=>{const t=Bt.each,n=em.BACKSPACE,o=em.DELETE,r=e.dom,s=e.selection,a=e.parser,i=Nt.browser,l=i.isFirefox(),d=i.isChromium()||i.isSafari(),c=Nt.deviceType.isiPhone()||Nt.deviceType.isiPad(),u=Nt.os.isMacOS()||Nt.os.isiOS(),m=(t,n)=>{try{e.getDoc().execCommand(t,!1,n)}catch(e){}},f=e=>e.isDefaultPrevented(),g=()=>{e.shortcuts.add("meta+a",null,"SelectAll")},p=()=>{e.inline||r.bind(e.getDoc(),"mousedown mouseup",(t=>{let n;if(t.target===e.getDoc().documentElement)if(n=s.getRng(),e.getBody().focus(),"mousedown"===t.type){if(xr(n.startContainer))return;s.placeCaretAt(t.clientX,t.clientY)}else s.setRng(n)}))},h=()=>{Range.prototype.getClientRects||e.on("mousedown",(t=>{if(!f(t)&&"HTML"===t.target.nodeName){const t=e.getBody();t.blur(),gf.setEditorTimeout(e,(()=>{t.focus()}))}}))},b=()=>{const t=Vl(e);e.on("click",(n=>{const o=n.target;/^(IMG|HR)$/.test(o.nodeName)&&"false"!==r.getContentEditableParent(o)&&(n.preventDefault(),e.selection.select(o),e.nodeChanged()),"A"===o.nodeName&&r.hasClass(o,t)&&0===o.childNodes.length&&(n.preventDefault(),s.select(o))}))},v=()=>{e.on("keydown",(e=>{if(!f(e)&&e.keyCode===n&&s.isCollapsed()&&0===s.getRng().startOffset){const t=s.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}}))},y=()=>{Ll(e)||e.on("BeforeExecCommand mousedown",(()=>{m("StyleWithCSS",!1),m("enableInlineTableEditing",!1),gl(e)||m("enableObjectResizing",!1)}))},C=()=>{e.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}")},w=()=>{e.inline||e.on("keydown",(()=>{document.activeElement===document.body&&e.getWin().focus()}))},x=()=>{e.inline||(e.contentStyles.push("body {min-height: 150px}"),e.on("click",(t=>{let n;"HTML"===t.target.nodeName&&(n=e.selection.getRng(),e.getBody().focus(),e.selection.setRng(n),e.selection.normalize(),e.nodeChanged())})))},k=()=>{u&&e.on("keydown",(t=>{!em.metaKeyPressed(t)||t.shiftKey||37!==t.keyCode&&39!==t.keyCode||(t.preventDefault(),e.selection.getSel().modify("move",37===t.keyCode?"backward":"forward","lineboundary"))}))},_=()=>{e.on("click",(e=>{let t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)})),e.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")},E=()=>{e.on("init",(()=>{e.dom.bind(e.getBody(),"submit",(e=>{e.preventDefault()}))}))},N=S;return ly(e)?(d&&(p(),b(),E(),g(),c&&(w(),x(),_())),l&&(h(),y(),C(),k())):(e.on("keydown",(t=>{let n,o;if(f(t)||t.keyCode!==em.BACKSPACE)return;n=s.getRng();const a=n.startContainer,i=n.startOffset,l=r.getRoot();if(o=a,n.collapsed&&0===i){for(;o&&o.parentNode&&o.parentNode.firstChild===o&&o.parentNode!==l;)o=o.parentNode;"BLOCKQUOTE"===o.tagName&&(e.formatter.toggle("blockquote",null,o),n=r.createRng(),n.setStart(a,0),n.setEnd(a,0),s.setRng(n))}})),(()=>{const t=e=>{const t=r.create("body"),n=e.cloneContents();return t.appendChild(n),s.serializer.serialize(t,{format:"html"})};e.on("keydown",(s=>{const a=s.keyCode;let i,l;if(!f(s)&&(a===o||a===n)){if(i=e.selection.isCollapsed(),l=e.getBody(),i&&!r.isEmpty(l))return;if(!i&&!(n=>{const o=t(n),s=r.createRng();return s.selectNode(e.getBody()),o===t(s)})(e.selection.getRng()))return;s.preventDefault(),e.setContent(""),l.firstChild&&r.isBlock(l.firstChild)?e.selection.setCursorLocation(l.firstChild,0):e.selection.setCursorLocation(l,0),e.nodeChanged()}}))})(),Nt.windowsPhone||e.on("keyup focusin mouseup",(t=>{em.modifierPressed(t)||(e=>{const t=e.getBody(),n=e.selection.getRng();return n.startContainer===n.endContainer&&n.startContainer===t&&0===n.startOffset&&n.endOffset===t.childNodes.length})(e)||s.normalize()}),!0),d&&(p(),b(),e.on("init",(()=>{m("DefaultParagraphSeparator",ji(e))})),E(),v(),a.addNodeFilter("br",(e=>{let t=e.length;for(;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()})),c?(w(),x(),_()):g()),l&&(e.on("keydown",(t=>{if(!f(t)&&t.keyCode===n){if(!e.getBody().getElementsByTagName("hr").length)return;if(s.isCollapsed()&&0===s.getRng().startOffset){const e=s.getNode(),n=e.previousSibling;if("HR"===e.nodeName)return r.remove(e),void t.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(r.remove(n),t.preventDefault())}}})),h(),(()=>{const n=()=>{const n=r.getAttribs(s.getStart().cloneNode(!1));return()=>{const o=s.getStart();o!==e.getBody()&&(r.setAttrib(o,"style",null),t(n,(e=>{o.setAttributeNode(e.cloneNode(!0))})))}},o=()=>!s.isCollapsed()&&r.getParent(s.getStart(),r.isBlock)!==r.getParent(s.getEnd(),r.isBlock);e.on("keypress",(t=>{let r;if(!f(t)&&(8===t.keyCode||46===t.keyCode)&&o())return r=n(),e.getDoc().execCommand("delete",!1,null),r(),t.preventDefault(),!1})),r.bind(e.getDoc(),"cut",(t=>{let r;!f(t)&&o()&&(r=n(),gf.setEditorTimeout(e,(()=>{r()})))}))})(),y(),e.on("SetContent ExecCommand",(e=>{"setcontent"!==e.type&&"mceInsertLink"!==e.command||t(r.select("a"),(e=>{let t=e.parentNode;const n=r.getRoot();if(t.lastChild===e){for(;t&&!r.isBlock(t);){if(t.parentNode.lastChild!==t||t===n)return;t=t.parentNode}r.add(t,"br",{"data-mce-bogus":1})}}))})),C(),k(),v())),{refreshContentEditable:N,isHidden:()=>{if(!l||e.removed)return!1;const t=e.selection.getSel();return!t||!t.rangeCount||0===t.rangeCount}}},vN=Hs.DOM,yN=e=>e.inline?e.getElement().nodeName.toLowerCase():void 0,CN=e=>ve(e,(e=>!1===v(e))),wN=e=>{const t=e.options.get,n=e.editorUpload.blobCache;return CN({allow_conditional_comments:t("allow_conditional_comments"),allow_html_data_urls:t("allow_html_data_urls"),allow_svg_data_urls:t("allow_svg_data_urls"),allow_html_in_named_anchor:t("allow_html_in_named_anchor"),allow_script_urls:t("allow_script_urls"),allow_unsafe_link_target:t("allow_unsafe_link_target"),convert_fonts_to_spans:t("convert_fonts_to_spans"),fix_list_elements:t("fix_list_elements"),font_size_legacy_values:t("font_size_legacy_values"),forced_root_block:t("forced_root_block"),forced_root_block_attrs:t("forced_root_block_attrs"),preserve_cdata:t("preserve_cdata"),remove_trailing_brs:t("remove_trailing_brs"),inline_styles:t("inline_styles"),root_name:yN(e),validate:!0,blob_cache:n,document:e.getDoc()})},xN=e=>{const t=e.options.get;return CN({custom_elements:t("custom_elements"),extended_valid_elements:t("extended_valid_elements"),invalid_elements:t("invalid_elements"),invalid_styles:t("invalid_styles"),schema:t("schema"),valid_children:t("valid_children"),valid_classes:t("valid_classes"),valid_elements:t("valid_elements"),valid_styles:t("valid_styles"),verify_html:t("verify_html"),padd_empty_block_inline_children:t("format_empty_lines")})},kN=e=>e.inline?e.ui.styleSheetLoader:e.dom.styleSheetLoader,SN=e=>{const t=kN(e),n=ul(e),o=e.contentCSS,r=()=>{t.unloadAll(o),e.inline||e.ui.styleSheetLoader.unloadAll(n)},s=()=>{e.removed?r():e.on("remove",r)};if(e.contentStyles.length>0){let t="";Bt.each(e.contentStyles,(e=>{t+=e+"\r\n"})),e.dom.addStyle(t)}const a=Promise.all(((e,t,n)=>{const o=[kN(e).loadAll(t)];return e.inline?o:o.concat([e.ui.styleSheetLoader.loadAll(n)])})(e,o,n)).then(s).catch(s),i=cl(e);return i&&((e,t)=>{const n=fn(e.getBody()),o=In(Mn(n)),r=un("style");qt(r,"type","text/css"),Zn(r,mn(t)),Zn(o,r),e.on("remove",(()=>{no(r)}))})(e,i),a},_N=e=>{!0!==e.removed&&((e=>{ly(e)||e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"})})(e),(e=>{e.bindPendingEventDelegates(),e.initialized=!0,(e=>{e.dispatch("Init")})(e),e.focus(!0),(e=>{const t=e.dom.getRoot();e.inline||Lu(e)&&e.selection.getStart(!0)!==t||xc(t).each((t=>{const n=t.getNode(),o=_o(n)?xc(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e),e.nodeChanged({initial:!0});const t=ql(e);w(t)&&t.call(e,e),(e=>{const t=Kl(e);t&&gf.setEditorTimeout(e,(()=>{let n;n=!0===t?e:e.editorManager.get(t),n.destroyed||n.focus()}),100)})(e)})(e))},EN=e=>{const t=e.getElement();let n=e.getDoc();e.inline&&(vN.addClass(t,"mce-content-body"),e.contentDocument=n=document,e.contentWindow=window,e.bodyElement=t,e.contentAreaContainer=t);const o=e.getBody();o.disabled=!0,e.readonly=Ll(e),e.readonly||(e.inline&&"static"===vN.getStyle(o,"position",!0)&&(o.style.position="relative"),o.contentEditable="true"),o.disabled=!1,e.editorUpload=Jy(e),e.schema=Ss(xN(e)),e.dom=Hs(n,{keep_values:!0,url_converter:e.convertURL,url_converter_scope:e,update_styles:!0,root_element:e.inline?e.getBody():null,collect:()=>e.inline,schema:e.schema,contentCssCors:ol(e),referrerPolicy:rl(e),onSetAttrib:t=>{e.dispatch("SetAttrib",t)}}),e.parser=(e=>{const t=Av(wN(e),e.schema);return t.addAttributeFilter("src,href,style,tabindex",((t,n)=>{let o,r,s=t.length;const a=e.dom,i="data-mce-"+n;for(;s--;)if(o=t[s],r=o.attr(n),r&&!o.attr(i)){if(0===r.indexOf("data:")||0===r.indexOf("blob:"))continue;"style"===n?(r=a.serializeStyle(a.parseStyle(r),o.name),r.length||(r=null),o.attr(i,r),o.attr(n,r)):"tabindex"===n?(o.attr(i,r),o.attr(n,null)):o.attr(i,e.convertURL(r,n,o.name))}})),t.addNodeFilter("script",(e=>{let t=e.length;for(;t--;){const n=e[t],o=n.attr("type")||"no/type";0!==o.indexOf("mce-")&&n.attr("type","mce-"+o)}})),e.options.get("preserve_cdata")&&t.addNodeFilter("#cdata",(t=>{let n=t.length;for(;n--;){const o=t[n];o.type=8,o.name="#comment",o.value="[CDATA["+e.dom.encode(o.value)+"]]"}})),t.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",(t=>{let n=t.length;const o=e.schema.getNonEmptyElements();for(;n--;){const e=t[n];e.isEmpty(o)&&0===e.getAll("br").length&&e.append(new Ff("br",1))}})),t})(e),e.serializer=yy((e=>{const t=e.options.get;return{...wN(e),...xN(e),...CN({url_converter:t("url_converter"),url_converter_scope:t("url_converter_scope"),element_format:t("element_format"),entities:t("entities"),entity_encoding:t("entity_encoding"),indent:t("indent"),indent_after:t("indent_after"),indent_before:t("indent_before")})}})(e),e),e.selection=hy(e.dom,e.getWin(),e.serializer,e),e.annotator=qu(e),e.formatter=lC(e),e.undoManager=cC(e),e._nodeChangeDispatcher=new V_(e),e._selectionOverrides=KE(e),(e=>{const t=Js(),n=Ws(!1),o=ea((t=>{e.dispatch("longpress",{...t,type:"longpress"}),n.set(!0)}),400);e.on("touchstart",(e=>{Rx(e).each((r=>{o.cancel();const s={x:r.clientX,y:r.clientY,target:e.target};o.throttle(e),n.set(!1),t.set(s)}))}),!0),e.on("touchmove",(r=>{o.cancel(),Rx(r).each((o=>{t.on((r=>{((e,t)=>{const n=Math.abs(e.clientX-t.x),o=Math.abs(e.clientY-t.y);return n>5||o>5})(o,r)&&(t.clear(),n.set(!1),e.dispatch("longpresscancel"))}))}))}),!0),e.on("touchend touchcancel",(r=>{o.cancel(),"touchcancel"!==r.type&&t.get().filter((e=>e.target.isEqualNode(r.target))).each((()=>{n.get()?r.preventDefault():e.dispatch("tap",{...r,type:"tap"})}))}),!0)})(e),(e=>{(e=>{e.on("click",(t=>{e.dom.getParent(t.target,"details")&&t.preventDefault()}))})(e),(e=>{e.parser.addNodeFilter("details",(e=>{$(e,(e=>{e.attr("data-mce-open",e.attr("open")),e.attr("open","open")}))})),e.serializer.addNodeFilter("details",(e=>{$(e,(e=>{const t=e.attr("data-mce-open");e.attr("open",m(t)?t:null),e.attr("data-mce-open",null)}))}))})(e)})(e),(e=>{const t="contenteditable",n=" "+Bt.trim(dd(e))+" ",o=" "+Bt.trim(ld(e))+" ",r=Dx(n),s=Dx(o),a=cd(e);a.length>0&&e.on("BeforeSetContent",(t=>{((e,t,n)=>{let o=t.length,r=n.content;if("raw"!==n.format){for(;o--;)r=r.replace(t[o],Px(e,r,ld(e)));n.content=r}})(e,a,t)})),e.parser.addAttributeFilter("class",(e=>{let n=e.length;for(;n--;){const o=e[n];r(o)?o.attr(t,"true"):s(o)&&o.attr(t,"false")}})),e.serializer.addAttributeFilter(t,(e=>{let n=e.length;for(;n--;){const o=e[n];(r(o)||s(o))&&(a.length>0&&o.attr("data-mce-content")?(o.name="#text",o.type=3,o.raw=!0,o.value=o.attr("data-mce-content")):o.attr(t,null))}}))})(e),ly(e)||((e=>{e.on("mousedown",(t=>{t.detail>=3&&(t.preventDefault(),OE(e))}))})(e),(e=>{hN(e)})(e));const r=j_(e);Nx(e,r),(e=>{e.on("NodeChange",O(Bx,e))})(e),(e=>{const t=e.dom,n=ji(e),o=hl(e),r=(s,a)=>{if((e=>{if(fC(e)){const t=e.keyCode;return!gC(e)&&(em.metaKeyPressed(e)||e.altKey||t>=112&&t<=123||j(uC,t))}return!1})(s))return;const i=e.getBody(),l=!(e=>fC(e)&&!(gC(e)||"keyup"===e.type&&229===e.keyCode))(s)&&((e,t,n)=>{if(Yr(fn(t),!1)){const o=t.firstElementChild;return!o||!e.getStyle(t.firstElementChild,"padding-left")&&!e.getStyle(t.firstElementChild,"padding-right")&&n===o.nodeName.toLowerCase()}return!1})(t,i,n);(""!==t.getAttrib(i,mC)!==l||a)&&(t.setAttrib(i,mC,l?o:null),t.setAttrib(i,"aria-placeholder",l?o:null),((e,t)=>{e.dispatch("PlaceholderToggle",{state:t})})(e,l),e.on(l?"keydown":"keyup",r),e.off(l?"keyup":"keydown",r))};o&&e.on("init",(t=>{r(t,!0),e.on("change SetContent ExecCommand",r),e.on("paste",(t=>gf.setEditorTimeout(e,(()=>r(t)))))}))})(e),kE(e);const s=(e=>{const t=e;return(e=>we(e.plugins,"rtc").bind((e=>M.from(e.setup))))(e).fold((()=>(t.rtcInstance=iy(e),M.none())),(e=>(t.rtcInstance=(()=>{const e=N(null),t=N("");return{init:{bindEvents:S},undoManager:{beforeChange:S,add:e,undo:e,redo:e,clear:S,reset:S,hasUndo:P,hasRedo:P,transact:e,ignore:S,extra:S},formatter:{match:P,matchAll:N([]),matchNode:N(void 0),canApply:P,closest:t,apply:S,remove:S,toggle:S,formatChanged:N({unbind:S})},editor:{getContent:t,setContent:N({content:"",html:""}),insertContent:N(""),addVisual:S},selection:{getContent:t},autocompleter:{addDecoration:S,removeDecoration:S},raw:{getModel:N(M.none())}}})(),M.some((()=>e().then((e=>(t.rtcInstance=(e=>{const t=e=>f(e)?e:{},{init:n,undoManager:o,formatter:r,editor:s,selection:a,autocompleter:i,raw:l}=e;return{init:{bindEvents:n.bindEvents},undoManager:{beforeChange:o.beforeChange,add:o.add,undo:o.undo,redo:o.redo,clear:o.clear,reset:o.reset,hasUndo:o.hasUndo,hasRedo:o.hasRedo,transact:(e,t,n)=>o.transact(n),ignore:(e,t)=>o.ignore(t),extra:(e,t,n,r)=>o.extra(n,r)},formatter:{match:(e,n,o,s)=>r.match(e,t(n),s),matchAll:r.matchAll,matchNode:r.matchNode,canApply:e=>r.canApply(e),closest:e=>r.closest(e),apply:(e,n,o)=>r.apply(e,t(n)),remove:(e,n,o,s)=>r.remove(e,t(n)),toggle:(e,n,o)=>r.toggle(e,t(n)),formatChanged:(e,t,n,o,s)=>r.formatChanged(t,n,o,s)},editor:{getContent:e=>s.getContent(e),setContent:(e,t)=>({content:s.setContent(e,t),html:""}),insertContent:(e,t)=>(s.insertContent(e),""),addVisual:s.addVisual},selection:{getContent:(e,t)=>a.getContent(t)},autocompleter:{addDecoration:i.addDecoration,removeDecoration:i.removeDecoration},raw:{getModel:()=>M.some(l.getRawModel())}}})(e),e.rtc.isRemote))))))))})(e);(e=>{const t=e.getDoc(),n=e.getBody();(e=>{e.dispatch("PreInit")})(e),Gl(e)||(t.body.spellcheck=!1,vN.setAttrib(n,"spellcheck","false")),e.quirks=bN(e),(e=>{e.dispatch("PostRender")})(e);const o=ml(e);void 0!==o&&(n.dir=o);const r=Yl(e);r&&e.on("BeforeSetContent",(e=>{Bt.each(r,(t=>{e.content=e.content.replace(t,(e=>"\x3c!--mce:protected "+escape(e)+"--\x3e"))}))})),e.on("SetContent",(()=>{e.addVisual(e.getBody())})),e.on("compositionstart compositionend",(t=>{e.composing="compositionstart"===t.type}))})(e),s.fold((()=>{SN(e).then((()=>_N(e)))}),(t=>{e.setProgressState(!0),SN(e).then((()=>{t().then((t=>{e.setProgressState(!1),_N(e),uy(e)}),(t=>{e.notificationManager.open({type:"error",text:String(t)}),_N(e),uy(e)}))}))}))},NN=(e,t)=>{if(e.inline||(e.getElement().style.visibility=e.orgVisibility),t||e.inline)EN(e);else{const t=e.iframeElement,o=(n=fn(t),ao(n,"load",qy,(()=>{o.unbind(),e.contentDocument=t.contentDocument,EN(e)})));t.srcdoc=e.iframeHTML}var n},RN=Hs.DOM,AN=Hs.DOM,ON=e=>({editorContainer:e,iframeContainer:e,api:{}}),TN=e=>{const t=e.getElement();return e.orgDisplay=t.style.display,m(yl(e))?(e=>e.theme.renderUI())(e):w(yl(e))?(e=>{const t=e.getElement(),n=yl(e)(e,t);return n.editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||e.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||e.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:t.offsetHeight,n})(e):(e=>{const t=e.getElement();return e.inline?ON(null):(e=>{const t=AN.create("div");return AN.insertAfter(t,e),ON(t)})(t)})(e)},BN=e=>{e.dispatch("ScriptsLoaded"),(e=>{const t=Bt.trim(Qi(e)),n=e.ui.registry.getAll().icons,o={...Ny.get("default").icons,...Ny.get(t).icons};fe(o,((t,o)=>{xe(n,o)||e.ui.registry.addIcon(o,t)}))})(e),(e=>{const t=yl(e);if(m(t)){const n=Iy.get(t);e.theme=n(e,Iy.urls[t])||{},w(e.theme.init)&&e.theme.init(e,Iy.urls[t]||e.documentBaseUrl.replace(/\/$/,""))}else e.theme={}})(e),(e=>{const t=wl(e),n=Ry.get(t);e.model=n(e,Ry.urls[t])})(e),(e=>{const t=[];$(Il(e),(n=>{((e,t,n)=>{const o=My.get(n),r=My.urls[n]||e.documentBaseUrl.replace(/\/$/,"");if(n=Bt.trim(n),o&&-1===Bt.inArray(t,n)){if(e.plugins[n])return;try{const s=o(e,r)||{};e.plugins[n]=s,w(s.init)&&(s.init(e,r),t.push(n))}catch(t){((e,t,n)=>{const o=Xs.translate(["Failed to initialize plugin: {0}",t]);Gu(e,"PluginLoadError",{message:o}),Hy(o,n),zy(e,o)})(e,n,t)}}})(e,t,(e=>e.replace(/^\-/,""))(n))}))})(e);const t=TN(e);((e,t)=>{const n={show:M.from(t.show).getOr(S),hide:M.from(t.hide).getOr(S),isEnabled:M.from(t.isEnabled).getOr(L),setEnabled:n=>{e.mode.isReadOnly()||M.from(t.setEnabled).each((e=>e(n)))}};e.ui={...e.ui,...n}})(e,M.from(t.api).getOr({}));const n={editorContainer:t.editorContainer,iframeContainer:t.iframeContainer};return e.editorContainer=n.editorContainer?n.editorContainer:null,(e=>{e.contentCSS=e.contentCSS.concat((e=>$y(e,dl(e)))(e),(e=>$y(e,ul(e)))(e))})(e),e.inline?NN(e):((e,t)=>{((e,t)=>{const n=e.translate("Rich Text Area"),o=Gt(fn(e.getElement()),"tabindex").bind(Ge),r=((e,t,n,o)=>{const r=un("iframe");return o.each((e=>qt(r,"tabindex",e))),Wt(r,n),Wt(r,{id:e+"_ifr",frameBorder:"0",allowTransparency:"true",title:t}),on(r,"tox-edit-area__iframe"),r})(e.id,n,Pi(e),o).dom;r.onload=()=>{r.onload=null,e.dispatch("load")},e.contentAreaContainer=t.iframeContainer,e.iframeElement=r,e.iframeHTML=(e=>{let t=Li(e)+"<html><head>";Mi(e)!==e.documentBaseUrl&&(t+='<base href="'+e.documentBaseURI.getURI()+'" />'),t+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />';const n=Ii(e),o=Fi(e),r=e.translate(Hl(e));return Ui(e)&&(t+='<meta http-equiv="Content-Security-Policy" content="'+Ui(e)+'" />'),t+=`</head><body id="${n}" class="mce-content-body ${o}" data-id="${e.id}" aria-label="${r}"><br></body></html>`,t})(e),RN.add(t.iframeContainer,r)})(e,t),t.editorContainer&&(RN.get(t.editorContainer).style.display=e.orgDisplay,e.hidden=RN.isHidden(t.editorContainer)),e.getElement().style.display="none",RN.setAttrib(e.id,"aria-hidden","true"),NN(e)})(e,n)},DN=Hs.DOM,PN=e=>"-"===e.charAt(0),LN=(e,t,n)=>M.from(t).filter((e=>We(e)&&!Ny.has(e))).map((t=>({url:`${e.editorManager.baseURL}/icons/${t}/icons${n}.js`,name:M.some(t)}))),MN=(e,t)=>{const n=qs.ScriptLoader,o=()=>{!e.removed&&(e=>{const t=yl(e);return!m(t)||C(Iy.get(t))})(e)&&(e=>{const t=wl(e);return C(Ry.get(t))})(e)&&BN(e)};((e,t)=>{const n=yl(e);if(m(n)&&!PN(n)&&!xe(Iy.urls,n)){const o=Cl(e),r=o?e.documentBaseURI.toAbsolute(o):`themes/${n}/theme${t}.js`;Iy.load(n,r).catch((()=>{((e,t,n)=>{jy(e,"ThemeLoadError",Vy("theme",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=wl(e);if("plugin"!==n&&!xe(Ry.urls,n)){const o=xl(e),r=m(o)?e.documentBaseURI.toAbsolute(o):`models/${n}/model${t}.js`;Ry.load(n,r).catch((()=>{((e,t,n)=>{jy(e,"ModelLoadError",Vy("model",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=sl(t),o=al(t);if(!1===Xs.hasCode(n)&&"en"!==n){const r=We(o)?o:`${t.editorManager.baseURL}/langs/${n}.js`;e.add(r).catch((()=>{((e,t,n)=>{jy(e,"LanguageLoadError",Vy("language",t,n))})(t,r,n)}))}})(n,e),((e,t,n)=>{const o=LN(t,"default",n),r=(e=>M.from(Ji(e)).filter(We).map((e=>({url:e,name:M.none()}))))(t).orThunk((()=>LN(t,Qi(t),"")));$((e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t})([o,r]),(n=>{e.add(n.url).catch((()=>{((e,t,n)=>{jy(e,"IconsLoadError",Vy("icons",t,n))})(t,n.url,n.name.getOrUndefined())}))}))})(n,e,t),((e,t)=>{const n=(t,n)=>{My.load(t,n).catch((()=>{((e,t,n)=>{jy(e,"PluginLoadError",Vy("plugin",t,n))})(e,n,t)}))};fe(Fl(e),((t,o)=>{n(o,t),e.options.set("plugins",Il(e).concat(o))})),$(Il(e),(e=>{!(e=Bt.trim(e))||My.urls[e]||PN(e)||n(e,`plugins/${e}/plugin${t}.js`)}))})(e,t),n.loadQueue().then(o,o)},IN=Ct().deviceType,FN=IN.isPhone(),UN=IN.isTablet(),zN=e=>{if(y(e))return[];{const t=p(e)?e:e.split(/[ ,]/),n=H(t,He);return K(n,We)}},jN=(e,t)=>{const n=((t,n)=>{const o={},r={};return be(t,((t,n)=>j(e,n)),he(o),he(r)),{t:o,f:r}})(t);return o=n.t,r=n.f,{sections:N(o),options:N(r)};var o,r},VN=(e,t)=>xe(e.sections(),t),HN=(e,t)=>({table_grid:!1,object_resizing:!1,resize:!1,toolbar_mode:we(e,"toolbar_mode").getOr("scrolling"),toolbar_sticky:!1,...t?{menubar:!1}:{}}),$N=(e,t)=>{var n;const o=null!==(n=t.external_plugins)&&void 0!==n?n:{};return e&&e.external_plugins?Bt.extend({},e.external_plugins,o):o},qN=(e,t,n,o,r)=>{var s;const a=e?{mobile:HN(null!==(s=r.mobile)&&void 0!==s?s:{},t)}:{},i=jN(["mobile"],Kk(a,r)),l=Bt.extend(n,o,i.options(),((e,t)=>e&&VN(t,"mobile"))(e,i)?((e,t,n={})=>{const o=e.sections(),r=we(o,t).getOr({});return Bt.extend({},n,r)})(i,"mobile"):{},{external_plugins:$N(o,i.options())});return((e,t,n,o)=>{const r=zN(n.forced_plugins),s=zN(o.plugins),a=((e,t)=>VN(e,t)?e.sections()[t]:{})(t,"mobile"),i=((e,t,n,o)=>e&&VN(t,"mobile")?o:n)(e,t,s,a.plugins?zN(a.plugins):s),l=((e,t)=>[].concat(zN(e)).concat(zN(t)))(r,i);return Bt.extend(o,{forced_plugins:r,plugins:l})})(e,i,o,l)},WN=e=>{(e=>{const t=t=>()=>{$("left,center,right,justify".split(","),(n=>{t!==n&&e.formatter.remove("align"+n)})),"none"!==t&&((t,n)=>{e.formatter.toggle(t,void 0),e.nodeChanged()})("align"+t)};e.editorCommands.addCommands({JustifyLeft:t("left"),JustifyCenter:t("center"),JustifyRight:t("right"),JustifyFull:t("justify"),JustifyNone:t("none")})})(e),(e=>{const t=t=>()=>{const n=e.selection,o=n.isCollapsed()?[e.dom.getParent(n.getNode(),e.dom.isBlock)]:n.getSelectedBlocks();return V(o,(n=>C(e.formatter.matchNode(n,t))))};e.editorCommands.addCommands({JustifyLeft:t("alignleft"),JustifyCenter:t("aligncenter"),JustifyRight:t("alignright"),JustifyFull:t("alignjustify")},"state")})(e)},KN=(e,t)=>{const n=e.selection,o=e.dom;return/^ | $/.test(t)?((e,t,n)=>{const o=fn(e.getRoot());return n=Mg(o,Ya.fromRangeStart(t))?n.replace(/^ /,"&nbsp;"):n.replace(/^&nbsp;/," "),Ig(o,Ya.fromRangeEnd(t))?n.replace(/(&nbsp;| )(<br( \/)>)?$/,"&nbsp;"):n.replace(/&nbsp;(<br( \/)?>)?$/," ")})(o,n.getRng(),t):t},GN=(e,t)=>{const{content:n,details:o}=(e=>{if("string"!=typeof e){const t=Bt.extend({paste:e.paste,data:{paste:e.paste}},e);return{content:e.content,details:t}}return{content:e,details:{}}})(t);Dv(e,{content:KN(e,n),format:"html",set:!1,selection:!0,paste:o.paste}).each((t=>{const n=((e,t,n)=>dy(e).editor.insertContent(t,n))(e,t.content,o);Pv(e,n,t),e.addVisual()}))},YN={"font-size":"size","font-family":"face"},XN=e=>(t,n)=>M.from(n).map(fn).filter(Ut).bind((n=>((e,t,n)=>ih(fn(n),(t=>(t=>Kn(t,e).orThunk((()=>"font"===Mt(t)?we(YN,e).bind((e=>Gt(t,e))):M.none())))(t)),(e=>vn(fn(t),e))))(e,t,n.dom).or(((e,t)=>M.from(Hs.DOM.getStyle(t,e,!0)))(e,n.dom)))).getOr(""),QN=XN("font-size"),JN=_((e=>e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")),XN("font-family")),ZN=e=>xc(e.getBody()).map((e=>{const t=e.container();return Ro(t)?t.parentNode:t})),eR=(e,t)=>((e,t)=>(e=>M.from(e.selection.getRng()).bind((t=>{const n=e.getBody();return t.startContainer===n&&0===t.startOffset?M.none():M.from(e.selection.getStart(!0))})))(e).orThunk(O(ZN,e)).map(fn).filter(Ut).bind(t))(e,E(M.some,t)),tR=(e,t)=>{if(/^[0-9.]+$/.test(t)){const n=parseInt(t,10);if(n>=1&&n<=7){const o=(e=>Bt.explode(e.options.get("font_size_style_values")))(e),r=(e=>Bt.explode(e.options.get("font_size_classes")))(e);return r?r[n-1]||t:o[n-1]||t}return t}return t},nR=e=>{const t=e.split(/\s*,\s*/);return H(t,(e=>-1===e.indexOf(" ")||ze(e,'"')||ze(e,"'")?e:`'${e}'`)).join(",")},oR=e=>{WN(e),(e=>{e.editorCommands.addCommands({"Cut,Copy,Paste":t=>{const n=e.getDoc();let o;try{n.execCommand(t)}catch(e){o=!0}if("paste"!==t||n.queryCommandEnabled(t)||(o=!0),o||!n.queryCommandSupported(t)){let t=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");(Nt.os.isMacOS()||Nt.os.isiOS())&&(t=t.replace(/Ctrl\+/g,"\u2318+")),e.notificationManager.open({text:t,type:"error"})}}})})(e),(e=>{e.editorCommands.addCommands({mceAddUndoLevel:()=>{e.undoManager.add()},mceEndUndoLevel:()=>{e.undoManager.add()},Undo:()=>{e.undoManager.undo()},Redo:()=>{e.undoManager.redo()}})})(e),(e=>{e.editorCommands.addCommands({mceSelectNodeDepth:(t,n,o)=>{let r=0;e.dom.getParent(e.selection.getNode(),(t=>{if(1===t.nodeType&&r++===o)return e.selection.select(t),!1}),e.getBody())},mceSelectNode:(t,n,o)=>{e.selection.select(o)},selectAll:()=>{const t=e.dom.getParent(e.selection.getStart(),Mo);if(t){const n=e.dom.createRng();n.selectNodeContents(t),e.selection.setRng(n)}}})})(e),(e=>{e.editorCommands.addCommands({mceCleanup:()=>{const t=e.selection.getBookmark();e.setContent(e.getContent()),e.selection.moveToBookmark(t)},insertImage:(t,n,o)=>{GN(e,e.dom.createHTML("img",{src:o}))},insertHorizontalRule:()=>{e.execCommand("mceInsertContent",!1,"<hr>")},insertText:(t,n,o)=>{GN(e,e.dom.encode(o))},insertHTML:(t,n,o)=>{GN(e,o)},mceInsertContent:(t,n,o)=>{GN(e,o)},mceSetContent:(t,n,o)=>{e.setContent(o)},mceReplaceContent:(t,n,o)=>{e.execCommand("mceInsertContent",!1,o.replace(/\{\$selection\}/g,e.selection.getContent({format:"text"})))},mceNewDocument:()=>{e.setContent("")}})})(e),(e=>{const t=(t,n,o)=>{const r=m(o)?{href:o}:o,s=e.dom.getParent(e.selection.getNode(),"a");f(r)&&m(r.href)&&(r.href=r.href.replace(/ /g,"%20"),s&&r.href||e.formatter.remove("link"),r.href&&e.formatter.apply("link",r,s))};e.editorCommands.addCommands({unlink:()=>{if(e.selection.isCollapsed()){const t=e.dom.getParent(e.selection.getStart(),"a");t&&e.dom.remove(t,!0)}else e.formatter.remove("link")},mceInsertLink:t,createLink:t})})(e),(e=>{e.editorCommands.addCommands({Indent:()=>{(e=>{kx(e,"indent")})(e)},Outdent:()=>{Sx(e)}}),e.editorCommands.addCommands({Outdent:()=>Cx(e)},"state")})(e),(e=>{e.editorCommands.addCommands({insertParagraph:()=>{A_(d_,e)},mceInsertNewLine:(t,n,o)=>{O_(e,o)},InsertLineBreak:(t,n,o)=>{A_(b_,e)}})})(e),(e=>{(e=>{e.editorCommands.addCommands({"InsertUnorderedList,InsertOrderedList":t=>{e.getDoc().execCommand(t);const n=e.dom.getParent(e.selection.getNode(),"ol,ul");if(n){const t=n.parentNode;if(/^(H[1-6]|P|ADDRESS|PRE)$/.test(t.nodeName)){const o=e.selection.getBookmark();e.dom.split(t,n),e.selection.moveToBookmark(o)}}}})})(e),(e=>{e.editorCommands.addCommands({"InsertUnorderedList,InsertOrderedList":t=>{const n=e.dom.getParent(e.selection.getNode(),"ul,ol");return n&&("insertunorderedlist"===t&&"UL"===n.tagName||"insertorderedlist"===t&&"OL"===n.tagName)}},"state")})(e)})(e),(e=>{(e=>{const t=(t,n)=>{e.formatter.toggle(t,n),e.nodeChanged()};e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>{t(e)},"ForeColor,HiliteColor":(e,n,o)=>{t(e,{value:o})},BackColor:(e,n,o)=>{t("hilitecolor",{value:o})},FontName:(t,n,o)=>{((e,t)=>{const n=tR(e,t);e.formatter.toggle("fontname",{value:nR(n)}),e.nodeChanged()})(e,o)},FontSize:(t,n,o)=>{((e,t)=>{e.formatter.toggle("fontsize",{value:tR(e,t)}),e.nodeChanged()})(e,o)},LineHeight:(t,n,o)=>{((e,t)=>{e.formatter.toggle("lineheight",{value:String(t)}),e.nodeChanged()})(e,o)},Lang:(e,n,o)=>{t(e,{value:o.code,customValue:o.customCode})},RemoveFormat:t=>{e.formatter.remove(t)},mceBlockQuote:()=>{t("blockquote")},FormatBlock:(e,n,o)=>{t(m(o)?o:"p")},mceToggleFormat:(e,n,o)=>{t(o)}})})(e),(e=>{const t=t=>e.formatter.match(t);e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>t(e),mceBlockQuote:()=>t("blockquote")},"state"),e.editorCommands.addQueryValueHandler("FontName",(()=>(e=>eR(e,(t=>JN(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("FontSize",(()=>(e=>eR(e,(t=>QN(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("LineHeight",(()=>(e=>eR(e,(t=>{const n=fn(e.getBody()),o=ih(t,(e=>Kn(e,"line-height")),O(vn,n));return o.getOrThunk((()=>{const e=parseFloat(qn(t,"line-height")),n=parseFloat(qn(t,"font-size"));return String(e/n)}))})).getOr(""))(e)))})(e)})(e),(e=>{e.editorCommands.addCommands({mceRemoveNode:(t,n,o)=>{const r=null!=o?o:e.selection.getNode();if(r!==e.getBody()){const t=e.selection.getBookmark();e.dom.remove(r,!0),e.selection.moveToBookmark(t)}},mcePrint:()=>{e.getWin().print()},mceFocus:(t,n,o)=>{((e,t)=>{e.removed||(t?kf(e):(e=>{const t=e.selection,n=e.getBody();let o=t.getRng();e.quirks.refreshContentEditable(),void 0!==e.bookmark&&!1===xf(e)&&mf(e).each((t=>{e.selection.setRng(t),o=t}));const r=((e,t)=>e.dom.getParent(t,(t=>"true"===e.dom.getContentEditable(t))))(e,t.getNode());if(e.dom.isChildOf(r,n))return wf(r),Cf(e,o),void kf(e);e.inline||(Nt.browser.isOpera()||wf(n),e.getWin().focus()),(Nt.browser.isFirefox()||e.inline)&&(wf(n),Cf(e,o)),kf(e)})(e))})(e,o)},mceToggleVisualAid:()=>{e.hasVisual=!e.hasVisual,e.addVisual()}})})(e)};class rR{constructor(e){this.commands={state:{},exec:{},value:{}},this.editor=e}execCommand(e,t,n,o){const r=this.editor,s=e.toLowerCase(),a=null==o?void 0:o.skip_focus;if(r.removed)return!1;if("mcefocus"!==s&&(/^(mceAddUndoLevel|mceEndUndoLevel)$/i.test(s)||a?(e=>{mf(e).each((t=>e.selection.setRng(t)))})(r):r.focus()),r.dispatch("BeforeExecCommand",{command:e,ui:t,value:n}).isDefaultPrevented())return!1;const i=this.commands.exec[s];return!!w(i)&&(i(s,t,n),r.dispatch("ExecCommand",{command:e,ui:t,value:n}),!0)}queryCommandState(e){if(this.editor.quirks.isHidden()||this.editor.removed)return!1;const t=e.toLowerCase(),n=this.commands.state[t];return!!w(n)&&n(t)}queryCommandValue(e){if(this.editor.quirks.isHidden()||this.editor.removed)return"";const t=e.toLowerCase(),n=this.commands.value[t];return w(n)?n(t):""}addCommands(e,t="exec"){const n=this.commands;fe(e,((e,o)=>{$(o.toLowerCase().split(","),(o=>{n[t][o]=e}))}))}addCommand(e,t,n){const o=e.toLowerCase();this.commands.exec[o]=(e,o,r)=>t.call(null!=n?n:this.editor,o,r)}queryCommandSupported(e){const t=e.toLowerCase();return!!this.commands.exec[t]}addQueryStateHandler(e,t,n){this.commands.state[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}addQueryValueHandler(e,t,n){this.commands.value[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}}const sR="data-mce-contenteditable",aR=(e,t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},iR=(e,t)=>{e.dom.contentEditable=t?"true":"false"},lR=(e,t)=>{const n=fn(e.getBody());((e,t,n)=>{an(e,t)&&!1===n?sn(e,t):n&&on(e,t)})(n,"mce-content-readonly",t),t?(e.selection.controlSelection.hideResizeRect(),e._selectionOverrides.hideFakeCaret(),(e=>{M.from(e.selection.getNode()).each((e=>{e.removeAttribute("data-mce-selected")}))})(e),e.readonly=!0,iR(n,!1),$(na(n,'*[contenteditable="true"]'),(e=>{qt(e,sR,"true"),iR(e,!1)}))):(e.readonly=!1,iR(n,!0),$(na(n,'*[data-mce-contenteditable="true"]'),(e=>{Xt(e,sR),iR(e,!0)})),aR(e,"StyleWithCSS",!1),aR(e,"enableInlineTableEditing",!1),aR(e,"enableObjectResizing",!1),(e=>xf(e)||(e=>{const t=Mn(fn(e.getElement()));return of(t).filter((t=>!(e=>{const t=e.classList;return void 0!==t&&(t.contains("tox-edit-area")||t.contains("tox-edit-area__iframe")||t.contains("mce-content-body"))})(t.dom)&&bf(e,t.dom))).isSome()})(e))(e)&&e.focus(),(e=>{e.selection.setRng(e.selection.getRng())})(e),e.nodeChanged())},dR=e=>e.readonly,cR=e=>{e.parser.addAttributeFilter("contenteditable",(t=>{dR(e)&&$(t,(e=>{e.attr(sR,e.attr("contenteditable")),e.attr("contenteditable","false")}))})),e.serializer.addAttributeFilter(sR,(t=>{dR(e)&&$(t,(e=>{e.attr("contenteditable",e.attr(sR))}))})),e.serializer.addTempAttr(sR)},uR=["copy"],mR=Bt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input beforeinput contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend touchcancel"," ");class fR{constructor(e){this.bindings={},this.settings=e||{},this.scope=this.settings.scope||this,this.toggleEvent=this.settings.toggleEvent||P}static isNative(e){return!!mR[e.toLowerCase()]}fire(e,t){return this.dispatch(e,t)}dispatch(e,t){const n=e.toLowerCase(),o=Rs(n,null!=t?t:{},this.scope);this.settings.beforeFire&&this.settings.beforeFire(o);const r=this.bindings[n];if(r)for(let e=0,t=r.length;e<t;e++){const t=r[e];if(!t.removed){if(t.once&&this.off(n,t.func),o.isImmediatePropagationStopped())return o;if(!1===t.func.call(this.scope,o))return o.preventDefault(),o}}return o}on(e,t,n,o){if(!1===t&&(t=P),t){const r={func:t,removed:!1};o&&Bt.extend(r,o);const s=e.toLowerCase().split(" ");let a=s.length;for(;a--;){const e=s[a];let t=this.bindings[e];t||(t=[],this.toggleEvent(e,!0)),t=n?[r,...t]:[...t,r],this.bindings[e]=t}}return this}off(e,t){if(e){const n=e.toLowerCase().split(" ");let o=n.length;for(;o--;){const r=n[o];let s=this.bindings[r];if(!r)return fe(this.bindings,((e,t)=>{this.toggleEvent(t,!1),delete this.bindings[t]})),this;if(s){if(t){const e=W(s,(e=>e.func===t));s=e.fail,this.bindings[r]=s,$(e.pass,(e=>{e.removed=!0}))}else s.length=0;s.length||(this.toggleEvent(e,!1),delete this.bindings[r])}}}else fe(this.bindings,((e,t)=>{this.toggleEvent(t,!1)})),this.bindings={};return this}once(e,t,n){return this.on(e,t,n,{once:!0})}has(e){return e=e.toLowerCase(),!(!this.bindings[e]||0===this.bindings[e].length)}}const gR=e=>(e._eventDispatcher||(e._eventDispatcher=new fR({scope:e,toggleEvent:(t,n)=>{fR.isNative(t)&&e.toggleNativeEvent&&e.toggleNativeEvent(t,n)}})),e._eventDispatcher),pR={fire(e,t,n){return this.dispatch(e,t,n)},dispatch(e,t,n){const o=this;if(o.removed&&"remove"!==e&&"detach"!==e)return Rs(e.toLowerCase(),null!=t?t:{},o);const r=gR(o).dispatch(e,t);if(!1!==n&&o.parent){let t=o.parent();for(;t&&!r.isPropagationStopped();)t.dispatch(e,r,!1),t=t.parent()}return r},on(e,t,n){return gR(this).on(e,t,n)},off(e,t){return gR(this).off(e,t)},once(e,t){return gR(this).once(e,t)},hasEventListeners(e){return gR(this).has(e)}},hR=Hs.DOM;let bR;const vR=(e,t)=>{if("selectionchange"===t)return e.getDoc();if(!e.inline&&/^mouse|touch|click|contextmenu|drop|dragover|dragend/.test(t))return e.getDoc().documentElement;const n=bl(e);return n?(e.eventRoot||(e.eventRoot=hR.select(n)[0]),e.eventRoot):e.getBody()},yR=(e,t,n)=>{(e=>!e.hidden&&!dR(e))(e)?e.dispatch(t,n):dR(e)&&((e,t)=>{if((e=>"click"===e.type)(t)&&!em.metaKeyPressed(t)){const n=fn(t.target);((e,t)=>Go(t,"a",(t=>vn(t,fn(e.getBody())))).bind((e=>Gt(e,"href"))))(e,n).each((n=>{if(t.preventDefault(),/^#/.test(n)){const t=e.dom.select(`${n},[name="${o=n,ze(o,"#")?((e,t)=>e.substring(t))(o,"#".length):o}"]`);t.length&&e.selection.scrollIntoView(t[0],!0)}else window.open(n,"_blank","rel=noopener noreferrer,menubar=yes,toolbar=yes,location=yes,status=yes,resizable=yes,scrollbars=yes");var o}))}else(e=>j(uR,e.type))(t)&&e.dispatch(t.type,t)})(e,n)},CR=(e,t)=>{let n;if(e.delegates||(e.delegates={}),e.delegates[t]||e.removed)return;const o=vR(e,t);if(bl(e)){if(bR||(bR={},e.editorManager.on("removeEditor",(()=>{e.editorManager.activeEditor||bR&&(fe(bR,((t,n)=>{e.dom.unbind(vR(e,n))})),bR=null)}))),bR[t])return;n=n=>{const o=n.target,r=e.editorManager.get();let s=r.length;for(;s--;){const e=r[s].getBody();(e===o||hR.isChildOf(o,e))&&yR(r[s],t,n)}},bR[t]=n,hR.bind(o,t,n)}else n=n=>{yR(e,t,n)},hR.bind(o,t,n),e.delegates[t]=n},wR={...pR,bindPendingEventDelegates(){const e=this;Bt.each(e._pendingNativeEvents,(t=>{CR(e,t)}))},toggleNativeEvent(e,t){const n=this;"focus"!==e&&"blur"!==e&&(n.removed||(t?n.initialized?CR(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&(n.dom.unbind(vR(n,e),e,n.delegates[e]),delete n.delegates[e])))},unbindAllNativeEvents(){const e=this,t=e.getBody(),n=e.dom;e.delegates&&(fe(e.delegates,((t,n)=>{e.dom.unbind(vR(e,n),n,t)})),delete e.delegates),!e.inline&&t&&n&&(t.onload=null,n.unbind(e.getWin()),n.unbind(e.getDoc())),n&&(n.unbind(t),n.unbind(e.getContainer()))}},xR=e=>m(e)?{value:e.split(/[ ,]/),valid:!0}:k(e,m)?{value:e,valid:!0}:{valid:!1,message:"The value must be a string[] or a comma/space separated string."},kR=(e,t)=>e+(Ke(t.message)?"":`. ${t.message}`),SR=e=>e.valid,_R=(e,t,n="")=>{const o=t(e);return b(o)?o?{value:e,valid:!0}:{valid:!1,message:n}:o},ER=["design","readonly"],NR=(e,t,n,o)=>{const r=n[t.get()],s=n[o];try{s.activate()}catch(e){return void console.error(`problem while activating editor mode ${o}:`,e)}r.deactivate(),r.editorReadOnly!==s.editorReadOnly&&lR(e,s.editorReadOnly),t.set(o),((e,t)=>{e.dispatch("SwitchMode",{mode:t})})(e,o)},RR=Bt.each,AR=Bt.explode,OR={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},TR=Bt.makeMap("alt,ctrl,shift,meta,access"),BR=e=>{let t;const n={},o=Nt.os.isMacOS()||Nt.os.isiOS();RR(AR(e.toLowerCase(),"+"),(e=>{e in TR?n[e]=!0:/^[0-9]{2,}$/.test(e)?n.keyCode=parseInt(e,10):(n.charCode=e.charCodeAt(0),n.keyCode=OR[e]||e.toUpperCase().charCodeAt(0))}));const r=[n.keyCode];for(t in TR)n[t]?r.push(t):n[t]=!1;return n.id=r.join(","),n.access&&(n.alt=!0,o?n.ctrl=!0:n.shift=!0),n.meta&&(o?n.meta=!0:(n.ctrl=!0,n.meta=!1)),n};class DR{constructor(e){this.shortcuts={},this.pendingPatterns=[],this.editor=e;const t=this;e.on("keyup keypress keydown",(e=>{!t.hasModifier(e)&&!t.isFunctionKey(e)||e.isDefaultPrevented()||(RR(t.shortcuts,(n=>{if(t.matchShortcut(e,n))return t.pendingPatterns=n.subpatterns.slice(0),"keydown"===e.type&&t.executeShortcutAction(n),!0})),t.matchShortcut(e,t.pendingPatterns[0])&&(1===t.pendingPatterns.length&&"keydown"===e.type&&t.executeShortcutAction(t.pendingPatterns[0]),t.pendingPatterns.shift()))}))}add(e,t,n,o){const r=this,s=r.normalizeCommandFunc(n);return RR(AR(Bt.trim(e)),(e=>{const n=r.createShortcut(e,t,s,o);r.shortcuts[n.id]=n})),!0}remove(e){const t=this.createShortcut(e);return!!this.shortcuts[t.id]&&(delete this.shortcuts[t.id],!0)}normalizeCommandFunc(e){const t=this,n=e;return"string"==typeof n?()=>{t.editor.execCommand(n,!1,null)}:Bt.isArray(n)?()=>{t.editor.execCommand(n[0],n[1],n[2])}:n}createShortcut(e,t,n,o){const r=Bt.map(AR(e,">"),BR);return r[r.length-1]=Bt.extend(r[r.length-1],{func:n,scope:o||this.editor}),Bt.extend(r[0],{desc:this.editor.translate(t),subpatterns:r.slice(1)})}hasModifier(e){return e.altKey||e.ctrlKey||e.metaKey}isFunctionKey(e){return"keydown"===e.type&&e.keyCode>=112&&e.keyCode<=123}matchShortcut(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)}executeShortcutAction(e){return e.func?e.func.call(e.scope):null}}const PR=()=>{const e=(()=>{const e={},t={},n={},o={},r={},s={},a={},i=(e,t)=>(n,o)=>e[n.toLowerCase()]={...o,type:t};return{addButton:i(e,"button"),addGroupToolbarButton:i(e,"grouptoolbarbutton"),addToggleButton:i(e,"togglebutton"),addMenuButton:i(e,"menubutton"),addSplitButton:i(e,"splitbutton"),addMenuItem:i(t,"menuitem"),addNestedMenuItem:i(t,"nestedmenuitem"),addToggleMenuItem:i(t,"togglemenuitem"),addAutocompleter:i(n,"autocompleter"),addContextMenu:i(r,"contextmenu"),addContextToolbar:i(s,"contexttoolbar"),addContextForm:i(s,"contextform"),addSidebar:i(a,"sidebar"),addIcon:(e,t)=>o[e.toLowerCase()]=t,getAll:()=>({buttons:e,menuItems:t,icons:o,popups:n,contextMenus:r,contextToolbars:s,sidebars:a})}})();return{addAutocompleter:e.addAutocompleter,addButton:e.addButton,addContextForm:e.addContextForm,addContextMenu:e.addContextMenu,addContextToolbar:e.addContextToolbar,addIcon:e.addIcon,addMenuButton:e.addMenuButton,addMenuItem:e.addMenuItem,addNestedMenuItem:e.addNestedMenuItem,addSidebar:e.addSidebar,addSplitButton:e.addSplitButton,addToggleButton:e.addToggleButton,addGroupToolbarButton:e.addGroupToolbarButton,addToggleMenuItem:e.addToggleMenuItem,getAll:e.getAll}},LR=Hs.DOM,MR=Bt.extend,IR=Bt.each;class FR{constructor(e,t,n){this.plugins={},this.contentCSS=[],this.contentStyles=[],this.loadedCSS={},this.isNotDirty=!1,this.editorManager=n,this.documentBaseUrl=n.documentBaseURL,MR(this,wR);const o=this;this.id=e,this.hidden=!1;const r=((e,t)=>qN(FN||UN,FN,t,e,t))(n.defaultOptions,t);this.options=((e,t)=>{const n={},o={},r=(e,t,n)=>{const r=_R(t,n);return SR(r)?(o[e]=r.value,!0):(console.warn(kR(`Invalid value passed for the ${e} option`,r)),!1)},s=e=>xe(n,e);return{register:(e,s)=>{const a=(e=>m(e.processor))(s)?(e=>{const t=(()=>{switch(e){case"array":return p;case"boolean":return b;case"function":return w;case"number":return x;case"object":return f;case"string":return m;case"string[]":return xR;case"object[]":return e=>k(e,f);case"regexp":return e=>u(e,RegExp)}})();return n=>_R(n,t,`The value must be a ${e}.`)})(s.processor):s.processor,i=((e,t,n)=>{if(!v(t)){const o=_R(t,n);if(SR(o))return o.value;console.error(kR(`Invalid default value passed for the "${e}" option`,o))}})(e,s.default,a);n[e]={...s,default:i,processor:a},we(o,e).orThunk((()=>we(t,e))).each((t=>r(e,t,a)))},isRegistered:s,get:e=>we(o,e).orThunk((()=>we(n,e).map((e=>e.default)))).getOrUndefined(),set:(e,t)=>{if(s(e)){const o=n[e];return o.immutable?(console.error(`"${e}" is an immutable option and cannot be updated`),!1):r(e,t,o.processor)}return console.warn(`"${e}" is not a registered option. Ensure the option has been registered before setting a value.`),!1},unset:e=>{const t=s(e);return t&&delete o[e],t},isSet:e=>xe(o,e)}})(0,r),(e=>{const t=e.options.register;t("id",{processor:"string",default:e.id}),t("selector",{processor:"string"}),t("target",{processor:"object"}),t("suffix",{processor:"string"}),t("cache_suffix",{processor:"string"}),t("base_url",{processor:"string"}),t("referrer_policy",{processor:"string",default:""}),t("language_load",{processor:"boolean"}),t("inline",{processor:"boolean",default:!1}),t("iframe_attrs",{processor:"object",default:{}}),t("doctype",{processor:"string",default:"<!DOCTYPE html>"}),t("document_base_url",{processor:"string",default:e.documentBaseUrl}),t("body_id",{processor:Di(e,"tinymce"),default:"tinymce"}),t("body_class",{processor:Di(e),default:""}),t("content_security_policy",{processor:"string",default:""}),t("br_in_pre",{processor:"boolean",default:!0}),t("forced_root_block",{processor:e=>{const t=m(e)&&We(e);return t?{value:e,valid:t}:{valid:!1,message:"Must be a non-empty string."}},default:"p"}),t("forced_root_block_attrs",{processor:"object",default:{}}),t("newline_behavior",{processor:e=>{const t=j(["block","linebreak","invert","default"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: block, linebreak, invert or default."}},default:"default"}),t("br_newline_selector",{processor:"string",default:".mce-toc h2,figcaption,caption"}),t("no_newline_selector",{processor:"string",default:""}),t("keep_styles",{processor:"boolean",default:!0}),t("end_container_on_empty_block",{processor:e=>b(e)||m(e)?{valid:!0,value:e}:{valid:!1,message:"Must be boolean or a string"},default:"blockquote"}),t("font_size_style_values",{processor:"string",default:"xx-small,x-small,small,medium,large,x-large,xx-large"}),t("font_size_legacy_values",{processor:"string",default:"xx-small,small,medium,large,x-large,xx-large,300%"}),t("font_size_classes",{processor:"string",default:""}),t("automatic_uploads",{processor:"boolean",default:!0}),t("images_reuse_filename",{processor:"boolean",default:!1}),t("images_replace_blob_uris",{processor:"boolean",default:!0}),t("icons",{processor:"string",default:""}),t("icons_url",{processor:"string",default:""}),t("images_upload_url",{processor:"string",default:""}),t("images_upload_base_path",{processor:"string",default:""}),t("images_upload_base_path",{processor:"string",default:""}),t("images_upload_credentials",{processor:"boolean",default:!1}),t("images_upload_handler",{processor:"function"}),t("language",{processor:"string",default:"en"}),t("language_url",{processor:"string",default:""}),t("entity_encoding",{processor:"string",default:"named"}),t("indent",{processor:"boolean",default:!0}),t("indent_before",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_after",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_use_margin",{processor:"boolean",default:!1}),t("indentation",{processor:"string",default:"40px"}),t("content_css",{processor:e=>{const t=!1===e||m(e)||k(e,m);return t?m(e)?{value:H(e.split(","),He),valid:t}:p(e)?{value:e,valid:t}:!1===e?{value:[],valid:t}:{value:e,valid:t}:{valid:!1,message:"Must be false, a string or an array of strings."}},default:Rl(e)?[]:["default"]}),t("content_style",{processor:"string"}),t("content_css_cors",{processor:"boolean",default:!1}),t("font_css",{processor:e=>{const t=m(e)||k(e,m);return t?{value:p(e)?e:H(e.split(","),He),valid:t}:{valid:!1,message:"Must be a string or an array of strings."}},default:[]}),t("inline_boundaries",{processor:"boolean",default:!0}),t("inline_boundaries_selector",{processor:"string",default:"a[href],code,span.mce-annotation"}),t("object_resizing",{processor:e=>{const t=b(e)||m(e);return t?!1===e||Ni.isiPhone()||Ni.isiPad()?{value:"",valid:t}:{value:!0===e?"table,img,figure.image,div,video,iframe":e,valid:t}:{valid:!1,message:"Must be boolean or a string"}},default:!Ri}),t("resize_img_proportional",{processor:"boolean",default:!0}),t("event_root",{processor:"object"}),t("service_message",{processor:"string"}),t("theme",{processor:e=>!1===e||m(e)||w(e),default:"silver"}),t("theme_url",{processor:"string"}),t("formats",{processor:"object"}),t("format_empty_lines",{processor:"boolean",default:!1}),t("preview_styles",{processor:e=>{const t=!1===e||m(e);return t?{value:!1===e?"":e,valid:t}:{valid:!1,message:"Must be false or a string"}},default:"font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow"}),t("custom_ui_selector",{processor:"string",default:""}),t("hidden_input",{processor:"boolean",default:!0}),t("submit_patch",{processor:"boolean",default:!0}),t("encoding",{processor:"string"}),t("add_form_submit_trigger",{processor:"boolean",default:!0}),t("add_unload_trigger",{processor:"boolean",default:!0}),t("custom_undo_redo_levels",{processor:"number",default:0}),t("disable_nodechange",{processor:"boolean",default:!1}),t("readonly",{processor:"boolean",default:!1}),t("plugins",{processor:"string[]",default:[]}),t("external_plugins",{processor:"object"}),t("forced_plugins",{processor:"string[]"}),t("model",{processor:"string",default:e.hasPlugin("rtc")?"plugin":"dom"}),t("model_url",{processor:"string"}),t("block_unsupported_drop",{processor:"boolean",default:!0}),t("visual",{processor:"boolean",default:!0}),t("visual_table_class",{processor:"string",default:"mce-item-table"}),t("visual_anchor_class",{processor:"string",default:"mce-item-anchor"}),t("iframe_aria_text",{processor:"string",default:"Rich Text Area. Press ALT-0 for help."}),t("setup",{processor:"function"}),t("init_instance_callback",{processor:"function"}),t("url_converter",{processor:"function",default:e.convertURL}),t("url_converter_scope",{processor:"object",default:e}),t("urlconverter_callback",{processor:"function"}),t("allow_conditional_comments",{processor:"boolean",default:!1}),t("allow_html_data_urls",{processor:"boolean",default:!1}),t("allow_svg_data_urls",{processor:"boolean"}),t("allow_html_in_named_anchor",{processor:"boolean",default:!1}),t("allow_script_urls",{processor:"boolean",default:!1}),t("allow_unsafe_link_target",{processor:"boolean",default:!1}),t("convert_fonts_to_spans",{processor:"boolean",default:!0,deprecated:!0}),t("fix_list_elements",{processor:"boolean",default:!1}),t("preserve_cdata",{processor:"boolean",default:!1}),t("remove_trailing_brs",{processor:"boolean"}),t("inline_styles",{processor:"boolean",default:!0,deprecated:!0}),t("element_format",{processor:"string",default:"html"}),t("entities",{processor:"string"}),t("schema",{processor:"string",default:"html5"}),t("convert_urls",{processor:"boolean",default:!0}),t("relative_urls",{processor:"boolean",default:!0}),t("remove_script_host",{processor:"boolean",default:!0}),t("custom_elements",{processor:"string"}),t("extended_valid_elements",{processor:"string"}),t("invalid_elements",{processor:"string"}),t("invalid_styles",{processor:Bi}),t("valid_children",{processor:"string"}),t("valid_classes",{processor:Bi}),t("valid_elements",{processor:"string"}),t("valid_styles",{processor:Bi}),t("verify_html",{processor:"boolean",default:!0}),t("auto_focus",{processor:e=>m(e)||!0===e}),t("browser_spellcheck",{processor:"boolean",default:!1}),t("protect",{processor:"array"}),t("images_file_types",{processor:"string",default:"jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp"}),t("deprecation_warnings",{processor:"boolean",default:!0}),t("a11y_advanced_options",{processor:"boolean",default:!1}),t("api_key",{processor:"string"}),t("paste_block_drop",{processor:"boolean",default:!1}),t("paste_data_images",{processor:"boolean",default:!0}),t("paste_preprocess",{processor:"function"}),t("paste_postprocess",{processor:"function"}),t("paste_webkit_styles",{processor:"string",default:"none"}),t("paste_remove_styles_if_webkit",{processor:"boolean",default:!0}),t("paste_merge_formats",{processor:"boolean",default:!0}),t("smart_paste",{processor:"boolean",default:!0}),t("paste_as_text",{processor:"boolean",default:!1}),t("paste_tab_spaces",{processor:"number",default:4}),t("text_patterns",{processor:e=>k(e,f)||!1===e?{value:Ei(!1===e?[]:e),valid:!0}:{valid:!1,message:"Must be an array of objects or false."},default:[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"#",format:"h1"},{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:"1. ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}]}),t("noneditable_class",{processor:"string",default:"mceNonEditable"}),t("editable_class",{processor:"string",default:"mceEditable"}),t("noneditable_regexp",{processor:e=>k(e,Oi)?{value:e,valid:!0}:Oi(e)?{value:[e],valid:!0}:{valid:!1,message:"Must be a RegExp or an array of RegExp."},default:[]}),t("table_tab_navigation",{processor:"boolean",default:!0}),e.on("ScriptsLoaded",(()=>{t("directionality",{processor:"string",default:Xs.isRtl()?"rtl":void 0}),t("placeholder",{processor:"string",default:Ai.getAttrib(e.getElement(),"placeholder")})}))})(o);const s=this.options.get;s("deprecation_warnings")&&((e,t)=>{((e,t)=>{const n=ky(e),o=Sy(t),r=o.length>0,s=n.length>0,a="mobile"===t.theme;if(r||s||a){const e="\n- ",t=a?`\n\nThemes:${e}mobile`:"",i=r?`\n\nPlugins:${e}${o.join(e)}`:"",l=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled and have been removed in TinyMCE 6.0. These features will no longer work and should be removed from the TinyMCE configuration. See https://www.tiny.cloud/docs/tinymce/6/migration-from-5x/ for more information."+t+i+l)}})(e,t)})(t,r);const a=s("suffix");a&&(n.suffix=a),this.suffix=n.suffix;const i=s("base_url");i&&n._setBaseUrl(i),this.baseUri=n.baseURI;const l=rl(o);l&&(qs.ScriptLoader._setReferrerPolicy(l),Hs.DOM.styleSheetLoader._setReferrerPolicy(l)),Qs.languageLoad=s("language_load"),Qs.baseURL=n.baseURL,this.setDirty(!1),this.documentBaseURI=new wv(Mi(o),{base_uri:this.baseUri}),this.baseURI=this.baseUri,this.inline=Rl(o),this.shortcuts=new DR(this),this.editorCommands=new rR(this),oR(this);const d=s("cache_suffix");d&&(Nt.cacheSuffix=d.replace(/^[\?\&]+/,"")),this.ui={registry:PR(),styleSheetLoader:void 0,show:S,hide:S,setEnabled:S,isEnabled:L},this.mode=(e=>{const t=Ws("design"),n=Ws({design:{activate:S,deactivate:S,editorReadOnly:!1},readonly:{activate:S,deactivate:S,editorReadOnly:!0}});return(e=>{e.serializer?cR(e):e.on("PreInit",(()=>{cR(e)}))})(e),(e=>{e.on("ShowCaret",(t=>{dR(e)&&t.preventDefault()})),e.on("ObjectSelected",(t=>{dR(e)&&t.preventDefault()}))})(e),{isReadOnly:()=>dR(e),set:o=>((e,t,n,o)=>{if(o!==n.get()){if(!xe(t,o))throw new Error(`Editor mode '${o}' is invalid`);e.initialized?NR(e,n,t,o):e.on("init",(()=>NR(e,n,t,o)))}})(e,n.get(),t,o),get:()=>t.get(),register:(e,t)=>{n.set(((e,t,n)=>{if(j(ER,t))throw new Error(`Cannot override default mode ${t}`);return{...e,[t]:{...n,deactivate:()=>{try{n.deactivate()}catch(e){console.error(`problem while deactivating editor mode ${t}:`,e)}}}}})(n.get(),e,t))}}})(o),n.dispatch("SetupEditor",{editor:this});const c=$l(o);w(c)&&c.call(o,o)}render(){(e=>{const t=e.id;Xs.setCode(sl(e));const n=()=>{DN.unbind(window,"ready",n),e.render()};if(!Ps.Event.domLoaded)return void DN.bind(window,"ready",n);if(!e.getElement())return;const o=fn(e.getElement()),r=Qt(o);e.on("remove",(()=>{q(o.dom.attributes,(e=>Xt(o,e.name))),Wt(o,r)})),e.ui.styleSheetLoader=((e,t)=>Xo.forElement(e,{contentCssCors:Ml(t),referrerPolicy:rl(t)}))(o,e),Rl(e)?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");const s=e.getElement().form||DN.getParent(t,"form");s&&(e.formElement=s,Al(e)&&!No(e.getElement())&&(DN.insertAfter(DN.create("input",{type:"hidden",name:t}),t),e.hasHiddenInput=!0),e.formEventDelegate=t=>{e.dispatch(t.type,t)},DN.bind(s,"submit reset",e.formEventDelegate),e.on("reset",(()=>{e.resetContent()})),!Ol(e)||s.submit.nodeType||s.submit.length||s._mceOldSubmit||(s._mceOldSubmit=s.submit,s.submit=()=>(e.editorManager.triggerSave(),e.setDirty(!1),s._mceOldSubmit(s)))),e.windowManager=Fy(e),e.notificationManager=Ly(e),(e=>"xml"===e.options.get("encoding"))(e)&&e.on("GetContent",(e=>{e.save&&(e.content=DN.encode(e.content))})),Tl(e)&&e.on("submit",(()=>{e.initialized&&e.save()})),Bl(e)&&(e._beforeUnload=()=>{!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),MN(e,e.suffix)})(this)}focus(e){this.execCommand("mceFocus",!1,e)}hasFocus(){return xf(this)}translate(e){return Xs.translate(e)}getParam(e,t,n){const o=this.options;return o.isRegistered(e)||(C(n)?o.register(e,{processor:n,default:t}):o.register(e,{processor:L,default:t})),o.isSet(e)||v(t)?o.get(e):t}hasPlugin(e,t){return!(!j(Il(this),e)||t&&void 0===My.get(e))}nodeChanged(e){this._nodeChangeDispatcher.nodeChanged(e)}addCommand(e,t,n){this.editorCommands.addCommand(e,t,n)}addQueryStateHandler(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)}addQueryValueHandler(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)}addShortcut(e,t,n,o){this.shortcuts.add(e,t,n,o)}execCommand(e,t,n,o){return this.editorCommands.execCommand(e,t,n,o)}queryCommandState(e){return this.editorCommands.queryCommandState(e)}queryCommandValue(e){return this.editorCommands.queryCommandValue(e)}queryCommandSupported(e){return this.editorCommands.queryCommandSupported(e)}show(){const e=this;e.hidden&&(e.hidden=!1,e.inline?e.getBody().contentEditable="true":(LR.show(e.getContainer()),LR.hide(e.id)),e.load(),e.dispatch("show"))}hide(){const e=this;e.hidden||(e.save(),e.inline?(e.getBody().contentEditable="false",e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(LR.hide(e.getContainer()),LR.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.dispatch("hide"))}isHidden(){return this.hidden}setProgressState(e,t){this.dispatch("ProgressState",{state:e,time:t})}load(e){const t=this;let n,o=t.getElement();if(t.removed)return"";if(o){(e=e||{}).load=!0;const r=No(o)?o.value:o.innerHTML;return n=t.setContent(r,e),e.element=o,e.no_events||t.dispatch("LoadContent",e),e.element=o=null,n}}save(e){const t=this;let n,o,r=t.getElement();if(r&&t.initialized&&!t.removed)return(e=e||{}).save=!0,e.element=r,n=e.content=t.getContent(e),e.no_events||t.dispatch("SaveContent",e),"raw"===e.format&&t.dispatch("RawSaveContent",e),n=e.content,No(r)?r.value=n:(!e.is_removing&&t.inline||(r.innerHTML=n),(o=LR.getParent(t.id,"form"))&&IR(o.elements,(e=>{if(e.name===t.id)return e.value=n,!1}))),e.element=r=null,!1!==e.set_dirty&&t.setDirty(!1),n}setContent(e,t){return Cy(this,e,t)}getContent(e){return((e,t={})=>{const n=((e,t)=>({...e,format:t,get:!0,getInner:!0}))(t,t.format?t.format:"html");return Tv(e,n).fold(R,(t=>{const n=((e,t)=>dy(e).editor.getContent(t))(e,t);return Bv(e,n,t)}))})(this,e)}insertContent(e,t){t&&(e=MR({content:e},t)),this.execCommand("mceInsertContent",!1,e)}resetContent(e){void 0===e?Cy(this,this.startContent,{format:"raw"}):Cy(this,e),this.undoManager.reset(),this.setDirty(!1),this.nodeChanged()}isDirty(){return!this.isNotDirty}setDirty(e){const t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.dispatch("dirty")}getContainer(){const e=this;return e.container||(e.container=LR.get(e.editorContainer||e.id+"_parent")),e.container}getContentAreaContainer(){return this.contentAreaContainer}getElement(){return this.targetElm||(this.targetElm=LR.get(this.id)),this.targetElm}getWin(){const e=this;let t;return e.contentWindow||(t=e.iframeElement,t&&(e.contentWindow=t.contentWindow)),e.contentWindow}getDoc(){const e=this;let t;return e.contentDocument||(t=e.getWin(),t&&(e.contentDocument=t.document)),e.contentDocument}getBody(){const e=this.getDoc();return this.bodyElement||(e?e.body:null)}convertURL(e,t,n){const o=this,r=o.options.get,s=Wl(o);return w(s)?s.call(o,e,n,!0,t):!r("convert_urls")||n&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:r("relative_urls")?o.documentBaseURI.toRelative(e):e=o.documentBaseURI.toAbsolute(e,r("remove_script_host"))}addVisual(e){((e,t)=>{((e,t)=>{cy(e).editor.addVisual(t)})(e,t)})(this,e)}remove(){(e=>{if(!e.removed){const{_selectionOverrides:t,editorUpload:n}=e,o=e.getBody(),r=e.getElement();o&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&r&&_y.remove(r.nextSibling),(e=>{e.dispatch("remove")})(e),e.editorManager.remove(e),!e.inline&&o&&(e=>{_y.setStyle(e.id,"display",e.orgDisplay)})(e),(e=>{e.dispatch("detach")})(e),_y.remove(e.getContainer()),Ey(t),Ey(n),e.destroy()}})(this)}destroy(e){((e,t)=>{const{selection:n,dom:o}=e;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),Ey(n),Ey(o)),(e=>{const t=e.formElement;t&&(t._mceOldSubmit&&(t.submit=t._mceOldSubmit,t._mceOldSubmit=null),_y.unbind(t,"submit reset",e.formEventDelegate))})(e),(e=>{e.contentAreaContainer=e.formElement=e.container=e.editorContainer=null,e.bodyElement=e.contentDocument=e.contentWindow=null,e.iframeElement=e.targetElm=null,e.selection&&(e.selection=e.selection.win=e.selection.dom=e.selection.dom.doc=null)})(e),e.destroyed=!0):e.remove())})(this,e)}uploadImages(){return this.editorUpload.uploadImages()}_scanForImages(){return this.editorUpload.scanForImages()}}const UR=Hs.DOM,zR=Bt.each;let jR,VR=!1,HR=[];const $R=e=>{const t=e.type;zR(GR.get(),(n=>{switch(t){case"scroll":n.dispatch("ScrollWindow",e);break;case"resize":n.dispatch("ResizeWindow",e)}}))},qR=e=>{if(e!==VR){const t=Hs.DOM;e?(t.bind(window,"resize",$R),t.bind(window,"scroll",$R)):(t.unbind(window,"resize",$R),t.unbind(window,"scroll",$R)),VR=e}},WR=e=>{const t=HR;return HR=K(HR,(t=>e!==t)),GR.activeEditor===e&&(GR.activeEditor=HR.length>0?HR[0]:null),GR.focusedEditor===e&&(GR.focusedEditor=null),t.length!==HR.length},KR="CSS1Compat"!==document.compatMode,GR={...pR,baseURI:null,baseURL:null,defaultOptions:{},documentBaseURL:null,suffix:null,majorVersion:"6",minorVersion:"1.0",releaseDate:"2022-06-29",i18n:Xs,activeEditor:null,focusedEditor:null,setup(){const e=this;let t,n,o="";n=wv.getDocumentBaseUrl(document.location),/^[^:]+:\/\/\/?[^\/]+\//.test(n)&&(n=n.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(n)||(n+="/"));const r=window.tinymce||window.tinyMCEPreInit;if(r)t=r.base||r.baseURL,o=r.suffix;else{const e=document.getElementsByTagName("script");for(let n=0;n<e.length;n++){const r=e[n].src||"";if(""===r)continue;const s=r.substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==s.indexOf(".min")&&(o=".min"),t=r.substring(0,r.lastIndexOf("/"));break}}if(!t&&document.currentScript){const e=document.currentScript.src;-1!==e.indexOf(".min")&&(o=".min"),t=e.substring(0,e.lastIndexOf("/"))}}var s;e.baseURL=new wv(n).toAbsolute(t),e.documentBaseURL=n,e.baseURI=new wv(e.baseURL),e.suffix=o,(s=e).on("AddEditor",O(vf,s)),s.on("RemoveEditor",O(yf,s))},overrideDefaults(e){const t=e.base_url;t&&this._setBaseUrl(t);const n=e.suffix;e.suffix&&(this.suffix=n),this.defaultOptions=e;const o=e.plugin_base_urls;void 0!==o&&fe(o,((e,t)=>{Qs.PluginManager.urls[t]=e}))},init(e){const t=this;let n;const o=Bt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");let r=e=>{n=e};const s=()=>{let n=0;const a=[];let i;UR.unbind(window,"ready",s),(n=>{const o=e.onpageload;o&&o.apply(t,[])})(),i=((e,t)=>{const n=[],o=w(t)?e=>V(n,(n=>t(n,e))):e=>j(n,e);for(let t=0,r=e.length;t<r;t++){const r=e[t];o(r)||n.push(r)}return n})((e=>Nt.browser.isIE()||Nt.browser.isEdge()?(Hy("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tiny.cloud/docs/tinymce/6/support/#supportedwebbrowsers"),[]):KR?(Hy("Failed to initialize the editor as the document is not in standards mode. TinyMCE requires standards mode."),[]):m(e.selector)?UR.select(e.selector):C(e.target)?[e.target]:[])(e)),Bt.each(i,(e=>{var n;(n=t.get(e.id))&&n.initialized&&!(n.getContainer()||n.getBody()).parentNode&&(WR(n),n.unbindAllNativeEvents(),n.destroy(!0),n.removed=!0,n=null)})),i=Bt.grep(i,(e=>!t.get(e.id))),0===i.length?r([]):zR(i,(s=>{((e,t)=>e.inline&&t.tagName.toLowerCase()in o)(e,s)?Hy("Could not initialize inline editor on invalid inline target element",s):((e,o,s)=>{const l=new FR(e,o,t);a.push(l),l.on("init",(()=>{++n===i.length&&r(a)})),l.targetElm=l.targetElm||s,l.render()})((e=>{let t=e.id;return t||(t=we(e,"name").filter((e=>!UR.get(e))).getOrThunk(UR.uniqueId),e.setAttribute("id",t)),t})(s),e,s)}))};return UR.bind(window,"ready",s),new Promise((e=>{n?e(n):r=t=>{e(t)}}))},get(e){return 0===arguments.length?HR.slice(0):m(e)?Q(HR,(t=>t.id===e)).getOr(null):x(e)&&HR[e]?HR[e]:null},add(e){const t=this,n=t.get(e.id);return n===e||(null===n&&HR.push(e),qR(!0),t.activeEditor=e,t.dispatch("AddEditor",{editor:e}),jR||(jR=e=>{const n=t.dispatch("BeforeUnload");if(n.returnValue)return e.preventDefault(),e.returnValue=n.returnValue,n.returnValue},window.addEventListener("beforeunload",jR))),e},createEditor(e,t){return this.add(new FR(e,t,this))},remove(e){const t=this;let n,o;if(e){if(!m(e))return o=e,h(t.get(o.id))?null:(WR(o)&&t.dispatch("RemoveEditor",{editor:o}),0===HR.length&&window.removeEventListener("beforeunload",jR),o.remove(),qR(HR.length>0),o);zR(UR.select(e),(e=>{o=t.get(e.id),o&&t.remove(o)}))}else for(n=HR.length-1;n>=0;n--)t.remove(HR[n])},execCommand(e,t,n){var o;const r=this,s=f(n)?null!==(o=n.id)&&void 0!==o?o:n.index:n;switch(e){case"mceAddEditor":if(!r.get(s)){const e=n.options;new FR(s,e,r).render()}return!0;case"mceRemoveEditor":{const e=r.get(s);return e&&e.remove(),!0}case"mceToggleEditor":{const e=r.get(s);return e?(e.isHidden()?e.show():e.hide(),!0):(r.execCommand("mceAddEditor",!1,n),!0)}}return!!r.activeEditor&&r.activeEditor.execCommand(e,t,n)},triggerSave:()=>{zR(HR,(e=>{e.save()}))},addI18n:(e,t)=>{Xs.add(e,t)},translate:e=>Xs.translate(e),setActive(e){const t=this.activeEditor;this.activeEditor!==e&&(t&&t.dispatch("deactivate",{relatedTarget:e}),e.dispatch("activate",{relatedTarget:t})),this.activeEditor=e},_setBaseUrl(e){this.baseURL=new wv(this.documentBaseURL).toAbsolute(e.replace(/\/+$/,"")),this.baseURI=new wv(this.baseURL)}};GR.setup();const YR=(()=>{const e=Js();return{FakeClipboardItem:e=>({items:e,types:ue(e),getType:t=>we(e,t).getOrUndefined()}),write:t=>{e.set(t)},read:()=>e.get().getOrUndefined(),clear:e.clear}})(),XR=Math.min,QR=Math.max,JR=Math.round,ZR=(e,t,n)=>{let o=t.x,r=t.y;const s=e.w,a=e.h,i=t.w,l=t.h,d=(n||"").split("");return"b"===d[0]&&(r+=l),"r"===d[1]&&(o+=i),"c"===d[0]&&(r+=JR(l/2)),"c"===d[1]&&(o+=JR(i/2)),"b"===d[3]&&(r-=a),"r"===d[4]&&(o-=s),"c"===d[3]&&(r-=JR(a/2)),"c"===d[4]&&(o-=JR(s/2)),eA(o,r,s,a)},eA=(e,t,n,o)=>({x:e,y:t,w:n,h:o}),tA={inflate:(e,t,n)=>eA(e.x-t,e.y-n,e.w+2*t,e.h+2*n),relativePosition:ZR,findBestRelativePosition:(e,t,n,o)=>{let r,s;for(s=0;s<o.length;s++)if(r=ZR(e,t,o[s]),r.x>=n.x&&r.x+r.w<=n.w+n.x&&r.y>=n.y&&r.y+r.h<=n.h+n.y)return o[s];return null},intersect:(e,t)=>{const n=QR(e.x,t.x),o=QR(e.y,t.y),r=XR(e.x+e.w,t.x+t.w),s=XR(e.y+e.h,t.y+t.h);return r-n<0||s-o<0?null:eA(n,o,r-n,s-o)},clamp:(e,t,n)=>{let o=e.x,r=e.y,s=e.x+e.w,a=e.y+e.h;const i=t.x+t.w,l=t.y+t.h,d=QR(0,t.x-o),c=QR(0,t.y-r),u=QR(0,s-i),m=QR(0,a-l);return o+=d,r+=c,n&&(s+=d,a+=c,o-=u,r-=m),s-=u,a-=m,eA(o,r,s-o,a-r)},create:eA,fromClientRect:e=>eA(e.left,e.top,e.width,e.height)},nA=(()=>{const e={},t={};return{load:(n,o)=>{const r=`Script at URL "${o}" failed to load`,s=`Script at URL "${o}" did not call \`tinymce.Resource.add('${n}', data)\` within 1 second`;if(void 0!==e[n])return e[n];{const a=new Promise(((e,a)=>{const i=((e,t,n=1e3)=>{let o=!1,r=null;const s=e=>(...t)=>{o||(o=!0,null!==r&&(clearTimeout(r),r=null),e.apply(null,t))},a=s(e),i=s(t);return{start:(...e)=>{o||null!==r||(r=setTimeout((()=>i.apply(null,e)),n))},resolve:a,reject:i}})(e,a);t[n]=i.resolve,qs.ScriptLoader.loadScript(o).then((()=>i.start(s)),(()=>i.reject(r)))}));return e[n]=a,a}},add:(n,o)=>{void 0!==t[n]&&(t[n](o),delete t[n]),e[n]=Promise.resolve(o)},unload:t=>{delete e[t]}}})();let oA;try{const e="__storage_test__";oA=window.localStorage,oA.setItem(e,e),oA.removeItem(e)}catch(e){oA=(()=>{let e={},t=[];const n={getItem:t=>e[t]||null,setItem:(n,o)=>{t.push(n),e[n]=String(o)},key:e=>t[e],removeItem:n=>{t=t.filter((e=>e===n)),delete e[n]},clear:()=>{t=[],e={}},length:0};return Object.defineProperty(n,"length",{get:()=>t.length,configurable:!1,enumerable:!1}),n})()}const rA={geom:{Rect:tA},util:{Delay:gf,Tools:Bt,VK:em,URI:wv,EventDispatcher:fR,Observable:pR,I18n:Xs,LocalStorage:oA,ImageUploader:e=>{const t=Ky(),n=Qy(e,t);return{upload:(t,o=!0)=>n.upload(t,o?Xy(e):void 0)}}},dom:{EventUtils:Ps,TreeWalker:Qo,TextSeeker:Ca,DOMUtils:Hs,ScriptLoader:qs,RangeUtils:Pm,Serializer:yy,StyleSheetLoader:Yo,ControlSelection:sm,BookmarkManager:Wu,Selection:hy,Event:Ps.Event},html:{Styles:_s,Entities:ms,Node:Ff,Schema:Ss,DomParser:Av,Writer:Wf,Serializer:Kf},Env:Nt,AddOnManager:Qs,Annotator:qu,Formatter:lC,UndoManager:cC,EditorCommands:rR,WindowManager:Fy,NotificationManager:Ly,EditorObservable:wR,Shortcuts:DR,Editor:FR,FocusManager:ff,EditorManager:GR,DOM:Hs.DOM,ScriptLoader:qs.ScriptLoader,PluginManager:My,ThemeManager:Iy,ModelManager:Ry,IconManager:Ny,Resource:nA,FakeClipboard:YR,trim:Bt.trim,isArray:Bt.isArray,is:Bt.is,toArray:Bt.toArray,makeMap:Bt.makeMap,each:Bt.each,map:Bt.map,grep:Bt.grep,inArray:Bt.inArray,extend:Bt.extend,walk:Bt.walk,resolve:Bt.resolve,explode:Bt.explode,_addCacheSuffix:Bt._addCacheSuffix},sA=Bt.extend(GR,rA);(e=>{window.tinymce=e,window.tinyMCE=e})(sA),(e=>{if("object"==typeof module)try{module.exports=e}catch(e){}})(sA)}();