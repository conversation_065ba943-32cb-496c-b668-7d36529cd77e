let allSearchArray = []
function findParent(allFoodTypeList, searchVal) {
  allSearchArray = []
  //遍历第一层foodType
  allFoodTypeList.forEach(fty => {
    let foodListArray = fty.foodList
    foodListArray.forEach(foodItem => {
      //搜索下层food,返回数组
      let resArr = checkFood(foodItem, 1, searchVal)
      if (resArr.length !== 0) {
        resArr.forEach(element => {
          element[0] = fty
        })
        // 根据resArr数组内长度排序
        resArr.sort((a, b) => {
          return a.length - b.length
        })
        allSearchArray.push(resArr)
      }
    })
  })
}
/**
 * @description:
 * @param {*} foodItem  FoodList数据
 * @param {*} index  父级层数
 * @return {*}
 */
function checkFood(foodItem, index, searchVal) {
  let hierarchicalArray = [] // 搜索值的层级数组
  if (!foodItem) return hierarchicalArray
  // 匹配中英文名称/标识
  const key = searchVal.replace(/([\$\^\*\(\)\+\?\{\}\[\]\.\\])/g, "\\$1")
  const reg = new RegExp(`(${key})`, "igm")
  let name = inListTitle(foodItem),
    listCode = foodItem.fCode || foodItem.code
  let res = reg.exec(name) || reg.exec(listCode)
  if (res) {
    let newArray = []
    newArray[index] = foodItem // 当前层级索引赋值,从第1层开始
    hierarchicalArray.push(newArray)
  }
  if (foodItem.foodList) {
    foodItem.foodList.forEach(food => {
      //搜索下层food,返回符合搜索结果的数组
      let resArr = checkFood(food, index + 1, searchVal)
      if (resArr.length > 0) {
        //有符合搜索结果的数据,遍历符合搜索结果的数组
        //如index=2,[[null,null,null,food],[null,null,null,food,foodType,food]]
        resArr.forEach(fightArr => {
          //设置当前索引值
          fightArr[index] = foodItem
          //如index=2,[[null,null,food,food],[null,null,food,food,foodType,food]]
          hierarchicalArray.push(fightArr)
        })
      }
    })
  }
  if (foodItem.allTypeArry) {
    let resArr = checkType(foodItem.allTypeArry, index + 1, searchVal)
    if (resArr.length > 0) {
      resArr.forEach(fightArr => {
        fightArr[index] = foodItem
        hierarchicalArray.push(fightArr)
      })
    }
  }

  return hierarchicalArray
}

function checkType(allTypeArry, index, searchVal) {
  let hierarchicalArray = []
  if (!allTypeArry) return hierarchicalArray
  allTypeArry.forEach(typeItem => {
    if (!typeItem.foodList) return
    typeItem.foodList.forEach(food => {
      let resArr = checkFood(food, index + 1, searchVal)
      if (resArr.length > 0) {
        resArr.forEach(fightArr => {
          fightArr[index] = typeItem
          hierarchicalArray.push(fightArr)
        })
      }
    })
  })
  return hierarchicalArray
}

// findParent(res)
function searchListDataClick(typeIndex, Hindex) {
  let targetArray = allSearchArray[typeIndex][Hindex],
    targetFood = targetArray[targetArray.length - 1],
    targetParent = targetArray[targetArray.length - 2],
    targetArrayLength = targetArray.length

  targetArray.forEach((item, index) => {
    // index为0时为foodType,大于0时候获取上一个对象的foodList数据找出索引
    manualAssignment(item, index)
    if (index > 0) {
      let parentArray, parentIndex
      if (item.typeName == "ftyItem") {
        parentArray = targetArray[index - 1].allTypeArry
        parentIndex = parentArray.findIndex(item => {
          return item.code === targetArray[index].code
        })
        pathList[index] = {
          id: item.code,
          type: item.typeName,
          name: outListTitle(item)
        }
      } else {
        parentArray = targetArray[index - 1].foodList
        parentIndex = parentArray.findIndex(item => {
          return item.fCode === targetArray[index].fCode
        })
        pathList[index] = {
          id: item.fCode,
          type: "food",
          name: inListTitle(item)
        }
      }
      pathList[index] = {
        ...pathList[index],
        index: parentIndex
      }
    } else if (index == 0) {
      let i = foodTypeList.findIndex(item => {
        return item.code === targetArray[0].code
      })
      pathList[0] = {
        id: item.code,
        index: i,
        type: item.typeName || "ftyItem",
        name: outListTitle(item)
      }
    }
  })
  let defaultTestII = targetArrayLength - 1
  let dataType
  if (pathList.length != 0) dataType = targetParent.typeName == "ftyItem" ? "fty" : "food"
  console.log("🚀 ~ file: cmsSearchList.js:142 ~ searchListDataClick ~ pathList", pathList)
  switch (defaultTestII) {
    case 1:
      createFoodElm(pathList[0].index, pathList[0].id, dataType)
      break
    case 2:
      createSetMealElm(pathList[1].index, pathList[1].id, dataType)
      break
    case 3:
      createSiElm(pathList[2].index, pathList[2].id, dataType)
      break
    case 4:
      createWuElm(pathList[3].index, pathList[3].id, dataType)
      break
    case 5:
      createLiuElm(pathList[4].index, pathList[4].id, dataType)
      break
    case 6:
      createQiElm(pathList[5].index, pathList[5].id, dataType)
      break
    case 7:
      createBaElm(pathList[6].index, pathList[6].id, dataType)
      break
    case 8:
      createJiuElm(pathList[7].index, pathList[7].id, dataType)
      break
    default:
    // if (type !== "init") createFoodTypeElm()
  }
  // 关闭弹窗
  $("#searchListModal").modal("hide")
  console.log(allSearchArray[typeIndex][Hindex], "点击的搜索数据")
}
// 不同层对象手动赋值
function manualAssignment(item, index) {
  switch (index) {
    case 2:
      siClickItem = item
      break
    case 3:
      wuClickItem = item
      break
    case 4:
      liuClickItem = item
      break
    case 5:
      qiClickItem = item
      break
    case 6:
      baClickItem = item
      break
    case 7:
      jiuClickItem = item
      break
    default:
      break
  }
}
// 打开搜索list弹窗
function showSearchListModal() {
  // 检查总数据foodTypeList数据是否已经加载
  if (!foodTypeList) {
    // 提示等待数据加载完成再启用搜索
    toastr.warning("Please wait for the data to load before searching")
  } else {
    $("#searchListModal").modal("show")
  }
}

//执行搜索结果
function getSearchList(content) {
  console.log("🚀 ~ file: menu.html:3040 ~ getSearchList ~ content", content)

  if (content) {
    findParent(foodTypeList, content)
    console.log("🚀 ~ file: menu.html:2972 ~ getSearchList ~ allSearchArray", allSearchArray)
    if (allSearchArray.length) {
      // jq隐藏.listSearch-Help
      $(".listSearch-Help").hide()
      // 遍历allSearchArray里面的数组拼接成字符串在.searchList-Data上
      let divDom = ""
      allSearchArray.forEach((everyType, typeIndex) => {
        everyType.forEach((everyHierarchy, Hindex) => {
          let str = ""
          everyHierarchy.forEach((item, index) => {
            // 判断item是否存在code这个字段
            if (item.code) {
              str += `<span class="searchList-Data-name">${outListTitle(item)}(${
                item.code
              })\u00A0/\u00A0</span>`
            } else {
              str += `<span class="searchList-Data-name">${inListTitle(item)}(${
                item.fCode
              })\u00A0/\u00A0</span>`
            }
          })
          // 删除str最后空格到"/"字符
          str = str.substring(0, str.lastIndexOf("\u00A0/\u00A0"))
          // 点击事件绑定索引
          divDom += `<li class="searchList-Data-li" onclick="searchListDataClick(${typeIndex},${Hindex})">
              <div class="searchList-Data-li-left">
                <span style="margin-right: 7px;">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" 
                         fill="currentColor" class="bi bi-file-earmark" viewBox="0 0 16 16">
                    <path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z"/>
                </svg>
              </span>
                <p>${str}</p> 
              </div>
              <span class='searchList-Data-selectSvg'>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-return-left" viewBox="0 0 16 16">
                      <path fill-rule="evenodd" d="M14.5 1.5a.5.5 0 0 1 .5.5v4.8a2.5 2.5 0 0 1-2.5 2.5H2.707l3.347 3.346a.5.5 0 0 1-.708.708l-4.2-4.2a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 8.3H12.5A1.5 1.5 0 0 0 14 6.8V2a.5.5 0 0 1 .5-.5z"/>
                  </svg>
              </span>
              </li>`
        })
      })
      document.querySelector(".searchList-Data").innerHTML = divDom
    } else {
      // jq显示.listSearch-Help
      document.querySelector(".searchList-Data").innerHTML = ""
      $(".listSearch-Help").show()
    }
    // console.log('ajax request ', allData)
  } else {
    document.querySelector(".searchList-Data").innerHTML = ""
    $(".listSearch-Help").show()
  }
  $(".DocSearch-Loading-Icon").hide()
  $(".DocSearch-Search-Icon").show()
}
// 输入框防抖
function debounce(fun, delay) {
  return function (args) {
    let that = this
    let _args = args
    clearTimeout(fun.id)
    fun.id = setTimeout(function () {
      fun.call(that, _args)
    }, delay)
  }
}

let inputb = document.getElementById("searchListInput")

let debounceAjax = debounce(getSearchList, 1000)

inputb.addEventListener("keyup", function (e) {
  //jq .DocSearch-Loading-Icon显示,.DocSearch-Search-Icon隐藏
  $(".DocSearch-Search-Icon").hide()
  $(".DocSearch-Loading-Icon").show()
  // 去除首尾空格
  e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, "")
  debounceAjax(e.target.value)
})

// jq监听打开搜索弹窗
$("#searchListModal").on("shown.bs.modal", function () {
  document.getElementById("searchListInput").focus()
})
// jq监听关闭搜索弹窗
$("#searchListModal").on("hide.bs.modal", function () {
  // 清空搜索框
  $("#searchListInput").val("")
  document.querySelector(".searchList-Data").innerHTML = ""
  $(".listSearch-Help").show()
})
