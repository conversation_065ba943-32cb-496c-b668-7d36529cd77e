<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>PayOrder</title>

    <link href="../static/vuetify/vuetify.min.css" rel="stylesheet" />
    <link href="../static/vendor/materialdesignicons.css" rel="stylesheet" />
    <link href="../static/vendor/Roboto.css" rel="stylesheet" />
    <link href="../static/vendor/Anta.css" rel="stylesheet" />
    <link href="../static/vendor/Nunito.css" rel="stylesheet" />
    <link href="../static/vendor/flagIcons/flag-icons.css" rel="stylesheet" />

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <script src="../static/byod_webUtils/public.js"></script>

    <style>
      /* @import "../static/vendor/Roboto.css";
      @import "../static/vendor/Anta.css";
      @import "../static/vendor/Nunito.css";
      @import "../static/vendor/flagIcons/flag-icons.css"; */
      /* @import "https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900";
      @import "https://fonts.googleapis.com/css2?family=Anta&display=swap";
      @import "https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,500;1,500&display=swap"; */

      [v-cloak] {
        display: none;
      }
      .anta-regular {
        font-family: "Anta", sans-serif;
        font-weight: 600;
        font-style: normal;
      }
      body {
        font-family: "Nunito", sans-serif;
        font-optical-sizing: auto;
        font-weight: 500;
        font-style: normal;
        box-sizing: border-box;
        --styleColor: #31958b;
      }
      #root {
        height: 100%;
        overflow: auto;
      }
      .v-list-item:has(.fi.fis) {
        min-height: 36px;
      }
      summary:focus-visible {
        outline: none;
      }
      .dovetailArrow-icon {
        position: absolute;
        right: 0.5rem;
        top: -1.3rem;
        width: 2rem;
      }
      /* 添加抖动动画到searchDialog-bottom-tip元素 */
      .shake-animation {
        animation: shake 0.5s ease-in-out;
      }
      .v-toolbar__content {
        height: calc(2.25 * 1.2rem) !important;
      }

      .text-orderStatus {
        color: var(--styleColor);
      }
      .text-notConfirmed {
        color: #fff !important;
        background-color: #ff5252 !important;
        border-radius: 0.3rem;
        padding: 0.15rem 0.2rem;
      }

      /* 订单未支付提醒和支付区域合并样式 */
      .order-unpaid-alert {
        background: linear-gradient(135deg, #ff5757 0%, #ff4757 100%);
        border: none;
        border-radius: 0.425rem;
        padding: 0.65rem 0.85rem;
        box-shadow: 0 0.125rem 0.75rem rgba(255, 71, 87, 0.2);
        position: relative;
        overflow: hidden;
        margin-bottom: 0.32rem;
      }

      .order-unpaid-alert::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.12), transparent);
        animation: shimmer 3s infinite;
      }

      .order-unpaid-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        position: relative;
        z-index: 1;
        gap: 0.65rem;
      }

      .order-unpaid-info {
        flex: 1;
        min-width: 0;
      }

      .order-unpaid-title {
        display: flex;
        align-items: center;
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 0.2rem;
        line-height: 1.2;
      }
      .order-unpaid-title-center {
        justify-content: center;
        font-size: 0.85rem;
        margin-bottom: unset;
        line-height: unset;
      }

      .order-unpaid-icon {
        margin-right: 0.32rem;
        font-size: 0.875rem !important;
        animation: pulse 2s ease-in-out infinite;
        flex-shrink: 0;
      }

      .order-unpaid-subtitle {
        font-size: 0.65rem;
        opacity: 0.92;
        display: flex;
        align-items: flex-start;
        line-height: 1.3;
        white-space: normal;
        word-wrap: break-word;
        word-break: break-word;
      }

      .order-unpaid-subtitle .v-icon {
        margin-right: 0.2rem;
        font-size: 0.75rem !important;
        flex-shrink: 0;
      }

      /* 立即支付按钮样式 */
      .payment-btn {
        background: #ffffff !important;
        border: 0.0625rem solid rgba(255, 255, 255, 0.2) !important;
        font-weight: 600 !important;
        letter-spacing: 0.01875rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.12);
        min-width: 4.7rem !important;
        height: 1.75rem !important;
        font-size: 0.7rem !important;
        border-radius: 0.32rem !important;
        text-transform: none !important;
        flex-shrink: 0;
      }

      .payment-btn:hover {
        background: rgba(255, 255, 255, 0.98) !important;
        box-shadow: 0 0.1875rem 0.75rem rgba(0, 0, 0, 0.18);
        transform: translateY(-0.03125rem);
        border-color: rgba(255, 255, 255, 0.4) !important;
      }

      .payment-btn .v-btn__content {
        color: #ff4757 !important;
        font-weight: 600;
      }

      .payment-btn .v-icon {
        transition: transform 0.3s ease !important;
        color: #ff4757 !important;
        font-size: 0.875rem !important;
      }

      .payment-btn:hover .v-icon {
        transform: translateX(0.0625rem);
      }

      .payment-btn span {
        color: #ff4757 !important;
        font-weight: 600 !important;
      }

      @keyframes shimmer {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.7;
          transform: scale(1.1);
        }
      }
      .v-dialog--fullscreen .v-sheet {
        background-color: transparent !important;
        box-shadow: none !important;
      }
    </style>
  </head>
  <body>
    <v-app id="root">
      <v-overlay :value="loading" opacity="1" color="#ffffff">
        <v-progress-circular color="primary" indeterminate size="32"></v-progress-circular>
      </v-overlay>

      <v-dialog
        v-model="payLoading"
        fullscreen
        hide-overlay
        persistent
        transition="fade-transition"
        scrim
      >
        <div class="d-flex align-center justify-center" style="height: 100%">
          <v-progress-circular indeterminate color="primary" size="32"></v-progress-circular>
        </div>
      </v-dialog>
      <version-tag></version-tag>
      <v-app-bar
        dense
        flat
        v-cloak
        app
        color="var(--styleColor)"
        style="height: calc(2.25 * 1.2rem)"
      >
        <v-btn icon color="var(--styleColor)" href="./menuPage">
          <img src="../static/img/page/black.jpg" alt="back" style="width: calc(0.8rem * 2.25)" />
          <!--           <v-icon color="#fff">mdi-arrow-u-left-top-bold</v-icon> -->
        </v-btn>
        <v-spacer></v-spacer>
        <v-btn icon color="var(--styleColor)" v-if="showGoHomeBtn" @click="toIndex">
          <v-icon color="#fff">mdi-home</v-icon>
        </v-btn>
        <v-btn icon color="var(--styleColor)" @click="showSearchDialog=true" v-if="showSearchIcon">
          <v-icon color="#fff">mdi-magnify</v-icon>
        </v-btn>
      </v-app-bar>

      <v-main v-cloak style="background-color: #f5f5f5">
        <v-toolbar dense flat style="margin-top: 10px" color="#f5f5f5">
          <template v-if="noOrders">
            <v-spacer></v-spacer>
            <v-toolbar-title class="anta-regular">
              {{systemLanguage.payOrderHeaderTxt}}
            </v-toolbar-title>
            <v-spacer></v-spacer>
          </template>
          <template v-else>
            <v-toolbar-title class="anta-regular">
              {{systemLanguage.payOrderHeaderTxt}}
            </v-toolbar-title>
            <v-spacer></v-spacer>

            <v-menu
              rounded="b-xl"
              offset-y
              bottom
              origin="center center"
              transition="slide-y-transition"
            >
              <template v-slot:activator="{ attrs, on }">
                <v-btn plain text depressed class="ma-2 text-none" v-bind="attrs" v-on="on">
                  <span>{{systemLanguage.sort}}</span>
                  <v-icon small>mdi-chevron-down</v-icon>
                </v-btn>
              </template>

              <v-list dense class="pa-0" two-line>
                <v-list-item-group mandatory v-model="sortMethodActive">
                  <v-list-item
                    v-for="item in sortMethods"
                    :key="item"
                    link
                    dense
                    selectable
                    style="min-height: 40px"
                    color="var(--styleColor)"
                  >
                    <v-list-item-title v-text="item"></v-list-item-title>
                  </v-list-item>
                </v-list-item-group>

                <v-divider></v-divider>
                <v-list-item-group mandatory v-model="sortTypeActive">
                  <v-list-item
                    v-for="item in sortTypes"
                    :key="item"
                    link
                    dense
                    selectable
                    style="min-height: 40px"
                    color="var(--styleColor)"
                  >
                    <v-list-item-title v-text="item"></v-list-item-title>
                  </v-list-item>
                </v-list-item-group>
              </v-list>
            </v-menu>
          </template>
        </v-toolbar>
        <v-layout d-flex column class="px-2">
          <template v-if="noOrders">
            <v-icon class="mt-16 align-self-center" size="100" color="#d7d8e0">mdi-shopping</v-icon>
            <div class="no-orders-tips-text pt-8 px-10 text-center text-subtitle-2">
              {{systemLanguage.nullPayOrderLan}}
            </div>
            <v-btn
              rounded
              color="var(--styleColor)"
              dark
              class="mt-8 text-none align-self-center"
              href="./menuPage"
            >
              {{systemLanguage.backMenuBtn}}
            </v-btn>
          </template>
          <template v-else>
            <template v-for="order in renderList" :key="order.merchantRef">
              <v-card
                tag="section"
                class="my-5 px-2 text-subtitle-2 text-no-wrap"
                min-height="200"
                color="#ffffff"
                style="box-shadow: unset"
                :ref="order.merchantRef"
              >
                <v-card-title class="py-2 justify-center text-subtitle-1 font-weight-bold">
                  {{semanticTime(order.date||order.createTime)}}
                </v-card-title>
                <v-divider class="pb-2"></v-divider>

                <v-row dense v-if="showOrderNotConfirmed(order)">
                  <v-col>
                    <div class="order-unpaid-alert">
                      <div class="order-unpaid-content">
                        <div class="order-unpaid-info">
                          <div
                            class="order-unpaid-title"
                            :class="{'order-unpaid-title-center':order.payKey === 'eft' && !order.sessionId}"
                          >
                            <v-icon class="order-unpaid-icon" color="white">
                              mdi-alert-circle
                            </v-icon>
                            {{systemLanguage.orderNotConfirmed}}
                          </div>
                          <div
                            v-if="order.payKey === 'eft' && order.sessionId"
                            class="order-unpaid-subtitle"
                          >
                            <v-icon color="white">mdi-credit-card-outline</v-icon>
                            {{systemLanguage.payOrderClickPayMentTip}}
                          </div>
                        </div>
                        <v-btn
                          v-if="order.payKey === 'eft' && order.sessionId"
                          class="payment-btn"
                          @click="handlePaymentRedirect(order.sessionId)"
                          elevation="0"
                        >
                          <span>{{systemLanguage.payOrderPaymentBtn}}</span>
                          <v-icon right class="ml-1">mdi-arrow-right-circle</v-icon>
                        </v-btn>
                      </div>
                    </div>
                  </v-col>
                </v-row>
                <v-row dense v-if="order.storeNumber">
                  <v-col class="text-body-2">{{systemLanguage.payOrderSelectedStores}}</v-col>
                  <v-col align-self="center" class="text-right" style="color: var(--styleColor)">
                    {{getStoreName(order)}}
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col class="text-body-2">{{systemLanguage.payOrderBillNum}}</v-col>
                  <v-col align-self="center" class="text-right">{{order.billNumber}}</v-col>
                </v-row>
                <v-row dense>
                  <v-col class="text-body-2">{{systemLanguage.payOrderMerchantRef}}</v-col>
                  <v-col align-self="center" class="text-right">{{order.merchantRef}}</v-col>
                </v-row>
                <v-row dense v-if="order.payVal">
                  <v-col class="text-body-2">{{systemLanguage.successPayMethod}}</v-col>
                  <v-col align-self="center" class="text-right">
                    {{formatPayVal(order.payVal)}}
                  </v-col>
                </v-row>
                <v-row dense v-if="order.orderStatus">
                  <v-col class="text-body-2">{{systemLanguage.payOrderStatus}}</v-col>
                  <v-col align-self="center" class="text-right">
                    <span
                      class="text-orderStatus"
                      :class="{'text-notConfirmed':showOrderNotConfirmed(order)}"
                    >
                      {{orderStatusTip(order.orderStatus)}}
                    </span>
                  </v-col>
                </v-row>
                <v-row dense v-else-if="!order.billNumber">
                  <v-col class="text-body-2">{{systemLanguage.payOrderStatus}}</v-col>
                  <v-col align-self="center" class="text-right">
                    <span class="text-notConfirmed">{{orderStatusTip(10)}}</span>
                  </v-col>
                </v-row>
                <v-row dense>
                  <v-col class="text-body-2">{{systemLanguage.payOrderPickUpDate}}</v-col>
                  <v-col align-self="center" class="text-right">
                    {{ order.pickupTime|| systemLanguage.instantPickup}}
                  </v-col>
                </v-row>

                <!--  orderData -->
                <template v-for="store in order.storeList">
                  <v-divider class="my-2" v-if="store.orderData.length"></v-divider>
                  <v-row dense v-if="store.orderData.length">
                    <template v-if="order.storeList.length!==1">
                      <v-col class="text-subtitle-2" style="color: var(--styleColor)">
                        {{getStoreName(store)}}
                      </v-col>
                      <v-col align-self="center" class="text-right">
                        {{formatPrice(store.amt,store.amtType)}}
                      </v-col>
                    </template>
                    <v-col class="text-subtitle-2" v-else>
                      {{systemLanguage.selectedProducts}}
                    </v-col>
                  </v-row>
                  <v-row
                    dense
                    v-for="food in store.orderData"
                    :key="order.merchantRef+food.fCode"
                    class="my-2"
                  >
                    <v-img
                      class="rounded-lg"
                      style="background-color: #f9f9f9; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px"
                      :aspect-ratio="3/2"
                      max-width="81"
                      max-height="54"
                      :src="combinImageUrl(food,store)"
                    ></v-img>
                    <v-col align-self="start" style="width: 1px">
                      <div class="text-subtitle-2">{{inListTitle(food)}}</div>
                      <div
                        class="text-truncate text-wrap text-caption pr-5"
                        :code="food.fCode||food.code"
                        style="position: relative"
                      >
                        <span>{{getFoodDetail(food,order)}}</span>
                        <v-icon
                          size="20"
                          style="position: absolute; right: 0; top: 0"
                          @click="toggleLine"
                        >
                          mdi-chevron-down
                        </v-icon>
                      </div>
                    </v-col>
                    <v-col class="flex-grow-0 text-right">
                      <div>{{formatPrice(calculatePrice(food),order.amtType)}}</div>
                      <div>x{{food.qty1}}</div>
                    </v-col>
                  </v-row>
                </template>

                <!-- 赠送food -->
                <v-divider class="my-2" v-if="order.promotionFoodList.length"></v-divider>
                <v-row dense v-if="order.promotionFoodList.length">
                  <v-col class="text-subtitle-2">{{systemLanguage.giveAway}}</v-col>
                </v-row>
                <template
                  v-for="promotion in order.promotionFoodList"
                  :key="order.merchantRef+promotion.id"
                >
                  <v-row
                    dense
                    v-for="prof in promotion.promotionFoodList"
                    :key="order.merchantRef+prof.fCode"
                    class="my-2"
                  >
                    <v-img
                      class="rounded-lg"
                      style="background-color: #f9f9f9; box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px"
                      :aspect-ratio="3/2"
                      max-width="81"
                      max-height="54"
                      :src="combinImageUrl(prof)"
                    ></v-img>
                    <v-col align-self="start" style="width: 1px">
                      <div class="text-subtitle-2">{{inListTitle(prof)}}</div>
                      <div class="text-body-sm-2 text-caption" style="color: var(--styleColor)">
                        {{promotion.desc}}
                      </div>
                    </v-col>
                    <v-col class="flex-grow-0 text-right">
                      <div>x{{prof.qty1}}</div>
                    </v-col>
                  </v-row>
                </template>
                <!-- 折扣 -->
                <v-divider class="my-2" v-if="order.discountList.length"></v-divider>
                <v-row dense v-if="order.discountList.length">
                  <v-col class="text-subtitle-2">{{systemLanguage.discount}}</v-col>
                </v-row>
                <v-row dense v-for="discount in order.discountList" :key="discount.amount">
                  <v-col class="text-subtitle-2">{{formatDiscountDesc(discount)}}</v-col>
                  <v-col align-self="center" class="text-right">
                    -{{formatPrice(+discount.amount,order.amtType) }}
                  </v-col>
                </v-row>
                <v-divider class="my-2"></v-divider>
                <v-row dense>
                  <v-col class="text-subtitle-1">{{systemLanguage.payOrderAmt}}</v-col>
                  <v-col align-self="center" class="text-right text-subtitle-1 font-weight-bold">
                    {{formatPrice(order.amt,order.amtType)}}
                  </v-col>
                </v-row>

                <v-card-actions></v-card-actions>
              </v-card>
            </template>
          </template>
        </v-layout>
      </v-main>
      <v-form ref="searchForm" v-model="valid" lazy-validation v-cloak>
        <v-row justify="center">
          <v-dialog v-model="showSearchDialog" persistent max-width="85%">
            <v-card>
              <v-card-title>
                <span class="searchDialog-title">{{systemLanguage.searchOrderDiaTittle}}</span>
              </v-card-title>
              <v-card-text>
                <v-row dense align="center" class="mt-2 mb-2">
                  <v-col class="d-flex pt-0 pb-0 pr-0" cols="5">
                    <v-select
                      v-model="searchForm.areaCode"
                      :items="Object.values(regionList)"
                      item-text="phone"
                      item-value="phone[0]"
                      :label="systemLanguage.searchOrderDiaAreaLabel"
                    >
                      <template slot="prepend-inner">
                        <span style="padding-top: 0.25em; z-index: 1">+</span>
                      </template>
                      <template slot="item" slot-scope="{item,index,select}">
                        <v-row dense class="d-flex" justify="center">
                          <v-col cols="12">
                            <span :class="'fi-'+item.code" class="fi fis"></span>
                            <span class="pl-1 text-caption">
                              <strong>{{ item.name }}</strong>
                              ({{ item.native }})
                            </span>
                            <span style="color: #cccccc" class="text-subtitle-2">
                              +{{item.phone[0]}}
                            </span>
                          </v-col>
                        </v-row>
                      </template>
                    </v-select>
                  </v-col>
                  <v-col class="d-flex pt-0 pb-0" cols="7">
                    <v-text-field
                      v-model="searchForm.phone"
                      :rules="searchForm.phone?rules.phone:false"
                      :label="systemLanguage.searchOrderDiaPhoneLabel"
                    ></v-text-field>
                  </v-col>
                </v-row>

                <v-text-field
                  v-model="searchForm.email"
                  :rules="searchForm.email?rules.email:false"
                  :label="systemLanguage.searchOrderDiaEmailLabel"
                ></v-text-field>
                <div class="searchDialog-bottom-tip" :class="{'shake-animation':diaBTipAnimation}">
                  {{systemLanguage.searchOrderDiaBottomTip}}
                </div>
              </v-card-text>
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="error" text @click="showSearchDialog = false">
                  {{systemLanguage.searchOrderDiaCloseBtn}}
                </v-btn>
                <v-btn color="blue darken-1" text @click="searchOrderList">
                  {{systemLanguage.searchOrderDiaConfirmBtn}}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-row>
      </v-form>
      <v-snackbar
        v-cloak
        v-model="notification"
        :timeout="timeOut"
        text
        centered
        light
        app
        :color="snackTheme"
      >
        {{ notificationText }}
        <template v-slot:action="{ attrs }">
          <v-btn :color="snackTheme" text v-bind="attrs" @click="notification = false">
            {{systemLanguage.btnTxtForConfirm}}
          </v-btn>
        </template>
      </v-snackbar>
      <v-snackbar
        v-cloak
        v-model="emptySnackbar"
        timeout="2200"
        shaped
        app
        text
        top
        light
        color="var(--styleColor)"
        class="empty-snackbar"
        max-width="90%"
        min-width="90%"
        elevation="10"
      >
        <img
          src="../static/img/newImage/dovetailArrow.jpg"
          class="dovetailArrow-icon"
          alt="arrow"
        />
        <span style="font-family: unset">{{systemLanguage.emptySnackbarTip}}</span>
      </v-snackbar>
      <v-footer app>
        <!-- -->
      </v-footer>
    </v-app>
    <script src="../static/I18n/initI18n.js"></script>
    <script src="../static/js-cookie/index.js"></script>
    <script src="../static/js/versionTag.js"></script>
    <script src="../static/js/page/lib-flexible.js"></script>
    <script src="../static/js/index/utils.js"></script>
    <script src="../static/byod_webUtils/public.js"></script>
    <script src="../static/js/index/device.js"></script>
    <script src="../static/vuetify/SnackBar.js"></script>
    <script src="../static/js/page/globalStoreMixin.js"></script>
    <script src="../static/vue/vue.min.js"></script>
    <script src="../static/vuetify/vuetify.min.js"></script>

    <script>
      /**
       * 加法 用来得到精确的加法结果
       * @param number
       * @returns number
       */
      function accuracyAdd(num1 = 0, num2 = 0) {
        let r1 // num1的小数部分长度
        let r2 // num2的小数部分长度
        try {
          r1 = num1.toString().split(".")[1].length
        } catch (e) {
          r1 = 0
        }
        try {
          r2 = num2.toString().split(".")[1].length
        } catch (e) {
          r2 = 0
        }
        // 要放大的倍数
        const commonMultiple = Math.pow(10, Math.max(r1, r2))
        // 把num1, num2 放大commonMultiple倍
        const maxMultiple = Math.max(r1, r2)
        const tempNum1 = Number(num1.toString().replace(".", "") + "0".repeat(maxMultiple - r1))
        const tempNum2 = Number(num2.toString().replace(".", "") + "0".repeat(maxMultiple - r2))

        // 整数之间的除法没有问题
        return (tempNum1 + tempNum2) / commonMultiple
      }
      Number.prototype.add = function (arg) {
        return accuracyAdd(arg, this)
      }

      /**
       * 减法 用来得到精确的减法结果
       * 返回值：arg1减去arg2的精确结果
       */
      function accSubtr(arg1, arg2) {
        var r1, r2, m, n
        try {
          r1 = arg1.toString().split(".")[1].length
        } catch (e) {
          r1 = 0
        }
        try {
          r2 = arg2.toString().split(".")[1].length
        } catch (e) {
          r2 = 0
        }
        m = Math.pow(10, Math.max(r1, r2))
        //动态控制精度长度
        n = r1 >= r2 ? r1 : r2
        return ((arg1 * m - arg2 * m) / m).toFixed(n)
      }
      //给Number类型增加一个subtr 方法，调用起来更加方便。
      Number.prototype.subtr = function (arg) {
        return accSubtr(arg, this)
      }

      /**
       * 乘法 用来得到精确的乘法结果
       * @param number
       * @returns number
       */
      function accuracySub(arg1 = 0, arg2 = 0) {
        // arg1、arg2小数部分长度和
        let m = 0
        const s1 = arg1.toString()
        const s2 = arg2.toString()

        try {
          m += s1.split(".")[1].length
        } catch (e) {
          // console.log(e)
        }
        try {
          m += s2.split(".")[1].length
        } catch (e) {
          // console.log(e)
        }
        return (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) / Math.pow(10, m)
      }

      Number.prototype.mul = function (arg) {
        return accuracySub(this, arg)
      }
      /**
       * 除法 用来得到精确的除法结果
       * 返回值：arg1除以arg2的精确结果
       */
      function accDiv(arg, acc) {
        var r1, r2, m
        try {
          r1 = this.toString().split(".")[1].length
        } catch (e) {
          r1 = 0
        }
        try {
          r2 = arg.toString().split(".")[1].length
        } catch (e) {
          r2 = 0
        }
        // 解决分子分母含有0的情况
        if (!this || !arg) {
          return 0
        }

        m = Math.pow(10, Math.max(r1, r2))
        var a = parseInt(this * m + 0.5)
        var b = parseInt(arg * m + 0.5)
        var result = a / b
        // 小数位数处理
        if (acc) {
          return Number(Number(result).toFixed(parseInt(acc)))
        } else {
          return result
        }
      }
      //给Number类型增加一个div方法，调用起来更加方便。
      Number.prototype.div = function (arg) {
        return accDiv(this, arg)
      }
    </script>
    <script type="module">
      // import { openTable, orderList, storeList } from "./orderList.js"
      let openTable = JSON.parse(sessionStorage.getItem("openTable"))
      let storeList = JSON.parse(sessionStorage.getItem("foodCourtStoreList") || "[]")
      let showFoodCourtView = sessionStorage.getItem("showFoodCourtView") === "true"
      function getThemeColor() {
        const isColor = strColor => {
          const s = new Option().style
          s.color = strColor
          return s.color !== ""
        }
        // 兼容foodCourt模式,未进入店铺则为*的color
        let { color = openTable.color } = showFoodCourtView
          ? JSON.parse(sessionStorage.getItem("mode") || "{}").config || {}
          : {}
        return isColor(color)
          ? color
          : getComputedStyle(document.body).getPropertyValue("--styleColor")
      }
      new Vue({
        el: "#root",
        vuetify: new Vuetify({
          theme: {
            themes: {
              light: {
                primary: getThemeColor(),
                secondary: "#b0bec5",
                anchor: "#8c9eff",
                accent: "#82B1FF",
                error: "#FF5252",
                info: "#2196F3",
                success: "#4CAF50",
                warning: "#FFC107"
              }
            }
          }
        }),
        data() {
          return {
            loadSuccess: false,
            loading: false,
            payLoading: false,
            showSearchDialog: false,
            showSearchIcon: false,
            emptySnackbar: false,
            openTable,
            sortMethodActive: 0,
            sortTypeActive: 1,
            orderList: [],
            storeList,
            regionList: {}, //地区列表()
            searchForm: {
              areaCode: null,
              phone: "",
              email: ""
            },
            diaBTipAnimation: false,
            rules: {},
            valid: true,
            systemLanguage: {},
            defaultAreaCode: null // 默认的地区码
          }
        },
        components: {
          VersionTag
        },
        mixins: [SnackBar, globalStoreMixin],
        computed: {
          sortMethods() {
            let { time = "Time", totalPrice = "Price" } = this.systemLanguage
            return [time, totalPrice]
          },
          sortTypes() {
            let { sortAscending = "Ascending", sortDescending = "Descending" } = this.systemLanguage
            return [sortAscending, sortDescending]
          },
          showGoHomeBtn() {
            let { isDiningStyleMode, initialTableNum } = this.openTable
            let specialMode = ["AssistMode", "StaffMode", "EnhAssistMode"].includes(initialTableNum)
            return specialMode || isDiningStyleMode
          },
          noOrders() {
            return !this.orderList.length
          },
          language() {
            return this.openTable.language || "en"
          },
          moneySign() {
            return this.openTable.currencyWay || "$"
          },
          // 处理orderList
          formatOrderList() {
            // 将赠送food从orderData中提取出来
            let list = this.orderList.map(order => {
              let { promotionFoodList = [], orderData = [], discountList = [] } = order
              return { ...order, promotionFoodList, orderData, discountList }
            })
            // 分组和重组数据,转为storeList
            return Object.values(this.groupBy(list, ({ merchantRef }) => merchantRef)).map(
              order => {
                if (Array.isArray(order) && order.length) {
                  let storeList = []
                  order.forEach(it => {
                    let { amt, storeNumber, orderData = [], amtType, specialItem = null } = it
                    storeList.push({ amt, storeNumber, orderData, amtType, specialItem })
                  })
                  let base = JSON.parse(JSON.stringify(order[0]))
                  if (order.length !== 1) {
                    delete base.storeNumber
                    delete base.amtType
                  }
                  delete base.orderData
                  return { ...base, storeList, amt: base.multipleTotalAmt || base.amt }
                }
                return order
              }
            )
          },
          // 用于渲染的orderList
          renderList() {
            // 处理排序...
            let sortMap = new Map()
            let order = this.sortTypeActive === 0
            sortMap.set(this.sortMethodActive === 0, "date")
            sortMap.set(this.sortMethodActive === 1, "amt")
            switch (sortMap.get(true)) {
              case "date":
                return this.formatOrderList.toSorted((a, b) => {
                  let aDate = a.date || a.createTime
                  let bDate = b.date || b.createTime
                  return order
                    ? this.compareDateTime(aDate, bDate)
                    : this.compareDateTime(bDate, aDate)
                })
              case "amt":
                return this.formatOrderList.toSorted((a, b) => {
                  return order ? a.amt - b.amt : b.amt - a.amt
                })
              default:
                break
            }
            return this.formatOrderList
          }
        },
        created() {
          this.initLocalConfig()
          this.initMenuLanguage()
          this.systemLanguage = Object.freeze(window.i18n[this.language])
          this.resetFontSize()
          this.beforeRequest()
          this.setRules()
          this.initSomePay()
        },
        mounted() {
          // 第三方支付后退刷新
          window.addEventListener("pageshow", function (event) {
            //event.persisted属性为true时，表示当前文档是从往返缓存中获取
            if (event.persisted) {
              this.loading = true
              location.reload()
            }
          })
          document.body.style.setProperty("--styleColor", getThemeColor())
          window.removeEventListener("resize", diy_resize, false)
        },

        methods: {
          // 处理支付跳转
          handlePaymentRedirect(sessionId) {
            // console.log("handlePaymentRedirect", sessionId)
            if (sessionId) this.initEFTPay(sessionId)
          },
          initMenuLanguage() {
            const i18n = sessionStorage.getItem("i18nPkg")
            try {
              const { menu } = JSON.parse(i18n)
              const lan = this.language
              const formatData = formatI18n([...menu])[lan]
              this.menuLanguageList = Object.freeze(formatData)
            } catch {}
          },
          formatPayVal(val) {
            return this.menuLanguageList[val + "Txt"] || val
          },
          showPayMethod(val = "") {
            // 跟支付成功逻辑一致:仅显示eft类别
            return val.toString().toUpperCase() === "EFT"
          },
          groupBy(list, keyFn) {
            const groups = {}
            for (const item of list) {
              const key = keyFn(item)
              const group = groups[key]
              if (!group) {
                groups[key] = [item]
              } else {
                group.push(item)
              }
            }
            return groups
          },
          initLocalConfig() {
            fetch("../static/utils/config.json")
              .then(r => r.json())
              .then(r => {
                document.title = r.BYOD.DocumentTitle || ""
              })
            fetch("../static/vuetify/region.json")
              .then(r => r.json())
              .then(r => {
                this.regionList = r
                if (Object.values(r).length) {
                  this.defaultAreaCode = Object.values(r)[0].phone[0]
                  this.searchForm.areaCode = this.defaultAreaCode
                }
              })
          },
          initSomePay() {
            //openTable获取配置前缀pix字段,动态加载script标签支付依赖
            if (this.openTable.webBaseUrl && !window.SpiralPG) {
              //动态创建script标签,前缀+"spiralpg.min.js",async属性加载
              let script = document.createElement("script")
              script.type = "text/javascript"
              script.src = this.openTable.webBaseUrl + "spiralpg.min.js"
              script.async = true
              document.body.appendChild(script)
            }
          },
          dealWithMerchantRef() {
            let merchantRefArray = Cookies.get("merchantRefArray")
            if (!merchantRefArray) {
              return []
            } else {
              merchantRefArray = JSON.parse(merchantRefArray)
              let merchantRefList = merchantRefArray.map(item => {
                return item.merchantRef
              })
              return merchantRefList
            }
          },
          beforeRequest() {
            // 没有此cookie或者长度为0都直接停止执行
            let merchantRefList = this.dealWithMerchantRef()
            if (merchantRefList.length) {
              this.getPayOrder({ merchantRefList })
            } else {
              let { initialTableNum, performType } = this.openTable
              if (initialTableNum === "TAKEAWAY" || performType == 2) {
                this.emptySnackbar = true
                this.showSearchIcon = true
              }
            }
          },
          // 获取历史记录
          getPayOrder(params) {
            this.loading = true
            let { companyName, storeNumber } = this.openTable
            let data = {
              companyName,
              storeNumber,
              email: "",
              customerPhone: "",
              merchantRefList: "",
              ...params
            }
            fetch("../pay/getOrderInfo", {
              method: "POST",
              body: this.toFormData(data)
            })
              .then(r => r.json())
              .then(res => {
                let { statusCode, orderList = [] } = res
                if (statusCode === 200) {
                  this.orderList = orderList
                } else {
                  this.requestErrorTip(statusCode)
                }
              })
              .finally(() => {
                this.loading = false
                this.$nextTick(this.initMultiLine)
              })
          },
          toFormData(obj) {
            let data = new FormData()
            for (const key in obj) {
              if (Object.hasOwnProperty.call(obj, key)) {
                const element = obj[key]
                data.append(key, element)
              }
            }
            return data
          },
          requestErrorTip(code) {
            let { errorMsg, errorTableKey, staticQRCodeNoOpenTableError } = this.systemLanguage
            let { tableNumber } = this.openTable
            let useTip = ""
            switch (code) {
              case 5003:
                useTip = errorTableKey //tableKey不正确
                break
              case 5005:
                //staticQRCodeNoOpenTableError字符串里面替换#tableNumber为当前桌号
                useTip = staticQRCodeNoOpenTableError.replace("#tableNumber", tableNumber) //未开台
                break
              default: //tableKey不正确
                useTip = errorMsg
                break
            }
            this.$snackbar(useTip, 2000, "var(--styleColor)")
          },
          // 重置fontsize, vuetify无法与MenuPage的fontSize匹配
          resetFontSize() {
            let zoomFontSize = sessionStorage.getItem("zoomFontSize")
            if (zoomFontSize) {
              let currentFontSize = JSON.parse(zoomFontSize).currentFontSize
              let fontSize = +currentFontSize.split("px")[0]
              document.documentElement.style.fontSize = (fontSize / 2.25 || 16) + "px"
            } else {
              //默认值
              document.documentElement.style.fontSize = "19px"
            }
          },
          toIndex() {
            let href = sessionStorage.getItem("indexPageUrl")
            window.location.href = href
          },
          /**
           * @description 将北京时间的日期时间字符串转为当前时区
           * @param {string} dataTimeString
           * @returns {Date} 当前时区的Dat对象
           */
          formatTime(dataTimeString) {
            const dateTimePattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
            if (dateTimePattern.test(dataTimeString)) {
              const BJTimeZone = "Asia/Shanghai"
              let [dataString, timeString] = dataTimeString.split(" ")
              let [year, month, day] = dataString.split("-")
              let [hour, minute, second] = timeString.split(":")
              let BJDate = new Date(year, month - 1, day, hour, minute, second)
              const BJDateOffsetHour = Math.abs(-480) / 60
              const currentOffset = new Date().getTimezoneOffset()
              const LikeUTCDate = new Date(BJDate.getTime() - BJDateOffsetHour * 60 * 60 * 1000)
              const UTCYear = LikeUTCDate.getFullYear()
              const UTCMonth = LikeUTCDate.getMonth()
              const UTCDay = LikeUTCDate.getDate()
              const UTCHour = LikeUTCDate.getHours()
              const UTCMinute = LikeUTCDate.getMinutes()
              const UTCSecond = LikeUTCDate.getSeconds()

              const date = new Date(
                // Date.UTC(UTCYear, UTCMonth, UTCDay, UTCHour, UTCMinute, UTCSecond),
                Date.UTC(year, month, day, hour, minute, second)
              )
              return date
            }
          },
          /**
           * @description 语义化/口语化日期
           * @example  1 hour ago, 2小时前
           * @param {Date} date  要转换的Date对象
           */
          semanticTime(date = new Date()) {
            date = new Date(date)
            // 只转换6小时内的时间字符串
            const MAX_HOURS = 6
            const curLanCode = navigator.language || navigator.userLanguage
            const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
            // 与当前时间判断,若大于6小时,则返回格式化后的对象
            if (false) {
              const rtf = new Intl.RelativeTimeFormat(curLanCode, { numeric: "auto" })
              console.log(rtf.format(-1, "hour"), date)
            } else {
              let hk = "zh"
              return new Intl.DateTimeFormat(hk, {
                year: "numeric",
                month: "numeric",
                day: "numeric",
                hour: "numeric",
                minute: "numeric",
                second: "numeric",
                hour12: false,
                timeZone
              }).format(date)
            }
          },
          // 根据storeNumber获取store数据
          getStoreByCode(storeNumber) {
            return this.storeList.find(e => e.storeNumber === storeNumber)
          },
          getStoreName({ storeNumber }) {
            let store = this.getStoreByCode(storeNumber)
            if (store) {
              return (store.storeName || {})[this.language] || ""
            }
            return (this.openTable.storeName || {})[this.language] || ""
          },
          // 用于显示order Food的图片
          combinImageUrl(obj, orderItem = {}) {
            let { fCode, photoSuffix = "jpg" } = obj
            let { specialItem } = orderItem
            const defaultOSS = "https://appwise.oss-cn-hongkong.aliyuncs.com"
            let oss =
              (sessionStorage.getItem("useBackupOss") && sessionStorage.getItem("backupOssUrl")) ||
              defaultOSS
            let domain = sessionStorage.getItem("domain") || window.location.hostname.split(".")[0]
            if (specialItem == "memberRecharge") {
              return `${oss}/${domain}/image/memberRecharge/${fCode}.${photoSuffix}`
            } else {
              return oss + "/" + domain + "/image/food/" + fCode + "." + photoSuffix
            }
          },
          // 显示food的name
          inListTitle(item) {
            switch (this.language) {
              case "en":
                return item.desc1 ? item.nameA || item.desc1 : item.nameA || item.name
              case "zh":
                return item.desc2 ? item.nameB || item.desc2 : item.nameB || item.name2
                break
              default:
                return item.multi1
                  ? item.multi1
                  : item.desc1
                  ? item.nameA || item.desc1
                  : item.nameA || item.name
            }
          },
          //获取该food下一级的所有flist/mlist的name组合一起<fType/mType另外处理>
          // 最多嵌套一层<外层food  -> 内层food  >
          getFoodDetail(food, { amtType }) {
            return this.parseOrderTitle(food, amtType)
          },
          /**
           * @description 解析NewOrderList,返回要显示的title字符串
           * @param {object} foodItem
           * @param {string} amtType
           * @returns {string}
           */
          parseOrderTitle(foodItem, amtType) {
            let {
              newOrderItemFoodList: flist = [],
              newOrderItemMListList: mlist = [],
              newOrderItemFoodTypeList: ftype = [],
              newOrderItemMTypeList: mtype = [],
              packingBoxMList: packaging = false
            } = foodItem
            let packingBoxMList = []
            if (packaging) {
              packingBoxMList.push(packaging)
            }
            const parseFlist = (list = []) => {
              if (!list.length) return ""
              let str = list.reduce((pre, cur) => {
                let { qty1, upa1 } = cur
                qty1 = +qty1
                let mutQty = qty1 > 1
                let mutQtyStr = (mutQty && `x ${mutQty}`) || ""

                let price = accuracySub(qty1, upa1)
                let showPrice = price !== 0 || ""
                let priceStr = this.formatPrice(price, amtType)
                let baseCombind =
                  this.inListTitle(cur) + (showPrice && `(${priceStr})`) + mutQtyStr + ","
                if (Array.isArray(cur.newOrderItemFoodList)) {
                  baseCombind += parseFlist(cur.newOrderItemFoodList)
                }
                if (Array.isArray(cur.newOrderItemMListList)) {
                  baseCombind += parseMlist(cur.newOrderItemMListList)
                }
                if (Array.isArray(cur.newOrderItemFoodTypeList)) {
                  baseCombind += parseFtype(cur.newOrderItemFoodTypeList)
                }
                if (Array.isArray(cur.newOrderItemMTypeList)) {
                  baseCombind += parseMtype(cur.newOrderItemMTypeList)
                }
                if (cur.packingBoxMList) {
                  packingBoxMList.push(cur.packingBoxMList)
                }
                pre += baseCombind
                return pre
              }, "")
              return str.trim()
            }
            const parseMlist = (list = []) => {
              if (!list.length) return ""
              let str = list.reduce((pre, cur) => {
                let { qty1, price: upa1 } = cur
                qty1 = +qty1
                let mutQty = qty1 > 1
                let mutQtyStr = (mutQty && `x ${mutQty}`) || ""
                let price = accuracySub(qty1, upa1)
                let showPrice = price !== 0 || ""
                let priceStr = this.formatPrice(price, amtType)
                let baseCombind =
                  this.inListTitle(cur) + (showPrice && `(${priceStr})`) + mutQtyStr + ","
                if (Array.isArray(cur.newOrderItemMListList)) {
                  baseCombind = removeLastSign(baseCombind)
                  let mlistCombind = parseMlist(cur.newOrderItemMListList)
                  mlistCombind = removeLastSign(mlistCombind)
                  baseCombind += (mlistCombind && `(${mlistCombind}),`) || ","
                }
                if (Array.isArray(cur.newOrderItemMTypeList)) {
                  baseCombind += parseMtype(cur.newOrderItemMTypeList)
                }
                if (cur.packingBoxMList) {
                  packingBoxMList.push(cur.packingBoxMList)
                }
                pre += baseCombind
                return pre
              }, "")
              return str.trim()
            }
            const parseFtype = (list = []) => {
              if (!list.length) return ""
              let str = list.reduce((pre, cur) => {
                if (Array.isArray(cur.newOrderItemFoodList)) {
                  pre += parseFlist(cur.newOrderItemFoodList)
                }
                return pre
              }, "")
              return str.trim()
            }
            const parseMtype = (list = []) => {
              if (!list.length) return ""
              let str = list.reduce((pre, cur) => {
                if (Array.isArray(cur.newOrderItemMListList)) {
                  pre += parseMlist(cur.newOrderItemMListList)
                }
                return pre
              }, "")
              return str.trim()
            }
            const removeLastSign = val => {
              if (val.endsWith(",")) return val.replace(/,*$/, "")
              if (val.endsWith("、")) return val.replace(/、*$/, "")
              return val
            }
            let result = ""
            result += parseFlist(flist)
            result += parseMlist(mlist)
            result += parseFtype(ftype)
            result += parseMtype(mtype)
            result += parseMlist(packingBoxMList)
            result = removeLastSign(result)
            return result.trim()
          },
          // 计算单个food的价格
          calculatePrice(foodItem) {
            let {
              newOrderItemFoodList: flist = [],
              newOrderItemMListList: mlist = [],
              newOrderItemFoodTypeList: ftype = [],
              newOrderItemMTypeList: mtype = [],
              packingBoxMList
            } = foodItem
            const calculateFlist = (list = []) => {
              if (!list.length) return ""
              const allPrice = list.reduce((pre, cur) => {
                let { qty1, upa1 } = cur
                qty1 = +qty1
                let basePrice = qty1.mul(upa1)
                if (Array.isArray(cur.newOrderItemFoodList)) {
                  basePrice = basePrice.add(calculateFlist(cur.newOrderItemFoodList))
                }
                if (Array.isArray(cur.newOrderItemMListList)) {
                  basePrice = basePrice.add(calculateMlist(cur.newOrderItemMListList))
                }
                if (Array.isArray(cur.newOrderItemFoodTypeList)) {
                  basePrice = basePrice.add(calculateFtype(cur.newOrderItemFoodTypeList))
                }
                if (Array.isArray(cur.newOrderItemMTypeList)) {
                  basePrice = basePrice.add(calculateMtype(cur.newOrderItemMTypeList))
                }
                if (cur.packingBoxMList) {
                  basePrice = basePrice.add(calculateMlist([cur.packingBoxMList]))
                }
                pre = pre.add(basePrice)
                return pre
              }, 0)
              return +allPrice
            }
            const calculateMlist = (list = []) => {
              if (!list.length) return ""
              const allPrice = list.reduce((pre, cur) => {
                let { qty1, price: upa1 } = cur
                qty1 = +qty1
                let basePrice = qty1.mul(upa1)

                if (Array.isArray(cur.newOrderItemMListList)) {
                  basePrice = basePrice.add(calculateMlist(cur.newOrderItemMListList))
                }
                if (Array.isArray(cur.newOrderItemMTypeList)) {
                  basePrice = basePrice.add(calculateMtype(cur.newOrderItemMTypeList))
                }
                if (cur.packingBoxMList) {
                  basePrice = basePrice.add(calculateMlist([cur.packingBoxMList]))
                }
                pre = pre.add(basePrice)
                return pre
              }, 0)
              return +allPrice
            }
            const calculateFtype = (list = []) => {
              if (!list.length) return ""
              let allPrice = list.reduce((pre, cur) => {
                let basePrice = 0
                if (Array.isArray(cur.newOrderItemFoodList)) {
                  basePrice = basePrice.add(calculateFlist(cur.newOrderItemFoodList))
                }
                pre = pre.add(basePrice)
                return pre
              }, 0)
              return +allPrice
            }
            const calculateMtype = (list = []) => {
              if (!list.length) return ""
              let allPrice = list.reduce((pre, cur) => {
                let basePrice = 0
                if (Array.isArray(cur.newOrderItemMListList)) {
                  basePrice = basePrice.add(calculateMlist(cur.newOrderItemMListList))
                }
                pre = pre.add(basePrice)
                return pre
              }, 0)
              return +allPrice
            }
            let price = foodItem.upa1 || foodItem.price || 0 // 加上foodItem本身的价格
            if (packingBoxMList) price = price.add(calculateMlist([packingBoxMList]))
            price = price.add(calculateFlist(flist))
            price = price.add(calculateMlist(mlist))
            price = price.add(calculateFtype(ftype))
            price = price.add(calculateMtype(mtype))
            price = accuracySub(price, +foodItem.qty1) // *数量
            return price
          },
          // 格式化价格 符号/四舍五入
          formatPrice(price, amtType) {
            let currencySymbol = amtType || this.moneySign
            if (price > 0) {
              return `${currencySymbol}${this.retainSignificantDecimals(price)}`
            } else if (price < 0) {
              let absPrice = Math.abs(price)
              return `-${currencySymbol}${this.retainSignificantDecimals(absPrice)}`
            }
            return currencySymbol + 0
          },
          // 格式化折扣信息描述
          formatDiscountDesc(discount) {
            if (discount.promotionDesc) {
              // 折扣码
              try {
                let desc = JSON.parse(discount.promotionDesc)
                return desc[this.language]
              } catch {
                return "Discount"
              }
            } else {
              // 会员折扣
              return this.language === "en" ? discount.desc : discount.desc2
            }
          },
          orderStatusTip(status) {
            let {
              pendingPaymentStatus,
              shopOrderPending,
              paymentPending,
              rechargingTip,
              rechargeSuccessTip
            } = this.systemLanguage
            let orderStatusObj = {
              6: pendingPaymentStatus, //待确认支付状态
              7: shopOrderPending, //等待店铺接受订单
              8: rechargingTip, //充值中
              9: rechargeSuccessTip, //充值成功
              10: paymentPending //前端自加,占用10(无orderStatus也无orderNumber)
            }
            return orderStatusObj[status] || ""
          },
          // 比较2个时间
          compareDateTime(date1, date2) {
            // 转为时间搓比较
            let date1Stamp = new Date(date1).getTime()
            let date2Stamp = new Date(date2).getTime()
            return date1Stamp - date2Stamp
          },
          searchOrderList() {
            let verifyRes = this.$refs.searchForm.validate()
            if (verifyRes && (this.searchForm.phone || this.searchForm.email)) {
              let { phone, email, areaCode } = this.searchForm
              let params = {
                customerPhone: phone ? `+${areaCode}${phone}` : "",
                email
              }
              setTimeout(() => {
                this.getPayOrder(params)
              }, 1000)
              this.showSearchDialog = false
              //手动重置表单
              this.searchForm = {
                areaCode: this.defaultAreaCode,
                phone: "",
                email: ""
              }
            } else {
              // 检查phone和email的值
              if (!this.searchForm.phone && !this.searchForm.email) {
                this.diaBTipAnimation = true
                let onceAnimation = setTimeout(() => {
                  this.diaBTipAnimation = false
                }, 500)
              }
            }
          },
          //动态给rules添加校验规则
          setRules() {
            const rules = {
              phone: [v => /^\d+$/.test(v) || this.systemLanguage.invalidPhoneMsg],
              email: [v => /.+@.+\..+/.test(v) || this.systemLanguage.invalidEmailMsg]
            }
            this.$set(this.$data, "rules", rules)
          },
          /**
           * @description 四舍五入
           * @example toFixed(1,1.14)->1.1  toFixed(1,'1.15')=>1.2
           * @param {number} d 保留的小数位数
           * @param {number|string} s 要保留的小数
           * */
          toFixed(d, s) {
            s = s.toString()
            if (!d) d = 0
            if (s.indexOf(".") === -1) s += "."
            s += new Array(d + 1).join("0")
            if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
              let s = "0" + RegExp.$2,
                pm = RegExp.$1,
                a = RegExp.$3.length,
                b = true
              if (a === d + 2) {
                a = s.match(/\d/g)
                if (parseInt(a[a.length - 1]) > 4) {
                  for (let i = a.length - 2; i >= 0; i--) {
                    a[i] = parseInt(a[i]) + 1
                    if (a[i] === 10) {
                      a[i] = 0
                      b = i !== 1
                    } else break
                  }
                }
                s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2")
              }
              if (b) s = s.substr(1)
              return +(pm + s).replace(/\.$/, "")
            }
            return this + ""
          },
          //保留有效小数
          retainSignificantDecimals(price) {
            const { keepDecimals: decimal } = this.openTable
            if (decimal) {
              price = typeof price !== "number" ? Number(price) : price
              //n:保留的位数,t:是否补零
              let { significantDigits: n, zeroPadding: t } = decimal
              if (price) {
                const isMeetLength = (o, n) => {
                  const s = o.toString().split(".")
                  return s.length === 2 ? s[1].length <= n : true
                }
                if (isMeetLength(price, n)) {
                  return t ? this.toFixed(n, price) : price
                }
                return this.toFixed(n, price)
              } else {
                return price
              }
            }
            return price
          },
          // 多文字时,点击切换多行/单行显示
          toggleLine(e) {
            let target = e.target.parentNode
            let icon = e.target
            if (target.classList.contains("text-truncate")) {
              icon.style.transform = "rotate(180deg)"
            } else {
              icon.style.transform = "rotate(0deg)"
            }
            target.classList.toggle("text-truncate")
          },
          // 在dom渲染完成时,初始化文本溢出状态
          initMultiLine() {
            let refs = this.orderList.map(e => e.merchantRef)
            refs.forEach(e => {
              let child = this.$refs[e][0].$el
              let dom = child.querySelectorAll("div[code]")
              Array.from(dom).forEach(d => {
                // 20 i标签的宽高
                let pw = d.offsetWidth - 20
                let cw = d.querySelector("span").offsetWidth
                let isOverflow = cw > pw
                if (!isOverflow) {
                  let btn = d.querySelector("button")
                  if (btn instanceof HTMLElement) {
                    d.removeChild(btn)
                  }
                }
              })
            })
          },
          showOrderNotConfirmed(order) {
            let { orderStatus, billNumber } = order
            return [6, 7, 8].includes(orderStatus) || (!orderStatus && !billNumber)
          },
          openPayLoading() {
            this.payLoading = true
          },
          closePayLoading() {
            this.payLoading = false
          },
          showPayErrorTip(error) {
            // 显示支付错误提示
            this.$snackbar(error, 2000, "#ff0000")
          }
        }
      })
    </script>
  </body>
</html>
