/* * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #f6f8fc 0%, #e9f0f8 100%);
  min-height: 100vh;
  max-height: 100vh;
  overflow: hidden;
} */
#rechargeDia {
  background: linear-gradient(135deg, #f6f8fc 0%, #e9f0f8 100%);
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
}
.rechargeBox-container {
  padding: 0.4rem 0.25rem 0;
  overflow: hidden;
  display: flex;
  flex: 1;
  flex-direction: column;
}

.rechargeBox-scroll-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 自定义滚动条样式 */
.rechargeBox-scroll-container::-webkit-scrollbar {
  width: 0.16rem;
}

.rechargeBox-scroll-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 0.08rem;
}

.rechargeBox-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.3);
  border-radius: 0.08rem;
}

.rechargeBox-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.5);
}

.rechargeBox-section-title {
  font-size: 0.35rem;
  color: #333;
  margin-bottom: 0.1rem;
  font-weight: 600;
  position: relative;
  padding-left: 0.6rem;
  height: 0.8rem;
  display: flex;
  align-items: center;
}

.rechargeBox-section-title::before {
  content: "";
  position: absolute;
  left: 0.4rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.08rem;
  height: 0.32rem;
  background: #409eff;
  border-radius: 0.04rem;
}

.rechargeBox-recharge-options {
  display: flex;
  flex-direction: column;
  gap: 0.35rem;
  margin: 0.3rem 0.4rem 0.6rem;
  box-sizing: border-box;
}

.rechargeBox-recharge-card {
  min-height: 2.5rem;
  border-radius: 0.24rem;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0.08rem 0.32rem rgba(0, 0, 0, 0.05);
  display: flex;
  background: #fff;
  padding: 0.25rem 0.32rem;
  border: 0.04rem solid transparent;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

.rechargeBox-recharge-card.rechargeBox-active {
  border-color: #409eff;
  box-shadow: 0 0.16rem 0.48rem rgba(64, 158, 255, 0.2);
  transform: translateY(-0.08rem) translateZ(0);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 左侧图片区域优化 */
.rechargeBox-card-image {
  width: 35%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rechargeBox-card-image::before {
  content: "📷";
  font-size: 0.8rem;
  opacity: 0.2;
}

.rechargeBox-card-image img {
  position: absolute;
  width: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
  opacity: 0;
  border-radius: 0.1rem;
}

.rechargeBox-card-image img.rechargeBox-loaded {
  opacity: 1;
}

/* 右侧内容区域重构 */
.rechargeBox-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.16rem;
  margin-left: 0.3rem;
}

/* 标题和金额区域 */
.rechargeBox-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rechargeBox-title-area {
  flex: 1;
  margin-right: 0.24rem;
  position: relative;
}

.rechargeBox-card-title {
  font-size: 0.36rem;
  color: #2c3e50;
  font-weight: 600;
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.rechargeBox-card-title::after {
  content: "";
  position: absolute;
  bottom: -0.05rem;
  left: 0;
  width: 0;
  height: 0.05rem;
  background: linear-gradient(90deg, #409eff, #3a8ee6);
  transition: width 0.3s ease;
  border-radius: 0.02rem;
}

.rechargeBox-recharge-card.rechargeBox-active .rechargeBox-card-title::after {
  width: 100%;
}

.rechargeBox-price-area {
  display: flex;
  align-items: baseline;
  white-space: nowrap;
}

.rechargeBox-currency {
  font-size: 0.28rem;
  color: #409eff;
  margin-right: 0.04rem;
}

.rechargeBox-price {
  font-size: 0.44rem;
  color: #409eff;
  font-weight: bold;
  line-height: 1;
}

/* 赠送信息区域重构 */
.rechargeBox-benefits-section {
  display: flex;
  flex-direction: column;
  gap: 0.08rem;
  padding: 0.08rem 0;
}

.rechargeBox-benefit-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.08rem;
  padding: 0.05rem 0.16rem 0;
  border-radius: 0.12rem;
  transition: all 0.3s ease;
}

.rechargeBox-benefit-icon {
  font-size: 0.24rem;
  width: 0.32rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 0.32rem;
  line-height: 1;
}

.rechargeBox-benefit-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.28rem;
}

.rechargeBox-benefit-label {
  color: #606266;
}

.rechargeBox-benefit-value {
  font-weight: 500;
}

.rechargeBox-benefit-value.rechargeBox-points {
  color: #67c23a;
}

.rechargeBox-benefit-value.rechargeBox-member-points {
  color: #e6a23c;
}

.rechargeBox-benefit-value.rechargeBox-expire {
  color: #f56c6c;
}

/* 选中状态动画 */
@keyframes rechargeBoxCardSelected {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-0.12rem) scale(1.02);
  }
  100% {
    transform: translateY(-0.08rem) scale(1.01);
  }
}

.rechargeBox-payment-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.3rem;
  margin: 0.3rem 0.4rem 0.6rem;
}

.rechargeBox-payment-item {
  border: 0.02rem solid #eee;
  border-radius: 0.24rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0.24rem;
  background: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 0.08rem 0.32rem rgba(0, 0, 0, 0.05);
}

.rechargeBox-payment-item.rechargeBox-active {
  transform: translateY(-0.08rem);
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
  box-shadow: 0 0.16rem 0.48rem rgba(64, 158, 255, 0.2);
}

.rechargeBox-payment-icon {
  width: 0.9rem;
  /* height: 0.64rem; */
  margin-bottom: 0.12rem;
}

.rechargeBox-payment-name {
  font-size: 0.27rem;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}

.rechargeBox-button-group {
  width: 100%;
  padding: 0.32rem;
  display: flex;
  gap: 0.32rem;
}

.rechargeBox-submit-btn {
  flex: 2;
  height: 0.88rem;
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  color: #fff;
  border: none;
  border-radius: 0.44rem;
  font-size: 0.38rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.rechargeBox-submit-btn:not(:disabled):active {
  transform: scale(0.98);
}

.rechargeBox-close-btn {
  flex: 1;
  height: 0.88rem;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  border: none;
  border-radius: 0.44rem;
  font-size: 0.38rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.rechargeBox-close-btn:active {
  transform: scale(0.98);
}

/* 添加空状态样式 */
.rechargeBox-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.2rem;
  margin: 0.3rem 0.4rem;
  background: #fff;
  border-radius: 0.24rem;
  min-height: 4rem;
}

.rechargeBox-empty-state-icon {
  font-size: 1.2rem;
  margin-bottom: 0.3rem;
  color: #909399;
}

.rechargeBox-empty-state-text {
  font-size: 0.36rem;
  color: #606266;
  text-align: center;
  line-height: 1.5;
  font-weight: 500;
}

.rechargeBox-empty-state-subtext {
  font-size: 0.28rem;
  color: #909399;
  margin-top: 0.16rem;
  line-height: 1.4;
}

/* 禁用状态的提交按钮 */
.rechargeBox-submit-btn:disabled {
  background: #a0cfff;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 优化支付选项网格在空状态下的显示 */
.rechargeBox-payment-options:empty {
  display: none;
}

/* 添加自定义样式 */
.rechargeBox-loading-skin .layui-layer-content {
  background-color: transparent !important;
}

.rechargeBox-loading-skin .layui-layer-loading {
  width: 100% !important;
  height: 100% !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rechargeBox-confirm-skin {
  border-radius: 0.24rem !important;
  overflow: hidden;
}

.rechargeBox-confirm-skin .layui-layer-btn {
  padding: 0.3rem !important;
  display: flex;
  justify-content: center;
  gap: 0.3rem;
}

.rechargeBox-confirm-skin .layui-layer-btn a {
  height: 0.88rem !important;
  line-height: 0.88rem !important;
  padding: 0 0.6rem !important;
  font-size: 0.32rem !important;
  border-radius: 0.44rem !important;
  min-width: 2.4rem;
  text-align: center;
}

.rechargeBox-confirm-skin .layui-layer-btn .layui-layer-btn0 {
  background-color: #f4f4f5 !important;
  color: #909399 !important;
  border-color: #f4f4f5 !important;
}

.rechargeBox-confirm-skin .layui-layer-btn .layui-layer-btn1 {
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%) !important;
  color: #fff !important;
  border: none !important;
}

.rechargeBox-confirm-skin .layui-layer-content {
  padding: 0.4rem !important;
  text-align: center;
  font-size: 0.32rem !important;
  color: #303133;
}
/* 充值选项加载状态 */
.rechargeBox-loading-wrapper {
  min-height: 10rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 0.24rem;
  margin: 0.3rem 0.4rem 0.6rem;
  padding: 1rem;
}

.rechargeBox-loading-spinner {
  width: 0.8rem;
  height: 0.8rem;
  border: 0.06rem solid #e4e7ed;
  border-top-color: #409eff;
  border-radius: 50%;
  animation: rechargeBox-loading 0.8s linear infinite;
}

@keyframes rechargeBox-loading {
  to {
    transform: rotate(360deg);
  }
}
