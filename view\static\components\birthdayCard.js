Vue.component("birthdayCard", {
  props: ["systemLanguage", "openTable"],
  template: `
  <div class="birthdayCard-warp" >
  <p class="bday-decor bday-decor--top-left balloonFloat">🎈</p>
  <!-- <p class="bday-decor bday-decor--bottom-right zoom-left-in-out">🎉</p> -->
  <div class="birthdayCard-warp-content">
    <img :src="combinedImageUrl('static/img/newImage/cake.svg')" class="cakeSvg" alt="" />
    <div class="birthdayCard-warp-txt text-fade-in" v-html="birthdayPrompt"></div>
  </div>
</div>
  `,
  data() {
    return {}
  },
  methods: {
    combinedImageUrl(url) {
      if (this.indexMark) {
        return `./${url}`
      }
      return `../${url}`
    }
  },
  computed: {
    indexMark() {
      return mark === "pc_index"
    },

    birthdayPrompt() {
      let displayCRM = (this.openTable && this.openTable.displayCRM) || {}
      let { birthdayPromptEN, birthdayPromptZH, birthdayPromptThirdLan } = displayCRM
      let { language } = this.openTable || {}
      let promptObj = {
        en: birthdayPromptEN,
        zh: birthdayPromptZH,
        third: birthdayPromptThirdLan
      }
      return promptObj[language]
    }
  }
})
